-- 
-- sound_db.lua: sound 
--

local log= (require 'klua.log'):new('sound_db')
require 'klua.table'
local km = require 'klua.macros'
local LA = love.audio
local LS = love.sound
local FS = love.filesystem

------------------------------------------------------------

local sound_db = {}
sound_db.path = nil
sound_db.sources = {}
sound_db.source_uses = {}       -- counts how many groups use a source
sound_db.sounds = {}            -- sounds definitions, aggregates all defined sounds. Read-only once all sounds loaded.
sound_db.groups = {}            -- all sound groups, loaded or not
sound_db.source_groups = {      -- populated by sound_db:load()
    --    TAUNTS      = { max_sources=5 },
    --    SWORD_FIGHT = { max_sources=3 },
}
sound_db.group_gains = {} -- { MUSIC = 1.0, etc}. These gains will be applied to all sources in this group.
sound_db.active_sources = {}
sound_db.sound_extras = {}
sound_db.ref_counters = {}
sound_db.qts = 0
sound_db.ts = 0
sound_db.paused = false
sound_db.global_source_mode = nil   -- 'stream': stream all audio from disk
                                    -- 'static': load all audio to buffers in memory
                                    --  nil: use whatever the sound definition specifies (per sound)

sound_db.load_queue = {}
sound_db.load_queue_current = nil
sound_db.group_progress = 0
sound_db.progress = 0
sound_db.groups_total = 0
sound_db.groups_done = 0
sound_db.sounds_uses = {}

------------------------------------------------------------
local _THREADS_ENABLED = true
local _MAX_THREADS = 8
local _LOAD_AUDIO_THREAD_CODE = [[
local cin,cout,th_i = ...
require "love.filesystem"
require "love.audio"
require "love.sound"
local file_count = 0
while true do
    -- get params
    local file = cin:demand()
    if file == 'QUIT' then goto quit end
    local mode = cin:demand()
    local id = cin:demand()
    
    if not love.filesystem.isFile(file) then
        cout:push({'ERROR','Not a file',file})
    else
        local ok, result = pcall(love.audio.newSource, file, mode)
        collectgarbage()
        if ok and result then
            cout:push({'OK',result,id})
            file_count = file_count + 1
        else
            cout:push({'ERROR',result,file})
        end
    end
end
::quit::
cout:supply({'DONE'})
--print('TH  ' ..th_i.. ' QUIT - FILES LOADED ' .. file_count .. '\n')
]]


------------------------------------------------------------

function sound_db:init(path,overrides)
    log.debug('path:%s', path)
    self.path = path
    self.files_path = path .. '/files'
    self.overrides = overrides or {}
    
    local f_settings = FS.load(path ..'/settings.lua')()
    if f_settings.source_groups then
        for gid,group in pairs(f_settings.source_groups) do
            self.source_groups[gid] = { max_sources = group.max_sources or 1 }
            self.active_sources[gid] = self.active_sources[gid] or {}
        end
    end

    local f_sounds = FS.load(path ..'/sounds.lua')()
    self.sounds = f_sounds
    
    local f_groups = FS.load(path ..'/groups.lua')()
    self.groups = f_groups
    
    local f_extra = nil
    if FS.isFile(path ..'/extra.lua') then
        f_extra = FS.load(path ..'/extra.lua')()       -- {sounds={}, groups={}}
    end
    -- overwrite sounds
    if f_extra and f_extra.sounds then
        for k,v in pairs(f_extra.sounds) do
            self.sounds[k] = v
        end
    end

    for id, sd in pairs(self.sounds) do
        self.sound_extras[id] = {}
    end
    
    -- append, alias or overwrite groups
    if f_extra and f_extra.groups then
        for k,v in pairs(f_extra.groups) do
            if v.append then
                if v.sounds then                    
                    for _,s in pairs(v.sounds) do
                        local sound = self.sounds[s] 
                        for _,f in pairs(sound.files) do
                            if not table.contains(self.groups[k].files, f) then 
                                table.insert(self.groups[k].files, f)
                            end
                        end
                    end
                end
                if v.files then
                    for _,f in pairs(v.files) do
                        if not table.contains(self.groups[k].files, f) then
                            table.insert(self.groups[k].files, f)
                        end
                    end
                end
            elseif v.alias then
                self.groups[k] = self.groups[v.alias]
            else
                self.groups[k] = v
            end
        end
    end

    -- platform specific
    --if KR_PLATFORM == 'ios' then
    --    local ffi = require 'ffi'
    --    ffi.cdef("bool kr_init_audio(const char* session_mode);")
    --    ffi.C.kr_init_audio("AVAudioSessionCategoryAmbient");
    --end
end

function sound_db:queue_load_done()
    if not self.load_queue_current and #self.load_queue == 0 then
        self.progress = 1
        self.groups_total = 0
        return true
    end

    ::next_group::
    if not self.co then
        --self.co = coroutine.create(self.load_sounds)
        self.co = coroutine.create(self.load_group)        
    end

    if self.load_queue_current then
        --local path, name = unpack(self.load_queue_current)
        local name = self.load_queue_current
        -- log.debug('resuming %s/%s', path, name)
        --local suc, err = coroutine.resume(self.co,self,path,name,true)
        local suc, err = coroutine.resume(self.co,self,name,true)
        if coroutine.status(self.co) ~= 'dead' and err == nil then
            self.progress = (self.groups_done + self.group_progress) / self.groups_total
            return false
        end
        -- group done
        if err ~= nil then
            log.error('Error in sound_db load coro: %s', debug.traceback(self.co, err))
        end
        self.co = nil
        self.groups_done = self.groups_done + 1
        log.debug('group %s done (%d/%d)', name, self.groups_done, self.groups_total)
    end

    if #self.load_queue > 0 then
        self.load_queue_current = table.remove(self.load_queue, 1)
        goto next_group
    end

    log.debug('sound queue loaded')
    self.load_queue_current = nil
    self.progress = 1
    self.groups_total = 0
    return true
end

function sound_db:queue_load_group(name)
    log.debug('queued %s', name)
    table.insert(self.load_queue, name)
    self.groups_total = self.groups_total + 1
    if #self.load_queue == 1 and not self.load_queue_current  then
        self.progress = 0
        self.groups_done = 0
    end
end

function sound_db:load_group(name, yielding, filter)
    local rt_start = love.timer.getTime()
    log.debug('loading sound group %s',name)

    if not self.groups[name] then
        log.error('sound group %s not found', name)
        return
    end
    
    if self.sounds_uses[name] then
        self.sounds_uses[name] = self.sounds_uses[name] + 1
        log.debug('sounds %s already loaded', name)
        return
    end
    self.sounds_uses[name] = 1
    self.group_progress = 0

    
    -- prepare files to load
    local files = {}
    local group = self.groups[name]
    if group.files then
        for _,f in pairs(group.files) do
            table.insert(files, {f, group.stream or false})
        end
    end
    if group.sounds then 
        -- solve sounds
        for _,s in pairs(self.groups[name].sounds) do
            log.debug('   getting sound %s from group %s', s,name)
            local sound = self.sounds[s]
            for _,f in pairs(sound.files) do
                table.insert(files, {f, sound.stream or false})
            end
        end
    end
    --if #sounds == 0 then
    --    log.debug('all sounds already loaded... skipping.')
    --    goto skip
    --end

    if _THREADS_ENABLED then
        -- threaded version

        -- create threads
        local load_threads = {}
        local th_i = 1           
        for i=1,_MAX_THREADS do
            local th = love.thread.newThread(_LOAD_AUDIO_THREAD_CODE)
            local cin = love.thread.newChannel()
            local cout = love.thread.newChannel()            
            th:start(cin,cout,i)
            if love.nx then
                th:setAffinity({false,true,true})
                log.paranoid(' ++++ SOUND_DB THREAD %s AFFINITY %s', th, getdump(th:getAffinity()))
            end
            table.insert(load_threads, {th,cin,cout})
        end

        -- queue files
        for _, item in pairs(files) do
            local fn,stream = unpack(item)
            local mode = self.global_source_mode  or (stream and 'stream') or 'static'
            if self.sources[fn] then
                self.source_uses[fn] = self.source_uses[fn] + 1
            else
                local file = string.format(self.files_path .. '/%s', fn)
                for _,ov in pairs(self.overrides) do
                    local f = string.format('%s/_ov/%s/%s',self.files_path , ov, fn)
                    if love.filesystem.isFile(f) then
                        file = f
                        break
                    end
                end
                local cin = load_threads[th_i][2]
                cin:push(file)                    
                cin:push(mode)
                cin:push(fn)
                th_i = km.zmod(th_i+1,#load_threads)                    
            end
        end
        
        -- queue kill threads
        for _,item in pairs(load_threads) do
            item[2]:push('QUIT')
        end

        -- get results back
        local yield_every = 0
        while #load_threads > 0 do
            local th,cin,cout = unpack(load_threads[1])
            if th:isRunning() then
                local result = cout:pop()
                if result then
                    local r1,r2,r3 = unpack(result)                
                    if r1 == 'DONE' then
                        --  thread finished
                        table.remove(load_threads,1)
                    elseif r1 == 'ERROR' then 
                        log.error("Failed to create audio source for file: %s. Error: %s", r3, r2)
                    elseif r1 == 'OK' then 
                        local fn,master_src = r3,r2
                        self.sources[fn] = { master_src }     -- A pool always contains at least one source.
                        self.source_uses[fn] = 1
                    end
                end
            else
                log.error('Thread error:%s', th:getError())
                table.remove(load_threads,1)
            end
            yield_every = yield_every + 1
            if yielding and yield_every == 1000 then
                -- TODO: group_progress 
                yield_every = 0
                coroutine.yield()
            end
        end                
        load_threads = nil
        collectgarbage() -- force threads to be released not to exceed the max of 96 in NX
        collectgarbage() -- second time needed for resurrection (PIL 3rd 17.6 Finalizers)

    else
        -- main thread version
        local yield_every = 0        
        for _, item in pairs(files) do
            local fn,stream = unpack(item)
            local mode = self.global_source_mode  or (stream and 'stream') or 'static'
            if self.sources[fn] then
                self.source_uses[fn] = self.source_uses[fn] + 1
            else
                local file = string.format(self.files_path .. '/%s', fn)
                local override_found = false
                for _,ov in pairs(self.overrides) do
                    local f = string.format(self.files_path ..'/_ov/%s'.. '/%s',ov, fn)
                    if love.filesystem.isFile(f) then
                        f = file
                        override_found = true
                        break
                    end
                end
                if override_found or love.filesystem.isFile(file) then                     
                    local ok, master_src = pcall(love.audio.newSource, file, mode)  -- newSource(file, ['stream'|'static']])
                    if ok and master_src then
                        -- source_pool is an array which may contain several cloned sources for the same clip.
                        -- This array will grow dynamically as needed. All cloned sources are backed by the same buffer.
                        self.sources[fn] = { master_src }    -- A pool always contains at least one source.
                        self.source_uses[fn] = 1
                        log.paranoid("Created audio source for %s", file)
                        collectgarbage()                      -- trigger cleanup of temporary audio data from native heap
                    else
                        -- log.warning("Failed to create audio source for file: %s. Error: %s", file, master_src)
                    end
                end
            end
            yield_every = yield_every + 1
            if yielding and yield_every == 1000 then
                -- TODO: group_progress
                yield_every = 0
                coroutine.yield()
            end            
        end
    end

    log.debug('Done loading sounds from group %s - time: %s', name, love.timer.getTime() - rt_start)

    ::skip::
    self.group_progress = 1
    --sound_db.sounds = table.merge(sound_db.sounds, sounds, true)
    
end

function sound_db:unload_group(name)
    if not self.sounds_uses[name] then
        log.error('sound group %s not loaded. cannot unload', name)
        return
    end
    self.sounds_uses[name] = self.sounds_uses[name] - 1
    if self.sounds_uses[name] > 0 then
        log.debug('sound group %s still in use', name)
        return
    end

    local group = self.groups[name]
    if group.keep then
        log.debug('sound group %s has keep flag. skipping unload', name)
        return
    end
    
    log.debug('unloading sound group %s', name)
    self.sounds_uses[name] = nil

    -- prepare files to unload
    local files = {}
    if group.files then 
        for _,f in pairs(group.files) do
            table.insert(files, {f, group.stream or false})
        end
    end
    if group.sounds then 
        -- solve sounds
        for _,s in pairs(self.groups[name].sounds) do
            local sound = self.sounds[s]
            for _,f in pairs(sound.files) do
                table.insert(files, {f, sound.stream or false})
            end
        end
    end
    
    for _,item in pairs(files) do
        local fn,stream = unpack(item)
        if self.sources[fn] then
            for _,s in pairs(self.sources[fn]) do
                s:stop()                
            end
            self.source_uses[fn] = self.source_uses[fn] - 1
            if self.source_uses[fn] < 1 then
                self.sources[fn] = nil
                self.source_uses[fn] = 0
                log.paranoid('  sound source deleted: %s', fn)
            else
                log.debug('  sound source %s still used by %s groups', fn, self.source_uses[fn])
            end
        end
    end
    
    collectgarbage()
end

sound_db.request_queue = {}
function sound_db:queue(id, options)
    if not id then return end
    if not self.sounds[id] then
        log.error('SOUND WITH ID %s NOT FOUND', tostring(id))
        return
    end
    local opts
    if options then
        -- passed options override original sound definitions params
        opts = table.merge(self.sounds[id], options, true)  -- returns a NEW table
    else
        opts = self.sounds[id]                              -- the usual case, avoid creating a table
    end
    local req = {id=id, options= opts, qts= self.ts}
    table.insert(self.request_queue, req)
end

sound_db.stop_queue = {}
function sound_db:stop(id)
    if id then 
        local opts = sound_db.sounds[id]
        if opts and (opts.loop or sound_db.sounds[id].interruptible) then
            log.paranoid('Sound %s stopping queued', id)
            local stop_req = {id = id, gid = nil}
            table.insert(sound_db.stop_queue, stop_req)
        else
            log.paranoid('Sound %s not interruptible nor loopable. Ignoring stop request.', id)
        end
    end
end

function sound_db:stop_group(gid)
    log.paranoid('Stopping sources for group: %s', gid)
    if gid then
        log.paranoid('Group %s stopping queued', gid)
        local stop_req = {id = nil, gid = gid}
        table.insert(sound_db.stop_queue, stop_req)
    end
end

function sound_db:stop_all()
    LA.stop()
    -- reset refcounters
    self.ref_counters = {}
end

function sound_db:pause()
    self.paused = true
    for gid, group_active_sources in pairs(self.active_sources) do
        for _, ast in pairs(group_active_sources) do
            ast.source:pause()
        end
    end
end

function sound_db:resume()
    self.paused = false
    for gid, group_active_sources in pairs(self.active_sources) do
        for _, ast in pairs(group_active_sources) do
            ast.source:resume()
        end
    end
end

function sound_db:sound_is_playing(id)
    local sd = sound_db.sounds[id]
    if sd then
        local gid = sd.source_group
        for _, ast in pairs(self.active_sources[gid]) do
            if ast.id == id then
                return true
            end
        end
    else
        log.error('No such sound id: %s', id)
    end
    return false
end

function sound_db:set_main_gain_fx(gain)
    local fx_groups = {
        BULLETS    = gain,
        DEATH      = gain,
        EXPLOSIONS = gain,        
        GUI        = gain,
        SFX        = gain,
        SPECIALS   = gain,
        SWORDS     = gain,
        TAUNTS     = gain,
        --
        REFCOUNTED = gain,
    }
    sound_db:set_groups_gains(fx_groups)
end

function sound_db:set_main_gain_music(gain)
    sound_db:set_groups_gains({MUSIC = gain})
end

function sound_db:fade_in_group(group,duration)
    local gv = self:get_group_volume(group)
    local gg = self:get_group_gain(group)
    --log.todo('fade in group %s volume : %s gain:%s', group, gv, gg)
    if gg == gv then
        return
    end
    self.fade_queue = self.fade_queue or {}
    self.fade_queue[group] = self.fade_queue[group] or {}
    local fq = self.fade_queue[group]
    if fq and fq.type == 'in' then
        -- already in progress
        --log.debug('fade in for %s already in progress', group)
        return
    end
    fq.type = 'in'
    fq.ts=self.ts
    fq.duration = duration or 1
    fq.v0 = gv
    fq.v1 = gg
    log.debug('fading in %s to %s', group, fq.v1)
end

function sound_db:fade_out_group(group,duration)
    local gv = self:get_group_volume(group)
    --log.todo('fade out group %s volume : %s', group, gv)
    if gv == 0 then
        return
    end
    self.fade_queue = self.fade_queue or {}
    self.fade_queue[group] = self.fade_queue[group] or {}
    local fq = self.fade_queue[group]
    if fq and fq.type == 'out' then
        -- already in progress
        --log.debug('fade out for %s already in progress', group)
        return
    end
    fq.type = 'out'
    fq.ts=self.ts
    fq.duration = duration or 1
    fq.v0 = gv
    fq.v1 = 0
    log.debug('fading out %s to %s', group, fq.v1)
end

-- Set audio gains for sources groups specified in the `ggs'
-- table. `ggs' is a table with group_ids as keys, and corresponding
-- gains as values.
-- If the optional `active_sources' argument is missing, the new gains
-- will apply ONLY to future source:play() calls.  When a table with
-- active sources is provided, these sources gains will be updated
-- according to their respective group gains immediately.
function sound_db:set_groups_gains(ggs)
    local active_sources = self.active_sources
    local group_gains = sound_db.group_gains
    for gid, gain in pairs(ggs) do
        group_gains[gid] = gain                          -- update gains for future source:play() calls
        if active_sources and active_sources[gid] then 
            for _, ast in pairs(active_sources[gid]) do 
                ast.source:setVolume(ast.ref_vol * gain) -- update currently playing sources if necessary
            end
        end
    end
end

function sound_db:get_group_gain(group)
    return sound_db.group_gains[group] or 0.8
end

function sound_db:get_group_volume(group,id)
    local active_sources = self.active_sources
    if active_sources and active_sources[group] then
        if active_sources[group][id] then
            return active_sources[group][id].source:getVolume()
        else
            for _,ast in pairs(active_sources[group]) do
                return km.clamp(0,1,ast.source:getVolume()/(ast.ref_vol or 1))
            end
        end
    end
    return 0
end

function sound_db:set_groups_volumes(ggs)
    local active_sources = self.active_sources
    for gid, gain in pairs(ggs) do
        if active_sources and active_sources[gid] then 
            for _, ast in pairs(active_sources[gid]) do 
                ast.source:setVolume(km.clamp(0,1,ast.ref_vol * gain)) -- update currently playing sources if necessary
            end
        end
    end
end

function sound_db:_stop_sources(req)
    log.paranoid('Stoping sources for stop request {id=%s, gid=%s}', req.id, req.gid)

    -- is this sound ref counted?
    if req.id and self.sounds[req.id].ref_counted then
        local rc = self.ref_counters[req.id] or 0
        rc = rc - 1
        self.ref_counters[req.id] = rc        
        log.paranoid('sound %s refcount is now %d', req.id, rc)
        if rc > 0 then
            return false
        end
    end

    -- Stop the actual sources
    for gid, group_active_sources in pairs(self.active_sources) do
        for _, ast in pairs(group_active_sources) do
            if gid == req.gid or ast.id == req.id then
                log.debug('about to stop source %s (ID = %s)', ast.source, id)
                ast.source:stop()
            end
        end
    end
end

function sound_db:update(dt)
    self.ts = self.ts + dt
    
    -- process queued stop requests
    for i=#sound_db.stop_queue, 1, -1 do
        -- The usual case, stop active sources
        local stop_request = sound_db.stop_queue[i]
        self:_stop_sources(stop_request, self.active_sources)

        -- Also: if a play request was queued, cancel playback by
        -- removing it from playback queue.
        -- This applies only to non-refcounted sounds. 
        if not self.ref_counters[stop_request.id] then
            for j=#sound_db.request_queue, 1, -1 do
                if sound_db.request_queue[j].id == stop_request.id then
                    table.remove(sound_db.request_queue, j)
                end
            end
        end
        table.remove(sound_db.stop_queue, i)
    end

    -- some sources may have finished playing, remove them from active sources list
    for gid, group_active_sources in pairs(self.active_sources) do   
        for i=#group_active_sources, 1, -1 do       -- housekeeping: reclaim stopped sources
            if group_active_sources[i].source:isStopped() then
                log.paranoid('Reclaiming source %s', group_active_sources[i].source)
                table.remove(group_active_sources, i)
            end
        end
    end    
    local queue = sound_db.request_queue
    local reqs_due = {}
    for i=#queue, 1, -1 do
        local req = queue[i]
        if not req.options.delay or (self.ts - req.qts >= req.options.delay) then
            self:play(req)          -- playback request is due
            table.remove(queue, i)
        end
    end

    -- fade in/out
    if self.fade_queue then
        local remove_groups = {}
        for g,fq in pairs(self.fade_queue) do
            local gg = self:get_group_gain(group)
            local ts = self.ts
            if fq.ts + fq.duration < self.ts then
                -- finished, remove from queue
                self:set_groups_volumes({[g]=fq.v1})
                table.insert(remove_groups, g)
                log.debug('finished fading %s : %s->%s', g, fq.v0, fq.v1)
            else
                -- still in progress, interpolate gain
                local phase = (ts - fq.ts) / fq.duration
                --local v = fq.v0 + (fq.v1 - fq.v0) * phase
                local v = km.clamp(0,1,km.ease_value(fq.v0, fq.v1, phase, fq.type == 'in' and 'cubic-out' or 'cubic-in'))
                self:set_groups_volumes({[g]=v})
                --log.debug('fading %s : %s->%s at %s', g, fq.v0, fq.v1, v)
            end            
        end
        for _,g in pairs(remove_groups) do
            self.fade_queue[g] = nil
        end
    end
end

function sound_db:play(request)
    -- NOTE: request is a structure. Use queue() for sound name strings
    local options = request.options
    local se = self.sound_extras[request.id]
    local last_play_ts = se.last_play_ts or 0
    local every_counter = se.every_counter or 0 -- only for sounds with 'every' option
    local play_due = true

    -- chance
    if options.chance and math.random() >= options.chance then
        return
    end
    
    -- options.every count
    if options.every then
        if every_counter ~= 0 then              -- a modulus-every counter, only
            play_due = false                    -- not yet, abort this request
            log.paranoid('%s plays every %d requests. (remaining: %d )',
                         request.id, options.every, options.every-every_counter)
        end
        every_counter = (every_counter+1) % options.every
        se.every_counter = every_counter
    end

    -- options.ignore time
    if options.ignore and (self.ts - last_play_ts < options.ignore) then    -- if ignore time option is set, the sound will play ONLY when enough
        play_due = false                                                    -- time elapsed since the the last playback. 
    end

    -- options.ref_counted 
    if options.ref_counted then
        local rc = self.ref_counters[request.id] or 0
        rc = rc + 1
        if rc ~= 1 then
            play_due = false
        end
        self.ref_counters[request.id] = rc 
    end

    -- source pool
    local pools = {}
    if options.mode == 'sequence' then
        table.insert(pools,self.sources[options.files[se.sequence or 1]])
        se.sequence = ((se.sequence or 1) % #options.files) + 1
    elseif options.mode == 'random' then
        table.insert(pools,self.sources[options.files[math.random(1,#options.files)]])
    elseif options.mode == 'concurrent' then
        -- play all of them
        for _,f in pairs(options.files) do
            table.insert(pools,self.sources[f])
        end
    else
        table.insert(pools,self.sources[options.files[1]])         -- the usual case, only one clip per sound.
    end
    if not pools or #pools == 0 then
        log.error('SOUND %s defined but sound sources missing. Missing file during load?', request.id)
        return 
    end
    
    -- playback
    if play_due then
        -- ok, it's actually time to play the sound
        log.paranoid('TTP: %s', request.id)
        for _,sp in pairs(pools) do
            self:_play(request,sp)
        end
        se.last_play_ts = self.ts                 -- update the last playback time
    end
end

--------------------------------------------------------------------------
local function soon_to_stop_source(group_active_sources)
    local mtp = 1e9
    local pos = 1
    -- find the playing source which is closest to finish playing
    for i,ast in ipairs(group_active_sources) do
        local remaining = ast.source:getDuration() - ast.source:tell()   -- remaining time
        if remaining < mtp then
            mtp = remaining
            pos = i
        end
    end
    return pos
end

local function get_or_create_source(source_pool)
    local source
    for i=1,#source_pool do   -- try to find a free playing source in the pool
        source = source_pool[i]
        if source:isStopped() then
            log.paranoid('Found free source at %d', i)
            break
        else
            source = nil
        end
    end
    if not source then      -- no stopped sources found in the pool, create a new one
        log.paranoid("No free sources in pool %s. Creating a new one", tostring(source_pool))
        source = source_pool[1]:clone()
        table.insert(source_pool, source)
    end
    return source
end

function sound_db:_play(request,source_pool)
    local opts  = request.options
    local se = self.sound_extras[request.id]
    local active_sources = self.active_sources
    
    -- How we find a source where to play this request:
    -- First, check how many sources are currently playing in the relevant group.
    -- If still below the group max, it is ok to play this request: get or create a new source for it from the pool.
    -- Else (the group maxed out), find a playing source in this group and stop it. It is now ok to play this request.
    -- This is the way the CocosDenshion Sound Engine used in the original KR behaves!
    
    if not active_sources[opts.source_group] then
        log.error('SOUND %s group %s not found', request.id, opts.source_group)
        return
    end
    
    local active = #active_sources[opts.source_group]
    local max = self.source_groups[opts.source_group].max_sources
    local source
    if max == 0 then
        log.info('max_sources for %s is 0', opts.source_group)
        return
    end
    if active < max then
        source = get_or_create_source(source_pool)
    else
        -- The group maxed out. We must evict a currently playing source from this group.
        log.paranoid("No free sources for %s. Source group %s maxed out at %d.",
                     request.id, opts.source_group, max)
        local ste_idx = soon_to_stop_source(active_sources[opts.source_group])               -- Find a playing source to evict, ste_idx is the index
        local ste_ast = active_sources[opts.source_group][ste_idx]                           -- for said ast in the active_sources[o.g] array.
        log.paranoid("Stopping evicted source %s (%s) in group %s", ste_ast.id, tostring(ste_ast.source), opts.source_group)
        ste_ast.source:stop()                                                         -- Stop the source we chose to evict and remove it from active sources.
        table.remove(active_sources[opts.source_group], ste_idx)

        -- We just evicted a playing source so it's now ok to play this sound
        -- get or create a source from pool
        source = get_or_create_source(source_pool)
    end

    local vol = 1.0                         -- GAIN
    if opts.gain then
        if type(opts.gain) == 'number' then
            vol = opts.gain
        elseif type(opts.gain) == 'table' then 
            local min, max = opts.gain[1], opts.gain[2]
            vol = min + (max-min) * math.random()
        end
    end

    local ref_vol = vol -- this is the reference volume for this source.
                        -- Further applied group gains will be applied to this reference volume.
    
    -- if a group gain is set for this source's group, apply it now.
    local group_gain = sound_db.group_gains[opts.source_group]
    if group_gain then
        vol = ref_vol * group_gain
    end
    source:setVolume(vol)
    source:setLooping(opts.loop)

    local success = source:play()
    if success then
        if opts.seek and type(opts.seek) == 'number' then              -- seek to specified time if required
            source:seek(opts.seek)
            if source:isPaused() then                                  -- workaround for love bug where sources can sometimes remain paused after seeking
                source:resume()
            end
        end
        local ast = { id=request.id , source=source, ref_vol=ref_vol }
        table.insert(active_sources[opts.source_group], ast)
    else
        log.error("source:play() failed! source: %s sound_id: %s", tostring(source), request.id)
    end
end

return sound_db
