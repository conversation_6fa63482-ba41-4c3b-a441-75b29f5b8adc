--
-- aux functions
--
local log = (require 'klua.log'):new('utils')
require 'klua.table'
local km = require 'klua.macros'

local bit = require 'bit'
local bor = bit.bor
local band = bit.band
local bnot = bit.bnot

local V = require 'klua.vector'
local P = require 'path_db'
local SSO = require 'klove.sso'
local GS = require 'game_settings'
require 'constants'

------------------------------------------------------------

local U = {} 

------------------------------------------------------------
-- float version of the random
function U.frandom(from,to)
    return (math.random() * (to - from) + from)
end

function U.random_sign()
    if math.random() < 0.5 then return -1 else return 1 end
end

function U.random_table_idx(list)
    -- list, where each item contains the probability of that index
        local rn = math.random()
        local acc = 0
        for i=1,#list do
            if rn <= list[i] + acc then
                return i
            end
            acc = acc + list[i]
        end
        return #list    
end

------------------------------------------------------------
-- easing

function U.y_ease_keys(store,key_tables,key_names,froms,tos,duration,easings,fn)
    local start_ts = store.tick_ts
    local phase
    easings = easings or {}
    repeat
        local dt = store.tick_ts - start_ts
        phase = km.clamp(0,1,dt/duration)
        for i,t in ipairs(key_tables) do
            local kn = key_names[i]
            t[kn] = U.ease_value(froms[i],tos[i],phase,easings[i])
        end
        if fn then fn(dt,phase) end
        coroutine.yield()
    until phase >= 1
end

function U.y_ease_key(store,key_table,key_name,from,to,duration,easing,fn)
    U.y_ease_keys(store,{key_table},{key_name},{from},{to},duration,{easing},fn)
end

function U.ease_value(from,to,phase,easing)
    return km.ease_value(from,to,phase,easing)
end

function U.ease_phase(phase,easing)
    return km.ease_phase(phase,easing)
end

function U.hover_pulse_alpha(t)
    local min,max,per = HOVER_PULSE_ALPHA_MIN, HOVER_PULSE_ALPHA_MAX, HOVER_PULSE_PERIOD
    return min + (max-min) * 0.5 * ( 1 + math.sin(t * km.twopi / per) )
end

------------------------------------------------------------
-- ellipse

function U.is_inside_ellipse_old(p,center,radius,aspect)
    if not aspect then aspect = 0.7 end
    local a = radius
    local b = radius * aspect
    return ( math.pow((p.x - center.x) / a, 2) + math.pow((p.y - center.y) / b, 2) <= 1 )
end

function U.is_inside_ellipse(p,center,radius,aspect)
    if not aspect then aspect = 0.7 end
    local dx = p.x - center.x
    local dy = p.y - center.y
    local aspect2 = aspect * aspect
    local dx2 = dx * dx
    local dy2 = dy * dy
    local r2 = radius * radius
    return (aspect2 * dx2 + dy2) <= (r2 * aspect2)
end

function U.point_on_ellipse(center,radius,angle,aspect)
    if not aspect then aspect = 0.7 end
    if not angle then angle = 0 end
    local a = radius
    local b = radius * aspect
    return V.v( center.x + a * math.cos(angle), center.y + b * math.sin(angle) )
end

function U.dist_factor_inside_ellipse(p,center,radius,min_radius,aspect)
    -- result goes from 0 at min radius to 1 at max radius
    if not aspect then aspect = 0.7 end
    local vx,vy = p.x-center.x,p.y-center.y
    local angle = V.angleTo(vx,vy)
    local a = radius
    local b = radius * aspect
    local v_len = V.len(vx,vy)
    local ab_len = V.len(a*math.cos(angle), b*math.sin(angle))
    if min_radius then        
        local ma,mb = min_radius,min_radius * aspect
        local mab_len = V.len(ma*math.cos(angle), mb*math.sin(angle))
        return km.clamp(0,1, (v_len - mab_len) / ( ab_len - mab_len ))
    else        
        return km.clamp(0,1, v_len / ab_len)
    end
end

------------------------------------------------------------
-- time

function U.y_wait(store,time,break_func)
    -- breaks if the break_function evals true, and returns true    
    local start_ts = store.tick_ts
    while store.tick_ts - start_ts < time do
        if break_func and break_func(store,time) then
            return true
        end
        coroutine.yield()
    end
    return false
end
    
------------------------------------------------------------
-- animation functions
-- TODO REFACTOR: move to script_utils

-- TODO: refactor times a loop boolean
function U.animation_start(entity, name, flip_x, ts, loop, idx, force_ts)
    -- name == nil: keep the default animation
    -- idx == nil: start all sprites
    -- loop = true - loop
    --        false - dont loop
    --        nil - use setting defined in the sprite
    -- force_ts: looping animations only reset the ts if forced
    --
    -- TODO: I'm not happy with the force_ts parameter. The previous
    -- behaviour, that resets the ts when the name changes was
    -- better. The parameter could be keep_ts to be used for those
    -- special cases like dragons.
    --
    if loop == -1 or loop == true then 
        loop = true
    else
        loop = false
    end

    local first,last
    if idx then
        first,last = idx,idx
    else
        first,last = 1,#entity.render.sprites
    end
    for i=first,last do
        local a = entity.render.sprites[i]
        if not a.ignore_start then
            local flip_x_i = flip_x
            if flip_x_i == nil then flip_x_i = a.flip_x end
            a.flip_x = flip_x_i
            if a.animated then 
                a.loop = loop or (a.loop_forced == true)
                if not a.loop or force_ts then
                    a.ts = ts
                    a.runs = 0
                end
                if (name and a.name ~= name) then -- keep on nil
                    a.name = name
                end

            end
        end
    end
end

function U.animation_finished(entity, idx, times)
    if not idx then idx = 1 end
    if not times then times = 1 end
    local a = entity.render.sprites[idx]
    if a.loop then
        if times == 1 then 
            log.debug('waiting fTor looping animation for entity %s - ', entity.id, entity.template_name)
        end
        return ( a.runs >= times )
    else
        return ( a.runs > 0 )
    end
end

function U.y_animation_wait(entity,idx,times)
    if not idx then idx = 1 end
    while not U.animation_finished(entity,idx,times) do
        coroutine.yield()
    end
end

function U.animation_name_for_angle(e, group, angle, idx)
    -- returns: animation_name, flip_x, angle_index
    --   - flip_x: true when animation should point left
    if not idx then idx = 1 end
    local a = e.render.sprites[idx]
    local angles = a.angles and a.angles[group] or nil
    angle = km.unroll(angle)  -- unroll for comparisons below

    if not angles then
        return group, ( angle > math.pi/2 and angle < 3*math.pi/2 ), 1

    elseif #angles == 1 then
        return angles[1], ( angle > math.pi/2 and angle < 3*math.pi/2 ), 1

    elseif #angles == 2 then  -- UP / DOWN
        local flip_x = angle > math.pi/2 and angle < 3*math.pi/2
        if angle > 0 and angle < math.pi then
            if a.angles_flip_horizontal and a.angles_flip_horizontal[1] then 
                flip_x = not flip_x
            end
            return angles[1], flip_x, 1
        else
            if a.angles_flip_horizontal and a.angles_flip_horizontal[2] then 
                flip_x = not flip_x
            end
            return angles[2], flip_x, 2
        end

    elseif #angles == 3 then  -- LEFT-RIGHT / UP / DOWN
        local o_name, o_flip, o_idx
        --local a1,a2,a3,a4 = 45,120,225,305 -- del original, en vez de 135
        local a1,a2,a3,a4 = 45,135,225,315 
        if a.angles_custom and a.angles_custom[group] then
            a1,a2,a3,a4 = unpack(a.angles_custom[group],1,4)
        end
        -- quadrant stickiness, to reduce random flips in diagonal paths
        local quadrant = a._last_quadrant
        local stickiness = a.angles_stickiness and a.angles_stickiness[group]
        if stickiness and quadrant then
            local skew = stickiness * ( ( quadrant == 1 or quadrant == 3 ) and 1 or -1 )
            a1,a3 = a1 - skew, a3 - skew
            a2,a4 = a2 + skew, a4 + skew
        end
        -- pick animation
        local angle_deg = angle * 180 / math.pi
        if angle_deg >= a1 and angle_deg < a2 then      -- down
            o_name,o_flip,o_idx = angles[2], false, 2
            quadrant = 1
        elseif angle_deg >= a2 and angle_deg < a3 then  -- right
            o_name,o_flip,o_idx = angles[1], true, 1
            quadrant = 2
        elseif angle_deg >= a3 and angle_deg < a4 then  -- up
            o_name,o_flip,o_idx = angles[3], false, 3
            quadrant = 3
        else                                            -- left
            o_name,o_flip,o_idx = angles[1], false, 1
            quadrant = 4
        end
        -- stickiness
        if stickiness then
            a._last_quadrant = quadrant
        end        
        -- force vertical flip
        if a.angles_flip_vertical and a.angles_flip_vertical[group] then
            o_flip = ( angle > math.pi/2 and angle < 3*math.pi/2 )
        end        
        --assert( o_name ~= nil, string.format('animation_name_for_angle name nil for entity (%s) - %s, group:%s, angle:%s, idx:%s', e.id, e.template_name, group, angle, idx))
        return o_name,o_flip,o_idx
    end
end

function U.animation_name_facing_point(e, group, point, idx, offset, use_path)
    -- use_path: instead of current pos, use the node position
    local fx,fy
    if e.nav_path and use_path then
        local npos = P:node_pos(e.nav_path,nil,nil,true)
        fx,fy = npos.x,npos.y
    else
        fx,fy = e.pos.x,e.pos.y
    end
    if offset then
        fx,fy = fx + offset.x,fy + offset.y
    end
    local vx,vy = V.sub(point.x,point.y, fx,fy)
    local v_angle = V.angleTo(vx,vy)
    local angle = km.unroll(v_angle)
    --log.debug('from:%.2f,%.2f to:%.2f,%.2f  v:%.2f,%.2f  angle:%.2f', fx,fy, point.x,point.y, vx,vy, angle)    
    return U.animation_name_for_angle(e,group,angle,idx)
end

function U.y_animation_play(entity, name, flip_x, ts, times, idx)
    -- WARNING: for times > 1 it will leave the animation looping.
    local loop = (times and times > 1)
    U.animation_start(entity, name, flip_x, ts, loop, idx, true) -- force_ts
    while not U.animation_finished(entity, idx, times) do
        coroutine.yield()
    end
end

function U.animation_start_group(entity, name, flip_x, ts, loop, group)
    if not group then
        U.animation_start(entity, name, flip_x, ts, loop)
        return
    end
    for i=1,#entity.render.sprites do
        local s = entity.render.sprites[i]
        if s.group == group then
            U.animation_start(entity, name, flip_x, ts, loop, i)
        end
    end
end

function U.animation_finished_group(entity, group, times)
    if not group then
        return U.animation_finished(entity, nil, times)
    end
    for i=1,#entity.render.sprites do
        local s = entity.render.sprites[i]
        if s.group == group then
            if U.animation_finished(entity, i, times) then
                return true
            end
        end
    end
end

function U.y_animation_play_group(entity, name, flip_x, ts, times, group)
    if not group then
        U.y_animation_play(entity, name, flip_x, ts, times)
        return
    end
    local loop = (times and times > 1)
    U.animation_start_group(entity, name, flip_x, ts, loop, group)
    local idx = nil
    for i=1,#entity.render.sprites do
        local s = entity.render.sprites[i]
        if s.group == group then
            idx = i
            break
        end
    end
    if idx then 
        while not U.animation_finished(entity, idx, times) do
            coroutine.yield()
        end
    end
end

function U.y_animation_wait_group(entity,group,times)
    if not group then
        U.y_animation_wait(entity,nil,times)
        return 
    end
    for i=1,#entity.render.sprites do
        local s = entity.render.sprites[i]
        if s.group == group then
            U.y_animation_wait(entity,i,times)
            break
        end
    end
end

function U.get_animation_ts(entity,group)
    if not group then
        return entity.render.sprites[1].ts
    else
        for i=1,#entity.render.sprites do
            local s = entity.render.sprites[i]
            if s.group == group then
                return s.ts
            end
        end
    end
end

function U.sprites_hide(entity,from,to,keep)
    if not entity or not entity.render then return end
    if not from then from = 1 end
    if not to then to = #entity.render.sprites end
    for i=from,to do
        local s = entity.render.sprites[i]
        if keep then
            -- sanity checks
            if s.hidden and s.hidden_count == 0 then
                -- if already hidden, increment by one
                s.hidden_count = 1
            end
            if not s.hidden and s.hidden_count > 0 then
                -- if not hidden, reset counter
                s.hidden_count = 0
            end
            s.hidden_count = s.hidden_count + 1
        end
        s.hidden = true
    end
end

function U.sprites_show(entity,from,to,restore)
    if not entity or not entity.render then return end
    if not from then from = 1 end
    if not to then to = #entity.render.sprites end
    for i=from,to do        
        local s = entity.render.sprites[i]
        if restore then
            s.hidden_count = math.max(0, s.hidden_count - 1)
            s.hidden = (s.hidden_count > 0)
        else
            s.hidden_count = 0
            s.hidden = nil
        end
    end
end

------------------------------------------------------------
-- movement
-- TODO REFACTOR: move to script_utils

function U.set_destination(e,pos)
    e.motion.dest = V.vclone(pos)
    e.motion.arrived = false
end

function U.set_heading(e,dest)
    if e.heading then
        local vx,vy = V.sub(dest.x,dest.y, e.pos.x,e.pos.y)        
        local v_angle = V.angleTo(vx,vy)
        e.heading.angle = v_angle
    end
end

function U.walk(e,dt,accel,unsnapped)
    -- TODO: unify accel param with motion.accel
    -- accel: acceleration towards destination (careful, modifies entity max_speed)
    -- unsnapped: do not snap on arrival, just advance what it would normally do
    -- returns: true if arrived, false if on route
    if e.motion.arrived then
        return true
    end    
    local m = e.motion
    local pos = e.pos
    local vx,vy = V.sub(m.dest.x,m.dest.y, pos.x,pos.y)        
    local v_angle = V.angleTo(vx,vy)
    local v_len = V.len(vx,vy)

    if accel then m.max_speed = m.max_speed + accel * dt end
    if m.accel and m.speed_limit and m.max_speed < m.speed_limit then
        m.max_speed = km.clamp(0,m.speed_limit, m.max_speed + m.accel * dt)
    end
    local step = m.max_speed * dt
    local nx,ny = V.normalize(V.rotate(v_angle,1,0))
    
    if v_len <= step then
        if unsnapped then
            -- move the full step
            local sx,sy = V.mul(step,nx,ny)
            pos.x,pos.y = V.add(pos.x,pos.y, sx,sy)
        else
            -- snap to destination
            pos.x,pos.y = m.dest.x,m.dest.y
        end
        m.speed.x,m.speed.y = 0,0
        m.arrived = true
        return true
    end

    -- heading when far enough
    if e.heading then 
        e.heading.angle = v_angle
    end
    
    -- move clamping to v_len 
    local sx,sy = V.mul(math.min(step,v_len),nx,ny)
    pos.x,pos.y = V.add(pos.x,pos.y, sx,sy)
    m.speed.x,m.speed.y = sx/dt, sy/dt
    m.arrived = false
    return false
end

function U.force_motion_step(this,dt,dest)
    local fm = this.force_motion
    local dx,dy = V.sub(dest.x,dest.y, this.pos.x,this.pos.y)
    local dist = V.len(dx,dy)
    local ramp_radius = fm.ramp_radius
    local df
    if not ramp_radius then
        df = 1
    elseif dist > ramp_radius then
        df = fm.ramp_max_factor
    else
        df =  math.max( dist / ramp_radius, fm.ramp_min_factor )
    end
    fm.a.x,fm.a.y = V.add(fm.a.x,fm.a.y, V.trim(fm.max_a, V.mul(fm.a_step * df, dx,dy)))
    --log.error('  >> ---------')
    --log.error('  >> raw a:%s,%s', fm.a.x,fm.a.y)
    fm.v.x,fm.v.y = V.add(fm.v.x,fm.v.y, V.mul(dt, fm.a.x,fm.a.y))
    --log.error('  >> raw v:%s,%s', fm.v.x,fm.v.y)
    fm.v.x,fm.v.y = V.trim(fm.max_v, fm.v.x,fm.v.y)
    --log.error('  >> clamped a:%s,%s v:%s,%s', fm.a.x,fm.a.y,fm.v.x,fm.v.y)
    this.pos.x,this.pos.y = V.add(this.pos.x,this.pos.y, V.mul(dt, fm.v.x,fm.v.y))
    -- kind of dynamic friction
    fm.a.x,fm.a.y = V.mul( -1 * fm.fr / dt, fm.v.x,fm.v.y)    
end

------------------------------------------------------------
-- targeting
-- TODO REFACTOR: move to script_utils

-- NOTE/REFACTOR: it was NOT a good idea to return nil because adds
-- always the extra not nil test before the for loop. An empty table
-- would have been better for the functions that return a list, in
-- contrast with the find_nearest_x or find_foremost_x that are OK
-- returning nil.


-- ------------------------------
-- soldiers

function U.find_nearest_soldier(entities, origin, min_range, max_range, flags, bans, filter_func)
    local soldiers = U.find_soldiers_in_range(entities,origin,min_range,max_range,flags,bans,filter_func)
    if not soldiers or #soldiers == 0 then
        return nil
    else
        table.sort(
            soldiers,
            function(e1,e2)
                return ( V.dist(e1.pos.x,e1.pos.y, origin.x,origin.y) <
                         V.dist(e2.pos.x,e2.pos.y, origin.x,origin.y) )
            end
        )
        return soldiers[1]
    end
end

function U.find_soldiers_in_range(entities, origin, min_range, max_range, flags, bans, filter_func)
    local l_entities = entities
    local soldiers
    if SSO and SSO:is_all_entities(entities) then
        soldiers = {}
        SSO:filter(soldiers, 'targets', origin.x,origin.y, max_range,
                       function(k,v)
                           return (
                               v.soldier and not v.health.dead and
                               band(v.vis.flags, bans) == 0 and
                               band(v.vis.bans, flags) == 0 and
                               U.is_inside_ellipse(v.pos, origin, max_range) and
                               ( min_range == 0 or not U.is_inside_ellipse(v.pos, origin, min_range) ) and
                               ( not filter_func or filter_func(v,origin) )
                           )
                       end
        )

    else    
        soldiers = table.filter(
            l_entities,
            function(k,v)
                return (
                    not v.pending_removal and 
                    v.soldier and v.vis and 
                    v.health and not v.health.dead and
                    band(v.vis.flags, bans) == 0 and
                    band(v.vis.bans, flags) == 0 and
                    U.is_inside_ellipse(v.pos, origin, max_range) and
                    ( min_range == 0 or not U.is_inside_ellipse(v.pos, origin, min_range) ) and
                    ( not filter_func or filter_func(v,origin) )
                )
            end
        )
    end
    if not soldiers or #soldiers == 0 then
        return nil
    else
        return soldiers
    end
end

-- ------------------------------
-- targets

function U.find_targets_in_range(entities, origin, min_range, max_range, flags, bans, filter_func)
    -- flags and bans: bitfield for attack
    -- filter_func: function that returns false if entity is to be discarded
    local targets
    if SSO and SSO:is_all_entities(entities) then
        targets = {}
        SSO:filter(targets, 'targets', origin.x,origin.y, max_range,
                       function(k,v)
                           return (
                               (v.enemy or v.soldier) and
                               not v.health.dead and
                               band(v.vis.flags, bans) == 0 and
                               band(v.vis.bans, flags) == 0 and
                               U.is_inside_ellipse(v.pos, origin, max_range) and
                               ( not v.nav_path or P:is_node_valid(v.nav_path.pi, v.nav_path.ni) ) and
                               ( min_range == 0 or not U.is_inside_ellipse(v.pos, origin, min_range) ) and
                               ( not filter_func or filter_func(v,origin) )
                           )
                       end
        )
    else
        targets = table.filter(
        entities,
        function(k,v)
            return (
                    not v.pending_removal and 
                    v.vis and (v.enemy or v.soldier) and
                    v.health and not v.health.dead and
                    band(v.vis.flags, bans) == 0 and
                    band(v.vis.bans, flags) == 0 and
                    U.is_inside_ellipse(v.pos, origin, max_range) and
                    ( not v.nav_path or P:is_node_valid(v.nav_path.pi, v.nav_path.ni) ) and
                    ( min_range == 0 or not U.is_inside_ellipse(v.pos, origin, min_range) ) and
                    ( not filter_func or filter_func(v,origin) )
            )
        end
        )
    end
    if not targets or #targets == 0 then
        return nil
    else
        return targets
    end    
end

function U.find_first_target(entities, origin, min_range, max_range, flags, bans, filter_func)
    -- first target that matches is returing, to avoid processing all of them
    -- filter_func: function that returns false if entity is to be discarded
    if not flags then flags = 0x0 end
    if not bans then bans = 0x0 end
    local l_entities = entities
    if SSO and SSO:is_all_entities(entities) then
        -- pre-filter
        l_entities = {}
        SSO:filter(l_entities, 'targets', origin.x,origin.y, max_range)
    end
    for _,v in pairs(l_entities) do
        if ( not v.pending_removal and 
             v.health and not v.health.dead and
             v.vis and 
             band(v.vis.flags, bans) == 0 and
             band(v.vis.bans, flags) == 0 and
             U.is_inside_ellipse(v.pos, origin, max_range) and
             (min_range == 0 or not U.is_inside_ellipse(v.pos, origin, min_range)) and
             ( not filter_func or filter_func(v,origin) ) )
        then
            return v
        end
    end
    return nil
end

function U.find_random_target(entities, origin, min_range, max_range, flags, bans, filter_func)
    -- flags and bans: bitfield for attack
    -- filter_func: function that returns false if entity is to be discarded
    if not flags then flags = 0x0 end
    if not bans then bans = 0x0 end
    local targets
    if SSO and SSO:is_all_entities(entities) then
        targets = {}
        SSO:filter(targets, 'targets', origin.x,origin.y, max_range,
                       function(k,v)
                           return (
                               v.health and not v.health.dead and
                               band(v.vis.flags, bans) == 0 and
                               band(v.vis.bans, flags) == 0 and
                               U.is_inside_ellipse(v.pos, origin, max_range) and
                               ( min_range == 0 or not U.is_inside_ellipse(v.pos, origin, min_range) ) and
                               ( not filter_func or filter_func(v,origin) )
                           )
                       end
        )
    else
        targets = table.filter(
            entities,
            function(k,v)
                return (
                    not v.pending_removal and 
                    v.health and not v.health.dead and
                    v.vis and 
                    band(v.vis.flags, bans) == 0 and
                    band(v.vis.bans, flags) == 0 and
                    U.is_inside_ellipse(v.pos, origin, max_range) and
                    (min_range == 0 or not U.is_inside_ellipse(v.pos, origin, min_range)) and
                    ( not filter_func or filter_func(v,origin) )
                )
            end
        )
    end
    if not targets or #targets == 0 then
        return nil
    else
        local idx = math.random(1,#targets)
        return targets[idx]
    end
end

-- ------------------------------
-- enemies

function U.find_nearest_enemy(entities, origin, min_range, max_range, flags, bans, filter_func)
    local targets = U.find_enemies_in_range(entities,origin,min_range,max_range,flags,bans,filter_func)
    if not targets or #targets == 0 then
        return nil
    else
        table.sort(
            targets,
            function(e1,e2)
                return ( V.dist(e1.pos.x,e1.pos.y, origin.x,origin.y) <
                         V.dist(e2.pos.x,e2.pos.y, origin.x,origin.y) )
            end
        )
        return targets[1],targets
    end        
end

function U.find_random_enemy(entities, origin, min_range, max_range, flags, bans, filter_func)
    -- flags and bans: bitfield for attack
    -- filter_func: function that returns false if entity is to be discarded
    if not flags then flags = 0x0 end
    if not bans then bans = 0x0 end
    local enemies = U.find_enemies_in_range(entities, origin, min_range, max_range, flags, bans, filter_func)
    if not enemies or #enemies == 0 then
        return nil
    else
        local idx = math.random(1,#enemies)
        return enemies[idx]
    end
end

function U.find_enemies_in_range(entities, origin, min_range, max_range, flags, bans, filter_func)
    -- flags and bans: bitfield for attack
    -- filter_func: function that returns false if entity is to be discarded
    local enemies
    if SSO and SSO:is_all_entities(entities) then
        enemies = {}
        SSO:filter(enemies, 'targets', origin.x,origin.y, max_range,
                       function(k,v)
                           return (
                               v.enemy and v.nav_path and 
                               not v.health.dead and
                               band(v.vis.flags, bans) == 0 and
                               band(v.vis.bans, flags) == 0 and
                               U.is_inside_ellipse(v.pos, origin, max_range) and
                               P:is_node_valid(v.nav_path.pi, v.nav_path.ni) and
                               ( min_range == 0 or not U.is_inside_ellipse(v.pos, origin, min_range) ) and
                               ( not filter_func or filter_func(v,origin) )
                           )
                       end
        )
    else
        enemies = table.filter(
            entities,
            function(k,v)
                return (
                    not v.pending_removal and 
                    v.enemy and v.vis and v.nav_path and 
                    v.health and not v.health.dead and
                    band(v.vis.flags, bans) == 0 and
                    band(v.vis.bans, flags) == 0 and
                    U.is_inside_ellipse(v.pos, origin, max_range) and
                    P:is_node_valid(v.nav_path.pi, v.nav_path.ni) and
                    ( min_range == 0 or not U.is_inside_ellipse(v.pos, origin, min_range) ) and
                    ( not filter_func or filter_func(v,origin) )
                )
            end
        )
    end
    if not enemies or #enemies == 0 then
        return nil
    else
        return enemies
    end
end

function U.find_enemies_in_paths(entities, origin, min_node_range, max_node_range, max_path_dist, flags, bans, only_upstream, filter_func)
    -- origin: x,y position of the origin
    -- min_node_range and max_node_range: distances in nodes
    -- only_upstream:
    --   - true: enemy is only upstream
    --   - false: enemy is only downstream
    --   - nil: enemy is either upstream or downstream
    -- filter_func: function that returns false if entity is to be discarded
    if not max_path_dist then max_path_dist = 30 end          -- SoliderHero.m#910
    if not flags then flags = 0x0 end
    if not bans then bans = 0x0 end
    local result = {}
    local nearest_nodes = P:nearest_nodes(origin.x,origin.y)
    for _,n in pairs(nearest_nodes) do
        local opi,ospi,oni,odist = unpack(n,1,4)
        if odist > max_path_dist or not P:is_node_valid(opi,oni) then
            goto continue
        end
        for _,e in pairs(entities) do
            if ( not e.pending_removal and 
                 e.enemy and e.nav_path and
                 e.health and not e.health.dead and                
                 e.nav_path.pi == opi and 
                 ( ( only_upstream == true and e.nav_path.ni < oni ) or
                   ( only_upstream == false and e.nav_path.ni > oni ) or
                   ( only_upstream == nil ) ) and
                 e.vis and 
                 band(e.vis.flags, bans) == 0 and
                 band(e.vis.bans, flags) == 0 and
                 math.abs( e.nav_path.ni - oni ) >= min_node_range and
                 math.abs( e.nav_path.ni - oni ) <= max_node_range and
                 ( not filter_func or filter_func(e,origin) ) )
            then
                table.insert(result,{enemy=e, origin=n})
            end
        end
        ::continue::
    end
    
    if not result or #result == 0 then
        return nil
    else
        table.sort(
            result,
            function(e1,e2)
                local p1 = e1.enemy.nav_path
                local p2 = e2.enemy.nav_path
                return P:nodes_to_goal(p1.pi, p1.spi, p1.ni) < P:nodes_to_goal(p2.pi, p2.spi, p2.ni)
            end
        )
        return result  -- { {enemy=..., origin={pi,spi,ni,dist}}, .. }
    end
end

function U.find_foremost_enemy(entities, origin, min_range, max_range, prediction_time, flags, bans, filter_func, min_override_flags)
    -- prediction_time: flight time to predict future path position using avg node dist in Enemy.m getNodesSpeed
    -- flags and bans: bitfield for attack
    -- filter_func: function that returns false if entity is to be discarded
    -- min_override_flags: flags that skip the min range check (eg. flying for reinforcements)
    --        
    -- returns: first_enemy, sorted_enemy_list, first_enemy_predicted_pos
    if not flags then flags = 0x0 end
    if not bans then bans = 0x0 end
    if not min_override_flags then min_override_flags = 0x0 end

    local l_entities = entities
    if SSO and SSO:is_all_entities(entities) then
        -- make range larger for the prediction case
        local range = max_range
        if prediction_time and prediction_time ~= false then
            -- NOTE: 150 around the highest speed used by enemies
            range = range + ((prediction_time == true) and 1 or prediction_time) * 150
        end
        l_entities = {}
        SSO:filter(l_entities, 'targets', origin.x,origin.y, range)
    end

    local enemies = {}
    for _,e in pairs(l_entities) do
        if  ( not SSO and (e.pending_removal or not e.enemy or not e.nav_path or not e.vis) ) or
            not e.enemy or
            not e.nav_path or
            ( e.health and e.health.dead ) or
            ( band(e.vis.flags, bans) ~= 0 ) or
            ( band(e.vis.bans, flags) ~= 0 ) or
            ( filter_func and not filter_func(e,origin) )
        then
            goto continue
        end
        local e_pos
        local e_ni
        if prediction_time and e.motion and e.motion.speed then
            if e.motion.forced_waypoint then
                -- spawners have the dest node so prediction should use the speed
                local dt = (prediction_time == true) and 1 or prediction_time
                e_pos = V.v(e.pos.x + dt * e.motion.speed.x, e.pos.y + dt * e.motion.speed.y)
                e_ni = e.nav_path.ni
            else
                local node_offset = P:predict_enemy_node_advance(e,prediction_time)
                e_ni = e.nav_path.ni + node_offset
                e_pos = P:node_pos(e.nav_path.pi, e.nav_path.spi, e_ni)
            end
        else
            e_pos = e.pos
            e_ni = e.nav_path.ni
        end
        if ( U.is_inside_ellipse(e_pos, origin, max_range) and
             P:is_node_valid(e.nav_path.pi, e_ni) and
             ( min_range == 0 or
               band(e.vis.flags, min_override_flags) ~= 0 or 
               not U.is_inside_ellipse(e_pos, origin, min_range) ) )
        then
            e.__ffe_pos = V.vclone(e_pos)
            table.insert(enemies,e)
        end        
        ::continue::
    end

    if not enemies or #enemies == 0 then
        return nil,nil
    else
        table.sort(
            enemies,
            function(e1,e2)
                local p1 = e1.nav_path
                local p2 = e2.nav_path
                return P:nodes_to_goal(p1.pi, p1.spi, p1.ni) < P:nodes_to_goal(p2.pi, p2.spi, p2.ni)
            end
        )
        return enemies[1],enemies,enemies[1].__ffe_pos
    end
end

function U.find_towers_in_range(entities, origin, attack, filter_func)
    local towers = table.filter(
        entities,
        function(k,v)
            return (
                not v.pending_removal and 
                    v.tower and not v.tower.blocked and
                    (not attack.excluded_templates or not table.contains(attack.excluded_templates, v.template_name)) and
                    U.is_inside_ellipse(v.pos, origin, attack.max_range) and
                    ( attack.min_range == 0 or not U.is_inside_ellipse(v.pos, origin, attack.min_range) ) and
                    ( not filter_func or filter_func(v,origin,attack) )
            )
        end
    )
    if not towers or #towers == 0 then
        return nil
    else
        return towers
    end
    
end

function U.find_entity_at_pos(entities, x,y, filter_func)
    -- only for clickable entities
    local found = {}
    for _,e in pairs(entities) do
        if e.pos and e.ui and e.ui.can_click then
            local r = e.ui.click_rect
            if ( x > e.pos.x + r.pos.x and
                     x < e.pos.x + r.pos.x + r.size.x and
                     y > e.pos.y + r.pos.y and
                     y < e.pos.y + r.pos.y + r.size.y and
                     (not filter_func or filter_func(e)) )
            then
                table.insert(found, e)
            end
        end
    end
    table.sort(
        found,
        function(e1,e2)
            if e1.ui.z == e2.ui.z then
                return e1.pos.y < e2.pos.y
            else
                return e1.ui.z > e2.ui.z
            end
        end
    )
    if #found > 0 then
        local e = found[1]
        log.paranoid('entity:%s template:%s', e.id, e.template_name)
        return e
    else
        return nil
    end        
end

function U.find_paths_with_enemies(entities, flags, bans, filter_func)
    local pis = {}
    for _,e in pairs(entities) do
        if ( not e.pending_removal and
                 e.enemy and e.nav_path and
                 e.health and not e.health.dead and
                 e.vis and
                 band(e.vis.flags, bans) == 0 and
                 band(e.vis.bans, flags) == 0 and
                 ( not filter_func or filter_func(e) ) )
        then
            pis[e.nav_path.pi] = true
        end
    end
    local out = {}
    for pi,_ in pairs(pis) do
        table.insert(out,pi)
    end
    if #out < 1 then
        return nil
    else        
        return out
    end
end

function U.find_entity_most_surrounded(entities)

    local sorted_entities = {}

    for _, e1 in ipairs(entities) do
        local distance_between_entities = 0
        for _, e2 in ipairs(entities) do
            if e1.id ~= e2.id
                and e1.health and not e1.health.dead
                and e2.health and not e2.health.dead
                and e1.pos and e2.pos then

                local distance = V.dist(e1.pos.x,e1.pos.y, e2.pos.x,e2.pos.y)
                distance_between_entities = distance_between_entities + distance
            end
        end

        table.insert(sorted_entities, {entity=e1, distance=distance_between_entities})
    end

    table.sort(
        sorted_entities,
        function(e1,e2)
            return e1.distance < e2.distance
        end
    )

    local out = {}
    for _,e in ipairs(sorted_entities) do
        table.insert(out,e.entity)
    end

    return out[1], out
end


------------------------------------------------------------
-- attacks
-- TODO REFACTOR: move to script_utils

function U.attack_order(attacks)
    local order = {} 
    for i=1,#attacks do
        local a = attacks[i]
        table.insert(order, {id=i, chance=(a.chance or 1), cooldown=a.cooldown})        
    end
    table.sort(
        order,
        function(o1,o2)
            -- first do those with lower chance, higher cooldown, then the order in the array
            --   cooldowns can be nil because they use shared cooldowns
            if o1.chance ~= o2.chance then return o1.chance < o2.chance
            elseif o1.cooldown and o2.cooldown and o1.cooldown ~= o2.cooldown then return o1.cooldown > o2.cooldown
            else return o1.id < o2.id end 
        end
    )
    local out = {}
    for i=1,#order do
        out[i] = order[i].id
    end
    return out
end


function U.melee_slot_position(soldier, enemy, rank, back)
    -- returns: soldier position, flip soldier
    if not rank then 
        rank = table.keyforobject(enemy.enemy.blockers, soldier.id)
        if not rank then
            return nil
        end
    end
    local idx = km.zmod(rank,3)
    local x_off,y_off = 0,0
    local spread_x = soldier.soldier.melee_slot_spread and soldier.soldier.melee_slot_spread.x or -3
    local spread_y = soldier.soldier.melee_slot_spread and soldier.soldier.melee_slot_spread.y or -6
    if idx == 2 then
        x_off = spread_x
        y_off = spread_y
    elseif idx == 3 then
        x_off = spread_x
        y_off = -1 * spread_y  -- symmetric vertically
    end
    local soldier_on_the_right = ( math.abs(km.signed_unroll(enemy.heading.angle)) < math.pi/2 )
    if back then soldier_on_the_right = not soldier_on_the_right end
    local soldier_pos = V.v(
        enemy.pos.x + ( enemy.enemy.melee_slot.x + x_off + soldier.soldier.melee_slot_offset.x ) * (soldier_on_the_right and 1 or -1), 
        enemy.pos.y + enemy.enemy.melee_slot.y + y_off + soldier.soldier.melee_slot_offset.y
    )
    return soldier_pos, soldier_on_the_right
end

function U.rally_formation_position(idx,barrack,count,angle_offset)
    -- returns: soldier.nav_rally position and center
    local pos
    if not count then count = #barrack.soldiers end
    if not angle_offset then angle_offset = 0 end
    if count == 1 then
        pos = V.vclone(barrack.rally_pos)
    else
        local a = 2 * math.pi / count
        pos = U.point_on_ellipse(barrack.rally_pos, barrack.rally_radius, (idx-1) * a - math.pi / 2 + angle_offset)
    end
    local center = V.vclone(barrack.rally_pos)
    return pos,center
end

------------------------------------------------------------
-- soldier blocking
-- TODO REFACTOR: move to script_utils

function U.get_blocker(store,blocked)
    if blocked.enemy and #blocked.enemy.blockers > 0 then 
        local blocker_id = blocked.enemy.blockers[1]
        local blocker = store.entities[blocker_id]
        return blocker
    end
    return nil
end

function U.get_blocked(store,blocker)
    local blocked_id = blocker.soldier.target_id
    local blocked = store.entities[blocked_id]
    return blocked
end

function U.blocker_rank(store,blocker)
    local blocked_id = blocker.soldier.target_id
    local blocked = store.entities[blocked_id]
    if blocked then 
        return table.keyforobject(blocked.enemy.blockers, blocker.id)
    end
    return nil
end
                            
function U.is_blocked_valid(store,blocker)
    local blocked_id = blocker.soldier.target_id
    local blocked = store.entities[blocked_id]
    return (
        blocked and not blocked.health.dead and
        ( not blocked.vis or bit.band(blocked.vis.bans, F_BLOCK) == 0 )
    )
end

function U.unblock_all(store,blocked)
    for _,blocker_id in pairs(blocked.enemy.blockers) do
        local blocker = store.entities[blocker_id]
        if blocker then
            blocker.soldier.target_id = nil
        end
    end
    blocked.enemy.blockers = {} 
end

function U.unblock_target(store,blocker)
    local blocked_id = blocker.soldier.target_id
    local blocked = store.entities[blocked_id]
    if blocked then 
        table.removeobject(blocked.enemy.blockers, blocker.id)
        -- move the last one to the first place to avoid all of them shifting places
        if #blocked.enemy.blockers > 1 then
            local last = table.remove(blocked.enemy.blockers)             
            table.insert(blocked.enemy.blockers, 1, last)
        end
    end
    blocker.soldier.target_id = nil
end

function U.block_enemy(store,blocker,blocked)
    if blocker.soldier.target_id ~= blocked.id then
        U.unblock_target(store,blocker)
    end
    if not table.keyforobject(blocked.enemy.blockers, blocker.id) then 
        table.insert(blocked.enemy.blockers, blocker.id)
        blocker.soldier.target_id = blocked.id
    end
end

function U.replace_blocker(store,old,new)
    local blocked_id = old.soldier.target_id
    local blocked = store.entities[blocked_id]
    if blocked then
        local idx = table.keyforobject(blocked.enemy.blockers, old.id)
        if idx then
            blocked.enemy.blockers[idx] = new.id
            new.soldier.target_id = blocked.id
            old.soldier.target_id = nil
        end
    end
end

function U.cleanup_blockers(store,blocked)
    local blockers = blocked.enemy.blockers
    if not blockers then return end
    for i=#blockers,1,-1 do
        local blocker_id = blockers[i]
        if not store.entities[blocker_id] then
            log.debug('cleanup_blockers for (%s) %s removing id %s',
                      blocked.id, blocked.template_name, blocker_id)
            table.remove(blockers,i)
        end        
    end
end

------------------------------------------------------------
-- damage
-- TODO REFACTOR: move to script_utils

function U.predict_damage(entity,damage)
    local e = entity   -- entity with health
    local d = damage   -- damage component

    if band(d.damage_type, bor(DAMAGE_INSTAKILL, DAMAGE_EAT)) ~= 0 then
        return entity.health.hp
    end
        
    local protection
    if band(d.damage_type, DAMAGE_POISON) ~= 0 then              -- poison
        protection = e.health.poison_armor

    elseif band(d.damage_type,
            bor(DAMAGE_TRUE, DAMAGE_DISINTEGRATE)) ~= 0 then       -- I_ARMOR
        protection = 0

    elseif band(d.damage_type, DAMAGE_PHYSICAL) ~= 0 then          -- P_ARMOR
        protection = e.health.armor - d.reduce_armor

    elseif band(d.damage_type, DAMAGE_MAGICAL) ~= 0 then           -- M_ARMOR
        protection = e.health.magic_armor - d.reduce_magic_armor

    elseif band(d.damage_type,
                bor(DAMAGE_EXPLOSION,DAMAGE_ELECTRICAL)) ~= 0 then -- E_ARMOR
        protection = ( e.health.armor - d.reduce_armor ) / 2

    elseif d.damage_type == DAMAGE_NONE then
        protection = 1
    end

    if not protection then
        protection = 0
        --log.warning('++  DAMAGE TYPE UNKNOWN: %x', d.damage_type)
    end
    
    -- NOTE: round from Enemy:predictDamage done first and floor is
    -- the (int) cast in Enemy:getArmorDamage
    local rounded_damage = d.value
    if band(d.damage_type, DAMAGE_MAGICAL) ~= 0 and e.health.damage_factor_magical then
        rounded_damage = km.round(rounded_damage * e.health.damage_factor_magical)
    end
    rounded_damage = km.round(rounded_damage * e.health.damage_factor)
    
    -- different rounding for enemies or soldiers
    local actual_damage
    if entity.soldier then
        -- Soldier.m#L830 - integer division, so the floor applies there 
        actual_damage = km.round(rounded_damage - math.floor(rounded_damage * km.clamp(0,1, protection)))
    else
        -- Enemy.m#L1147 - floating division, the floor applies to the whole result
        actual_damage = math.floor(rounded_damage * km.clamp(0,1, 1 - protection))
    end

    if band(d.damage_type, DAMAGE_NO_KILL) ~= 0 and e.health and e.health.hp <= actual_damage  then
        actual_damage = e.health.hp - 1
    end
    return actual_damage
end

------------------------------------------------------------
-- seen
-- TODO REFACTOR: move to script_utils

function U.is_seen(store,id)
    return store.seen[id]
end

function U.mark_seen(store,id)
    if not store.seen[id] then
        store.seen[id] = true
        store.seen_dirty = true
    end
end

------------------------------------------------------------
-- stars

-- WARNING: does not consider last_victory stars!
function U.count_stars(slot)
    local campaign = 0
    local heroic = 0
    local iron = 0

    local heroic_stars = GS.stars_per_mode or 1
    local iron_stars = GS.stars_per_mode or 1

    for i,v in pairs(slot.levels) do
        if i < 80 then  -- ignore endless levels
            heroic = heroic + ( v[GAME_MODE_HEROIC] and heroic_stars or 0 )
            iron   = iron + ( v[GAME_MODE_IRON] and iron_stars or 0 )
            campaign = campaign + ( v.stars or 0 )
        end
    end
    return campaign+heroic+iron,heroic,iron
end

------------------------------------------------------------
-- level unlocking
-- TODO REFACTOR: move to script_utils

function U.filter_level_indexes(slot, filter)
    local indexes = {}
    if not slot or not slot.levels or not filter then
        return indexes
    end
    for k,v in pairs(slot.levels) do
        if filter(v) then
            table.insert(indexes, tonumber(k))
        end
    end
    table.sort(indexes,
               function(v1,v2)
                   return tonumber(v1) < tonumber(v2)
               end
    )
    return indexes
end

function U.find_next_level_in_ranges(ranges,cur)
    local last_range = ranges[#ranges]
    local nex = last_range[#last_range]
    for ri,r in ipairs(ranges) do
        if r.list then
            local idx = table.keyforobject(r,cur)
            if idx then
                if idx < #r then
                    -- next item in range
                    nex = r[idx+1]
                    break
                elseif ri < #ranges then
                    -- first of next range
                    nex = ranges[ri+1][1]
                    break
                end
            end
        else
            local r1,r2 = unpack(r)
            if r1 == cur or (r2 and cur >= r1 and cur < r2) then
                nex = cur + 1
                break
            elseif r2 and cur == r2 then
                if ri < #ranges then
                    nex = ranges[ri+1][1]
                    break
                end
            end
        end
    end
    return nex
end

function U.get_expansion_index(name)
    -- skips dlcs when counting index, ignores the base expansion
    local index = 1
    for i,v in ipairs(GS.level_range_names) do
        if v == 'base' then
            goto continue
        end
        local first_level = GS.level_ranges[i][1]
        if U.is_dlc_level(first_level) then
            goto continue
        end
        if v == name then 
            return index
        end
        index = index + 1
        ::continue::
    end
    log.error('error finding expansion with name %s', name)
end

function U.get_dlc_index(dlc)
    for i, v in ipairs(GS.dlc_names) do
        if v.id == dlc then
            return i
        end
    end
    log.error('error finding dlc index! dlc: ' .. dlc)
end

function U.get_dlc_id(name)
    for _,v in pairs(GS.dlc_names) do
        if v.name == name then
            return v.id
        end
    end
    log.error('error finding dlc id! dlc name: %s', name)
end

function U.get_dlc_level_range(dlc)
    for _, v in pairs(GS.dlc_names) do
        if v.id == dlc then
            local range_idx
            for i, w in ipairs(GS.level_range_names) do
                if v.name == w then
                    return GS.level_ranges[i]
                end
            end
        end
    end
    log.error('error finding dlc level range! dlc: ' .. dlc)
end

function U.is_dlc_level(level_idx)
    for k, v in pairs(GS.dlc_names) do
        local range = U.get_dlc_level_range(v.id)
        if level_idx >= range[1] and level_idx <= range[2] then
            return true
        end
    end
    return false
end

function U.get_bundle_override_value(bundle_id, value)
    -- If the value is a table, look for a key with the name of the
    -- bundle_id, or return the first value of the table.
    -- In any other case just return the value
    if type(value) == 'table' then
        if value[bundle_id] then
            -- look for a key with the bundle_id name
            return value[bundle_id]
        else
            -- fallback to the first value of the array (which can be a table)
            return value[1]
        end
    else
        return value
    end
end

function U.get_dlcs_unlock_level(game_settings)
    local dul = game_settings.dlcs_unlock_level
    return U.get_bundle_override_value(version.bundle_id, dul)
end

function U.get_expansions_unlock_level(game_settings)
    local eul = game_settings.expansions_unlock_level
    if eul.default then return eul end  -- no overrides exist
    return U.get_bundle_override_value(version.bundle_id, eul)
end

function U.unlock_next_levels_in_ranges(unlock_data, levels, game_settings, unlocked_dlcs)
    local level_ranges = game_settings.level_ranges
    local level_range_names = game_settings.level_range_names
    local main_campaing_last_level = game_settings.main_campaign_levels
    local expansions_unlock_level = U.get_expansions_unlock_level(game_settings) or {default=main_campaing_last_level} -- fallback for trilogy
    local dlcs_unlock_level = U.get_dlcs_unlock_level(game_settings)
    local dirty = false

    local function sanitize_unlock(idx)
        levels[idx] = {}
        if not unlock_data.new_level then
            unlock_data.new_level = idx
        end
        table.insert(unlock_data.unlocked_levels, idx)
        dirty = true
        log.debug('>>> sanitizing : added level %s', idx)
    end

    -- all updates unlocked when expansions_unlock_level is completed
    for i=2,#level_ranges do  -- exclude campaign
        local range = level_ranges[i]
        local name = level_range_names and level_range_names[i]
        local u_idx = name and expansions_unlock_level[name] or expansions_unlock_level.default
        if levels[u_idx] and levels[u_idx][GAME_MODE_CAMPAIGN] and not levels[range[1]] then
            levels[range[1]] = {}
            table.insert(unlock_data.unlocked_levels, range[1])
            dirty = true
        end
    end

    -- check dlc unlock
    if unlocked_dlcs then
        for _, dlc in pairs(unlocked_dlcs) do
            local dlc_first_level = U.get_dlc_level_range(dlc)[1]
            if not levels[dlc_first_level] then
                if not unlock_data.unlocked_levels then unlock_data.unlocked_levels = {} end
                table.insert(unlock_data.unlocked_levels, dlc_first_level)
                levels[dlc_first_level] = {}
                dirty = true
            end
        end
    end
    
    -- next levels
    for _,range in pairs(level_ranges) do
        if range[2] then
            if range.list then
                -- range is a list of non contiguous levels
                local prev 
                for i,v in ipairs(range) do
                    if prev and levels[prev] and levels[prev][GAME_MODE_CAMPAIGN] and not levels[v] then
                        sanitize_unlock(v)
                        goto next_range
                    end
                    prev = v
                end
            else
                -- range of levels
                for i=range[1],range[2]-1 do
                    if levels[i] and levels[i][GAME_MODE_CAMPAIGN] and not levels[i+1] then
                        sanitize_unlock(i+1)
                        goto next_range
                    end
                end
            end
        end
        ::next_range::
    end

    -- returns true if changes were made to the levels
    return dirty
end    


------------------------------------------------------------
-- flags
-- TODO REFACTOR: move to script_utils
function U.flags_pass(vis,vis_x)
    -- vis:  vis component
    -- vis_x: component with vis_flags and vis_bans (bullet, modifier, etc.)
    return ( band(vis.flags, vis_x.vis_bans) == 0 and band(vis.bans, vis_x.vis_flags) == 0)
end

function U.flag_set(value,flag)
    return bor(value,flag)
end

function U.flag_clear(value,flag)
    return band(value, bnot(flag))
end

function U.flag_has(value,flag)
    return (band(value, flag) ~= 0)
end

------------------------------------------------------------
-- bans stack
-- TODO REFACTOR: move to script_utils
-- 
-- When pushing a ban, a new stack is created keeping the history of
-- all values that modify the vis.bans value.
-- The value of vis.bans is recalculated in every push/pop so the rest
-- of the game works as always.
-- If the value of vis.bans is assigned directly, the stack will be
-- removed. This gives it compatibility with legacy code.
--
-- This is ideal for overlapped modifiers that set/clear bans in
-- unknown order (eg: mod_teleport, mod_mark and water/cliff/land
-- changes)
-- 

-- vis metatable shared by all entities
local vis_meta = {}
vis_meta.__index = function(t,k)
    if k == 'bans' then
        -- Returns the calculated value when 'bans' does not exist in vis table.
        -- NOTE: Cannot use the vis value directly because newindex
        -- would not be called and the stack would not be cleared on
        -- forced assignment.
        return t._bans_stack_value
    end
end
vis_meta.__newindex = function(t,k,v)
    if k == 'bans' then
        -- when assigned, the stack is cleared and the vis table value is assinged
        rawset(t, '_bans_stack', nil)
        rawset(t, '_bans_stack_value', nil)        
        rawset(t, 'bans', v)
    else
        rawset(t, k, v)
    end
end

function U.calc_vis_stack(s)
    local o = 0x0
    for _,r in pairs(s) do
        local op,flag = unpack(r)
        if op == 'set' then
            o = flag
        else
            local fop = bit[op]
            if not fop then
                goto continue
            end
            o = fop(o, flag)
        end
        ::continue::
    end
    return o
end

-- 
function U.push_bans(t,value,op)
    -- Adds a ban to at the end of the entity bans stack.
    -- If it has no stack, a new stack is created and the existing
    -- value of vis.bans is set as the first element with op 'set'
    --
    -- t: the vis component of the entity
    -- value: the value of the ban to set
    -- op: the bitop name (string) that will be used to calculate the final value.
    --     this op will be applied to the calculated value up to the previous stack item.
    --     a special op named 'set' will ignore the previous values in the stack and set it to the passed value.
    --     if empty, 'bor' is used.
    --
    -- returns: reference to the table added to the stack, wich is REQUIRED later to pop it.
    -- 
    if not getmetatable(t) then
        setmetatable(t, vis_meta)
    end
    if not t._bans_stack then
        rawset(t, '_bans_stack', {})
        table.insert(t._bans_stack, {'set',t.bans})
        rawset(t, 'bans', nil)
    end
    op = op or 'bor'  -- defaults to bor
    if op ~= 'set' and not bit[op] then 
        if DEBUG then
            assert(false, 'error in push_ban: invalid bit op ' .. tostring(op) .. ' for vis table ' .. tostring(t))
        else
            return
        end
    end
    local row = {op, value}
    table.insert(t._bans_stack, row)
    rawset(t,'_bans_stack_value', U.calc_vis_stack(t._bans_stack))
    return row
end

function U.pop_bans(t,ref)
    -- Removes the ref object from the vis bans stack.
    -- If the stack is empty, removes the stack.
    -- 
    -- t: the vis component of the entity
    -- ref: a table value returned by U.push_bans which points to the object in the stack to be removed
    -- 
    if not t._bans_stack then
        if DEBUG then
            log.error('error in pop_ban: nil _bans_stack for vis table %s', t)
            return
        else
            -- ignore in production
            return
        end
    end
    if #t._bans_stack <= 1 then
        if DEBUG then
            assert(false, 'error in pop_ban: popping with stack size <= 1 for vis ' .. tostring(t))
        else
            return
        end
    end
    local ti = table.keyforobject(t._bans_stack, ref)
    if ti ~= nil then
        -- NOTE: the metatable is kept
        table.remove(t._bans_stack, ti)
        if #t._bans_stack == 1 then
            rawset(t, 'bans', t._bans_stack[1][2])  -- get first value
            rawset(t, '_bans_stack', nil)  -- remove stack
            rawset(t, '_bans_stack_value', nil)  -- clean calculated value
        else
            rawset(t, '_bans_stack_value', U.calc_vis_stack(t._bans_stack))
        end
    end
end

------------------------------------------------------------
-- hero level
function U.get_hero_level(xp,thresholds)
    -- args:
    --   - thresholds: array with thresholds from game_settings
    -- returns: level,phase in level
    local level = 1
    while ((level < 10) and (thresholds[level] <= xp) ) do
        level = level + 1
    end
    local phase
    if level > #thresholds then
        phase = 1
    elseif xp == thresholds[level] then
        phase = 0
    else
        local this_xp = thresholds[level-1] or 0
        local next_xp = thresholds[level]
        phase = (xp-this_xp)/(next_xp-this_xp)
    end
    return level,phase    
end

------------------------------------------------------------
-- modifiers
-- TODO REFACTOR: move to script_utils

function U.get_modifiers(store,entity,list)
    local l_entities = SSO and SSO:get_p_list('modifiers') or store.entities
    local mods = table.filter(
        l_entities,
        function(k,v)
            return ( v.modifier and v.modifier.target_id == entity.id and
                     (not list or table.contains(list, v.template_name)) )
        end
    )
    return mods
end

function U.has_modifiers(store,entity,mod_name)
    local l_entities = SSO and SSO:get_p_list('modifiers') or store.entities
    local mods = table.filter(
        l_entities,
        function(k,v)
            return v.modifier and v.modifier.target_id == entity.id and
                (not mod_name or mod_name == v.template_name)
        end
    )
    return (#mods > 0),mods
end

function U.has_modifier_in_list(store,entity,list)
    local l_entities = SSO and SSO:get_p_list('modifiers') or store.entities
    for _,e in pairs(l_entities) do
        if e.modifier and e.modifier.target_id == entity.id and table.contains(list,e.template_name) then
            return true
        end
    end
    return false
end

function U.has_modifier_types(store,entity,...)
    local l_entities = SSO and SSO:get_p_list('modifiers') or store.entities
    local types = {...}
    local mods = table.filter(
        l_entities,
        function(k,v)
            return ( v.modifier and
                         v.modifier.target_id == entity.id and
                         table.contains(types, v.modifier.type))
        end
    )
    return (#mods > 0),mods
end

------------------------------------------------------------
--balance format
--- inserts string following the path that was set between %$, if the closing %$ is follow by % the value is multiply by 100
--- example "Upgrade value by %$root.leaf1[1]%$ and increase the attack by %$root.leaf2[1]%$%"
---@param s string
---@param b balance object
---@return string
-- TODO: consider refactoring using string.gsub with %b{} pattern that's probably faster

function U.balance_format(s,b)
    local function get_value(obj, path)
        local p = {}
        for v in path:gmatch("[^%.%[%]]+") do
            local i = tonumber(v)
            if i then
                table.insert(p, i)
            else
                table.insert(p, v)
            end
        end
    
        local val = obj
        log.paranoid('values are '..getfulldump(p))
        for _, v in ipairs(p) do
            val = val[v]
            if not val then
                return nil
            end
            log.paranoid('value part is '..v)
        end
        
    
        return val
    end
    
    local i, f
    if not s then
        return s
    end
    repeat
        i = string.find(s, '%$')
        if i then
            f = string.find(s, '%$', i + 1)
            if f then
                log.paranoid('index i '..i..' end '..f)
                local p = string.sub(s, i + 1, f -2)
            
                local v = get_value(b,p)
                if not v then
                    v = ''
                else
                    if string.sub(s,f+1,f+1)=='%' then
                        v = v*100
                    end
                end

                s = string.sub(s,1, i-2)..v..string.sub(s,f+1)
            end
        end
    until not i or not f
    return s
end
------------------------------------------------------------
--format date 
function U.format_countdown_time(rem_time, hour_format)
    local days    = math.floor( rem_time / (24 * 60 * 60))
    local hours   = math.floor((rem_time % (24 * 60 * 60)) / (60 * 60))
    local minutes = math.floor((rem_time % (60 * 60))      / (60))
    local seconds = math.floor( rem_time % 60)
    --log.debug('dhms: %s %s %s %s', days,hours,minutes,seconds)
    local text
    if hour_format then
        text = string.format('%02d:%02d:%02d', hours, minutes, seconds)
    else
        if days > 0 then
            text = string.format(
                '%d%s %02d%s',
                days, _('DAYS_ABBREVIATION'), hours, _('HOURS_ABBREVIATION'))
        elseif hours > 0 then
            text = string.format(
                '%d%s %02d%s',
                hours, _('HOURS_ABBREVIATION'), minutes, _('MINUTES_ABBREVIATION'))
        else
            text = string.format(
                '%02d%s %02d%s',
                minutes, _('MINUTES_ABBREVIATION'), seconds, _('SECONDS_ABBREVIATION'))
        end
    end
    return text
end

------------------------------------------------------------
return U


