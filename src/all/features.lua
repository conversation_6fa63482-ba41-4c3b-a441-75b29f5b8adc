--
-- features.lua:
-- Enable/disable game features according to bundle id.
--
-- NOTE: Only the config for the bunlde id is included in the distro
-- files. See bottom.
--



local _features = {
    ------------------------------------------------------------
    -- TEST SETTINGS

    -- General Tests
    ['net.kalio.test.CLEAN'] = {
    },

    ['net.kalio.test.PP'] = {
        requires_privacy_policy = true,
        platform_services = {
            http         = { name='http',   enabled=true, src='platform_services_http', essential=true },
        }
    },
    ['net.kalio.test.RESTORE'] = {
        has_restore_savegame = true,
        platform_services = {
            http         = { name='http',   enabled=true, src='platform_services_http', essential=true },
            deep_links   = { name='deep_links', enabled=true, src='platform_services_deep_links', params={url='https://link.kingdomrush.com'}}, 
        }
    },
    ['net.kalio.test.CHALLENGES'] = {        
        has_challenges = true,
        platform_services = {
            iap          = { name='gpiab',  enabled=true, src='test_platform_services_iap'},            
        },
    },
    ['net.kalio.test.KR5_DESKTOP_DEV'] = {
        no_gems = true,
        main_params = {
            texture_size_list = { {'FullHD+', 'ipadhd_bc3',1e9}, {'FullHD','fullhd_bc3',1200}, {'XGA', 'ipad', 700}, },
            texture_size = 'ipadhd_bc3',
            image_db_uses_canvas = true,
            skip_settings_dialog = true,
            first_launch_fullscreen = true,
        },
        asset_game_fallback_for_texture_size = {
            fullhd_bc3 = {
                {path='kr5-desktop', texture_size='ipadhd_bc3'},
            },
        },
        platform_services = {
            --iap          = { name='iap_premium',  enabled=true, src='platform_services_iap_premium'},
            iap            = { name='iap_premium',  enabled=true, src='test_platform_services_steam', params={app_id=2849080, dlcs={{id='dlc_1', app_id=3368630, includes={'hero_lava','tower_dwarf'}},{id='dlc_2', app_id=3732970, includes={'hero_wukong','tower_pandas'}}}}},
            http           = { name='http', enabled=true, src='platform_services_http', essential=true },
            remote_balance = { name='rbgs', enabled=true, src='platform_services_rbgs', order=90, init_cond={only_for_debug=true}, params={  -- ,on_signals={'screen_slots-ready'}
                                   sheet_id='1ZoCYWmzgIi42FKAFI5s0nKX8TYk_MBnrvsv4_IL2CYY',
                                   api_key='AIzaSyARRXehnlPpDQiqXMk72IE6L35Iuao6qX0',
                                   sync_on_init=true,
                             }},
        },
    },
    ['net.kalio.test.KR5_PHONE_DEV'] = {
        platform_services = {
            http           = { name='http', enabled=true, src='platform_services_http', essential=true },
            remote_balance = { name='rbgs', enabled=true, src='platform_services_rbgs', order=90, init_cond={only_for_debug=true}, params={  -- ,on_signals={'screen_slots-ready'}
                                   sheet_id='1ZoCYWmzgIi42FKAFI5s0nKX8TYk_MBnrvsv4_IL2CYY',
                                   api_key='AIzaSyARRXehnlPpDQiqXMk72IE6L35Iuao6qX0',
                                   sync_on_init=true,
                             }},
            
            iap          = { name='gpiab',  enabled=true, src='test_platform_services_iap'},
        
        },
    },    
    ['net.kalio.test.KR5_DESKTOP_DEV_CN'] = {
        overrides={'censored_cn'},
        censored_cn = true,
        no_gems = true,
        main_params = {
            texture_size_list = { {'FullHD+', 'ipadhd_bc3',1e9}, {'FullHD','fullhd_bc3',1200}, {'XGA', 'ipad', 700}, },
            texture_size = 'ipadhd_bc3',
            image_db_uses_canvas = true,
            skip_settings_dialog = true,
            first_launch_fullscreen = true,
        },
        asset_game_fallback_for_texture_size = {
            fullhd_bc3 = {
                {path='kr5-desktop', texture_size='ipadhd_bc3'},
            },
        },
        platform_services = {
            iap          = { name='iap_premium',  enabled=true, src='platform_services_iap_premium'},
        },
    },
    ['net.kalio.test.KR5_PHONE_DEV_CN'] = {
        overrides={'censored_cn'},
        censored_cn = true,
        pops_hidden = true,
        forced_locale = 'zh-Hans',  -- disables language selection button in settings
        default_locale = 'zh-Hans',
        
        platform_services = {
            http           = { name='http', enabled=true, src='platform_services_http', essential=true },
            iap          = { name='iap_test',  enabled=true, src='platform_services_iap_china',params={rc_suffix='gpiab'}},
        },
    },
   ['net.kalio.test.KR6_DESKTOP_DEV'] = {
        no_gems = true,
        main_params = {
            texture_size_list = { {'UHD', 'uhd_bc3',1e9}, {'FullHD','fullhd_bc3',1200}, {'XGA', 'ipad', 700}, },
            texture_size = 'uhd',
            image_db_uses_canvas = true,
            skip_settings_dialog = true,
            first_launch_fullscreen = true,
        },
        asset_game_fallback_for_texture_size = {
            uhd = {
                {path='kr6-desktop', texture_size='ipadhd'},
            },
            uhd_bc3 = {
                {path='kr6-desktop', texture_size='ipadhd_bc3'},
            },
        },
        platform_services = {
            http             = { name='http', enabled=true, src='platform_services_http', essential=true },
            --iap              = { name='iap_premium',  enabled=true, src='platform_services_iap_premium'},
            remote_balance = { name='rbgs', enabled=true, src='platform_services_rbgs', order=90, init_cond={only_for_debug=true}, params={  -- ,on_signals={'screen_slots-ready'}
                                   sheet_id='1DppY0pgDsXlkHSjzruNK3FZBjepdnnvXZTaI3p_aOUk',
                                   api_key='AIzaSyARRXehnlPpDQiqXMk72IE6L35Iuao6qX0',
                                   sync_on_init=true,
                             }},
        },
    },
    ['net.kalio.test.KR6_PHONE_DEV'] = {
        asset_game_fallback_for_texture_size = {
            ipadhd = {
                {path='kr6-phone', texture_size='uhd'},
            },
        },
        platform_services = {
            http           = { name='http', enabled=true, src='platform_services_http', essential=true },
            --iap          = { name='gpiab',  enabled=true, src='test_platform_services_iap'},
            remote_balance = { name='rbgs', enabled=true, src='platform_services_rbgs', order=90, init_cond={only_for_debug=true}, params={  -- ,on_signals={'screen_slots-ready'}
                                   sheet_id='1DppY0pgDsXlkHSjzruNK3FZBjepdnnvXZTaI3p_aOUk',
                                   api_key='AIzaSyARRXehnlPpDQiqXMk72IE6L35Iuao6qX0',
                                   sync_on_init=true,
                             }},
        },
    },    
    ['net.kalio.test.FORCE_IPADHD'] = {
        no_gems = true,
        main_params = {
            texture_size_list = { {'FullHD+', 'ipadhd_bc3',1e9}, {'FullHD','fullhd_bc3',1200}, {'XGA', 'ipad', 700}, },
            texture_size = 'ipadhd_bc3',
            image_db_uses_canvas = true,
        },
        asset_game_fallback_for_texture_size = {
            fullhd_bc3 = {
                {path='kr5-desktop', texture_size='ipadhd_bc3'},
            },
        },
        platform_services = {
            http         = { name='http',   enabled=true, src='platform_services_http', essential=true },
            iap          = { name='iap_premium',  enabled=true, src='platform_services_iap_premium'},
            news         = { name='news_ih',  enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kra-steam-osx',
                                 news_store='steam',
                           }},
        },
    },
    ['net.kalio.test.NEWS'] = {
        no_gems = true,
        main_params = {
            image_db_uses_canvas = true,
        },
        platform_services = {
            http         = { name='http',   enabled=true, src='platform_services_http', essential=true },
            news         = { name='news_ih',  enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kra-appstore-iphone',
                                 news_store='appstore',
                           }},
            
        },
    },
    ['net.kalio.test.GOLIATH'] = {
        main_params = {
            texture_size_list = { {'FullHD+', 'ipadhd_bc3',1e9}, {'FullHD','fullhd_bc3',1200}, {'XGA', 'ipad', 700}, },
            texture_size = 'ipadhd_bc3',
            image_db_uses_canvas = true,
            skip_settings_dialog = true,
            first_launch_fullscreen = true,
        },
        asset_game_fallback_for_texture_size = {
            fullhd_bc3 = {
                {path='kr5-desktop', texture_size='ipadhd_bc3'},
            },
        },
        platform_services = {
            http         = { name='http',   enabled=true, src='platform_services_http', essential=true },
            iap          = { name='iap_premium',  enabled=true, src='platform_services_iap_premium'},
            --cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud'},
            news         = { name='news_ih',  enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kra-appstore-iphone',
                                 news_store='appstore',
                           }},
            goliath      = { name='goliath',  enabled=true, order=90, src='platform_services_mc_goliath', params={
                                 platform='apple',
                                 game_id='1944',  -- desktop v1.2
                                 staging = {
                                     -- steam staging
                                     api_key='93b950ba-2762-451f-b960-a9acbe311742',
                                     shared_secret='c00480a5-e021-4129-b993-f446b728f33b',
                                     api_url='https://93b950ba-2762-451f-b960-a9acbe311742.goliath.atlas.bi.miniclippt.com',
                                 },
                                 production = {
                                     -- steam production
                                     api_key='ea4ed4a2-c9ae-40f9-a3b3-20db93550dbc',
                                     shared_secret='9f9c7755-d50c-4384-9ee4-a95426bfff57',
                                     api_url='https://ea4ed4a2-c9ae-40f9-a3b3-20db93550dbc.goliath.atlas.bi.miniclippt.com',
                                 }
                           }},      
        }
    },
    ['net.kalio.test.GOLIATH_PHONE'] = {
        platform_services = {
            http         = { name='http',   enabled=true, src='platform_services_http', essential=true },
            iap          = { name='iap_premium',  enabled=true, src='platform_services_iap_premium'},
            --cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud'},
            news         = { name='news_ih',  enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kra-appstore-iphone',
                                 news_store='appstore',
                           }},
            goliath      = { name='goliath',  enabled=true, order=90, src='platform_services_mc_goliath', params={
                                 platform='apple',
                                 game_id='1943',  -- mobile v1.1
                                 staging = {
                                     api_key='6933973c-d471-418e-9c55-a15e2880b18a',
                                     shared_secret='a0577d5b-f1fa-415e-a20b-7e2a09674c32',
                                     api_url='https://6933973c-d471-418e-9c55-a15e2880b18a.goliath.atlas.bi.miniclippt.com',
                                 },
                                 production = {
                                     api_key='2f4aa74d-dde8-4c79-8abd-920c44e7e97a',
                                     shared_secret='ff983ecb-5eab-449b-8e29-0f17a14afb1c',
                                     api_url='https://2f4aa74d-dde8-4c79-8abd-920c44e7e97a.goliath.atlas.bi.miniclippt.com',
                                 },
                           }},            
        }
    },
    
    -- Console Nintendo Switch devel
    ['com.ironhidegames.frontiers.nx.DEVEL'] = {},

    -- Console Console Xbox One devel
    ['com.ironhidegames.kingdomrush.xbox.DEVEL'] = { overrides={'xbox'}, },
    ['com.ironhidegames.frontiers.xbox.DEVEL']   = { overrides={'xbox'}, },
    ['com.ironhidegames.origins.xbox.DEVEL']     = { overrides={'xbox'}, },
        
    -- Android on mac
    ['net.kalio.test.android.DEVEL'] = {
        requires_privacy_policy = true,
        hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        platform_services = {
            http         = { name='http',   enabled=true, src='platform_services_http', essential=true },
            iap          = { name='gpiab',  enabled=true, src='test_platform_services_iap'},
            news         = { name='news_ih',enabled=true, src='platform_services_news_ih', params={
                                 news_id='kr-googleplay',
                                 --news_id='IH-kr2-phone-android',  -- TODO: change to krf-googleplay
                                 --news_id='IH-kr3-phone-android',  -- TODO: change to kro-googleplay
                                 news_store='google play',
                           }},
        }
    },

    -- Android fullads development on mac
    ['net.kalio.test.android.fullads'] = {
        requires_privacy_policy = true,
        platform_services = {
            http    = { name='http',  enabled=true, src='platform_services_http', essential=true },
            iap     = { name='fullads',  enabled=true, src='platform_services_fullads'},
            fullads = { name='fullads',  enabled=true, src='platform_services_fullads'},
            ads     = { name='ads',      enabled=true, src='test_platform_services_ads', params={
                            providers={
                                yodo1 = {enabled=true, srvid=10, srvparams={appid='5a27f15555fbf70b8d778232',signature='7438aa207bc32e65f62dd17a2116bf7cebc44d3f'}},                            
                            },
                            prio={'yodo1'},
                      }},
        },
    },

    -- Android on phone, with Kalio accounts values
    ['net.kalio.test.android.kalio.account'] = {    
        platform_services = {
            achievements = { name='gps',   enabled=true, src='platform_services_gps' },  -- google play services
            leaderboards = { name='gps',   enabled=true, src='platform_services_gps' },
            cloudsave    = { name='gps',   enabled=true, src='platform_services_gps' },
            iap          = { name='gpiab', enabled=true, src='platform_services_gpiab', params={pubkey='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlGjl0ZiYv/kUMI60l9ZDAij4B97N3I1m7NTJPn+T9wsc/iiYOTsKzIA6AaCrIvoySwkzQH1S1tsjGYxUf+6lcR0knDMxxAOcZbemlWGt3p0gUY36taZ/3UtZPHGMMsY1QkY9MQ/duI8mmMuJh7mDPssrK+4PkNeC24huxwc2Fuk9VYTQezviRv65Vitq5N3BVvSzPisnc4Q3z/3lDHfAwmcnFNYifwjC9VC/B8YnIouAlXsS5UOkmNDXma/ViYGkVQMalWSXq8zmjyTX4wx3mqfosnOMwJot/JhO4veRJqPMaVScXB1LwkB1gOmhrdaD/XMthjO3fPnhlpJVUo6rdwIDAQAB'}},
            remoteconfig = { name='fbrc',  enabled=true, src='platform_services_firebase_rc'},
            analytics    = { name='fba',   enabled=true, src='platform_services_firebase_a'},            
            ads = {
                name='ads', enabled=true, src='platform_services_ads', params={
                    providers={
                        chartboost={enabled=true, srvid=10, srvparams={appid='5a27f15555fbf70b8d778232',signature='7438aa207bc32e65f62dd17a2116bf7cebc44d3f'}},                            
                        adcolony={enabled=true, srvid=11,  srvparams={appid='app72bfc91143e3499381', zoneids='vz9c7f447ff53d4d88b1'}},                        
                    },
                    prio={'adcolony', 'chartboost'},   -- tiers of priority { {1a,1b}, {2}, {3a,3b,4c} }
            }},
        }
    },
    
    -- iOS fullads development (iPad/iPhone)
    ['net.kalio.test.ios.fullads'] = {
        requires_privacy_policy = true,
        hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        platform_services = {
            http         = { name='http',  enabled=true, src='platform_services_http', essential=true },
            news         = { name='news_ih',enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kr-appstore-fullads',
                                 news_store='appstore',
                           }},
            iap          = { name='fullads',  enabled=true, src='platform_services_fullads'},
            fullads      = { name='fullads',  enabled=true, src='platform_services_fullads'},            
            ads          = { name='yodo1', enabled=true, src='platform_services_yodo1', params={appkey='P8otsgNTFGI'}},
            remoteconfig = { name='fbrc',  enabled=true, src='platform_services_firebase_rc'},
            analytics    = { name='fba',   enabled=true, src='platform_services_firebase_a'},            
            push_noti    = { name='fbm',   enabled=true, src='platform_services_firebase_m'},            
            --dynamiclinks = { name='fbdl',  enabled=true, src='platform_services_firebase_dl'},
            leadeboards  = { name='gamecenter', enabled='true', src='platform_services_gamecenter', params={id='gamecenter_fullads_cn'}},
            achievements = { name='gamecenter', enabled='true', src='platform_services_gamecenter', params={id='gamecenter_fullads_cn'}},
            --cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud'},
        }
    },

    -- iOS with store development (iPad/iPhone)
    ['net.kalio.test.ios.DEVEL'] = {
        requires_privacy_policy = true,
        hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        --simple_privacy_button = true,
        --hide_external_links = true,
        platform_services = {
            http         = { name='http',  enabled=true, src='platform_services_http', essential=true },
            news         = { name='news_ih',enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kr-appstore-fullads',
                                 news_store='appstore',
                           }},
            iap          = { name='gpiab', enabled=true, src='test_platform_services_iap'},            
            --leadeboards  = { name='gamecenter', enabled='true', src='platform_services_gamecenter', params={id='gamecenter'}},
            --achievements = { name='gamecenter', enabled='true', src='platform_services_gamecenter', params={id='gamecenter'}},
        }
    },
    
    
    -- conslole and phone on pc    
    ['net.kalio.test.kr1_console_on_pc'] = {},
    ['net.kalio.test.kr2_console_on_pc'] = {},
    ['net.kalio.test.kr3_console_on_pc'] = {},

    ['net.kalio.test.kr2_phone_on_pc'] = {},    
    ['net.kalio.test.kr2_phone_on_pc'] = {},
    ['net.kalio.test.kr3_phone_on_pc'] = {},

    ['net.kalio.test.kr1_xbox_on_pc'] = {overrides={'xbox'}},
    ['net.kalio.test.kr2_xbox_on_pc'] = {overrides={'xbox'}},
    ['net.kalio.test.kr3_xbox_on_pc'] = {overrides={'xbox'}},
    
    -- Desktop Mac AppStore
    ['net.kalio.test.mac'] = {
        libs={'krequest', 'kgamekit'}, 
        platform_services = {
            achievements = { name='gamecenter', enabled='true', src='platform_services_gamecenter'},
        }
    },

    -- Desktop Premium Devel
    ['net.kalio.test.mac.premium'] = {
        platform_services = {
            iap          = { name='iap_premium',  enabled=true, src='platform_services_iap_premium'},
            --leaderboards = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter_universal_premium'}},                
            --achievements = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter_universal_premium'}},            
            cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud'},
            rating       = { name='appstore_rating', enabled=true, src='platform_services_appstore_rating'},
        }
    },
    
    
    ------------------------------------------------------------
    -- ORIGINAL (NORMAL)

    -- Desktop Standalone: no steam, no appstore
    ['com.ironhidegames.kingdomrush.standalone'] = {
    },

    -- KR1 STEAM --------------------
    -- KR1 Desktop Win Steam
    ['com.ironhidegames.kingdomrush.windows.steam'] = {
        libs={'steam_api'}, 
        platform_services = {
            achievements = { name='steam', enabled='true', src='platform_services_steam', params={app_id=246420}},
        }
    },
    
    -- KR1 Desktop Mac Steam
    ['com.ironhidegames.kingdomrush.mac.steam'] = {
        libs={'steam_api'}, 
        platform_services = {
            achievements = { name='steam', enabled='true', src='platform_services_steam', params={app_id=246420}},
        }
    },
    
    -- KR1 Desktop Linux Steam (Linux+SteamOS)
    ['com.ironhidegames.kingdomrush.linux.steam'] = {
        libs={'steam_api'}, 
        platform_services = {
            achievements = { name='steam', enabled='true', src='platform_services_steam', params={app_id=246420}},
        }
    },

    -- KR1 KONG --------------------
    -- KR1 Desktop Win Kongregate Kartridge
    ['com.ironhidegames.kingdomrush.windows.kongregate'] = {
        libs={'kartridge-sdk'}, 
        platform_services = {
            achievements = { name='kart', enabled='true', src='platform_services_kart', params={game_id=294485}},
        }
    },
    -- KR1 Desktop Mac Kongregate Kartridge
    ['com.ironhidegames.kingdomrush.mac.kongregate'] = {
        libs={'kartridge-sdk'}, 
        platform_services = {
            achievements = { name='kart', enabled='true', src='platform_services_kart', params={game_id=294485}},
        }
    },

    -- KR1 Desktop Windows Store (UWP! Not used)
    ['com.ironhidegames.kingdomrush.windows.msstore'] = {
        --overrides={'xbox'},
        libs={'kxsapi', 'XCurl', 'Microsoft.Xbox.Services.141.GDK.C.Thunks'},
        platform_services = {
            auth         = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl','Microsoft.Xbox.Services.141.GDK.C.Thunks'}, scid='00000000-0000-0000-0000-00006f87eb59', signin_on_init=true }},
            achievements = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl','Microsoft.Xbox.Services.141.GDK.C.Thunks'}, scid='00000000-0000-0000-0000-00006f87eb59', }},
            cloudsave    = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl','Microsoft.Xbox.Services.141.GDK.C.Thunks'}, scid='00000000-0000-0000-0000-00006f87eb59', }},
            joystick     = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl','Microsoft.Xbox.Services.141.GDK.C.Thunks'}, scid='00000000-0000-0000-0000-00006f87eb59', }},
        }        
    },
    
    -- KR1 Desktop Windows (gdk)
    ['com.ironhidegames.kingdomrush.gdk_win'] = {
        platform_services = {
            auth         = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-00006f87eb59', signin_on_init=true }},
            achievements = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-00006f87eb59', }},
            cloudsave    = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-00006f87eb59', }},
            --joystick     = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-00006f87eb59', }},
        }        
    },

    -- KR1 APPSTORE / MAC --------------------
    -- KR1 Desktop Mac AppStore (Mac AppStore only!)
    ['com.ironhidegames.kingdomrush-mac'] = {
        libs={'krequest','kgamekit'}, 
        platform_services = {
            achievements = { name='gamecenter', enabled='true', src='platform_services_gamecenter'},
        }        
    },

    -- KR1 APPSTORE / iOS --------------------
    -- KR1 Phone iOS Appstore
    ['com.armorgames.kingdomrushiphone'] = {
        requires_privacy_policy = true,
        hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        has_restore_savegame = true,
        platform_services = {
            http         = { name='http',     enabled=true, src='platform_services_http', essential=true },
            cmp          = { name='gump',     enabled=true, src='platform_services_gump', params={sync_on_init=true}},--, test_device="BD9BB6FD-61B7-4FD5-84AC-EE4D5A4AC8C4", test_geography="eea"}},
            iap          = { name='storekit', enabled=true, src='platform_services_storekit'},
            rating       = { name='storekit', enabled=true, src='platform_services_storekit'},
            analytics    = { name='fba',      enabled=true, src='platform_services_firebase_a'},  -- also crashlytics
            remoteconfig = { name='fbrc',     enabled=true, src='platform_services_firebase_rc'},
            push_noti    = { name='fbm',      enabled=true, src='platform_services_firebase_m'},
            dynamiclinks = { name='fbdl',     enabled=true, src='platform_services_firebase_dl'},
            deep_links   = { name='deep_links', enabled=true, src='platform_services_deep_links', params={url='https://link.kingdomrush.com'}},
            news         = { name='news_ih',  enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kr',   -- NOTE: kr-googleplay has news to test
                                 news_store='appstore',
                           }},
            ads          = {
               name='ads', enabled=true, src='platform_services_ads', params={
                   providers={
                       admob={enabled=true, srvid=12, srvparams={appid='ca-app-pub-6986192377602678~**********', unitid='ca-app-pub-6986192377602678/**********',}},  -- IMPORTANT: appid must be set on game's plist
                   },
                   prio={'admob'},
            }},
            leaderboards = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter'}},
            achievements = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter'}},
            cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud', params={sync_legacy=true}},
        }
    },

    -- KR1 Tablet iOS Appstore
    ['com.armorgames.kingdomrush'] = {
        requires_privacy_policy = true,
        has_restore_savegame = true,
        hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        platform_services = {
            http         = { name='http',     enabled=true, src='platform_services_http', essential=true },
            cmp          = { name='gump',     enabled=true, src='platform_services_gump', params={sync_on_init=true}},--, test_device="0CB00623-BA08-4631-A55F-ECD5E225571B", test_geography="eea"}},
            iap          = { name='storekit', enabled=true, src='platform_services_storekit'},
            rating       = { name='storekit', enabled=true, src='platform_services_storekit'},
            analytics    = { name='fba',      enabled=true, src='platform_services_firebase_a'},  -- also crashlytics
            remoteconfig = { name='fbrc',     enabled=true, src='platform_services_firebase_rc'},
            push_noti    = { name='fbm',      enabled=true, src='platform_services_firebase_m'},
            dynamiclinks = { name='fbdl',     enabled=true, src='platform_services_firebase_dl'},
            deep_links   = { name='deep_links', enabled=true, src='platform_services_deep_links', params={url='https://link.kingdomrush.com'}},                        
            news         = { name='news_ih',  enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kr',   -- NOTE: kr-googleplay has news to test
                                 news_store='appstore',
                           }},
            ads          = {
               name='ads', enabled=true, src='platform_services_ads', params={
                   providers={
                       admob={enabled=true, srvid=12, srvparams={appid='ca-app-pub-6986192377602678~**********', unitid='ca-app-pub-6986192377602678/**********',}},  -- IMPORTANT: appid must be set on game's plist
                   },
                   prio={'admob'},
            }},
            leaderboards = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter'}},
            achievements = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter'}},
            cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud', params={sync_legacy=true}},
        }
    },
    
    
    -- KR1 Phone iOS Fullads China    
    ['com.ironhidegames.kingdomrush.fullads.cn'] = {
        requires_privacy_policy = true,
        platform_services = {
            http         = { name='http',  enabled=true, src='platform_services_http', essential=true },
            analytics    = { name='fba',   enabled=true, src='platform_services_firebase_a'},  -- also crashlytics
            remoteconfig = { name='fbrc',  enabled=true, src='platform_services_firebase_rc'},
            push_noti    = { name='fbm',   enabled=true, src='platform_services_firebase_m'},
            dynamiclinks = { name='fbdl',  enabled=true, src='platform_services_firebase_dl'},
            news         = { name='news_ih',enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kr-appstore-fullads',
                                 news_store='appstore',
                           }},
            iap          = { name='fullads',  enabled=true, src='platform_services_fullads'},
            fullads      = { name='fullads',  enabled=true, src='platform_services_fullads'},
            ads          = { name='yodo1', enabled=true, src='platform_services_yodo1', params={appkey='EmXusi1nXYH'}},
            leaderboards = { name='gamecenter', enabled='true', src='platform_services_gamecenter', params={id='gamecenter_fullads_cn'}},
            achievements = { name='gamecenter', enabled='true', src='platform_services_gamecenter', params={id='gamecenter_fullads_cn'}},
            cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud'},
        }
    },
    
    -- KR1 CONSOLES --------------------
    -- KR1 Console Nintendo Switch
    ['com.ironhidegames.kingdomrush.nx'] = {
        storage_io = 'storage_io_nx',
        platform_services = {
            nx         = { name='nx', enabled=true, src='platform_services_nx'},
        }
    },

    -- KR1 Console Xbox One (uwp)
    ['com.ironhidegames.kingdomrush.xbox'] = {
        overrides={'xbox'},
        libs={'kr-bridge-uwp-xbox'}, 
        platform_services = {
            auth         = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kr-bridge-uwp-xbox', signin_on_init=true }},
            achievements = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kr-bridge-uwp-xbox' }},
            cloudsave    = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kr-bridge-uwp-xbox' }},
            joystick     = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kr-bridge-uwp-xbox' }},
        }        
    },

    -- KR1 Console Xbox One (gdk)
    ['com.ironhidegames.kingdomrush.gdk_xbox'] = {
        overrides={'xbox'},
        --libs={'kr-bridge-uwp-xbox'}, 
        platform_services = {
            auth         = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-00006f87eb59', signin_on_init=true }},
            achievements = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-00006f87eb59', }},
            cloudsave    = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-00006f87eb59', }},
            joystick     = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-00006f87eb59', }},
            
        }        
    },
    
    -- KR1 ANDROID --------------------
    -- KR1 Phone Android Google Play
    ['com.ironhidegames.android.kingdomrush'] = {
        requires_privacy_policy = true,
        hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        has_restore_savegame = true,
        more_games_with_label = true,
        platform_services = {
            license      = { name='glvl2', enabled=true, src='platform_services_glvl2', essential=true },
            http         = { name='http',  enabled=true, src='platform_services_http', essential=true },
            cmp          = { name='gump',  enabled=true, src='platform_services_gump', params={sync_on_init=true}}, --, test_device="91437805F3F10757A18115711FD2B3D6", test_geography="eea"}},
            achievements = { name='gps',   enabled=true, src='platform_services_gps' },  -- google play services
            leaderboards = { name='gps',   enabled=true, src='platform_services_gps' },
            cloudsave    = { name='gps',   enabled=true, src='platform_services_gps' },
            iap          = { name='gpiab', enabled=true, src='platform_services_gpiab', params={pubkey='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiLmcZaqXkNLP4qa67drTXzNFPCIZtZoMKyjm3XvmDNUGxlXZVaikluoQ/wvOtgbnRG9UcyqhhOras0idQDQvixC0WC7j9GZy+WvT16oExPgbSnTLY8IcxKLotDrXFs9bqJrFOCZT+GLDXDxK7dtxeqdaANF98vKJHbaNRF5iD6J/BehaeZGpDyzLqhEpK+GxI2e8/SFELRSBG0Dr0CeHk37sXTJKxKHEzif2Fq14cAdLfZD/JGJcVfnsvy2F3LYz8OzwqY8U4L//vt7jQ7EEX1zHVBRK+CMxTTsVi0RLQvAFT7xGKl3/J3bdnkW/Smk36Tnxy8P0GHR75MBYKuJQaQIDAQAB'}},
            remoteconfig = { name='fbrc',  enabled=true, src='platform_services_firebase_rc'},
            analytics    = { name='fba',   enabled=true, src='platform_services_firebase_a'},
            push_noti    = { name='fbm',   enabled=true, src='platform_services_firebase_m'},
            ads          = {
                name='ads', enabled=true, src='platform_services_ads', params={
                    providers={
                        chartboost={enabled=true, srvid=10, srvparams={appid='55637f50c909a63644546846',signature='eb9c93e2d1496d38d0403f37b2d9b752a48a4cf6'}},
                        --adcolony={enabled=true, srvid=11,  srvparams={appid='app1f686aea683f4492ab', zoneids='vza470b15baba84e739e'}},
                        admob={enabled=true, srvid=12,  srvparams={appid='ca-app-pub-6986192377602678~**********', unitid='ca-app-pub-6986192377602678/**********', }},    -- production
                                                                   --test_device_id='0A820683C85FFD5E3C8AC00AEF7BC8C9'}},   -- test: moto g4
                                                                   --test_device_id = "8C72AF6B1AAB510C66DA6217158D0356"}}, -- test: huawei sne-lx3  (from log line RequestConfiguration.Builder().setTestDeviceIds...
                        --admob={enabled=true, srvid=12,  srvparams={appid='ca-app-pub-3940256099942544~**********', unitid='ca-app-pub-3940256099942544/5224354917'}}, -- testing app/unit
                        
                    },
                    prio={'admob', 'adcolony', 'chartboost'},   -- tiers of priority { {1a,1b}, {2}, {3a,3b,4c} }
            }},
            news         = { name='news_ih',enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kr-googleplay',
                                 news_store='google play',
                           }},
            dynamiclinks = { name='fbdl', enabled=true, src='platform_services_firebase_dl'},
            deep_links = { name='deep_links', enabled=true, src='platform_services_deep_links', params={url='https://link.kingdomrush.com'}},
        }
    },

    -- Phone Android Humble Bundle / Bemobi
    ['com.ironhidegames.android.kingdomrush.humble'] = {
        requires_privacy_policy = true,
        hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        platform_services = {
            http         = { name='http',  enabled=true, src='platform_services_http', essential=true },
            achievements = { name='gps',   enabled=true, src='platform_services_gps' },
            leaderboards = { name='gps',   enabled=true, src='platform_services_gps' },
            cloudsave    = { name='gps',   enabled=true, src='platform_services_gps' },
            analytics    = { name='fba',   enabled=true, src='platform_services_firebase_a'},
            remoteconfig = { name='fbrc',  enabled=true, src='platform_services_firebase_rc'},
            push_noti    = { name='fbm',   enabled=true, src='platform_services_firebase_m'},
        }        
    },

    -- KR1 Phone Android Hatch
    ['com.ironhidegames.android.kingdomrush.hatch'] = {
    },

    -- KR1 Phone Android Yodo1 SDK (Fullads)
    ['com.ironhidegames.android.kingdomrush.yodo1sdk'] = {
        requires_privacy_policy = false,
        more_games_with_label = true,
        forced_locale = 'zh-Hans',  -- disables language selection button in settings
        default_locale = 'zh-Hans',
        hide_privacy_policy = true,  -- uses yodo PP
        hide_external_links = true,
        hide_strategy_guide = true, -- because it's not translated to chinese it cannot be used in chinese distribution channels
        resource_dirs={'yodo_splash'},
        overrides={'yodo1sdk'},
        show_options_footer = {
            text_key="YODO1_OPTIONS_FOOTER"
        },
        show_age_rating_popup = {
            icon='_assets/_resources/com.ironhidegames.android.kingdomrush.yodo1sdk/yodo_cadpa/cadpa-12-s.png',
            text_key='YODO1_SDK_CADPA_12_TIPS',
        },
        show_splash_custom = {
            path='yodo_splash',
            sound=nil,
            image='Yodo1-Logo-cn2.png',
            text_key='YODO1_SDK_SPLASH_CUSTOM_TEXT',
        },
        show_consent_simple = {
            style='yodo1sdk',
            template='screen_consent_simple',
            consent_key='YODO1_SDK_CONSENT_MSG',
            tos_key = 'YODO1_SDK_TOS_BUTTON',
            
        },
        platform_services = {
            channel      = { name='yodo1sdk', enabled=true, src='platform_services_yodo1_sdk', params={yodo1_appkey='uPnMsLj0zMP'} },
            auth         = { name='yodo1sdk', enabled=true, src='platform_services_yodo1_sdk', params={yodo1_appkey='uPnMsLj0zMP'} },
            ads          = { name='yodo1sdk', enabled=true, src='platform_services_yodo1_sdk', params={yodo1_appkey='uPnMsLj0zMP'} },
            analytics    = { name='yodo1sdk', enabled=true, src='platform_services_yodo1_sdk', params={yodo1_appkey='uPnMsLj0zMP'} },            
            iap          = { name='fullads',  enabled=true, src='platform_services_fullads'},
            fullads      = { name='fullads',  enabled=true, src='platform_services_fullads'},
        }
    },
    
    ------------------------------------------------------------
    -- FRONTIERS
    
    -- KR2 Desktop Standalone: no steam, no app store.
    ['com.ironhidegames.frontiers.standalone'] = {
        platform_services = {
            iap = { name='iap_premium',  enabled=true, src='platform_services_iap_premium'}, -- endless + gems
        },        
    },

    -- KR2 Desktop Win Steam
    ['com.ironhidegames.frontiers.windows.steam'] = {
        libs={'steam_api'}, 
        platform_services = {
            iap          = { name='iap_premium',  enabled=true, src='platform_services_iap_premium'},
            achievements = { name='steam', enabled='true', src='platform_services_steam', params={app_id=458710}},
            leaderboards = { name='steam', enabled='true', src='platform_services_steam', params={app_id=458710}},
        }
    },

    -- KR2 Desktop Win CYUGAME Rail
    ['com.ironhidegames.frontiers.windows.cyugame'] = {
        overrides={'censored_cn'},
        pops_hidden = true,
        gov_approval_id = '',
        health_warning = true,
        hide_achievements_popup = true,
        default_locale = 'zh-Hans',
        libs={'rail_api','rail_c_wrapper'}, 
        platform_services = {
            --leaderboards with in
            achievements = { name='rail', enabled=true, src='platform_services_rail', params={game_id=2000212}},
            -- no need: cloudsave    = { name='rail', enabled=true, src='platform_services_rail' },
        },
    },
    
    -- KR2 Desktop Mac Steam
    ['com.ironhidegames.frontiers.mac.steam'] = {
        libs={'steam_api'}, 
        platform_services = {
            iap          = { name='iap_premium',  enabled=true, src='platform_services_iap_premium'},
            achievements = { name='steam', enabled='true', src='platform_services_steam', params={app_id=458710}},
            leaderboards = { name='steam', enabled='true', src='platform_services_steam', params={app_id=458710}},
        }
    },

    -- KR2 Desktop Mac AppStore (Mac AppStore only!)
    ['com.ironhidegames.kingdomrushfrontiers-mac'] = {
        requires_privacy_policy = false,
        libs={'krequest', 'kgamekit', 'kcloud', 'kstore'},
        platform_services = {
            iap          = { name='iap_premium', enabled=true, src='platform_services_iap_premium'},
            leaderboards = { name='gamecenter', enabled=true, src='platform_services_gamecenter',   params={id='gamecenter'}}, 
            achievements = { name='gamecenter', enabled='true', src='platform_services_gamecenter', params={id='gamecenter'}},
            rating       = { name='appstore_rating', enabled=true, src='platform_services_appstore_rating'},
        }        
    },

    -- KR2 Desktop Linux Steam (Linux+SteamOS)
    ['com.ironhidegames.frontiers.linux.steam'] = {
        libs={'steam_api'}, 
        platform_services = {
            iap          = { name='iap_premium',  enabled=true, src='platform_services_iap_premium'},
            achievements = { name='steam', enabled='true', src='platform_services_steam', params={app_id=458710}},
            leaderboards = { name='steam', enabled='true', src='platform_services_steam', params={app_id=458710}},
        }
    },

    -- KR2 Desktop Windows Store
    ['com.ironhidegames.frontiers.windows.msstore'] = {
        --overrides={'xbox'},
        libs={'kxsapi', 'XCurl', 'Microsoft.Xbox.Services.141.GDK.C.Thunks'},
        platform_services = {
            auth         = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl','Microsoft.Xbox.Services.141.GDK.C.Thunks'}, scid='00000000-0000-0000-0000-000063cbc155', signin_on_init=true }},
            achievements = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl','Microsoft.Xbox.Services.141.GDK.C.Thunks'}, scid='00000000-0000-0000-0000-000063cbc155', }},
            cloudsave    = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl','Microsoft.Xbox.Services.141.GDK.C.Thunks'}, scid='00000000-0000-0000-0000-000063cbc155', }},
            joystick     = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl','Microsoft.Xbox.Services.141.GDK.C.Thunks'}, scid='00000000-0000-0000-0000-000063cbc155', }},
        }        
    },

    -- KR2 Desktop Windows (gdk)
    ['com.ironhidegames.frontiers.gdk_win'] = {
        platform_services = {
            auth         = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-000063cbc155', signin_on_init=true }},
            achievements = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-000063cbc155', }},
            cloudsave    = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-000063cbc155', }},
            --joystick     = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-000063cbc155', }},
        }        
    },
    
    -- KR2 Console Nintendo Switch
    ['com.ironhidegames.frontiers.nx'] = {
        storage_io = 'storage_io_nx',
        platform_services = {
            nx         = { name='nx', enabled=true, src='platform_services_nx'},
        }
    },

    -- KR2 Console Xbox One (uwp)
    ['com.ironhidegames.frontiers.xbox'] = {
        overrides={'xbox'},
        libs={'kr-bridge-uwp-xbox'}, 
        platform_services = {
            auth         = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kr-bridge-uwp-xbox', signin_on_init=true }},
            achievements = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kr-bridge-uwp-xbox' }},
            cloudsave    = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kr-bridge-uwp-xbox' }},
            joystick     = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kr-bridge-uwp-xbox' }},
        }        
    },
    
    -- KR2 Console Xbox One (gdk)
    ['com.ironhidegames.frontiers.gdk_xbox'] = {
        overrides={'xbox'},
        platform_services = {
            auth         = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-000063cbc155', signin_on_init=true }},
            achievements = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-000063cbc155', }},
            cloudsave    = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-000063cbc155', }},
            joystick     = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-000063cbc155', }},
            
        }        
    },

    -- KR2 Phone Android Google Play
    ['com.ironhidegames.android.kingdomrushfrontiers'] = {
        requires_privacy_policy = true,
        hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        has_challenges = true,
        has_restore_savegame = true,
        more_games_with_label = true,
        platform_services = {
            license      = { name='glvl2', enabled=true, src='platform_services_glvl2', essential=true },
            http         = { name='http',  enabled=true, src='platform_services_http', essential=true },
            cmp          = { name='gump',  enabled=true, src='platform_services_gump', params={sync_on_init=true}}, --, test_device="E8A96B95833F0847C6CE4F37BC060B7D", test_geography="eea"}},
            achievements = { name='gps',   enabled=true, src='platform_services_gps' },  -- google play services
            leaderboards = { name='gps',   enabled=true, src='platform_services_gps' },
            cloudsave    = { name='gps',   enabled=true, src='platform_services_gps' },
            iap          = { name='gpiab', enabled=true, src='platform_services_gpiab', params={pubkey='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiCr+CFO87fc2ElbeLKx0r0lULQAfl70lo4hlpX9nIVWF/ve3xBZ/+LsN3/3EcVC1rhN+QNWzFOnSEtANoACy7le0gGgAiDNI3/rrc7EJc6wPuM2XFD55omrJylBXs00bJ6lwVX5ffqgYqooMREQoFqaEX9VCoBr0JkDEfKntoXgPNWc69EQPHIsrvs0YnnwfHphD5Fw4Cl93KF4UvkD3LqHruJQZ4qt94eFe1cVNAoKY7zt3fRC9jKWFzNasWbFYD0z12lobuuJ5X014vyaln+aGByOgbDkEb/vlQUAp+Rg2vazLEs0vE3XK5dySnCcxkZNaFY5dNCcVM78+WqLTnQIDAQAB'}},
            remoteconfig = { name='fbrc',  enabled=true, src='platform_services_firebase_rc'},
            analytics    = { name='fba',   enabled=true, src='platform_services_firebase_a'},
            push_noti    = { name='fbm',   enabled=true, src='platform_services_firebase_m'},
            ads = {
                name='ads', enabled=true, src='platform_services_ads', params={
                    providers={
                        chartboost={enabled=true, srvid=10, srvparams={appid='557eddd20d60256bd33dec0b',signature='a579580e4fe1fac66c51bef9a3eeff89820b92dc'}},
                        --adcolony={enabled=true, srvid=11,  srvparams={appid='appd3df3a10cfd848e989', zoneids='vzd8c1293b8b6b48fab4'}},  -- service stopped working on 2024-01
                        admob={enabled=true, srvid=12,  srvparams={appid='ca-app-pub-6986192377602678~**********', unitid='ca-app-pub-6986192377602678/**********', }},
                    },
                    prio={'admob', 'adcolony', 'chartboost'},   -- tiers of priority { {1a,1b}, {2}, {3a,3b,4c} }
            }},
            news         = { name='news_ih',enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='krf-googleplay',
                                 news_store='google play',
                           }},
            dynamiclinks = { name='fbdl', enabled=true, src='platform_services_firebase_dl'},
            deep_links = { name='deep_links', enabled=true, src='platform_services_deep_links', params={url='https://link.kingdomrushfrontiers.com'}},
        }
    },

    -- KR2 Phone Android Amazon
    ['com.ironhidegames.android.kingdomrushfrontiers.amazon'] = {
    },

    -- KR2 Phone Android Humble Bundle/Bemobi
    ['com.ironhidegames.android.kingdomrushfrontiers.humble'] = {
        requires_privacy_policy = true,
        hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        platform_services = {
            http         = { name='http',  enabled=true, src='platform_services_http', essential=true },
            achievements = { name='gps',   enabled=true, src='platform_services_gps' },
            leaderboards = { name='gps',   enabled=true, src='platform_services_gps' },
            cloudsave    = { name='gps',   enabled=true, src='platform_services_gps' },
            analytics    = { name='fba',   enabled=true, src='platform_services_firebase_a'},
            remoteconfig = { name='fbrc',  enabled=true, src='platform_services_firebase_rc'},
        }        
    },

    -- KR2 Phone Android Huawei
    ['com.ironhidegames.android.kingdomrushfrontiers.huawei'] = {
        platform_services = {
            analytics    = { name='fba',   enabled=true, src='platform_services_firebase_a'},
            remoteconfig = { name='fbrc',  enabled=true, src='platform_services_firebase_rc'},            
            push_noti    = { name='fbm',   enabled=true, src='platform_services_firebase_m'},
            ads = {
                name='ads', enabled=true, src='platform_services_ads', params={
                    providers={
                        chartboost={enabled=true, srvid=10, srvparams={appid='557eddd20d60256bd33dec0b',signature='a579580e4fe1fac66c51bef9a3eeff89820b92dc'}},
                        adcolony={enabled=true, srvid=11,  srvparams={appid='appd3df3a10cfd848e989', zoneids='vzd8c1293b8b6b48fab4'}},
                    },
                    prio={'adcolony', 'chartboost'},   -- tiers of priority { {1a,1b}, {2}, {3a,3b,4c} }
            }},
            iap          = { name='gpiab', enabled=true, src='platform_services_hwpms',
                             params={
                                 app_id        ='100522083',
                                 cp_id         ='999',
                                 merchant_id   ='999',
                                 merchant_name ='Ironhide S.A.',
                                 priv_key      ='xx',
                                 pub_key       ='xx',
                                 drm_on        ='0',
                                 drm_id        ='xxx',
                                 drm_pub_key   ='xxx',
                                 assistant_on  ='0',
            }},
        }
    },

    -- KR2 Phone Android Hatch
    ['com.ironhidegames.android.kingdomrushorigins.hatch'] = {
    },
    
    -- KR2 Phone iOS Fullads China    
    ['com.ironhidegames.kingdomrushfrontiers.fullads.cn'] = {
        requires_privacy_policy = true,
        platform_services = {
            http         = { name='http',  enabled=true, src='platform_services_http', essential=true },
            analytics    = { name='fba',   enabled=true, src='platform_services_firebase_a'},  -- also crashlytics
            remoteconfig = { name='fbrc',  enabled=true, src='platform_services_firebase_rc'},
            push_noti    = { name='fbm',   enabled=true, src='platform_services_firebase_m'},
            dynamiclinks = { name='fbdl',  enabled=true, src='platform_services_firebase_dl'},
            news         = { name='news_ih',enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kr-appstore-fullads',
                                 news_store='appstore',
                           }},
            iap          = { name='fullads',  enabled=true, src='platform_services_fullads'},
            fullads      = { name='fullads',  enabled=true, src='platform_services_fullads'},
            ads          = { name='yodo1', enabled=true, src='platform_services_yodo1', params={appkey='U77isi1ptse'}},
            leaderboards = { name='gamecenter', enabled='true', src='platform_services_gamecenter', params={id='gamecenter_fullads_cn'}},
            achievements = { name='gamecenter', enabled='true', src='platform_services_gamecenter', params={id='gamecenter_fullads_cn'}},
            cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud'},
        }
    },

    -- KR2 AppStore Greats (ios universal + macOS)
    ['com.ironhidegames.frontiers.universal.premium'] = {
        universal = true, 
        phone = {
            splash_video_service = 'video',
            splash_video_service_path = 'splash_videos',
            splash_video_service_items = { -- manually copy them to the target dir
                --'4.3x3_Landscape.mp4',  -- ipad only
                --'4x3_Landscape.mp4',    -- ipad only
                '16x9_Landscape_1920.mp4',
                '16x9_Landscape_2560.mp4',
                --'16x9_Landscape_4096.mp4', -- mac only
                --'16x9_Landscape_5120.mp4', -- mac only
                --'16x10_Landscape_1440.mp4', -- mac only
                --'16x10_Landscape_2880.mp4', -- mac only
                '19.5x9_Landscape.mp4',
            },
            splash_fade_out_duration = 0.25,
            requires_privacy_policy = false,
            simple_privacy_button = true,
            delay_services_init = true,
            hide_external_links = true,
            platform_services = {
                video        = { name='kvideo', enabled=true, src='platform_services_kvideo', essential=true},
                iap          = { name='iap_premium',  enabled=true, src='platform_services_iap_premium'},
                leaderboards = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter_universal_premium'}},
                achievements = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter_universal_premium'}},
                cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud'},
                rating       = { name='appstore_rating', enabled=true, src='platform_services_appstore_rating'},
            }
        },
        tablet = {
            splash_video_service = 'video',
            splash_video_service_path = 'splash_videos',
            splash_video_service_items = {
                '4.3x3_Landscape.mp4',
                '4x3_Landscape.mp4',
                --'16x9_Landscape_1920.mp4', 
                --'16x9_Landscape_2560.mp4',
                --'16x9_Landscape_4096.mp4',  -- mac only
                --'16x9_Landscape_5120.mp4',  -- mac only
                --'16x10_Landscape_1440.mp4', -- mac only
                --'16x10_Landscape_2880.mp4', -- mac only
                --'19.5x9_Landscape.mp4',
            },
            splash_fade_out_duration = 0.25,
            requires_privacy_policy = false,
            simple_privacy_button = true,
            delay_services_init = true,
            hide_external_links = true,
            asset_all_fallback  = {
                {path='all-phone', stop=true},
            },
            asset_game_fallback = {
                {path='kr2-tablet' , texture_size='ipadhd'},
                {path='kr2-phone', texture_size='iphonehd'}
            },
            platform_services = {
                video        = { name='kvideo', enabled=true, src='platform_services_kvideo', essential=true},
                iap          = { name='iap_premium',  enabled=true, src='platform_services_iap_premium'},
                leaderboards = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter_universal_premium'}},
                achievements = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter_universal_premium'}},
                cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud'},
                rating       = { name='appstore_rating', enabled=true, src='platform_services_appstore_rating'},
            }
        },
        desktop = {
            splash_fade_out_duration = 0.25,
            splash_video_service = 'video',
            splash_video_service_path = 'splash_videos',
            splash_video_service_items = {
                '4.3x3_Landscape.mp4',
                '4x3_Landscape.mp4',
                '16x9_Landscape_1920.mp4',
                '16x9_Landscape_2560.mp4',
                '16x9_Landscape_4096.mp4',
                '16x9_Landscape_5120.mp4',
                '16x10_Landscape_1440.mp4',
                '16x10_Landscape_2880.mp4',
                '19.5x9_Landscape.mp4',
            },
            requires_privacy_policy = false,
            delay_services_init = true,
            hide_external_links = true,
            -- NOTE: export DYLD_LIBRARY_PATH=/Users/<USER>/doc/src/ih/kr-love/platform/bin/macOS; to run love from the command line and find these libs
            libs={'krequest', 'kgamekit', 'kcloud', 'kstore', 'kfairplay', 'kvideo'},
            resource_dirs={'splash_videos'},
            platform_services = {
                license      = { name='fairplay', enabled=true, src='platform_services_fairplay', essential=true },
                video        = { name='kvideo', enabled=true, src='platform_services_kvideo', essential=true},
                iap          = { name='iap_premium', enabled=true, src='platform_services_iap_premium'},
                leaderboards = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter_universal_premium'}},                
                achievements = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter_universal_premium'}},
                cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud'},
                rating       = { name='appstore_rating', enabled=true, src='platform_services_appstore_rating'},
            }
        },
    },

    -- KR2 APPSTORE / iOS -----------------------
    -- KR2 Phone iOS Appstore
    ['com.ironhidegames.frontiers'] = {
        requires_privacy_policy = true,
        hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        --has_challenges = true,
        has_restore_savegame = true,
        platform_services = {
            http         = { name='http',     enabled=true, src='platform_services_http', essential=true },
            cmp          = { name='gump',     enabled=true, src='platform_services_gump', params={sync_on_init=true}},--, test_device="2BA05C50-25C9-44D4-8780-A557ECD949AD", test_geography="eea"}},
            iap          = { name='storekit', enabled=true, src='platform_services_storekit'},
            rating       = { name='storekit', enabled=true, src='platform_services_storekit'},
            analytics    = { name='fba',      enabled=true, src='platform_services_firebase_a'},  -- also crashlytics
            remoteconfig = { name='fbrc',     enabled=true, src='platform_services_firebase_rc'},
            push_noti    = { name='fbm',      enabled=true, src='platform_services_firebase_m'},
            dynamiclinks = { name='fbdl',     enabled=true, src='platform_services_firebase_dl'},
            deep_links   = { name='deep_links', enabled=true, src='platform_services_deep_links', params={url='https://link.kingdomrushfrontiers.com'}},
            news         = { name='news_ih',  enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kr-frontiers',   -- NOTE: kr-googleplay has news to test
                                 news_store='appstore',
                           }},
            ads          = {
               name='ads', enabled=true, src='platform_services_ads', params={
                   providers={
                       admob={enabled=true, srvid=12, srvparams={appid='ca-app-pub-6986192377602678~**********', unitid='ca-app-pub-6986192377602678/**********',}},  -- IMPORTANT: appid must be set on game's plist
                   },
                   prio={'admob'},
            }},
            leaderboards = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter'}},
            achievements = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter'}},
            cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud', params={sync_legacy=true}},
        }
    },

    -- KR2 Tablet iOS Appstore
    ['com.ironhidegames.frontiers-hd'] = {
        requires_privacy_policy = true,
        has_restore_savegame = true,
        hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        platform_services = {
            http         = { name='http',     enabled=true, src='platform_services_http', essential=true },
            cmp          = { name='gump',     enabled=true, src='platform_services_gump', params={sync_on_init=true}},--, test_device="7824CEE5-F3A9-4376-8CF4-5B2271CAC88A", test_geography="eea"}},
            iap          = { name='storekit', enabled=true, src='platform_services_storekit'},
            rating       = { name='storekit', enabled=true, src='platform_services_storekit'},
            analytics    = { name='fba',      enabled=true, src='platform_services_firebase_a'},  -- also crashlytics
            remoteconfig = { name='fbrc',     enabled=true, src='platform_services_firebase_rc'},
            push_noti    = { name='fbm',      enabled=true, src='platform_services_firebase_m'},
            dynamiclinks = { name='fbdl',     enabled=true, src='platform_services_firebase_dl'},
            deep_links   = { name='deep_links', enabled=true, src='platform_services_deep_links', params={url='https://link.kingdomrushfrontiers.com'}},
            news         = { name='news_ih',  enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kr-frontiers',   -- NOTE: kr-googleplay has news to test
                                 news_store='appstore',
                           }},
            ads          = {
               name='ads', enabled=true, src='platform_services_ads', params={
                   providers={
                       admob={enabled=true, srvid=12, srvparams={appid='ca-app-pub-6986192377602678~**********', unitid='ca-app-pub-6986192377602678/**********',}},  -- IMPORTANT: appid must be set on game's plist
                   },
                   prio={'admob'},
            }},
            leaderboards = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter'}},
            achievements = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter'}},
            cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud', params={sync_legacy=true}},
        }
    },

    ------------------------------------------------------------
    -- ORIGINS 

    -- KR3 Desktop Standalone: no steam, no app store.
    ['com.ironhidegames.origins.standalone'] = {
    },

    -- KR3 STEAM --------------------
    -- KR3 Desktop Win Steam
    ['com.ironhidegames.origins.windows.steam'] = {
        libs={'steam_api'}, 
        platform_services = {
            achievements = { name='steam', enabled='true', src='platform_services_steam', params={app_id=816340}},
        }
    },
    -- KR3 Desktop Mac Steam
    ['com.ironhidegames.origins.mac.steam'] = {
        libs={'steam_api'}, 
        platform_services = {
            achievements = { name='steam', enabled='true', src='platform_services_steam', params={app_id=816340}},
        }
    },
    -- KR3 Desktop Linux Steam (Linux+SteamOS)
    ['com.ironhidegames.origins.linux.steam'] = {
        libs={'steam_api'}, 
        platform_services = {
            achievements = { name='steam', enabled='true', src='platform_services_steam', params={app_id=816340}},
        }
    },
    
    -- KR3 KONG --------------------
    -- KR3 Desktop Win Kongregate Kartridge
    ['com.ironhidegames.origins.windows.kongregate'] = {
        libs={'kartridge-sdk'}, 
        platform_services = {
            achievements = { name='kart', enabled='true', src='platform_services_kart', params={game_id=299445}},
        }
    },
    -- KR3 Desktop Mac Kongregate Kartridge
    ['com.ironhidegames.origins.mac.kongregate'] = {
        libs={'kartridge-sdk'}, 
        platform_services = {
            achievements = { name='kart', enabled='true', src='platform_services_kart', params={game_id=299445}},
        }
    },

    -- KR3 MAC APP STORE --------------------
    -- KR3 Desktop Mac AppStore (Mac AppStore only!)    
    ['com.ironhidegames.kingdomrushorigins-mac'] = {
        libs={'krequest','kgamekit'}, 
        platform_services = {
            achievements = { name='gamecenter', enabled='true', src='platform_services_gamecenter'},
        }
    },

    -- KR3 Desktop Windows Store
    ['com.ironhidegames.origins.windows.msstore'] = {
        --overrides={'xbox'},
        libs={'kxsapi', 'XCurl', 'Microsoft.Xbox.Services.141.GDK.C.Thunks'},
        platform_services = {
            auth         = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl','Microsoft.Xbox.Services.141.GDK.C.Thunks'}, scid='00000000-0000-0000-0000-000076bfd687', signin_on_init=true }},
            achievements = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl','Microsoft.Xbox.Services.141.GDK.C.Thunks'}, scid='00000000-0000-0000-0000-000076bfd687', }},
            cloudsave    = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl','Microsoft.Xbox.Services.141.GDK.C.Thunks'}, scid='00000000-0000-0000-0000-000076bfd687', }},
            joystick     = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl','Microsoft.Xbox.Services.141.GDK.C.Thunks'}, scid='00000000-0000-0000-0000-000076bfd687', }},
        }        
    },
    
    -- KR3 Desktop Windows (gdk)
    ['com.ironhidegames.origins.gdk_win'] = {
        platform_services = {
            auth         = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-000076bfd687', signin_on_init=true }},
            achievements = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-000076bfd687', }},
            cloudsave    = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-000076bfd687', }},
            --joystick     = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-000076bfd687', }},
        }        
    },

    -- KR3 CONSOLES --------------------
    -- KR3 Console Nintendo Switch
    ['com.ironhidegames.origins.nx'] = {
        storage_io = 'storage_io_nx',
        platform_services = {
            nx         = { name='nx', enabled=true, src='platform_services_nx'},
        }
    },

    -- KR3 Console Xbox One (uwp)
    ['com.ironhidegames.origins.xbox'] = {
        overrides={'xbox'},
        libs={'kr-bridge-uwp-xbox'}, 
        platform_services = {
            auth         = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kr-bridge-uwp-xbox', signin_on_init=true }},
            achievements = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kr-bridge-uwp-xbox' }},
            cloudsave    = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kr-bridge-uwp-xbox' }},
            joystick     = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kr-bridge-uwp-xbox' }},
        }        
    },
    
    -- KR3 Console Xbox One (gdk)
    ['com.ironhidegames.origins.gdk_xbox'] = {
        overrides={'xbox'},
        platform_services = {
            auth         = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-000076bfd687', signin_on_init=true }},
            achievements = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-000076bfd687', }},
            cloudsave    = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-000076bfd687', }},
            joystick     = { name='xbox', enabled='true', src='platform_services_xbox', params={ lib_name='kxsapi', required_libs={'XCurl'}, scid='00000000-0000-0000-0000-000076bfd687', }},            
        }        
    },

    -- KR3 ANDROID --------------------
    -- KR3 Phone Android Google Play
    ['com.ironhidegames.android.kingdomrushorigins'] = {
        requires_privacy_policy = true,
        hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        has_restore_savegame = true,
        more_games_with_label = true,
        platform_services = {
            license      = { name='glvl2', enabled=true, src='platform_services_glvl2', essential=true },
            http         = { name='http',  enabled=true, src='platform_services_http', essential=true },
            cmp          = { name='gump',  enabled=true, src='platform_services_gump', params={sync_on_init=true}},
            achievements = { name='gps',   enabled=true, src='platform_services_gps' },  -- google play services
            leaderboards = { name='gps',   enabled=true, src='platform_services_gps' },
            cloudsave    = { name='gps',   enabled=true, src='platform_services_gps' },
            iap          = { name='gpiab', enabled=true, src='platform_services_gpiab', params={pubkey='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAp35oe37rQpoTD/2udVbuZFU90tRxP4xF4e0C2WaPEAUbhP14fP5sGBGR929qb//C63o87yyrteo3U3R8Xu7W1AleeAU6odDB6TAiQNq+T1VSMAm1z65Z8zkDikEJQhArFmUKaPYCrmaO6jcmMMKOZkeevnN7WFHvAwhOUNDLIqdRXvIU7Zs6+C5Li12YkjVVem6uO3t6gh+dyQBs2MjmBJyHPS6lBnG4kuXngxOOrlSJQKCSXX3R17Jf66cf1S0U4UuPy7j4u5UHYJTdrh0/I/m4svS3Q5dcmziZ7HUkhtqPEugTtSa8LvN4uyixHXK0a7YMaUYsN+QXOZRZFdTcHwIDAQAB'}},
            remoteconfig = { name='fbrc',  enabled=true, src='platform_services_firebase_rc'},
            analytics    = { name='fba',   enabled=true, src='platform_services_firebase_a'},
            push_noti    = { name='fbm',   enabled=true, src='platform_services_firebase_m'},
            ads = {
                name='ads', enabled=true, src='platform_services_ads', params={
                    providers={
                        chartboost={enabled=true, srvid=10, srvparams={appid='557ede8b04b0162e91069b40',signature='5ec7d7cc37bbd1a4892548b6aafe31aff8ce555c'}},
                        --adcolony={enabled=true, srvid=11,  srvparams={appid='app3897f70643ea43a092', zoneids='vzfaa70385fbc348329c'}},
                        admob={enabled=true, srvid=12,  srvparams={appid='ca-app-pub-6986192377602678~**********', unitid='ca-app-pub-6986192377602678/**********', }},
                    },
                    prio={'admob', 'adcolony', 'chartboost'},   -- tiers of priority { {1a,1b}, {2}, {3a,3b,4c} }
            }},
            news         = { name='news_ih',enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kro-googleplay',
                                 news_store='google play',
                           }},
            dynamiclinks = { name='fbdl', enabled=true, src='platform_services_firebase_dl'},
            deep_links = { name='deep_links', enabled=true, src='platform_services_deep_links', params={url='https://link.kingdomrushorigins.com'}},
        }
    },

    -- KR3 Phone Android Amazon
    ['com.ironhidegames.android.kingdomrushorigins.amazon'] = {
    },

    -- KR3 Phone Android Humble Bundle / Bemobi
    ['com.ironhidegames.android.kingdomrushorigins.humble'] = {
        requires_privacy_policy = true,
        hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        platform_services = {
            http         = { name='http',  enabled=true, src='platform_services_http', essential=true },
            achievements = { name='gps',   enabled=true, src='platform_services_gps' },
            leaderboards = { name='gps',   enabled=true, src='platform_services_gps' },
            cloudsave    = { name='gps',   enabled=true, src='platform_services_gps' },
            analytics    = { name='fba',   enabled=true, src='platform_services_firebase_a'},
            remoteconfig = { name='fbrc',  enabled=true, src='platform_services_firebase_rc'},
        }        
    },

    -- KR3 Phone Android Huawei
    ['com.ironhidegames.android.kingdomrushorigins.huawei'] = {
        platform_services = {
            analytics    = { name='fba',   enabled=true, src='platform_services_firebase_a'},
            remoteconfig = { name='fbrc',  enabled=true, src='platform_services_firebase_rc'},            
            push_noti    = { name='fbm',   enabled=true, src='platform_services_firebase_m'},
            ads = {
                name='ads', enabled=true, src='platform_services_ads', params={
                    providers={
                        chartboost={enabled=true, srvid=10, srvparams={appid='557ede8b04b0162e91069b40',signature='5ec7d7cc37bbd1a4892548b6aafe31aff8ce555c'}},
                        adcolony={enabled=true, srvid=11,  srvparams={appid='app3897f70643ea43a092', zoneids='vzfaa70385fbc348329c'}},
                    },
                    prio={'adcolony', 'chartboost'},   -- tiers of priority { {1a,1b}, {2}, {3a,3b,4c} }
            }},
            iap          = { name='hwpms', enabled=true, src='platform_services_hwpms',
                             params={
                                 app_id        ='100522083',
                                 cp_id         ='890598000000000018',
                                 merchant_id   ='890598000000000018',
                                 merchant_name ='Ironhide S.A.',
                                 priv_key      ='MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCa/csnuja35tvmCtlbODE2Q2VNHyDfgmagHFlC2yRr2M+949UaRSWpm+l3mlPgD9q+88RJBXNuzcZhTrXxsOFr4F5MHFj5bryHZR/L6jAlcDOsx7PrhNHFyHaidv6mMtPaxyktoHwSsfyVbHF6Q0ckvkptkGQryV0uq+/TqRnsP+uQfyOWjhFtFZ1MNK+1cZDv369hpS57PZusht7d38F1quOwZjbhDDYXg1WBMy/3hd+rQ6D9uRtZdLojwkNXJQKtH+qz34GfiH8Y7pvYZBwzlP+TKlNogdihQjGPyUpY/wJDBJD31EmaZh8ljhf6Vde9k4Ah8X2crDvh5GiRJhPlAgMBAAECggEAHB3BqpuKfRp9VjIuI3DiZopb2F13AGzesplp7OmlpgXPC87GlA0qq6XLuMVvhURDBaRhfXCmc9mS7QiTE3znq8LoQEzdLCSVQUeMONjrMoNz8FpK7oTp7AP1V3wYLmymh3fdJbiJAXg1zqJ9RHVrdMQrasxIi7Pr+kE5EK/KstNOVatQHP9UyW46uNxdmzrp0TxQ0/AWkC29ZAs4F+8Qk1f0W4mAL+GRgXSUHDgsaBqJuVUXrFPBL9ij8+UjscsjEXYzvYwwUeQWViJ9Pep6bDPEtzDB6sn+JwsVTmhs9ObfyxzHEhtOm0KMiInu/wx79HwMpifHune4ROizxaiwSQKBgQDOvK6YGW+pqD7/xRVVpHjbZZsKGRG5O/5aliuMXOGWBrb9fWDxCO3BlXRn7Nj8w4rLPLo+bTspH12osocmNFRrSqJqU5rHKzmMOohfwcs8lxHfGKljG4VLz3KL7u/bG4jzRRUULMcSMFPVVEajEgHcv/klvljIsR/WiYztyg8o3QKBgQC/7IljdSndk1KsmVUDPkrnoiJzLn2aHdOobb2Rn9zBdJ2BMDgPEoFmySBEG9IkKsVwhTF7YgUf28TIxI5dG0KniEZXJrT+SAyk0Vfm3OO0OVUpQQyGFqDlcs7ZHoQftNfy1n8QuMMS6h6GBZKYXgh0F7pLWskhp/7I3h8JrcXiqQKBgEYo0FO75iI8f8zLNJdQ7tb+H2SI53PmgUZ6yiKjbsc8ViOmxhxmvFFDCDMwtN7q+ZJJdKc06KvnAiD7HVSA09lphb7xyognCiulvZxJp1BTsHP26Z1BvLXim+wTVYEVUh/E5QdovtJUZk7yf9pdhRZGbCY79+N8AOMM4S/dr9FdAoGAO4D7q7FktIT7qfW0LNhAYwaaxblRSdobiGnVz3OP/w7H0oj/qHSlWBfdgCBcC8iTBGrXR5jBW/pITbLbLLTJ+qU+ocCndvshqGwbY8Fye6sW4z5+wof7hIgO57QVZLsuBOVJ6gZOvOuHYSgTJlhpKI0+viQ8ncm3LdHoL5kH9FkCgYEAkEyuPRfbDZ/k1ZdDc0MH66ddbskz/gagUhdZD5eQpNAtAEiizTprn3mvCjWXcy5xSCf7dY1YIeIDxM6Q/k3g92w8jUTaHE3dZuM41lChJ7I5QzEhEDMeKuXl/chxrs6Vla0s6nfYkDCErKLsQGWs02tew84gPYFLzgnuv3fMUuQ=',
                                 pub_key       ='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmv3LJ7o2t+bb5grZWzgxNkNlTR8g34JmoBxZQtska9jPvePVGkUlqZvpd5pT4A/avvPESQVzbs3GYU618bDha+BeTBxY+W68h2Ufy+owJXAzrMez64TRxch2onb+pjLT2scpLaB8ErH8lWxxekNHJL5KbZBkK8ldLqvv06kZ7D/rkH8jlo4RbRWdTDSvtXGQ79+vYaUuez2brIbe3d/BdarjsGY24Qw2F4NVgTMv94Xfq0Og/bkbWXS6I8JDVyUCrR/qs9+Bn4h/GO6b2GQcM5T/kypTaIHYoUIxj8lKWP8CQwSQ99RJmmYfJY4X+lXXvZOAIfF9nKw74eRokSYT5QIDAQAB',
                                 drm_on        = '1',  -- must be string
                                 drm_id        = '890598000000000018',
                                 drm_pub_key   ='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAl4W1ZUhq2vtAYTWQacgWev/Tx7aHchH7JwMwuAYFNS97rsZeHlQG/fEtSzTOmSyHS7A7/uE/IE7ixye7kFoAgowUDYE65UYmYEhLIl5y+uVTXq+sMyN7hGLeudQkV3FubCQvdFcmchg0dvWTqJK5UcnTJU3cn6r0mMP39HumMprRiXG9K9hI29a4OFZU6hRIsDxk+1dkYVyBpjXA5rIlMpjqU+vb7vbiFHgXtQsBmO6dkiZuAqwT4pNfV/fQa9NnQ426oxfYpFKle4ICwvHwn4rYmA7UWYUa/BoZSaBMX3ag0KzipN7bVmIXcFbZAv+Br7UQnYiYkL8/ZtJhIyC4UwIDAQAB',
                                 assistant_on  = '1',  -- must be string
            }},
        }
    },

    -- KR3 Phone Android Hatch
    ['com.ironhidegames.android.kingdomrushorigins.hatch'] = {
    },

    -- KR3 Phone iOS Fullads China    
    ['com.ironhidegames.kingdomrushorigins.fullads.cn'] = {
        requires_privacy_policy = true,
        platform_services = {
            http         = { name='http',  enabled=true, src='platform_services_http', essential=true },
            analytics    = { name='fba',   enabled=true, src='platform_services_firebase_a'},  -- also crashlytics
            remoteconfig = { name='fbrc',  enabled=true, src='platform_services_firebase_rc'},
            push_noti    = { name='fbm',   enabled=true, src='platform_services_firebase_m'},
            dynamiclinks = { name='fbdl',  enabled=true, src='platform_services_firebase_dl'},
            news         = { name='news_ih',enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kr-appstore-fullads',
                                 news_store='appstore',
                           }},
            iap          = { name='fullads',  enabled=true, src='platform_services_fullads'},
            fullads      = { name='fullads',  enabled=true, src='platform_services_fullads'},
            ads          = { name='yodo1', enabled=true, src='platform_services_yodo1', params={appkey='1Glxsi1pQhq'}},
            leaderboards = { name='gamecenter', enabled='true', src='platform_services_gamecenter', params={id='gamecenter_fullads_cn'}},
            achievements = { name='gamecenter', enabled='true', src='platform_services_gamecenter', params={id='gamecenter_fullads_cn'}},
            cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud'},
        }
    },

    -- KR3 APPSTORE / iOS --------------------
    -- KR3 Phone iOS Appstore
    ['com.ironhidegames.kingdomrush.elves'] = {
        requires_privacy_policy = true,
        has_restore_savegame = true,
        hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        platform_services = {
            http         = { name='http',     enabled=true, src='platform_services_http', essential=true },
            cmp          = { name='gump',     enabled=true, src='platform_services_gump', params={sync_on_init=true}},--,test_device="BD9BB6FD-61B7-4FD5-84AC-EE4D5A4AC8C4", test_geography="eea"}},    
            iap          = { name='storekit', enabled=true, src='platform_services_storekit'},
            rating       = { name='storekit', enabled=true, src='platform_services_storekit'},
            analytics    = { name='fba',      enabled=true, src='platform_services_firebase_a'},  -- also crashlytics
            remoteconfig = { name='fbrc',     enabled=true, src='platform_services_firebase_rc'},
            push_noti    = { name='fbm',      enabled=true, src='platform_services_firebase_m'},
            dynamiclinks = { name='fbdl',     enabled=true, src='platform_services_firebase_dl'},
            deep_links   = { name='deep_links', enabled=true, src='platform_services_deep_links', params={url='https://link.kingdomrushorigins.com'}},
            news         = { name='news_ih',  enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kr-elves',   -- NOTE: kr-googleplay has news to test
                                 news_store='appstore',
                           }},
            ads          = {
               name='ads', enabled=true, src='platform_services_ads', params={
                   providers={
                       admob={enabled=true, srvid=12, srvparams={appid='ca-app-pub-6986192377602678~**********', unitid='ca-app-pub-6986192377602678/**********',}},  -- IMPORTANT: appid must be set on game's plist
                   },
                   prio={'admob'},
            }},
            leaderboards = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter'}},
            achievements = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter'}},
            cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud', params={sync_legacy=true}},
        }
    },

    -- KR3 Tablet iOS Appstore
    ['com.ironhidegames.kingdomrush.elves-hd'] = {
        requires_privacy_policy = true,
        has_restore_savegame = true,
        hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        platform_services = {
            http         = { name='http',     enabled=true, src='platform_services_http', essential=true },
            cmp          = { name='gump',     enabled=true, src='platform_services_gump', params={sync_on_init=true}},--, test_device="A3CABB2C-473A-402A-8559-6589963698E5", test_geography="eea"}},
            iap          = { name='storekit', enabled=true, src='platform_services_storekit'},
            rating       = { name='storekit', enabled=true, src='platform_services_storekit'},
            analytics    = { name='fba',      enabled=true, src='platform_services_firebase_a'},  -- also crashlytics
            remoteconfig = { name='fbrc',     enabled=true, src='platform_services_firebase_rc'},
            push_noti    = { name='fbm',      enabled=true, src='platform_services_firebase_m'},
            dynamiclinks = { name='fbdl',     enabled=true, src='platform_services_firebase_dl'},
            deep_links   = { name='deep_links', enabled=true, src='platform_services_deep_links', params={url='https://link.kingdomrushorigins.com'}},            
            news         = { name='news_ih',  enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kr-elves',   -- NOTE: kr-googleplay has news to test
                                 news_store='appstore',
                           }},
            ads          = {
               name='ads', enabled=true, src='platform_services_ads', params={
                   providers={
                       admob={enabled=true, srvid=12, srvparams={appid='ca-app-pub-6986192377602678~**********', unitid='ca-app-pub-6986192377602678/**********',}},  -- IMPORTANT: appid must be set on game's plist
                   },
                   prio={'admob'},
            }},
            leaderboards = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter'}},
            achievements = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter'}},
            cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud', params={sync_legacy=true}},
        }
    },

    -- KR5 ------------------------------------------------------------
    
    -- KR5 iOS Appstore
    ['com.ironhidegames.kingdomrush.alliance'] = {
        main_params = {
            texture_size = 'ipadhd',
            image_db_releases_compressed_data = true,
        },
        asset_all_fallback  = { {path='kr5-phone', stop=true} },
        asset_game_fallback = { {path='kr5-phone' , texture_size='ipadhd'} },
        has_restore_savegame = true,
        requires_privacy_policy = true,
        hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        platform_services = {
            http         = { name='http',       enabled=true, src='platform_services_http', essential=true },
            -- <50
            iap          = { name='storekit',   enabled=true, order=40, src='platform_services_storekit'},
            rating       = { name='storekit',   enabled=true, order=41, src='platform_services_storekit'},
            -- 50
            analytics    = { name='fba',        enabled=true, src='platform_services_firebase_a'},
            remoteconfig = { name='fbrc',       enabled=true, src='platform_services_firebase_rc'},
            deep_links   = { name='deep_links', enabled=true, src='platform_services_deep_links', params={url='https://link.kingdomrushalliance.com'}},
            cloudsave    = { name='icloud',     enabled=true, src='platform_services_icloud'},
            achievements = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter'}},
            news         = { name='news_ih',    enabled=true, src='platform_services_news_ih_https', init_cond={min_launch_count=2}, params={
                              news_id='kra-appstore-iphone',
                              news_store='appstore',
                           }},
            -- >50
            goliath      = { name='goliath',    enabled=true, order=85, src='platform_services_mc_goliath', params={
                                 platform='apple',
                                 game_id='1943',  -- mobile v1.1
                                 staging = {
                                     api_key='6933973c-d471-418e-9c55-a15e2880b18a',
                                     shared_secret='a0577d5b-f1fa-415e-a20b-7e2a09674c32',
                                     api_url='https://6933973c-d471-418e-9c55-a15e2880b18a.goliath.atlas.bi.miniclippt.com',
                                 },
                                 production = {
                                     api_key='2f4aa74d-dde8-4c79-8abd-920c44e7e97a',
                                     shared_secret='ff983ecb-5eab-449b-8e29-0f17a14afb1c',
                                     api_url='https://2f4aa74d-dde8-4c79-8abd-920c44e7e97a.goliath.atlas.bi.miniclippt.com',
                                 },
                           }},
            cmp          = { name='gump',         enabled=true, order=80, src='platform_services_gump',            params={sync_on_init=false}}, --, test_device="6ED5A38D-D3FC-433B-B837-116745A9DA76", test_geography="eea"}},
            overseervice = { name='overseervice', enabled=true, order=89, src='platform_services_ih_overseervice', params={send_install_on_init=true,
                                 staging = {
                                     api_key='91d9e47fd42342ed979a2695993698c9',   -- KR5 TESTING
                                     shared_secret='6VD/ZEbGo0S03OJ/joGjXg==',     -- KR5 TESTING
                                     api_url='https://api.overseervice.ironhidegames.com'
                                 },
                                 production = {
                                     api_key='62e1d3fb562f4629a3a1a27fee253df1',
                                     shared_secret='tynmc+xYC0+n87pmrWmjOg==',
                                     api_url='https://api.overseervice.ironhidegames.com'
                                 },
                           }},
            appsflyer    = { name='appsflyer',  enabled=true, order=90, src='platform_services_appsflyer',  init_cond={on_signals={'screen_slots-ready'}}, params={dev_key='c9K4JCYiD7Gfvri4wmjcx4', apple_app_id='1622869542', has_cmp=true}}, -- syncs cmp 
            push_noti    = { name='fbm',        enabled=true, order=95, src='platform_services_firebase_m', init_cond={min_launch_count=5, on_signals={'screen_slots-ready'}} },
        }
    },

    -- KR5 Android Google Play
    ['com.ironhidegames.android.kingdomrush.alliance'] = {
        main_params = {
            texture_size = 'ipadhd',
            image_db_releases_compressed_data = true,
        },
        fps_fallback = 30,
        fps_fallback_time_threshold_factor = 1.2,  -- 1.2 == 50 fps
        fps_fallback_count_threshold = 60,
        asset_all_fallback  = { {path='kr5-phone', stop=true} },
        asset_game_fallback = { {path='kr5-phone', texture_size='ipadhd'} },
        has_restore_savegame = true,
        requires_privacy_policy = true,
        hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        platform_services = {
            license      = { name='glvl2',  enabled=true, src='platform_services_glvl2', essential=true },
            http         = { name='http',   enabled=true, src='platform_services_http', essential=true },
            -- <50
            iap          = { name='gpiab',  enabled=true, order=40, src='platform_services_gpiab', params={pubkey='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlP+hlvqG0cTu030wwmhh68z4PuWGCqHu33xK3Wvg5b2W+HuNoi7iayP0OEXWmasMshtwT4gyiTcEEFO4b5hy7zkrxVhsLpo2sLD6by2vO+Q/P9Ir6Rb61HhWQqDEgG7DgJih7esqVnBAYD9WUW7jnwLWewQkGKsKRDk03245IYLXMmvjRMnsCuWvtyHgyPRMBUz3CYDsfOhp1iYHLBOh3cmC/LaXnZCkaM3IoTAjYyAHlCNjrdKFQs5qNX1YujdE4OnzJzlqnhxRUwKS89T4WXAsNYyGyRdskdrOEbsCvp/EvRg/B21NZlept4rNUdLHnaq5vslR5XspkfqFgnaeywIDAQAB'}},
            -- 50
            analytics    = { name='fba',    enabled=true,  src='platform_services_firebase_a'},
            remoteconfig = { name='fbrc',   enabled=true,  src='platform_services_firebase_rc'},
            deep_links   = { name='deep_links', enabled=true, src='platform_services_deep_links', params={url='https://link.kingdomrushalliance.com'}},
            cloudsave    = { name='gps',    enabled=true,  src='platform_services_gps' },
            achievements = { name='gps',    enabled=true, src='platform_services_gps' },
            news         = { name='news_ih',enabled=true, src='platform_services_news_ih_https', init_cond={min_launch_count=2}, params={
                              news_id='kra-googleplay',
                              news_store='appstore',
                           }},           
            -- >50
            goliath   = { name='goliath',   enabled=true, order=85, src='platform_services_mc_goliath', params={
                              platform='google',
                              game_id='1943',  -- mobile v1.1
                              staging = {
                                  api_key='6933973c-d471-418e-9c55-a15e2880b18a',
                                  shared_secret='a0577d5b-f1fa-415e-a20b-7e2a09674c32',
                                  api_url='https://6933973c-d471-418e-9c55-a15e2880b18a.goliath.atlas.bi.miniclippt.com',
                              },
                              production = {
                                  api_key='2f4aa74d-dde8-4c79-8abd-920c44e7e97a',
                                  shared_secret='ff983ecb-5eab-449b-8e29-0f17a14afb1c',
                                  api_url='https://2f4aa74d-dde8-4c79-8abd-920c44e7e97a.goliath.atlas.bi.miniclippt.com',
                              },
                        }},
            cmp          = { name='gump',         enabled=true, order=80, src='platform_services_gump',            params={sync_on_init=false}}, --, test_device="FD037DA83276CBBCC951615738D43755", test_geography="eea"}},
            overseervice = { name='overseervice', enabled=true, order=89, src='platform_services_ih_overseervice', params={
                                 staging = {
                                     api_key='91d9e47fd42342ed979a2695993698c9',    -- KR5 TESTING 
                                     shared_secret='6VD/ZEbGo0S03OJ/joGjXg==',      -- KR5 TESTING
                                     api_url='https://api.overseervice.ironhidegames.com'
                                 },
                                 production = {
                                     api_key='39400f1af6154543a77b22f1b79a4b5c',
                                     shared_secret='3d7Rx3ksGEC6wePwclJ9Ww==', 
                                     api_url='https://api.overseervice.ironhidegames.com'
                                 },
                           }},
            appsflyer = { name='appsflyer', enabled=true, order=90, src='platform_services_appsflyer',  init_cond={on_signals={'screen_slots-ready'}}, params={dev_key='c9K4JCYiD7Gfvri4wmjcx4', has_cmp=true}}, -- after goliath, syncs cmp
            push_noti = { name='fbm',       enabled=true, order=95, src='platform_services_firebase_m', init_cond={min_launch_count=5, on_signals={'screen_slots-ready'}} },
        }
    },

    -- KR5 iOS Appstore China
    ['com.ironhidegames.kingdomrush.alliance.cn'] = {
        overrides={'censored_cn'},
        main_params = {
            texture_size = 'ipadhd',
            image_db_releases_compressed_data = true,
        },
        asset_all_fallback  = { {path='kr5-phone', stop=true} },
        asset_game_fallback = { {path='kr5-phone' , texture_size='ipadhd'} },
        forced_locale = 'zh-Hans',  -- disables language selection button in settings
        default_locale = 'zh-Hans',
        --has_restore_savegame = true,
        --requires_privacy_policy = true,
        --hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        
        censored_cn = true,
        pops_hidden = true,
        
        platform_services = {

            http         = { name='http', enabled=true, src='platform_services_http', essential=true },
            -- <50
            iap          = { name='test_iap',  enabled=true, src='platform_services_iap_china',params={rc_suffix='storekit'}},
            -- 50
            
        },
        
    },
    -- KR5 Android Google Play
    ['com.ironhidegames.android.kingdomrush.alliance.cn'] = {
        overrides={'censored_cn'},
        main_params = {
            texture_size = 'ipadhd',
            image_db_releases_compressed_data = true,
        },
        fps_fallback = 30,
        fps_fallback_time_threshold_factor = 1.2,  -- 1.2 == 50 fps
        fps_fallback_count_threshold = 60,
        asset_all_fallback  = { {path='kr5-phone', stop=true} },
        asset_game_fallback = { {path='kr5-phone', texture_size='ipadhd'} },
        forced_locale = 'zh-Hans',  -- disables language selection button in settings
        default_locale = 'zh-Hans',
        censored_cn = true,
        pops_hidden = true,
        has_restore_savegame = true,
        requires_privacy_policy = false,
        hidden_for_underage = {'achievements', 'leaderboards', 'strategy'},
        platform_services = {
       --     license      = { name='glvl2',  enabled=true, src='platform_services_glvl2', essential=true },
            http         = { name='http',   enabled=true, src='platform_services_http', essential=true },
            -- <50
            iap          = { name='test_iap',  enabled=true, src='platform_services_iap_china', params={rc_suffix='gpiab'}},
          -- iap          = { name='gpiab',  enabled=true, order=40, src='platform_services_gpiab', params={pubkey='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlP+hlvqG0cTu030wwmhh68z4PuWGCqHu33xK3Wvg5b2W+HuNoi7iayP0OEXWmasMshtwT4gyiTcEEFO4b5hy7zkrxVhsLpo2sLD6by2vO+Q/P9Ir6Rb61HhWQqDEgG7DgJih7esqVnBAYD9WUW7jnwLWewQkGKsKRDk03245IYLXMmvjRMnsCuWvtyHgyPRMBUz3CYDsfOhp1iYHLBOh3cmC/LaXnZCkaM3IoTAjYyAHlCNjrdKFQs5qNX1YujdE4OnzJzlqnhxRUwKS89T4WXAsNYyGyRdskdrOEbsCvp/EvRg/B21NZlept4rNUdLHnaq5vslR5XspkfqFgnaeywIDAQAB'}},
            -- 50
         --   remoteconfig = { name='fbrc',   enabled=true,  src='platform_services_firebase_rc'},
       --     deep_links   = { name='deep_links', enabled=true, src='platform_services_deep_links', params={url='https://link.kingdomrushalliance.com'}},
       --     cloudsave    = { name='gps',    enabled=true,  src='platform_services_gps' },
       --     achievements = { name='gps',    enabled=true, src='platform_services_gps' },
       
        }
    },

    -- KR5 Desktop Standalone
    ['com.ironhidegames.kingdomrush.alliance.mac.standalone'] = {
        no_gems = true,
        libs={'kcolorspace'},
        main_params = {
            texture_size_list = { {'FullHD+', 'ipadhd_bc3',1e9}, {'FullHD','fullhd_bc3',1200}, {'XGA', 'ipad', 700}, },
            texture_size = 'ipadhd_bc3',
            image_db_uses_canvas = true,
            skip_settings_dialog = true,
            first_launch_fullscreen = true,
        },
        asset_game_fallback_for_texture_size = {
            fullhd_bc3 = {
                {path='kr5-desktop', texture_size='ipadhd_bc3'},
            },
        },        
        platform_services = {
            iap          = { name='iap_premium',  enabled=true, src='platform_services_iap_premium'},
        },
    },
    ['com.ironhidegames.kingdomrush.alliance.windows.standalone'] = {
        no_gems = true,
        main_params = {
            texture_size_list = { {'FullHD+', 'ipadhd_bc3',1e9}, {'FullHD','fullhd_bc3',1200}, {'XGA', 'ipad', 700}, },
            texture_size = 'ipadhd_bc3',
            image_db_uses_canvas = true,
            skip_settings_dialog = true,
            first_launch_fullscreen = true,
        },
        asset_game_fallback_for_texture_size = {
            fullhd_bc3 = {
                {path='kr5-desktop', texture_size='ipadhd_bc3'},
            },
        },        
        platform_services = {
            iap          = { name='iap_premium',  enabled=true, src='platform_services_iap_premium'},
        },
    },
    
    -- KR5 Desktop Steam
    ['com.ironhidegames.kingdomrush.alliance.mac.steam'] = {
        no_gems = true,
        libs={'kcolorspace','steam_api', 'krequest', 'ksystem', 'khttps'}, 
        main_params = {
            texture_size_list = { {'FullHD+', 'ipadhd_bc3',1e9}, {'FullHD','fullhd_bc3',1200}, {'XGA', 'ipad', 700}, },
            texture_size = 'ipadhd_bc3',
            image_db_uses_canvas = true,
            skip_settings_dialog = true,
            first_launch_fullscreen = true,
        },
        asset_game_fallback_for_texture_size = {
            fullhd_bc3 = {
                {path='kr5-desktop', texture_size='ipadhd_bc3'},
            },
        },        
        platform_services = {
            http         = { name='http',   enabled=true, src='platform_services_http', essential=true },
            iap          = { name='steam_iap',    order=40, enabled=true, src='platform_services_steam', params={app_id=2849080, dlcs={{id='dlc_1', app_id=3368630, includes={'hero_lava','tower_dwarf'}},{id='dlc_2', app_id=3732970, includes={'hero_wukong','tower_pandas'}}}}},
            achievements = { name='steam_ach',    order=41, enabled=true, src='platform_services_steam'},
            news         = { name='news_ih',      order=50, enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kra-steam-osx',
                                 news_store='steam',
                           }},
            goliath      = { name='goliath',  enabled=true, order=90, src='platform_services_mc_goliath', params={
                                 platform='steam',
                                 game_id='1944',  -- desktop v1.2
                                 staging = {
                                     -- steam staging
                                     api_key='93b950ba-2762-451f-b960-a9acbe311742',
                                     shared_secret='c00480a5-e021-4129-b993-f446b728f33b',
                                     api_url='https://93b950ba-2762-451f-b960-a9acbe311742.goliath.atlas.bi.miniclippt.com',
                                 },
                                 production = {
                                     -- steam production
                                     api_key='ea4ed4a2-c9ae-40f9-a3b3-20db93550dbc',
                                     shared_secret='9f9c7755-d50c-4384-9ee4-a95426bfff57',
                                     api_url='https://ea4ed4a2-c9ae-40f9-a3b3-20db93550dbc.goliath.atlas.bi.miniclippt.com',
                                 }
                           }},            
        },
        
    },
    ['com.ironhidegames.kingdomrush.alliance.windows.steam'] = {
        no_gems = true,
        -- khttps.dll and ksystem.dll depend on VCRUNTIME140.dll (VS 2019 runtime).
        libs={'libcurl-x64', 'khttps', 'ksystem', 'steam_api'}, 
        main_params = {
            texture_size_list = { {'FullHD+', 'ipadhd_bc3',1e9}, {'FullHD','fullhd_bc3',1200}, {'XGA', 'ipad', 700}, },
            texture_size = 'ipadhd_bc3',
            image_db_uses_canvas = true,
            skip_settings_dialog = true,
            first_launch_fullscreen = true,
        },
        asset_game_fallback_for_texture_size = {
            fullhd_bc3 = {
                {path='kr5-desktop', texture_size='ipadhd_bc3'},
            },
        },
        platform_services = {
            http         = { name='http',   enabled=true, src='platform_services_http', essential=true },
            iap          = { name='steam_iap',    order=40, enabled=true, src='platform_services_steam', params={app_id=2849080, dlcs={{id='dlc_1', app_id=3368630, includes={'hero_lava','tower_dwarf'}},{id='dlc_2', app_id=3732970, includes={'hero_wukong','tower_pandas'}}}}},
            achievements = { name='steam_ach',    order=41, enabled=true, src='platform_services_steam'},
            news         = { name='news_ih',      order=50, enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kra-steam-win',
                                 news_store='steam',
                           }},
            goliath      = { name='goliath',  enabled=true, order=90, src='platform_services_mc_goliath', params={
                                 platform='steam',
                                 game_id='1944',  -- desktop v1.2
                                 staging = {
                                     -- steam staging
                                     api_key='93b950ba-2762-451f-b960-a9acbe311742',
                                     shared_secret='c00480a5-e021-4129-b993-f446b728f33b',
                                     api_url='https://93b950ba-2762-451f-b960-a9acbe311742.goliath.atlas.bi.miniclippt.com',
                                 },
                                 production = {
                                     -- steam production
                                     api_key='ea4ed4a2-c9ae-40f9-a3b3-20db93550dbc',
                                     shared_secret='9f9c7755-d50c-4384-9ee4-a95426bfff57',
                                     api_url='https://ea4ed4a2-c9ae-40f9-a3b3-20db93550dbc.goliath.atlas.bi.miniclippt.com',
                                 }
                           }},            
        }
    },

    -- KR5 Desktop Mac Appstore
    ['com.ironhidegames.kingdomrush.alliance.mac.appstore'] = {
        no_gems = true,
        libs={'kcolorspace','krequest','kgamekit', 'ksystem', 'khttps', 'kstore'}, 
        main_params = {
            texture_size_list = { {'FullHD+', 'ipadhd_bc3',1e9}, {'FullHD','fullhd_bc3',1200}, {'XGA', 'ipad', 700}, },
            texture_size = 'ipadhd_bc3',
            image_db_uses_canvas = true,
            skip_settings_dialog = true,
            first_launch_fullscreen = true,
        },
        asset_game_fallback_for_texture_size = {
            fullhd_bc3 = {
                {path='kr5-desktop', texture_size='ipadhd_bc3'},
            },
        },
        platform_services = {
            http         = { name='http',   enabled=true, src='platform_services_http', essential=true },
            iap          = { name='iap_premium',  enabled=true, src='platform_services_iap_premium'},
            achievements = { name='gamecenter', enabled='true', src='platform_services_gamecenter'},
            rating       = { name='appstore_rating', enabled=true, src='platform_services_appstore_rating'},
            news         = { name='news_ih',  enabled=true, src='platform_services_news_ih_https', params={
                                 news_id='kra-appstore-osx',
                                 news_store='steam',
                           }},
            goliath      = { name='goliath',  enabled=true, order=90, src='platform_services_mc_goliath', params={
                                 platform='apple',
                                 game_id='1944',  -- desktop v1.2
                                 staging = {
                                     -- steam staging
                                     api_key='93b950ba-2762-451f-b960-a9acbe311742',
                                     shared_secret='c00480a5-e021-4129-b993-f446b728f33b',
                                     api_url='https://93b950ba-2762-451f-b960-a9acbe311742.goliath.atlas.bi.miniclippt.com',
                                 },
                                 production = {
                                     -- steam production
                                     api_key='ea4ed4a2-c9ae-40f9-a3b3-20db93550dbc',
                                     shared_secret='9f9c7755-d50c-4384-9ee4-a95426bfff57',
                                     api_url='https://ea4ed4a2-c9ae-40f9-a3b3-20db93550dbc.goliath.atlas.bi.miniclippt.com',
                                 }
                           }},            
        }
    },
    
    -- KR5 AppStore Greats (ios universal + macOS)
    ['com.ironhidegames.kingdomrush.alliance.universal.premium'] = {
        universal = true, 
        phone = {
            splash_video_service = 'video',
            splash_video_service_path = 'splash_videos',
            splash_video_service_items = { -- manually copy them to the target dir
                --'4.3x3_Landscape.mp4',  -- ipad only
                --'4x3_Landscape.mp4',    -- ipad only
                '16x9_Landscape_1920.mp4',
                '16x9_Landscape_2560.mp4',
                --'16x9_Landscape_4096.mp4', -- mac only
                --'16x9_Landscape_5120.mp4', -- mac only
                --'16x10_Landscape_1440.mp4', -- mac only
                --'16x10_Landscape_2880.mp4', -- mac only
                '19.5x9_Landscape.mp4',
            },
            splash_fade_out_duration = 0.25,
            main_params = {
                texture_size = 'ipadhd',
                image_db_releases_compressed_data = true,
            },
            asset_all_fallback  = { {path='kr5-phone', stop=true} },
            asset_game_fallback = { {path='kr5-phone' , texture_size='ipadhd'} },
            has_restore_savegame = true,
            requires_privacy_policy = false,
            simple_privacy_button = true,
            delay_services_init = true,
            hide_external_links = true,
            platform_services = {
                video        = { name='kvideo', enabled=true, src='platform_services_kvideo', essential=true},
                iap          = { name='iap_premium',  enabled=true, src='platform_services_iap_premium'},
                achievements = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter_universal_premium'}},
                cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud'},
                rating       = { name='appstore_rating', enabled=true, src='platform_services_appstore_rating'},
                deep_links   = { name='deep_links', enabled=true, src='platform_services_deep_links', params={url='https://link.kingdomrushalliance.com'}},
            }
        },
        tablet = {
            splash_video_service = 'video',
            splash_video_service_path = 'splash_videos',
            splash_video_service_items = {
                '4.3x3_Landscape.mp4',
                '4x3_Landscape.mp4',
                --'16x9_Landscape_1920.mp4', 
                --'16x9_Landscape_2560.mp4',
                --'16x9_Landscape_4096.mp4',  -- mac only
                --'16x9_Landscape_5120.mp4',  -- mac only
                --'16x10_Landscape_1440.mp4', -- mac only
                --'16x10_Landscape_2880.mp4', -- mac only
                --'19.5x9_Landscape.mp4',
            },
            splash_fade_out_duration = 0.25,
            main_params = {
                texture_size = 'ipadhd',
                image_db_releases_compressed_data = true,
            },
            asset_all_fallback  = { {path='kr5-phone', stop=true} },
            asset_game_fallback = { {path='kr5-phone' , texture_size='ipadhd'} },
            has_restore_savegame = true,            
            requires_privacy_policy = false,
            simple_privacy_button = true,
            delay_services_init = true,
            hide_external_links = true,
            platform_services = {
                video        = { name='kvideo', enabled=true, src='platform_services_kvideo', essential=true},
                iap          = { name='iap_premium',  enabled=true, src='platform_services_iap_premium'},
                achievements = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter_universal_premium'}},
                cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud'},
                rating       = { name='appstore_rating', enabled=true, src='platform_services_appstore_rating'},
                deep_links   = { name='deep_links', enabled=true, src='platform_services_deep_links', params={url='https://link.kingdomrushalliance.com'}},                
            }
        },
        desktop = {
            splash_fade_out_duration = 0.25,
            splash_video_service = 'video',
            splash_video_service_path = 'splash_videos',
            splash_video_service_items = {
                '4.3x3_Landscape.mp4',
                '4x3_Landscape.mp4',
                '16x9_Landscape_1920.mp4',
                '16x9_Landscape_2560.mp4',
                '16x9_Landscape_4096.mp4',
                '16x9_Landscape_5120.mp4',
                '16x10_Landscape_1440.mp4',
                '16x10_Landscape_2880.mp4',
                '19.5x9_Landscape.mp4',
            },            
            requires_privacy_policy = false,
            delay_services_init = true,
            hide_external_links = true,
            -- NOTE: export DYLD_LIBRARY_PATH=/Users/<USER>/doc/src/ih/kr-love/platform/bin/macOS; to run love from the command line and find these libs
            libs={'kcolorspace', 'krequest', 'ksystem', 'kgamekit', 'kcloud', 'kstore', 'kfairplay', 'kvideo'},
            resource_dirs={'splash_videos'},
            main_params = {
                texture_size_list = { {'FullHD+', 'ipadhd_bc3',1e9}, {'FullHD','fullhd_bc3',1200}, {'XGA', 'ipad', 700}, },
                texture_size = 'ipadhd_bc3',
                image_db_uses_canvas = true,
                skip_settings_dialog = true,
                first_launch_fullscreen = true,
            },
            asset_game_fallback_for_texture_size = {
                fullhd_bc3 = {
                    {path='kr5-desktop', texture_size='ipadhd_bc3'},
                },  
            },          
            platform_services = {
                license      = { name='fairplay', enabled=true, src='platform_services_fairplay', essential=true },
                video        = { name='kvideo', enabled=true, src='platform_services_kvideo', essential=true},
                iap          = { name='iap_premium', enabled=true, src='platform_services_iap_premium'},
                achievements = { name='gamecenter', enabled=true, src='platform_services_gamecenter', params={id='gamecenter_universal_premium'}},
                cloudsave    = { name='icloud', enabled=true, src='platform_services_icloud'},
                rating       = { name='appstore_rating', enabled=true, src='platform_services_appstore_rating'},
            }
        },
    },

}

------------------------------------------------------------
if ... == 'features' then
    
    -- use this in development
    require 'version'
    local bundle_id = version.bundle_id
    local t = _features[bundle_id]

    if t.universal then
        local _mt_universal = {
            __index = function(t,k)
                local tv = rawget(t, KR_TARGET)
                if tv then
                    return tv[k]
                else
                    return nil
                end
            end
        }
        --print('TTTTTTTTTTTTT hooking metatable to table: ', k)
        setmetatable(t, _mt_universal)
    end
    
    return t
else
    -- print only the selected bundle, for distribution
    --   eg: lua features.lua net.kalio.test.android.krf
    -- 
    -- for universal apps, return a metatable that will solve the
    -- proper feature set according to the KR_TARGET global var.
    
    package.path = package.path .. ';' .. '../src/lib/?.lua'
    local serpent = require 'serpent'
    args = {...}
    dist_bundle = args[1]
    local t = _features[dist_bundle]
    if not t then
        print(string.format('ERROR: no features.lua config for bundle:%s target:%s', dist_bundle, dist_target))
        os.exit(-1)
    end
    local lines = {}
    local function l(s) lines[#lines+1] = s end    
    l('-- features for bundle id: ' .. dist_bundle)
    l('local _ft = ' .. serpent.line(t,{comment=false,sortkeys=true}))
    if t.libs or t.overrides then
        l('-- COMMENTS BELOW USED BY distribute.sh | DO NOT REMOVE!')
    end
    if t.libs then
        l('-- lib_names: ' .. table.concat(t.libs, ' '))
    end
    if t.overrides then
        l('-- overrides: ' .. table.concat(t.overrides, ' '))
    end

    if t.universal then
        -- universal metatable resolution
        local mt_code = [[
local _mt_universal = {
    __index = function(t,k)
        local tv = rawget(t, KR_TARGET)
        if tv then
            return tv[k]
        else
            return nil
        end
    end
}
setmetatable(_ft, _mt_universal)
]]
        l('-- mt code')        
        l(mt_code)
    end
   
    l('return _ft')    
    print(table.concat(lines,'\n'))
    
    os.exit()
end

