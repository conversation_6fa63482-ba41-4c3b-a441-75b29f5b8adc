return {
  AreaAttack = {
    files = {
      "Sound_CommonAreaHit.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "BULLETS"
  },
  ArrowSound = {
    files = {
      "Sound_ArrowRelease2.ogg",
      "Sound_ArrowRelease3.ogg",
      "kra_sfx_combat_rangedAttack_arrows_var1_v1.ogg",
      "kra_sfx_combat_rangedAttack_arrows_var3_v1.ogg",
      "kra_sfx_combat_rangedAttack_arrows_var4_v1.ogg",
    },
    loop = false,
    mode = "random",
    gain = {0.65, 0.8}, -- ARROW_SOUND_MIN, ARROW_SOUND_MAX
    source_group = "BULLETS"
  },
  BoltSound = {
    files = {
      "Sound_MageShot.ogg"
    },
    gain = 0.68,
    loop = false,
    source_group = "BULLETS"
  },
  BombExplosionSound = {
    files = {
      "Sound_Bomb1.ogg"
    },
    gain = 0.8,
    loop = false,
    source_group = "EXPLOSIONS"
  },
  BombShootSound = {
    files = {
      "Sound_EngineerShot.ogg"
    },
    gain = 0.75,
    loop = false,
    source_group = "EXPLOSIONS"
  },
  CommonNoSwordAttack = {
    files = {
      "kra_sfx_combat_meleeAttack_noSword_var1.ogg",
      "kra_sfx_combat_meleeAttack_noSword_var6.ogg",
      "kra_sfx_combat_meleeAttack_noSword_var5.ogg",
      "kra_sfx_combat_meleeAttack_noSword_var4.ogg",
      "kra_sfx_combat_meleeAttack_noSword_var3.ogg",
      "kra_sfx_combat_meleeAttack_noSword_var2.ogg",
    },
    gain = 0.75,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  CommonLightning = {
    files = {
      "kr5_sfx_lightning_op1.ogg",
      "kr5_sfx_lightning_op2.ogg",
      "kr5_sfx_lightning_op3.ogg",
    },
    gain = 1,
    loop = false,
    mode = "sequence",
    source_group = "SFX"
  },
  DeathEplosion = {
    files = {
      "Sound_EnemyExplode1.ogg"
    },
    gain = 0.4,
    loop = false,
    source_group = "DEATH"
  },
  DeathHuman = {
    files = {
      "Sound_HumanDead1.ogg",
      "Sound_HumanDead2.ogg",
      "Sound_HumanDead3.ogg",
      "Sound_HumanDead4.ogg"
    },
    loop = false,
    mode = "random",
    source_group = "DEATH"
  },
  GUISplash = {
    files = {
      "KR5_SFX_IronhideLogo_24042024.ogg"
    },
    delay = 0,
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIAchievementWin = {
    files = {
      "Sound_AchievementWin.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIButtonCommon = {
    files = {
      "kr5_sfx_genericbuttonsoft_op1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIButtonUnavailable = {
    files = {
      "kra_sfx_ui_buttonUnavailable_v1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIBuyUpgrade = {
    files = {
      "Sound_GUIBuyUpgrade.ogg"
    },
    gain = 0.6,
    loop = false,
    source_group = "GUI"
  },
  GUICoins = {
    files = {
      "Sound_Coins.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUILooseLife = {
    files = {
      "Sound_LooseLife.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIMapNewFlah = {
    files = {
      "Sound_MapNewFlag.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUINextWaveIncoming = {
    files = {
      "Sound_WaveIncoming.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUINextWaveReady = {
    files = {
      "Sound_NextWaveReady.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUINotificationClose = {
    files = {
      "Sound_NotificationClose.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUINotificationOpen = {
    files = {
      "Sound_NotificationOpen.ogg"
    },
    gain = 0.8,
    loop = false,
    source_group = "GUI"
  },
  GUINotificationPaperOver = {
    files = {
      "Sound_NotificationPaperOver.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUINotificationSecondLevel = {
    files = {
      "Sound_NotificationSecondLevel.ogg"
    },
    gain = 0.8,
    loop = false,
    source_group = "GUI"
  },
  GUIPlaceRallyPoint = {
    files = {
      "Sound_RallyPointPlaced.ogg"
    },
    gain = 0.8,
    loop = false,
    source_group = "GUI"
  },
  GUIQuestCompleted = {
    files = {
      "kra_sfx_ui_stageVictory_v1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIQuestFailed = {
    files = {
      "kra_sfx_ui_stageDefeat_v1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIQuickMenuOpen = {
    files = {
      "Sound_GUIOpenTowerMenu.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIQuickMenuOver = {
    files = {
      "Sound_GUIMouseOverTowerIcon.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUISpellRefresh = {
    files = {
      "Sound_SpellRefresh.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUISpellSelect = {
    files = {
      "Sound_SpellSelect.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUITowerBuilding = {
    files = {
      "Sound_TowerBuilding.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUITowerOpenDoor = {
    files = {
      "kra_sfx_tower_paladinCovenant_deploy_v1.ogg"
    },
    gain = 0.2,
    delay = 0.2,
    loop = false,
    source_group = "GUI"
  },
  GUITowerSell = {
    files = {
      "Sound_TowerSell.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUITransitionClose = {
    files = {
      "kr5_sfx_UIgate-close.ogg"
    },
    gain = 1,
    delay = 0.1,
    loop = false,
    source_group = "GUI"
  },
  GUITransitionOpen = {
    files = {
      "kr5_sfx_UIgate-open_op1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIWinStars = {
    files = {
      "kr5_sfx_victorystars_3_v1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI",
    interruptible = true
  },
  GuimapNewRoad = {
    files = {
      "Sound_MapRoad.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIAchievementClaim = {
    files = {
      "kr5_sfx_achievementcollect.ogg"
    },
    gain = 0.8,
    loop = false,
    source_group = "GUI",
    ignore = 0.2
  },
  GUICardPreGlow = {
    files = {
      "kra_sfx_ui_rewards_unlock_glow_v1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUICardAppear = {
    files = {
      "kra_sfx_ui_rewards_unlock_appear-stomp_v1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUICardUnlock = {
    files = {
      "kra_sfx_ui_rewards_unlock_cardReveal_v1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUICardUnlockFade = {
    files = {
      "kra_sfx_ui_rewards_disappear_var1_v1.ogg",
      "kra_sfx_ui_rewards_disappear_var2_v1.ogg",
      "kra_sfx_ui_rewards_disappear_var3_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIEquip = {
    files = {
      "kr5_sfx_heroselect_op2.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIBalloonIn = {
    files = {
      "kr5_sfx_balloon-in.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIBalloonOut = {
    files = {
      "kr5_sfx_balloon-out.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIFlagFall = {
    files = {
      "kr5_sfx_flagfall.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIButtonSoft1 = {
    files = {
      "kr5_sfx_genericbuttonsoft_op1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIButtonSoft2 = {
    files = {
      "kr5_sfx_genericbuttonsoft_op2.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIHeroScroll = {
    files = {
      "kr5_sfx_heroscroll_op1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIHeroSelect = {
    files = {
      "kr5_sfx_heroselect_op1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIHeroSkillConfirm = {
    files = {
      "kr5_sfx_heroskillconfirm.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIHeroSkillSelect = {
    files = {
      "kr5_sfx_heroskillselect.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  HeroLevelUp = {
    files = {
      "Level_up.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroReinforcementTauntIntro = {
    files = {
      "Level_up.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroReinforcementTauntSelect = {
    files = {
      "Reinforcement-01a.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "TAUNTS"
  },
  HitSound = {
    files = {
      "Sound_ArrowHit2.ogg",
      "Sound_ArrowHit3.ogg"
    },
    gain = 0.15,
    loop = false,
    source_group = "BULLETS"
  },
  InAppBuyGems = {
    files = {
      "inapp_cash.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "SPECIALS"
  },
  InAppBuyInApp = {
    files = {
      "inapp_chin.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "SPECIALS"
  },
  InAppEarnGems = {
    files = {
      "inapp_gems.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "SPECIALS"
  },
  InAppExtraGold = {
    files = {
      "inapp_gnome.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "SPECIALS"
  },
  --KR5 GUI
  GUIRewardUnlockCardAppear = {
    files = {
      "kra_sfx_ui_rewards_unlock_cardAppear_v1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIRewardUnlockPreGlow = {
    files = {
      "kra_sfx_ui_rewards_unlock_pre-glow_v1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIRewardUnlockCardUnlock = {
    files = {
      "kra_sfx_ui_rewards_unlock_op1_v1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIMapDotsAppear = {
    files = {
      "kra_sfx_ui_mapDotsAppear_op2_v2.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIMapStageFlagAppear = {
    files = {
      "kra_sfx_ui_stageFlagAppear_v1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIMapStageFlagHeroicWings = {
    files = {
      "kra_sfx_uiMap_heroicChallengeFlag_v1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIMapCultistBridgeAppear = {
    files = {
      "kra_sfx_uiMap_cultistBridge_op2_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "GUI",
    delay = 0.7
  },
  GUIMapCloudRemoval = {
    files = {
      "kra_sfx_uiMap_cloudRemoval_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "GUI",
    delay = 1.5
  },
  GUIButtonHover = {
    files = {
        "kra_sfx_uiMenu_hover_op1_v1.ogg",
        --"kr5_button_hover_op1.ogg"
        --"kr5_button_hover_op2.ogg"
        --"kr5_button_hover_op3.ogg"
    },
    gain = 0.2,
    ignore = 0.1,
    loop = false,
    source_group = "GUI"
  },
  GUIButtonOut = {
    files = {
      "kra_sfx_ui_buttonOut_op2_v2.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIResetUpgrade = {
    files = {
      "kra_sfx_ui_resetSkills_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIHeroTowerSelect = {
    files = {
      "kra_sfx_ui_heroTowerSelect_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUITowerWheelTapOn = {
    files = {
      "kra_sfx_ui_towerWheelDrag_tapOn_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUITowerWheelTapOff = {
    files = {
      "kra_sfx_ui_towerWheelDrag_tapOff_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIStageVictory = {
    files = {
      "kra_sfx_ui_stageVictory_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIStageDefeat = {
    files = {
      "kra_sfx_ui_stageDefeat_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "GUI"
  },
  GUIGemCounterSingle = {
    files = {
      "kra_sfx_ui_gemCounter_SINGLE_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "GUI",
    ignore = 0.1
  },

  --KR5 HEROES
  HeroVesperTaunt = {
    files = {
      "kr_voice_vesper_taunt2.ogg",
      "kr_voice_vesper_taunt3.ogg",
      "kr_voice_vesper_taunt4.ogg",
      "kr_voice_vesper_taunt.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroVesperTauntIntro = {
    files = {
      "kr_voice_vesper_taunt.ogg"
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroVesperTauntSelect = {
    files = {
      "kr_voice_vesper_taunt.ogg"
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroVesperDeath = {
    files = {
      "kr_voice_vesper_death_var1d.ogg"
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroVesperArrowToTheKneeCast = {
    files = {
      "kra_heroes_vesper_arrowToTheKnee_cast_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroVesperArrowToTheKneeImpact = {
    files = {
      "kra_heroes_vesper_arrowToTheKnee_impact_op1_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroVesperRicochetCast = {
    files = {
      "kra_heroes_vesper_ricochet_cast_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroVesperRicochetImpact = {
    files = {
      "kra_heroes_vesper_ricochet_impact_var1_v1.ogg",
      "kra_heroes_vesper_ricochet_impact_var2_v1.ogg",
      "kra_heroes_vesper_ricochet_impact_var3_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroVesperMartialFlourishCast = {
    files = {
      "kra_heroes_vesper_oneShot_martialFlourish_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroVesperDisengageCast = {
    files = {
      "kra_sfx_heroes_vesper_disengage_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroVesperUltimateLvl1 = {
    files = {
      "kra_heroes_vesper_arrowStorm_low_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroVesperUltimateLvl2 = {
    files = {
      "kra_heroes_vesper_arrowStorm_mid_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroVesperUltimateLvl3 = {
    files = {
      "kra_heroes_vesper_arrowStorm_high_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroNyruTaunt = {
    files = {
      "kr_voice_nyru_taunt2_var1c.ogg",
      "kr_voice_nyru_taunt3_var1a.ogg",
      "kr_voice_nyru_taunt4_var1a.ogg",
      "kr_voice_nyru_taunt_var1c.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroNyruTauntIntro = {
    files = {
      "kr_voice_nyru_taunt2_var1c.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroNyruTauntSelect = {
    files = {
      "kr_voice_nyru_taunt2_var1c.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroNyruDeath = {
    files = {
      "kr_voice_nyru_death_var1a.ogg"
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroNyruSentinelWispsCast = {
    files = {
      "kra_heroes_nyru_sentinelWisps_cast_v1.ogg"
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  HeroNyruSentinelWispsSpawn = {
    files = {
      "kra_heroes_nyru_sentinelWisps_spawn_v1.ogg"
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  HeroNyruSentinelWispsShoot = {
    files = {
      "kra_heroes_nyru_sentinelWisps_attack_var1_v1.ogg",
      "kra_heroes_nyru_sentinelWisps_attack_var3_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroNyruVerdantBlastCast = {
    files = {
      "kra_heroes_nyru_verdantBlast_cast_oneShot_v1.ogg"
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  HeroNyruVerdantBlastHit = {
    files = {
      "kra_heroes_nyru_verdantBlast_impact_v1.ogg"
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  HeroNyruLeafWhirlwindCast = {
    files = {
      "kra_heroes_nyru_leafWhirlwind_op1_v2.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroNyruFairyDustCast = {
    files = {
      "kra_heroes_nyru_fairyDust_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroNyruRootDefenderStartLvl1 = {
    files = {
      "kra_heroes_nyru_rootDefender_low_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroNyruRootDefenderStartLvl2 = {
    files = {
      "kra_heroes_nyru_rootDefender_mid_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroNyruRootDefenderStartLvl3 = {
    files = {
      "kra_heroes_nyru_rootDefender_high_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroNyruRootDefenderEnd = {
    files = {
      "kra_heroes_nyru_rootDefender_retract_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroNyruTreewalk = {
    files = {
      "kra_heroes_nyru_treewalk_LOOP_var2_v2.ogg"
    },
    gain = 1,
    loop = true,
    source_group = "SFX"
  },
  HeroNyruBasicAttackRanged = {
    files = {
      "kra_heroes_nyru_basicAttack_ranged_var1_v1.ogg",
      "kra_heroes_nyru_basicAttack_ranged_var2_v1.ogg",
      "kra_heroes_nyru_basicAttack_ranged_var3_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroNyruBasicAttackMelee = {
    files = {
      "kra_heroes_nyru_basicAttack_melee_var1_op1_v1.ogg",
      "kra_heroes_nyru_basicAttack_melee_var2_op1_v1.ogg",
      "kra_heroes_nyru_basicAttack_melee_var3_op1_v1.ogg",
    },
    gain = 0.1,
    loop = false,
    delay = 0.25,
    mode = "random",
    source_group = "SFX"
  },
  HeroRaelynTaunt = {
    files = {
      "kr_voice_raelyn_taunt_var1c.ogg",
      "kr_voice_raelyn_taunt2_var2a.ogg",
      "kr_voice_raelyn_taunt3_var2b.ogg",
      "kr_voice_raelyn_taunt4_var1a.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroRaelynTauntIntro = {
    files = {
      "kr_voice_raelyn_taunt3_var2b.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroRaelynTauntSelect = {
    files = {
      "kr_voice_raelyn_taunt3_var2b.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroRaelynDeath = {
    files = {
      "kr_voice_raelyn_death_var1e.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroRaelynUnbreakableCast = {
    files = {
      "kra_sfx_heroes_raelyn_unbreakable_op1_v1.ogg"
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  HeroRaelynInspireFearCast = {
    files = {
      "kra_sfx_heroes_raelyn_inspireFear_op2_v1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  HeroRaelynBrutalSlashCast = {
    files = {
      "kra_sfx_heroes_raelyn_brutalSlash_op2_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroRaelynOnslaughtCast = {
    files = {
      "kra_sfx_heroes_raelyn_onslaught_var3_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    delay = 0.2,
    source_group = "SFX"
  },
  HeroRaelynBasicAttack = {
    files = {
      "kra_sfx_heroes_raelyn_basicAttack_var1_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    delay = 0.2,
    source_group = "SFX"
  },
  HeroRaelynUltimateCast = {
    files = {
      "kra_sfx_heroes_raelyn_commandOrders_v2_op1.ogg"
    },
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  HeroRaelynUltimateTaunt = {
    files = {
      "kr_voice_darkknight_taunt01_d.ogg",
      "kr_voice_darkknight_taunt02_c.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroRaelynUltimateDeath = {
    files = {
      "kra_sfx_heroes_raelyn_commandOrders_death_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroSpaceElfTaunt = {
    files = {
      "kr_voice_theriennethevoidadept_select_c.ogg",
      "kr_voice_theriennethevoidadept_taunt01_d.ogg",
      "kr_voice_theriennethevoidadept_taunt02_c.ogg",
      "kr_voice_theriennethevoidadept_taunt03_b.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroSpaceElfTauntIntro = {
    files = {
      "kr_voice_theriennethevoidadept_select_c.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroSpaceElfTauntSelect = {
    files = {
      "kr_voice_theriennethevoidadept_select_c.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroSpaceElfDeath = {
    files = {
      "kr_voice_theriennethevoidadept_death_h.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroSpaceElfAstralReflection = {
    files = {
      "kra_sfx_heroes_therien_astralReflection_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  HeroSpaceElfBlackAegis = {
    files = {
      "kra_sfx_heroes_therien_blackAegis_cast_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  HeroSpaceElfBlackAegisExplosion = {
    files = {
      "kra_sfx_heroes_therien_blackAegis_explosion.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  HeroSpaceElfVoidRift = {
    files = {
      "kra_sfx_heroes_therien_voidRift_cast_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroSpaceElfSpatialDistortion = {
    files = {
      "kra_sfx_heroes_therien_spatialDistortion_cast_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  HeroSpaceElfTeleportIn = {
    files = {
      "kra_sfx_heroes_therien_teleportIn_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  HeroSpaceElfTeleportOut = {
    files = {
      "kra_sfx_heroes_therien_teleportOut_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  HeroSpaceElfCosmicPrisonIn = {
    files = {
      "kra_sfx_heroes_therien_cosmicPrison_phaseIn_v1.ogg",
    },
    gain = 0.2,
    loop = false,
    source_group = "SFX"
  },
  HeroSpaceElfCosmicPrisonOut = {
    files = {
      "kra_sfx_heroes_therien_cosmicPrison_phaseOut_v1.ogg",
    },
    gain = 0.3,
    loop = false,
    source_group = "SFX"
  },
  HeroBuilderTaunt = {
    files = {
      "kr_voice_torrestheforeman_select_a.ogg",
      "kr_voice_torrestheforeman_taunt01_c.ogg",
      "kr_voice_torrestheforeman_taunt02_b.ogg",
      "kr_voice_torrestheforeman_taunt03_e.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroBuilderTauntIntro = {
    files = {
      "kr_voice_torrestheforeman_select_a.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroBuilderTauntSelect = {
    files = {
      "kr_voice_torrestheforeman_select_a.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroBuilderDeath = {
    files = {
      "kr_voice_torrestheforeman_death_a.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroBuilderBasicAttack = {
    files = {
      "kra_heroes_nyru_basicAttack_melee_var1_op1_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroBuilderWreckingBall = {
    files = {
      "kra_sfx_heroes_torres_wreckingBall_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  HeroBuilderMenAtWork = {
    files = {
      "kra_sfx_heroes_torres_menAtWork_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroBuilderDemolitionMan = {
    files = {
      "kra_sfx_heroes_torres_demolitionMan_spin_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroBuilderLunchBreak = {
    files = {
      "kra_sfx_heroes_torres_lunchBreak_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  HeroBuilderDefensiveTurretCast = {
    files = {
      "kra_sfx_heroes_torres_defensiveTurret_cast_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroBuilderDefensiveTurretDestroy = {
    files = {
      "kra_sfx_heroes_torres_defensiveTurret_destroy_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroMechaTaunt = {
    files = {
      "kr_voice_onagro_taunt_a.ogg",
      "kr_voice_onagro_taunt02_a.ogg",
      "kr_voice_onagro_taunt03_e.ogg",
      "kr_voice_onagro_taunt04_c.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroMechaTauntIntro = {
    files = {
      "kr_voice_onagro_taunt_a.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroMechaTauntSelect = {
    files = {
      "kr_voice_onagro_taunt_a.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroMechaDeath = {
    files = {
      "kr_voice_onagro_death_c.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroMechaBasicAttack = {
    files = {
      "kra_sfx_heroes_onagro_basicAttack_cast_var1_v1.ogg",
      "kra_sfx_heroes_onagro_basicAttack_cast_var2_v1.ogg",
      "kra_sfx_heroes_onagro_basicAttack_cast_var3_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    delay = 0.3,
    mode = "random",
    source_group = "SFX"
  },
  HeroMechaBasicAttackHit = {
    files = {
      "kra_sfx_heroes_onagro_basicAttack_impact_var1_v1.ogg",
      "kra_sfx_heroes_onagro_basicAttack_impact_var2_v1.ogg",
      "kra_sfx_heroes_onagro_basicAttack_impact_var3_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroMechaGoblidroneCast = {
    files = {
      "kra_sfx_heroes_goblidrone_cast_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  HeroMechaGoblidroneAttack = {
    files = {
      "kra_sfx_heroes_goblidrone_attack_var1_v1.ogg",
      "kra_sfx_heroes_goblidrone_attack_var2_v1.ogg",
      "kra_sfx_heroes_goblidrone_attack_var3_v1.ogg",
      "kra_sfx_heroes_goblidrone_attack_var4_v1.ogg",
    },
    gain = 1,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroMechaMineDropCast = {
    files = {
      "kra_sfx_heroes_mineDrop_cast_var1_v1.ogg",
      "kra_sfx_heroes_mineDrop_cast_var2_v1.ogg",
      "kra_sfx_heroes_mineDrop_cast_var3_v1.ogg",
    },
    gain = 0.7,
    delay = 1.7,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroMechaMineDropExplosion = {
    files = {
      "kra_sfx_heroes_mineDrop_explosion_v2.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  HeroMechaDeathFromAboveCast = {
    files = {
      "kra_sfx_heroes_onagro_deathFromAbove_cast_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  HeroMechaDeathFromAboveAttack = {
    files = {
        "kra_sfx_heroes_onagro_deathFromAbove_attack_shot_var1_v1.ogg",
        "kra_sfx_heroes_onagro_deathFromAbove_attack_shot_var2_v1.ogg",
        "kra_sfx_heroes_onagro_deathFromAbove_attack_shot_var3_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroMechaDeathFromAboveExplosion = {
    files = {
      "kra_sfx_heroes_onagro_deathFromAbove_attack_explosion_var1_v1.ogg",
      "kra_sfx_heroes_onagro_deathFromAbove_attack_explosion_var2_v1.ogg",
      "kra_sfx_heroes_onagro_deathFromAbove_attack_explosion_var3_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroMechaPowerSlamCast = {
    files = {
      "kra_sfx_heroes_powerSlam_cast_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  HeroMechaTarBombCast = {
    files = {
      "kra_sfx_heroes_tarBomb_cast_var1_v1.ogg",
      "kra_sfx_heroes_tarBomb_cast_var2_v1.ogg",
      "kra_sfx_heroes_tarBomb_cast_var3_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroMechaTarBombExplosion = {
    files = {
      "kra_sfx_heroes_tarBomb_explosion_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  HeroLumenirTaunt = {
    files = {
      "kr_voice_lumenir_taunt03_a.ogg",
      "kr_voice_lumenir_taunt_b.ogg",
      "kr_voice_lumenir_taunt02_c.ogg",
      "kr_voice_lumenir_taunt04_a.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroLumenirTauntIntro = {
    files = {
      "kr_voice_lumenir_taunt03_a.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroLumenirTauntSelect = {
    files = {
      "kr_voice_lumenir_taunt03_a.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroLumenirDeath = {
    files = {
      "kr_voice_lumenir_death_c.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroLumenirBasicAttack = {
    files = {
      "kra_sfx_heroes_lumenir_basicAttack_cast_var1_op2_v1.ogg",
      "kra_sfx_heroes_lumenir_basicAttack_cast_var2_op2_v1.ogg",
      "kra_sfx_heroes_lumenir_basicAttack_cast_var3_op2_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroLumenirBlessingOfRetributionCast = {
    files = {
      "kra_sfx_heroes_lumenir_blessingOfRetribution_cast_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  HeroLumenirCallOfTriumphCast= {
    files = {
      "kra_sfx_heroes_lumenir_callOfTriumph_cast_var1_v2.ogg",
      "kra_sfx_heroes_lumenir_callOfTriumph_cast_var2_v2.ogg",
      "kra_sfx_heroes_lumenir_callOfTriumph_cast_var3_v2.ogg",
    },
    gain = 1,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroLumenirCallOfTriumphOut= {
    files = {
      "kra_sfx_heroes_lumenir_callOfTriumph_out_var1_v1.ogg",
      "kra_sfx_heroes_lumenir_callOfTriumph_out_var2_v1.ogg",
      "kra_sfx_heroes_lumenir_callOfTriumph_out_var3_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroLumenirCelestialJudgementCast = {
    files = {
      "kra_sfx_heroes_lumenir_celestialJudgement_cast_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  HeroLumenirCelestialJudgementImpact = {
    files = {
      "kra_sfx_heroes_lumenir_celestialJudgement_impact_v2_op2.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  HeroLumenirRadiantWaveCast = {
    files = {
      "kra_sfx_heroes_lumenir_radiantWave_cast_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroLumenirLightCompanionCast = {
    files = {
      "kra_sfx_heroes_lumenir_lightCompanion_cast_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroLumenirLightCompanionBasicAttack= {
    files = {
      "kra_sfx_heroes_lumenir_lightCompanion_basicAttack_var1_v1.ogg",
      "kra_sfx_heroes_lumenir_lightCompanion_basicAttack_var2_v1.ogg",
      "kra_sfx_heroes_lumenir_lightCompanion_basicAttack_var3_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroVenomTaunt = {
    files = {
      "kr_voice_grimson_taunt_b.ogg",
      "kr_voice_grimson_taunt02_b.ogg",
      "kr_voice_grimson_taunt03_b.ogg",
      "kr_voice_grimson_taunt04_c.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroVenomTauntIntro = {
    files = {
      "kr_voice_grimson_taunt_b.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroVenomTauntSelect = {
    files = {
      "kr_voice_grimson_taunt_b.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroVenomDeath = {
    files = {
      "kr_voice_grimson_death_b.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroVenomBasicAttack = {
    files = {
      "kra_sfx_heroes_grimson_basicAttack_var1_v1.ogg",
      "kra_sfx_heroes_grimson_basicAttack_var2_v1.ogg",
      "kra_sfx_heroes_grimson_basicAttack_var3_v1.ogg",
    },
    gain = 0.3,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroVenomHeartseekerCast = {
    files = {
      "kra_sfx_heroes_grimson_heartseeker_cast_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  HeroVenomInnerBeastCast = {
    files = {
      "kra_sfx_heroes_grimson_innerBeast_cast_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  HeroVenomInnerBeastOut = {
    files = {
      "kra_sfx_heroes_grimson_innerBeast_out_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroVenomDeadlySpikesCast = {
    files = {
      "kra_sfx_heroes_grimson_deadlySpikes_cast_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroVenomDeadlySpikesOut = {
    files = {
      "kra_sfx_heroes_grimson_deadlySpikes_out_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroVenomRenewFleshCast = {
    files = {
      "kra_sfx_heroes_grimson_renewFlesh_cast_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  HeroVenomRenewCreepingDeathCast = {
    files = {
      "kra_sfx_heroes_grimson_creepingDeath_cast_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  HeroVenomRenewCreepingDeathSpikes = {
    files = {
      "kra_sfx_heroes_grimson_creepingDeath_spikes_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  HeroRobotTaunt = {
    files = {
      "kr_voice_warhead_taunt01_b.ogg",
      "kr_voice_warhead_taunt02_a.ogg",
      "kr_voice_warhead_taunt03_a.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroRobotTauntIntro = {
    files = {
      "kr_voice_warhead_select_b.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroRobotTauntSelect = {
    files = {
      "kr_voice_warhead_select_b.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroRobotDeath = {
    files = {
      "kr_voice_warhead_death_c.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroRobotDeepImpactCast = {
    files = {
      "kra_sfx_hero_warhead_deepImpact_cast_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroRobotDeepImpactImpact = {
    files = {
      "kra_sfx_hero_warhead_deepImpact_impact_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  HeroRobotSmokescreenCast = {
    files = {
      "kra_sfx_heroes_warhead_smokescreen_cast_wSomeScreen_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  HeroRobotImmolationCast = {
    files = {
      "kra_sfx_heroes_warhead_immolation_cast_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  HeroRobotUppercutCast = {
    files = {
      "kra_sfx_heroes_warhead_uppercut_cast_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroRobotJetpackCast = {
    files = {
      "kra_sfx_heroes_warhead_jetpack_oneShot_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  HeroRobotMotorheadCast = {
    files = {
      "kra_sfx_heroes_warhead_motorhead_cast-march_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  HeroHunterTaunt = {
    files = {
      "kr_voice_anya_select_c.ogg",
      "kr_voice_anya_taunt_a.ogg",
      "kr_voice_anya_taunt02_a.ogg",
      "kr_voice_anya_taunt03_b.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroHunterTauntIntro = {
    files = {
      "kr_voice_anya_select_c.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroHunterTauntSelect = {
    files = {
      "kr_voice_anya_select_c.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroHunterDeath = {
    files = {
      "kr_voice_anya_death_d.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroHunterBasicAttack = {
    files = {
      "kra_sfx_heroes_anya_basicAttack_var1_v1.ogg",
      "kra_sfx_heroes_anya_basicAttack_var2_v1.ogg",
      "kra_sfx_heroes_anya_basicAttack_var3_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroHunterHealStrikeCast = {
    files = {
      "kra_sfx_heroes_anya_vampiricStrike_cast_var1_v1.ogg",
      "kra_sfx_heroes_anya_vampiricStrike_cast_var2_v1.ogg",
      "kra_sfx_heroes_anya_vampiricStrike_cast_var3_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroHunterRicochetCast = {
    files = {
      "kra_sfx_heroes_anya_mistyStep_cast_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  HeroHunterRicochetBounce = {
    files = {
      "kra_sfx_heroes_anya_mistyStep_bounce_op1_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroHunterShootAroundCast = {
    files = {
      "kra_sfx_heroes_anya_argentStorm_shot_op1_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX",
    interruptible = true,
  },
  HeroHunterShootAroundInterrupt = {
    files = {
      "kra_sfx_heroes_anya_argentStorm_fadeOut_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX",
  },
  HeroHunterBeastsCast = {
    files = {
      "kra_sfx_heroes_anya_duskBeasts_cast_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  HeroHunterUltimateCast = {
    files = {
      "kra_sfx_heroes_anya_huntersAid_cast_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  HeroHunterUltimateAttack = {
    files = {
      "kra_sfx_heroes_anya_huntersAid_attack_op2_var1_v1.ogg",
      "kra_sfx_heroes_anya_huntersAid_attack_op2_var2_v1.ogg",
      "kra_sfx_heroes_anya_huntersAid_attack_op2_var3_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX",
    delay = 0.2
  },
  HeroDragonGemTaunt = {
    files = {
      "kr_voice_kosmyr_select_c.ogg",
      "kr_voice_kosmyr_taunt01_a.ogg",
      "kr_voice_kosmyr_taunt02_a.ogg",
      "kr_voice_kosmyr_taunt03_a.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroDragonGemTauntIntro = {
    files = {
      "kr_voice_kosmyr_select_c.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroDragonGemTauntSelect = {
    files = {
      "kr_voice_kosmyr_select_c.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroDragonGemDeath = {
    files = {
      "kr_voice_kosmyr_death_b.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroDragonGemBasicAttackCast = {
    files = {
      "kra_sfx_heroes_kosmyr_basicAttack_cast_var1_v1.ogg",
      "kra_sfx_heroes_kosmyr_basicAttack_cast_var2_v1.ogg",
      "kra_sfx_heroes_kosmyr_basicAttack_cast_var3_v1.ogg",
    },
    gain = 1,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroDragonGemBasicAttackImpact = {
    files = {
      "kra_sfx_heroes_kosmyr_basicAttack_impact_var1_v1.ogg",
      "kra_sfx_heroes_kosmyr_basicAttack_impact_var2_v1.ogg",
      "kra_sfx_heroes_kosmyr_basicAttack_impact_var3_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroDragonGemPrismaticShardCast = {
    files = {
      "kra_sfx_heroes_kosmyr_prismaticShard_cast_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  HeroDragonGemPrismaticShardRipple = {
    files = {
      "kra_sfx_heroes_kosmyr_prismaticShard_ripple_var1_v1.ogg",
      "kra_sfx_heroes_kosmyr_prismaticShard_ripple_var2_v1.ogg",
      "kra_sfx_heroes_kosmyr_prismaticShard_ripple_var3_v1.ogg",
      "kra_sfx_heroes_kosmyr_prismaticShard_ripple_var4_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroDragonGemParalyzingBreathCast = {
    files = {
      "kra_sfx_heroes_kosmyr_paralyzingBreath_Cast_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  HeroDragonGemRedDeathCast = {
    files = {
      "kra_sfx_heroes_kosmyr_redDeath_cast_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  HeroDragonGemRedDeathExplosion = {
    files = {
      "kra_sfx_heroes_kosmyr_redDeath_explosion_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  HeroDragonGemPowerConduitCast = {
    files = {
      "kra_sfx_heroes_kosmyr_powerConduit_cast_shot_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  HeroDragonGemPowerConduitCrystal = {
    files = {
      "kra_sfx_heroes_kosmyr_powerConduit_crystal_op1_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  HeroDragonGemUltimateCast = {
    files = {
      "kra_sfx_heroes_kosmyr_crystalAvalanch_cast_var1_v1.ogg",
      "kra_sfx_heroes_kosmyr_crystalAvalanch_cast_var2_v1.ogg",
      "kra_sfx_heroes_kosmyr_crystalAvalanch_cast_var3_v1.ogg",
      "kra_sfx_heroes_kosmyr_crystalAvalanch_cast_var4_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroBirdTaunt = {
    files = {
      "kr_voice_broden_select_b.ogg",
      "kr_voice_broden_taunt01_c.ogg",
      "kr_voice_broden_taunt02_a.ogg",
      "kr_voice_broden_taunt03_d.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroBirdTauntIntro = {
    files = {
      "kr_voice_broden_select_b.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroBirdTauntSelect = {
    files = {
      "kr_voice_broden_select_b.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroBirdDeath = {
    files = {
      "kr_voice_broden_death_a.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroBirdBasicAttackCast = {
    files = {
      "kra_sfx_heroes_broden_basicAttack_cast_var1_v1.ogg",
      "kra_sfx_heroes_broden_basicAttack_cast_var2_v1.ogg",
      "kra_sfx_heroes_broden_basicAttack_cast_var3_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroBirdBasicAttackImpact = {
    files = {
      "kra_sfx_heroes_broden_basicAttack_impact_var1_v1.ogg",
      "kra_sfx_heroes_broden_basicAttack_impact_var2_v1.ogg",
      "kra_sfx_heroes_broden_basicAttack_impact_var3_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroBirdBasicCarpetBombingCast = {
    files = {
      "kra_sfx_heroes_broden_carpetBombing_cast_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  HeroBirdBasicCarpetBombingImpact = {
    files = {
      "kra_sfx_heroes_broden_carpetBombing_impact_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    source_group = "SFX",
    ignore = 0.5
  },
  HeroBirdTerrorShriekCast = {
    files = {
      "kra_sfx_heroes_broden_terrorShriek_cast_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX",
    delay = 0.4
  },
  HeroBirdBulletRainCast = {
    files = {
      "kra_sfx_heroes_broden_bulletRain_cast_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    source_group = "SFX",
    interruptible = true,
  },
  HeroBirdBulletRainEnd = {
    files = {
      "kra_sfx_heroes_broden_bulletRain_loopEnd.ogg",
    },
    gain = 0.4,
    loop = false,
    source_group = "SFX",
    interruptible = true,
  },
  HeroBirdHuntingDiveCast = {
    files = {
      "kra_sfx_heroes_broden_huntingDive_cast_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    source_group = "SFX",
    delay = 0.4
  },
  HeroBirdBirdsOfPreyCast = {
    files = {
      "kra_sfx_heroes_broden_birdsOfPrey_cast_op2_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  HeroBirdBirdsOfPreyGryphonAttack = {
    files = {
      "kra_sfx_heroes_broden_birdsOfPrey_gryphonAttack_var1_v1.ogg",
      "kra_sfx_heroes_broden_birdsOfPrey_gryphonAttack_var2_v1.ogg",
      "kra_sfx_heroes_broden_birdsOfPrey_gryphonAttack_var3_v1.ogg",
      "kra_sfx_heroes_broden_birdsOfPrey_gryphonAttack_var4_v1.ogg",
    },
    gain = 0.3,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroWitchTaunt = {
    files = {
      "kr_voice_stregi_select_b.ogg",
      "kr_voice_stregi_taunt-01_a.ogg",
      "kr_voice_stregi_taunt-02_d.ogg",
      "kr_voice_stregi_taunt-03_c.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroWitchTauntIntro = {
    files = {
      "kr_voice_stregi_select_b.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroWitchTauntSelect = {
    files = {
      "kr_voice_stregi_select_b.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroWitchDeath = {
    files = {
      "kr_voice_stregi_death_b.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroWitchBasicAttackCast = {
    files = {
      "kra_sfx_heroes_stregi_basicAttack_cast_var1_v1.ogg",
      "kra_sfx_heroes_stregi_basicAttack_cast_var2_v1.ogg",
      "kra_sfx_heroes_stregi_basicAttack_cast_var3_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "sequence",
    delay = 0.4,
    source_group = "SFX"
  },
  HeroWitchDazzlingDecoyCast = {
    files = {
      "kra_sfx_heroes_stregi_dazzlingDecoy_cast_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroWitchDazzlingDecoyExplosion = {
    files = {
      "kra_sfx_heroes_stregi_dazzlingDecoy_explosion_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroWitchNightFuriesCast = {
    files = {
      "kra_sfx_heroes_stregi_nightFuries_cast_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroWitchVeggiefyIn = {
    files = {
      "kra_sfx_heroes_stregi_veggiefy_in_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroWitchVeggiefyOut = {
    files = {
      "kra_sfx_heroes_stregi_veggiefy_out_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroWitchSquishNSquashCast = {
    files = {
      "kra_sfx_heroes_squishNSquash_cast_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroWitchSquishNSquashImpact = {
    files = {
      "kra_sfx_heroes_squishNSquash_impact_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroDragonBoneUltimateIn = {
    files = {
      "kra_sfx_heroes_stregi_drowsyReturn_in_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroDragonBoneUltimateOut = {
    files = {
      "kra_sfx_heroes_stregi_drowsyReturn_out_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },

  HeroDragonBoneTaunt = {
    files = {
      "kr_voice_bonehart_select_c.ogg",
      "kr_voice_bonehart_taunt-01_b.ogg",
      "kr_voice_bonehart_taunt-02_a.ogg",
      "kr_voice_bonehart_taunt-03_c.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroDragonBoneTauntIntro = {
    files = {
      "kr_voice_bonehart_select_c.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroDragonBoneTauntSelect = {
    files = {
      "kr_voice_bonehart_select_c.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroDragonBoneDeath = {
    files = {
      "kr_voice_bonehart_death_a.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroDragonBoneBasicAttackCast = {
    files = {
      "kra_sfx_heroes_bonehart_basicAttack_cast_var1.ogg",
      "kra_sfx_heroes_bonehart_basicAttack_cast_var2.ogg",
      "kra_sfx_heroes_bonehart_basicAttack_cast_var3.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "sequence",
    source_group = "SFX"
  },
  HeroDragonBoneBasicAttackImpact = {
    files = {
      "kra_sfx_heroes_bonehart_basicAttack_impact_var1.ogg",
      "kra_sfx_heroes_bonehart_basicAttack_impact_var2.ogg",
      "kra_sfx_heroes_bonehart_basicAttack_impact_var3.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "sequence",
    source_group = "SFX"
  },
  HeroDragonBoneDiseaseNovaCast = {
    files = {
      "kra_sfx_heroes_bonehart_diseaseNova_cast_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroDragonBonePlagueCloudCast = {
    files = {
      "kra_sfx_heroes_bonehart_plagueCloud_cast_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  HeroDragonBoneSpineRainCast = {
    files = {
      "kra_sfx_heroes_bonehart_spineRain_cast_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  HeroDragonBoneSpineRainImpact = {
    files = {
      "kra_sfx_heroes_bonehart_spineRain_impact_var1_v1.ogg",
      "kra_sfx_heroes_bonehart_spineRain_impact_var2_v1.ogg",
      "kra_sfx_heroes_bonehart_spineRain_impact_var3_v1.ogg",
    },
    gain = 0.3,
    loop = false,
    mode = "sequence",
    source_group = "SFX"
  },
  HeroDragonBoneSpreadingBurstCast = {
    files = {
      "kra_sfx_heroes_bonehart_spreadingBurst_cast_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroDragonBoneSpreadingBurstImpact = {
    files = {
      "kra_sfx_heroes_bonehart_spreadingBurst_impact_var1_v1.ogg",
      "kra_sfx_heroes_bonehart_spreadingBurst_impact_var2_v1.ogg",
      "kra_sfx_heroes_bonehart_spreadingBurst_impact_var3_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    mode = "sequence",
    source_group = "SFX"
  },
  HeroDragonBoneUltimateCast = {
    files = {
      "kra_sfx_heroes_bonehart_raiseDrakes_cast_op2_v1.ogg"
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroDragonArbDeath = {
    files = {
      "kr_voice_silvara_death_c.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroDragonArbTaunt = {
    files = {
      "kr_voice_silvara_move2_a.ogg",
      "kr_voice_silvara_move3_b.ogg",
      "kr_voice_silvara_move4_b.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroDragonArbTauntIntro = {
    files = {
      "kr_voice_silvara_move2_a.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroDragonArbTauntSelect = {
    files = {
      "kr_voice_silvara_move2_a.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroDragonArbArboreansHit = {
    files = {
      "kra_sfx_crocs_hero_spawned_unit_shield_hit_var1_v1.ogg",
      "kra_sfx_crocs_hero_spawned_unit_shield_hit_var2_v1.ogg",
      "kra_sfx_crocs_hero_spawned_unit_shield_hit_var3_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    delay = 0.333,
    mode = "random",
    source_group = "SFX",
  },
  HeroDragonArbAttackSplints = {
    files = {
      "kra_sfx_crocs_hero_throws_spikes_var1_v1.ogg",
      "kra_sfx_crocs_hero_throws_spikes_var2_v1.ogg",
      "kra_sfx_crocs_hero_throws_spikes_var3_v1.ogg",
      "kra_sfx_crocs_hero_throws_spikes_var4_v1.ogg",
      "kra_sfx_crocs_hero_throws_spikes_var4_v1.ogg",
      "kra_sfx_crocs_hero_throws_spikes_var4_v1.ogg",
      "kra_sfx_crocs_hero_throws_spikes_var4_v1.ogg",
    },
    gain = 0.23,
    loop = false,
    mode = "random",
    -- delay = 0.333,
    source_group = "SFX",
  },
  HeroDragonArbUltimate = {
    files = {
      "kra_sfx_crocs_hero_ultimate_blue_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    -- delay = 0.333,
    source_group = "SFX",
  },

  HeroKratoaTaunt = {
    files = {
      "kr_voice_kratoa_taunt-select_c.ogg",
      "kr_voice_kratoa_taunt02_b.ogg",
      "kr_voice_kratoa_taunt03_b.ogg",
      "kr_voice_kratoa_taunt04_c.ogg",
    },
    gain = 0.6,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroKratoaTauntIntro = {
    files = {
      "kr_voice_kratoa_taunt04_c.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroKratoaTauntSelect = {
    files = {
      "kr_voice_kratoa_taunt-select_c.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroKratoaDeath = {
    files = {
      "kr_voice_kratoa_death_b.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroKratoaBasicAttack = {
    files = {
      "kra_sfx_heroes_kratoa_basicAttack_cast_var1_v1.ogg",
      "kra_sfx_heroes_kratoa_basicAttack_cast_var2_v1.ogg",
      "kra_sfx_heroes_kratoa_basicAttack_cast_var3_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    delay = 0.5,
    mode = "random",
    source_group = "SFX",
  },
  HeroKratoaTemperTantrum = {
    files = {
      "kra_sfx_heroes_temperTantrum_cast_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    delay = 0.4,
    source_group = "SFX",
  },
  HeroKratoaHotheaded = {
    files = {
      "kra_sfx_heroes_kratoa_hotheaded_cast_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX",
  },
  HeroKratoaDoubleTroubleCast = {
    files = {
      "kra_sfx_heroes_kratoa_doubleTrouble_cast_op1_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  HeroKratoaDoubleTroubleImpact = {
    files = {
      "kra_sfx_heroes_kratoa_doubleTrouble_impact_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  HeroKratoaWildEruption = {
    files = {
      "kra_sfx_heroes_wildEruption_cast_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    delay = 0.5,
    source_group = "SFX",
  },
  HeroKratoaRageOutburstCast = {
    files = {
      "kra_sfx_heroes_kratoa_rageOutburst_cast_var2_v1.ogg",
      "kra_sfx_heroes_kratoa_rageOutburst_cast_var2_v1.ogg",
      "kra_sfx_heroes_kratoa_rageOutburst_cast_var3_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    ignore = 0.5,
    mode = 'random',
    source_group = "SFX",
  },
  HeroKratoaRageOutburstImpact = {
    files = {
      "kra_sfx_heroes_kratoa_rageOutburst_impact_var2_v1.ogg",
      "kra_sfx_heroes_kratoa_rageOutburst_impact_var2_v1.ogg",
      "kra_sfx_heroes_kratoa_rageOutburst_impact_var3_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    mode = 'random',
    source_group = "SFX",
  },
  HeroKratoaRageOutburstDeath = {
    files = {
      "kra_sfx_heroes_kratoa_rageOutburst_death_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  HeroSpiderTunnelingIn = {
    files = {
      "kra_sfx_spiders_heroe_tunneling_in_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    source_group = "SFX",
  },
  HeroSpiderTunnelingOut = {
    files = {
      "kra_sfx_spiders_heroe_tunneling_out_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX",
  },
  HeroSpiderTunnelingAppear = {
    files = {
      "kra_sfx_spiders_heroe_tunneling_appear_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    source_group = "SFX",
  },
  HeroSpiderGlobalCocoons = {
    files = {
      "kra_sfx_spiders_heroe_global_cocoons_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    source_group = "SFX",
  },
  HeroSpiderGlobalSpawn = {
    files = {
      "kra_sfx_spiders_heroe_global_spawn_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX",
  },
  HeroSpiderSupremeHunter = {
    files = {
      "kra_sfx_spiders_heroe_supremehunter_fullSeq_v1.ogg",
    },
    gain = 0.38,
    loop = false,
    source_group = "SFX",
  },
  HeroSpiderAreaDamage = {
    files = {
      "kra_sfx_spiders_heroe_areadamage_v1.ogg",
    },
    gain = 0.45,
    loop = false,
    source_group = "SFX",
    delay = 0.8,

  },
  HeroSpiderBasicAttack = {
    files = {
      "kra_sfx_spiders_heroe_melee_op1_var1_v1.ogg",
      "kra_sfx_spiders_heroe_melee_op1_var2_v1.ogg",
      "kra_sfx_spiders_heroe_melee_op1_var3_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    delay = 0.5,
    mode = "random",
    source_group = "SFX",
  },
  HeroSpiderInstakill = {
    files = {
      "kra_sfx_spiders_heroe_instakill_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX",
    delay = 0.3,

  },
  HeroSpiderAttackRanged = {
    files = {
      "kra_sfx_spiders_heroe_range_var1_v1.ogg",
      "kra_sfx_spiders_heroe_range_var2_v1.ogg",
      "kra_sfx_spiders_heroe_range_var3_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  HeroSpiderTaunt = {
    files = {
      "kr_voice_spydyr_01a.ogg",
      "kr_voice_spydyr_03b.ogg",
      "kr_voice_spydyr_04d.ogg",
      "kr_voice_spydyr_taunt-select_b.ogg",
    },
    gain = 0.6,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroSpiderTauntIntro = {
    files = {
      "kr_voice_spydyr_01a.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroSpiderTauntSelect = {
    files = {
      "kr_voice_spydyr_taunt-select_b.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroSpiderDeath = {
    files = {
      "kr_voice_spydyr_death_b.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroWukongTaunt = {
    files = {
      "kr_voice_sunwukong_1_c.ogg",
      "kr_voice_sunwukong_2_b.ogg",
      "kr_voice_sunwukong_3_b.ogg",
      "kr_voice_sunwukong_4_a.ogg",
    },
    gain = 0.6,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroWukongTauntIntro = {
    files = {
      "kr_voice_sunwukong_1_c.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroWukongTauntSelect = {
    files = {
      "kr_voice_sunwukong_4_a.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroWukongDeath = {
    files = {
      "kr_voice_sunwukong_5_c.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroWukongTauntZH = {
    files = {
      "kr_voice_CN_wukong_here_comes_your_grandpa_sun_1.ogg",
      "kr_voice_CN_wukong_are_there_any_peaches_in_your_land_1.ogg",
      "kr_voice_CN_wukong_vivid_and_monkey-like_2.ogg",
      "kr_voice_CN_wukong_where_are_you_going_demon_2.ogg",
    },
    gain = 0.9,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  HeroWukongTauntZHIntro = {
    files = {
      "kr_voice_CN_wukong_here_comes_your_grandpa_sun_1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroWukongTauntZHSelect = {
    files = {
      "kr_voice_CN_wukong_here_comes_your_grandpa_sun_1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroWukongDeathZH = {
    files = {
      "kr_voice_CN_wukong_grandpa_sun_will_be_right_back_2.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "TAUNTS"
  },
  HeroWukongDeathSFX = {
    files = {
      "kra_sfx_wukong_hero_death_op3_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroWukongUltimate = {
    files = {
      "kra_sfx_wukong_hero_ultimate_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroWukongClones = {
    files = {
      "kra_sfx_wukong_hero_hair_clones_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroWukongInstakill = {
    files = {
      "kra_sfx_wukong_hero_instakill_v2.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroWukongMeleeFast = {
    files = {
      "kra_sfx_wukong_hero_melee_fast_hits_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    chance = 0.5,
    source_group = "SFX"
  },
  HeroWukongMeleeJump = {
    files = {
      "kra_sfx_wukong_hero_melee_jump_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    chance = 0.5,
    source_group = "SFX"
  },
  HeroWukongMeleeSimple = {
    files = {
      "kra_sfx_wukong_hero_melee_simple_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    chance = 0.5,
    source_group = "SFX"
  },
  HeroWukongMeleeSpin = {
    files = {
      "kra_sfx_wukong_hero_melee_spin_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    chance = 0.5,
    source_group = "SFX"
  },
  HeroWukongMultiStaff = {
    files = {
      "kra_sfx_wukong_hero_multi_staf_op1_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  HeroWukongZhuSmash = {
    files = {
      "kra_sfx_wukong_hero_zhu_smash_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },

  --KR5 TOWERS
  TowerRoyalArchersTaunt = {
    files = {
      "kr_voice_royalArchers_taunt_var1c.ogg",
      "kr_voice_royalArchers_taunt2_var1a.ogg",
      "kr_voice_royalArchers_taunt3_var1a.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerRoyalArchersTauntSelect = {
    files = {
      "kr_voice_royalArchers_taunt_var1c.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerRoyalArchersSkillATaunt = {
    files = {
      "kr_voice_royalArchers_skill_a_var1a.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerRoyalArchersSkillBTaunt = {
    files = {
      "kr_voice_royalArchers_skill_b_var1b.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerRoyalArchersArmorPiercerShot = {
    files = {
      "kra_sfx_tower_royalArchers_skill_armorPiercer_v1.ogg"
    },
    gain = 0.8,
    loop = false,
    delay = 1.12,
    source_group = "SFX"
  },
  TowerRoyalArchersArmorPiercerHit = {
    files = {
      "kra_sfx_tower_royalArchers_skill_impact_v1.ogg"
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  TowerRoyalArchersRapaciousHunterTakeOff = {
    files = {
      "kra_sfx_tower_royalArchers_skill_rapaciousHunter_takeOff_v1.ogg"
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  TowerRoyalArchersRapaciousHunterDescend = {
    files = {
      "kra_sfx_tower_royalArchers_skill_strike_var1_v1.ogg",
      "kra_sfx_tower_royalArchers_skill_strike_var2_v1.ogg"
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  TowerRoyalArchersRapaciousHunterHit = {
    files = {
      "kra_sfx_tower_royalArchers_skill_rapaciousHunter_impact_var1_v1.ogg",
      "kra_sfx_tower_royalArchers_skill_rapaciousHunter_impact_var2_v1.ogg"
    },
    mode = "sequence",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  TowerArcaneWizardTaunt = {
    files = {
      "kr_voice_arcanewizard_taunt_var1c.ogg",
      "kr_voice_arcanewizard_taunt2_var1a.ogg",
      "kr_voice_arcanewizard_taunt3_var1b.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerArcaneWizardTauntSelect = {
    files = {
      "kr_voice_arcanewizard_taunt_var1c.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerArcaneWizardSkillATaunt = {
    files = {
      "kr_voice_arcanewizard_skill_a_var1a.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerArcaneWizardSkillBTaunt = {
    files = {
      "kr_voice_arcanewizard_skill_b_var1a.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerArcaneWizardBasicAttack = {
    files = {
      "kra_sfx_tower_arcaneWizard_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_arcaneWizard_basicAttack_var2_v1.ogg",
      "kra_sfx_tower_arcaneWizard_basicAttack_var3_v1.ogg",
    },
    gain = 1,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerArcaneWizardDisintegrate = {
    files = {
      "kra_sfx_tower_arcaneWizard_skill_disintegration_v1.ogg",
    },
    gain = 0.6,
    delay = 0.2,
    loop = false,
    source_group = "SFX"
  },
  TowerArcaneWizardEmpowerment = {
    files = {
      "kra_sfx_tower_arcaneWizard_skill_empowerment_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  TowerTricannonTaunt = {
    files = {
      "kr_voice_tricannon_taunt3_var1a.ogg",
      "kr_voice_tricannon_taunt2_var1c.ogg",
      "kr_voice_tricannon_taunt_var1a.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerTricannonTauntSelect = {
    files = {
      "kr_voice_tricannon_taunt3_var1a.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerTricannonSkillATaunt = {
    files = {
      "kr_voice_tricannon_skill_a_var1c.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerTricannonSkillBTaunt = {
    files = {
      "kr_voice_tricannon_skill_b_var1a.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerTricannonBasicAttackFire = {
    files = {
      "kra_sfx_tower_tricannon_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_tricannon_basicAttack_var2_v1.ogg",
      "kra_sfx_tower_tricannon_basicAttack_var3_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerTricannonBasicAttackImpact = {
    files = {
      "kra_sfx_tower_tricannon_basicAttack_impact-single_var1_v1.ogg",
      "kra_sfx_tower_tricannon_basicAttack_impact-single_var2_v1.ogg",
      "kra_sfx_tower_tricannon_basicAttack_impact-single_var3_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerTricannonBombardmentLvl1 = {
    files = {
      "kra_sfx_tower_tricannon_skill_bombardment_lvl1_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  TowerTricannonBombardmentLvl2 = {
    files = {
      "kra_sfx_tower_tricannon_skill_bombardment_lvl2_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  TowerTricannonBombardmentLvl3 = {
    files = {
      "kra_sfx_tower_tricannon_skill_bombardment_lvl3_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  TowerTricannonOverheat = {
    files = {
      "kra_sfx_tower_tricannon_skill_overheat-oneshot_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  TowerPaladinCovenantTaunt = {
    files = {
      "kr_voice_paladincovenant_taunt3_var1a.ogg",
      "kr_voice_paladincovenant_taunt2_var1a.ogg",
      "kr_voice_paladincovenant_taunt_var1a.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerPaladinCovenantTauntSelect = {
    files = {
      "kr_voice_paladincovenant_taunt3_var1a.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerPaladinCovenantSkillATaunt = {
    files = {
      "kr_voice_paladincovenant_skill_a_var1a.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerPaladinCovenantSkillBTaunt = {
    files = {
      "kr_voice_paladincovenant_skill_b_var1b.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerPaladinCovenantHealingPrayer = {
    files = {
      "kra_sfx_tower_paladinCovenant_skill_healingPrayer_v1.ogg",
    },
    gain = 0.5,
    delay = 0.5,
    loop = false,
    source_group = "SFX"
  },
  TowerPaladinCovenantLeadByExample = {
    files = {
      "kra_sfx_tower_paladinCovenant_skill_leadByExampleAura_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  TowerPaladinCovenantUnitDeath = {
    files = {
      "kra_sfx_tower_paladinCovenant_unitDeath_var1_v1.ogg",
      "kra_sfx_tower_paladinCovenant_unitDeath_var2_v1.ogg",
      "kra_sfx_tower_paladinCovenant_unitDeath_var3_v1.ogg",
    },
    gain = 1.5,
    loop = false,
    mode = "sequence",
    source_group = "SFX"
  },
  TowerDemonPitTaunt = {
    files = {
      "kr_voice_demonpit_taunt3_var1a.ogg",
      "kr_voice_demonpit_taunt2_var1c.ogg",
      "kr_voice_demonpit_taunt_var1a.ogg",
    },
    gain = 1,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerDemonPitTauntSelect = {
    files = {
      "kr_voice_demonpit_taunt3_var1a.ogg",
    },
    gain = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerDemonPitSkillATaunt = {
    files = {
      "kr_voice_demonpit_skill_a_var1c.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerDemonPitSkillBTaunt = {
    files = {
      "kr_voice_demonpit_skill_b_var1a.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerDemonPitBasicAttack = {
    files = {
      "kra_sfx_tower_demonPit_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_demonPit_basicAttack_var2_v1.ogg",
      "kra_sfx_tower_demonPit_basicAttack_var3_v1.ogg",
    },
    gain = 1,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerDemonPitDemonExplosion = {
    files = {
      "kra_sfx_tower_demonPit_demonExplosion_v1.ogg",
    },
    gain = 1,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerDemonPitBigGuyBasicAttack = {
    files = {
      "kra_sfx_tower_demonPit_bigGuy_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_demonPit_bigGuy_basicAttack_var2_v1.ogg",
      "kra_sfx_tower_demonPit_bigGuy_basicAttack_var3_v1.ogg",
    },
    delay = 0.2,
    gain = 0.4,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerArboreanEmissaryTaunt = {
    files = {
      "kr_voice_arboreanemissary_taunt3_var1a.ogg",
      "kr_voice_arboreanemissary_taunt2_var1a.ogg",
      "kr_voice_arboreanemissary_taunt_var1a.ogg",
    },
    gain = 1,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerArboreanEmissaryTauntSelect = {
    files = {
      "kr_voice_arboreanemissary_taunt3_var1a.ogg",
    },
    gain = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerArboreanEmissarySkillATaunt = {
    files = {
      "kr_voice_arboreanemissary_skill_a_var1b.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerArboreanEmissarySkillBTaunt = {
    files = {
      "kr_voice_arboreanemissary_skill_b_var1a.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerArboreanEmissaryBasicAttack = {
    files = {
      "kra_sfx_tower_arboreanEmissary_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_arboreanEmissary_basicAttack_var3_v1.ogg",
      "kra_sfx_tower_arboreanEmissary_basicAttack_var2_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerArboreanEmissaryGiftOfNature = {
    files = {
      "kra_sfx_tower_arboreanEmissary_giftOfNature_cast_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  TowerArboreanEmissaryThornyGarden = {
    files = {
      "kra_sfx_tower_arboreanEmissary_thornyGarden_spawn_var3_v1.ogg",
      "kra_sfx_tower_arboreanEmissary_thornyGarden_spawn_var2_v1.ogg",
      "kra_sfx_tower_arboreanEmissary_thornyGarden_spawn_var1_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerElvenStargazersTaunt = {
    files = {
      "kr_voice_elvenstargazers_taunt01_d.ogg",
      "kr_voice_elvenstargazers_taunt02_d.ogg",
      "kr_voice_elvenstargazers_taunt03_c.ogg",
    },
    gain = 1,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerElvenStargazersTauntSelect = {
    files = {
      "kr_voice_elvenstargazers_taunt01_d.ogg",
    },
    gain = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerElvenStargazersSkillATaunt = {
    files = {
      "kr_voice_elvenstargazers_skill_b_d.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerElvenStargazersSkillBTaunt = {
    files = {
      "kr_voice_elvenstargazers_skill_a_c.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerElvenStargazersBasicAttack = {
    files = {
      "kra_sfx_tower_elvenStargazer_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_elvenStargazer_basicAttack_var2_v1.ogg",
      "kra_sfx_tower_elvenStargazer_basicAttack_var3_v1.ogg",
      "kra_sfx_tower_elvenStargazer_basicAttack_var4_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerElvenStargazersRisingStarImpact = {
    files = {
      "kra_sfx_tower_elvenStargazer_risingStar_impact_var1_v1.ogg",
      "kra_sfx_tower_elvenStargazer_risingStar_impact_var2_v1.ogg",
      "kra_sfx_tower_elvenStargazer_risingStar_impact_var3_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerElvenStargazersEventHorizonCast = {
    files = {
      "kra_sfx_tower_elvenStargazer_eventHorizon_cast_v1.ogg",
    },
    gain = 0.7,
    delay = 0.5,
    loop = false,
    source_group = "SFX"
  },
  TowerElvenStargazersEventHorizonTeleportIn = {
    files = {
      "kra_sfx_tower_elvenStargazer_eventHorizon_teleportIn_v1.ogg",
    },
    gain = 0.7,
    delay = 0.3,
    loop = false,
    source_group = "SFX"
  },
  TowerElvenStargazersEventHorizonTeleportOut = {
    files = {
      "kra_sfx_tower_elvenStargazer_eventHorizon_teleportOut_v1.ogg",
    },
    gain = 0.7,
    delay = 0.3,
    loop = false,
    source_group = "SFX"
  },
  TowerBallistaTaunt = {
    files = {
      "kr_voice_ballistaoutpost_taunt01_d.ogg",
      "kr_voice_ballistaoutpost_taunt02_c.ogg",
      "kr_voice_ballistaoutpost_taunt03_b.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerBallistaTauntSelect = {
    files = {
      "kr_voice_ballistaoutpost_taunt01_d.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerBallistaSkillATaunt = {
    files = {
      "kr_voice_ballistaoutpost_skill_a_b.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerBallistaSkillBTaunt = {
    files = {
      "kr_voice_ballistaoutpost_skill_b_b.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerBallistaBasicAttack = {
    files = {
      "kra_sfx_tower_ballistaOutpost_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_ballistaOutpost_basicAttack_var2_v1.ogg",
      "kra_sfx_tower_ballistaOutpost_basicAttack_var3_v1.ogg",
      "kra_sfx_tower_ballistaOutpost_basicAttack_var4_v1.ogg",
      "kra_sfx_tower_ballistaOutpost_basicAttack_var5_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerBallistaScrapBombCast = {
    files = {
      "kra_sfx_tower_ballistaOutpost_scrapBomb_cast_var1_v1.ogg",
      "kra_sfx_tower_ballistaOutpost_scrapBomb_cast_var2_v1.ogg",
      "kra_sfx_tower_ballistaOutpost_scrapBomb_cast_var3_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerBallistaScrapBombExplosion = {
    files = {
      "kra_sfx_tower_ballistaOutpost_scrapBomb_explosion_var1_v1.ogg",
      "kra_sfx_tower_ballistaOutpost_scrapBomb_explosion_var2_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerBallistaFinalNail = {
    files = {
      "kra_sfx_tower_ballistaOutpost_finalNail_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  TowerNecromancerTaunt = {
    files = {
      "kr_voice_necromancerslair_taunt01_c.ogg",
      "kr_voice_necromancerslair_taunt02_a.ogg",
      "kr_voice_necromancerslair_taunt03_c.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerNecromancerTauntSelect = {
    files = {
      "kr_voice_necromancerslair_taunt01_c.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerNecromancerSkillATaunt = {
    files = {
      "kr_voice_necromancerslair_skill-a_c.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerNecromancerSkillBTaunt = {
    files = {
      "kr_voice_necromancerslair_skill-b_a.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerNecromancerBasicAttack = {
    files = {
      "kra_sfx_tower_necromancer_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_necromancer_basicAttack_var2_v1.ogg",
      "kra_sfx_tower_necromancer_basicAttack_var3_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerNecromancerBasicAttackHit = {
    files = {
      "kra_sfx_tower_necromancer_basicAttack_hit_var1_v1.ogg",
      "kra_sfx_tower_necromancer_basicAttack_hit_var2_v1.ogg",
      "kra_sfx_tower_necromancer_basicAttack_hit_var3_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerNecromancerBasicAttackSummon = {
    files = {
      "kra_sfx_tower_necromancer_basicAttack_boltSummon_var1_v1.ogg",
      "kra_sfx_tower_necromancer_basicAttack_boltSummon_var2_v1.ogg",
      "kra_sfx_tower_necromancer_basicAttack_boltSummon_var3_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerNecromancerSkeletonSummon = {
    files = {
      "kra_sfx_tower_necromancer_skeletonSummon_v2.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  TowerNecromancerDeathRider = {
    files = {
      "kra_sfx_tower_necromancer_deathRider_op1_v1.ogg",
    },
    gain = 1,
    delay = 0.3,
    loop = false,
    source_group = "SFX"
  },
  TowerNecromancerSigilOfSilence = {
    files = {
      "kra_sfx_tower_necromancer_sigilOfSilence_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },

  TowerRocketGunnersTaunt = {
    files = {
      "kr_voice_rocketgunners_taunt01_c.ogg",
      "kr_voice_rocketgunners_taunt02_a.ogg",
      "kr_voice_rocketgunners_taunt03_a.ogg",
    },
    gain = 1,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerRocketGunnersTauntSelect = {
    files = {
      "kr_voice_rocketgunners_taunt01_c.ogg",
    },
    gain = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerRocketGunnersSkillATaunt = {
    files = {
      "kr_voice_rocketgunners_skill-a_c.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerRocketGunnersSkillBTaunt = {
    files = {
      "kr_voice_rocketgunners_skill-b_c.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerRocketGunnersLiftoffTaunt = {
    files = {
      "kr_voice_rocketgunners_liftoff_b.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerRocketGunnersTouchdownTaunt = {
    files = {
      "kr_voice_rocketgunners_touchdown_c.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerRocketGunnersSpawn = {
    files = {
      "kra_sfx_tower_rocketGunners_unitSpawn_v1.ogg",
    },
    delay = 0.8,
    gain = 0.3,
    loop = false,
    source_group = "SFX"
  },
  TowerRocketGunnersTakeoff = {
    files = {
      "kra_sfx_tower_rocketGunners_takeoff_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  TowerRocketGunnersBasicAttack = {
    files = {
      "kra_sfx_tower_rocketGunners_basicAttack_var1_1.ogg",
      "kra_sfx_tower_rocketGunners_basicAttack_var2_1.ogg",
      "kra_sfx_tower_rocketGunners_basicAttack_var3_1.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerRocketGunnersStingMissileCast = {
    files = {
      "kra_sfx_tower_rocketGunners_stingMissile_cast_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  TowerRocketGunnersStingMissileExplosion = {
    files = {
      "kra_sfx_tower_rocketGunners_stingMissile_explosion_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  TowerRocketGunnersPhosphoricCoating = {
    files = {
      "kra_sfx_tower_rocketGunners_phosphoricCoating_var1_v1.ogg",
      "kra_sfx_tower_rocketGunners_phosphoricCoating_var2_v1.ogg",
      "kra_sfx_tower_rocketGunners_phosphoricCoating_var3_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerFlamespitterTaunt = {
    files = {
      "kr_voice_dwarvenflamespitter_taunt01_c.ogg",
      "kr_voice_dwarvenflamespitter_taunt02_a.ogg",
      "kr_voice_dwarvenflamespitter_taunt03_a.ogg",
    },
    gain = 1,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerFlamespitterTauntSelect = {
    files = {
      "kr_voice_dwarvenflamespitter_taunt01_c.ogg",
    },
    gain = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerFlamespitterSkillATaunt = {
    files = {
      "kr_voice_dwarvenflamespitter_skill-a_b.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerFlamespitterSkillBTaunt = {
    files = {
      "kr_voice_dwarvenflamespitter_skill-b_d.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerFlamespitterBasicAttack = {
    files = {
      "kra_sfx_tower_dwarvenFlamespitter_basicAttack_cast_var1_v1.ogg",
      "kra_sfx_tower_dwarvenFlamespitter_basicAttack_cast_var2_v1.ogg",
      "kra_sfx_tower_dwarvenFlamespitter_basicAttack_cast_var3_v1.ogg",
    },
    gain = 0.75,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerFlamespitterBlazingTrailCast = {
    files = {
      "kra_sfx_tower_dwarvenFlamespitter_blazingTrail_cast_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  TowerFlamespitterBlazingTrailImpact = {
    files = {
      "kra_sfx_tower_dwarvenFlamespitter_blazingTrail_impact_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  TowerFlamespitterScorchingTorchesCast = {
    files = {
      "kra_sfx_tower_dwarvenFlamespitter_scorchingTorches_cast_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  TowerFlamespitterScorchingTorchesFlareUp = {
    files = {
      "kra_sfx_tower_dwarvenFlamespitter_scorchingTorches_flareUp_var1_v1.ogg",
      "kra_sfx_tower_dwarvenFlamespitter_scorchingTorches_flareUp_var2_v1.ogg",
      "kra_sfx_tower_dwarvenFlamespitter_scorchingTorches_flareUp_var3_v1.ogg",
      "kra_sfx_tower_dwarvenFlamespitter_scorchingTorches_flareUp_var4_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerBarrelTaunt = {
    files = {
      "kr_voice_battlebrewmasters_select_c.ogg",
      "kr_voice_battlebrewmasters_taunt01_d.ogg",
      "kr_voice_battlebrewmasters_taunt02_a.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerBarrelTauntSelect = {
    files = {
      "kr_voice_battlebrewmasters_select_c.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerBarrelSkillATaunt = {
    files = {
      "kr_voice_battlebrewmasters_skill-a_b.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerBarrelSkillBTaunt = {
    files = {
      "kr_voice_battlebrewmasters_skill-b_d.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerBarrelBasicAttackCast = {
    files = {
      "kra_sfx_battleBrewmasters_basicAttack_cast_var1_v1.ogg",
      "kra_sfx_battleBrewmasters_basicAttack_cast_var2_v1.ogg",
      "kra_sfx_battleBrewmasters_basicAttack_cast_var3_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    mode = "random",
    source_group = "SFX",
    delay = 0.1,
  },
  TowerBarrelBasicAttackImpact = {
    files = {
      "kra_sfx_tower_brewMaster_basicAttack_impact_var1_v1.ogg",
      "kra_sfx_tower_brewMaster_basicAttack_impact_var2_v1.ogg",
      "kra_sfx_tower_brewMaster_basicAttack_impact_var3_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerBarrelBadBatchRattle = {
    files = {
      "kra_sfx_tower_brewMaster_badBatch_rattle_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  TowerBarrelBadBatchExplosion = {
    files = {
      "kra_sfx_tower_brewMaster_badBatch_explosion_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  TowerBarrelElixirOfMightEvict = {
    files = {
      "kra_sfx_tower_brewMaster_elixirOfMight_evict_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  TowerBarrelElixirOfMightDrink = {
    files = {
      "kra_sfx_tower_brewMaster_elixirOfMight_drinkAndBoost_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
    delay = 2.5,
  },
  TowerSandTaunt = {
    files = {
      "kr_voice_dunesentinels_select_e.ogg",
      "kr_voice_dunesentinels_taunt01_b.ogg",
      "kr_voice_dunesentinels_taunt02_d.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerSandTauntSelect = {
    files = {
      "kr_voice_dunesentinels_select_e.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerSandSkillATaunt = {
    files = {
      "kr_voice_dunesentinels_skill-a_d.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerSandSkillBTaunt = {
    files = {
      "kr_voice_dunesentinels_skill-b_c.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerSandBasicAttackHit = {
    files = {
      "kra_sfx_tower_duneSentinels_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_duneSentinels_basicAttack_var2_v1.ogg",
      "kra_sfx_tower_duneSentinels_basicAttack_var3_v1.ogg",
      "kra_sfx_tower_duneSentinels_basicAttack_var4_v1.ogg",
      "kra_sfx_tower_duneSentinels_basicAttack_var5_v1.ogg",
    },
    gain = 0.2,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  TowerSandSkillGoldCast = {
    files = {
      "kra_sfx_tower_duneSentinels_bountyHunt_cast_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  TowerSandSkillBigBladeCast = {
    files = {
      "kra_sfx_tower_duneSentinels_whirlingDoom_cast_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
    delay = 0.5
  },
  TowerGhostTaunt = {
    files = {
      "kr_voice_grimwraiths_select_c.ogg",
      "kr_voice_grimwraiths_taunt01_c.ogg",
      "kr_voice_grimwraiths_taunt02_c.ogg",
    },
    gain = 0.6,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerGhostTauntSelect = {
    files = {
      "kr_voice_grimwraiths_select_c.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerGhostSkillATaunt = {
    files = {
      "kr_voice_grimwraiths_skill-a_b.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerGhostSkillBTaunt = {
    files = {
      "kr_voice_grimwraiths_skill-b_d.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerGhostExtraDamageCast = {
    files = {
      "kra_sfx_tower_grimWraiths_soulSiphoning_cast_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
    delay = 0,
  },
  TowerGhostSoulAttackCast = {
    files = {
      "kra_sfx_tower_grimWraiths_undyingDread_cast_one_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
    delay = 0,
  },
  TowerGhostSoulAttackTravel = {
    files = {
      "kra_sfx_tower_grimWraiths_undyingDread_travel_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
    delay = 0,
  },
  TowerGhostSoulAttackImpact = {
    files = {
      "kra_sfx_tower_grimWraiths_undyingDread_impact_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
    delay = 0,
  },
  TowerGhostTeleport = {
    files = {
      "kra_sfx_tower_grimWraiths_teleport_out-in_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
    delay = 0,
  },
  TowerGhostSpawnUnit = {
    files = {
      "kra_sfx_tower_grimWraiths_spawnUnit_var1_v1.ogg",
      "kra_sfx_tower_grimWraiths_spawnUnit_var2_v1.ogg",
      "kra_sfx_tower_grimWraiths_spawnUnit_var3_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  TowerRayTaunt = {
    files = {
      "kr_voice_eldrictchchannelers_select_[2]a.ogg",
      "kr_voice_eldrictchchannelers_taunt01_b.ogg",
      "kr_voice_eldrictchchannelers_taunt02_[2]b.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerRayTauntSelect = {
    files = {
      "kr_voice_eldrictchchannelers_select_[2]a.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerRaySkillATaunt = {
    files = {
      "kr_voice_eldrictchchannelers_skill-a_[2]c.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerRaySkillBTaunt = {
    files = {
      "kr_voice_eldrictchchannelers_skill-b_b.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerRayBasicAttackCast = {
    files = {
      "kra_sfx_tower_eldrictchChannelers_basicAttack_long_var1_v1.ogg",
      "kra_sfx_tower_eldrictchChannelers_basicAttack_long_var2_v1.ogg",
      "kra_sfx_tower_eldrictchChannelers_basicAttack_long_var3_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    mode = "random",
    source_group = "SFX",
    interruptible = true,
  },
  TowerRayBasicAttackOffset = {
    files = {
      "kra_sfx_tower_eldrictchChannelers_basicAttack_offset_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
    delay = 0,
  },
  TowerRayMutationHexCast = {
    files = {
      "kra_sfx_tower_eldrictchChannelers_mutationHex_cast_var1_v1.ogg",
      "kra_sfx_tower_eldrictchChannelers_mutationHex_cast_var3_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  TowerDarkElfTaunt = {
    files = {
      "kr_voice_twilightlongbows_taunt-01_a.ogg",
      "kr_voice_twilightlongbows_taunt-02_b.ogg",
      "kr_voice_twilightlongbows_select_b.ogg",
    },
    gain = 0.8,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerDarkElfTauntSelect = {
    files = {
      "kr_voice_twilightlongbows_select_b.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerDarkElfSkillATaunt = {
    files = {
      "kr_voice_twilightlongbows_skill-a_g.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerDarkElfSkillBTaunt = {
    files = {
      "kr_voice_twilightlongbows_skill-b_d.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerDarkElfUnitTaunt = {
    files = {
      "kr_voice_twilightlongbows_skill-a_g.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerDarkElfBasicAttackCast = {
    files = {
      "kra_sfx_tower_twlightLongbows_basicAttack_cast-noCharge_var1_v1.ogg",
      "kra_sfx_tower_twlightLongbows_basicAttack_cast-noCharge_var2_v1.ogg",
      "kra_sfx_tower_twlightLongbows_basicAttack_cast-noCharge_var3_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    mode = "random",
    source_group = "SFX",
    interruptible = true,
  },
  TowerDarkElfSupportBladesSpawn = {
    files = {
      "kra_sfx_tower_twlightLongbows_supportBlades_spawn_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX",
  },
  TowerDarkElfThrillOfTheHuntCast = {
    files = {
      "kra_sfx_tower_twlightLongbows_thrillOfTheHunt_cast-travelOnly_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    delay = 0.7,
    source_group = "SFX",
  },
  TowerWeirdwoodBasicAttackCast = {
    files = {
      "kra_sfx_stage17_weirdwood_basicAttack_throw_var1_v1.ogg",
      "kra_sfx_stage17_weirdwood_basicAttack_throw_var2_v1.ogg",
      "kra_sfx_stage17_weirdwood_basicAttack_throw_var3_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    mode = "random",
    source_group = "SFX",
    interruptible = true,
  },
  TowerWeirdwoodBasicAttackHit = {
    files = {
      "kra_sfx_stage17_weirdwood_basicAttack_explosion_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX",
  },
  TowerWeirdwoodTransform = {
    files = {
      "kra_sfx_stage17_weirdwood_deathwoodTransform_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX",
  },
  TowerWeirdwoodCorruption = {
    files = {
      "kra_sfx_stage17_weirdwood_leavesFall_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX",
  },
  TowerElvenBarrackUnitTaunt = {
    files = {
      "kr_voice_elvenmercenaries_taunt-01_c.ogg",
      "kr_voice_elvenmercenaries_taunt-02_b.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerHermitToadTaunt = {
    files = {
      "kr_voice_boghermit_build_a.ogg",
      "kr_voice_boghermit_build2_b.ogg",
      "kr_voice_boghermit_build3_c.ogg",
    },
    gain = 0.8,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerHermitToadTauntSelect = {
    files = {
      "kr_voice_boghermit_build_a.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerHermitToadSkillATaunt = {
    files = {
      "kr_voice_boghermit_power1_c.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerHermitToadSkillBTaunt = {
    files = {
      "kr_voice_boghermit_power2_c.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerHermitToadSwitchToArtillery = {
    files = {
      "kr_voice_boghermit_switchtoartillery_c.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerHermitToadSwitchToMage = {
    files = {
      "kr_voice_boghermit_switchtomage_c.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerHermitToadShootMagic = {
    files = {
      "kra_sfx_crocs_tower_pipe_shoot_magic_var1_v1.ogg",
    },
    gain = 0.25,
    loop = false,
    -- delay = 0.333,
    source_group = "SFX",
  },
  TowerHermitToadShootEngineer = {
    files = {
      "kra_sfx_crocs_tower_pipe_shoot_water_var3_v1.ogg",
    },
    gain = 0.25,
    loop = false,
    -- delay = 0.333,
    source_group = "SFX",
  },
  TowerHermitToadShootEngineerImpact = {
    files = {
      "kra_sfx_crocs_tower_pipe_shoot_water_impact_var3_v1.ogg",
    },
    gain = 0.25,
    loop = false,
    -- delay = 0.333,
    source_group = "SFX",
  },
  TowerHermitToadBackToPond = {
    files = {
      "kra_sfx_crocs_tower_stomp_path_backToPond_v1.ogg",
    },
    gain = 0.35,
    loop = false,
    -- delay = 0.333,
    source_group = "SFX",
  },
  TowerHermitToadJump = {
    files = {
      "kra_sfx_crocs_tower_stomp_path_jumpOut_v1.ogg",
    },
    gain = 0.35,
    loop = false,
    -- delay = 0.333,
    source_group = "SFX",
  },
  TowerHermitToadFall = {
    files = {
      "kra_sfx_crocs_tower_stomp_path_v1.ogg",
    },
    gain = 0.35,
    loop = false,
    -- delay = 0.333,
    source_group = "SFX",
  },
  TowerHermitToadTongue = {
    files = {
      "kra_sfx_crocs_tower_tonge_shoot_v1.ogg",
    },
    gain = 0.35,
    loop = false,
    -- delay = 0.333,
    source_group = "SFX",
  },

  TowerDwarfTaunt = {
    files = {
      "kr_voice_cannoneersquad_taunt-select_e.ogg",
      "kr_voice_cannoneersquad_taunt02_b.ogg",
      "kr_voice_cannoneersquad_taunt03_c.ogg",
    },
    gain = 0.8,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerDwarfTauntSelect = {
    files = {
      "kr_voice_cannoneersquad_taunt-select_e.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerDwarfSkillATaunt = {
    files = {
      "kr_voice_cannoneersquad_skill-a_c.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerDwarfSkillBTaunt = {
    files = {
      "kr_voice_cannoneersquad_skill-b_c.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerDwarfBasicAttack = {
    files = {
      "kra_sfx_towers_cannoneers_basicAttack_var1_v1.ogg",
      "kra_sfx_towers_cannoneers_basicAttack_var2_v1.ogg",
      "kra_sfx_towers_cannoneers_basicAttack_var3_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerDwarfIncendiaryAmmo = {
    files = {
      "kra_sfx_towers_cannoneers_incendiaryAmmo_impact_var1_v1.ogg",
      "kra_sfx_towers_cannoneers_incendiaryAmmo_impact_var2_v1.ogg",
      "kra_sfx_towers_cannoneers_incendiaryAmmo_impact_var3_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerDwarfIncendiaryJump = {
    files = {
      "kra_sfx_towers_cannoneers_jump_cast_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    delay = 0.4,
    source_group = "SFX"
  },
  TowerDwarfUnitDeath = {
    files = {
      "kra_sfx_towers_cannoneers_death_var1.ogg",
      "kra_sfx_towers_cannoneers_death_var2.ogg",
      "kra_sfx_towers_cannoneers_death_var3.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerSparkingGeodeRay = {
    files = {
      "kra_sfx_spiders_tower_heode_ray_var1_v1.ogg",
      "kra_sfx_spiders_tower_heode_ray_var2_v1.ogg",
      "kra_sfx_spiders_tower_heode_ray_var3_v1.ogg",
      "kra_sfx_spiders_tower_heode_ray_var4_v1.ogg",
      "kra_sfx_spiders_tower_heode_ray_var5_v1.ogg",
    },
    gain = 0.15,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerSparkingGeodeCristalizeBolt = {
    files = {
      "kra_sfx_spiders_tower_heode_cristalize_bolt_var1_v1.ogg",
      "kra_sfx_spiders_tower_heode_cristalize_bolt_var2_v1.ogg",
      "kra_sfx_spiders_tower_heode_cristalize_bolt_var3_v1.ogg",
    },
    gain = 0.45,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  TowerSparkingGeodeCristalizeCast = {
    files = {
      "kra_sfx_spiders_tower_heode_cristalize_cast_var3_v1.ogg",
    },
    gain = 0.45,
    loop = false,
    source_group = "SFX"
  },
  TowerSparkingGeodeSpikeCast = {
    files = {
      "kra_sfx_spiders_tower_heode_spike_cast_v1.ogg",
    },
    gain = 0.45,
    loop = false,
    source_group = "SFX"
  },
  TowerSparkingGeodeSpikeLoop = {
    files = {
      "kra_sfx_spiders_tower_heode_spike_sparksLOOP_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  TowerSparkingGeodeTaunt = {
    files = {
      "kr_voice_surgecolossus_taunt-select_c.ogg",
      "kr_voice_surgecolossus_04c.ogg",
      "kr_voice_surgecolossus_05c.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerSparkingGeodeTauntSelect = {
    files = {
      "kr_voice_surgecolossus_taunt-select_c.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerSparkingGeodeSkillATaunt = {
    files = {
      "kr_voice_surgecolossus_03c.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerSparkingGeodeSkillBTaunt = {
    files = {
      "kr_voice_surgecolossus_02c.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerPandasTaunt = {
    files = {
      "kr_voice_pandatower_taunt01_f.ogg",
      "kr_voice_pandatower_taunt02_b.ogg",
      "kr_voice_pandatower_taunt03_c.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerPandasTauntSelect = {
    files = {
      "kr_voice_pandatower_taunt01_f.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerPandasSkillATaunt = {
    files = {
      "kr_voice_pandatower_thunderskill_a.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerPandasSkillBTaunt = {
    files = {
      "kr_voice_pandatower_hatskill_a.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerPandasSkillCTaunt = {
    files = {
      "kr_voice_pandatower_fieryskill_b.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerPandasTauntZH = {
    files = {
      "kr_voice_CN_pandas_panda_style_3.ogg",
      "kr_voice_CN_pandas_no_charge_for_awesome_2.ogg",
      "kr_voice_CN_pandas_we_know_kung-fu_2.ogg",
    },
    gain = 0.9,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerPandasTauntZHSelect = {
    files = {
      "kr_voice_CN_pandas_panda_style_3.ogg",
    },
    gain = 0.9,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  TowerPandasSkillATauntZH = {
    files = {
      "kr_voice_CN_pandas_ayumbabayeee_1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerPandasSkillBTauntZH = {
    files = {
      "kr_voice_CN_pandas_watch_and_see_3.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerPandasSkillCTauntZH = {
    files = {
      "kr_voice_CN_pandas_get_over_there_2.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "TAUNTS"
  },
  TowerPandasArrival = {
  files = {
    "kra_sfx_wukong_tower_pandas_arrival_single_var1_v1.ogg",
    "kra_sfx_wukong_tower_pandas_arrival_single_var2_v1.ogg",
    "kra_sfx_wukong_tower_pandas_arrival_single_var3_v1.ogg",
  },
    gain = 0.4,
    loop = false,
    mode = "sequence",
    source_group = "SFX",
  },
  TowerPandasDeath = {
  files = {
    "kra_sfx_wukong_tower_pandas_death_generic_var1_v1.ogg",
    "kra_sfx_wukong_tower_pandas_death_generic_var2_v1.ogg",
    "kra_sfx_wukong_tower_pandas_death_generic_var3_v1.ogg",
  },
    gain = 0.4,
    loop = false,
    mode = "sequence",
    source_group = "SFX",
  },
  TowerPandasRangedBolt = {
  files = {
    "kra_sfx_wukong_tower_pandas_ranged_bolt_var1_v1.ogg",
    "kra_sfx_wukong_tower_pandas_ranged_bolt_var2_v1.ogg",
    "kra_sfx_wukong_tower_pandas_ranged_bolt_var3_v1.ogg",
  },
    gain = 0.4,
    loop = false,
    mode = "sequence",
    source_group = "BULLETS",
  },
  TowerPandasRangedFire = {
  files = {
    "kra_sfx_wukong_tower_pandas_ranged_fire_var1_v1.ogg",
    "kra_sfx_wukong_tower_pandas_ranged_fire_var2_v1.ogg",
    "kra_sfx_wukong_tower_pandas_ranged_fire_var3_v1.ogg",
  },
    gain = 0.4,
    loop = false,
    mode = "sequence",
    source_group = "BULLETS",
  },
  TowerPandasRangedHat = {
  files = {
    "kra_sfx_wukong_tower_pandas_ranged_hat_var1_v1.ogg",
    "kra_sfx_wukong_tower_pandas_ranged_hat_var2_v1.ogg",
    "kra_sfx_wukong_tower_pandas_ranged_hat_var3_v1.ogg",
  },
    gain = 0.4,
    loop = false,
    mode = "sequence",
    source_group = "BULLETS",
  },
  TowerPandasSkillBolt = {
  files = {
    "kra_sfx_wukong_tower_pandas_skill_bolt_op1_v1.ogg",
  },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  TowerPandasSkillFire = {
  files = {
    "kra_sfx_wukong_tower_pandas_skill_fire_v1.ogg",
  },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  TowerPandasSkillHat = {
  files = {
    "kra_sfx_wukong_tower_pandas_skill_hat_throw_v1.ogg",
  },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  TowerPandasMelee = {
  files = {
    "kra_sfx_wukong_tower_pandas_melee_var1_v1.ogg",
    "kra_sfx_wukong_tower_pandas_melee_var2_v1.ogg",
    "kra_sfx_wukong_tower_pandas_melee_var3_v1.ogg",
  },
    gain = 0.2,
    loop = false,
    mode = "sequence",
    source_group = "SFX",
    chance = 0.2,
  },

  --KR5 REINFORCEMENTS
  ReinforcementTaunt = {
  files = {
    "kr_voice_neutralreinforcements_taunt01_a.ogg",
    "kr_voice_neutralreinforcements_taunt02_a.ogg",
    "kr_voice_neutralreinforcements_taunt03_a.ogg",
  },
    gain = 0.6,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS",
  },
  
  ReinforcementLinireaTaunt = {
  files = {
    "kr_voice_linireanreinforcements_taunt01_c.ogg",
    "kr_voice_linireanreinforcements_taunt02_c.ogg",
    "kr_voice_linireanreinforcements_taunt03_b.ogg",
  },
    gain = 0.6,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS",
  },

  ReinforcementDarkArmyTaunt = {
  files = {
    "kr_voice_darkarmyreinforcements_taunt01_a.ogg",
    "kr_voice_darkarmyreinforcements_taunt02_b.ogg",
    "kr_voice_darkarmyreinforcements_taunt03_b.ogg",
  },
    gain = 0.6,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS",
  },  

  --KR5 ENEMIES TERRAIN 1
  EnemyTuskedBrawlerDeath = {
    files = {
      "kra_sfx_enemy_tuskedBrawler_death_op1_var3.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  EnemyBearVanguardRage = {
    files = {
      "kra_sfx_enemy_bearVanguard_rage_v1.ogg"
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  EnemyBearVanguardDeath = {
    files = {
      "kra_sfx_enemy_bearVanguard_death_v1.ogg"
    },
    gain = 0.7,
    delay = 0.7,
    loop = false,
    source_group = "SFX"
  },
  EnemyTurtleShamanBasicAttack = {
    files = {
      "kra_sfx_enemy_turtleShaman_basicAttack_var3.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  EnemyTurtleShamanHealing = {
    files = {
      "kra_sfx_enemy_turtleShaman_healing_v1.ogg"
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  EnemyTurtleShamanDeath = {
    files = {
      "kra_sfx_enemy_turtleShaman_death_op2_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  EnemyRottenfangHyenaFeast = {
    files = {
      "kra_sfx_enemy_rottenfangHyena_barbaricFeast_v1.ogg"
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  EnemyRottenfangHyenaDeath = {
    files = {
      "kra_sfx_enemy_rottenfangHyena_death_var3_v1_op2.ogg"
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  EnemyCutthroatRat = {
    files = {
      "kra_sfx_enemy_cutthroatRat_stealthSkill-oneShot_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  EnemyCutthroatRatDeath = {
    files = {
      "kra_sfx_enemy_cutthroatRat_death_var3_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  EnemySkunkBombardierBasicAttackCast = {
    files = {
      "kra_sfx_enemy_skunkBombardier_basicAttack-whoosh_var1_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  EnemySkunkBombardierBasicAttackImpact = {
    files = {
      "kra_sfx_enemy_skunkBombardier_basicAttack-impact_var1_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  EnemySkunkBombardierDeath = {
    files = {
      "kra_sfx_enemy_skunkBombardier_death_v1.ogg"
    },
    gain = 0.85,
    delay = 0.8,
    loop = false,
    source_group = "SFX"
  },
  EnemyDreadeyeViperDeath = {
    files = {
      "kra_sfx_enemy_dreadeyeViper_death_var1_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  EnemyPatrollingVultureDeath = {
    files = {
      "kra_sfx_enemy_patrollingVulture_death_var4_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  EnemyRazingRhinoBasicAttack = {
    files = {
      "kra_sfx_enemy_razingRhino_basicAttack_var3_v1.ogg"
    },
    gain = 0.5,
    delay = 0.5,
    loop = false,
    source_group = "SFX"
  },
  EnemyRazingRhinoCharge = {
    files = {
      "kra_sfx_enemy_razingRhino_charge_v1.ogg"
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  EnemyRazingRhinoDeath = {
    files = {
      "kra_sfx_enemy_razingRhino_death_v1.ogg"
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  --KR5 ENEMIES TERRAIN 2
  EnemyAcolyteDeath = {
    files = {
      "kra_sfx_enemy_cultistAcolyte_death_var1_v1.ogg",
      "kra_sfx_enemy_cultistAcolyte_death_var2_v1.ogg",
      "kra_sfx_enemy_cultistAcolyte_death_var3_v1.ogg",
      "kra_sfx_enemy_cultistAcolyte_death_var4_v1.ogg",
      "kra_sfx_enemy_cultistAcolyte_death_var5_v1.ogg",
    },
    mode = "random",
    gain = 0.2,
    loop = false,
    source_group = "SFX"
  },
  EnemyAcolyteDeathSpecial = {
    files = {
      "kra_sfx_enemy_cultistAcolyte_deathSpecial_v1.ogg"
    },
    gain = 0.7,
    loop = false,
    delay = 0.4,
    source_group = "SFX"
  },
  EnemyAcolyteTentacleBasicAttack = {
    files = {
      "kra_sfx_enemy_acolyteTentacle_attack_var1_v1.ogg",
      "kra_sfx_enemy_acolyteTentacle_attack_var2_v1.ogg",
      "kra_sfx_enemy_acolyteTentacle_attack_var3_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyAcolyteTentacleDeath = {
    files = {
      "kra_sfx_enemy_acolyteTentacle_death_var1_v1.ogg",
      "kra_sfx_enemy_acolyteTentacle_death_var2_v1.ogg",
      "kra_sfx_enemy_acolyteTentacle_death_var3_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyVoidBlinkerTeleport = {
    files = {
      "kra_sfx_enemy_voidBlinker_teleport_v1.ogg"
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  EnemyVoidBlinkerDeath = {
    files = {
      "kra_sfx_enemy_voidBlinker_death_var1_v1.ogg",
      "kra_sfx_enemy_voidBlinker_death_var3_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyTwistedSisterSummonCast = {
    files = {
      "kra_sfx_enemy_twistedSister_summon_cast_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  EnemyTwistedSisterSummonSpawn = {
    files = {
      "kra_sfx_enemy_twistedSister_summon_spawn_v2.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  EnemyTwistedSisterDeath = {
    files = {
      "kra_sfx_enemy_twistedSister_death_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  EnemyNightmareDeath = {
    files = {
      "kra_sfx_enemy_nightmare_death_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  EnemyUnblindedPriestDeath = {
    files = {
      "kra_sfx_enemy_unblindedPriest_death_var1.ogg",
      "kra_sfx_enemy_unblindedPriest_death_var2.ogg",
      "kra_sfx_enemy_unblindedPriest_death_var3.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyUnblindedPriestTransformCast = {
    files = {
      "kra_sfx_enemy_unblindedPriest_transform_cast_v2.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
    interruptible = true
  },
  EnemyUnblindedPriestTransformSpawn = {
    files = {
      "kra_sfx_enemy_unblindedPriest_transform_spawn_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  EnemyAbominationDeath = {
    files = {
      "kra_sfx_enemy_abomination_death_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  }, 
  EnemyAbominationInstakill = {
    files = {
      "kra_sfx_enemy_abomination_instakill_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },     
  EnemySpiderlingDeath = {
    files = {
      "kra_sfx_enemy_spiderling_death_var1_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },     
  EnemyShacklerDeath = {
    files = {
      "kra_sfx_enemy_shackler_death_var-003.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },     
  EnemyShacklerBlockTowerBlock = {
    files = {
      "kra_sfx_enemy_shackler_blockTower_block_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  EnemyShacklerBlockTowerUnblock = {
    files = {
      "kra_sfx_enemy_shackler_blockTower_unblock_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  EnemyBoundNightmareDeath = {
    files = {
      "kra_sfx_enemy_boundNightmare_death_op2_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },    
  EnemyCorruptedStalkerDeath = {
    files = {
      "kra_sfx_enemy_corruptedStalker_death_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },    
  EnemyCrystalGolemDeath = {
    files = {
      "kra_sfx_enemy_stoneGolem_death_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },    
  EnemyGlarelingDeath = {
    files = {
      "kra_sfx_enemy_voidBlinker_death_var1_v1.ogg",
      "kra_sfx_enemy_voidBlinker_death_var3_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX",
    delay = 0.6
  },

  --KR 5 ENEMIES TERRAIN 3
  EnemyVoidBlinkerStareCast = {
    files = {
      "kra_sfx_enemy_voidBlinker_stare_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  EnemyMindlessHuskDeath = {
    files = {
      "kra_sfx_enemy_mindlessHusk_death_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX",
    delay = 0.4
  },
  EnemyMindlessHuskSpawnDeath = {
    files = {
      "kra_sfx_enemy_mindlessHusk_deathSpawn_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX",
    delay = 0.4
  },
  EnemyVileSpawnerDeath = {
    files = {
      "kra_sfx_enemy_vileSpawner_death_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX",
    delay = 0.2
  },
  EnemyVileSpawnerSpawnCast = {
    files = {
      "kra_sfx_enemy_vileSpawner_spawn_cast_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX",
    delay = 0
  },
  EnemyLesserEyeDeath = {
    files = {
      "kra_sfx_enemy_voidBlinker_death_var1_v1.ogg",
      "kra_sfx_enemy_voidBlinker_death_var3_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX",
    delay = 0.25
  },
  EnemyNoxiousHorrorDeath = {
    files = {
      "kra_sfx_enemy_noxiousHorror_death_op1_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX",
    delay = 0.4
  },
  EnemyNoxiousHorrorBasicAttackCast = {
    files = {
      "kra_sfx_enemy_noxiousHorror_basicAttack_cast_op2_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    source_group = "SFX",
    delay = 0
  },
  EnemyNoxiousHorrorBasicAttackImpact = {
    files = {
      "kra_sfx_enemy_noxiousHorror_basicAttack_impact_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    source_group = "SFX",
    delay = 0
  },
  EnemyHardenedHorrorDeath = {
    files = {
      "kra_sfx_enemy_hardenedHorror_death_op2_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX",
    delay = 0.1
  },
  EnemyEvolvingScourgeDeath = {
    files = {
      "kra_sfx_enemy_evolvingScourge_death_var2_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX",
    delay = 0.1
  },
  EnemyEvolvingScourgeEvolve = {
    files = {
      "kra_sfx_enemy_evolvingScourge_evolve_op1_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },    
  EnemyAmalgamDeath = {
    files = {
      "kra_sfx_enemy_fleshBehemoth_death_op1_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX",
    delay = 0.1
  },
  EnemySheepDeath = {
    files = {
      "kra_sfx_enemy_sheep_death_var1_v1.ogg",
      "kra_sfx_enemy_sheep_death_var2_v1.ogg",
      "kra_sfx_enemy_sheep_death_var3_v1.ogg",
    },
    gain = 1,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  --KR5 Update 1 Enemies
  EnemyCorruptedElfSpawn = {
    files = {
      "kra_sfx_enemy_corruptedRanger_spawn_var1_v1.ogg",
      "kra_sfx_enemy_corruptedRanger_spawn_var2_v1.ogg",
      "kra_sfx_enemy_corruptedRanger_spawn_var3_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    ignore = 1,
    mode = "random",
    source_group = "SFX"
  },
  EnemyCorruptedElfDeath = {
    files = {
      "kra_sfx_enemy_corruptedRanger_death_var1_v2.ogg",
      "kra_sfx_enemy_corruptedRanger_death_var2_v2.ogg",
      "kra_sfx_enemy_corruptedRanger_death_var3_v2.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemySpecterRushAnticipation = {
    files = {
      "kra_sfx_enemy_specter_interact_cast_var1_v2.ogg",
      "kra_sfx_enemy_specter_interact_cast_var2_v2.ogg",
      "kra_sfx_enemy_specter_interact_cast_var3_v2.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  EnemySpecterRush = {
    files = {
      "kra_sfx_enemy_specter_interact_cast_op2_var-003.ogg",
      "kra_sfx_enemy_specter_interact_cast_op2_var-004.ogg",
      "kra_sfx_enemy_specter_interact_cast_var-005.ogg",
    },
    gain = 0.6,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  EnemySpecterCorruption = {
    files = {
      "kra_sfx_stage17_weirdwood_specterImpact_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX",
  },
  EnemySpecterDeath = {
    files = {
      "kra_sfx_enemy_specter_death_var1_v2.ogg",
      "kra_sfx_enemy_specter_death_var2_v2.ogg",
      "kra_sfx_enemy_specter_death_var3_v2.ogg",
      "kra_sfx_enemy_specter_death_var4_v2.ogg",
      "kra_sfx_enemy_specter_death_var5_v2.ogg",
      "kra_sfx_enemy_specter_death_var6_v2.ogg",
    },
    gain = 0.8,
    loop = false,
    ignore = 0.5,
    mode = "random",
    source_group = "SFX"
  },
  EnemyDustCryptidDeath = {
    files = {
      "kra_sfx_enemy_dustCryptid_death_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  EnemyBaneWolfDeath = {
    files = {
      "kra_sfx_enemy_baneWolf_death_var1_v1.ogg",
      "kra_sfx_enemy_baneWolf_death_var2_v1.ogg",
      "kra_sfx_enemy_baneWolf_death_var3_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyDeathwoodRangedAttackCast = {
    files = {
      "kra_sfx_enemies_deathwood_rangedAttack_cast_var1_v1.ogg",
      "kra_sfx_enemies_deathwood_rangedAttack_cast_var2_v1.ogg",
      "kra_sfx_enemies_deathwood_rangedAttack_cast_var3_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyDeathwoodRangedAttackImpact = {
    files = {
      "kra_sfx_enemies_deathwood_rangedAttack_impact_var1_v1.ogg",
      "kra_sfx_enemies_deathwood_rangedAttack_impact_var2_v1.ogg",
      "kra_sfx_enemies_deathwood_rangedAttack_impact_var3_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyDeathwoodDeath = {
    files = {
      "kra_sfx_enemy_deathwood_death_op1_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  EnemyAnimatedArmorDeath = {
    files = {
      "kra_sfx_enemy_animatedArmor_death_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  EnemyAnimatedArmorRevive = {
    files = {
      "kra_sfx_enemy_animatedArmor_revive_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  EnemyRevenantSoulcallerAttackCast = {
    files = {
      "kra_sfx_enemy_soulcaller_attack_cast_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  EnemyRevenantSoulcallerBlockTowerIn = {
    files = {
      "kra_sfx_enemy_soulcaller_towerBlock_in_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  EnemyRevenantSoulcallerBlockTowerOut = {
    files = {
      "kra_sfx_enemy_soulcaller_towerBlock_out_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  EnemyRevenantSoulcallerDeath = {
    files = {
      "kra_sfx_enemy_soulcaller_death_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  EnemyRevenantHarvesterClone = {
    files = {
      "kra_sfx_enemy_harvester_duplicate_v1.ogg",
    },
    gain = 0.7,
    delay = 0.2,
    loop = false,
    source_group = "SFX"
  },
  EnemyRevenantHarvesterDeath = {
    files = {
      "kra_sfx_enemy_harvester_death_var1_v1.ogg",
      "kra_sfx_enemy_harvester_death_var2_v1.ogg",
      "kra_sfx_enemy_harvester_death_var3_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyPumpkinDeath = {
    files = {
      "kra_sfx_heroes_stregi_veggiefy_death_var1_v1.ogg",
      "kra_sfx_heroes_stregi_veggiefy_death_var2_v1.ogg",
      "kra_sfx_heroes_stregi_veggiefy_death_var3_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  --KR5 DLC 1 Enemies
  EnemyDarksteelHammererDeath = {
    files = {
      "kra_sfx_enemy_darksteelHammerer_death_var1_v1.ogg",
      "kra_sfx_enemy_darksteelHammerer_death_var2_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyDarksteelShielderDeath = {
    files = {
      "kra_sfx_enemy_darksteelShielder_death_var1_v1.ogg",
      "kra_sfx_enemy_darksteelShielder_death_var2_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyCommonCloneDeath = {
    files = {
      "kra_sfx_enemy_darksteelHammerer_death_var1_v1.ogg",
      "kra_sfx_enemy_darksteelHammerer_death_var2_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyRollingSentryDeath = {
    files = {
      "kra_sfx_enemy_rollingSentry_death_var1_v1.ogg",
      "kra_sfx_enemy_rollingSentry_death_var2_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyRollingSentryAttack = {
    files = {
      "kra_sfx_enemy_rollingSentry_attack_op1_var1_v1.ogg",
      "kra_sfx_enemy_rollingSentry_attack_op1_var2_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    mode = "random",
    ignore = 1,
    source_group = "SFX"
  },
  EnemyScrapSpeedsterDeath = {
    files = {
      "kra_sfx_enemy_scrapSpeedster_death_var1_v1.ogg",
      "kra_sfx_enemy_scrapSpeedster_death_var3_v1.ogg",
    },
    gain = 0.3,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyBruteWelderDeath = {
    files = {
      "kra_sfx_enemy_bruteWelder_death_var1_v1.ogg",
      "kra_sfx_enemy_bruteWelder_death_var2_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyBruteWelderDeathImpact = {
    files = {
      "kra_sfx_enemy_bruteWelder_deathImpact_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  EnemyDarksteelFistDeath = {
    files = {
      "kra_sfx_enemy_darksteelFist_death_var1_v1.ogg",
      "kra_sfx_enemy_darksteelFist_death_var2_v1.ogg",
      "kra_sfx_enemy_darksteelFist_death_var3_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyDarksteelFistStun = {
    files = {
      "kra_sfx_enemy_darksteelFist_stun_op1_v1.ogg",
      "kra_sfx_enemy_darksteelFist_stun_op2_v1.ogg",
    },
    gain = 0.5,
    delay = 0.1,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyMadTinkererDeath = {
    files = {
      "kra_sfx_enemy_madTinkerer_death_var1_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  EnemyMadTinkererRayCast = {
    files = {
      "kra_sfx_enemy_madTinkerer_rayCast_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  EnemyMadTinkererSummon = {
    files = {
      "kra_sfx_enemy_madTinkerer_summon_v1.ogg",
    },
    gain = 0.5,
    delay = 0.5,
    loop = false,
    source_group = "SFX"
  },
  EnemyScrapDroneDeath = {
    files = {
      "kra_sfx_enemy_scrapDrone_death_v1.ogg",
      "kra_sfx_enemy_scrapDrone_death_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyDarksteelAnvilDeath = {
    files = {
      "kra_sfx_enemy_darksteelAnvil_death_var1_v1.ogg",
      "kra_sfx_enemy_darksteelAnvil_death_var3_v1.ogg",
      "kra_sfx_enemy_darksteelAnvil_death_var4_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyDarksteelAnvilBeat = {
    files = {
      "kra_sfx_enemy_darksteelAnvil_beat_SHORT_v2.ogg",
    },
    gain = 0.4,
    delay = 1.2,
    loop = false,
    source_group = "SFX"
  },
  EnemyDarksteelHulkDeath = {
    files = {
      "kra_sfx_enemy_darksteelHulk_death_var2_v1.ogg",
      "kra_sfx_enemy_darksteelHulk_death_var3_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyDarksteelHulkCharge = {
    files = {
      "kra_sfx_enemy_darksteelHulk_charge_op1_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyDarksteelGuardianAttack = {
    files = {
      "kra_sfx_enemy_darksteelGuardian_attack_var1_v1.ogg",
      "kra_sfx_enemy_darksteelGuardian_attack_var2_v1.ogg",
      "kra_sfx_enemy_darksteelGuardian_attack_var3_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EnemyDarksteelGuardianDeath = {
    files = {
      "kra_sfx_enemy_darksteelGuardian_death_oneShot_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  EnemyDarksteelGuardianActivation = {
    files = {
      "kra_sfx_stage23_darksteelGuardian_activation_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  EnemyDarksteelGuardianRock = {
    files = {
      "kra_sfx_stage23_darksteelGuardian_rockBreak_v1.ogg",
    },
    gain = 0.4,
    delay = 0.3,
    loop = false,
    source_group = "SFX"
  },
  EnemyDarksteelEnrage = {
    files = {
      "kra_sfx_enemy_darksteelGuardian_enrage_cast_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  EnemyDarksteelRageAttack = {
    files = {
      "kra_sfx_enemy_darksteelGuardian_enrage_attack_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    delay = 0.25,
    source_group = "SFX"
  },

  --KR5 ENEMIES TERRAIN CROCS
  EnemyCrokinderDeath = {
    files = {
      "kra_sfx_crocs_crokinder_death_var1_v1.ogg",
    },
    mode = "random",
    gain = 0.2,
    loop = false,
    source_group = "SFX"
  },
  EnemyCrokinderEvolve = {
    files = {
      "kra_sfx_crocs_crokinder_transform_var3_v1.ogg",
    },
    mode = "random",
    gain = 0.2,
    loop = false,
    source_group = "SFX"
  },
  EnemyCrocBasicMelee = {
    files = {
      "kra_sfx_crocs_gator_melee_var1_v1.ogg",
      "kra_sfx_crocs_gator_melee_var2_v1.ogg",
      "kra_sfx_crocs_gator_melee_var3_v1.ogg",
    },
    mode = "random",
    gain = 0.2,
    delay = 0.2666,
    loop = false,
    source_group = "SFX"
  },
  EnemyKillertileMelee = {
    files = {
      "kra_sfx_crocs_killertile_bitemelee_v1.ogg",
    },
    mode = "random",
    gain = 0.2,
    loop = false,
    source_group = "SFX"
  },
  EnemyQuickfeetMelee = {
    files = {
      "kra_sfx_crocs_quickfeet_melee_var1_v1.ogg",
      "kra_sfx_crocs_quickfeet_melee_var2_v1.ogg",
      "kra_sfx_crocs_quickfeet_melee_var3_v1.ogg",
    },
    mode = "random",
    gain = 0.2,
    delay = 0.2666,
    loop = false,
    source_group = "SFX"
  },
  EnemyQuickfeetRanged = {
    files = {
      "kra_sfx_crocs_quickfeet_throw_chicken_leg_and_throwup_v1.ogg",
    },
    mode = "random",
    gain = 0.2,
    loop = false,
    source_group = "SFX"
  },
  EnemyCrocsBasicEvolve = {
    files = {
      "kra_sfx_crocs_quickfeet_throw_chicken_leg_eat_and_grow_v1.ogg",
    },
    mode = "random",
    gain = 0.2,
    loop = false,
    source_group = "SFX"
  },
  EnemyCrocsRangedShot = {
    files = {
      "kra_sfx_crocs_lizardshot_range_var1_v1.ogg",
      "kra_sfx_crocs_lizardshot_range_var2_v1.ogg",
      "kra_sfx_crocs_lizardshot_range_var3_v1.ogg",
    },
    mode = "random",
    gain = 0.2,
    loop = false,
    source_group = "SFX"
  },
  EnemyCrocsRangedMelee = {
    files = {
      "kra_sfx_crocs_lizardshot_melee_var1_v1.ogg",
      "kra_sfx_crocs_lizardshot_melee_var2_v1.ogg",
      "kra_sfx_crocs_lizardshot_melee_var3_v1.ogg",
    },
    mode = "random",
    gain = 0.2,
    delay = 0.2666,
    loop = false,
    source_group = "SFX"
  },
  EnemyNestingGatorMelee = {
    files = {
      "kra_sfx_crocs_nesting_gator_melee_var1_v1.ogg",
      "kra_sfx_crocs_nesting_gator_melee_var2_v1.ogg",
      "kra_sfx_crocs_nesting_gator_melee_var3_v1.ogg",
    },
    mode = "random",
    gain = 0.2,
    delay = 0.2666,
    loop = false,
    source_group = "SFX"
  },
  EnemyNestingGatorAbility = {
    files = {
      "kra_sfx_crocs_nesting_gator_spawn_op1_v1.ogg",
    },
    mode = "random",
    gain = 0.2,
    loop = false,
    source_group = "SFX"
  },
  EnemyCrocShamanShot = {
    files = {
      "kra_sfx_crocs_wise_range_cast_var1_v1.ogg",
    },
    mode = "random",
    gain = 0.15,
    loop = false,
    source_group = "SFX"
  },
  EnemyCrocTankSpin = {
    files = {
      "kra_sfx_crocs_tank_spin_op2_v1.ogg",
    },
    mode = "random",
    gain = 0.2,
    delay = 0.3333,
    loop = false,
    source_group = "SFX"
  },

  --KR5 ENEMIES TERRAIN SPIDERS
  EnemyCultbroodDeath = {
    files = {
      "kra_sfx_spiders_enemy_spider_cultbrood_death_var1_v1.ogg",
      "kra_sfx_spiders_enemy_spider_cultbrood_death_var2_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyCultbroodMelee = {
    files = {
      "kra_sfx_spiders_enemy_spider_cultbrood_melee_var1_v1.ogg",
      "kra_sfx_spiders_enemy_spider_cultbrood_melee_var2_v1.ogg",
      "kra_sfx_spiders_enemy_spider_cultbrood_melee_var3_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    delay = 0.466,
    loop = false,
    source_group = "SFX"
  },
  EnemyDrainbroodMelee = {
    files = {
      "kra_sfx_spiders_enemy_spider_drainbrood_melee_var1_v1ogg",
      "kra_sfx_spiders_enemy_spider_drainbrood_melee_var2_v1.ogg",
      "kra_sfx_spiders_enemy_spider_drainbrood_melee_var3_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    delay = 0.33,
    loop = false,
    source_group = "SFX"
  },
  EnemyGlarenwardenDeath = {
    files = {
      "kra_sfx_spiders_enemy_spider_glarenwarden_death_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    delay = 0.3,
    loop = false,
    source_group = "SFX"
  },
  EnemyGlarenwardenMelee = {
    files = {
      "kra_sfx_spiders_enemy_spider_glarenwarden_melee_var1_v1.ogg",
      "kra_sfx_spiders_enemy_spider_glarenwarden_melee_var2_v1.ogg",
      "kra_sfx_spiders_enemy_spider_glarenwarden_melee_var3_v1.ogg",
    },
    mode = "random",
    gain = 0.3,
    delay = 0.5,
    loop = false,
    source_group = "SFX"
  },
  EnemySpiderPriestTransform = {
    files = {
      "kra_sfx_spiders_enemy_spider_priest_transform_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    delay = 0.9,
    loop = false,
    source_group = "SFX"
  },
  EnemySpiderSisterRange = {
    files = {
      "kra_sfx_spiders_enemy_spider_sister_range_var1_v1.ogg",
      "kra_sfx_spiders_enemy_spider_sister_range_var2_v1.ogg",
      "kra_sfx_spiders_enemy_spider_sister_range_var3_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemySpiderSisterSpawn = {
    files = {
      "kra_sfx_spiders_enemy_spider_sister_spawn_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    delay = 0.3,
    loop = false,
    source_group = "SFX"
  },
  EnemySpidersMechanicSpawnerInflate = {
    files = {
      "kra_sfx_spiders_mechanic_spawner_prev_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemySpidersMechanicSpawnerExplode = {
    files = {
      "kra_sfx_spiders_mechanic_spawner_explode_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    delay = 4,
    loop = false,
    source_group = "SFX"
  },
  EnemySpidersMechanicSpawnerRegenerate = {
    files = {
      "kra_sfx_spiders_mechanic_spawner_regenerate_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemySpidersMechanicTowerSpiderDeath = {
    files = {
      "kra_sfx_spiders_mechanic_spider_death_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemySpidersMechanicTowerSpiderWorkingLoop = {
    files = {
      "kra_sfx_spiders_mechanic_spider_working_LOOP_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = true,
    source_group = "SFX"
  },

  --KR5 ENEMIES TERRAIN WUKONG
  EnemyAshSpiritDeath = {
    files = {
      "kra_sfx_wukong_enemy_ash_spirit_death_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyAshSpiritMelee = {
    files = {
      "kra_sfx_wukong_enemy_ash_spirit_melee_var1.ogg",
      "kra_sfx_wukong_enemy_ash_spirit_melee_var2.ogg",
      "kra_sfx_wukong_enemy_ash_spirit_melee_var3.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyBlazeRaiderMeleeSpecial = {
    files = {
      "kra_sfx_wukong_enemy_blaze_raider_special_v1.ogg",
    },
    mode = "random",
    gain = 0.25,
    delay = 0.15,
    loop = false,
    source_group = "SFX"
  },
  EnemyBurningTreantDeath = {
    files = {
      "kra_sfx_wukong_enemy_burning_treant_death_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyBurningTreantSpecial = {
    files = {
      "kra_sfx_wukong_enemy_burning_treant_special_v1.ogg",
    },
    mode = "random",
    gain = 0.25,
    loop = false,
    source_group = "SFX"
  },
  EnemyFireFoxDeath = {
    files = {
      "kra_sfx_wukong_enemy_fire_fox_death_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyFireFoxMelee = {
    files = {
      "kra_sfx_wukong_enemy_fire_fox_melee_var1_v1.ogg",
      "kra_sfx_wukong_enemy_fire_fox_melee_var2_v1.ogg",
      "kra_sfx_wukong_enemy_fire_fox_melee_var3_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    delay = 0.1,
    loop = false,
    source_group = "SFX"
  },
  EnemyFirePhoenixDeath = {
    files = {
      "kra_sfx_wukong_enemy_fire_phoenix_death_wScreech_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    delay = 0.1,
    loop = false,
    source_group = "SFX"
  },
  EnemyFlameGuardMeleeSpecial = {
    files = {
      "kra_sfx_wukong_enemy_flame_guard_special_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    dealy = 0.1,
    loop = false,
    source_group = "SFX"
  },
  EnemyNineTailedFoxDeath = {
    files = {
      "kra_sfx_wukong_enemy_nine_tailed_fox_death_wWhimper_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyNineTailedFoxMeleeDouble = {
    files = {
      "kra_sfx_wukong_enemy_nine_tailed_fox_melee_double_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    delay = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyNineTailedFoxMelee = {
    files = {
      "kra_sfx_wukong_enemy_nine_tailed_fox_melee_var1_v1.ogg",
      "kra_sfx_wukong_enemy_nine_tailed_fox_melee_var2_v1.ogg",
      "kra_sfx_wukong_enemy_nine_tailed_fox_melee_var3_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    delay = 0.05,
    loop = false,
    source_group = "SFX"
  },
  EnemyNineTailedFoxTeleportIn = {
    files = {
      "kra_sfx_wukong_enemy_nine_tailed_fox_teleport_IN_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyNineTailedFoxTeleportOut = {
    files = {
      "kra_sfx_wukong_enemy_nine_tailed_fox_teleport_OUT_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyWuxianDeath = {
    files = {
      "kra_sfx_wukong_enemy_wuxian_death_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyWuxianRanged = {
    files = {
      "kra_sfx_wukong_enemy_wuxian_ranged_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyWuxianSpecial = {
    files = {
      "kra_sfx_wukong_enemy_wuxian_special_woVoice_v1.ogg",
      "kra_sfx_wukong_enemy_wuxian_special_wVoice_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyStormSpiritLeap = {
    files = {
      "kra_sfx_wukong_enemy_drakeling_leap_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyStormSpiritDeath = {
    files = {
      "kra_sfx_wukong_enemy_drakeling_death_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyQiongqiRanged = {
    files = {
      "kra_sfx_wukong_enemy_qiongqi_ranged_op1_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyQiongqiDeath = {
    files = {
      "kra_sfx_wukong_enemy_qiongqi_death_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyWaterSorceressSpecial = {
    files = {
      "kra_sfx_wukong_enemy_wmaster_special_v1.ogg",
    },
    mode = "random",
    gain = 0.2,
    loop = false,
    delay = 0.5,
    source_group = "SFX"
  },



  EnemyElementalMelee = {
    files = {
      "kra_sfx_wukong_enemy_elemental_melee_var1_v1.ogg",
      "kra_sfx_wukong_enemy_elemental_melee_var2_v1.ogg",
      "kra_sfx_wukong_enemy_elemental_melee_var3_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    delay = 0.7,
    source_group = "SFX"
  },
  EnemyElementalRangedImpact = {
    files = {
      "kra_sfx_wukong_enemy_elemental_ranged_impact_var1_v1.ogg",
      "kra_sfx_wukong_enemy_elemental_ranged_impact_var2_v1.ogg",
      "kra_sfx_wukong_enemy_elemental_ranged_impact_var3_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyElementalDeathEffectCast = {
    files = {
      "kra_sfx_wukong_enemy_elemental_death_effect_cast_op1_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    delay = 1.0,
    source_group = "SFX"
  },
  EnemyElementalDeathEffectStun = {
    files = {
      "kra_sfx_wukong_enemy_elemental_death_effect_stun_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyElementalDeath = {
    files = {
      "kra_sfx_wukong_enemy_elemental_death_normal_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    delay = 1.0,
    source_group = "SFX"
  },

  EnemyFanGuardDeath = {
    files = {
      "kra_sfx_wukong_enemy_fan_guard_death_op1_v1.ogg",
      "kra_sfx_wukong_enemy_fan_guard_death_op2_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyFanGuardSpecial = {
    files = {
      "kra_sfx_wukong_enemy_fan_guard_special_v1.ogg"
    },
    mode = "random",
    gain = 0.4,
    delay = 0.7,
    loop = false,
    source_group = "SFX"
  },

  EnemyBossPrincessClone = {
    files = {
      "kra_sfx_wukong_princess_clone_v1.ogg"
    },
    mode = "random",
    gain = 0.7,
    loop = false,
    source_group = "SPECIALS"
  },
  EnemyBossPrincessDeath = {
    files = {
      "kra_sfx_wukong_princess_death_v1.ogg"
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyBossPrincessHeroStunChannel = {
    files = {
      "kra_sfx_wukong_princess_hero_stun_channel_v2.ogg"
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SPECIALS"
  },
  EnemyBossPrincessHeroStunFail = {
    files = {
      "kra_sfx_wukong_princess_hero_stun_fail_woVoice_v1.ogg",
      "kra_sfx_wukong_princess_hero_stun_fail_wVoice_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyBossPrincessHeroStunSuccess = {
    files = {
      "kra_sfx_wukong_princess_hero_stun_success_v2.ogg"
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SPECIALS"
  },
  EnemyBossPrincessMeleeArea = {
    files = {
      "kra_sfx_wukong_princess_melee_area_v1.ogg"
    },
    mode = "random",
    gain = 0.4,
    delay = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyBossPrincessMelee = {
    files = {
      "kra_sfx_wukong_princess_melee_var1_v1.ogg",
      "kra_sfx_wukong_princess_melee_var2_v1.ogg",
      "kra_sfx_wukong_princess_melee_var3_v1.ogg"
    },
    mode = "random",
    gain = 0.4,
    delay = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyBossPrincessMudPoolTransformation = {
    files = {
      "kra_sfx_wukong_princess_mud_pool_transformation_v1.ogg"
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyBossPrincessMudPoolSummon = {
    files = {
      "kra_sfx_wukong_princess_mud_summon_var1_v1.ogg",
      "kra_sfx_wukong_princess_mud_summon_var2_v1.ogg",
      "kra_sfx_wukong_princess_mud_summon_var3_v1.ogg",
      "kra_sfx_wukong_princess_mud_summon_var4_v1.ogg",
      "kra_sfx_wukong_princess_mud_summon_var5_v1.ogg"
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyBossPrincessMudTower = {
    files = {
      "kra_sfx_wukong_princess_mud_tower_op1_v2.ogg",
      "kra_sfx_wukong_princess_mud_tower_op2_v2.ogg",
    },
    mode = "random",
    delay = 0.0,
    gain = 0.8,
    loop = false,
    source_group = "SPECIALS"
  },
  EnemyBossPrincessRangedCast = {
    files = {
      "kra_sfx_wukong_princess_ranged_cast_v1.ogg"
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyBossPrincessRangedImpact = {
    files = {
      "kra_sfx_wukong_princess_ranged_impact_var1_v1.ogg",
      "kra_sfx_wukong_princess_ranged_impact_var2_v1.ogg",
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyBossPrincessTeleportIn = {
    files = {
      "kra_sfx_wukong_princess_teleport-IN_v1.ogg"
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  EnemyBossPrincessTeleportOut = {
    files = {
      "kra_sfx_wukong_princess_teleport-OUT_v1.ogg"
    },
    mode = "random",
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },

  --KR5 TERRAIN 1 COMMON AMBIENCE
  Terrain1AmbienceSoundBirds = {
    files = {
      "kra_sfx_terrain1Ambient_birds_var1_v1.ogg",
      "kra_sfx_terrain1Ambient_birds_var2_v1.ogg",
      "kra_sfx_terrain1Ambient_birds_var3_v1.ogg",
      "kra_sfx_terrain1Ambient_birds_var4_v1.ogg",
      "kra_sfx_terrain1Ambient_birds_var5_v1.ogg",
      "kra_sfx_terrain1Ambient_birds_var6_v1.ogg",
    },
    gain = 1,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  Terrain1AmbienceSoundWind = {
    files = {
      "kra_sfx_terrain1Ambient_wind_var1_v1.ogg",
      "kra_sfx_terrain1Ambient_wind_var2_v1.ogg",
      "kra_sfx_terrain1Ambient_wind_var3_v1.ogg",
    },
    gain = 1,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  --KR5 TERRAIN 1 COMMON INTERACTIONS
  Terrain1CommonArboreanTapIn = {
    files = {
      "kra_sfx_easterEgg_arboreanTap_in_var1_v2.ogg",
      "kra_sfx_easterEgg_arboreanTap_in_var2_v2.ogg",
    },
    gain = 0.9,
    delay = 0.4,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  Terrain1CommonArboreanTapOut = {
    files = {
      "kra_sfx_easterEgg_arboreanTap_out_fullSeq_op1_v2.ogg",
      "kra_sfx_easterEgg_arboreanTap_out_fullSeq_op2_v2.ogg",
    },
    gain = 0.9,
    delay = 0.8,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  EasterEggCommonTap = {
    files = {
      "kra_sfx_easterEgg_interactionTap.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  
  --KR5 TERRAIN 2 COMMON AMBIENCE
  Terrain2AmbienceSoundBats = {
    files = {
      "kra_sfx_terrain2Ambient_bats_var1_v1.ogg",
      "kra_sfx_terrain2Ambient_bats_var2_v1.ogg",
      "kra_sfx_terrain2Ambient_bats_var3_v1.ogg",
    },
    gain = 1,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  Terrain2AmbienceSoundWind = {
    files = {
      "kra_sfx_terrain2Ambient_wind_var1_v1.ogg",
      "kra_sfx_terrain2Ambient_wind_var2_v1.ogg",
      "kra_sfx_terrain2Ambient_wind_var3_v1.ogg",
    },
    gain = 1,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  Terrain2AmbienceSoundWaterDrop= {
    files = {
      "kra_sfx_terrain2Ambient_waterDrops_var1_v1.ogg",
      "kra_sfx_terrain2Ambient_waterDrops_var2_v1.ogg",
      "kra_sfx_terrain2Ambient_waterDrops_var3_v1.ogg",
    },
    gain = 1,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },

  --KR5 TERRAIN 3 COMMON AMBIENCE
  Terrain3AmbienceSoundGutural= {
    files = {
      "kra_sfx_terrain3Ambient_gutural_var2_v1.ogg",
      "kra_sfx_terrain3Ambient_gutural_var3_v1.ogg",
      "kra_sfx_terrain3Ambient_gutural_var5_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },

  --KR5 TERRAIN 4 COMMON AMBIENCE
  Terrain4AmbienceSoundWind= {
    files = {
      "kra_sfx_update1Ambient_wind_var1.ogg",
      "kra_sfx_update1Ambient_wind_var2.ogg",
      "kra_sfx_update1Ambient_wind_var3.ogg",
      "kra_sfx_update1Ambient_wind_var4.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },

--KR5 TERRAIN 6 COMMON AMBIENCE
Terrain6AmbienceSoundWindRocks= {
  files = {
    "kra_sfx_dlc1Ambient_windRocks_var1_v1.ogg",
    "kra_sfx_dlc1Ambient_windRocks_var2_v1.ogg",
    "kra_sfx_dlc1Ambient_windRocks_var3_v1.ogg",
  },
  gain = 0.5,
  loop = false,
  mode = "random",
  source_group = "SFX"
},

Terrain6AmbienceSoundForge= {
  files = {
    "kra_sfx_dlc1Ambient_forge_var1_v1.ogg",
    "kra_sfx_dlc1Ambient_forge_var2_v1.ogg",
    "kra_sfx_dlc1Ambient_forge_var3_v1.ogg",
  },
  gain = 0.7,
  loop = false,
  mode = "random",
  source_group = "SFX"
},

  --KR5 STAGES
  Stage01ArboreanSageAppear = {
    files = {
      "kra_sfx_stageTutorial_arboreanSage_appear_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  Stage01ArboreanSageDisappear = {
    files = {
      "kra_sfx_stageTutorial_arboreanSage_disappear_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX"
  },
  Stage01ArboreanSageCast = {
    files = {
      "kra_sfx_stageTutorial_arboreanSage_cast_op2_v1.ogg",
    },
    gain = 0.5,
    delay = 0.3,
    loop = false,
    source_group = "SFX"
  },
  Stage01ArboreanSageShrubDisappear = {
    files = {
      "kra_sfx_stageTutorial_arboreanSage_shrubDisappear_var1_v1.ogg",
      "kra_sfx_stageTutorial_arboreanSage_shrubDisappear_var2_v1.ogg",
      "kra_sfx_stageTutorial_arboreanSage_shrubDisappear_var3_v1.ogg",
      "kra_sfx_stageTutorial_arboreanSage_shrubDisappear_var4_v1.ogg",
    },
    gain = 0.6,
    delay = 0.3,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  Stage01Rune = {
    files = {
      "kra_sfx_easterEgg_runeStage1_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  Stage01FireOff = {
    files = {
      "kra_sfx_easterEgg_campfire_off_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  Stage01FireOn = {
    files = {
      "kra_sfx_easterEgg_campfire_on_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  Stage01FireFinal = {
    files = {
      "kra_sfx_easterEgg_camperFire_tap3_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  Stage01RobinHood = {
    files = {
      "kra_sfx_easterEgg_robinHood_v1.ogg",
    },
    gain = 0.7,
    delay = 0.3,
    loop = false,
    source_group = "SFX"
  },
  Stage02LinkFishing = {
    files = {
      "kra_sfx_easterEgg_linkFishing_var3_v1.ogg",
      "kra_sfx_easterEgg_linkFishing_var2_v1.ogg",
      "kra_sfx_easterEgg_linkFishing_var1_v1.ogg",
    },
    gain = 1,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  Stage02GuardianTreePreCast = {
    files = {
      "kra_sfx_stageMechanic_guardianTree_pre-cast_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  Stage02LionKing = {
    files = {
      "kra_sfx_easterEgg_lionKing_op1_v1.ogg",
    },
    gain = 1,
    delay = 0.85,
    loop = false,
    source_group = "SFX"
  },
  Stage02GuardianTreeCast = {
    files = {
      "kra_sfx_stageMechanic_guardianTree_cast_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  Stage02GuardianTreeRoots = {
    files = {
      "kra_sfx_stageMechanic_guardianTree_roots_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  Stage0203Rune = {
    files = {
      "kra_sfx_easterEgg_runeStage2-3_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  Stage02RaelynTeleport = {
    files = {
      "kra_sfx_stage02_cinematic_raelyn_teleport_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  Stage02VeznanTeleport = {
    files = {
      "kra_sfx_stage02_cinematic_veznan_teleport_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  Stage03HeartOfTheForestReady = {
    files = {
      "kra_sfx_stageMechanic_heartOfTheForest_ready_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage03HeartOfTheForestCast = {
    files = {
      "kra_sfx_stageMechanic_heartOfTheForest_cast_v1.ogg",
    },
    gain = 1,
    loop = false,
    delay = 0.7,
    source_group = "SFX"
  },
  Stage03HeartOfTheForestBlast = {
    files = {
      "kra_sfx_stageMechanic_heartOfTheForest_energyBlasts_var1_v1.ogg",
      "kra_sfx_stageMechanic_heartOfTheForest_energyBlasts_var2_v1.ogg",
      "kra_sfx_stageMechanic_heartOfTheForest_energyBlasts_var3_v1.ogg",
      "kra_sfx_stageMechanic_heartOfTheForest_energyBlasts_var4_v1.ogg",
    },
    gain = 1,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  Stage04ArboreanThornspears = {
    files = {
      "kr_voice_arboreanthornspears_taunt01_a.ogg",
      "kr_voice_arboreanthornspears_taunt02_b.ogg",
    },
    gain = 0.7,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  Stage04ElevatorIn = {
    files = {
      "kra_sfx_stage04_wildbeastElevator_inFull_op1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage04ElevatorOut = {
    files = {
      "kra_sfx_stage04_wildbeastElevator_out_v3.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  Stage04ElevatorBreak = {
    files = {
      "kra_sfx_stageMechanic_wildbeastElevator_break_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  Stage04Rune = {
    files = {
      "kra_sfx_easterEgg_runeStage4_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  Stage04ArboreanFall = {
    files = {
      "kra_sfx_easterEgg_arboreanFall_op2_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage04SheepyFall = {
    files = {
      "kra_sfx_easterEgg_sheepyTerrain1_fall_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  Stage04SheepyImpact = {
    files = {
      "kra_sfx_easterEgg_sheepyTerrain1_impact_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
    delay = 0.75
  },
  Stage05WoodcutterBearRoar = {
    files = {
      "kra_sfx_stageMechanic_woodcutterBear_roar_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage05WoodcutterBearChop = {
    files = {
      "kra_sfx_stageMechanic_woodcutterBear_chop_2_v1.ogg",
      "kra_sfx_stageMechanic_woodcutterBear_chop_1_v1.ogg",
    },
    gain = 1,
    delay = 0.2,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  Stage0506Rune = {
    files = {
      "kra_sfx_easterEgg_runeStage5-6_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  Stage06BossPigSnore = {
    files = {
      "kra_sfx_stage06_bossFight_cinematic_goregrindSnore_v1.ogg",
    },
    gain = 1,
    delay = 0,
    loop = false,
    source_group = "SFX"
  },
  Stage06BossPigWakeUp = {
    files = {
      "kra_sfx_stage06_bossFight_cinematic_goregrindWakeUp_v1.ogg",
    },
    gain = 1,
    delay = 0,
    loop = false,
    source_group = "SFX"
  },
  Stage06BossPigAttack = {
    files = {
      "kra_sfx_boss_goregrind_attack_var3_v1.ogg",
      "kra_sfx_boss_goregrind_attack_var2_v1.ogg",
      "kra_sfx_boss_goregrind_attack_var1_v1.ogg",
    },
    gain = 0.8,
    delay = 0.4,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  Stage06BossPigJumpCinematic = {
    files = {
      "kra_sfx_stage06_bossFight_Cinematic_goregrindJump.ogg",
    },
    gain = 1,
    delay = 0,
    loop = false,
    source_group = "SFX"
  },
  Stage06BossPigJump = {
    files = {
      "kra_sfx_boss_goregrind_jumpCast.ogg",
    },
    gain = 1,
    delay = 1.2,
    loop = false,
    source_group = "SFX"
  },
  Stage06BossPigLand = {
    files = {
      "kra_sfx_boss_goregrind_jumpImpact_land_v1.ogg",
    },
    gain = 1,
    delay = 0.1,
    loop = false,
    source_group = "SFX"
  },
  Stage06BossPigFalling = {
    files = {
      "kra_sfx_boss_goregrind_jumpImpact_falling_v1.ogg",
    },
    gain = 1,
    delay = 0.1,
    loop = false,
    source_group = "SFX"
  },
  Stage06BossPigDeath = {
    files = {
      "kra_sfx_boss_goregrind_death_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage06BossPigHorn = {
    files = {
      "kra_sfx_boss_goregrind_horn_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage06EasterEggMinecraftClick = {
    files = {
      "kra_sfx_easterEgg_minecraftPig_var3_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  Stage06EasterEggMinecraftDeath = {
    files = {
      "kra_sfx_easterEgg_minecraftPig_death_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  Stage06BurrowOpen = {
    files = {
      "kra_sfx_stageMechanic_burrowOpen_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage06WoodenDoorOpen = {
    files = {
      "kra_sfx_stageMechanic_woodenDoorOpen_v1.ogg",
    },
    gain = 1,
    loop = true,
    source_group = "SFX"
  },
  Stage06WoodenDoorClose = {
    files = {
      "kra_sfx_stageMechanic_woodenDoorForcedClose_v1.ogg",
    },
    gain = 1,
    delay = 0.25,
    loop = false,
    source_group = "SFX"
  },
  Stage06AcolyteTeleport = {
    files = {
      "kra_sfx_cinematicStage06_acolyteTeleport_v1.ogg",
    },
    gain = 1,
    -- delay = 0.5,
    loop = false,
    source_group = "SFX"
  },
  Stage07CultTemple = {
    files = {
      "kra_sfx_stage07_cultTempleBridge_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage07Witcher = {
    files = {
      "kra_sfx_easterEgg_witcher_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  Stage07CrowCaw = {
    files = {
      "kra_sfx_easterEgg_crow_caw_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  Stage07CrowFly = {
    files = {
      "kra_sfx_easterEgg_crow_fly_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  Stage08RescuedElves = {
    files = {
      "kr_voice_rescuedelves_taunt01_b.ogg",
      "kr_voice_rescuedelves_taunt02_c.ogg",
    },
    gain = 1,
    ignore = 1,
    loop = false,
    mode = "sequence",
    source_group = "TAUNTS"
  },
  Stage08BasketTap = {
    files = {
      "kra_sfx_easterEgg_basket_tap_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage08BasketBreak = {
    files = {
      "kra_sfx_easterEgg_basket_break_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  Stage09CultBridge = {
    files = {
      "kra_sfx_stage09_cultBridge_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage09CultBridgeRumble = {
    files = {
      "kra_stage09_cultBridge_rumble_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage09NightmarePortalCandles = {
    files = {
      "kra_sfx_stage09_nightmarePortalOn-candles_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage09NightmarePortalEye = {
    files = {
      "kra_sfx_stage09_nightmarePortalOn-eye_v1.ogg",
    },
    gain = 1,
    delay = 0.2,
    loop = false,
    source_group = "SFX"
  },
  Stage09DryBonesBreak = {
    files = {
      "kra_sfx_easterEgg_dryBones_break_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  Stage09DryBonesReform = {
    files = {
      "kra_sfx_easterEgg_dryBones_reform_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  Stage09SheepyCamera = {
    files = {
      "kra_sfx_easterEgg_sheepyTerrain2_camera_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
    delay = 0.7
  },
  Stage09SheepyBridge = {
    files = {
      "kra_sfx_easterEgg_sheepyTerrain2_bridgeBaa_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage10ObeliskActivation = {
    files = {
      "kra_sfx_stage10_obelisk_activation_op1_v3.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage10ObeliskEffectChange = {
    files = {
      "kra_sfx_stage10_obelisk_effectChange_v2.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage10ObeliskEffectStun = {
    files = {
      "kra_sfx_stage10_obelisk_effectStun_cast_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage10ObeliskEffectHealLoopStart = {
    files = {
      "kra_sfx_stage10_obelisk_effectHeal_cast_loopStart.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage10ObeliskEffectHealLoop = {
    files = {
      "kra_sfx_stage10_obelisk_effectHeal_cast_LOOP_v1.ogg",
    },
    gain = 1,
    loop = true,
    source_group = "SFX"
  },
  Stage10ObeliskEffectTeleport = {
    files = {
      --"kra_sfx_stage10_obelisk_effectHeal_cast_LOOP_v1.ogg", Acá va el teleport del void blinker.
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  Stage10ObeliskEffectGolemSpawnCast = {
    files = {
      "kra_sfx_stage10_obelisk_golemSpawn_cast.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  Stage10ObeliskEffectGolemSpawnGolem = {
    files = {
      "kra_sfx_stage10_obelisk_golemSpawn_golem.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  Stage10VillagePeopleStatuePuff = {
    files = {
      "kra_sfx_easterEgg_villagePeople_statuePuff_op1_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  Stage10VillagePeopleFireworks = {
    files = {
      "kra_sfx_easterEgg_villagePeople_fireworks_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  Stage10VillagePeopleSong = {
    files = {
      "kra_sfx_easterEgg_villagePeople_hits_op2_v2.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage11AmbienceThunder = {
    files = {
      "kra_sfx_stage11_ambienceThunder_var1_v1.ogg",
      "kra_sfx_stage11_ambienceThunder_var2_v1.ogg",
      "kra_sfx_stage11_ambienceThunder_var3_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  Stage11PortalOpen = {
    files = {
      "kra_sfx_stage11_bossFight_portalOpen_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage11PortalClose = {
    files = {
      "kra_sfx_stage11_bossFight_portalClose_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage11MydriasIllusionSummonCast = {
    files = {
      "kra_sfx_stage11_bossFight_mydriasIllusionSummon_cast_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage11MydriasIllusionShieldCast = {
    files = {
      "kra_sfx_stage11_bossFight_mydriasIllusionShield_cast_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage11MydriasIllusionTendrilsCast = {
    files = {
      "kra_sfx_stage11_bossFight_mydriasIllusionTendrils_cast_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage11MydriasIllusionTendrilsDeath = {
    files = {
      "kra_sfx_stage11_bossFight_mydriasIllusionTendrils_death_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage11MydriasIllusionDeath = {
    files = {
      "kra_sfx_stage11_bossFight_mydriasIllusion_death_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage11MidCinematicChainBreak = {
    files = {
      "kra_sfx_stage11_bossFight_midCinematic_chainBreak_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  Stage11MidCinematicPlatformMove = {
    files = {
      "kra_sfx_stage11_bossFight_midCinematic_platformMove_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  Stage11MidCinematicDenasJump = {
    files = {
      "kra_sfx_stage11_bossFight_midCinematic_denasJump_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  Stage11MidCinematicDenasJumpLand = {
    files = {
      "kra_sfx_stage11_bossFight_midCinematic_denasJumpLand_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  Stage11MidCinematicVeznanTeleport = {
    files = {
      "kra_sfx_stage11_bossFight_midCinematic_veznanTeleport_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage11VeznanReady = {
    files = {
      "kra_sfx_stage11_bossFight_veznanReady_op2_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  Stage11VeznanSoulImpactCast = {
    files = {
      "kra_sfx_stage11_bossFight_veznanSoulImpact_cast_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage11VeznanSoulImpactImpact = {
    files = {
      "kra_sfx_stage11_bossFight_veznanSoulImpact_impact_var1_v1.ogg",
      "kra_sfx_stage11_bossFight_veznanSoulImpact_impact_var2_v1.ogg",
      "kra_sfx_stage11_bossFight_veznanSoulImpact_impact_var3_v1.ogg",
    },
    gain = 1,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  Stage11VeznanDemonGuardCast = {
    files = {
      "kra_sfx_stage11_bossFight_veznanDemonGuard_cast_withoutPortal_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage11VeznanMagicShacklesCast = {
    files = {
      "kra_sfx_stage11_bossFight_veznanMagicShackles_cast_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage11VeznanMagicShacklesRelease = {
    files = {
      "kra_sfx_stage11_bossFight_veznanMagicShackles_release_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage11BossCorruptedDenasAttack = {
    files = {
      "kra_sfx_stage11_bossFight_denasAttack_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  Stage11BossCorruptedDenasGlarelingSpawn = {
    files = {
      "kra_sfx_stage11_bossFight_denasGlarelingSpawn_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  Stage11BossCorruptedDenasTransformationIn = {
    files = {
      "kra_sfx_stage11_bossFight_denasTransformation_in_op1_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  Stage11BossCorruptedDenasTransformationOut = {
    files = {
      "kra_sfx_stage11_bossFight_denasTransformation_out_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  Stage11EasterEggFrodoAndSam = {
    files = {
      "kra_sfx_easterEgg_hobbitsShelob_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage11CreepPortalIn = {
    files = {
      "kra_sfx_stage11_bossFight_portalCreepIn_var1.ogg",
      "kra_sfx_stage11_bossFight_portalCreepIn_var2.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  Stage11CultLeaderLeave = {
    files = {
      "kra_sfx_stage11_bossFight_endCinematic_platformMove_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  Stage12SheepyPart1 = {
    files = {
      "kra_sfx_easterEgg_sheepyTerrain3_part1_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage12SheepyPart2 = {
    files = {
      "kra_sfx_easterEgg_sheepyTerrain3_part2_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage12SheepyPart3 = {
    files = {
      "kra_sfx_easterEgg_sheepyTerrain3_part3_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage12WeirderThingsEnterChar = {
    files = {
      "kra_sfx_easterEgg_weirderThings_enterChars_climb_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
    delay = 0.5
  },
  Stage12WeirderThingsFirstStrum = {
    files = {
      "kra_sfx_easterEgg_weirderThings_enterChars_firstStrum_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
    delay = 3.8,
  },
  Stage12WeirderThingsEnterCharTap2 = {
    files = {
      "kra_sfx_easterEgg_weirderThings_enterChars_tap2_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
    delay = 0.25
  },
  Stage13DarkRayTowerRepair = {
    files = {
      "kra_sfx_stage13_darkRayTower_repair_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage13DarkRayAttack = {
    files = {
      "kra_sfx_stage13_darkRayTower_attack_op2_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage13DarkRaySpecialAttack = {
    files = {
      "kra_sfx_stage13_darkRayTower_specialAttack_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  Stage13DarkRayDestroy = {
    files = {
      "kra_sfx_stage13_darkRayTower_destroy_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
    delay = 0.3,
  },
  Stage14NewPath = {
    files = {
      "kra_sfx_stage14_newPath_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage14BehemothPoolSplash = {
    files = {
      "kra_sfx_stage14_behemothPool_splash_var1_v1.ogg",
      "kra_sfx_stage14_behemothPool_splash_var2_v1.ogg",
      "kra_sfx_stage14_behemothPool_splash_var3_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "random",
    source_group = "SFX",
    delay = 0.5
  },
  Stage14BehemothPoolSpawn1 = {
    files = {
      "kra_sfx_stage14_behemothPool_spawn1_v2.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage14BehemothPoolSpawn2 = {
    files = {
      "kra_sfx_stage14_behemothPool_spawn2_v2.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage14BehemothPoolSpawn3 = {
    files = {
      "kra_sfx_stage14_behemothPool_spawn3_v2.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
    delay = 1.5
  },
  Stage14RickPortal12Open = {
    files = {
      "kra_sfx_easterEgg_wobbaLubbaDubDub_portal12_open_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX",
    delay = 0.5
  },
  Stage14RickPortal12Pass = {
    files = {
      "kra_sfx_easterEgg_wobbaLubbaDubDub_portal12_pass_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX",
    delay = 0
  },
  Stage14RickPortal12Close = {
    files = {
      "kra_sfx_easterEgg_wobbaLubbaDubDub_portal12_close_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX",
    delay = 0
  },
  Stage14RickPortalOpenNoLaser = {
    files = {
      "kra_sfx_easterEgg_wobbaLubbaDubDub_portal12_open-noLaser_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX",
    delay = 0
  },
  Stage14RickPortal3Out = {
    files = {
      "kra_sfx_easterEgg_wobbaLubbaDubDub_portal3_out_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX",
    delay = 0.5
  },
  Stage15ReinforcementDenasSummon = {
    files = {
      "kr_voice_kingdenas_taunt01_a.ogg",
      "kr_voice_kingdenas_taunt02_c.ogg",
      "kr_voice_kingdenas_taunt03_a.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "random",
    source_group = "TAUNTS",
  },
  Stage15ReinforcementDenasBasicAttack1 = {
    files = {
      "kra_sfx_stage15_reinforcementDenas_basicAttack_p1_var1_v1.ogg",
      "kra_sfx_stage15_reinforcementDenas_basicAttack_p1_var2_v1.ogg",
      "kra_sfx_stage15_reinforcementDenas_basicAttack_p1_var3_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage15ReinforcementDenasBasicAttack2 = {
    files = {
      "kra_sfx_stage15_reinforcementDenas_basicAttack_p2_var1_v1.ogg",
      "kra_sfx_stage15_reinforcementDenas_basicAttack_p2_var2_v1.ogg",
      "kra_sfx_stage15_reinforcementDenas_basicAttack_p2_var3_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage15ReinforcementDenasSpecialAttack = {
    files = {
      "kra_sfx_stage15_reinforcementDenas_secondaryAttack_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage15ReinforcementDenasSpawn = {
    files = {
      "kra_sfx_stage15_reinforcementDenas_summon_op1_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage15MydriasEnter = {
    files = {
      "kra_sfx_stage15_cinematic_mydriasEnter_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX",
  },
  Stage15MydriasExit = {
    files = {
      "kra_sfx_stage15_cinematic_mydriasExit_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX",
  },
  Stage15MutatedMydriasEnter = {
    files = {
      "kra_sfx_stage15_cinematic_mutatedMydriasEnter_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX",
  },
  Stage15MydriasTentacleTrap = {
    files = {
      "kra_sfx_stage15_bossFight_mydriasTentacleTrap_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage15MydriasTentacleCircleCounter = {
    files = {
      "kra_sfx_stage15_bossFight_mydriasTentacleCircleCounter_op1_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage15MydriasTentacleCircle = {
    files = {
      "kra_sfx_stage15_bossFight_mydriasTentacleCircle_op2_v2.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage15MydriasRay = {
    files = {
      "kra_sfx_stage15_bossFight_mydriasRay_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
    delay = 0.4,
  },
  Stage15MydriasUncloak = {
    files = {
      "kra_sfx_stage15_bossFight_mydriasUncloak_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage15MydriasBurrowIn = {
    files = {
      "kra_sfx_stage15_bossFight_mydriasBurrowIn_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage15MydriasBurrowOut = {
    files = {
      "kra_sfx_stage15_bossFight_mydriasBurrowOut_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage15RiffPortalOpen = {
    files = {
      "kra_sfx_easterEgg_riffPortal_portalOpen_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage15RiffPortalClose = {
    files = {
      "kra_sfx_easterEgg_riffPortal_portalClose_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage15RiffPortalBroom = {
    files = {
      "kra_sfx_easterEgg_riffPortal_Broom_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage15MydriasDeath = {
    files = {
      "kra_sfx_stage15_bossFight_mydriasDeath.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage15ReinforcementDenasOut = {
    files = {
      "kra_sfx_stage15_reinforcementDenas_out_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage16OverseerRumble = {
    files = {
      "kra_sfx_stage16_bossFight_overseerRumble_op1_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage16OverseerUnchainCenter = {
    files = {
      "kra_sfx_stage16_bossFight_overseerUnchainCenter_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage16OverseerUnchainLeftRight = {
    files = {
      "kra_sfx_stage16_bossFight_overseerUnchainLeftRight_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage16OverseerUnchainDown = {
    files = {
      "kra_sfx_stage16_bossFight_overseerUnchainDown_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage16OverseerSpawnerCast = {
    files = {
      "kra_sfx_stage16_bossFight_overseerSpawnerCast_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage16OverseerSpawnerImpact = {
    files = {
      "kra_sfx_stage16_bossFight_overseerSpawnerImpact_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage16OverseerTeleportCharge = {
    files = {
      "kra_sfx_stage16_bossFight_overseerTowerTeleport_PreCharge_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage16OverseerTeleport = {
    files = {
      "kra_sfx_stage16_bossFight_overseerTowerTeleport_TowerTeleport_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage16OverseerDestroyCharge = {
    files = {
      "kra_sfx_stage16_bossFight_overseerTowerHolderDestroy_charge_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage16OverseerDestroyRay = {
    files = {
      "kra_sfx_stage16_bossFight_overseerTowerHolderDestroy_Ray_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage16OverseerDestroyExplosion = {
    files = {
      "kra_sfx_stage16_bossFight_overseerTowerHolderDestroy_explosion_op2_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX",
  },
  Stage16OverseerHurt = {
    files = {
      "kra_sfx_stage16_bossFight_overseerHurt_op1_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage16OverseerDeath = {
    files = {
      "kra_sfx_stage16_bossFight_overseerDefeat_fullSeq_v2.ogg",
    },
    gain = 1.0,
    loop = false,
    source_group = "SFX",
    delay = 0.5,
  },
  Stage17VinesOut = {
    files = {
      "kra_sfx_stage17_rootSoulcaller_cast.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage17RootSoulcallerIn = {
    files = {
      "kra_sfx_stage17_rootSoulcaller_in.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage17RootSoulcallerOut = {
    files = {
      "kra_sfx_stage17_rootSoulcaller_out.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
    delay = 0.25
  },
  Stage18EridanInOut = {
    files = {
      "kra_sfx_stage18_eridan_in-out_v2.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage18EridanInstakill = {
    files = {
      "kra_sfx_stage18_eridan_instakill_v2.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage18LampBreak = {
    files = {
      "kra_sfx_stage18_lamp_break_op1_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage18CuckooIn = {
    files = {
      "kra_sfx_easterEgg_stage18Sheepy_tap-OPEN_v1.ogg",
    },
    gain = 1.0,
    delay = 0.2,
    loop = false,
    source_group = "SFX"
  },
  Stage18CuckooOut = {
    files = {
      "kra_sfx_easterEgg_stage18Sheepy_tap-CLOSE_v1.ogg",
    },
    gain = 1.0,
    delay = 0.6,
    loop = false,
    source_group = "SFX"
  },
  Stage19NaviraEnter = {
    files = {
      "kra_sfx_stage19_bossFight_navira_enter_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage19NaviraFireballSpawn = {
    files = {
      "kra_sfx_stage19_bossFight_navira_fireball_spawn_var1_v1.ogg",
      "kra_sfx_stage19_bossFight_navira_fireball_spawn_var2_v1.ogg",
      "kra_sfx_stage19_bossFight_navira_fireball_spawn_var3_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage19NaviraFireballCast = {
    files = {
      "kra_sfx_stage19_bossFight_navira_fireball_cast_var1_v1.ogg",
      "kra_sfx_stage19_bossFight_navira_fireball_cast_var2_v1.ogg",
      "kra_sfx_stage19_bossFight_navira_fireball_cast_var3_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage19NaviraFireballHit = {
    files = {
      "kra_sfx_stage19_bossFight_navira_fireball_impact_var1_v2.ogg",
      "kra_sfx_stage19_bossFight_navira_fireball_impact_var2_v2.ogg",
      "kra_sfx_stage19_bossFight_navira_fireball_impact_var3_v2.ogg",
    },
    gain = 0.6,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage19NaviraHandsDown = {
    files = {
      "kra_sfx_stage19_bossFight_statue_handsDown_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage19NaviraHandsUp = {
    files = {
      "kra_sfx_stage19_bossFight_statue_handsUp_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    delay = 4.9,
    source_group = "SFX",
  },
  Stage19NaviraTornadoIn = {
    files = {
      "kra_sfx_stage19_bossFight_navira_tornado_transformIn_op1_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage19NaviraTornadoOut = {
    files = {
      "kra_sfx_stage19_bossFight_navira_tornado_transformOut_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage19NaviraDeath = {
    files = {
      "kra_sfx_stage19_bossFight_navira_death_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage19Statue12 = {
    files = {
      "kra_sfx_stage19_statueGame_tap12_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage19Statue3 = {
    files = {
      "kra_sfx_stage19_statueGame_tap3_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage20TreeWakeup = {
    files = {
      "kra_sfx_crocs_tree_wakeup_op1_v1.ogg",
      "kra_sfx_crocs_tree_wakeup_op2_v1.ogg",
      "kra_sfx_crocs_tree_wakeup_op3_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    mode = "random",
    delay = 1.0,
    source_group = "SFX",
  },
  Stage20TreeHeadScratch = {
    files = {
      "kra_sfx_crocs_head_scratch_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    delay = 0.833,
    source_group = "SFX",
  },
  Stage20TreeHitFloor = {
    files = {
      "kra_sfx_crocs_trunk_hit_floor_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage20TreeHitFloorRepeat = {
    files = {
      "kra_sfx_tower_logBounce_var1_v1.ogg",
      "kra_sfx_tower_logBounce_var2_v1.ogg",
      "kra_sfx_tower_logBounce_var3_v1.ogg",
    },
    gain = 0.2,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage20BeesFly = {
    files = {
      "kra_sfx_crocs_bee_flying_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage20BeesThrow = {
    files = {
      "kra_sfx_crocs_bee_hit_floor_THROW_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage20BeesImpact = {
    files = {
      "kra_sfx_crocs_bee_hit_floor_IMPACT_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage20HouseImpact = {
    files = {
      "kra_sfx_crocs_arboreans_house_hit_var1_v1.ogg",
      "kra_sfx_crocs_arboreans_house_hit_var2_v1.ogg",
      "kra_sfx_crocs_arboreans_house_hit_var3_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    source_group = "SFX",
  },
  Stage20HouseDestroyed = {
    files = {
      "kra_sfx_crocs_arboreans_house_destroy_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX",
  },
  Stage21JuanchoEngineFail = {
    files = {
      "kra_sfx_crocs_juancho_engine_fail_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    delay = 0.333,
    source_group = "SFX",
  },
  Stage21JuanchoEngineSuccess = {
    files = {
      "kra_sfx_crocs_juancho_engine_sucess_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    delay = 0.333,
    source_group = "SFX",
  },

  Stage22AbominorAcidHit = {
    files = {
      "kra_sfx_crocs_abominor_acid_hit_floor_var1_v1.ogg",
      "kra_sfx_crocs_abominor_acid_hit_floor_var2_v1.ogg",
      "kra_sfx_crocs_abominor_acid_hit_floor_var3_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    -- delay = 0.333,
    source_group = "SFX",
  },
  Stage22AbominorCatchArm = {
    files = {
      "kra_sfx_crocs_abominor_catch_arm_again_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    -- delay = 0.333,
    source_group = "SFX",
  },
  Stage22AbominorDeath = {
    files = {
      "kra_sfx_crocs_abominor_death_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    -- delay = 0.333,
    source_group = "SFX",
  },
  Stage22AbominorEatEnemy = {
    files = {
      "kra_sfx_crocs_abominor_eat_enemy_op2_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    -- delay = 0.333,
    source_group = "SFX",
  },
  Stage22AbominorEatTower = {
    files = {
      "kra_sfx_crocs_abominor_eat_tower_eat_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    -- delay = 0.5,
    source_group = "SFX",
  },
  Stage22AbominorEatTowerV2 = {
    files = {
      "kra_sfx_crocs_abominor_release_eatTower_v2.ogg",
    },
    gain = 0.8,
    loop = false,
    -- delay = 0.5,
    source_group = "SFX",
  },
  Stage22AbominorEatTowerFistRemove = {
    files = {
      "kra_sfx_crocs_abominor_eat_tower_fistRemove_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    -- delay = 0.333,
    source_group = "SFX",
  },
  Stage22AbominorFireballHit = {
    files = {
      "kra_sfx_crocs_abominor_fireball_hit_floor_var1_v1.ogg",
      "kra_sfx_crocs_abominor_fireball_hit_floor_var2_v1.ogg",
      "kra_sfx_crocs_abominor_fireball_hit_floor_var3_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    -- delay = 0.333,
    source_group = "SFX",
  },
  Stage22AbominorMeleeHit = {
    files = {
      "kra_sfx_crocs_abominor_hit_enemy_var1_v1.ogg",
      "kra_sfx_crocs_abominor_hit_enemy_var2_v1.ogg",
      "kra_sfx_crocs_abominor_hit_enemy_var3_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    -- delay = 0.333,
    source_group = "SFX",
  },
  Stage22AbominorReleaseArm = {
    files = {
      "kra_sfx_crocs_abominor_release_arm_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    -- delay = 0.333,
    source_group = "SFX",
  },
  Stage22AbominorReleaseArmEatTowerOneshot = {
    files = {
      "kra_sfx_crocs_abominor_release_arm-eatTower_v2.ogg",
    },
    gain = 0.8,
    loop = false,
    -- delay = 0.333,
    source_group = "SFX",
  },
  Stage22AbominorScreamTransformation = {
    files = {
      "kra_sfx_crocs_abominor_grow_v1.ogg",
    },
    gain = 0.733,
    loop = false,
    delay = 0.9,
    source_group = "SFX",
  },
  Stage22AbominorSetFree = {
    files = {
      "kra_sfx_crocs_abominor_set_free_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    -- delay = 0.333,
    source_group = "SFX",
  },
  Stage22AbominorShootAcid = {
    files = {
      "kra_sfx_crocs_abominor_shoot_acid_throw_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    delay = 0.0666,
    source_group = "SFX",
  },
  Stage22AbominorShootFireball = {
    files = {
      "kra_sfx_crocs_abominor_shoot_fireball_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    delay = 0.0333,
    source_group = "SFX",
  },
  Stage22AbominorSpitEggs = {
    files = {
      "kra_sfx_crocs_abominor_spit_eggs_var1_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    delay = 0.15,
    source_group = "SFX",
  },
  Stage22AbominorFallToPath = {
    files = {
      "kra_sfx_crocs_abominor_stones_hit_floor_oneShot_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    delay = 0.0666,
    source_group = "SFX",
  },
  Stage22TowerRestore = {
    files = {
      "kra_sfx_crocs_magic_tower_restore_op1_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    delay = 0.0,
    source_group = "SFX",
  },

  Stage23BootOpen = {
    files = {
      "kra_sfx_stage23_automatonFoot_open_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage23BootClose = {
    files = {
      "kra_sfx_stage23_automatonFoot_close_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage23TruckOneShot = {
    files = {
      "kra_sfx_easterEgg_truck_oneShot_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage23TruckTap3 = {
    files = {
      "kra_sfx_easterEgg_truck_tap3_full_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage24UpgradeStationIn = {
    files = {
      "kra_sfx_stage24_upgradeStation_in_op2_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage24UpgradeStationOut = {
    files = {
      "kra_sfx_stage24_upgradeStation_out_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage24UpgradeStationTransform = {
    files = {
      "kra_sfx_stage24_upgradeStation_transform_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage24MachinistEnter = {
    files = {
      "kra_sfx_stage24_machinist_enter_op1_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage24MachinistExit = {
    files = {
      "kra_sfx_stage24_machinist_exit_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage24MachinistLever1 = {
    files = {
      "kra_sfx_stage24_machinist_lever_1_v1.ogg",
    },
    gain = 0.5,
    delay = 0.3,
    loop = false,
    source_group = "SFX",
  },
  Stage24MachinistLever2 = {
    files = {
      "kra_sfx_stage24_machinist_lever_2_v1.ogg",
    },
    gain = 0.5,
    delay = 0.1,
    loop = false,
    source_group = "SFX",
  },
  Stage24MachinistLever3 = {
    files = {
      "kra_sfx_stage24_machinist_lever_3_v1.ogg",
    },
    gain = 0.5,
    delay = 0.1,
    loop = false,
    source_group = "SFX",
  },
  Stage24FactoryTurnOnStart = {
    files = {
      "kra_sfx_stage24_factory_turnOn_start_v1.ogg",
    },
    gain = 0.2,
    loop = false,
    source_group = "SFX",
  },
  Stage24FactoryTurnOnEnd = {
    files = {
      "kra_sfx_stage24_factory_turnOn_end_v1.ogg",
    },
    gain = 0.4,
    delay = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage24FactoryTurnOff = {
    files = {
      "kra_sfx_stage24_factory_turnOff_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    source_group = "SFX",
  },
  Stage24Outro = {
    files = {
      "kra_sfx_stage24_outro_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage24BFMachinistCannonCastShot = {
    files = {
      "kra_sfx_stage24_bossFight_machinist_cannon_cast_shot_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage24BFMachinistCannonImpact = {
    files = {
      "kra_sfx_stage24_bossFight_machinist_cannon_impact_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },

  Stage25TorsoOpen = {
    files = {
      "kra_sfx_stage25_torso_open_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage25TorsoOperateLever1 = {
    files = {
      "kra_sfx_stage25_torso_operate_lever1_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX",
  },
  Stage25TorsoOperateLever2 = {
    files = {
      "kra_sfx_stage25_torso_operate_lever2_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage25TorsoClose = {
    files = {
      "kra_sfx_stage25_torso_close_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage25FistSlam = {
    files = {
      "kra_sfx_stage25_fist_slam_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage25TorsoButton = {
    files = {
      "kra_sfx_stage25_torso_button_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX",
  },
  Stage25MissileLaunch = {
    files = {
      "kra_sfx_stage25_missile_launch_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage25MissileImpact = {
    files = {
      "kra_sfx_stage25_missile_impact_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage25IntroCrash = {
    files = {
      "kra_sfx_stage25_intro_crash_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage25IntroCrashFinalExplosion = {
    files = {
      "kra_sfx_stage25_intro_crash-finalExplosion_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage25Outro = {
    files = {
      "kra_sfx_stage25_outro_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage25SolidSnakeTap12= {
    files = {
      "kra_sfx_easterEgg_solidSnake_tap12_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage25SolidSnakeTap3= {
    files = {
      "kra_sfx_easterEgg_solidSnake_tap3-MG_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX",
  },

  Stage26Chain = {
    files = {
      "kra_sfx_stage26_grymbeardChainPull_short.ogg",
    },
    gain = 0.5,
    loop = false,
    delay = 1,
    source_group = "SFX",
  },
  Stage26FistSpawnerHand = {
    files = {
      "kra_sfx_stage26_fistSpawner_HAND_var1_v1.ogg",
      "kra_sfx_stage26_fistSpawner_HAND_var2_v1.ogg",
      "kra_sfx_stage26_fistSpawner_HAND_var3_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage26FistSpawnerBoothFrontDoorOpen = {
    files = {
      "kra_sfx_stage26_fistSpawner_BoothFrontDoorOpen_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage26FistSpawnerBoothFrontDoorClose = {
    files = {
      "kra_sfx_stage26_fistSpawner_BoothFrontDoorClose_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage26CloneSpawnerIn = {
    files = {
      "kra_sfx_stage26_cloneSpawner_IN.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX",
  },
  Stage26CloneSpawnerOut = {
    files = {
      "kra_sfx_stage26_cloneSpawner_OUT.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX",
  },
  Stage26HulkSpawnerShotTransform = {
    files = {
      "kra_sfx_stage26_hulkSpawner_shot-transform_oneShot_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage26PreBFCinematic = {
    files = {
      "kra_sfx_stage26_preBFCinematic_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    delay = 0.5,
    source_group = "SFX",
  },
  Stage26BFGrymbeardDamaged = {
    files = {
      "kra_sfx_stage26_bossFight_grymbeard_damaged_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage26Outro = {
    files = {
      "kra_sfx_stage26_outro_v2.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage26MewtwoTap12 = {
    files = {
      "kra_sfx_easterEgg_mewtwo_tap12_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage26MewtwoTap3 = {
    files = {
      "kra_sfx_easterEgg_mewtwo_tap3_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage26MewtwoFlightFullSequence = {
    files = {
      "kra_sfx_easterEgg_mewtwo_flight_fullSeq_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  
  Stage27Intro = {
    files = {
      "kra_sfx_stage27_intro_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX",
  },
  Stage27PlatformUp = {
    files = {
      "kra_sfx_stage27_platformUp_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage27PlatformDown = {
    files = {
      "kra_sfx_stage27_platformDown_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage27PlatformDestroyChains = {
    files = {
      "kra_sfx_stage27_platformDestroy_chains_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage27PlatformDestroyHeadImpacts = {
    files = {
      "kra_sfx_stage27_platformDestroy_headImpacts_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage27CloneCannonOneShot = {
    files = {
      "kra_sfx_stage27_cloneCannon_oneShot_shot-retreat_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    delay = 0.5,
    source_group = "SFX",
  },
  Stage27CloneCannonAlarm = {
    files = {
      "kra_sfx_stage27_cloneCannon_alarm_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage27HeadOpen = {
    files = {
      "kra_sfx_stage27_headOpen_v1.ogg",
    },
    gain = 1,
    loop = false,
    delay = 0.3,
    source_group = "SFX",
  },
  Stage27HeadClose = {
    files = {
      "kra_sfx_stage27_headClose_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage27HeadMove = {
    files = {
      "kra_sfx_stage27_headFireblast_move_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    delay = 0.5,
    source_group = "SFX",
  },
  Stage27HeadReturn = {
    files = {
      "kra_sfx_stage27_headFireblast_move-returntocenter_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage27HeadFireblastCharge = {
    files = {
      "kra_sfx_stage27_headFireblast_charge_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX",
    interruptible = true,
  },
  Stage27HeadFireblastRelease = {
    files = {
      "kra_sfx_stage27_headFireblast_release_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
    interruptible = true,
  },
  Stage27HeadFireblastCancelTap = {
    files = {
      "kra_sfx_stage27_headFireblast_cancelTap_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
  },
  Stage27HeadFireblastInterrupt = {
    files = {
      "kra_sfx_stage27_headFireblast_interrupt_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage27PreBossfightCinematic = {
    files = {
      "kra_sfx_stage27_preBossFightCinematic_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage27BFGrymbeardDeath = {
    files = {
      "kra_sfx_stage27_bossFight_grymbeard_death_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage27BFGrymbeardMeleeAttack = {
    files = {
      "kra_sfx_stage27_bossFight_grymbeard_meleeAttack_var1_v1.ogg",
      "kra_sfx_stage27_bossFight_grymbeard_meleeAttack_var2_v1.ogg",
      "kra_sfx_stage27_bossFight_grymbeard_meleeAttack_var3_v1.ogg",
    },
    gain = 0.8,
    mode = "random",
    loop = false,
    delay = 0.9,
    source_group = "SFX",
  },
  Stage27BFGrymbeardRangedAttackCast = {
    files = {
      "kra_sfx_stage27_bossFight_grymbeard_rangedAttack_cast_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage27BFGrymbeardRangedAttackImpact = {
    files = {
      "kra_sfx_stage27_bossFight_grymbeard_rangedAttack_impact_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage27BFRobotScrapCast = {
    files = {
      "kra_sfx_stage27_bossFight_robotScrap_cast_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX",
  },
  Stage27BeamWorkersTap1 = {
    files = {
      "kra_sfx-easterEgg_beamWorkers_tap1_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage27BeamWorkersTap2 = {
    files = {
      "kra_sfx-easterEgg_beamWorkers_tap2_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage27BeamWorkersTap3 = {
    files = {
      "kra_sfx-easterEgg_beamWorkers_tap3_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage30BossfightCinematic = {
    files = {
      "kra_sfx_spiders_bossfight_cinematic_op1_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage30BossfightClawOpen = {
    files = {
      "kra_sfx_spiders_bossfight_claw_open_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage30BossfightClawClose = {
    files = {
      "kra_sfx_spiders_bossfight_claw_close_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage30BossfightRange = {
    files = {
      "kra_sfx_spiders_bossfight_range_var1_v1.ogg",
      "kra_sfx_spiders_bossfight_range_var2_v1.ogg",
      "kra_sfx_spiders_bossfight_range_var3_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage30BossfightSpit = {
    files = {
      "kra_sfx_spiders_bossfight_spit_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage30BossfightJump = {
    files = {
      "kra_sfx_spiders_bossfight_jump_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage30BossfightFall = {
    files = {
      "kra_sfx_spiders_bossfight_fall_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage30BossfightDead = {
    files = {
      "kra_sfx_spiders_bossfight_dead_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage30BossfightDrainLoopStart = {
    files = {
      "kra_sfx_spiders_bossfight_drain_charge_loop-start_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage30BossfightDrainLoop = {
    files = {
      "kra_sfx_spiders_bossfight_drain_charge_loop_v1.ogg",
    },
    gain = 0.7,
    loop = true,
    source_group = "SFX",
  },
  Stage30BossfightDrainExecute = {
    files = {
      "kra_sfx_spiders_bossfight_drain_execute_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  Stage30BossfightBuffCharge = {
    files = {
      "kra_sfx_spiders_bossfight_buff_charge_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },

  Stage31FountainRefill = {
    files = {
      "kra_sfx_wukong_mechanic_stage1_fountain_refill_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    delay = 3.0,
    source_group = "SPECIALS",
  },
  Stage31FountainSplash = {
    files = {
      "kra_sfx_wukong_mechanic_stage1_fountain_splash_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SPECIALS",
  },
  Stage31FountainTapoon = {
    files = {
      "kra_sfx_wukong_mechanic_stage1_fountain_tapon_var1_v1.ogg",
      "kra_sfx_wukong_mechanic_stage1_fountain_tapon_var2_v1.ogg",
      "kra_sfx_wukong_mechanic_stage1_fountain_tapon_var3_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SPECIALS",
  },

  -- Stage32RedboyDragonGoDown = {
  --   files = {
  --   },
  --   gain = 0.7,
  --   loop = false,
  --   mode = "random",
  --   source_group = "SFX",
  -- },
  Stage32RedboyDragonSamadhiFireStart = {
    files = {
      "kra_sfx_wukong_stage32_redboy_samadhi_fire_part1_v2.ogg",
    },
    gain = 0.7,
    delay = 0.35,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage32RedboyDragonSamadhiFireEnd = {
    files = {
      "kra_sfx_wukong_stage32_redboy_samadhi_fire_part2_v2.ogg",
    },
    gain = 0.7,
    delay = 2.7,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage32RedboyDragonLavaSurge = {
    files = {
      "kra_sfx_wukong_stage32_redboy_lava_surge_woVoice_v2.ogg",
    },
    gain = 0.7,
    loop = false,
    delay = 0.6,
    mode = "random",
    source_group = "SFX",
  },
  Stage32RedboyDragonRoar = {
    files = {
      "kra_sfx_wukong_stage32_dragon_roar_v1.ogg",
    },
    gain = 0.7,
    delay = 1,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage32RedboyDragonBlockTowers = {
    files = {
      "kra_sfx_wukong_stage32_dragon_lava_spit_op2_v1.ogg"
    },
    gain = 0.7,
    delay = 1.5,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage32RedboyAbsorbFire = {
    files = {
      "kra_sfx_wukong_stage32_redboy_explosion_v1.ogg",
    },
    gain = 0.7,
    delay = 0.2,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage32RedboyTransform = {
    files = {
      "kra_sfx_wukong_stage32_redboy_transform_v1.ogg",
    },
    gain = 0.7,
    delay = 0.5,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage32RedboyJumpFromDragon = {
    files = {
      "kra_sfx_wukong_stage32_redboy_entrance_op2_v1.ogg",
    },
    gain = 0.7,
    delay = 0.6,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage32RedboySamadhiAsTeen = {
    files = {
      "kra_sfx_wukong_stage32_redboy_samadhi_teen_prep_op2_v1.ogg",
    },
    gain = 0.7,
    delay = 1.3,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage32RedboyDeathStart = {
    files = {
      "kra_sfx_wukong_stage32_redboy_death_part1_v1.ogg"
    },
    gain = 0.7,
    delay = 0,
    loop = false,
    mode = "random",
    source_group = "SPECIALS",
  },
  Stage32RedboyDeathEnd = {
    files = {
      "kra_sfx_wukong_stage32_redboy_death_part2_v1.ogg"
    },
    gain = 0.7,
    delay = 0.6,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },

  Stage33StormStart = {
    files = {
      "kra_sfx_wukong_mechanic_stage3_storm_ambience_LOOPStart_v1.ogg"
    },
    gain = 0.2,
    loop = false,
    mode = "random",
    source_group = "SPECIALS",
  },

  Stage33StormLoop = {
    files = {
      "kra_sfx_wukong_mechanic_stage3_storm_ambience_LOOP_v1.ogg"
    },
    gain = 0.2,
    loop = true,
    source_group = "SPECIALS",
  },
  Stage33StormDistantThunder = {
    files = {
      "kra_sfx_wukong_mechanic_stage3_storm_ambience_distantThunder_var1_v1.ogg",
      "kra_sfx_wukong_mechanic_stage3_storm_ambience_distantThunder_var2_v1.ogg",
      "kra_sfx_wukong_mechanic_stage3_storm_ambience_distantThunder_var3_v1.ogg",
    },
    gain = 0.2,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage33StormLightning = {
    files = {
      "kra_sfx_wukong_mechanic_stage3_storm_lightning_strike_var1_v1.ogg",
      "kra_sfx_wukong_mechanic_stage3_storm_lightning_strike_var2_v1.ogg",
      "kra_sfx_wukong_mechanic_stage3_storm_lightning_strike_var3_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage33StormLightningMark = {
    files = {
      "kra_sfx_wukong_mechanic_stage3_storm_lightning_mark_v1.ogg"
    },
    gain = 0.7,
    loop = false,
    mode = "random",
    source_group = "SFX",
  },
  Stage33BoatDrumLoop = {
    files = {
      "kra_sfx_wukong_mechanic_stage3_boat_drum_op2_v1.ogg"
    },
    gain = 0.5,
    loop = true,
    source_group = "SPECIALS",
  },

  Terrain3GlareOnSmall1 = {
    files = {
      "kra_sfx_terrain3_glareOn_littleEye_op1_v1.ogg",
    },
    gain = 1.0,
    loop = false,
    source_group = "SFX"
  },
  Terrain3GlareOnSmall2 = {
    files = {
      "kra_sfx_terrain3_glareOn_littleEye_op2_v1.ogg",
    },
    gain = 1.0,
    loop = false,
    source_group = "SFX"
  },
  Terrain3GlareOnBig = {
    files = {
      "kra_sfx_terrain3_glareOn_bigEye_v1.ogg",
    },
    gain = 1.0,
    loop = false,
    source_group = "SFX"
  },
  Terrain3GlareOff = {
    files = {
      "kra_sfx_terrain3_glare_off_v1.ogg",
    },
    gain = 1.0,
    loop = false,
    delay = 0.75,
    source_group = "SFX"
  },

  Terrain4CheshireCatIn = {
    files = {
      "kra_sfx_easterEgg_cheshireCat_appear_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  Terrain4CheshireCatOut = {
    files = {
      "kra_sfx_easterEgg_cheshireCat_disappear_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    delay = 1.4,
    source_group = "SFX"
  },
  Terrain4HowlingTree = {
    files = {
      "kra_sfx_easterEgg_howlingTree_var1_v1.ogg",
      "kra_sfx_easterEgg_howlingTree_var2_v1.ogg",
      "kra_sfx_easterEgg_howlingTree_var3_v1.ogg",
    },
    gain = 1.0,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  Terrain6ExodiaPart = {
    files = {
      "kra_sfx_easterEgg_exodiaPart_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },

  TerrainWukongMeteoriteCast = {
    files = {
      "kra_sfx_wukong_mechanic_stage1_meteorites_LOOP_in_faded_martin.ogg",
    },
    gain = 0.55,
    loop = false,
    source_group = "SPECIALS",
  },
  TerrainWukongMeteoriteImpact = {
    files = {
      "kra_sfx_wukong_mechanic_stage1_meteorites_impact_v2.ogg",
    },
    gain = 0.55,
    loop = false,
    source_group = "SPECIALS",
  },
  TerrainWukongMeteoriteTravelLoop = {
    files = {
      "kra_sfx_wukong_mechanic_stage1_meteorites_travel-LOOP_v1.ogg",
    },
    gain = 0.55,
    loop = true,
    source_group = "SPECIALS",
  },
  TerrainWukongElementalHolderEvolve = {
    files = {
      "kra_sfx_wukong_mechanic_stage1_holder_evolve_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  TerrainWukongElementalHolderUnlock = {
    files = {
      "kra_sfx_wukong_mechanic_stage1_holder_unlock.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  TerrainWukongElementalHolderWoodActive = {
    files = {
      "kra_sfx_wukong_mechanic_stage1_holder_active_vines_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  TerrainWukongElementalHolderFireActiveIn = {
    files = {
      "kra_sfx_wukong_mechanic_stage2_holder_active_instakill_v1_in.ogg",
    },
    gain = 0.3,
    loop = false,
    source_group = "SFX",
  },
  TerrainWukongElementalHolderFireActiveKill = {
    files = {
      "kra_sfx_wukong_mechanic_stage2_holder_active_instakill_v1_kill.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX",
  },
  TerrainWukongElementalHolderEarthActive = {
    files = {
      "kra_sfx_wukong_mechanic_stage34_holder_active_summon_v1.ogg",
    },
    gain = 0.7,
    delay = 2,
    loop = false,
    source_group = "SFX",
  },
  
  --KR5 ITEMS
  ItemsClusterBombCast = {
    files = {
      "kra_sfx_inApps_clusterBomb_cast_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  ItemsClusterBombSmallBombs = {
    files = {
      "kra_sfx_inApps_clusterBomb_smallBombs_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  ItemsPortableCoilCast = {
    files = {
      "kra_sfx_inApps_portableCoil_cast_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  ItemsPortableCoilAttack = {
    files = {
      "kra_sfx_inApps_portableCoil_attack_var1_v1.ogg",
      "kra_sfx_inApps_portableCoil_attack_var2_v1.ogg",
      "kra_sfx_inApps_portableCoil_attack_var3_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX",
    mode = "random"
  },
  ItemsScrollOfSpaceshiftCast = {
    files = {
      "kra_sfx_inApps_scrollOfSpaceshift_cast_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  ItemsScrollOfSpaceshiftTeleportIn = {
    files = {
      "kra_sfx_inApps_scrollOfSpaceshift_teleportIn_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  ItemsScrollOfSpaceshiftTeleportOut = {
    files = {
      "kra_sfx_inApps_scrollOfSpaceshift_teleportOut_v1.ogg",
    },
    gain = 0.4,
    loop = false,
    source_group = "SFX"
  },
  ItemsSecondBreathCast = {
    files = {
      "kra_sfx_inApps_secondBreath_cast_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  ItemsDeathsTouchCast = {
    files = {
      "kra_sfx_inApps_deathsTouch_cast_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  ItemsWinterAgeCast = {
    files = {
      "kra_sfx_inApps_winterAge_cast_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  ItemsWinterAgeLoop = {
    files = {
      "kra_sfx_inApps_winterAge_loop_v1.ogg",
    },
    gain = 0.8,
    loop = true,
    source_group = "SFX"
  },
  ItemsWinterAgeRelease = {
    files = {
      "kra_sfx_inApps_winterAge_release_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  ItemsLootBoxCast = {
    files = {
      "kra_sfx_inApps_lootBox_cast_v1.ogg",
    },
    gain = 0.7,
    loop = false,
    source_group = "SFX"
  },
  ItemsMedicalKitCast = {
    files = {
      "kra_sfx_inApps_medicalKit_cast_op1_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  ItemsMedicalKitHeartAdd = {
    files = {
      "kra_sfx_inApps_medicalKit_heartAdd_v1.ogg",
    },
    gain = 0.9,
    loop = false,
    source_group = "SFX"
  },
  ItemsBlackburnCast = {
    files = {
      "kra_sfx_inApps_helmOfBlackburn_cast.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX"
  },
  ItemsBlackburnMeleeAttack = {
    files = {
      "kra_sfx_inApps_helmOfBlackburn_meleeAttack_var1_v1.ogg",
      "kra_sfx_inApps_helmOfBlackburn_meleeAttack_var2_v1.ogg",
      "kra_sfx_inApps_helmOfBlackburn_meleeAttack_var3_v1.ogg",
    },
    gain = 0.5,
    loop = false,
    source_group = "SFX",
    mode = "random",
    delay = 0.4
  },
  ItemsBlackburnRangedAttack = {
    files = {
      "kra_sfx_inApps_helmOfBlackburn_rangedAttack_op2_v1.ogg",
    },
    gain = 0.6,
    loop = false,
    source_group = "SFX",
  },
  ItemsVeznanWrathEnter = {
    files = {
      "kr5_sfx_veznanwrath_appear_sinrisa_v1.ogg",
    },
    gain = 0.8,
    loop = false,
    source_group = "SFX"
  },
  ItemsVeznanWrathInitialBurst = {
    files = {
      "kr5_sfx_veznanwrath_initialburst_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  ItemsVeznanWrathExplosion = {
    files = {
      "kr5_sfx_veznanwrath_flame_var1_v1.ogg",
      "kr5_sfx_veznanwrath_flame_var3_v1.ogg",
      "kr5_sfx_veznanwrath_flame_var4_v1.ogg",
    },
    gain = 1,
    loop = false,
    mode = "random",
    source_group = "SFX"
  },
  UpgradeLimitPushing = {
    files = {
      "kra_sfx_upgrade_limitPushing_trigger_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  UpgradeDisplayOfTrueMightDarkArmy = {
    files = {
      "kra_sfx_upgrade_displayOfTrueMight_darkArmy_trigger_var1_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  UpgradeDisplayOfTrueMightLinirea = {
    files = {
      "kra_sfx_upgrade_displayOfTrueMight_linirea_trigger_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  UpgradeFavoriteCustomer = {
    files = {
      "kra_sfx_upgrade_favouriteCustomer_trigger_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  UpgradeArcaneTeleporterIn = {
    files = {
      "kra_sfx_upgrade_arcaneTeleporter_trigger_IN_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  UpgradeArcaneTeleporterOut = {
    files = {
      "kra_sfx_upgrade_arcaneTeleporter_trigger_OUT_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  UpgradeArcaneTeleporterFull = {
    files = {
      "kra_sfx_upgrade_arcaneTeleporter_trigger_FullSeq_v1.ogg",
    },
    gain = 1,
    loop = false,
    source_group = "SFX"
  },
  UpgradeSealOfPunishmentHit = {
    files = {
      "kra_sfx_upgrade_sealOfPunishment_trigger_var1_v1.ogg",
      "kra_sfx_upgrade_sealOfPunishment_trigger_var2_v1.ogg",
      "kra_sfx_upgrade_sealOfPunishment_trigger_var3_v1.ogg",
      "kra_sfx_upgrade_sealOfPunishment_trigger_var4_v1.ogg",
      "kra_sfx_upgrade_sealOfPunishment_trigger_var5_v1.ogg",
    },
    gain = 1,
    loop = false,
    mode = "random",
    ignore = 0.5,
    source_group = "SFX"
  },
  kra_sfx_ui_mapDotsAppear_op2_v2 = { files={"kra_sfx_ui_mapDotsAppear_op2_v2.ogg"} , gain=1, loop=false, source_group="SFX" },
  kra_sfx_ui_stageFlagAppear   = { files={"kra_sfx_ui_stageFlagAppear_v1.ogg"  } , gain=1, loop=false, source_group="SFX" },

  kr4_flag_glow = {
    files={
      "kr4_flag_glow.ogg"
    },
    gain=0.2,
    loop=false,
    source_group="SFX"
  },
  kr4_map_star = {
    files={
      "kr4_map_star.ogg"
    },
    gain=1.0,
    loop=false,
    source_group="SFX"
  },
  MeleeSword = {
    files = {
      "Sound_SoldiersFighting-01.ogg",
      "Sound_SoldiersFighting-02.ogg",
      "Sound_SoldiersFighting-03.ogg",
      "Sound_SoldiersFighting-04.ogg",
      "Sound_SoldiersFighting-05.ogg",
    },
    source_group = "SWORDS",
    mode = "sequence",
    gain = 0.2,
    ignore = 0.45,
    loop = false,
  },

  -- screen music
  MusicMainMenu  = { files= {"kr5_bgmusic_mainMenu_v1.ogg" },     loop=true, gain=1.0, source_group='MUSIC', stream=true},
  MusicMap       = { files= {"kr5_bgmusic_atlas_v1.ogg"},         loop=true, gain=0.6, source_group='MUSIC', stream=true},
  MusicCredits   = { files= {"kr5_bgmusic_credits_v3.ogg"},       loop=true, gain=0.8, source_group='MUSIC', stream=true},
    
  -- battle preparation
  MusicBattlePrep_01 = { files={ "kr5_bgmusic_t1_preparation1_v1.ogg" },      loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_02 = { files={ "kr5_bgmusic_t1_preparation2_v1.ogg" },      loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_03 = { files={ "kr5_bgmusic_t1_preparation1_v1.ogg" },      loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_04 = { files={ "kr5_bgmusic_t1_preparation2_v1.ogg" },      loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_05 = { files={ "kr5_bgmusic_t1_preparation1_v1.ogg" },      loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_06 = { files={ "kr5_bgmusic_t1_preparation2_v1.ogg" },      loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_07 = { files={ "kr5_bgmusic_t2_preparation1_vN.ogg" },      loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_08 = { files={ "kr5_bgmusic_t2_preparation2_v2.ogg" },      loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_09 = { files={ "kr5_bgmusic_t2_preparation1_vN.ogg" },      loop=true, gain=0.4, source_group='MUSIC', stream=true},
  MusicBattlePrep_10 = { files={ "kr5_bgmusic_t2_preparation2_v2.ogg" },      loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_11 = { files={ "kr5_bgmusic_t2_preparation1_vN.ogg" },      loop=true, gain=0.4, source_group='MUSIC', stream=true},
  MusicBattlePrep_12 = { files={ "kr5_bgmusic_t3_preparation1_v1.ogg" },      loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_13 = { files={ "kr5_bgmusic_t3_preparation2_v1.ogg" },      loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_14 = { files={ "kr5_bgmusic_t3_preparation1_v1.ogg" },      loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_15 = { files={ "kr5_bgmusic_t3_preparation1_v1.ogg" },      loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_16 = { files={ "kr5_bgmusic_t3_preparation3_v1.ogg" },      loop=true, gain=0.4, source_group='MUSIC', stream=true},
  MusicBattlePrep_17 = { files={ "kr5_update1_bgmusic_preparation1_v3.ogg" }, loop=true, gain=0.5, source_group='MUSIC', stream=true},
  MusicBattlePrep_18 = { files={ "kr5_update1_bgmusic_preparation1_v3.ogg" }, loop=true, gain=0.6, source_group='MUSIC', stream=true},
  MusicBattlePrep_19 = { files={ "kr5_update1_bgmusic_preparation1_v3.ogg" }, loop=true, gain=0.6, source_group='MUSIC', stream=true},
  MusicBattlePrep_20 = { files={ "kr5_update2_bgmusic_preparation1_v1.ogg" }, loop=true, gain=0.6, source_group='MUSIC', stream=true},
  MusicBattlePrep_21 = { files={ "kr5_update2_bgmusic_preparation1_v1.ogg" }, loop=true, gain=0.5, source_group='MUSIC', stream=true},
  MusicBattlePrep_22 = { files={ "kr5_update2_bgmusic_preparation1_v1.ogg" }, loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_23 = { files={ "kr5_dlc1_bgmusic_preparation1_v1.ogg" },    loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_24 = { files={ "kr5_dlc1_bgmusic_preparation2_v2.ogg" },    loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_25 = { files={ "kr5_dlc1_bgmusic_preparation1_v1.ogg" },    loop=true, gain=0.4, source_group='MUSIC', stream=true},
  MusicBattlePrep_26 = { files={ "kr5_dlc1_bgmusic_preparation2_v2.ogg" },    loop=true, gain=0.4, source_group='MUSIC', stream=true},
  MusicBattlePrep_27 = { files={ "kr5_dlc1_bgmusic_preparation1_v1.ogg" },    loop=true, gain=0.4, source_group='MUSIC', stream=true},
  MusicBattlePrep_28 = { files={ "kr5_update3_bgmusic_preparation1_v1.ogg" }, loop=true, gain=0.6, source_group='MUSIC', stream=true},
  MusicBattlePrep_29 = { files={ "kr5_update3_bgmusic_preparation1_v1.ogg" }, loop=true, gain=0.5, source_group='MUSIC', stream=true},
  MusicBattlePrep_30 = { files={ "kr5_update3_bgmusic_preparation1_v1.ogg" }, loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_31 = { files={ "kr5_dlc2_bgmusic_preparation1_v1.ogg" },    loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_32 = { files={ "kr5_dlc2_bgmusic_preparation2_v1.ogg" },    loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_33 = { files={ "kr5_dlc2_bgmusic_preparation1_v1.ogg" },    loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_34 = { files={ "kr5_dlc2_bgmusic_preparation1_v1.ogg" },    loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_35 = { files={ "kr5_dlc2_bgmusic_preparation2_v1.ogg" },    loop=true, gain=0.3, source_group='MUSIC', stream=true},
  MusicBattlePrep_81 = { files={ "kr5_bgmusic_t3_preparation1_v1.ogg" },      loop=true, gain=0.6, source_group='MUSIC', stream=true},  -- same 1
  MusicBattlePrep_82 = { files={ "kr5_bgmusic_t3_preparation1_v1.ogg" },      loop=true, gain=0.5, source_group='MUSIC', stream=true},  -- same 2

  -- level music
  MusicBattle_01 = { files={ "kr5_bgmusic_t1_battle1_v1.ogg" },      loop=true, gain=0.25, source_group='MUSIC', stream=true},
  MusicBattle_02 = { files={ "kr5_bgmusic_t1_battle2_v1.ogg" },      loop=true, gain=0.25, source_group='MUSIC', stream=true},
  MusicBattle_03 = { files={ "kr5_bgmusic_t1_battle1_v1.ogg" },      loop=true, gain=0.25, source_group='MUSIC', stream=true},
  MusicBattle_04 = { files={ "kr5_bgmusic_t1_battle2_v1.ogg" },      loop=true, gain=0.25, source_group='MUSIC', stream=true},
  MusicBattle_05 = { files={ "kr5_bgmusic_t1_battle1_v1.ogg" },      loop=true, gain=0.25, source_group='MUSIC', stream=true},
  MusicBattle_06 = { files={ "kr5_bgmusic_t1_battle2_v1.ogg" },      loop=true, gain=0.25, source_group='MUSIC', stream=true},
  MusicBattle_07 = { files={ "kr5_bgmusic_t2_battle1_v1.ogg" },      loop=true, gain=0.25, source_group='MUSIC', stream=true},
  MusicBattle_08 = { files={ "kr5_bgmusic_t2_battle2_v2.ogg" },      loop=true, gain=0.25, source_group='MUSIC', stream=true},
  MusicBattle_09 = { files={ "kr5_bgmusic_t2_battle1_v1.ogg" },      loop=true, gain=0.25, source_group='MUSIC', stream=true},
  MusicBattle_10 = { files={ "kr5_bgmusic_t2_battle2_v2.ogg" },      loop=true, gain=0.25, source_group='MUSIC', stream=true},
  MusicBattle_11 = { files={ "kr5_bgmusic_t2_battle1_v1.ogg" },      loop=true, gain=0.25, source_group='MUSIC', stream=true},
  MusicBattle_12 = { files={ "kr5_bgmusic_t3_battle1_v1.ogg" },      loop=true, gain=0.3,  source_group='MUSIC', stream=true},
  MusicBattle_13 = { files={ "kr5_bgmusic_t3_battle2_v1.ogg" },      loop=true, gain=0.3,  source_group='MUSIC', stream=true},
  MusicBattle_14 = { files={ "kr5_bgmusic_t3_battle1_v1.ogg" },      loop=true, gain=0.3,  source_group='MUSIC', stream=true},
  MusicBattle_15 = { files={ "kr5_bgmusic_t3_battle2_v1.ogg" },      loop=true, gain=0.3,  source_group='MUSIC', stream=true},
  MusicBattle_16 = { files={ "kr5_bgmusic_t3_boss2_v1.ogg" },        loop=true, gain=0.4,  source_group='MUSIC', stream=true},
  MusicBattle_17 = { files={ "kr5_update1_bgmusic_battle1_v2.ogg" }, loop=true, gain=0.4,  source_group='MUSIC', stream=true},
  MusicBattle_18 = { files={ "kr5_update1_bgmusic_battle1_v2.ogg" }, loop=true, gain=0.4,  source_group='MUSIC', stream=true},
  MusicBattle_19 = { files={ "kr5_update1_bgmusic_battle1_v2.ogg" }, loop=true, gain=0.4,  source_group='MUSIC', stream=true},
  MusicBattle_20 = { files={ "kr5_update2_bgmusic_battle1_v1.ogg" }, loop=true, gain=0.2,  source_group='MUSIC', stream=true},
  MusicBattle_21 = { files={ "kr5_update2_bgmusic_battle1_v1.ogg" }, loop=true, gain=0.2,  source_group='MUSIC', stream=true},
  MusicBattle_22 = { files={ "kr5_update2_bgmusic_battle1_v1.ogg" }, loop=true, gain=0.3,  source_group='MUSIC', stream=true},
  --T2 Music as proxy for Dwarf DLC Stages
  MusicBattle_23 = { files={ "kr5_dlc1_bgmusic_battle1_v1.ogg" },    loop=true, gain=0.25, source_group='MUSIC', stream=true},
  MusicBattle_24 = { files={ "kr5_dlc1_bgmusic_battle2_v1.ogg" },    loop=true, gain=0.30, source_group='MUSIC', stream=true},
  MusicBattle_25 = { files={ "kr5_dlc1_bgmusic_battle1_v1.ogg" },    loop=true, gain=0.25, source_group='MUSIC', stream=true},
  MusicBattle_26 = { files={ "kr5_dlc1_bgmusic_battle2_v1.ogg" },    loop=true, gain=0.30, source_group='MUSIC', stream=true},
  MusicBattle_27 = { files={ "kr5_dlc1_bgmusic_battle1_v1.ogg" },    loop=true, gain=0.25, source_group='MUSIC', stream=true},
  MusicBattle_28 = { files={ "kr5_update3_bgmusic_battle1_v1.ogg" }, loop=true, gain=0.4,  source_group='MUSIC', stream=true},
  MusicBattle_29 = { files={ "kr5_update3_bgmusic_battle1_v1.ogg" }, loop=true, gain=0.4,  source_group='MUSIC', stream=true},
  MusicBattle_30 = { files={ "kr5_update3_bgmusic_battle1_v1.ogg" }, loop=true, gain=0.4,  source_group='MUSIC', stream=true},
  MusicBattle_31 = { files={ "kr5_dlc2_bgmusic_battle1_v1.ogg" },    loop=true, gain=0.2,  source_group='MUSIC', stream=true},
  MusicBattle_32 = { files={ "kr5_dlc2_bgmusic_battle2_v1.ogg" },   loop=true, gain=0.2,  source_group='MUSIC', stream=true},
  MusicBattle_33 = { files={ "kr5_dlc2_bgmusic_battle3_v1.ogg" },   loop=true, gain=0.2,  source_group='MUSIC', stream=true},
  MusicBattle_34 = { files={ "kr5_dlc2_bgmusic_battle1_v1.ogg" },   loop=true, gain=0.2,  source_group='MUSIC', stream=true},
  MusicBattle_35 = { files={ "kr5_dlc2_bgmusic_battle2_v1.ogg" },   loop=true, gain=0.2,  source_group='MUSIC', stream=true},
  MusicBattle_81 = { files={ "kr5_bgmusic_t3_battle2_v1" },          loop=true, gain=0.2,  source_group='MUSIC', stream=true},
  MusicBattle_82 = { files={ "kr5_bgmusic_t3_battle2_v1" },          loop=true, gain=0.2,  source_group='MUSIC', stream=true},

  -- boss fights
  MusicBossFight_6 =    { files= {"kr5_bgmusic_t1_boss_v2.ogg" },            loop=true, gain=0.4, source_group='MUSIC', stream=true},
  MusicBossFight_11 =   { files= {"kr5_bgmusic_t2_boss_v1.ogg" },            loop=true, gain=0.4, source_group='MUSIC', stream=true},
  MusicBossFight_15 =   { files= {"kr5_bgmusic_t3_boss1_v1.ogg" },           loop=true, gain=0.4, source_group='MUSIC', stream=true},
  MusicBossFight_16 =   { files= {"kr5_bgmusic_t3_boss2_v1.ogg" },           loop=true, gain=0.4, source_group='MUSIC', stream=true},
  MusicBossFight_19 =   { files= {"kr5_update1_bgmusic_bossBattle_v2.ogg" }, loop=true, gain=0.4, source_group='MUSIC', stream=true},
  MusicBossFight_22 =   { files= {"kr5_update2_bgmusic_bossBattle_v1.ogg" }, loop=true, gain=0.4, source_group='MUSIC', stream=true},
  MusicBossFight_27 =   { files= {"kr5_dlc1_bgmusic_bossBattle_v1.ogg" },    loop=true, gain=0.4, source_group='MUSIC', stream=true},
  MusicBossFight_30 =   { files= {"kr5_update3_bgmusic_bossBattle_v1.ogg" }, loop=true, gain=0.4, source_group='MUSIC', stream=true},


  MusicEndVictory =      { files= {"kr5_bgmusic_t3_boss_victory.ogg"  }, gain=0.7, source_group='MUSIC', stream=true},
  MusicSuspense =        { files= {"MusicSuspense.ogg"                }, gain=0.7, source_group='MUSIC', stream=true},  -- TODO: kr3?
}
