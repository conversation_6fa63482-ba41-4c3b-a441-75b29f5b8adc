return {
  common = {
    files = {
      "kr_voice_surgecolossus_taunt-select_c.ogg",
      "kr_voice_cannoneersquad_taunt-select_e.ogg",
      "kr_voice_boghermit_build_a.ogg",
      "kr_voice_twilightlongbows_select_b.ogg",
      "kr_voice_eldrictchchannelers_select_[2]a.ogg",
      "kr_voice_grimwraiths_select_c.ogg",
      "kr_voice_dunesentinels_select_e.ogg",
      "kr_voice_battlebrewmasters_select_c.ogg",
      "kr_voice_dwarvenflamespitter_taunt01_c.ogg",
      "kr_voice_necromancerslair_taunt01_c.ogg",
      "kr_voice_rocketgunners_taunt01_c.ogg",
      "kr_voice_ballistaoutpost_taunt01_d.ogg",
      "kr_voice_elvenstargazers_taunt01_d.ogg",
      "kr_voice_arboreanemissary_taunt3_var1a.ogg",
      "kr_voice_demonpit_taunt3_var1a.ogg",
      "kr_voice_tricannon_taunt3_var1a.ogg",
      "kr_voice_arcanewizard_taunt_var1c.ogg",
      "kr_voice_paladincovenant_taunt3_var1a.ogg",
      "kr_voice_royalArchers_taunt_var1c.ogg",
      "kr_voice_theriennethevoidadept_select_c.ogg",
      "kr_voice_vesper_taunt.ogg",
      "kr_voice_nyru_taunt2_var1c.ogg",
      "kr_voice_warhead_select_b.ogg",
      "kr_voice_raelyn_taunt3_var2b.ogg",
      "kr_voice_torrestheforeman_select_a.ogg",
      "kr_voice_onagro_taunt_a.ogg",
      "kr_voice_lumenir_taunt03_a.ogg",
      "kr_voice_grimson_taunt_b.ogg",
      "kr_voice_anya_select_c.ogg",
      "kr_voice_kosmyr_select_c.ogg",
      "kr_voice_broden_select_b.ogg",
      "kr_voice_stregi_select_b.ogg",
      "kr_voice_bonehart_select_c.ogg",
      "kr_voice_silvara_move2_a.ogg",
      "kr_voice_kratoa_taunt-select_c.ogg",
      "kr_voice_spydyr_taunt-select_b.ogg",
      "kr_voice_sunwukong_4_a.ogg",
      "kr_voice_CN_wukong_here_comes_your_grandpa_sun_1.ogg",
      "kra_sfx_easterEgg_interactionTap.ogg",
      "kr5_sfx_UIgate-close.ogg",
      "kr5_sfx_UIgate-open_op1.ogg",
      "Level_up.ogg",
      "Sound_AchievementWin.ogg",
      "Sound_ArrowHit2.ogg",
      "Sound_ArrowHit3.ogg",
      "Sound_ArrowRelease2.ogg",
      "Sound_ArrowRelease3.ogg",
      "Sound_Bomb1.ogg",
      "Sound_Coins.ogg",
      "Sound_CommonAreaHit.ogg",
      "Sound_EnemyExplode1.ogg",
      "Sound_GUIBuyUpgrade.ogg",
      "Sound_GUIMouseOverTowerIcon.ogg",
      "Sound_GUIOpenTowerMenu.ogg",
      "Sound_HumanDead1.ogg",
      "Sound_HumanDead2.ogg",
      "Sound_HumanDead3.ogg",
      "Sound_HumanDead4.ogg",
      "Sound_LooseLife.ogg",
      "Sound_MageShot.ogg",
      "Sound_MapNewFlag.ogg",
      "Sound_MapRoad.ogg",
      "Sound_NextWaveReady.ogg",
      "Sound_NotificationClose.ogg",
      "Sound_NotificationOpen.ogg",
      "Sound_NotificationPaperOver.ogg",
      "Sound_NotificationSecondLevel.ogg",
      "Sound_RallyPointPlaced.ogg",
      "Sound_SpellRefresh.ogg",
      "Sound_SpellSelect.ogg",
      "Sound_TowerBuilding.ogg",
      "Sound_TowerSell.ogg",
      "Sound_WaveIncoming.ogg",
      "kr5_sfx_victorystars_3_v1.ogg",
      "inapp_cash.ogg",
      "inapp_chin.ogg",
      "inapp_gems.ogg",
      "inapp_gnome.ogg",
      "kr_voice_linireanreinforcements_taunt01_c.ogg",
      "kr_voice_linireanreinforcements_taunt02_c.ogg",
      "kr_voice_linireanreinforcements_taunt03_b.ogg",
      "kr_voice_darkarmyreinforcements_taunt01_a.ogg",
      "kr_voice_darkarmyreinforcements_taunt02_b.ogg",
      "kr_voice_darkarmyreinforcements_taunt03_b.ogg",
      "kr5_sfx_achievementcollect.ogg",
      "kra_sfx_ui_rewards_disappear_var1_v1.ogg",
      "kra_sfx_ui_rewards_disappear_var2_v1.ogg",
      "kra_sfx_ui_rewards_disappear_var3_v1.ogg",
      "kr5_sfx_heroselect_op2.ogg",
      "kr5_sfx_balloon-in.ogg",
      "kr5_sfx_balloon-out.ogg",
      "kra_sfx_uiMenu_hover_op1_v1.ogg",
      "kr5_sfx_flagfall.ogg",
      "kr5_sfx_genericbuttonsoft_op1.ogg",
      "kr5_sfx_genericbuttonsoft_op2.ogg",
      "kra_sfx_ui_buttonUnavailable_v1.ogg",
      "kr5_sfx_heroscroll_op1.ogg",
      "kr5_sfx_heroselect_op1.ogg",
      "kr5_sfx_heroskillconfirm.ogg",
      "kr5_sfx_heroskillselect.ogg",
      "kra_sfx_upgrade_limitPushing_trigger_v1.ogg",
      "kra_sfx_upgrade_displayOfTrueMight_darkArmy_trigger_var1_v1.ogg",
      "kra_sfx_upgrade_displayOfTrueMight_linirea_trigger_v1.ogg",
      "kra_sfx_upgrade_favouriteCustomer_trigger_v1.ogg",
      "kra_sfx_upgrade_arcaneTeleporter_trigger_FullSeq_v1.ogg",
      "kra_sfx_upgrade_sealOfPunishment_trigger_var1_v1.ogg",
      "kra_sfx_upgrade_sealOfPunishment_trigger_var2_v1.ogg",
      "kra_sfx_upgrade_sealOfPunishment_trigger_var3_v1.ogg",
      "kra_sfx_upgrade_sealOfPunishment_trigger_var4_v1.ogg",
      "kra_sfx_upgrade_sealOfPunishment_trigger_var5_v1.ogg",
      "kra_sfx_combat_meleeAttack_noSword_var1.ogg",
      "kra_sfx_combat_meleeAttack_noSword_var6.ogg",
      "kra_sfx_combat_meleeAttack_noSword_var5.ogg",
      "kra_sfx_combat_meleeAttack_noSword_var4.ogg",
      "kra_sfx_combat_meleeAttack_noSword_var3.ogg",
      "kra_sfx_combat_meleeAttack_noSword_var2.ogg",
      "kra_sfx_combat_rangedAttack_arrows_var1_v1.ogg",
      "kra_sfx_combat_rangedAttack_arrows_var3_v1.ogg",
      "kra_sfx_combat_rangedAttack_arrows_var4_v1.ogg",
      "kra_sfx_ui_rewards_unlock_appear-stomp_v1.ogg",
      "kra_sfx_ui_rewards_unlock_glow_v1.ogg",
      "kra_sfx_ui_rewards_unlock_cardReveal_v1.ogg",
      "kra_sfx_ui_mapDotsAppear_op2_v2.ogg",
      "kra_sfx_ui_stageFlagAppear_v1.ogg",
      "kra_sfx_ui_buttonOut_op2_v2.ogg",
      "kra_sfx_ui_resetSkills_v1.ogg",
      "kra_sfx_ui_heroTowerSelect_v1.ogg",
      "kra_sfx_ui_towerWheelDrag_tapOn_v1.ogg",
      "kra_sfx_ui_towerWheelDrag_tapOff_v1.ogg",
      "kra_sfx_ui_stageVictory_v1.ogg",
      "kra_sfx_ui_stageDefeat_v1.ogg",
      "kra_sfx_ui_gemCounter_SINGLE_v1.ogg",
      "kra_sfx_uiMap_cultistBridge_op2_v1.ogg",
      "kra_sfx_uiMap_cloudRemoval_v1.ogg",
      "kr4_flag_glow.ogg",
      "kr4_map_star.ogg",
      "kra_sfx_uiMap_heroicChallengeFlag_v1.ogg",
      "Sound_SoldiersFighting-01.ogg",
      "Sound_SoldiersFighting-02.ogg",
      "Sound_SoldiersFighting-03.ogg",
      "Sound_SoldiersFighting-04.ogg",
      "Sound_SoldiersFighting-05.ogg",
      "kr_voice_neutralreinforcements_taunt01_a.ogg",
      "kr_voice_neutralreinforcements_taunt02_a.ogg",
      "kr_voice_neutralreinforcements_taunt03_a.ogg",
      --"kr5_button_hover_op1.ogg",
      "kr5_button_hover_op2.ogg",
      --"kr5_button_hover_op3.ogg",
      "kr5_sfx_lightning_op1.ogg",
      "kr5_sfx_lightning_op2.ogg",
      "kr5_sfx_lightning_op3.ogg",
      "kr_voice_pandatower_taunt01_f.ogg",
      "kr_voice_CN_pandas_panda_style_3.ogg",
    },
    ids = {},
    keep = true
  },
  splash = {
    files = {
      "KR5_SFX_IronhideLogo_24042024.ogg",
    },
    ids = {},
  },

  music_screen_map     = {sounds={ 'MusicMap', }},
  music_screen_slots   = {sounds={ 'MusicMainMenu',  }},
  music_screen_credits = {sounds={ 'MusicCredits' }},
  music_screen_kr5_end = {sounds={ 'MusicEndVictory', 'MusicSuspense', 'MusicCredits' }}, 
  music_stage01 = {sounds={ 'MusicBattlePrep_01', 'MusicBattle_01', }},
  music_stage02 = {sounds={ 'MusicBattlePrep_02', 'MusicBattle_02', }},
  music_stage03 = {sounds={ 'MusicBattlePrep_03', 'MusicBattle_03', }},
  music_stage04 = {sounds={ 'MusicBattlePrep_04', 'MusicBattle_04', }},
  music_stage05 = {sounds={ 'MusicBattlePrep_05', 'MusicBattle_05', }},
  music_stage06 = {sounds={ 'MusicBattlePrep_06', 'MusicBattle_06', 'MusicBossFight_6'}},
  music_stage07 = {sounds={ 'MusicBattlePrep_07', 'MusicBattle_07', }},
  music_stage08 = {sounds={ 'MusicBattlePrep_08', 'MusicBattle_08', }},
  music_stage09 = {sounds={ 'MusicBattlePrep_09', 'MusicBattle_09', }},
  music_stage10 = {sounds={ 'MusicBattlePrep_10', 'MusicBattle_10', }},
  music_stage11 = {sounds={ 'MusicBattlePrep_11', 'MusicBattle_11', 'MusicBossFight_11'}},
  music_stage12 = {sounds={ 'MusicBattlePrep_12', 'MusicBattle_12', }},
  music_stage13 = {sounds={ 'MusicBattlePrep_13', 'MusicBattle_13', }},
  music_stage14 = {sounds={ 'MusicBattlePrep_14', 'MusicBattle_14', }},
  music_stage15 = {sounds={ 'MusicBattlePrep_15', 'MusicBattle_15', 'MusicBossFight_15'}},
  music_stage16 = {sounds={ 'MusicBattlePrep_16', 'MusicBattle_16', 'MusicBossFight_16'}},
  music_stage17 = {sounds={ 'MusicBattlePrep_17', 'MusicBattle_17', }},
  music_stage18 = {sounds={ 'MusicBattlePrep_18', 'MusicBattle_18', }},
  music_stage19 = {sounds={ 'MusicBattlePrep_19', 'MusicBattle_19', 'MusicBossFight_19', }},
  music_stage20 = {sounds={ 'MusicBattlePrep_20', 'MusicBattle_20', }},
  music_stage21 = {sounds={ 'MusicBattlePrep_21', 'MusicBattle_21', }},
  music_stage22 = {sounds={ 'MusicBattlePrep_22', 'MusicBattle_22', 'MusicBossFight_22'}},
  --T2 Music as proxy for Dwarf DLC Stages
  music_stage23 = {sounds={ 'MusicBattlePrep_23', 'MusicBattle_23', }},
  music_stage24 = {sounds={ 'MusicBattlePrep_24', 'MusicBattle_24', }},
  music_stage25 = {sounds={ 'MusicBattlePrep_25', 'MusicBattle_25', }},
  music_stage26 = {sounds={ 'MusicBattlePrep_26', 'MusicBattle_26', }},
  music_stage27 = {sounds={ 'MusicBattlePrep_27', 'MusicBattle_27', 'MusicBossFight_27' }},
  music_stage28 = {sounds={ 'MusicBattlePrep_28', 'MusicBattle_28', }},
  music_stage29 = {sounds={ 'MusicBattlePrep_29', 'MusicBattle_29', }},
  music_stage30 = {sounds={ 'MusicBattlePrep_30', 'MusicBattle_30', 'MusicBossFight_30'  }},
  -- DLC2 Wukong
  music_stage31 = {sounds={ 'MusicBattlePrep_31', 'MusicBattle_31', }},
  music_stage32 = {sounds={ 'MusicBattlePrep_32', 'MusicBattle_32', }},
  music_stage33 = {sounds={ 'MusicBattlePrep_33', 'MusicBattle_33', }},
  music_stage34 = {sounds={ 'MusicBattlePrep_34', 'MusicBattle_34', }},
  music_stage35 = {sounds={ 'MusicBattlePrep_35', 'MusicBattle_35', }},

  music_stage81 = {sounds={ 'MusicBattlePrep_81', 'MusicBattle_81', }},
  music_stage82 = {sounds={ 'MusicBattlePrep_82', 'MusicBattle_82', }},

  --kr5
  hero_vesper = {
    files = {
      "kr_voice_vesper_taunt.ogg",
      "kr_voice_vesper_taunt2.ogg",
      "kr_voice_vesper_taunt3.ogg",
      "kr_voice_vesper_taunt4.ogg",
      "kr_voice_vesper_death_var1d.ogg",
      "kra_sfx_heroes_vesper_disengage_v1.ogg",
      "kra_heroes_vesper_arrowStorm_high_v1.ogg",
      "kra_heroes_vesper_arrowStorm_low_v1.ogg",
      "kra_heroes_vesper_arrowStorm_mid_v1.ogg",
      "kra_heroes_vesper_arrowToTheKnee_cast_v1.ogg",
      "kra_heroes_vesper_arrowToTheKnee_impact_op1_v1.ogg",
      "kra_heroes_vesper_oneShot_martialFlourish_v1.ogg",
      "kra_heroes_vesper_ricochet_cast_v1.ogg",
      "kra_heroes_vesper_ricochet_impact_var1_v1.ogg",
      "kra_heroes_vesper_ricochet_impact_var2_v1.ogg",
      "kra_heroes_vesper_ricochet_impact_var3_v1.ogg",
    }
  },
  hero_muyrn = {
    files = {
      "kr_voice_nyru_taunt_var1c.ogg",
      "kr_voice_nyru_taunt2_var1c.ogg",
      "kr_voice_nyru_taunt3_var1a.ogg",
      "kr_voice_nyru_taunt4_var1a.ogg",
      "kr_voice_nyru_death_var1a.ogg",
      "kra_heroes_nyru_basicAttack_melee_var1_op1_v1.ogg",
      "kra_heroes_nyru_basicAttack_melee_var2_op1_v1.ogg",
      "kra_heroes_nyru_basicAttack_melee_var3_op1_v1.ogg",
      "kra_heroes_nyru_basicAttack_ranged_var1_v1.ogg",
      "kra_heroes_nyru_basicAttack_ranged_var2_v1.ogg",
      "kra_heroes_nyru_basicAttack_ranged_var3_v1.ogg",
      "kra_heroes_nyru_fairyDust_v1.ogg",
      "kra_heroes_nyru_leafWhirlwind_op1_v2.ogg",
      "kra_heroes_nyru_rootDefender_high_v1.ogg",
      "kra_heroes_nyru_rootDefender_low_v1.ogg",
      "kra_heroes_nyru_rootDefender_mid_v1.ogg",
      "kra_heroes_nyru_rootDefender_retract_v1.ogg",
      "kra_heroes_nyru_sentinelWisps_attack_var1_v1.ogg",
      "kra_heroes_nyru_sentinelWisps_attack_var3_v1.ogg",
      "kra_heroes_nyru_sentinelWisps_cast_v1.ogg",
      "kra_heroes_nyru_sentinelWisps_spawn_v1.ogg",
      "kra_heroes_nyru_treewalk_LOOP_var2_v2.ogg",
      "kra_heroes_nyru_verdantBlast_cast_oneShot_v1.ogg",
      "kra_heroes_nyru_verdantBlast_impact_v1.ogg",
    }
  },
  hero_raelyn = {
    files = {
      "kr_voice_raelyn_death_var1e.ogg",
      "kr_voice_raelyn_taunt_var1c.ogg",
      "kr_voice_raelyn_taunt2_var2a.ogg",
      "kr_voice_raelyn_taunt3_var2b.ogg",
      "kr_voice_raelyn_taunt4_var1a.ogg",
      "kra_sfx_heroes_raelyn_basicAttack_var1_v1.ogg",
      "kra_sfx_heroes_raelyn_brutalSlash_op2_v1.ogg",
      "kra_sfx_heroes_raelyn_commandOrders_death_v1.ogg",
      "kra_sfx_heroes_raelyn_commandOrders_op2_v1.ogg",
      "kra_sfx_heroes_raelyn_inspireFear_op2_v1.ogg",
      "kra_sfx_heroes_raelyn_onslaught_var3_v1.ogg",
      "kra_sfx_heroes_raelyn_unbreakable_op1_v1.ogg",
      "kra_sfx_heroes_raelyn_commandOrders_v2_op1.ogg",
      "kr_voice_darkknight_taunt01_d.ogg",
      "kr_voice_darkknight_taunt02_c.ogg",
    }
  },
  hero_space_elf = {
    files = {
      "kr_voice_theriennethevoidadept_death_h.ogg",
      "kr_voice_theriennethevoidadept_select_c.ogg",
      "kr_voice_theriennethevoidadept_taunt01_d.ogg",
      "kr_voice_theriennethevoidadept_taunt02_c.ogg",
      "kr_voice_theriennethevoidadept_taunt03_b.ogg",
      "kra_sfx_heroes_therien_astralReflection_v1.ogg",
      "kra_sfx_heroes_therien_blackAegis_cast_v1.ogg",
      "kra_sfx_heroes_therien_blackAegis_explosion.ogg",
      "kra_sfx_heroes_therien_cosmicPrison_phaseIn_v1.ogg",
      "kra_sfx_heroes_therien_cosmicPrison_phaseOut_v1.ogg",
      "kra_sfx_heroes_therien_spatialDistortion_cast_v1.ogg",
      "kra_sfx_heroes_therien_teleportIn_v1.ogg",
      "kra_sfx_heroes_therien_teleportOut_v1.ogg",
      "kra_sfx_heroes_therien_voidRift_cast_v1.ogg",
      "kra_heroes_nyru_basicAttack_melee_var1_op1_v1.ogg",
      "kra_sfx_enemy_turtleShaman_basicAttack_var3.ogg"
    }
  },
  hero_builder = {
    files = {
      "kr_voice_torrestheforeman_death_a.ogg",
      "kr_voice_torrestheforeman_select_a.ogg",
      "kr_voice_torrestheforeman_taunt01_c.ogg",
      "kr_voice_torrestheforeman_taunt02_b.ogg",
      "kr_voice_torrestheforeman_taunt03_e.ogg",
      "kra_sfx_heroes_torres_demolitionMan_spin_v1.ogg",
      "kra_sfx_heroes_torres_defensiveTurret_cast_v1.ogg",
      "kra_sfx_heroes_torres_defensiveTurret_destroy_v1.ogg",
      "kra_sfx_heroes_torres_lunchBreak_v1.ogg",
      "kra_sfx_heroes_torres_menAtWork_v1.ogg",
      "kra_sfx_heroes_torres_wreckingBall_v1.ogg",
      "kra_heroes_nyru_basicAttack_melee_var1_op1_v1.ogg", -- reused for Torres' basic attack
    }
  },
  hero_mecha = {
    files = {
      "kr_voice_onagro_death_c.ogg",
      "kr_voice_onagro_taunt_a.ogg",
      "kr_voice_onagro_taunt02_a.ogg",
      "kr_voice_onagro_taunt03_e.ogg",
      "kr_voice_onagro_taunt04_c.ogg",
      "kra_sfx_heroes_goblidrone_attack_var1_v1.ogg",
      "kra_sfx_heroes_goblidrone_attack_var2_v1.ogg",
      "kra_sfx_heroes_goblidrone_attack_var3_v1.ogg",
      "kra_sfx_heroes_goblidrone_attack_var4_v1.ogg",
      "kra_sfx_heroes_goblidrone_cast_v1.ogg",
      "kra_sfx_heroes_mineDrop_cast_var1_v1.ogg",
      "kra_sfx_heroes_mineDrop_cast_var2_v1.ogg",
      "kra_sfx_heroes_mineDrop_cast_var3_v1.ogg",
      "kra_sfx_heroes_mineDrop_explosion_v2.ogg",
      "kra_sfx_heroes_onagro_basicAttack_cast_var1_v1.ogg",
      "kra_sfx_heroes_onagro_basicAttack_cast_var2_v1.ogg",
      "kra_sfx_heroes_onagro_basicAttack_cast_var3_v1.ogg",
      "kra_sfx_heroes_onagro_basicAttack_impact_var1_v1.ogg",
      "kra_sfx_heroes_onagro_basicAttack_impact_var2_v1.ogg",
      "kra_sfx_heroes_onagro_basicAttack_impact_var3_v1.ogg",
      "kra_sfx_heroes_onagro_deathFromAbove_attack_shot_var1_v1.ogg",
      "kra_sfx_heroes_onagro_deathFromAbove_attack_shot_var2_v1.ogg",
      "kra_sfx_heroes_onagro_deathFromAbove_attack_shot_var3_v1.ogg",
      "kra_sfx_heroes_onagro_deathFromAbove_attack_explosion_var1_v1.ogg",
      "kra_sfx_heroes_onagro_deathFromAbove_attack_explosion_var2_v1.ogg",
      "kra_sfx_heroes_onagro_deathFromAbove_attack_explosion_var3_v1.ogg",
      "kra_sfx_heroes_onagro_deathFromAbove_cast_v1.ogg",
      "kra_sfx_heroes_powerSlam_cast_v1.ogg",
      "kra_sfx_heroes_tarBomb_cast_var1_v1.ogg",
      "kra_sfx_heroes_tarBomb_cast_var2_v1.ogg",
      "kra_sfx_heroes_tarBomb_cast_var3_v1.ogg",
      "kra_sfx_heroes_tarBomb_explosion_v1.ogg",
    }
  },
  hero_lumenir = {
    files = {
      "kr_voice_lumenir_death_c.ogg",
      "kr_voice_lumenir_taunt_b.ogg",
      "kr_voice_lumenir_taunt02_c.ogg",
      "kr_voice_lumenir_taunt03_a.ogg",
      "kr_voice_lumenir_taunt04_a.ogg",
      "kra_sfx_heroes_lumenir_basicAttack_cast_var1_op2_v1.ogg",
      "kra_sfx_heroes_lumenir_basicAttack_cast_var2_op2_v1.ogg",
      "kra_sfx_heroes_lumenir_basicAttack_cast_var3_op2_v1.ogg",
      "kra_sfx_heroes_lumenir_blessingOfRetribution_cast_v1.ogg",
      "kra_sfx_heroes_lumenir_callOfTriumph_cast_var1_v1.ogg",
      "kra_sfx_heroes_lumenir_callOfTriumph_cast_var2_v1.ogg",
      "kra_sfx_heroes_lumenir_callOfTriumph_cast_var3_v1.ogg",
      "kra_sfx_heroes_lumenir_callOfTriumph_out_var1_v1.ogg",
      "kra_sfx_heroes_lumenir_callOfTriumph_out_var2_v1.ogg",
      "kra_sfx_heroes_lumenir_callOfTriumph_out_var3_v1.ogg",
      "kra_sfx_heroes_lumenir_celestialJudgement_cast_v1.ogg",
      "kra_sfx_heroes_lumenir_celestialJudgement_impact_v1.ogg",
      "kra_sfx_heroes_lumenir_radiantWave_cast_v1.ogg",
      "kra_sfx_heroes_lumenir_lightCompanion_cast_v1.ogg",
      --"kra_sfx_heroes_lumenir_death_v1.ogg",
      "kra_sfx_heroes_lumenir_lightCompanion_basicAttack_var1_v1.ogg",
      "kra_sfx_heroes_lumenir_lightCompanion_basicAttack_var2_v1.ogg",
      "kra_sfx_heroes_lumenir_lightCompanion_basicAttack_var3_v1.ogg",
      "kra_sfx_heroes_lumenir_callOfTriumph_cast_var1_v2.ogg",
      "kra_sfx_heroes_lumenir_callOfTriumph_cast_var2_v2.ogg",
      "kra_sfx_heroes_lumenir_callOfTriumph_cast_var3_v2.ogg",
      "kra_sfx_heroes_lumenir_celestialJudgement_impact_v2_op2.ogg",
    }
  },
  hero_venom = {
    files = {
      "kr_voice_grimson_taunt_b.ogg",
      "kr_voice_grimson_taunt02_b.ogg",
      "kr_voice_grimson_taunt03_b.ogg",
      "kr_voice_grimson_taunt04_c.ogg",
      "kr_voice_grimson_death_b.ogg",
      "kra_sfx_heroes_grimson_basicAttack_var1_v1.ogg",
      "kra_sfx_heroes_grimson_basicAttack_var2_v1.ogg",
      "kra_sfx_heroes_grimson_basicAttack_var3_v1.ogg",
      "kra_sfx_heroes_grimson_innerBeast_cast_v1.ogg",
      "kra_sfx_heroes_grimson_innerBeast_out_v1.ogg",
      "kra_sfx_heroes_grimson_deadlySpikes_cast_v1.ogg",
      "kra_sfx_heroes_grimson_deadlySpikes_out_v1.ogg",
      "kra_sfx_heroes_grimson_renewFlesh_cast_v1.ogg",
      "kra_sfx_heroes_grimson_creepingDeath_cast_v1.ogg",
      "kra_sfx_heroes_grimson_creepingDeath_spikes_v1.ogg",
      "kra_sfx_heroes_grimson_heartseeker_cast_v1.ogg",
    }
  },
  hero_robot = {
    files = {
      "kr_voice_warhead_death_c.ogg",
      "kr_voice_warhead_select_b.ogg",
      "kr_voice_warhead_taunt01_b.ogg",
      "kr_voice_warhead_taunt02_a.ogg",
      "kr_voice_warhead_taunt03_a.ogg",
      "kra_sfx_hero_warhead_deepImpact_cast_v1.ogg",
      "kra_sfx_hero_warhead_deepImpact_impact_v1.ogg",
      "kra_sfx_heroes_warhead_smokescreen_cast_wSomeScreen_v1.ogg",
      "kra_sfx_heroes_warhead_immolation_cast_v1.ogg",
      "kra_sfx_heroes_warhead_uppercut_cast_v1.ogg",
      "kra_sfx_heroes_warhead_jetpack_oneShot_v1.ogg",
      "kra_sfx_heroes_warhead_motorhead_cast-march_v1.ogg",
    }
  },
  hero_hunter = {
    files = {
      "kr_voice_anya_select_c.ogg",
      "kr_voice_anya_taunt_a.ogg",
      "kr_voice_anya_taunt02_a.ogg",
      "kr_voice_anya_taunt03_b.ogg",
      "kr_voice_anya_death_d.ogg",
      "kra_sfx_heroes_anya_basicAttack_var1_v1.ogg",
      "kra_sfx_heroes_anya_basicAttack_var2_v1.ogg",
      "kra_sfx_heroes_anya_basicAttack_var3_v1.ogg",
      "kra_sfx_heroes_anya_vampiricStrike_cast_var1_v1.ogg",
      "kra_sfx_heroes_anya_vampiricStrike_cast_var2_v1.ogg",
      "kra_sfx_heroes_anya_vampiricStrike_cast_var3_v1.ogg",
      "kra_sfx_heroes_anya_mistyStep_cast_v1.ogg",
      "kra_sfx_heroes_anya_mistyStep_bounce_op1_v1.ogg",
      "kra_sfx_heroes_anya_argentStorm_shot_op1_v1.ogg",
      "kra_sfx_heroes_anya_argentStorm_fadeOut_v1.ogg",
      "kra_sfx_heroes_anya_duskBeasts_cast_v1.ogg",
      "kra_sfx_heroes_anya_huntersAid_cast_v1.ogg",
      "kra_sfx_heroes_anya_huntersAid_attack_op2_var1_v1.ogg",
      "kra_sfx_heroes_anya_huntersAid_attack_op2_var2_v1.ogg",
      "kra_sfx_heroes_anya_huntersAid_attack_op2_var3_v1.ogg",
    }
  },

  hero_dragon_gem = {
    files = {
      "kr_voice_kosmyr_select_c.ogg",
      "kr_voice_kosmyr_taunt01_a.ogg",
      "kr_voice_kosmyr_taunt02_a.ogg",
      "kr_voice_kosmyr_taunt03_a.ogg",
      "kr_voice_kosmyr_death_b.ogg",
      "kra_sfx_heroes_kosmyr_basicAttack_cast_var1_v1.ogg",
      "kra_sfx_heroes_kosmyr_basicAttack_cast_var2_v1.ogg",
      "kra_sfx_heroes_kosmyr_basicAttack_cast_var3_v1.ogg",
      "kra_sfx_heroes_kosmyr_basicAttack_impact_var1_v1.ogg",
      "kra_sfx_heroes_kosmyr_basicAttack_impact_var2_v1.ogg",
      "kra_sfx_heroes_kosmyr_basicAttack_impact_var3_v1.ogg",
      "kra_sfx_heroes_kosmyr_prismaticShard_cast_v1.ogg",
      "kra_sfx_heroes_kosmyr_prismaticShard_ripple_var1_v1.ogg",
      "kra_sfx_heroes_kosmyr_prismaticShard_ripple_var2_v1.ogg",
      "kra_sfx_heroes_kosmyr_prismaticShard_ripple_var3_v1.ogg",
      "kra_sfx_heroes_kosmyr_prismaticShard_ripple_var4_v1.ogg",
      "kra_sfx_heroes_kosmyr_paralyzingBreath_Cast_v1.ogg",
      "kra_sfx_heroes_kosmyr_redDeath_cast_v1.ogg",
      "kra_sfx_heroes_kosmyr_redDeath_explosion_v1.ogg",
      "kra_sfx_heroes_kosmyr_powerConduit_cast_shot_v1.ogg",
      "kra_sfx_heroes_kosmyr_powerConduit_crystal_op1_v1.ogg",
      "kra_sfx_heroes_kosmyr_crystalAvalanch_cast_var1_v1.ogg",
      "kra_sfx_heroes_kosmyr_crystalAvalanch_cast_var2_v1.ogg",
      "kra_sfx_heroes_kosmyr_crystalAvalanch_cast_var3_v1.ogg",
      "kra_sfx_heroes_kosmyr_crystalAvalanch_cast_var4_v1.ogg",
    }
  },

  hero_bird = {
    files = {
      "kr_voice_broden_select_b.ogg",
      "kr_voice_broden_taunt01_c.ogg",
      "kr_voice_broden_taunt02_a.ogg",
      "kr_voice_broden_taunt03_d.ogg",
      "kr_voice_broden_death_a.ogg",
      "kra_sfx_heroes_broden_basicAttack_cast_var1_v1.ogg",
      "kra_sfx_heroes_broden_basicAttack_cast_var2_v1.ogg",
      "kra_sfx_heroes_broden_basicAttack_cast_var3_v1.ogg",
      "kra_sfx_heroes_broden_basicAttack_impact_var1_v1.ogg",
      "kra_sfx_heroes_broden_basicAttack_impact_var2_v1.ogg",
      "kra_sfx_heroes_broden_basicAttack_impact_var3_v1.ogg",
      "kra_sfx_heroes_broden_carpetBombing_cast_v1.ogg",
      "kra_sfx_heroes_broden_carpetBombing_impact_v1.ogg",
      "kra_sfx_heroes_broden_terrorShriek_cast_v1.ogg",
      "kra_sfx_heroes_broden_bulletRain_cast_v1.ogg",
      "kra_sfx_heroes_broden_bulletRain_loopEnd.ogg",
      "kra_sfx_heroes_broden_huntingDive_cast_v1.ogg",
      "kra_sfx_heroes_broden_birdsOfPrey_cast_op2_v1.ogg",
      "kra_sfx_heroes_broden_birdsOfPrey_gryphonAttack_var1_v1.ogg",
      "kra_sfx_heroes_broden_birdsOfPrey_gryphonAttack_var2_v1.ogg",
      "kra_sfx_heroes_broden_birdsOfPrey_gryphonAttack_var3_v1.ogg",
      "kra_sfx_heroes_broden_birdsOfPrey_gryphonAttack_var4_v1.ogg",
    }
  },

  hero_witch = {
    files = {
      "kr_voice_stregi_death_b.ogg",
      "kr_voice_stregi_select_b.ogg",
      "kr_voice_stregi_taunt-01_a.ogg",
      "kr_voice_stregi_taunt-02_d.ogg",
      "kr_voice_stregi_taunt-03_c.ogg",
      "kra_sfx_heroes_stregi_basicAttack_cast_var1_v1.ogg",
      "kra_sfx_heroes_stregi_basicAttack_cast_var2_v1.ogg",
      "kra_sfx_heroes_stregi_basicAttack_cast_var3_v1.ogg",
      "kra_sfx_heroes_stregi_dazzlingDecoy_cast_v1.ogg",
      "kra_sfx_heroes_stregi_dazzlingDecoy_explosion_v1.ogg",
      "kra_sfx_heroes_stregi_nightFuries_cast_v1.ogg",
      "kra_sfx_heroes_stregi_veggiefy_in_v1.ogg",
      "kra_sfx_heroes_stregi_veggiefy_out_v1.ogg",
      "kra_sfx_heroes_squishNSquash_cast_v1.ogg",
      "kra_sfx_heroes_squishNSquash_impact_v1.ogg",
      "kra_heroes_nyru_basicAttack_melee_var1_op1_v1.ogg",
      "kra_sfx_heroes_stregi_drowsyReturn_in_v1.ogg",
      "kra_sfx_heroes_stregi_drowsyReturn_out_v1.ogg",
      "kra_sfx_heroes_stregi_veggiefy_death_var1_v1.ogg",
      "kra_sfx_heroes_stregi_veggiefy_death_var2_v1.ogg",
      "kra_sfx_heroes_stregi_veggiefy_death_var3_v1.ogg",
    }
  },

  hero_dragon_bone = {
    files = {
      "kr_voice_bonehart_select_c.ogg",
      "kr_voice_bonehart_taunt-01_b.ogg",
      "kr_voice_bonehart_taunt-02_a.ogg",
      "kr_voice_bonehart_taunt-03_c.ogg",
      "kr_voice_bonehart_death_a.ogg",
      "kra_sfx_heroes_bonehart_basicAttack_cast_var1.ogg",
      "kra_sfx_heroes_bonehart_basicAttack_cast_var2.ogg",
      "kra_sfx_heroes_bonehart_basicAttack_cast_var3.ogg",
      "kra_sfx_heroes_bonehart_basicAttack_impact_var1.ogg",
      "kra_sfx_heroes_bonehart_basicAttack_impact_var2.ogg",
      "kra_sfx_heroes_bonehart_basicAttack_impact_var3.ogg",
      "kra_sfx_heroes_bonehart_diseaseNova_cast_v1.ogg",
      "kra_sfx_heroes_bonehart_plagueCloud_cast_v1.ogg",
      "kra_sfx_heroes_bonehart_spineRain_cast_v1.ogg",
      "kra_sfx_heroes_bonehart_spineRain_impact_var1_v1.ogg",
      "kra_sfx_heroes_bonehart_spineRain_impact_var2_v1.ogg",
      "kra_sfx_heroes_bonehart_spineRain_impact_var3_v1.ogg",
      "kra_sfx_heroes_bonehart_spreadingBurst_cast_v1.ogg",
      "kra_sfx_heroes_bonehart_spreadingBurst_impact_var1_v1.ogg",
      "kra_sfx_heroes_bonehart_spreadingBurst_impact_var2_v1.ogg",
      "kra_sfx_heroes_bonehart_spreadingBurst_impact_var3_v1.ogg",
      "kra_sfx_heroes_bonehart_raiseDrakes_cast_op2_v1.ogg"
    }
  },

  hero_dragon_arb = {
    files = {
      "kr_voice_silvara_death_c.ogg",
      "kr_voice_silvara_move2_a.ogg",
      "kr_voice_silvara_move3_b.ogg",
      "kr_voice_silvara_move4_b.ogg",
      "kra_sfx_crocs_hero_spawned_unit_shield_hit_var1_v1.ogg",
      "kra_sfx_crocs_hero_spawned_unit_shield_hit_var2_v1.ogg",
      "kra_sfx_crocs_hero_spawned_unit_shield_hit_var3_v1.ogg",
      "kra_sfx_crocs_hero_throws_spikes_var1_v1.ogg",
      "kra_sfx_crocs_hero_throws_spikes_var2_v1.ogg",
      "kra_sfx_crocs_hero_throws_spikes_var3_v1.ogg",
      "kra_sfx_crocs_hero_throws_spikes_var4_v1.ogg",
      "kra_sfx_crocs_hero_ultimate_blue_v1.ogg",
    }
  },

  hero_lava = {
    files = {
      "kr_voice_kratoa_taunt02_b.ogg",
      "kr_voice_kratoa_taunt03_b.ogg",
      "kr_voice_kratoa_taunt04_c.ogg",
      "kr_voice_kratoa_taunt-select_c.ogg",
      "kr_voice_kratoa_death_b.ogg",
      "kra_sfx_heroes_kratoa_basicAttack_cast_var1_v1.ogg",
      "kra_sfx_heroes_kratoa_basicAttack_cast_var2_v1.ogg",
      "kra_sfx_heroes_kratoa_basicAttack_cast_var3_v1.ogg",
      "kra_sfx_heroes_temperTantrum_cast_v1.ogg",
      "kra_sfx_heroes_kratoa_hotheaded_cast_v1.ogg",
      "kra_sfx_heroes_kratoa_doubleTrouble_cast_op1_v1.ogg",
      "kra_sfx_heroes_kratoa_doubleTrouble_impact_v1.ogg",
      "kra_sfx_heroes_wildEruption_cast_v1.ogg",
      "kra_sfx_heroes_kratoa_rageOutburst_cast_var2_v1.ogg",
      "kra_sfx_heroes_kratoa_rageOutburst_cast_var2_v1.ogg",
      "kra_sfx_heroes_kratoa_rageOutburst_cast_var3_v1.ogg",
      "kra_sfx_heroes_kratoa_rageOutburst_impact_var2_v1.ogg",
      "kra_sfx_heroes_kratoa_rageOutburst_impact_var2_v1.ogg",
      "kra_sfx_heroes_kratoa_rageOutburst_impact_var3_v1.ogg",
      "kra_sfx_heroes_kratoa_rageOutburst_death_v1.ogg",
    }
  },

  hero_spider = {
    files = {
      "kr_voice_spydyr_01a.ogg",
      "kr_voice_spydyr_03b.ogg",
      "kr_voice_spydyr_04d.ogg",
      "kr_voice_spydyr_death_b.ogg",
      "kr_voice_spydyr_taunt-select_b.ogg",
      "kra_sfx_spiders_heroe_areadamage_v1.ogg",
      "kra_sfx_spiders_heroe_global_cocoons_v1.ogg",
      "kra_sfx_spiders_heroe_global_spawn_v1.ogg",
      "kra_sfx_spiders_heroe_instakill_v1.ogg",
      "kra_sfx_spiders_heroe_melee_op1_var1_v1.ogg",
      "kra_sfx_spiders_heroe_melee_op1_var2_v1.ogg",
      "kra_sfx_spiders_heroe_melee_op1_var3_v1.ogg",
      "kra_sfx_spiders_heroe_range_var1_v1.ogg",
      "kra_sfx_spiders_heroe_range_var2_v1.ogg",
      "kra_sfx_spiders_heroe_range_var3_v1.ogg",
      "kra_sfx_spiders_heroe_supremehunter_fullSeq_v1.ogg",
      "kra_sfx_spiders_heroe_tunneling_appear_v1.ogg",
      "kra_sfx_spiders_heroe_tunneling_in_v1.ogg",
      "kra_sfx_spiders_heroe_tunneling_out_v1.ogg",
    }
  },

  hero_wukong = {
    files = {
      "kr_voice_sunwukong_1_c.ogg",
      "kr_voice_sunwukong_2_b.ogg",
      "kr_voice_sunwukong_3_b.ogg",
      "kr_voice_sunwukong_4_a.ogg",
      "kr_voice_sunwukong_5_c.ogg",
      "kr_voice_CN_wukong_are_there_any_peaches_in_your_land_1.ogg",
      "kr_voice_CN_wukong_grandpa_sun_will_be_right_back_2.ogg",
      "kr_voice_CN_wukong_here_comes_your_grandpa_sun_1.ogg",
      "kr_voice_CN_wukong_where_are_you_going_demon_2.ogg",
      "kr_voice_CN_wukong_vivid_and_monkey-like_2.ogg",
      "kra_sfx_wukong_hero_death_op3_v1.ogg",
	    "kra_sfx_wukong_hero_hair_clones_v1.ogg",
	    "kra_sfx_wukong_hero_instakill_v2.ogg",
	    "kra_sfx_wukong_hero_melee_fast_hits_v1.ogg",
	    "kra_sfx_wukong_hero_melee_jump_v1.ogg",
	    "kra_sfx_wukong_hero_melee_simple_v1.ogg",
	    "kra_sfx_wukong_hero_melee_spin_v1.ogg",
	    "kra_sfx_wukong_hero_multi_staf_op1_v1.ogg",
	    "kra_sfx_wukong_hero_ultimate_v1.ogg",
	    "kra_sfx_wukong_hero_zhu_smash_v1.ogg",
    }
  },

  tower_royal_archers = {
    files = {
      "kr_voice_royalArchers_skill_a_var1a.ogg",
      "kr_voice_royalArchers_skill_b_var1b.ogg",
      "kr_voice_royalArchers_taunt_var1c.ogg",
      "kr_voice_royalArchers_taunt2_var1a.ogg",
      "kr_voice_royalArchers_taunt3_var1a.ogg",
      "kra_sfx_tower_royalArchers_skill_armorPiercer_v1.ogg",
      "kra_sfx_tower_royalArchers_skill_impact_v1.ogg",
      "kra_sfx_tower_royalArchers_skill_rapaciousHunter_takeOff_v1.ogg",
      "kra_sfx_tower_royalArchers_skill_strike_var1_v1.ogg",
      "kra_sfx_tower_royalArchers_skill_strike_var2_v1.ogg",
      "kra_sfx_tower_royalArchers_skill_rapaciousHunter_impact_var1_v1.ogg",
      "kra_sfx_tower_royalArchers_skill_rapaciousHunter_impact_var2_v1.ogg"
    }
  },
  tower_arcane_wizard = {
    files = {
      "kr_voice_arcanewizard_skill_a_var1a.ogg",
      "kr_voice_arcanewizard_skill_b_var1a.ogg",
      "kr_voice_arcanewizard_taunt_var1c.ogg",
      "kr_voice_arcanewizard_taunt2_var1a.ogg",
      "kr_voice_arcanewizard_taunt3_var1b.ogg",
      "kra_sfx_tower_arcaneWizard_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_arcaneWizard_basicAttack_var2_v1.ogg",
      "kra_sfx_tower_arcaneWizard_basicAttack_var3_v1.ogg",
      "kra_sfx_tower_arcaneWizard_skill_disintegration_v1.ogg",
      "kra_sfx_tower_arcaneWizard_skill_empowerment_v1.ogg",
    }
  },
  tower_tricannon = {
    files = {
      "kr_voice_tricannon_taunt3_var1a.ogg",
      "kr_voice_tricannon_taunt2_var1c.ogg",
      "kr_voice_tricannon_taunt_var1a.ogg",
      "kr_voice_tricannon_skill_b_var1a.ogg",
      "kr_voice_tricannon_skill_a_var1c.ogg",
      "kra_sfx_tower_tricannon_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_tricannon_basicAttack_var2_v1.ogg",
      "kra_sfx_tower_tricannon_basicAttack_var3_v1.ogg",
      "kra_sfx_tower_tricannon_basicAttack_impact-single_var1_v1.ogg",
      "kra_sfx_tower_tricannon_basicAttack_impact-single_var2_v1.ogg",
      "kra_sfx_tower_tricannon_basicAttack_impact-single_var3_v1.ogg",
      "kra_sfx_tower_tricannon_skill_bombardment_lvl1_v1.ogg",
      "kra_sfx_tower_tricannon_skill_bombardment_lvl2_v1.ogg",
      "kra_sfx_tower_tricannon_skill_bombardment_lvl3_v1.ogg",
      "kra_sfx_tower_tricannon_skill_overheat-oneshot_v1.ogg",
    }
  },
  tower_paladin_covenant = {
    files = {
      "kr_voice_paladincovenant_taunt3_var1a.ogg",
      "kr_voice_paladincovenant_taunt2_var1a.ogg",
      "kr_voice_paladincovenant_taunt_var1a.ogg",
      "kr_voice_paladincovenant_skill_b_var1b.ogg",
      "kr_voice_paladincovenant_skill_a_var1a.ogg",
      "kra_sfx_tower_paladinCovenant_deploy_v1.ogg",
      "kra_sfx_tower_paladinCovenant_skill_healingPrayer_v1.ogg",
      "kra_sfx_tower_paladinCovenant_skill_leadByExampleAura_v1.ogg",
      "kra_sfx_tower_paladinCovenant_unitDeath_var1_v1.ogg",
      "kra_sfx_tower_paladinCovenant_unitDeath_var2_v1.ogg",
      "kra_sfx_tower_paladinCovenant_unitDeath_var3_v1.ogg",
    }
  },
  tower_demon_pit = {
    files = {
      "kr_voice_demonpit_taunt3_var1a.ogg",
      "kr_voice_demonpit_taunt2_var1c.ogg",
      "kr_voice_demonpit_taunt_var1a.ogg",
      "kr_voice_demonpit_skill_b_var1a.ogg",
      "kr_voice_demonpit_skill_a_var1c.ogg",
      "kra_sfx_tower_demonPit_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_demonPit_basicAttack_var2_v1.ogg",
      "kra_sfx_tower_demonPit_basicAttack_var3_v1.ogg",
      "kra_sfx_tower_demonPit_demonExplosion_v1.ogg",
      "kra_sfx_tower_demonPit_bigGuy_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_demonPit_bigGuy_basicAttack_var2_v1.ogg",
      "kra_sfx_tower_demonPit_bigGuy_basicAttack_var3_v1.ogg",
    }
  },
  tower_arborean_emissary = {
    files = {
      "kr_voice_arboreanemissary_taunt3_var1a.ogg",
      "kr_voice_arboreanemissary_taunt2_var1a.ogg",
      "kr_voice_arboreanemissary_taunt_var1a.ogg",
      "kr_voice_arboreanemissary_skill_b_var1a.ogg",
      "kr_voice_arboreanemissary_skill_a_var1b.ogg",
      "kra_sfx_tower_arboreanEmissary_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_arboreanEmissary_basicAttack_var3_v1.ogg",
      "kra_sfx_tower_arboreanEmissary_basicAttack_var2_v1.ogg",
      "kra_sfx_tower_arboreanEmissary_thornyGarden_spawn_var3_v1.ogg",
      "kra_sfx_tower_arboreanEmissary_thornyGarden_spawn_var2_v1.ogg",
      "kra_sfx_tower_arboreanEmissary_thornyGarden_spawn_var1_v1.ogg",
      "kra_sfx_tower_arboreanEmissary_giftOfNature_cast_v1.ogg",
    }
  },
  tower_elven_stargazers = {
    files = {
      "kr_voice_elvenstargazers_taunt01_d.ogg",
      "kr_voice_elvenstargazers_taunt02_d.ogg",
      "kr_voice_elvenstargazers_taunt03_c.ogg",
      "kr_voice_elvenstargazers_skill_a_c.ogg",
      "kr_voice_elvenstargazers_skill_b_d.ogg",
      "kra_sfx_tower_elvenStargazer_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_elvenStargazer_basicAttack_var2_v1.ogg",
      "kra_sfx_tower_elvenStargazer_basicAttack_var3_v1.ogg",
      "kra_sfx_tower_elvenStargazer_basicAttack_var4_v1.ogg",
      "kra_sfx_tower_elvenStargazer_risingStar_impact_var1_v1.ogg",
      "kra_sfx_tower_elvenStargazer_risingStar_impact_var2_v1.ogg",
      "kra_sfx_tower_elvenStargazer_risingStar_impact_var3_v1.ogg",
      "kra_sfx_tower_elvenStargazer_eventHorizon_cast_v1.ogg",
      "kra_sfx_tower_elvenStargazer_eventHorizon_teleportIn_v1.ogg",
      "kra_sfx_tower_elvenStargazer_eventHorizon_teleportOut_v1.ogg",

    }
  },
  tower_ballista = {
    files = {
      "kr_voice_ballistaoutpost_taunt01_d.ogg",
      "kr_voice_ballistaoutpost_taunt02_c.ogg",
      "kr_voice_ballistaoutpost_taunt03_b.ogg",
      "kr_voice_ballistaoutpost_skill_a_b.ogg",
      "kr_voice_ballistaoutpost_skill_b_b.ogg",
      "kra_sfx_tower_ballistaOutpost_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_ballistaOutpost_basicAttack_var2_v1.ogg",
      "kra_sfx_tower_ballistaOutpost_basicAttack_var3_v1.ogg",
      "kra_sfx_tower_ballistaOutpost_basicAttack_var4_v1.ogg",
      "kra_sfx_tower_ballistaOutpost_basicAttack_var5_v1.ogg",
      "kra_sfx_tower_ballistaOutpost_scrapBomb_cast_var1_v1.ogg",
      "kra_sfx_tower_ballistaOutpost_scrapBomb_cast_var2_v1.ogg",
      "kra_sfx_tower_ballistaOutpost_scrapBomb_cast_var3_v1.ogg",
      "kra_sfx_tower_ballistaOutpost_scrapBomb_explosion_var1_v1.ogg",
      "kra_sfx_tower_ballistaOutpost_scrapBomb_explosion_var2_v1.ogg",
      "kra_sfx_tower_ballistaOutpost_finalNail_v1.ogg",
    }
  },
  tower_necromancer = {
    files = {
      "kr_voice_necromancerslair_taunt01_c.ogg",
      "kr_voice_necromancerslair_taunt02_a.ogg",
      "kr_voice_necromancerslair_taunt03_c.ogg",
      "kr_voice_necromancerslair_skill-a_c.ogg",
      "kr_voice_necromancerslair_skill-b_a.ogg",
      "kra_sfx_tower_necromancer_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_necromancer_basicAttack_var2_v1.ogg",
      "kra_sfx_tower_necromancer_basicAttack_var3_v1.ogg",
      "kra_sfx_tower_necromancer_skeletonSummon_v2.ogg",
      "kra_sfx_tower_necromancer_deathRider_op1_v1.ogg",
      "kra_sfx_tower_necromancer_sigilOfSilence_v1.ogg",
      "kra_sfx_tower_necromancer_basicAttack_hit_var1_v1.ogg",
      "kra_sfx_tower_necromancer_basicAttack_hit_var2_v1.ogg",
      "kra_sfx_tower_necromancer_basicAttack_hit_var3_v1.ogg",
      "kra_sfx_tower_necromancer_basicAttack_boltSummon_var1_v1.ogg",
      "kra_sfx_tower_necromancer_basicAttack_boltSummon_var2_v1.ogg",
      "kra_sfx_tower_necromancer_basicAttack_boltSummon_var3_v1.ogg",
    }
  },
  tower_rocket_gunners = {
    files = {
      "kr_voice_rocketgunners_taunt01_c.ogg",
      "kr_voice_rocketgunners_taunt02_a.ogg",
      "kr_voice_rocketgunners_taunt03_a.ogg",
      "kr_voice_rocketgunners_skill-a_c.ogg",
      "kr_voice_rocketgunners_skill-b_c.ogg",
      "kr_voice_rocketgunners_touchdown_c.ogg",
      "kr_voice_rocketgunners_liftoff_b.ogg",
      "kra_sfx_tower_rocketGunners_unitSpawn_v1.ogg",
      "kra_sfx_tower_rocketGunners_basicAttack_var1_1.ogg",
      "kra_sfx_tower_rocketGunners_basicAttack_var2_1.ogg",
      "kra_sfx_tower_rocketGunners_basicAttack_var3_1.ogg",
      "kra_sfx_tower_rocketGunners_unitSpawn_v1.ogg",
      "kra_sfx_tower_rocketGunners_takeoff_v1.ogg",
      "kra_sfx_tower_rocketGunners_stingMissile_cast_v1.ogg",
      "kra_sfx_tower_rocketGunners_stingMissile_explosion_v1.ogg",
      "kra_sfx_tower_rocketGunners_phosphoricCoating_var1_v1.ogg",
      "kra_sfx_tower_rocketGunners_phosphoricCoating_var2_v1.ogg",
      "kra_sfx_tower_rocketGunners_phosphoricCoating_var3_v1.ogg",
    }
  },

  tower_flamespitter = {
    files = {
      "kr_voice_dwarvenflamespitter_taunt01_c.ogg",
      "kr_voice_dwarvenflamespitter_taunt02_a.ogg",
      "kr_voice_dwarvenflamespitter_taunt03_a.ogg",
      "kr_voice_dwarvenflamespitter_skill-a_b.ogg",
      "kr_voice_dwarvenflamespitter_skill-b_d.ogg",
      "kra_sfx_tower_dwarvenFlamespitter_basicAttack_cast_var1_v1.ogg",
      "kra_sfx_tower_dwarvenFlamespitter_basicAttack_cast_var2_v1.ogg",
      "kra_sfx_tower_dwarvenFlamespitter_basicAttack_cast_var3_v1.ogg",
      "kra_sfx_tower_dwarvenFlamespitter_blazingTrail_cast_v1.ogg",
      "kra_sfx_tower_dwarvenFlamespitter_blazingTrail_impact_v1.ogg",
      "kra_sfx_tower_dwarvenFlamespitter_scorchingTorches_cast_v1.ogg",
      "kra_sfx_tower_dwarvenFlamespitter_scorchingTorches_flareUp_var1_v1.ogg",
      "kra_sfx_tower_dwarvenFlamespitter_scorchingTorches_flareUp_var2_v1.ogg",
      "kra_sfx_tower_dwarvenFlamespitter_scorchingTorches_flareUp_var3_v1.ogg",
      "kra_sfx_tower_dwarvenFlamespitter_scorchingTorches_flareUp_var4_v1.ogg",
    }
  },

  tower_barrel = {
    files = {
      "kr_voice_battlebrewmasters_select_c.ogg",
      "kr_voice_battlebrewmasters_taunt01_d.ogg",
      "kr_voice_battlebrewmasters_taunt02_a.ogg",
      "kr_voice_battlebrewmasters_skill-a_b.ogg",
      "kr_voice_battlebrewmasters_skill-b_d.ogg",
      "kra_sfx_battleBrewmasters_basicAttack_cast_var1_v1.ogg",
      "kra_sfx_battleBrewmasters_basicAttack_cast_var2_v1.ogg",
      "kra_sfx_battleBrewmasters_basicAttack_cast_var3_v1.ogg",
      "kra_sfx_tower_brewMaster_basicAttack_impact_var1_v1.ogg",
      "kra_sfx_tower_brewMaster_basicAttack_impact_var2_v1.ogg",
      "kra_sfx_tower_brewMaster_basicAttack_impact_var3_v1.ogg",
      "kra_sfx_tower_brewMaster_badBatch_rattle_v1.ogg",
      "kra_sfx_tower_brewMaster_badBatch_explosion_v1.ogg",
      "kra_sfx_tower_brewMaster_elixirOfMight_evict_v1.ogg",
      "kra_sfx_tower_brewMaster_elixirOfMight_drinkAndBoost_v1.ogg",
    }
  },

  tower_sand = {
    files = {
      "kr_voice_dunesentinels_select_e.ogg",
      "kr_voice_dunesentinels_taunt01_b.ogg",
      "kr_voice_dunesentinels_taunt02_d.ogg",
      "kr_voice_dunesentinels_skill-a_d.ogg",
      "kr_voice_dunesentinels_skill-b_c.ogg",
      "kra_sfx_tower_duneSentinels_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_duneSentinels_basicAttack_var2_v1.ogg",
      "kra_sfx_tower_duneSentinels_basicAttack_var3_v1.ogg",
      "kra_sfx_tower_duneSentinels_basicAttack_var4_v1.ogg",
      "kra_sfx_tower_duneSentinels_basicAttack_var5_v1.ogg",
      "kra_sfx_tower_duneSentinels_bountyHunt_cast_v1.ogg",
      "kra_sfx_tower_duneSentinels_whirlingDoom_cast_v1.ogg",
    }
  },

  tower_ghost = {
    files = {
      "kr_voice_grimwraiths_select_c.ogg",
      "kr_voice_grimwraiths_taunt01_c.ogg",
      "kr_voice_grimwraiths_taunt02_c.ogg",
      "kr_voice_grimwraiths_skill-a_b.ogg",
      "kr_voice_grimwraiths_skill-b_d.ogg",
      "kra_sfx_tower_grimWraiths_soulSiphoning_cast_v1.ogg",
      "kra_sfx_tower_grimWraiths_undyingDread_travel_v1.ogg",
      "kra_sfx_tower_grimWraiths_undyingDread_impact_v1.ogg",
      "kra_sfx_tower_grimWraiths_teleport_out-in_v1.ogg",
      "kra_sfx_tower_grimWraiths_spawnUnit_var1_v1.ogg",
      "kra_sfx_tower_grimWraiths_spawnUnit_var2_v1.ogg",
      "kra_sfx_tower_grimWraiths_spawnUnit_var3_v1.ogg",
    }
  },

  tower_ray = {
    files = {
      "kr_voice_eldrictchchannelers_select_[2]a.ogg",
      "kr_voice_eldrictchchannelers_taunt01_b.ogg",
      "kr_voice_eldrictchchannelers_taunt02_[2]b.ogg",
      "kr_voice_eldrictchchannelers_skill-a_[2]c.ogg",
      "kr_voice_eldrictchchannelers_skill-b_b.ogg",
      "kra_sfx_tower_eldrictchChannelers_basicAttack_long_var1_v1.ogg",
      "kra_sfx_tower_eldrictchChannelers_basicAttack_long_var2_v1.ogg",
      "kra_sfx_tower_eldrictchChannelers_basicAttack_long_var3_v1.ogg",
      "kra_sfx_tower_eldrictchChannelers_basicAttack_offset_v1.ogg",
      "kra_sfx_tower_eldrictchChannelers_mutationHex_cast_var1_v1.ogg",
      "kra_sfx_tower_eldrictchChannelers_mutationHex_cast_var3_v1.ogg",
      "kra_sfx_enemy_sheep_death_var1_v1.ogg",
      "kra_sfx_enemy_sheep_death_var2_v1.ogg",
      "kra_sfx_enemy_sheep_death_var3_v1.ogg",
    }
  },

  tower_dark_elf = {
    files = {
      "kr_voice_twilightlongbows_select_b.ogg",
      "kr_voice_twilightlongbows_skill-a_g.ogg",
      "kr_voice_twilightlongbows_skill-b_d.ogg",
      "kr_voice_twilightlongbows_taunt-01_a.ogg",
      "kr_voice_twilightlongbows_taunt-02_b.ogg",
      "kra_sfx_tower_twlightLongbows_basicAttack_cast-noCharge_var1_v1.ogg",
      "kra_sfx_tower_twlightLongbows_basicAttack_cast-noCharge_var2_v1.ogg",
      "kra_sfx_tower_twlightLongbows_basicAttack_cast-noCharge_var3_v1.ogg",
      "kra_sfx_tower_twlightLongbows_supportBlades_spawn_v1.ogg",
      "kra_sfx_tower_twlightLongbows_thrillOfTheHunt_cast-travelOnly_v1.ogg",
    }
  },

  tower_hermit_toad = {
    files = {
      "kr_voice_boghermit_build_a.ogg",
      "kr_voice_boghermit_build2_b.ogg",
      "kr_voice_boghermit_build3_c.ogg",
      "kr_voice_boghermit_power1_c.ogg",
      "kr_voice_boghermit_power2_c.ogg",
      "kr_voice_boghermit_switchtoartillery_c.ogg",
      "kr_voice_boghermit_switchtomage_c.ogg",
      "kra_sfx_crocs_tower_pipe_shoot_magic_var1_v1.ogg",
      "kra_sfx_crocs_tower_pipe_shoot_water_impact_var3_v1.ogg",
      "kra_sfx_crocs_tower_pipe_shoot_water_var3_v1.ogg",
      "kra_sfx_crocs_tower_stomp_path_backToPond_v1.ogg",
      "kra_sfx_crocs_tower_stomp_path_jumpOut_v1.ogg",
      "kra_sfx_crocs_tower_stomp_path_v1.ogg",
      "kra_sfx_crocs_tower_tonge_shoot_v1.ogg",
    }
  },

  tower_dwarf = {
    files = {
      "kr_voice_cannoneersquad_taunt-select_e.ogg",
      "kr_voice_cannoneersquad_taunt02_b.ogg",
      "kr_voice_cannoneersquad_taunt03_c.ogg",
      "kr_voice_cannoneersquad_skill-a_c.ogg",
      "kr_voice_cannoneersquad_skill-b_c.ogg",
      "kra_sfx_towers_cannoneers_basicAttack_var1_v1.ogg",
      "kra_sfx_towers_cannoneers_basicAttack_var2_v1.ogg",
      "kra_sfx_towers_cannoneers_basicAttack_var3_v1.ogg",
      "kra_sfx_towers_cannoneers_incendiaryAmmo_impact_var1_v1.ogg",
      "kra_sfx_towers_cannoneers_incendiaryAmmo_impact_var2_v1.ogg",
      "kra_sfx_towers_cannoneers_incendiaryAmmo_impact_var3_v1.ogg",
      "kra_sfx_towers_cannoneers_jump_cast_v1.ogg",
      "kra_sfx_towers_cannoneers_death_var1.ogg",
      "kra_sfx_towers_cannoneers_death_var2.ogg",
      "kra_sfx_towers_cannoneers_death_var3.ogg",
    }
  },

  tower_sparking_geode = {
    files = {
      "kr_voice_surgecolossus_02c.ogg",
      "kr_voice_surgecolossus_03c.ogg",
      "kr_voice_surgecolossus_04c.ogg",
      "kr_voice_surgecolossus_05c.ogg",
      "kr_voice_surgecolossus_taunt-select_c.ogg",
      "kra_sfx_spiders_tower_heode_cristalize_ONESHOT_v1.ogg",
      "kra_sfx_spiders_tower_heode_cristalize_bolt_var1_v1.ogg",
      "kra_sfx_spiders_tower_heode_cristalize_bolt_var2_v1.ogg",
      "kra_sfx_spiders_tower_heode_cristalize_bolt_var3_v1.ogg",
      "kra_sfx_spiders_tower_heode_cristalize_cast_var3_v1.ogg",
      "kra_sfx_spiders_tower_heode_ray_var1_v1.ogg",
      "kra_sfx_spiders_tower_heode_ray_var2_v1.ogg",
      "kra_sfx_spiders_tower_heode_ray_var3_v1.ogg",
      "kra_sfx_spiders_tower_heode_ray_var4_v1.ogg",
      "kra_sfx_spiders_tower_heode_ray_var5_v1.ogg",
      "kra_sfx_spiders_tower_heode_spike_cast_v1.ogg",
      "kra_sfx_spiders_tower_heode_spike_sparksLOOP_v1.ogg",
  }
},

tower_pandas = {
  files = {
    "kr_voice_pandatower_taunt01_f.ogg",
    "kr_voice_pandatower_taunt02_b.ogg",
    "kr_voice_pandatower_taunt03_c.ogg",
    "kr_voice_pandatower_thunderskill_a.ogg",
    "kr_voice_pandatower_fieryskill_b.ogg",
    "kr_voice_pandatower_hatskill_a.ogg",
    "kr_voice_CN_pandas_panda_style_3.ogg",
    "kr_voice_CN_pandas_no_charge_for_awesome_2.ogg",
    "kr_voice_CN_pandas_we_know_kung-fu_2.ogg",
    "kr_voice_CN_pandas_ayumbabayeee_1.ogg",
    "kr_voice_CN_pandas_watch_and_see_3.ogg",
    "kr_voice_CN_pandas_get_over_there_2.ogg",
    "kra_sfx_wukong_tower_pandas_arrival_single_var1_v1.ogg",
    "kra_sfx_wukong_tower_pandas_arrival_single_var2_v1.ogg",
    "kra_sfx_wukong_tower_pandas_arrival_single_var3_v1.ogg",
    "kra_sfx_wukong_tower_pandas_death_generic_var1_v1.ogg",
    "kra_sfx_wukong_tower_pandas_death_generic_var2_v1.ogg",
    "kra_sfx_wukong_tower_pandas_death_generic_var3_v1.ogg",
    "kra_sfx_wukong_tower_pandas_ranged_bolt_var1_v1.ogg",
    "kra_sfx_wukong_tower_pandas_ranged_bolt_var2_v1.ogg",
    "kra_sfx_wukong_tower_pandas_ranged_bolt_var3_v1.ogg",
    "kra_sfx_wukong_tower_pandas_ranged_fire_var1_v1.ogg",
    "kra_sfx_wukong_tower_pandas_ranged_fire_var2_v1.ogg",
    "kra_sfx_wukong_tower_pandas_ranged_fire_var3_v1.ogg",
    "kra_sfx_wukong_tower_pandas_ranged_hat_var1_v1.ogg",
    "kra_sfx_wukong_tower_pandas_ranged_hat_var2_v1.ogg",
    "kra_sfx_wukong_tower_pandas_ranged_hat_var3_v1.ogg",
    "kra_sfx_wukong_tower_pandas_skill_bolt_op1_v1.ogg",
    "kra_sfx_wukong_tower_pandas_skill_fire_v1.ogg",
    "kra_sfx_wukong_tower_pandas_skill_hat_throw_v1.ogg",
    "kra_sfx_wukong_tower_pandas_melee_var1_v1.ogg",
    "kra_sfx_wukong_tower_pandas_melee_var2_v1.ogg",
    "kra_sfx_wukong_tower_pandas_melee_var3_v1.ogg"
  }
},

  enemies_sea_of_trees = {
    files = {
      "kra_sfx_enemy_bearVanguard_rage_v1.ogg",
      "kra_sfx_enemy_rottenfangHyena_barbaricFeast_v1.ogg",
      "kra_sfx_enemy_turtleShaman_basicAttack_var3.ogg",
      "kra_sfx_enemy_turtleShaman_healing_v1.ogg",
      "kra_sfx_enemy_tuskedBrawler_death_op1_var3.ogg",
      "kra_sfx_enemy_cutthroatRat_stealthSkill-oneShot_v1.ogg",
      "kra_sfx_enemy_skunkBombardier_basicAttack-whoosh_var1_v1.ogg",
      "kra_sfx_enemy_skunkBombardier_basicAttack-impact_var1_v1.ogg",
      "kra_sfx_enemy_dreadeyeViper_death_var1_v1.ogg",
      "kra_sfx_enemy_patrollingVulture_death_var4_v1.ogg",
      "kra_sfx_enemy_skunkBombardier_death_v1.ogg",
      "kra_sfx_enemy_rottenfangHyena_death_var3_v1_op2.ogg",
      "kra_sfx_enemy_cutthroatRat_death_var3_v1.ogg",
      "kra_sfx_enemy_turtleShaman_death_op2_v1.ogg",
      "kra_sfx_enemy_bearVanguard_death_v1.ogg",
      "kra_sfx_enemy_razingRhino_basicAttack_var3_v1.ogg",
      "kra_sfx_enemy_razingRhino_charge_v1.ogg",
      "kra_sfx_enemy_razingRhino_death_v1.ogg",
      "kra_sfx_enemy_sheep_death_var1_v1.ogg",
      "kra_sfx_enemy_sheep_death_var2_v1.ogg",
      "kra_sfx_enemy_sheep_death_var3_v1.ogg",
    }
  },
  enemies_terrain_2 = {
    files = {
      "kra_sfx_enemy_cultistAcolyte_death_var1_v1.ogg",
      "kra_sfx_enemy_cultistAcolyte_death_var2_v1.ogg",
      "kra_sfx_enemy_cultistAcolyte_death_var3_v1.ogg",
      "kra_sfx_enemy_cultistAcolyte_death_var4_v1.ogg",
      "kra_sfx_enemy_cultistAcolyte_death_var5_v1.ogg",
      "kra_sfx_enemy_cultistAcolyte_deathSpecial_v1.ogg",
      "kra_sfx_enemy_acolyteTentacle_attack_var1_v1.ogg",
      "kra_sfx_enemy_acolyteTentacle_attack_var2_v1.ogg",
      "kra_sfx_enemy_acolyteTentacle_attack_var3_v1.ogg",
      "kra_sfx_enemy_acolyteTentacle_death_var1_v1.ogg",
      "kra_sfx_enemy_acolyteTentacle_death_var2_v1.ogg",
      "kra_sfx_enemy_acolyteTentacle_death_var3_v1.ogg",
      "kra_sfx_enemy_voidBlinker_teleport_v1.ogg",
      "kra_sfx_enemy_voidBlinker_death_var1_v1.ogg",
      "kra_sfx_enemy_voidBlinker_death_var3_v1.ogg",
      "kra_sfx_enemy_voidBlinker_stare_v1.ogg",
      "kra_sfx_enemy_twistedSister_summon_cast_v1.ogg",
      "kra_sfx_enemy_twistedSister_summon_spawn_v2.ogg",
      "kra_sfx_enemy_twistedSister_death_v1.ogg",
      "kra_sfx_enemy_nightmare_death_v1.ogg",
      "kra_sfx_enemy_unblindedPriest_death_var1.ogg",
      "kra_sfx_enemy_unblindedPriest_death_var2.ogg",
      "kra_sfx_enemy_unblindedPriest_death_var3.ogg",
      "kra_sfx_enemy_unblindedPriest_transform_cast_v2.ogg",
      "kra_sfx_enemy_unblindedPriest_transform_spawn_v1.ogg",
      "kra_sfx_enemy_abomination_death_v1.ogg",
      "kra_sfx_enemy_abomination_instakill_v1.ogg",
      "kra_sfx_enemy_spiderling_death_var1_v1.ogg",
      "kra_sfx_enemy_shackler_death_var-003.ogg",
      "kra_sfx_enemy_shackler_blockTower_block_v1.ogg",
      "kra_sfx_enemy_shackler_blockTower_unblock_v1.ogg",
      "kra_sfx_enemy_boundNightmare_death_op2_v1.ogg",
      "kra_sfx_enemy_corruptedStalker_death_v1.ogg",
      "kra_sfx_enemy_stoneGolem_death_v1.ogg",
      "kra_sfx_enemy_sheep_death_var1_v1.ogg",
      "kra_sfx_enemy_sheep_death_var2_v1.ogg",
      "kra_sfx_enemy_sheep_death_var3_v1.ogg",
    }
  },
  enemies_terrain_3 = {
    files = {
      "kra_sfx_enemy_voidBlinker_teleport_v1.ogg",
      "kra_sfx_enemy_voidBlinker_death_var1_v1.ogg",
      "kra_sfx_enemy_voidBlinker_death_var3_v1.ogg",
      "kra_sfx_enemy_voidBlinker_stare_v1.ogg",
      "kra_sfx_enemy_abomination_death_v1.ogg",
      "kra_sfx_enemy_abomination_instakill_v1.ogg",
      "kra_sfx_enemy_mindlessHusk_death_v1.ogg",
      "kra_sfx_enemy_mindlessHusk_deathSpawn_v1.ogg",
      "kra_sfx_enemy_vileSpawner_death_v1.ogg",
      "kra_sfx_enemy_vileSpawner_spawn_cast_v1.ogg",
      "kra_sfx_enemy_hardenedHorror_death_op2_v1.ogg",
      "kra_sfx_enemy_noxiousHorror_basicAttack_cast_op2_v1.ogg",
      "kra_sfx_enemy_noxiousHorror_basicAttack_impact_v1.ogg",
      "kra_sfx_enemy_noxiousHorror_death_op1_v1.ogg",
      "kra_sfx_enemy_fleshBehemoth_death_op1_v1.ogg",
      "kra_sfx_enemy_evolvingScourge_evolve_op1_v1.ogg",
      "kra_sfx_enemy_evolvingScourge_death_var2_v1.ogg",
    }
  },
  enemies_terrain_4 = {
    files = {
      "kra_sfx_enemy_corruptedRanger_spawn_var1_v1.ogg",
      "kra_sfx_enemy_corruptedRanger_spawn_var2_v1.ogg",
      "kra_sfx_enemy_corruptedRanger_spawn_var3_v1.ogg",
      "kra_sfx_enemy_corruptedRanger_death_var1_v2.ogg",
      "kra_sfx_enemy_corruptedRanger_death_var2_v2.ogg",
      "kra_sfx_enemy_corruptedRanger_death_var3_v2.ogg",
      "kra_sfx_enemy_specter_death_var1_v2.ogg",
      "kra_sfx_enemy_specter_death_var2_v2.ogg",
      "kra_sfx_enemy_specter_death_var3_v2.ogg",
      "kra_sfx_enemy_specter_death_var4_v2.ogg",
      "kra_sfx_enemy_specter_death_var5_v2.ogg",
      "kra_sfx_enemy_specter_death_var6_v2.ogg",
      "kra_sfx_enemy_specter_interact_cast_var1_v2.ogg",
      "kra_sfx_enemy_specter_interact_cast_var2_v2.ogg",
      "kra_sfx_enemy_specter_interact_cast_var3_v2.ogg",
      "kra_sfx_enemy_specter_interact_cast_op2_var-003.ogg",
      "kra_sfx_enemy_specter_interact_cast_op2_var-004.ogg",
      "kra_sfx_enemy_specter_interact_cast_var-005.ogg",
      "kra_sfx_stage17_weirdwood_specterImpact_v1.ogg",
      "kra_sfx_enemy_dustCryptid_death_v1.ogg",
      "kra_sfx_enemy_baneWolf_death_var1_v1.ogg",
      "kra_sfx_enemy_baneWolf_death_var2_v1.ogg",
      "kra_sfx_enemy_baneWolf_death_var3_v1.ogg",
      "kra_sfx_enemies_deathwood_rangedAttack_cast_var1_v1.ogg",
      "kra_sfx_enemies_deathwood_rangedAttack_cast_var2_v1.ogg",
      "kra_sfx_enemies_deathwood_rangedAttack_cast_var3_v1.ogg",
      "kra_sfx_enemies_deathwood_rangedAttack_impact_var1_v1.ogg",
      "kra_sfx_enemies_deathwood_rangedAttack_impact_var2_v1.ogg",
      "kra_sfx_enemies_deathwood_rangedAttack_impact_var3_v1.ogg",
      "kra_sfx_enemy_deathwood_death_op1_v1.ogg",
      "kra_sfx_enemy_animatedArmor_death_v1.ogg",
      "kra_sfx_enemy_animatedArmor_revive_v1.ogg",
      "kra_sfx_enemy_soulcaller_attack_cast_v1.ogg",
      "kra_sfx_enemy_soulcaller_towerBlock_in_v1.ogg",
      "kra_sfx_enemy_soulcaller_towerBlock_out_v1.ogg",
      "kra_sfx_enemy_soulcaller_death_v1.ogg",
      "kra_sfx_enemy_harvester_duplicate_v1.ogg",
      "kra_sfx_enemy_harvester_death_var1_v1.ogg",
      "kra_sfx_enemy_harvester_death_var2_v1.ogg",
      "kra_sfx_enemy_harvester_death_var3_v1.ogg",
    }
  },
  enemies_terrain_crocs = {
    files = {
      "kra_sfx_crocs_crokinder_death_var1_v1.ogg",
      "kra_sfx_crocs_crokinder_transform_var3_v1.ogg",
      "kra_sfx_crocs_gator_melee_var1_v1.ogg",
      "kra_sfx_crocs_gator_melee_var2_v1.ogg",
      "kra_sfx_crocs_gator_melee_var3_v1.ogg",
      "kra_sfx_crocs_killertile_bitemelee_v1.ogg",
      "kra_sfx_crocs_quickfeet_melee_var1_v1.ogg",
      "kra_sfx_crocs_quickfeet_melee_var2_v1.ogg",
      "kra_sfx_crocs_quickfeet_melee_var3_v1.ogg",
      "kra_sfx_crocs_quickfeet_throw_chicken_leg_and_throwup_v1.ogg",
      "kra_sfx_crocs_quickfeet_throw_chicken_leg_eat_and_grow_v1.ogg",
      "kra_sfx_crocs_lizardshot_range_var1_v1.ogg",
      "kra_sfx_crocs_lizardshot_range_var2_v1.ogg",
      "kra_sfx_crocs_lizardshot_range_var3_v1.ogg",
      "kra_sfx_crocs_lizardshot_melee_var1_v1.ogg",
      "kra_sfx_crocs_lizardshot_melee_var2_v1.ogg",
      "kra_sfx_crocs_lizardshot_melee_var3_v1.ogg",
      "kra_sfx_crocs_nesting_gator_melee_var1_v1.ogg",
      "kra_sfx_crocs_nesting_gator_melee_var2_v1.ogg",
      "kra_sfx_crocs_nesting_gator_melee_var3_v1.ogg",
      "kra_sfx_crocs_nesting_gator_spawn_op1_v1.ogg",
      "kra_sfx_crocs_wise_range_cast_var1_v1.ogg",
      "kra_sfx_crocs_tank_spin_op2_v1.ogg",
    }
  },
  enemies_terrain_6 = {
    files = {
      "kra_sfx_enemy_darksteelHammerer_death_var1_v1.ogg",
      "kra_sfx_enemy_darksteelHammerer_death_var2_v1.ogg",
      "kra_sfx_enemy_darksteelShielder_death_var1_v1.ogg",
      "kra_sfx_enemy_darksteelShielder_death_var2_v1.ogg",
      "kra_sfx_enemy_rollingSentry_death_var1_v1.ogg",
      "kra_sfx_enemy_rollingSentry_death_var2_v1.ogg",
      "kra_sfx_enemy_rollingSentry_attack_op1_var1_v1.ogg",
      "kra_sfx_enemy_rollingSentry_attack_op1_var2_v1.ogg",
      "kra_sfx_enemy_scrapSpeedster_death_var1_v1.ogg",
      "kra_sfx_enemy_scrapSpeedster_death_var3_v1.ogg",
      "kra_sfx_enemy_bruteWelder_death_var1_v1.ogg",
      "kra_sfx_enemy_bruteWelder_death_var2_v1.ogg",
      "kra_sfx_enemy_bruteWelder_deathImpact_v1.ogg",
      "kra_sfx_enemy_darksteelFist_death_var1_v1.ogg",
      "kra_sfx_enemy_darksteelFist_death_var2_v1.ogg",
      "kra_sfx_enemy_darksteelFist_death_var3_v1.ogg",
      "kra_sfx_enemy_darksteelFist_stun_op1_v1.ogg",
      "kra_sfx_enemy_darksteelFist_stun_op2_v1.ogg",
      "kra_sfx_enemy_madTinkerer_death_var1_v1.ogg",
      "kra_sfx_enemy_madTinkerer_rayCast_v1.ogg",
      "kra_sfx_enemy_madTinkerer_summon_v1.ogg",
      "kra_sfx_enemy_scrapDrone_death_v1.ogg",
      "kra_sfx_enemy_scrapDrone_death_v1.ogg",
      "kra_sfx_enemy_darksteelAnvil_death_var1_v1.ogg",
      "kra_sfx_enemy_darksteelAnvil_death_var3_v1.ogg",
      "kra_sfx_enemy_darksteelAnvil_death_var4_v1.ogg",
      "kra_sfx_enemy_darksteelAnvil_beat_SHORT_v2.ogg",
      "kra_sfx_enemy_darksteelHulk_death_var2_v1.ogg",
      "kra_sfx_enemy_darksteelHulk_death_var3_v1.ogg",
      "kra_sfx_enemy_darksteelHulk_charge_op1_v1.ogg",
      "kra_sfx_enemy_darksteelGuardian_attack_var1_v1.ogg",
      "kra_sfx_enemy_darksteelGuardian_attack_var2_v1.ogg",
      "kra_sfx_enemy_darksteelGuardian_attack_var3_v1.ogg",
      "kra_sfx_enemy_darksteelGuardian_death_oneShot_v1.ogg",
      "kra_sfx_enemy_darksteelGuardian_enrage_cast_v1.ogg",
      "kra_sfx_enemy_darksteelGuardian_enrage_attack_v1.ogg",
    }
  },
  enemies_terrain_spiders = {
    files = {
      "kra_sfx_spiders_enemy_spider_cultbrood_death_var1_v1.ogg",
      "kra_sfx_spiders_enemy_spider_cultbrood_death_var2_v1.ogg",
      "kra_sfx_spiders_enemy_spider_cultbrood_melee_var1_v1.ogg",
      "kra_sfx_spiders_enemy_spider_cultbrood_melee_var2_v1.ogg",
      "kra_sfx_spiders_enemy_spider_cultbrood_melee_var3_v1.ogg",
      "kra_sfx_spiders_enemy_spider_drainbrood_melee_var1_v1ogg",
      "kra_sfx_spiders_enemy_spider_drainbrood_melee_var2_v1.ogg",
      "kra_sfx_spiders_enemy_spider_drainbrood_melee_var3_v1.ogg",
      "kra_sfx_spiders_enemy_spider_glarenwarden_death_v1.ogg",
      "kra_sfx_spiders_enemy_spider_glarenwarden_melee_var1_v1.ogg",
      "kra_sfx_spiders_enemy_spider_glarenwarden_melee_var2_v1.ogg",
      "kra_sfx_spiders_enemy_spider_glarenwarden_melee_var3_v1.ogg",
      "kra_sfx_enemy_unblindedPriest_transform_cast_v2.ogg",
      "kra_sfx_spiders_enemy_spider_priest_transform_v1.ogg",
      "kra_sfx_spiders_enemy_spider_sister_range_var1_v1.ogg",
      "kra_sfx_spiders_enemy_spider_sister_range_var2_v1.ogg",
      "kra_sfx_spiders_enemy_spider_sister_range_var3_v1.ogg",
      "kra_sfx_spiders_enemy_spider_sister_spawn_v1.ogg",
      "kra_sfx_spiders_mechanic_spider_death_v1.ogg",
      "kra_sfx_spiders_mechanic_spider_working_LOOP_v1.ogg",
    }
  },
  enemies_terrain_wukong_1 = {
    files = {
      "kra_sfx_wukong_enemy_ash_spirit_death_v1.ogg",
      "kra_sfx_wukong_enemy_ash_spirit_melee_var1.ogg",
      "kra_sfx_wukong_enemy_ash_spirit_melee_var2.ogg",
      "kra_sfx_wukong_enemy_ash_spirit_melee_var3.ogg",
      "kra_sfx_wukong_enemy_blaze_raider_special_v1.ogg",
      "kra_sfx_wukong_enemy_burning_treant_death_v1.ogg",
      "kra_sfx_wukong_enemy_burning_treant_special_v1.ogg",
      "kra_sfx_wukong_enemy_fire_fox_death_v1.ogg",
      "kra_sfx_wukong_enemy_fire_fox_melee_var1_v1.ogg",
      "kra_sfx_wukong_enemy_fire_fox_melee_var2_v1.ogg",
      "kra_sfx_wukong_enemy_fire_fox_melee_var3_v1.ogg",
      "kra_sfx_wukong_enemy_fire_phoenix_death_wScreech_v1.ogg",
      "kra_sfx_wukong_enemy_flame_guard_special_v1.ogg",
      "kra_sfx_wukong_enemy_nine_tailed_fox_death_wWhimper_v1.ogg",
      "kra_sfx_wukong_enemy_nine_tailed_fox_melee_double_v1.ogg",
      "kra_sfx_wukong_enemy_nine_tailed_fox_melee_var1_v1.ogg",
      "kra_sfx_wukong_enemy_nine_tailed_fox_melee_var2_v1.ogg",
      "kra_sfx_wukong_enemy_nine_tailed_fox_melee_var3_v1.ogg",
      "kra_sfx_wukong_enemy_nine_tailed_fox_teleport_IN_v1.ogg",
      "kra_sfx_wukong_enemy_nine_tailed_fox_teleport_OUT_v1.ogg",
      "kra_sfx_wukong_enemy_wuxian_death_v1.ogg",
      "kra_sfx_wukong_enemy_wuxian_ranged_v1.ogg",
      "kra_sfx_wukong_enemy_wuxian_special_woVoice_v1.ogg",
      "kra_sfx_wukong_enemy_wuxian_special_wVoice_v1.ogg",
    }
  },
  enemies_terrain_wukong_2 = {
    files = {
      "kra_sfx_wukong_enemy_drakeling_death_v1.ogg",
      "kra_sfx_wukong_enemy_drakeling_leap_v1.ogg",
      "kra_sfx_wukong_enemy_qiongqi_ranged_op1_v1.ogg",
      "kra_sfx_wukong_enemy_qiongqi_death_v1.ogg",
      "kra_sfx_wukong_enemy_wmaster_special_v1.ogg",
      "kra_sfx_wukong_enemy_elemental_melee_var1_v1.ogg",
      "kra_sfx_wukong_enemy_elemental_melee_var2_v1.ogg",
      "kra_sfx_wukong_enemy_elemental_melee_var3_v1.ogg",
      "kra_sfx_wukong_enemy_elemental_ranged_impact_var1_v1.ogg",
      "kra_sfx_wukong_enemy_elemental_ranged_impact_var2_v1.ogg",
      "kra_sfx_wukong_enemy_elemental_ranged_impact_var3_v1.ogg",
      "kra_sfx_wukong_enemy_elemental_death_effect_cast_op1_v1.ogg",
      "kra_sfx_wukong_enemy_elemental_death_effect_stun_v1.ogg",
      "kra_sfx_wukong_enemy_elemental_death_normal_v1.ogg",
      "kra_sfx_wukong_enemy_fan_guard_death_op1_v1.ogg",
      "kra_sfx_wukong_enemy_fan_guard_death_op2_v1.ogg",
      "kra_sfx_wukong_enemy_fan_guard_special_v1.ogg",
    }
  },
  enemies_terrain_wukong_3 = {
    files = {
    }
  },
  terrain_1_common = {
    files = {
      "kra_sfx_terrain1Ambient_wind_var1_v1.ogg",
      "kra_sfx_terrain1Ambient_wind_var2_v1.ogg",
      "kra_sfx_terrain1Ambient_wind_var3_v1.ogg",
      "kra_sfx_terrain1Ambient_birds_var1_v1.ogg",
      "kra_sfx_terrain1Ambient_birds_var2_v1.ogg",
      "kra_sfx_terrain1Ambient_birds_var3_v1.ogg",
      "kra_sfx_terrain1Ambient_birds_var4_v1.ogg",
      "kra_sfx_terrain1Ambient_birds_var5_v1.ogg",
      "kra_sfx_terrain1Ambient_birds_var6_v1.ogg",
      "kra_sfx_easterEgg_arboreanTap_in_var1_v2.ogg",
      "kra_sfx_easterEgg_arboreanTap_in_var2_v2.ogg",
      "kra_sfx_easterEgg_arboreanTap_out_fullSeq_op1_v2.ogg",
      "kra_sfx_easterEgg_arboreanTap_out_fullSeq_op2_v2.ogg",
    }
  },
  terrain_2_common = {
    files = {
      "kra_sfx_terrain2Ambient_wind_var1_v1.ogg",
      "kra_sfx_terrain2Ambient_wind_var2_v1.ogg",
      "kra_sfx_terrain2Ambient_wind_var3_v1.ogg",
      "kra_sfx_terrain2Ambient_bats_var1_v1.ogg",
      "kra_sfx_terrain2Ambient_bats_var2_v1.ogg",
      "kra_sfx_terrain2Ambient_bats_var3_v1.ogg",
      "kra_sfx_terrain2Ambient_waterDrops_var1_v1.ogg",
      "kra_sfx_terrain2Ambient_waterDrops_var2_v1.ogg",
      "kra_sfx_terrain2Ambient_waterDrops_var3_v1.ogg",
    }
  },
  terrain_3_common = {
    files = {
      "kra_sfx_terrain3_glareOn_littleEye_op1_v1.ogg",
      "kra_sfx_terrain3_glareOn_littleEye_op2_v1.ogg",
      "kra_sfx_terrain3_glareOn_bigEye_v1.ogg",
      "kra_sfx_terrain3_glare_off_v1.ogg",
      "kra_sfx_terrain3Ambient_gutural_var2_v1.ogg",
      "kra_sfx_terrain3Ambient_gutural_var3_v1.ogg",
      "kra_sfx_terrain3Ambient_gutural_var5_v1.ogg",
    }
  },
  terrain_4_common = {
    files = {
      "kra_sfx_easterEgg_cheshireCat_appear_v1.ogg",
      "kra_sfx_easterEgg_cheshireCat_disappear_v1.ogg",
      "kra_sfx_easterEgg_howlingTree_var1_v1.ogg",
      "kra_sfx_easterEgg_howlingTree_var2_v1.ogg",
      "kra_sfx_easterEgg_howlingTree_var3_v1.ogg",
      "kra_sfx_update1Ambient_wind_var1.ogg",
      "kra_sfx_update1Ambient_wind_var2.ogg",
      "kra_sfx_update1Ambient_wind_var3.ogg",
      "kra_sfx_update1Ambient_wind_var4.ogg",
    }
  },
  terrain_6_common = {
    files = {
      "kra_sfx_dlc1Ambient_windRocks_var1_v1.ogg",
      "kra_sfx_dlc1Ambient_windRocks_var2_v1.ogg",
      "kra_sfx_dlc1Ambient_windRocks_var3_v1.ogg",
      "kra_sfx_dlc1Ambient_forge_var1_v1.ogg",
      "kra_sfx_dlc1Ambient_forge_var2_v1.ogg",
      "kra_sfx_dlc1Ambient_forge_var3_v1.ogg",
      "kra_sfx_easterEgg_exodiaPart_v1.ogg",
    }
  },
  terrain_wukong_common = {
    files = {
      "kra_sfx_wukong_mechanic_stage1_holder_active_vines_v1.ogg",
      "kra_sfx_wukong_mechanic_stage1_holder_evolve_v1.ogg",
      "kra_sfx_wukong_mechanic_stage1_holder_unlock.ogg",
      "kra_sfx_wukong_mechanic_stage2_holder_active_instakill_v1_in.ogg",
      "kra_sfx_wukong_mechanic_stage2_holder_active_instakill_v1_kill.ogg",
      "kra_sfx_wukong_mechanic_stage34_holder_active_summon_v1.ogg",
    }
  },

  stage_01 = {
    files = {
      "kra_sfx_stageTutorial_arboreanSage_appear_v1.ogg",
      "kra_sfx_stageTutorial_arboreanSage_disappear_v1.ogg",
      "kra_sfx_stageTutorial_arboreanSage_cast_op2_v1.ogg",
      "kra_sfx_stageTutorial_arboreanSage_shrubDisappear_var1_v1.ogg",
      "kra_sfx_stageTutorial_arboreanSage_shrubDisappear_var2_v1.ogg",
      "kra_sfx_stageTutorial_arboreanSage_shrubDisappear_var3_v1.ogg",
      "kra_sfx_stageTutorial_arboreanSage_shrubDisappear_var4_v1.ogg",
      "kra_sfx_easterEgg_runeStage1_v1.ogg",
      "kra_sfx_easterEgg_campfire_off_v1.ogg",
      "kra_sfx_easterEgg_campfire_on_v1.ogg",
      "kra_sfx_easterEgg_camperFire_tap3_v1.ogg",
      "kra_sfx_easterEgg_robinHood_v1.ogg",
    }
  },
  stage_02 = {
    files = {
      "kra_sfx_stageMechanic_guardianTree_roots_v1.ogg",
      "kra_sfx_stageMechanic_guardianTree_cast_v1.ogg",
      "kra_sfx_stageMechanic_guardianTree_pre-cast_v1.ogg",
      "kra_sfx_easterEgg_runeStage2-3_v1.ogg",
      "kra_sfx_easterEgg_linkFishing_var3_v1.ogg",
      "kra_sfx_easterEgg_linkFishing_var2_v1.ogg",
      "kra_sfx_easterEgg_linkFishing_var1_v1.ogg",
      "kra_sfx_easterEgg_lionKing_op1_v1.ogg",
      "kra_sfx_stage02_cinematic_raelyn_teleport_v1.ogg",
      "kra_sfx_stage02_cinematic_veznan_teleport_v1.ogg",
    }
  },
  stage_03 = {
    files = {
      "kra_sfx_stageMechanic_heartOfTheForest_ready_v1.ogg",
      "kra_sfx_stageMechanic_heartOfTheForest_cast_v1.ogg",
      "kra_sfx_stageMechanic_heartOfTheForest_energyBlasts_var1_v1.ogg",
      "kra_sfx_stageMechanic_heartOfTheForest_energyBlasts_var2_v1.ogg",
      "kra_sfx_stageMechanic_heartOfTheForest_energyBlasts_var3_v1.ogg",
      "kra_sfx_stageMechanic_heartOfTheForest_energyBlasts_var4_v1.ogg",
      "kra_sfx_easterEgg_runeStage2-3_v1.ogg",
    }
  },
  stage_04 = {
    files = {
      "kra_sfx_stageMechanic_wildbeastElevator_break_v1.ogg",
      "kra_sfx_stage04_wildbeastElevator_in_op1_v3.ogg",
      "kra_sfx_stage04_wildbeastElevator_out_v3.ogg",
      "kra_sfx_stage04_wildbeastElevator_inFull_op1.ogg",
      "kra_sfx_easterEgg_runeStage4_v1.ogg",
      "kr_voice_arboreanthornspears_taunt01_a.ogg",
      "kr_voice_arboreanthornspears_taunt02_b.ogg",
      "kra_sfx_easterEgg_arboreanFall_op2_v1.ogg",
      "kra_sfx_easterEgg_sheepyTerrain1_fall_v1.ogg",
      "kra_sfx_easterEgg_sheepyTerrain1_impact_v1.ogg",
    }
  },
  stage_05 = {
    files = {
      "kra_sfx_stageMechanic_woodcutterBear_chop_2_v1.ogg",
      "kra_sfx_stageMechanic_woodcutterBear_chop_1_v1.ogg",
      "kra_sfx_stageMechanic_woodcutterBear_roar_v1.ogg",
      "kra_sfx_easterEgg_runeStage5-6_v1.ogg",
    }
  },
  stage_06 = {
    files = {
      "kra_sfx_boss_goregrind_attack_var3_v1.ogg",
      "kra_sfx_boss_goregrind_attack_var2_v1.ogg",
      "kra_sfx_boss_goregrind_attack_var1_v1.ogg",
      "kra_sfx_boss_goregrind_jumpImpact_land_v1.ogg",
      "kra_sfx_boss_goregrind_jumpImpact_falling_v1.ogg",
      "kra_sfx_boss_goregrind_jumpCast.ogg",
      "kra_sfx_boss_goregrind_death_v1.ogg",
      "kra_sfx_boss_goregrind_horn_v1.ogg",
      "kra_sfx_easterEgg_runeStage5-6_v1.ogg",
      "kra_sfx_easterEgg_minecraftPig_var3_v1.ogg",
      "kra_sfx_easterEgg_minecraftPig_death_v1.ogg",
      "kra_sfx_stageMechanic_burrowOpen_v1.ogg",
      "kra_sfx_stageMechanic_woodenDoorOpen_v1.ogg",
      "kra_sfx_stageMechanic_woodenDoorForcedClose_v1.ogg",
      "kra_sfx_cinematicStage06_acolyteTeleport_v1.ogg",
      "kra_sfx_stage11_bossFight_mydriasIllusionSummon_cast_v1.ogg",
      "kra_sfx_stage06_bossFight_Cinematic_goregrindJump.ogg",
      "kra_sfx_stage06_bossFight_cinematic_goregrindSnore_v1.ogg",
      "kra_sfx_stage06_bossFight_cinematic_goregrindWakeUp_v1.ogg",
    }
  },
  stage_07 = {
    files = {
      "kra_sfx_stage07_cultTempleBridge_v1.ogg",
      "kra_sfx_easterEgg_witcher_v1.ogg",
      "kra_sfx_easterEgg_crow_fly_v1.ogg",
      "kra_sfx_easterEgg_crow_caw_v1.ogg",
    }
  },
  stage_08 = {
    files = {
      "kr_voice_rescuedelves_taunt01_b.ogg",
      "kr_voice_rescuedelves_taunt02_c.ogg",
      "kra_sfx_easterEgg_basket_tap_v1.ogg",
      "kra_sfx_easterEgg_basket_break_v1.ogg",
    }
  },
  stage_09 = {
    files = {
      "kra_sfx_stage09_cultBridge_v1.ogg",
      "kra_sfx_stage09_nightmarePortalOn-candles_v1.ogg",
      "kra_sfx_stage09_nightmarePortalOn-eye_v1.ogg",
      "kra_stage09_cultBridge_rumble_v1.ogg",
      "kra_sfx_easterEgg_dryBones_break_v1.ogg",
      "kra_sfx_easterEgg_dryBones_reform_v1.ogg",
      "kra_sfx_easterEgg_sheepyTerrain2_camera_v1.ogg",
      "kra_sfx_easterEgg_sheepyTerrain2_bridgeBaa_v1.ogg",
    }
  },
  stage_10 = {
    files = {
      "kra_sfx_stage10_obelisk_activation_op1_v3.ogg",
      "kra_sfx_stage10_obelisk_effectStun_cast_v1.ogg",
      "kra_sfx_stage10_obelisk_effectHeal_cast_loopStart.ogg",
      "kra_sfx_stage10_obelisk_effectHeal_cast_LOOP_v1.ogg",
      "kra_sfx_stage10_obelisk_effectChange_v2.ogg",
      "kra_sfx_stage10_obelisk_golemSpawn_cast.ogg",
      "kra_sfx_stage10_obelisk_golemSpawn_golem.ogg",
      "kra_sfx_easterEgg_villagePeople_statuePuff_op1_v1.ogg",
      "kra_sfx_easterEgg_villagePeople_fireworks_v1.ogg",
      "kra_sfx_easterEgg_villagePeople_hits_op2_v2.ogg",
    }
  },
  stage_11 = {
    files = {
      "kra_sfx_stage11_ambienceThunder_var1_v1.ogg",
      "kra_sfx_stage11_ambienceThunder_var2_v1.ogg",
      "kra_sfx_stage11_ambienceThunder_var3_v1.ogg",
      "kra_sfx_stage11_bossFight_portalOpen_v1.ogg",
      "kra_sfx_stage11_bossFight_portalClose_v1.ogg",
      "kra_sfx_stage11_bossFight_mydriasIllusionSummon_cast_v1.ogg",
      "kra_sfx_stage11_bossFight_mydriasIllusionShield_cast_v1.ogg",
      "kra_sfx_stage11_bossFight_mydriasIllusionTendrils_cast_v1.ogg",
      "kra_sfx_stage11_bossFight_mydriasIllusionTendrils_death_v1.ogg",
      "kra_sfx_stage11_bossFight_mydriasIllusion_death_v1.ogg",
      "kra_sfx_stage11_bossFight_midCinematic_chainBreak_v1.ogg",
      "kra_sfx_stage11_bossFight_midCinematic_platformMove_v1.ogg",
      "kra_sfx_stage11_bossFight_midCinematic_denasJump_v1.ogg",
      "kra_sfx_stage11_bossFight_midCinematic_denasJumpLand_v1.ogg",
      "kra_sfx_stage11_bossFight_midCinematic_veznanTeleport_v1.ogg",
      "kra_sfx_stage11_bossFight_veznanReady_op2_v1.ogg",
      "kra_sfx_stage11_bossFight_veznanSoulImpact_cast_v1.ogg",
      "kra_sfx_stage11_bossFight_veznanSoulImpact_impact_var1_v1.ogg",
      "kra_sfx_stage11_bossFight_veznanSoulImpact_impact_var2_v1.ogg",
      "kra_sfx_stage11_bossFight_veznanSoulImpact_impact_var3_v1.ogg",
      "kra_sfx_stage11_bossFight_veznanDemonGuard_cast_withoutPortal_v1.ogg",
      "kra_sfx_stage11_bossFight_veznanMagicShackles_cast_v1.ogg",
      "kra_sfx_stage11_bossFight_veznanMagicShackles_release_v1.ogg",
      "kra_sfx_stage11_bossFight_denasAttack_v1.ogg",
      "kra_sfx_stage11_bossFight_denasGlarelingSpawn_v1.ogg",
      "kra_sfx_stage11_bossFight_denasTransformation_in_op1_v1.ogg",
      "kra_sfx_stage11_bossFight_denasTransformation_out_v1.ogg",
      "kra_sfx_tower_demonPit_bigGuy_basicAttack_var1_v1.ogg",
      "kra_sfx_tower_demonPit_bigGuy_basicAttack_var2_v1.ogg",
      "kra_sfx_tower_demonPit_bigGuy_basicAttack_var3_v1.ogg",
      "kra_sfx_easterEgg_hobbitsShelob_v1.ogg",
      "kra_sfx_stage11_bossFight_portalCreepIn_var1.ogg",
      "kra_sfx_stage11_bossFight_portalCreepIn_var2.ogg",
      "kra_sfx_stage11_bossFight_endCinematic_platformMove_v1.ogg",
    }
  },
  stage_12 = {
    files = {
      "kra_sfx_easterEgg_sheepyTerrain3_part1_v1.ogg",
      "kra_sfx_easterEgg_sheepyTerrain3_part2_v1.ogg",
      "kra_sfx_easterEgg_sheepyTerrain3_part3_v1.ogg",
      "kra_sfx_easterEgg_weirderThings_enterChars_climb_v1.ogg",
      "kra_sfx_easterEgg_weirderThings_enterChars_firstStrum_v1.ogg",
      "kra_sfx_easterEgg_weirderThings_enterChars_tap2_v1.ogg",
    }
  },
  stage_13 = {
    files = {
      "kra_sfx_stage13_darkRayTower_repair_v1.ogg",
      "kra_sfx_stage13_darkRayTower_attack_op2_v1.ogg",
      "kra_sfx_stage13_darkRayTower_specialAttack_v1.ogg",
      "kra_sfx_stage13_darkRayTower_destroy_v1.ogg",
    }
  },
  stage_14 = {
    files = {
      "kra_sfx_stage14_newPath_v1.ogg",
      "kra_sfx_stage14_behemothPool_splash_var1_v1.ogg",
      "kra_sfx_stage14_behemothPool_splash_var2_v1.ogg",
      "kra_sfx_stage14_behemothPool_splash_var3_v1.ogg",
      "kra_sfx_stage14_behemothPool_spawn1_v2.ogg",
      "kra_sfx_stage14_behemothPool_spawn2_v2.ogg",
      "kra_sfx_stage14_behemothPool_spawn3_v2.ogg",
      "kra_sfx_easterEgg_wobbaLubbaDubDub_portal12_open_v1.ogg",
      "kra_sfx_easterEgg_wobbaLubbaDubDub_portal12_pass_v1.ogg",
      "kra_sfx_easterEgg_wobbaLubbaDubDub_portal12_close_v1.ogg",
      "kra_sfx_easterEgg_wobbaLubbaDubDub_portal12_open-noLaser_v1.ogg",
      "kra_sfx_easterEgg_wobbaLubbaDubDub_portal3_out_v1.ogg",
    }
  },
  stage_15 = {
    files = {
      "kr_voice_kingdenas_taunt01_a.ogg",
      "kr_voice_kingdenas_taunt02_c.ogg",
      "kr_voice_kingdenas_taunt03_a.ogg",
      "kra_sfx_stage15_reinforcementDenas_basicAttack_p1_var1_v1.ogg",
      "kra_sfx_stage15_reinforcementDenas_basicAttack_p1_var2_v1.ogg",
      "kra_sfx_stage15_reinforcementDenas_basicAttack_p1_var3_v1.ogg",
      "kra_sfx_stage15_reinforcementDenas_basicAttack_p2_var1_v1.ogg",
      "kra_sfx_stage15_reinforcementDenas_basicAttack_p2_var2_v1.ogg",
      "kra_sfx_stage15_reinforcementDenas_basicAttack_p2_var3_v1.ogg",
      "kra_sfx_stage15_reinforcementDenas_secondaryAttack_v1.ogg",
      "kra_sfx_stage15_reinforcementDenas_summon_op1_v1.ogg",
      "kra_sfx_stage15_cinematic_mydriasEnter_v1.ogg",
      "kra_sfx_stage15_cinematic_mydriasExit_v1.ogg",
      "kra_sfx_stage15_cinematic_mutatedMydriasEnter_v1.ogg",
      "kra_sfx_stage15_bossFight_mydriasTentacleTrap_v1.ogg",
      "kra_sfx_stage15_bossFight_mydriasTentacleCircleCounter_op1_v1.ogg",
      "kra_sfx_stage15_bossFight_mydriasTentacleCircle_op2_v2.ogg",
      "kra_sfx_stage15_bossFight_mydriasRay_v1.ogg",
      "kra_sfx_stage15_bossFight_mydriasBurrowIn_v1.ogg",
      "kra_sfx_stage15_bossFight_mydriasBurrowOut_v1.ogg",
      "kra_sfx_stage15_bossFight_mydriasUncloak_v1.ogg",
      "kra_sfx_easterEgg_riffPortal_portalOpen_v1.ogg",
      "kra_sfx_easterEgg_riffPortal_portalClose_v1.ogg",
      "kra_sfx_easterEgg_riffPortal_Broom_v1.ogg",
      "kra_sfx_stage15_bossFight_mydriasDeath.ogg",
      "kra_sfx_stage15_reinforcementDenas_out_v1.ogg",
    }
  },
  stage_16 = {
    files = {
      "kra_sfx_stage16_bossFight_overseerRumble_op1_v1.ogg",
      "kra_sfx_stage16_bossFight_overseerUnchainCenter_v1.ogg",
      "kra_sfx_stage16_bossFight_overseerUnchainLeftRight_v1.ogg",
      "kra_sfx_stage16_bossFight_overseerUnchainDown_v1.ogg",
      "kra_sfx_stage16_bossFight_overseerSpawnerCast_v1.ogg",
      "kra_sfx_stage16_bossFight_overseerSpawnerImpact_v1.ogg",
      "kra_sfx_stage16_bossFight_overseerTowerTeleport_PreCharge_v1.ogg",
      "kra_sfx_stage16_bossFight_overseerTowerTeleport_TowerTeleport_v1.ogg",
      "kra_sfx_stage16_bossFight_overseerTowerHolderDestroy_charge_v1.ogg",
      "kra_sfx_stage16_bossFight_overseerTowerHolderDestroy_Ray_v1.ogg",
      "kra_sfx_stage16_bossFight_overseerTowerHolderDestroy_explosion_op2_v1.ogg",
      "kra_sfx_stage16_bossFight_overseerHurt_op1_v1.ogg",
      "kra_sfx_stage16_bossFight_overseerDefeat_fullSeq_v2.ogg",
    }
  },
  stage_17 = {
    files = {
      "kra_sfx_stage17_weirdwood_basicAttack_throw_var1_v1.ogg",
      "kra_sfx_stage17_weirdwood_basicAttack_throw_var2_v1.ogg",
      "kra_sfx_stage17_weirdwood_basicAttack_throw_var3_v1.ogg",
      "kra_sfx_stage17_weirdwood_basicAttack_explosion_v1.ogg",
      "kra_sfx_stage17_weirdwood_deathwoodTransform_v1.ogg",
      "kra_sfx_stage17_weirdwood_leavesFall_v1.ogg",
      "kra_sfx_stage17_rootSoulcaller_in.ogg",
      "kra_sfx_stage17_rootSoulcaller_out.ogg",
      "kra_sfx_stage17_rootSoulcaller_cast.ogg",
    }
  },
  stage_18 = {
    files = {
      "kr_voice_elvenmercenaries_taunt-01_c.ogg",
      "kr_voice_elvenmercenaries_taunt-02_b.ogg",
      "kra_sfx_stage18_eridan_in-out_v2.ogg",
      "kra_sfx_stage18_eridan_instakill_v2.ogg",
      "kra_sfx_tower_paladinCovenant_unitDeath_var1_v1.ogg",
      "kra_sfx_tower_paladinCovenant_unitDeath_var2_v1.ogg",
      "kra_sfx_tower_paladinCovenant_unitDeath_var3_v1.ogg", 
      "kra_sfx_stage18_lamp_break_op1_v1.ogg",
      "kra_sfx_easterEgg_stage18Sheepy_tap-OPEN_v1.ogg",
      "kra_sfx_easterEgg_stage18Sheepy_tap-CLOSE_v1.ogg",
    }
  },
  stage_19 = {
    files = {
      "kra_sfx_stage19_bossFight_navira_enter_v1.ogg",
      "kra_sfx_stage19_bossFight_navira_fireball_cast_var1_v1.ogg",
      "kra_sfx_stage19_bossFight_navira_fireball_cast_var2_v1.ogg",
      "kra_sfx_stage19_bossFight_navira_fireball_cast_var3_v1.ogg",
      "kra_sfx_stage19_bossFight_navira_fireball_spawn_var1_v1.ogg",
      "kra_sfx_stage19_bossFight_navira_fireball_spawn_var2_v1.ogg",
      "kra_sfx_stage19_bossFight_navira_fireball_spawn_var3_v1.ogg",
      "kra_sfx_stage19_bossFight_navira_fireball_impact_var1_v2.ogg",
      "kra_sfx_stage19_bossFight_navira_fireball_impact_var2_v2.ogg",
      "kra_sfx_stage19_bossFight_navira_fireball_impact_var3_v2.ogg",
      "kra_sfx_stage19_bossFight_statue_handsDown_v1.ogg",
      "kra_sfx_stage19_bossFight_statue_handsUp_v1.ogg",
      "kra_sfx_stage19_bossFight_navira_tornado_transformIn_op1_v1.ogg",
      "kra_sfx_stage19_bossFight_navira_tornado_transformOut_v1.ogg",
      "kra_sfx_stage19_bossFight_navira_death_v1.ogg",
      "kra_sfx_stage19_statueGame_tap12_v1.ogg",
      "kra_sfx_stage19_statueGame_tap3_v1.ogg",
    }
  },
  stage_20 = {
    files = {
      "kra_sfx_crocs_tree_wakeup_op1_v1.ogg",
      "kra_sfx_crocs_tree_wakeup_op2_v1.ogg",
      "kra_sfx_crocs_tree_wakeup_op3_v1.ogg",
      "kra_sfx_crocs_head_scratch_v1.ogg",
      "kra_sfx_crocs_trunk_hit_floor_v1.ogg",
      "kra_sfx_tower_logBounce_var1_v1.ogg",
      "kra_sfx_tower_logBounce_var2_v1.ogg",
      "kra_sfx_tower_logBounce_var3_v1.ogg",
      "kra_sfx_crocs_bee_flying_v1.ogg",
      "kra_sfx_crocs_bee_hit_floor_IMPACT_v1.ogg",
      "kra_sfx_crocs_bee_hit_floor_THROW_v1.ogg",
      "kra_sfx_crocs_arboreans_house_destroy_v1.ogg",
      "kra_sfx_crocs_arboreans_house_hit_var1_v1.ogg",
      "kra_sfx_crocs_arboreans_house_hit_var2_v1.ogg",
      "kra_sfx_crocs_arboreans_house_hit_var3_v1.ogg",
    }
  },
  stage_21 = {
    files = {
      "kra_sfx_crocs_juancho_engine_fail_v1.ogg",
      "kra_sfx_crocs_juancho_engine_sucess_v1.ogg",
    }
  },
  stage_22 = {
    files = {
      "kra_sfx_crocs_abominor_acid_hit_floor_var1_v1.ogg",
      "kra_sfx_crocs_abominor_acid_hit_floor_var2_v1.ogg",
      "kra_sfx_crocs_abominor_acid_hit_floor_var3_v1.ogg",
      "kra_sfx_crocs_abominor_catch_arm_again_v1.ogg",
      "kra_sfx_crocs_abominor_death_v1.ogg",
      "kra_sfx_crocs_abominor_eat_enemy_op2_v1.ogg",
      "kra_sfx_crocs_abominor_eat_tower_eat_v1.ogg",
      "kra_sfx_crocs_abominor_eat_tower_fistRemove_v1.ogg",
      "kra_sfx_crocs_abominor_fireball_hit_floor_var1_v1.ogg",
      "kra_sfx_crocs_abominor_fireball_hit_floor_var2_v1.ogg",
      "kra_sfx_crocs_abominor_fireball_hit_floor_var3_v1.ogg",
      "kra_sfx_crocs_abominor_hit_enemy_var1_v1.ogg",
      "kra_sfx_crocs_abominor_hit_enemy_var2_v1.ogg",
      "kra_sfx_crocs_abominor_hit_enemy_var3_v1.ogg",
      "kra_sfx_crocs_abominor_release_arm_v1.ogg",
      "kra_sfx_crocs_abominor_grow_v1.ogg",
      'kra_sfx_crocs_abominor_release_eatTower_v2.ogg',
      'kra_sfx_crocs_abominor_release_arm-eatTower_v2.ogg',
      "kra_sfx_crocs_abominor_set_free_v1.ogg",
      "kra_sfx_crocs_abominor_shoot_acid_throw_v1.ogg",
      "kra_sfx_crocs_abominor_shoot_fireball_v1.ogg",
      "kra_sfx_crocs_abominor_spit_eggs_var1_v1.ogg",
      "kra_sfx_crocs_abominor_stones_hit_floor_oneShot_v1.ogg",
      "kra_sfx_crocs_magic_tower_restore_op1_v1.ogg",
    }
  },

  stage_23 = {
    files = {
      "kra_sfx_stage23_automatonFoot_open_v1.ogg",
      "kra_sfx_stage23_automatonFoot_close_v1.ogg",
      "kra_sfx_stage23_darksteelGuardian_activation_v1.ogg",
      "kra_sfx_stage23_darksteelGuardian_rockBreak_v1.ogg",
      "kra_sfx_easterEgg_truck_oneShot_v1.ogg",
      "kra_sfx_easterEgg_truck_tap3_full_v1.ogg",
    }
  },

  stage_24 = {
    files = {
      "kra_sfx_stage24_upgradeStation_in_op2_v1.ogg",
      "kra_sfx_stage24_upgradeStation_out_v1.ogg",
      "kra_sfx_stage24_upgradeStation_transform_v1.ogg",
      "kra_sfx_stage24_machinist_enter_op1_v1.ogg",
      "kra_sfx_stage24_machinist_exit_v1.ogg",
      "kra_sfx_stage24_machinist_lever_1_v1.ogg",
      "kra_sfx_stage24_machinist_lever_2_v1.ogg",
      "kra_sfx_stage24_machinist_lever_3_v1.ogg",
      "kra_sfx_stage24_factory_turnOn_start_v1.ogg",
      "kra_sfx_stage24_factory_turnOn_end_v1.ogg",
      "kra_sfx_stage24_factory_turnOff_v1.ogg",
      "kra_sfx_stage24_outro_v1.ogg",
      "kra_sfx_stage24_bossFight_machinist_cannon_cast_shot_v1.ogg",
      "kra_sfx_stage24_bossFight_machinist_cannon_impact_v1.ogg",
    }
  },

  stage_25 = {
    files = {
      "kra_sfx_stage25_torso_open_v1.ogg",
      "kra_sfx_stage25_torso_operate_lever1_v1.ogg",
      "kra_sfx_stage25_torso_operate_lever2_v1.ogg",
      "kra_sfx_stage25_torso_close_v1.ogg",
      "kra_sfx_stage25_fist_slam_v1.ogg",
      "kra_sfx_stage25_torso_button_v1.ogg",
      "kra_sfx_stage25_missile_launch_v1.ogg",
      "kra_sfx_stage25_missile_impact_v1.ogg",
      "kra_sfx_stage25_intro_crash_v1.ogg",
      "kra_sfx_stage25_intro_crash-finalExplosion_v1.ogg",
      "kra_sfx_stage25_outro_v1.ogg",
      "kra_sfx_easterEgg_solidSnake_tap12_v1.ogg",
      "kra_sfx_easterEgg_solidSnake_tap3-MG_v1.ogg",
    }
  },

  stage_26 = {
    files = {
      "kra_sfx_stage26_fistSpawner_HAND_var1_v1.ogg",
      "kra_sfx_stage26_fistSpawner_HAND_var2_v1.ogg",
      "kra_sfx_stage26_fistSpawner_HAND_var3_v1.ogg",
      "kra_sfx_stage26_fistSpawner_BoothFrontDoorOpen_v1.ogg",
      "kra_sfx_stage26_fistSpawner_BoothFrontDoorClose_v1.ogg",
      "kra_sfx_stage26_cloneSpawner_IN.ogg",
      "kra_sfx_stage26_cloneSpawner_OUT.ogg",
      "kra_sfx_stage26_hulkSpawner_shot-transform_oneShot_v1.ogg",
      "kra_sfx_stage26_preBFCinematic_v1.ogg",
      "kra_sfx_stage26_bossFight_grymbeard_damaged_v1.ogg",
      "kra_sfx_stage26_outro_v2.ogg",
      "kra_sfx_stage26_grymbeardChainPull_short.ogg",
      "kra_sfx_easterEgg_mewtwo_tap12_v1.ogg",
      "kra_sfx_easterEgg_mewtwo_tap3_v1.ogg",
      "kra_sfx_easterEgg_mewtwo_flight_fullSeq_v1.ogg",
    }
  },

  stage_27 = {
    files = {
      "kra_sfx_stage27_intro_v1.ogg",
      "kra_sfx_stage27_platformUp_v1.ogg",
      "kra_sfx_stage27_platformDown_v1.ogg",
      "kra_sfx_stage27_platformDestroy_chains_v1.ogg",
      "kra_sfx_stage27_platformDestroy_headImpacts_v1.ogg",
      "kra_sfx_stage27_cloneCannon_oneShot_shot-retreat_v1.ogg",
      "kra_sfx_stage27_cloneCannon_alarm_v1.ogg",
      "kra_sfx_stage27_headOpen_v1.ogg",
      "kra_sfx_stage27_headClose_v1.ogg",
      "kra_sfx_stage27_headFireblast_move_v1.ogg",
      "kra_sfx_stage27_headFireblast_move-returntocenter_v1.ogg",
      "kra_sfx_stage27_headFireblast_charge_v1.ogg",
      "kra_sfx_stage27_headFireblast_release_v1.ogg",
      "kra_sfx_stage27_headFireblast_cancelTap_v1.ogg",
      "kra_sfx_stage27_headFireblast_interrupt_v1.ogg",
      "kra_sfx_stage27_preBossFightCinematic_v1.ogg",
      "kra_sfx_stage27_bossFight_grymbeard_death_v1.ogg",
      "kra_sfx_stage27_bossFight_grymbeard_meleeAttack_var1_v1.ogg",
      "kra_sfx_stage27_bossFight_grymbeard_meleeAttack_var2_v1.ogg",
      "kra_sfx_stage27_bossFight_grymbeard_meleeAttack_var3_v1.ogg",
      "kra_sfx_stage27_bossFight_grymbeard_rangedAttack_cast_v1.ogg",
      "kra_sfx_stage27_bossFight_grymbeard_rangedAttack_impact_v1.ogg",
      "kra_sfx_stage27_bossFight_robotScrap_cast_v1.ogg",
      "kra_sfx-easterEgg_beamWorkers_tap1_v1.ogg",
      "kra_sfx-easterEgg_beamWorkers_tap2_v1.ogg",
      "kra_sfx-easterEgg_beamWorkers_tap3_v1.ogg",
      "kra_sfx_easterEgg_sheepyTerrain2_camera_v1.ogg",
    }
},
stage_28 = {
files = {
}
},

stage_29 = {
files = {
"kra_sfx_spiders_mechanic_spawner_prev_v1.ogg",
"kra_sfx_spiders_mechanic_spawner_explode_v1.ogg",
"kra_sfx_spiders_mechanic_spawner_regenerate_v1.ogg",
}
},

stage_30 = {
files = {
  "kra_sfx_spiders_bossfight_buff_charge_v1.ogg",
	"kra_sfx_spiders_bossfight_cinematic_op1_v1.ogg",
	"kra_sfx_spiders_bossfight_claw_close_v1.ogg",
	"kra_sfx_spiders_bossfight_claw_open_v1.ogg",
	"kra_sfx_spiders_bossfight_dead_v1.ogg",
	"kra_sfx_spiders_bossfight_drain_charge_loop-start_v1.ogg",
	"kra_sfx_spiders_bossfight_drain_charge_loop_v1.ogg",
	"kra_sfx_spiders_bossfight_drain_charge_oneShot_v1.ogg",
	"kra_sfx_spiders_bossfight_drain_execute_v1.ogg",
	"kra_sfx_spiders_bossfight_fall_v1.ogg",
	"kra_sfx_spiders_bossfight_jump_v1.ogg",
	"kra_sfx_spiders_bossfight_range_var1_v1.ogg",
	"kra_sfx_spiders_bossfight_range_var2_v1.ogg",
	"kra_sfx_spiders_bossfight_range_var3_v1.ogg",
	"kra_sfx_spiders_bossfight_spit_v1.ogg",
}
  },

  stage_31 = {
    files = {
      "kra_sfx_wukong_mechanic_stage1_fountain_refill_v1.ogg",
      "kra_sfx_wukong_mechanic_stage1_fountain_splash_v1.ogg",
      "kra_sfx_wukong_mechanic_stage1_fountain_tapon_var1_v1.ogg",
      "kra_sfx_wukong_mechanic_stage1_fountain_tapon_var2_v1.ogg",
      "kra_sfx_wukong_mechanic_stage1_fountain_tapon_var3_v1.ogg",
      "kra_sfx_wukong_mechanic_stage1_meteorites_LOOP_in_faded_martin.ogg",
      "kra_sfx_wukong_mechanic_stage1_meteorites_impact_v2.ogg",
      "kra_sfx_wukong_mechanic_stage1_meteorites_travel-LOOP_v1.ogg",
    }
  },

  stage_32 = {
    files = {
      "kra_sfx_wukong_mechanic_stage1_meteorites_LOOP_in_faded_martin.ogg",
      "kra_sfx_wukong_mechanic_stage1_meteorites_impact_v2.ogg",
      "kra_sfx_wukong_mechanic_stage1_meteorites_travel-LOOP_v1.ogg",
      "kra_sfx_wukong_stage32_redboy_samadhi_fire_part1_v2.ogg",
      "kra_sfx_wukong_stage32_redboy_samadhi_fire_part2_v2.ogg",
      "kra_sfx_wukong_stage32_redboy_lava_surge_woVoice_v2.ogg",
      "kra_sfx_wukong_stage32_redboy_explosion_v1.ogg",
      "kra_sfx_wukong_stage32_dragon_roar_v1.ogg",
      "kra_sfx_wukong_stage32_dragon_lava_spit_op2_v1.ogg",
      "kra_sfx_wukong_stage32_redboy_transform_v1.ogg",
      "kra_sfx_wukong_stage32_redboy_entrance_op2_v1.ogg",
      "kra_sfx_wukong_stage32_redboy_samadhi_teen_prep_op2_v1.ogg",
      "kra_sfx_wukong_stage32_redboy_death_part1_v1.ogg",
      "kra_sfx_wukong_stage32_redboy_death_part2_v1.ogg"
    }
  },

  stage_33 = {
    files = {
      "kra_sfx_wukong_mechanic_stage3_storm_ambience_LOOPStart_v1.ogg",
      "kra_sfx_wukong_mechanic_stage3_storm_ambience_LOOP_v1.ogg",
      "kra_sfx_wukong_mechanic_stage3_storm_ambience_distantThunder_var1_v1.ogg",
      "kra_sfx_wukong_mechanic_stage3_storm_ambience_distantThunder_var2_v1.ogg",
      "kra_sfx_wukong_mechanic_stage3_storm_ambience_distantThunder_var3_v1.ogg",
      "kra_sfx_wukong_mechanic_stage3_storm_lightning_strike_var1_v1.ogg",
      "kra_sfx_wukong_mechanic_stage3_storm_lightning_strike_var2_v1.ogg",
      "kra_sfx_wukong_mechanic_stage3_storm_lightning_strike_var3_v1.ogg",
      "kra_sfx_wukong_mechanic_stage3_storm_lightning_mark_v1.ogg",
      "kra_sfx_wukong_mechanic_stage3_boat_drum_op2_v1.ogg"
    }
  },

  stage_34 = {
    files = {
      "kra_sfx_wukong_princess_clone_v1.ogg",
      "kra_sfx_wukong_princess_death_v1.ogg",
      "kra_sfx_wukong_princess_hero_stun_channel_v2.ogg",
      "kra_sfx_wukong_princess_hero_stun_fail_woVoice_v1.ogg",
      "kra_sfx_wukong_princess_hero_stun_fail_wVoice_v1.ogg",
      "kra_sfx_wukong_princess_hero_stun_success_v2.ogg",
      "kra_sfx_wukong_princess_melee_area_v1.ogg",
      "kra_sfx_wukong_princess_melee_var1_v1.ogg",
      "kra_sfx_wukong_princess_melee_var2_v1.ogg",
      "kra_sfx_wukong_princess_melee_var3_v1.ogg",
      "kra_sfx_wukong_princess_mud_pool_transformation_v1.ogg",
      "kra_sfx_wukong_princess_mud_summon_var1_v1.ogg",
      "kra_sfx_wukong_princess_mud_summon_var2_v1.ogg",
      "kra_sfx_wukong_princess_mud_summon_var3_v1.ogg",
      "kra_sfx_wukong_princess_mud_summon_var4_v1.ogg",
      "kra_sfx_wukong_princess_mud_summon_var5_v1.ogg",
      "kra_sfx_wukong_princess_mud_tower_op1_v2.ogg",
      "kra_sfx_wukong_princess_mud_tower_op2_v2.ogg",
      "kra_sfx_wukong_princess_ranged_cast_v1.ogg",
      "kra_sfx_wukong_princess_ranged_impact_var1_v1.ogg",
      "kra_sfx_wukong_princess_ranged_impact_var2_v1.ogg",
      "kra_sfx_wukong_princess_teleport-IN_v1.ogg",
      "kra_sfx_wukong_princess_teleport-OUT_v1.ogg"
    }
  },

  stage_35 = {
    files = {
      "kra_sfx_wukong_princess_hero_stun_channel_v2.ogg",
      "kra_sfx_wukong_princess_hero_stun_fail_woVoice_v1.ogg",
      "kra_sfx_wukong_princess_hero_stun_fail_wVoice_v1.ogg",
      "kra_sfx_wukong_princess_hero_stun_success_v2.ogg",
      "kra_sfx_wukong_princess_mud_tower_op1_v2.ogg",
      "kra_sfx_wukong_princess_mud_tower_op2_v2.ogg",
    }
  },

  --Items
  item_cluster_bomb = {
    files = {
      "kra_sfx_inApps_clusterBomb_cast_v1.ogg",
      "kra_sfx_inApps_clusterBomb_smallBombs_v1.ogg",
    }
  },
  item_portable_coil = {
    files = {
      "kra_sfx_inApps_portableCoil_cast_v1.ogg",
      "kra_sfx_inApps_portableCoil_attack_var1_v1.ogg",
      "kra_sfx_inApps_portableCoil_attack_var2_v1.ogg",
      "kra_sfx_inApps_portableCoil_attack_var3_v1.ogg",
    }
  },
  item_scroll_of_spaceshift = {
    files = {
      "kra_sfx_inApps_scrollOfSpaceshift_cast_v1.ogg",
      "kra_sfx_inApps_scrollOfSpaceshift_teleportIn_v1.ogg",
      "kra_sfx_inApps_scrollOfSpaceshift_teleportOut_v1.ogg",
    }
  },
  item_second_breath = {
    files = {
      "kra_sfx_inApps_secondBreath_cast_v1.ogg",
    }
  },
  item_deaths_touch = {
    files = {
      "kra_sfx_inApps_deathsTouch_cast_v1.ogg",
    }
  },
  item_winter_age = {
    files = {
      "kra_sfx_inApps_winterAge_cast_v1.ogg",
      "kra_sfx_inApps_winterAge_loop_v1.ogg",
      "kra_sfx_inApps_winterAge_release_v1.ogg",
    }
  },
  item_loot_box = {
    files = {
      "kra_sfx_inApps_lootBox_cast_v1.ogg",
    }
  },
  item_medical_kit = {
    files = {
      "kra_sfx_inApps_medicalKit_cast_op1_v1.ogg",
      "kra_sfx_inApps_medicalKit_heartAdd_v1.ogg",
    }
  },
  item_summon_blackburn = {
    files = {
      "kra_sfx_inApps_helmOfBlackburn_cast.ogg",
      "kra_sfx_inApps_helmOfBlackburn_meleeAttack_var1_v1.ogg",
      "kra_sfx_inApps_helmOfBlackburn_meleeAttack_var2_v1.ogg",
      "kra_sfx_inApps_helmOfBlackburn_meleeAttack_var3_v1.ogg",
      "kra_sfx_inApps_helmOfBlackburn_rangedAttack_op2_v1.ogg",
    }
  },
  item_veznan_wrath = {
    files = {
      "kr5_sfx_veznanwrath_appear_sinrisa_v1.ogg",
      "kr5_sfx_veznanwrath_initialburst_v1.ogg",
      "kr5_sfx_veznanwrath_flame_var1_v1.ogg",
      "kr5_sfx_veznanwrath_flame_var3_v1.ogg",
      "kr5_sfx_veznanwrath_flame_var4_v1.ogg",
    }
  },
}
