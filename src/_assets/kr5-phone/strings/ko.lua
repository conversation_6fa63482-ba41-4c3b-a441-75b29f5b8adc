-- ------------------------------------------------
-- -- WARNING: DO NOT EDIT BY HAND                 
-- -- Generated by kr-i18n/tools/strings-export.lua
-- ------------------------------------------------
return {
["!!!COMMENT_LOCALIZATION_SOURCE"] = "Keywords + testers (fiew for kr2)",
["%d Life"] = "%d 라이프",
["%d Lives"] = "%d 라이프",
["%i sec."] = "%i 초",
["- if heroes are allowed"] = "- 영웅이 허용될 경우",
["- max upgrade level allowed"] = "- 최고 업그레이드 레벨 허용됨",
["A good challenge!"] = "멋진 도전입니다!",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "타락한 윌리",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "타락한 헨리",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "타락한 제프리",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "변비에 걸린 니콜라스",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "에도미네이션",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "호보미네이션",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "오도미네이션",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "타락한 세드릭",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "할보미네이션",
["ACHIEVEMENT"] = "도전 과제",
["ACHIEVEMENTS"] = "도전 과제",
["ACHIEVEMENTS_TITLE"] = "도전 과제",
["ACHIEVEMENT_AGE_OF_HEROES_DESCRIPTION"] = "모든 영웅 모드 캠페인 도전을 완료하세요.",
["ACHIEVEMENT_AGE_OF_HEROES_NAME"] = "영웅의 시대",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_DESCRIPTION"] = "블링커 182 마리를 제거하세요.",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_NAME"] = "모든 작은 것들",
["ACHIEVEMENT_ARACHNED_DESCRIPTION"] = "거미 여왕 미갈을 처치하라.",
["ACHIEVEMENT_ARACHNED_NAME"] = "무기와의 작별",
["ACHIEVEMENT_A_COON_OF_SURPRISES_DESCRIPTION"] = "프레도가 탈출할 수 있도록 도와라.",
["ACHIEVEMENT_A_COON_OF_SURPRISES_NAME"] = "놀라운 고치",
["ACHIEVEMENT_A_TEST_OF_PROWESS_DESCRIPTION"] = "한 스테이지에서 별 3 개를 획득하세요.",
["ACHIEVEMENT_A_TEST_OF_PROWESS_NAME"] = "숙련도 시험",
["ACHIEVEMENT_BREAKER_OF_CHAINS_DESCRIPTION"] = "카민 광산에서 네 명의 엘프를 구하세요",
["ACHIEVEMENT_BREAKER_OF_CHAINS_NAME"] = "체인 브레이커",
["ACHIEVEMENT_BUTTERTENTACLES_DESCRIPTION"] = "미드리아스가 당신의 유닛을 포획하는 것을 막으면서 눈엣가시 타워를 완료하세요.",
["ACHIEVEMENT_BUTTERTENTACLES_NAME"] = "미끄러운 병사들",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_DESCRIPTION"] = "예언자 미드리아스를 물리치세요.",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_NAME"] = "안녕, 안녕, 아름다운 이여",
["ACHIEVEMENT_CIRCLE_OF_LIFE_DESCRIPTION"] = "갓태어난 아보리아인의 발표에 참석하세요.",
["ACHIEVEMENT_CIRCLE_OF_LIFE_NAME"] = "생명의 순환",
["ACHIEVEMENT_CLEANSE_THE_KING_DESCRIPTION"] = "리니리아 왕을 구출하세요.",
["ACHIEVEMENT_CLEANSE_THE_KING_NAME"] = "왕에게 영광을",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_DESCRIPTION"] = "전략 지점의 잔해를 치우지 않고 황폐화된 교외를 완료하세요.",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_NAME"] = "청소는 선택사항입니다",
["ACHIEVEMENT_CONJUNTIVICTORY_DESCRIPTION"] = "오버시어를 물리치세요.",
["ACHIEVEMENT_CONJUNTIVICTORY_NAME"] = "공동승리",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_DESCRIPTION"] = "공허의 저편의 모든 스테이지에서 별 3 개를 획득하세요.",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_NAME"] = "공허의 정복자",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_DESCRIPTION"] = "야수들의 소굴에서 돼지 갈비살 세 개를 모두 모으세요.",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_NAME"] = "광산 제작",
["ACHIEVEMENT_CROWD_CONTROL_DESCRIPTION"] = "구덩이에서 육체 거대괴수가 나타나지 않은 채로 부패의 계곡을 완료하세요.",
["ACHIEVEMENT_CROWD_CONTROL_NAME"] = "군중 제어",
["ACHIEVEMENT_CROW_SCARER_DESCRIPTION"] = "황량한 계곡의 모든 까마귀를 쫓아내세요",
["ACHIEVEMENT_CROW_SCARER_NAME"] = "까마귀 허수아비",
["ACHIEVEMENT_CRYSTAL_CLEAR_DESCRIPTION"] = "버려진 협곡의 모든 스테이지에서 별 3 개를 획득하세요.",
["ACHIEVEMENT_CRYSTAL_CLEAR_NAME"] = "크리스탈 클리어",
["ACHIEVEMENT_DARK_LIEUTENANT_DESCRIPTION"] = "레일린을 레벨 10까지 성장시키세요.",
["ACHIEVEMENT_DARK_LIEUTENANT_NAME"] = "암흑 중위",
["ACHIEVEMENT_DARK_RUTHLESSNESS_DESCRIPTION"] = "암흑 군대의 타워와 영웅만을 사용하여 한 스테이지에서 승리하세요.",
["ACHIEVEMENT_DARK_RUTHLESSNESS_NAME"] = "냉혹한 어둠",
["ACHIEVEMENT_DISTURBING_THE_PEACE_DESCRIPTION"] = "도미니언 돔에서 근로자들의 점심 시간을 방해하세요.",
["ACHIEVEMENT_DISTURBING_THE_PEACE_NAME"] = "평화 방해",
["ACHIEVEMENT_DLC1_WIN_BOSS_DESCRIPTION"] = "그림비어드를 처치하고 전쟁 기계 건설을 중단시키세요.",
["ACHIEVEMENT_DLC1_WIN_BOSS_NAME"] = "자가 실업",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_DESCRIPTION"] = "템페스트 섬에서 홍바오 8개를 모으세요.",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_NAME"] = "부와 번영을 기원합니다.",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_DESCRIPTION"] = "요새에서 우마왕을 물리치세요.",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_NAME"] = "손오공의 귀환",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_DESCRIPTION"] = "철선공주와 그녀의 수중 부대를 처치하라.",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_NAME"] = "사악한 바람이 일어나고 있다",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_DESCRIPTION"] = "홍해아와 그의 불의 군대를 물리치세요.",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_NAME"] = "모든 것이 변했다...",
["ACHIEVEMENT_DOMO_ARIGATO_DESCRIPTION"] = "거대한 코어에서 커다란 주먹으로 20 명의 적을 처치하세요.",
["ACHIEVEMENT_DOMO_ARIGATO_NAME"] = "도모 아리가토",
["ACHIEVEMENT_FACTORY_STRIKE_DESCRIPTION"] = "센트리의 생성을 막은 채로 광란의 조립 라인을 완료하세요.",
["ACHIEVEMENT_FACTORY_STRIKE_NAME"] = "공장 파업",
["ACHIEVEMENT_FIELD_TRIP_RUINER_DESCRIPTION"] = "캠퍼의 불을 끄세요.",
["ACHIEVEMENT_FIELD_TRIP_RUINER_NAME"] = "야외 수업 망치는 사람",
["ACHIEVEMENT_FOREST_PROTECTOR_DESCRIPTION"] = "니루를 레벨 10까지 성장시키세요.",
["ACHIEVEMENT_FOREST_PROTECTOR_NAME"] = "숲의 수호자",
["ACHIEVEMENT_GARBAGE_DISPOSAL_DESCRIPTION"] = "10 명의 미친 수리공들을 고철 드론을 생성하기 전에 처치하세요.",
["ACHIEVEMENT_GARBAGE_DISPOSAL_NAME"] = "쓰레기 처리",
["ACHIEVEMENT_GEM_SPILLER_DESCRIPTION"] = "모든 보석 바구니를 부수세요.",
["ACHIEVEMENT_GEM_SPILLER_NAME"] = "보석을 흘리는 자",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_DESCRIPTION"] = "퍼즐을 풀고 밴드를 소환하세요.",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_NAME"] = "파티를 시작하세요",
["ACHIEVEMENT_GIFT_OF_LIFE_DESCRIPTION"] = "복제실에서 복제 실험을 해방시키세요.",
["ACHIEVEMENT_GIFT_OF_LIFE_NAME"] = "생명의 선물",
["ACHIEVEMENT_GREENLIT_ALLIES_DESCRIPTION"] = "아르보리아 가시창전사 10 명을 소환하세요",
["ACHIEVEMENT_GREENLIT_ALLIES_NAME"] = "푸른빛 동맹",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_DESCRIPTION"] = "악어 왕을 찾으세요.",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_NAME"] = "K에게 경의를, 베이비!",
["ACHIEVEMENT_HEARTLESS_VICTORY_DESCRIPTION"] = "아르보리아의 심장의 능력을 사용하지 않고 숲의 심장을 완료하세요.",
["ACHIEVEMENT_HEARTLESS_VICTORY_NAME"] = "냉혹한 승리",
["ACHIEVEMENT_INTO_THE_OGREVERSE_DESCRIPTION"] = "신비로운 거미 인간의 비밀을 밝혀라.",
["ACHIEVEMENT_INTO_THE_OGREVERSE_NAME"] = "불친절한 이웃",
["ACHIEVEMENT_IRONCLAD_DESCRIPTION"] = "모든 강철 모드 켐페인 도전을 완료하세요.",
["ACHIEVEMENT_IRONCLAD_NAME"] = "철갑",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_DESCRIPTION"] = "랭크가 5 루피를 낚도록 도와주세요.",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_NAME"] = "모두에게 비밀이야",
["ACHIEVEMENT_KEPT_YOU_WAITING_DESCRIPTION"] = "거대한 코어에서 은밀한 병사를 찾으세요.",
["ACHIEVEMENT_KEPT_YOU_WAITING_NAME"] = "기다리게 했지, 응?",
["ACHIEVEMENT_LEARNING_THE_ROPES_DESCRIPTION"] = "튜토리얼에서 별 3 개를 획득하세요.",
["ACHIEVEMENT_LEARNING_THE_ROPES_NAME"] = "요령 익히기",
["ACHIEVEMENT_LINIREAN_RESISTANCE_DESCRIPTION"] = "리니리아 타워와 영웅만을 사용하여 한 스테이지에서 승리하세요.",
["ACHIEVEMENT_LINIREAN_RESISTANCE_NAME"] = "리니리아의 저항",
["ACHIEVEMENT_LUCAS_SPIDER_DESCRIPTION"] = "루쿠스가 만족할 때까지 놀아라.",
["ACHIEVEMENT_LUCAS_SPIDER_NAME"] = "거미 루쿠스",
["ACHIEVEMENT_MASTER_TACTICIAN_DESCRIPTION"] = "불가능 난이도로 캠페인을 완료하세요.",
["ACHIEVEMENT_MASTER_TACTICIAN_NAME"] = "마스터 전술가",
["ACHIEVEMENT_MECHANICAL_BURNOUT_DESCRIPTION"] = "다크스틸 게이트에서 기계에 과잉공급하세요.",
["ACHIEVEMENT_MECHANICAL_BURNOUT_NAME"] = "기계적 탈진",
["ACHIEVEMENT_MIGHTY_III_DESCRIPTION"] = "적 10000 명을 처치하세요.",
["ACHIEVEMENT_MIGHTY_III_NAME"] = "마이티 III",
["ACHIEVEMENT_MIGHTY_II_DESCRIPTION"] = "적 3000 명을 처치하세요.",
["ACHIEVEMENT_MIGHTY_II_NAME"] = "마이티 II",
["ACHIEVEMENT_MIGHTY_I_DESCRIPTION"] = "적 500 명을 처치하세요.",
["ACHIEVEMENT_MIGHTY_I_NAME"] = "마이티 I",
["ACHIEVEMENT_MOST_DELICIOUS_DESCRIPTION"] = "아르보리아인 비기에게 꿀을 좀 주세요.",
["ACHIEVEMENT_MOST_DELICIOUS_NAME"] = "가장 맛있는 것",
["ACHIEVEMENT_NATURES_WRATH_DESCRIPTION"] = "아르보리아의 심장을 사용하여 30 명의 적을 처치하세요.",
["ACHIEVEMENT_NATURES_WRATH_NAME"] = "자연의 분노",
["ACHIEVEMENT_NONE_SHALL_PASS_DESCRIPTION"] = "추가 적들이 문을 통과하지 못하게 한 채 야수들의 소굴을 완료하세요.",
["ACHIEVEMENT_NONE_SHALL_PASS_NAME"] = "아무도 못 지나간다!",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_DESCRIPTION"] = "웨이브를 15 차례 조기 시작하세요.",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_NAME"] = "한 순간도 낭비할 수 없어",
["ACHIEVEMENT_NO_FLY_ZONE_DESCRIPTION"] = "부양 거미 50마리를 처치하라.",
["ACHIEVEMENT_NO_FLY_ZONE_NAME"] = "비행 금지 구역",
["ACHIEVEMENT_OBLITERATE_DESCRIPTION"] = "각 거대한 위협 스테이지에서 금지된 로봇의 부품을 찾으세요",
["ACHIEVEMENT_OBLITERATE_NAME"] = "흔적 지우기!",
["ACHIEVEMENT_ONE_SHOT_TOWER_DESCRIPTION"] = "암흑광선 타워의 빔으로 한번에 10 명의 적을 제거하세요.",
["ACHIEVEMENT_ONE_SHOT_TOWER_NAME"] = "영광을 향한 한 방",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_DESCRIPTION"] = "불가능 난이도에서, 고어그라인드가 점프하기 전에 물리치세요.",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_NAME"] = "얽힘",
["ACHIEVEMENT_OVER_THE_EDGE_DESCRIPTION"] = "나무 꼭대기에서 아르보리아인들을 미세요.",
["ACHIEVEMENT_OVER_THE_EDGE_NAME"] = "게임 오버",
["ACHIEVEMENT_OVINE_JOURNALISM_DESCRIPTION"] = "각 캠페인 지형에서 쉬피를 찾으세요.",
["ACHIEVEMENT_OVINE_JOURNALISM_NAME"] = "양 저널리즘",
["ACHIEVEMENT_PEST_CONTROL_DESCRIPTION"] = "조막눈 300 마리를 처치하세요.",
["ACHIEVEMENT_PEST_CONTROL_NAME"] = "해충 방제",
["ACHIEVEMENT_PLAYFUL_FRIENDS_DESCRIPTION"] = "숲의 심장에서 모든 아르보리아인들과 \"굴파기\" 놀이를 하세요.",
["ACHIEVEMENT_PLAYFUL_FRIENDS_NAME"] = "장난스러운 친구들",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_DESCRIPTION"] = "고어그라인드를 처치하세요.",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_NAME"] = "메뉴에서 돼지고기 제외",
["ACHIEVEMENT_PROMOTION_DENIED_DESCRIPTION"] = "그들이 흉물로 변하기 전에 이교도 사제 30 명을 처치하세요.",
["ACHIEVEMENT_PROMOTION_DENIED_NAME"] = "거부된 승진",
["ACHIEVEMENT_ROCK_BEATS_ROCK_DESCRIPTION"] = "동상이 스스로 승리하게 하세요.",
["ACHIEVEMENT_ROCK_BEATS_ROCK_NAME"] = "바위가 바위를 이긴다?",
["ACHIEVEMENT_ROOM_achievement_claim"] = "보상을 청구하세요!",
["ACHIEVEMENT_ROYAL_CAPTAIN_DESCRIPTION"] = "베스퍼를 레벨 10까지 성장시키세요.",
["ACHIEVEMENT_ROYAL_CAPTAIN_NAME"] = "왕실 대위",
["ACHIEVEMENT_RUNEQUEST_DESCRIPTION"] = "늘빛숲 전체에 있는 여섯 룬을 모두 활성화하세요.",
["ACHIEVEMENT_RUNEQUEST_NAME"] = "룬 퀘스트",
["ACHIEVEMENT_RUST_IN_PEACE_DESCRIPTION"] = "살아 움직이는 갑옷이 부활하지 않도록 하면서 스테이지를 완료하세요.",
["ACHIEVEMENT_RUST_IN_PEACE_NAME"] = "평안히 녹슬다",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_DESCRIPTION"] = "아르보리아의 꽃을 하나도 잃지 않고 스테이지를 이기세요.",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_NAME"] = "숲의 구원자",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_DESCRIPTION"] = "늘빛숲의 모든 스테이지에서 별 3 개를 획득하세요.",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_NAME"] = "녹색의 구원자",
["ACHIEVEMENT_SCRAMBLED_EGGS_DESCRIPTION"] = "부화하기 전에 크로킨더 50 명을 죽이세요.",
["ACHIEVEMENT_SCRAMBLED_EGGS_NAME"] = "달걀 스크램블",
["ACHIEVEMENT_SEASONED_GENERAL_DESCRIPTION"] = "베테랑 난이도로 캠페인을 완료하세요.",
["ACHIEVEMENT_SEASONED_GENERAL_NAME"] = "노련한 사령관",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_DESCRIPTION"] = "포식자 아보미노르를 물리치세요.",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_NAME"] = "나중에 봐, 악어야",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_DESCRIPTION"] = "그림비어드가 타워를 불태우지 못하게 하면서 도미니언 돔을 완료하세요.",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_NAME"] = "입 다물어!",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_DESCRIPTION"] = "영웅의 주문을 500 회 사용하세요.",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_NAME"] = "서명 기술",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_DESCRIPTION"] = "게르하르트가 나무 몬스터를 처치하도록 도와주세요.",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_NAME"] = "몬스터를 위한 은",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_DESCRIPTION"] = "친절한 게이터가 보트를 시동 걸 수 있도록 도와주세요.",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_NAME"] = "스무드 오퍼-게이터",
["ACHIEVEMENT_SPECTRAL_FURY_DESCRIPTION"] = "나비라를 물리치고 망령의 침공을 막으세요.",
["ACHIEVEMENT_SPECTRAL_FURY_NAME"] = "영혼의 분노",
["ACHIEVEMENT_STARLIGHT_DESCRIPTION"] = "프레도와 새미가 거대 거미로부터 탈출하도록 도와주세요.",
["ACHIEVEMENT_STARLIGHT_NAME"] = "별빛",
["ACHIEVEMENT_TAKE_ME_HOME_DESCRIPTION"] = "리프 고블린을 그의 집으로 돌려보내세요.",
["ACHIEVEMENT_TAKE_ME_HOME_NAME"] = "테이크 온 미",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_DESCRIPTION"] = "1000 명의 지원군을 호출하세요.",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_NAME"] = "기병대가 왔다!",
["ACHIEVEMENT_TIPPING_THE_SCALES_DESCRIPTION"] = "로빈 우드를 강에 던지세요.",
["ACHIEVEMENT_TIPPING_THE_SCALES_NAME"] = "저울 기울이기",
["ACHIEVEMENT_TREE_HUGGER_DESCRIPTION"] = "이상한 나무가 적어도 하나 남은 상태로 안개 낀 폐허를 완료하세요.",
["ACHIEVEMENT_TREE_HUGGER_NAME"] = "나무 애호가",
["ACHIEVEMENT_TURN_A_BLIND_EYE_DESCRIPTION"] = "성난 눈의 효과를 받은 부패한 생성물 100 마리를 처치하세요.",
["ACHIEVEMENT_TURN_A_BLIND_EYE_NAME"] = "눈 감아",
["ACHIEVEMENT_UNBOUND_VICTORY_DESCRIPTION"] = "어떤 악몽도 갑옷을 입은 악몽으로 변하지 않은 채로 사악한 교차로를 완료하세요.",
["ACHIEVEMENT_UNBOUND_VICTORY_NAME"] = "해방된 승리",
["ACHIEVEMENT_UNENDING_RICHES_DESCRIPTION"] = "총 150000 골드를 수집하세요.",
["ACHIEVEMENT_UNENDING_RICHES_NAME"] = "끝없는 부",
["ACHIEVEMENT_UNTAMED_BEAST_DESCRIPTION"] = "그림슨을 레벨 10까지 성장시키세요.",
["ACHIEVEMENT_UNTAMED_BEAST_NAME"] = "길들여지지 않은 야수",
["ACHIEVEMENT_WAR_MASONRY_DESCRIPTION"] = "100 개의 타워를 건설하세요.",
["ACHIEVEMENT_WAR_MASONRY_NAME"] = "전쟁의 석공",
["ACHIEVEMENT_WEIRDER_THINGS_DESCRIPTION"] = "어니와 대스턴이 황폐한 평원에서 블링커들을 물리치도록 도와주세요.",
["ACHIEVEMENT_WEIRDER_THINGS_NAME"] = "더 기이한 것들",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_DESCRIPTION"] = "꺼지지 않는 분노 캠페인의 각 스테이지에서 잡기 힘든 고양이를 찾아보세요.",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_NAME"] = "우리 모두 미쳤어",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_DESCRIPTION"] = "뒤틀린 자매 15 명을 악몽을 생성하기 전에 처치하세요.",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_NAME"] = "우리는 그것을 받아들이지 않을 것이다",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_DESCRIPTION"] = "닉과 마티의 포탈 건을 수리하세요.",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_NAME"] = "워바-러바-덥-덥!",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_DESCRIPTION"] = "불가능 난이도에서, 예언자 미드리아스가 그림자를 만들지 못하게 한 채로 타락한 데나스를 물리치세요.",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_NAME"] = "시전하지 못할 것이다!",
["ADS_MESSAGE_OK"] = "확인",
["ADS_MESSAGE_TITLE"] = "보석 구매",
["ADS_NO_REWARD_VIDEO_AVAILABLE"] = "지금은 재생할 보상 동영상이 없습니다. 나중에 다시 시도하세요.",
["ADS_REWARD_EARNED"] = "동영상을 보고 보석 %i개를 받았습니다.",
["ADVANCED TOWERS"] = "상급 타워",
["ALERT_VERSION"] = "게임의 새 버전이 있습니다. 스토어에서 다운로드하십시오.",
["ALL FOR"] = "총 가격:",
["ARCHER TOWER"] = "궁수 타워",
["ARE YOU SURE YOU WANT TO QUIT?"] = "정말 나가시겠습니까?",
["ARMORED ENEMIES!"] = "갑옷 입은 적!",
["ARTILLERY"] = "대포",
["Achievements"] = "도전 과제",
["BARRACKS"] = "병영",
["BASIC TOWERS"] = "기본 타워",
["BEST VALUE"] = "최고 가치",
["BOSS_BULL_KING_NAME"] = "우마왕",
["BOSS_CORRUPTED_DENAS_DESCRIPTION"] = "패배한 리니리아의 왕은 이제 오버시어 추종자들의 어둠의 힘에 의해 엄청난 흉물로 변했습니다.",
["BOSS_CORRUPTED_DENAS_EXTRA"] = "- 조막눈을 생성",
["BOSS_CORRUPTED_DENAS_NAME"] = "타락한 데나스",
["BOSS_CROCS_DESCRIPTION"] = "인격화된 굶주림, 제어하지 않으면 세상 자체를 삼켜버릴 수 있는 고대 존재.",
["BOSS_CROCS_EXTRA"] = "- 타워를 먹음\n- 배고픔을 만족시킨 후에 진화함\n- 크로킨더 소환",
["BOSS_CROCS_LVL1_DESCRIPTION"] = "인격화된 굶주림, 제어하지 않으면 세상 자체를 삼켜버릴 수 있는 고대 존재.",
["BOSS_CROCS_LVL1_EXTRA"] = "- 타워를 먹음\n- 배고픔을 만족시킨 후에 진화함\n- 크로킨더 소환",
["BOSS_CROCS_LVL1_NAME"] = "아보미노르",
["BOSS_CROCS_LVL2_DESCRIPTION"] = "인격화된 굶주림, 제어하지 않으면 세상 자체를 삼켜버릴 수 있는 고대 존재.",
["BOSS_CROCS_LVL2_EXTRA"] = "- 타워를 먹음\n- 배고픔을 만족시킨 후에 진화함\n- 크로킨더 소환",
["BOSS_CROCS_LVL2_NAME"] = "아보미노르",
["BOSS_CROCS_LVL3_DESCRIPTION"] = "인격화된 굶주림, 제어하지 않으면 세상 자체를 삼켜버릴 수 있는 고대 존재.",
["BOSS_CROCS_LVL3_EXTRA"] = "- 타워를 먹음\n- 배고픔을 만족시킨 후에 진화함\n- 크로킨더 소환",
["BOSS_CROCS_LVL3_NAME"] = "아보미노르",
["BOSS_CROCS_LVL4_DESCRIPTION"] = "인격화된 굶주림, 제어하지 않으면 세상 자체를 삼켜버릴 수 있는 고대 존재.",
["BOSS_CROCS_LVL4_EXTRA"] = "- 타워를 먹음\n- 배고픔을 만족시킨 후에 진화함\n- 크로킨더 소환",
["BOSS_CROCS_LVL4_NAME"] = "아보미노르",
["BOSS_CROCS_LVL5_DESCRIPTION"] = "인격화된 굶주림, 제어하지 않으면 세상 자체를 삼켜버릴 수 있는 고대 존재.",
["BOSS_CROCS_LVL5_EXTRA"] = "- 타워를 먹음\n- 배고픔을 만족시킨 후에 진화함\n- 크로킨더 소환",
["BOSS_CROCS_LVL5_NAME"] = "아보미노르",
["BOSS_CROCS_NAME"] = "아보미노르",
["BOSS_CULT_LEADER_DESCRIPTION"] = "이교도의 현재 지도자인 미드리아스는 오버시어의 손이 되어 세계 침략을 계획합니다.",
["BOSS_CULT_LEADER_EXTRA"] = "- 저지되지 않는 동안 높은 방어력과 마법 저항력\n - 높은 범위 공격력",
["BOSS_CULT_LEADER_NAME"] = "예언자 미드리아스",
["BOSS_GRYMBEARD_DESCRIPTION"] = "위험할 정도로 광기 어린, 과대망상에 빠진 자아도취적인 드워프.",
["BOSS_GRYMBEARD_EXTRA"] = "- 플레이어 유닛을 향해 로켓 펀치를 발사합니다.",
["BOSS_GRYMBEARD_NAME"] = "그림비어드",
["BOSS_MACHINIST_DESCRIPTION"] = "그림비어드가 이 최신 발명품에 올라타 적을 추적하며 불과 금속의 비를 내립니다.",
["BOSS_MACHINIST_EXTRA"] = "- 비행\n- 유닛에게 고철을 발사",
["BOSS_MACHINIST_NAME"] = "그림비어드",
["BOSS_NAVIRA_DESCRIPTION"] = "위신을 잃고 금지된 죽음의 마법의 힘을 두드리는 나비라는 엘프들에게 영광을 회복하려고 합니다.",
["BOSS_NAVIRA_EXTRA"] = "- 불덩어리로 타워를 무력화\n- 막을 수 없는 회오리바람으로 변함",
["BOSS_NAVIRA_NAME"] = "나비라",
["BOSS_PIG_DESCRIPTION"] = "유일무이한 자칭 '야수들의 왕'인 그는 거대한 철퇴를 사용하여 적을 분쇄합니다.",
["BOSS_PIG_EXTRA"] = "- 경로를 가로질러 먼 거리를 점프",
["BOSS_PIG_NAME"] = "고어그라인드",
["BOSS_PRINCESS_IRON_FAN_DESCRIPTION"] = "우아함과 치명적인 힘을 겸비한 철선공주는 우마왕의 아내일 뿐만 아니라 스스로도 강력한 적수이다. 침착하고 계산적인 마녀. 전설의 철부채를 다루며, 불꽃을 꺼뜨리고 폭풍을 일으킬 수 있다.",
["BOSS_PRINCESS_IRON_FAN_EXTRA"] = "- 자신을 복제함\n- 영웅을 플라스크에 가둠\n- 탑을 적 생성기로 바꿈",
["BOSS_REDBOY_TEEN_DESCRIPTION"] = "사납고 자존심 강한 젊은 마왕자. 불같은 성격과 건방진 태도, 끝없는 야망으로 유명하다. 사마디의 불꽃을 다루는 지휘관이자 창술 무술의 달인. 공주와 우마대왕의 아들.",
["BOSS_REDBOY_TEEN_EXTRA"] = "- 광역 피해 공격\n- 용에게 명령하여 타워를 기절시킴",
["BOSS_REDBOY_TEEN_NAME"] = "Red Boy",
["BOSS_SPIDER_QUEEN_DESCRIPTION"] = "태고의 거미 여왕, 정당한 권리를 되찾기 위해 깊은 잠에서 깨어난 원시적 존재.",
["BOSS_SPIDER_QUEEN_EXTRA"] = "- 포탑을 기절시킴\n- 주변 적의 생명력을 흡수함\n- 생명력 흡수 거미를 소환함\n- 눈에 거미줄을 던짐",
["BOSS_SPIDER_QUEEN_NAME"] = "미갈레",
["BRIEFING_LEVEL_WARNING"] = "신규 캠페인!",
["BUTTON_BUG_CRASH"] = "게임 오류",
["BUTTON_BUG_OTHER"] = "기타",
["BUTTON_BUG_REPORT"] = "오류",
["BUTTON_BUY"] = "구매",
["BUTTON_BUY_UPGRADE"] = "업그레이드 구매",
["BUTTON_CLOSE"] = "닫기",
["BUTTON_CONFIRM"] = "확인",
["BUTTON_CONTINUE"] = "계속하기",
["BUTTON_DISABLE"] = "비활성화",
["BUTTON_DONE"] = "완료",
["BUTTON_ENDLESS_QUIT"] = "나가기",
["BUTTON_ENDLESS_TRYAGAIN"] = "다시 시도",
["BUTTON_GET_GEMS"] = "아이템 구매",
["BUTTON_LEVEL_SELECT_FIGHT"] = "전투 개시!",
["BUTTON_LOST_CONTENT"] = "분실된 콘텐츠",
["BUTTON_MAIN_MENU"] = "메인 메뉴",
["BUTTON_NO"] = "아니오",
["BUTTON_OK"] = "확인",
["BUTTON_QUIT"] = "나가기",
["BUTTON_RESET"] = "초기화",
["BUTTON_RESTART"] = "재시작",
["BUTTON_UNDO"] = "취소",
["BUTTON_YES"] = "예",
["BUY UPGRADES!"] = "업그레이드를 구매하세요!",
["Basic Tower Types"] = "기본 타워 유형",
["CARD_REWARDS_CAMPAIGN"] = "신규 캠페인!",
["CARD_REWARDS_DLC_1"] = "거대한 위협",
["CARD_REWARDS_DLC_2"] = "오공의 여정",
["CARD_REWARDS_HERO"] = "새로운 영웅!",
["CARD_REWARDS_TOWER"] = "새로운 타워!",
["CARD_REWARDS_TOWER_LEVEL"] = "새로운 타워 레벨!",
["CARD_REWARDS_TOWER_LEVEL_PREFIX"] = "레벨",
["CARD_REWARDS_UPDATE_01"] = "꺼지지 않는 분노",
["CARD_REWARDS_UPDATE_02"] = "고대의 허기",
["CARD_REWARDS_UPDATE_03"] = "거미 공포증",
["CARD_REWARDS_UPGRADES"] = "업그레이드 포인트!",
["CArmor0"] = "없음",
["CArmor1"] = "낮음",
["CArmor2"] = "보통",
["CArmor3"] = "높음",
["CArmor4"] = "매우 높음",
["CArmor9"] = "면역",
["CArmorSmall0"] = "없음",
["CArmorSmall1"] = "낮음",
["CArmorSmall2"] = "보통",
["CArmorSmall3"] = "높음",
["CArmorSmall4"] = "최상",
["CArmorSmall9"] = "면역",
["CHALLENGE_RULE_DIFFICULTY_CASUAL"] = "캐주얼",
["CHALLENGE_RULE_DIFFICULTY_IMPOSSIBLE"] = "불가능",
["CHALLENGE_RULE_DIFFICULTY_NORMAL"] = "일반",
["CHALLENGE_RULE_DIFFICULTY_VETERAN"] = "베테랑",
["CHANGE_LANGUAGE_QUESTION"] = "언어 설정을 정말 변경하시겠습니까?",
["CINEMATICS_TAP_TO_CONTINUE"] = "누르면 계속 진행",
["CINEMATICS_TAP_TO_CONTINUE_KR1"] = "눌러서 계속 진행...",
["CINEMATICS_TAP_TO_CONTINUE_KR2"] = "눌러서 계속 진행...",
["CINEMATICS_TAP_TO_CONTINUE_KR3"] = "눌러서 계속 진행...",
["CINEMATICS_TAP_TO_CONTINUE_KR5"] = "눌러서 계속 진행...",
["CLAIM_GIFT"] = "선물을 받다",
["CLOUDSYNC_PLEASE_WAIT"] = "클라우드에 저장된 게임 업데이트 중 ...",
["CLOUD_DIALOG_NO"] = "아니요",
["CLOUD_DIALOG_OK"] = "확인",
["CLOUD_DIALOG_YES"] = "예",
["CLOUD_DOWNLOAD_QUESTION"] = "iCloud에서 저장 데이터를 다운로드하시겠습니까?",
["CLOUD_DOWNLOAD_TITLE"] = "iCloud에서 다운로드",
["CLOUD_SAVE"] = "클라우드 저장",
["CLOUD_SAVE_DISABLE_EXTRA"] = "참고: 게임을 제거하면 게임 진행 데이터가 손실될 수 있습니다.",
["CLOUD_SAVE_DISABLE_GENERIC_DESCRIPTION"] = "게임 진행 데이터를 클라우드에 저장하는 기능을 비활성화하시겠습니까?",
["CLOUD_SAVE_OFF"] = "클라우드 스토리지 꺼짐",
["CLOUD_SAVE_ON"] = "클라우드 스토리지 활성",
["CLOUD_UPLOAD_QUESTION"] = "iCloud에 저장 데이터를 업로드하시겠습니까?",
["CLOUD_UPLOAD_TITLE"] = "iCloud에 업로드",
["COMIC_10_1_KR5_KR5"] = "날 풀어줘! 난 왕국을 위해 최선을 다하고 있어!",
["COMIC_10_2_KR5_KR5"] = "신성 모독을 멈춰라, 형제여. 이건 엘프의 방식이 아니야.",
["COMIC_10_3_KR5_KR5"] = "고맙다, 내 오랜 제자. 이제부터 우리가 맡지.",
["COMIC_10_4_KR5_KR5"] = "나중에 캠프에서 보자...",
["COMIC_10_5_KR5_KR5"] = "그럼... 베즈난을 믿을 수 있겠어?",
["COMIC_10_6_KR5_KR5"] = "지켜보고 있지만...",
["COMIC_10_7_KR5_KR5"] = "...지금은 착하게 굴고 있어.",
["COMIC_10_8_KR5_KR5"] = "그래. 지금은...",
["COMIC_11_1_KR5_KR5"] = "늪이 깨어난 것 같다...",
["COMIC_11_2_KR5_KR5"] = "...우리를 지켜보는 듯...",
["COMIC_11_3_KR5_KR5"] = "...전진하며 숨어서...",
["COMIC_11_4_KR5_KR5"] = "...우리를 집어삼킬 준비를 하고 있다.",
["COMIC_11_5_KR5_KR5"] = "조심해!",
["COMIC_11_6_KR5_KR5"] = "우리가 공격 받고 있어!",
["COMIC_11_7_KR5_KR5"] = "가라, 작은 위스프! 우리의 안전이 너에게 달려있어!",
["COMIC_12_1_KR5_KR5"] = "단순히 너를 가둔 게 실수였군. 반복하지 않을 것이다.",
["COMIC_12_2_KR5_KR5"] = "안돼!!!",
["COMIC_12_3_KR5_KR5"] = "너를 영원히 추방한다!!",
["COMIC_12_4_KR5_KR5"] = "콜록!",
["COMIC_12_5_KR5_KR5"] = "콜록, 콜록!",
["COMIC_12_6_KR5_KR5"] = "음, 연습이 부족한 것 같군.",
["COMIC_13_1_KR5_KR5"] = "그들은 이것이 미친 짓이라고 말했다.",
["COMIC_13_2_KR5_KR5"] = "그런 무기는 불가능하다고.",
["COMIC_13_3_KR5_KR5"] = "하지만 곧 그들이 얼마나 틀렸는지 알게 될 것이다...",
["COMIC_13_4_KR5_KR5"] = "...그리고 그림비어드의 천재성 앞에 굴복할 것이다!",
["COMIC_14_1_KR5_KR5"] = "그들을 어떻게 하지?",
["COMIC_14_2_KR5_KR5"] = "내게 맡겨!",
["COMIC_14_3_KR5_KR5"] = "딱 좋은 장소를 알고 있어.",
["COMIC_14_4_KR5_KR5"] = "바로 이곳인가?",
["COMIC_14_5_KR5_KR5"] = "그림비어드를 감옥에서 썩게 만든다?!",
["COMIC_14_6_KR5_KR5"] = "정반대야, 내 작은 친구...",
["COMIC_14_7_KR5_KR5"] = "...너의 큰 두뇌를 위한 큰 계획이 내게 있어!",
["COMIC_15_10_KR5_KR5"] = "…하지만 상태가 좋지 않다.",
["COMIC_15_1_KR5_KR5"] = "산속 어딘가에서.",
["COMIC_15_2_KR5_KR5"] = "야, 고블린!",
["COMIC_15_3_KR5_KR5"] = "일해라!",
["COMIC_15_4_KR5_KR5"] = "너는 전령을 전달해야 한다.",
["COMIC_15_5_KR5_KR5"] = "더 많은 정찰병을 보내야 한다. 저 광신도들이 배회하는 한, 우리는 안심할 수 없다.",
["COMIC_15_6_KR5_KR5"] = "도움을 주기 위해 몇몇 위습을 보낼 수도 있다…",
["COMIC_15_7_KR5_KR5"] = "어둠의 군주님! 긴급 소식입니다!",
["COMIC_15_8_KR5_KR5"] = "음…",
["COMIC_15_9_KR5_KR5"] = "정찰병을 찾았는데…",
["COMIC_16_1_KR5_KR5"] = "나는 반드시 복수할 것이다!",
["COMIC_16_2_KR5_KR5"] = "내 누이...뭐어어어?!",
["COMIC_17_10_KR5_KR5"] = "우리가 막지 않으면, 그들은 모든 왕국을 쓸어버릴 거야!",
["COMIC_17_11_KR5_KR5"] = "우리는 그를 도와야 해!",
["COMIC_17_12_KR5_KR5"] = "오, 물론이지.",
["COMIC_17_13_KR5_KR5"] = "그래, 그래…",
["COMIC_17_1_KR5_KR5"] = "아름다운 오후네요, 그렇죠?",
["COMIC_17_2_KR5_KR5"] = "이 평화에 익숙해질 수도 있겠어.",
["COMIC_17_3_KR5_KR5"] = "그러지 않는 게 좋겠어.",
["COMIC_17_4_KR5_KR5"] = "태양이냐?! 그냥 손 한번 흔들어주면 되잖아…",
["COMIC_17_5_KR5_KR5"] = "친구들, 끔찍한 일이 벌어졌어...",
["COMIC_17_6_KR5_KR5"] = "나는 내 거북이 안에서 평화롭게 명상하고 있었는데…",
["COMIC_17_7_KR5_KR5"] = "세 마왕이 갑자기 나타났어!",
["COMIC_17_8_KR5_KR5"] = "말할 것도 없이, 나는 용감히 싸웠지만…",
["COMIC_17_9_KR5_KR5"] = "그들은 부끄러운 방법으로 내 천구를 훔쳐 갔어!",
["COMIC_1_1_KR5"] = "우리가 잃어버린 국왕을 찾아 이 땅에 도착한 지 한 달이 되었습니다.",
["COMIC_1_2B_KR5"] = "암흑 마법사 베즈난에 의해 추방된 뒤로...",
["COMIC_1_4_KR5"] = "우리는 머물 곳을 찾아 캠프를 설치하고 힘을 회복했습니다...",
["COMIC_1_5_KR5"] = "...평화 속에서...",
["COMIC_1_8_KR5"] = "...하지만 이제 끝난 것 같습니다.",
["COMIC_2_1_KR5"] = "만세!",
["COMIC_2_3_KR5"] = "영웅 베즈난?!",
["COMIC_2_4a_KR5"] = "진정해... 나는 제안하러 왔어...",
["COMIC_2_4b_KR5"] = "...거래를.",
["COMIC_2_5_KR5"] = "우리 왕국에 이런 짓을 하고도?",
["COMIC_2_6_KR5"] = "데나스의 눈을 뜨게 해야 했어.",
["COMIC_2_7_KR5"] = "그는 왕국을 침범하는 위험을 보려 하지 않았지.",
["COMIC_2_8_1_KR5"] = "당신들의 왕을 찾으러 가자구...",
["COMIC_2_8_2_KR5"] = "...그리곤 이 위협에 종지부를 찍는 거야.",
["COMIC_2_8b_KR5"] = "...우리 함께.",
["COMIC_3_1_KR5"] = "세상에! 여기 있는 게 뭐지...?",
["COMIC_3_2_KR5"] = "엘리니의 강력한 검이군!",
["COMIC_3_3_KR5"] = "아야!",
["COMIC_3_4a_KR5"] = "물론...",
["COMIC_3_4b_KR5"] = "낭비할 시간이 없어!",
["COMIC_3_5a_KR5"] = "아... 그런데 네 생각보다 그는 가까이 있어.",
["COMIC_3_5b_KR5"] = "우리 왕은 여전히 실종 상태라구.",
["COMIC_3_6_KR5"] = "힘든 싸움이 될 수도 있지만.",
["COMIC_4_10a_KR5"] = "하! 나는 항상 옳다구.",
["COMIC_4_10b_KR5"] = "그래서... 이제 무슨 일이지?",
["COMIC_4_11_KR5"] = "우리는 서로 다른 점이 있겠지...",
["COMIC_4_12_KR5"] = "...하지만 우리 모두에겐 더 큰 위협이 존재해.",
["COMIC_4_1_KR5"] = "엘리니...",
["COMIC_4_2_KR5"] = "...그에게 힘을 주세요!",
["COMIC_4_4_KR5"] = "아아아아아!",
["COMIC_4_7a_KR5"] = "휴가'가 큰 도움이 된 것을 보니 좋군!",
["COMIC_4_7b_KR5"] = "너!!!",
["COMIC_4_8_KR5"] = "네 장난에 대한 대가를 치러야 해!",
["COMIC_4_9_KR5"] = "하지만 네가 옳았어.",
["COMIC_5_1_KR2"] = "승리!",
["COMIC_5_1_KR5_KR5"] = "벌레들은 멈출 줄 모르는군...",
["COMIC_5_2_KR2"] = "승리!",
["COMIC_5_2_KR5_KR5"] = "새로운 세계가!",
["COMIC_5_6_KR5_KR5"] = "깨어났다!",
["COMIC_5_7a_KR5_KR5"] = "올 것이 왔군...",
["COMIC_5_7b_KR5_KR5"] = "최종 대결이다.",
["COMIC_6_1a_KR5_KR5"] = "용감히도 나에게 도전하다니.",
["COMIC_6_1b_KR5_KR5"] = "...하지만 여기선 있을 수 없는 일이야!",
["COMIC_6_4_KR5_KR5"] = "이봐!",
["COMIC_6_5_KR5_KR5"] = "우주 슬러그, 너는...",
["COMIC_6_6_KR5_KR5"] = "...내 힘을 얕보는군!!!",
["COMIC_6_8_KR5_KR5"] = "준비해. 오래는 못 버텨!",
["COMIC_7_1_KR5_KR5"] = "안돼! 이건... 있을 수 없어!!!",
["COMIC_7_3_KR5_KR5"] = "그래서... 이제 뭐?",
["COMIC_7_4a_KR5_KR5"] = "음, 내 임무는 완수됐어...",
["COMIC_7_4b_KR5_KR5"] = "...그리고 나는 그들에게 왕이 필요하다고 생각해.",
["COMIC_7_5_2_KR2"] = "아니.",
["COMIC_7_6_KR5_KR5"] = "다음에 보자구, 친애하는 적이여.",
["COMIC_7_7_KR5_KR5"] = "나중에, 늘빛숲에서...",
["COMIC_8_1_KR5_KR5"] = "아, 드디어!",
["COMIC_8_2_KR5_KR5"] = "이 힘이, 다시 한번... ",
["COMIC_8_4_KR5_KR5"] = "... 내 것! ",
["COMIC_8_5_KR5_KR5"] = "무하하하하!",
["COMIC_9_1_KR5_KR5"] = "얼마 전까지만 해도 우리 엘프는 마법과 기품으로 존경받았지...",
["COMIC_9_2_KR5_KR5"] = "...신성한 유물이 타락하고 과거의 그림자가 되기 전까진 말이야.",
["COMIC_9_3_KR5_KR5"] = "하지만 이 군대를 통해 우리의 영광을 되찾을 거야",
["COMIC_9_4_KR5_KR5"] = "...그리고 엘프가 지배하는 새로운 세상을 이끌 것이다!!!",
["COMIC_BALLOON_0002_KR1"] = "승리!",
["COMIC_BALLOON_02_KR1"] = "승리!",
["COMIC_balloon_0002_KR1"] = "승리!",
["COMMAND YOUR TROOPS!"] = "부대를 지휘하세요!",
["CONFIRM_EXIT"] = "종료하시겠습니까?",
["CONFIRM_RESTART"] = "다시 시작하겠습니까?",
["CONTROLLER_STAGE_16_OVERSEER_DESCRIPTION"] = "다른 세계를 침략하고 정복하여 그들의 에너지를 흡수하는 초차원적 괴물. 모든 비용을 들여서라도 막아야 합니다.",
["CONTROLLER_STAGE_16_OVERSEER_EXTRA"] = "- 플레이어의 타워를 교체함\n- 조막눈을 생성함\n- 전략 지점을 파괴함",
["CONTROLLER_STAGE_16_OVERSEER_NAME"] = "오버시어",
["CREDITS_COPYRIGHT"] = "© 2014 Ironhide Game Studio. All rights reserved.",
["CREDITS_POWERED_BY"] = "Powered by",
["CREDITS_SUBTITLE_01"] = "(알파벳순)",
["CREDITS_SUBTITLE_07"] = "(알파벳순)",
["CREDITS_SUBTITLE_09"] = "(알파벳순)",
["CREDITS_SUBTITLE_16"] = "(알파벳순)",
["CREDITS_TEXT_18"] = "가족과 친구, 커뮤니티의 여러분,",
["CREDITS_TEXT_18_2"] = "오랫동안 응원해 주셔서 감사합니다.",
["CREDITS_TITLE_01"] = "크리에이티브 디렉터 & 총괄 프로듀서",
["CREDITS_TITLE_01_CREATIVE_DIRECTORS"] = "크리에이티브 디렉터",
["CREDITS_TITLE_01_EXECUTIVE_PRODUCERS"] = "총괄 프로듀서",
["CREDITS_TITLE_02"] = "리드 게임 디자이너",
["CREDITS_TITLE_02_LEAD_GAME_DESIGNERS"] = "리드 게임 디자이너",
["CREDITS_TITLE_03"] = "게임 디자이너",
["CREDITS_TITLE_03_GAME_DESIGNER"] = "게임 디자이너",
["CREDITS_TITLE_04"] = "스토리 작가",
["CREDITS_TITLE_04_STORY_WRITERS"] = "스토리 작가",
["CREDITS_TITLE_05"] = "텍스트 작가",
["CREDITS_TITLE_06"] = "리드 프로그래머",
["CREDITS_TITLE_06_LEAD_PROGRAMMERS"] = "리드 프로그래머",
["CREDITS_TITLE_07"] = "프로그래머",
["CREDITS_TITLE_08"] = "리드 아티스트",
["CREDITS_TITLE_09"] = "아티스트",
["CREDITS_TITLE_10"] = "코믹 아티스트",
["CREDITS_TITLE_11"] = "코믹 작가",
["CREDITS_TITLE_12"] = "테크니컬 아티스트",
["CREDITS_TITLE_13"] = "사운드 FX",
["CREDITS_TITLE_14"] = "작곡",
["CREDITS_TITLE_15"] = "성우",
["CREDITS_TITLE_16"] = "Q&A & 테스트",
["CREDITS_TITLE_17"] = "베타테스트",
["CREDITS_TITLE_18"] = "고마운 분들",
["CREDITS_TITLE_19_PMO"] = "프로젝트 관리자",
["CREDITS_TITLE_20_PRODUCER"] = "프로듀서",
["CREDITS_TITLE_21_MARKETING"] = "마케팅",
["CREDITS_TITLE_22_SPECIAL_COLLAB"] = "특별 협력자",
["CREDITS_TITLE_ANCIENT_HUNGER_UPDATE"] = "고대의 허기 / 거미 공포증 / 오공의 여정",
["CREDITS_TITLE_GAME_ENGINE_PROGRAMMER"] = "게임 엔진 프로그래머",
["CREDITS_TITLE_LOCALIZATION"] = "로컬라이제이션",
["CREDITS_TITLE_LOGO"] = "게임 제작:",
["CRange0"] = "단거리",
["CRange1"] = "중거리",
["CRange2"] = "중장거리",
["CRange3"] = "장거리",
["CRange4"] = "초장거리",
["CReload0"] = "매우 느림",
["CReload1"] = "느림",
["CReload2"] = "보통",
["CReload3"] = "빠름",
["CReload4"] = "매우 빠름",
["CSpeed0"] = "느림",
["CSpeed1"] = "보통",
["CSpeed2"] = "빠름",
["C_DIFFICULTY_EASY"] = "캐주얼 완료",
["C_DIFFICULTY_HARD"] = "베테랑 완료",
["C_DIFFICULTY_IMPOSSIBLE"] = "불가능 난이도 완료",
["C_DIFFICULTY_NORMAL"] = "일반 완료",
["C_REWARD"] = "보상:",
["Campaign"] = "캠페인",
["Cancel"] = "취소",
["Casual"] = "캐주얼",
["Challenge Rules"] = "도전 규칙",
["Clear_progress"] = "진행 상황 초기화",
["Community Manager"] = "커뮤니티 관리자",
["Credits"] = "제작진",
["DAYS_ABBREVIATION"] = "일",
["DELETE SLOT?"] = "슬롯을 삭제할까요?",
["DIFFICULTY_SELECTION_EASY_DESCRIPTION"] = "전략 게임 초보자에게 적합합니다!",
["DIFFICULTY_SELECTION_HARD_DESCRIPTION"] = "매우 어렵습니다! 위험은 알아서 감수하세요!",
["DIFFICULTY_SELECTION_IMPOSSIBLE_DESCRIPTION"] = "가장 강한 자만이 도전할 수 있습니다!",
["DIFFICULTY_SELECTION_IMPOSSIBLE_LOCKED_DESCRIPTION"] = "캠페인을 완료하면 이 모드가 해제됩니다.",
["DIFFICULTY_SELECTION_NORMAL_DESCRIPTION"] = "멋진 도전입니다!",
["DIFFICULTY_SELECTION_NOTE"] = "스테이지 선택 시 언제든지 난이도를 변경할 수 있습니다.",
["DIFFICULTY_SELECTION_TITLE"] = "난이도 레벨을 선택하세요!",
["DISCOUNT"] = "할인",
["DLC_OWNED"] = "구매했다",
["Difficulty Level"] = "난이도 레벨",
["ELITE STAGE!"] = "엘리트 스테이지!",
["ENEMY_ACOLYTE_DESCRIPTION"] = "작고 온화한 시종들은 전투에서 그들의 머릿수를 잘 이용합니다.",
["ENEMY_ACOLYTE_EXTRA"] = "- 사망 시 촉수를 생성합니다",
["ENEMY_ACOLYTE_NAME"] = "이교도 시종",
["ENEMY_ACOLYTE_SPECIAL"] = "사망 시 촉수 생성",
["ENEMY_ACOLYTE_TENTACLE_DESCRIPTION"] = "마지막 수단으로, 시종들은 오버시어에게 자신의 남은 생명을 바쳐 치명적인 촉수를 생성합니다.",
["ENEMY_ACOLYTE_TENTACLE_EXTRA"] = "- 죽은 시종들로부터 생성",
["ENEMY_ACOLYTE_TENTACLE_NAME"] = "시종의 촉수",
["ENEMY_AMALGAM_DESCRIPTION"] = "공허 저편의 살과 흙으로 만들어진 괴물입니다. 거대괴수들은 전장을 누비는 속도가 느리지만 두려운 존재입니다.",
["ENEMY_AMALGAM_EXTRA"] = "- 미니 보스\n- 죽을 때 폭발",
["ENEMY_AMALGAM_NAME"] = "육체 거대거수",
["ENEMY_ANIMATED_ARMOR_DESCRIPTION"] = "과거의 전투에서 박살난 퇴물들이 이제 유령에 빙의되어 전투에 뛰어들게 됩니다.",
["ENEMY_ANIMATED_ARMOR_EXTRA"] = "- 쓰러지면 혼령에 의해 부활 가능",
["ENEMY_ANIMATED_ARMOR_NAME"] = "살아 움직이는 갑옷",
["ENEMY_ARMORED_NIGHTMARE_DESCRIPTION"] = "이교도의 마법 덕분에 갑옷을 입은 악몽들은 황급히 전투에 뛰어듭니다.",
["ENEMY_ARMORED_NIGHTMARE_EXTRA"] = "- 높은 방어력\n- 패배하면 악몽으로 변함",
["ENEMY_ARMORED_NIGHTMARE_NAME"] = "구속된 악몽",
["ENEMY_ARMORED_NIGHTMARE_SPECIAL"] = "패배하면 악몽으로 변합니다.",
["ENEMY_ASH_SPIRIT_DESCRIPTION"] = "강력한 정령들이 용암과 재, 슬픔에서 태어난 무시무시한 괴물로 변했다.",
["ENEMY_ASH_SPIRIT_EXTRA"] = "- 높은 체력\n- 높은 방어력\n- 불타는 지면에서 체력 회복",
["ENEMY_ASH_SPIRIT_NAME"] = "Ash Spirit",
["ENEMY_BALLOONING_SPIDER_DESCRIPTION"] = "빠르고 은밀한 거미로, 위기를 피하는 데 능숙하다.",
["ENEMY_BALLOONING_SPIDER_EXTRA"] = "- 궁지에 몰리면 날기 시작함\n- 중형 갑옷",
["ENEMY_BALLOONING_SPIDER_FLYER_DESCRIPTION"] = "빠르고 은밀한 거미로, 위기를 피하는 데 능숙하다.",
["ENEMY_BALLOONING_SPIDER_FLYER_EXTRA"] = "- 궁지에 몰리면 날기 시작함\n- 중형 갑옷",
["ENEMY_BALLOONING_SPIDER_FLYER_NAME"] = "부양 거미",
["ENEMY_BALLOONING_SPIDER_NAME"] = "부양 거미",
["ENEMY_BANE_WOLF_DESCRIPTION"] = "그들이 다가오는 것을 너무 늦게 본 사람들을 잡아먹는 뒤틀린 늑대들입니다.",
["ENEMY_BANE_WOLF_EXTRA"] = "- 피해를 받을 때마다 더 빠르게 이동",
["ENEMY_BANE_WOLF_NAME"] = "베인 울프",
["ENEMY_BEAR_VANGUARD_DESCRIPTION"] = "크고, 거칠고, 못된 그들은 적들을 수십 명씩 찢어발깁니다.",
["ENEMY_BEAR_VANGUARD_EXTRA"] = "- 높은 방어력\n- 근처에서 곰이 죽으면 분노함.",
["ENEMY_BEAR_VANGUARD_NAME"] = "곰 선봉군",
["ENEMY_BEAR_VANGUARD_SPECIAL"] = "근처에서 다른 곰이 죽으면 광란의 상태에 빠집니다.",
["ENEMY_BEAR_WOODCUTTER_DESCRIPTION"] = "근무 중에 잠을 자는 경향이 있지만, 일단 깨어나면 상황이 심각해집니다.",
["ENEMY_BEAR_WOODCUTTER_EXTRA"] = "- 높은 방어력\n- 근처에서 곰이 죽으면 분노함",
["ENEMY_BEAR_WOODCUTTER_NAME"] = "곰 나무꾼",
["ENEMY_BIG_TERRACOTA_DESCRIPTION"] = "살의에 사로잡힌 여러 영혼이 융합되어 태어난 인형 형태의 진흙 덩어리.",
["ENEMY_BIG_TERRACOTA_EXTRA"] = "- 근접",
["ENEMY_BIG_TERRACOTA_NAME"] = "괴물 환영 미끼",
["ENEMY_BLAZE_RAIDER_DESCRIPTION"] = "자랑스럽고 건장한 대장들로, 불의 길을 걷는 입문자이며, 뱀창을 휘둘러 적을 능숙하게 따돌린다.",
["ENEMY_BLAZE_RAIDER_EXTRA"] = "- 방어력 낮음\n- 불타는 지면에서 특수 공격",
["ENEMY_BLAZE_RAIDER_NAME"] = "화염 약탈자",
["ENEMY_BLINKER_DESCRIPTION"] = "위협적인 눈빛과 박쥐 같은 날개를 지닌 깜빡이는 방심하고 있는 적을 사냥합니다.",
["ENEMY_BLINKER_EXTRA"] = "- 적을 기절시킴",
["ENEMY_BLINKER_NAME"] = "공허의 깜빡이",
["ENEMY_BLINKER_SPECIAL"] = "적을 기절시킵니다.",
["ENEMY_BOSS_BULL_KING_DESCRIPTION"] = "무자비하고 권위적인 지도자이자 전쟁 베테랑이자 실용적인 전략가. 한때 손오공의 맹우였던 그는 이제 경쟁자의 천구를 훔치려 한다. 엄청난 힘, 앙심 어린 성격, 무술 실력으로 유명하다.",
["ENEMY_BOSS_BULL_KING_EXTRA"] = "-높은 방어력\n- 높은 마법 저항\n- 유닛과 타워에 광범위 기절 효과",
["ENEMY_BOSS_BULL_KING_NAME"] = "Bull Demon King",
["ENEMY_BOSS_CORRUPTED_DENAS_NAME"] = "타락한 데나스",
["ENEMY_BOSS_CROCS_2_NAME"] = "아보미노르 베노무스",
["ENEMY_BOSS_CROCS_3_NAME"] = "아보미노르 이그니스",
["ENEMY_BOSS_CROCS_NAME"] = "아보미노르",
["ENEMY_BOSS_CULT_LEADER_NAME"] = "예언자 미드리아스",
["ENEMY_BOSS_DEFORMED_GRYMBEARD_NAME"] = "기형의 그림비어드",
["ENEMY_BOSS_GRYMBEARD_NAME"] = "그림비어드",
["ENEMY_BOSS_MACHINIST_NAME"] = "그림비어드",
["ENEMY_BOSS_NAVIRA_NAME"] = "나비라",
["ENEMY_BOSS_OVERSEER_NAME"] = "오버시어",
["ENEMY_BOSS_PIG_NAME"] = "고어그라인드",
["ENEMY_BOSS_PRINCESS_IRON_FAN_CLONE_NAME"] = "철선공주 클론",
["ENEMY_BOSS_PRINCESS_IRON_FAN_NAME"] = "Princess Iron Fan",
["ENEMY_BOSS_REDBOY_TEEN_NAME"] = "붉은 소년",
["ENEMY_BOSS_SPIDER_QUEEN_NAME"] = "미갈레",
["ENEMY_BRUTE_WELDER_DESCRIPTION"] = "이 일꾼들은 이유 없이 적들에게 그들의 토치를 사용할 것 입니다.",
["ENEMY_BRUTE_WELDER_EXTRA"] = "- 죽으면 타워를 차단",
["ENEMY_BRUTE_WELDER_NAME"] = "난폭한 용접공",
["ENEMY_BURNING_TREANT_DESCRIPTION"] = "불타는 숲 한가운데서 태어난 악의적인 의도를 지닌 나무 생물.",
["ENEMY_BURNING_TREANT_EXTRA"] = "- 광역 피해\n- 공격 시 불타는 지면을 남김",
["ENEMY_BURNING_TREANT_NAME"] = "Burning Treant",
["ENEMY_CITIZEN_1_DESCRIPTION"] = "공주를 섬기며 암시장에 몰래 숨어드는 사악한 어부들.",
["ENEMY_CITIZEN_1_EXTRA"] = "- 약함",
["ENEMY_CITIZEN_1_NAME"] = "늙은 생선 장수",
["ENEMY_CITIZEN_2_DESCRIPTION"] = "공주를 섬기며 암시장을 누비는 불길한 어부들.",
["ENEMY_CITIZEN_2_EXTRA"] = "- 약함",
["ENEMY_CITIZEN_2_NAME"] = "블랙워터 어부",
["ENEMY_CITIZEN_3_DESCRIPTION"] = "공주를 섬기며 암시장에 몰래 숨어드는 사악한 어부들.",
["ENEMY_CITIZEN_3_EXTRA"] = "- 약함",
["ENEMY_CITIZEN_3_NAME"] = "잉크 밀수업자",
["ENEMY_CITIZEN_4_DESCRIPTION"] = "공주를 섬기며 암시장에 몰래 숨어드는 사악한 어부들.",
["ENEMY_CITIZEN_4_EXTRA"] = "- 약함",
["ENEMY_CITIZEN_4_NAME"] = "파도 밀렵꾼",
["ENEMY_COMMON_CLONE_DESCRIPTION"] = "눈에 띄지 않으며, 특별하지도 않고, 원본과 크게 다르지 않습니다.",
["ENEMY_COMMON_CLONE_EXTRA"] = "- 생각없이 앞으로 밀고 나아감",
["ENEMY_COMMON_CLONE_NAME"] = "클론",
["ENEMY_CORRUPTED_ELF_DESCRIPTION"] = "원거리에서 적을 사냥하는 부활한 엘프입니다. 죽어서도 그들은 여전히 뛰어납니다.",
["ENEMY_CORRUPTED_ELF_EXTRA"] = "- 사망 시 혼령 생성",
["ENEMY_CORRUPTED_ELF_NAME"] = "레버넌트 레인저",
["ENEMY_CORRUPTED_STALKER_DESCRIPTION"] = "사제들에 의해 길들여진 구름 추적자는 이제 교단의 탈것 역할을 합니다.",
["ENEMY_CORRUPTED_STALKER_EXTRA"] = "- 비행",
["ENEMY_CORRUPTED_STALKER_NAME"] = "길들여진 추적자",
["ENEMY_CORRUPTED_STALKER_SPECIAL"] = "비행.",
["ENEMY_CROCS_BASIC_DESCRIPTION"] = "자랑스러운 크록 전사입니다. 아직 어리지만 살인 기계로 변하기까지는 얼마 남지 않았습니다.",
["ENEMY_CROCS_BASIC_EGG_DESCRIPTION"] = "막 태어났지만 걷는 것조차 멈추지 않는, 이 놀라운 작은 아이들 덕분에 \"정말 빨리 자란다\"는 표현이 생겼습니다.",
["ENEMY_CROCS_BASIC_EGG_EXTRA"] = "- 저지 불가\n- 낮은 방어력\n- 몇 초 후 게이터로 부화",
["ENEMY_CROCS_BASIC_EGG_NAME"] = "크로킨더",
["ENEMY_CROCS_BASIC_EXTRA"] = "- 근거리",
["ENEMY_CROCS_BASIC_NAME"] = "게이터",
["ENEMY_CROCS_EGG_SPAWNER_DESCRIPTION"] = "이 크록은 문제로 가득 찬 둥지를 꾸리고 있습니다! 몇 걸음 걸을 때마다 알을 떨어뜨리는데, 이 알들은 크로킨더로 부화합니다. 이동식 탁아소 같지만 훨씬 많이 뭅니다.",
["ENEMY_CROCS_EGG_SPAWNER_EXTRA"] = "- 경로에 크로킨더를 생성함",
["ENEMY_CROCS_EGG_SPAWNER_NAME"] = "둥지 게이터",
["ENEMY_CROCS_FLIER_DESCRIPTION"] = "자연 진화를 경멸하며 공중 우위를 점하기 위해 스스로 날개를 만든 교활한 크록들입니다.",
["ENEMY_CROCS_FLIER_EXTRA"] = "- 비행",
["ENEMY_CROCS_FLIER_NAME"] = "날개달린 크록",
["ENEMY_CROCS_HYDRA_DESCRIPTION"] = "두 개의 머리가 하나보다 낫다는 것을 히드라는 그것을 증명합니다. 이와 같은 세 머리를 가진 짐승에 관한 오래된 신화가 있지만, 아마도 거짓일 겁니다.",
["ENEMY_CROCS_HYDRA_EXTRA"] = "- 죽을 때 세 번째 머리를 생성함\n-  땅에 독을 뱉음",
["ENEMY_CROCS_HYDRA_NAME"] = "히드라",
["ENEMY_CROCS_QUICKFEET_GATOR_NAME"] = "퀵피트",
["ENEMY_CROCS_RANGED_DESCRIPTION"] = "빠르고 재빠른 사냥 도마뱀으로, 원거리 슬링샷으로 적을 상대합니다.",
["ENEMY_CROCS_RANGED_EXTRA"] = "- 빠름\n- 원거리",
["ENEMY_CROCS_RANGED_NAME"] = "리자드샷",
["ENEMY_CROCS_SHAMAN_DESCRIPTION"] = "크록들에게 매우 중요한 마법의 존재입니다. 결국, 냉혈한 종족에게 하늘의 변덕을 예견하는 능력은 삶과 죽음의 문제입니다. ",
["ENEMY_CROCS_SHAMAN_EXTRA"] = "- 원거리 마법 피해\n- 높은 마법 저항\n- 다른 크록 치료\n- 타워 기절\n- 기절한 타워에 스턴 강화",
["ENEMY_CROCS_SHAMAN_NAME"] = "크록 현자",
["ENEMY_CROCS_TANK_DESCRIPTION"] = "크록 군대의 중추입니다. \"좋은 방어가 최선의 공격이다\"라는 정신으로, 그들은 몇몇 포탄을 훔쳐 그것이 최선의 방법이라고 생각하면서 사용하기 시작했습니다.",
["ENEMY_CROCS_TANK_EXTRA"] = "- 높은 생명력\n- 높은 방어력\n- 저지 시 회전함",
["ENEMY_CROCS_TANK_NAME"] = "탱크자드",
["ENEMY_CRYSTAL_GOLEM_DESCRIPTION"] = "크리스탈에서 나오는 기이한 마력이 깃든 이 돌 조각상은 거의 막을 수 없습니다.",
["ENEMY_CRYSTAL_GOLEM_EXTRA"] = "- 미니 보스\n- 매우 높은 방어력",
["ENEMY_CRYSTAL_GOLEM_NAME"] = "크리스탈 골렘",
["ENEMY_CULTBROOD_DESCRIPTION"] = "반은 거미, 반은 광신도 괴물로, 두려움이나 자비 없이 전장으로 돌진한다.",
["ENEMY_CULTBROOD_EXTRA"] = "- 빠름\n- 독 공격\n- 중독된 적이 죽으면 또 다른 컬트브루드가 태어남",
["ENEMY_CULTBROOD_NAME"] = "컬트브루드",
["ENEMY_CUTTHROAT_RAT_DESCRIPTION"] = "천성적으로 교활하고 사악한 쥐는 솜씨좋은 암살자이자 침입자입니다.",
["ENEMY_CUTTHROAT_RAT_EXTRA"] = "- 빠른 속도\n- 적을 공격한 후에 투명화.",
["ENEMY_CUTTHROAT_RAT_NAME"] = "살인쥐",
["ENEMY_CUTTHROAT_RAT_SPECIAL"] = "적을 공격한 후에 보이지 않게 됩니다.",
["ENEMY_DARKSTEEL_ANVIL_DESCRIPTION"] = "전쟁 북에 대한 드워프의 대답. 더 무거워 보일수록 더 큰 소리를 냅니다.",
["ENEMY_DARKSTEEL_ANVIL_EXTRA"] = "- 적에게 방어력과 속도 버프를 제공",
["ENEMY_DARKSTEEL_ANVIL_NAME"] = "다크스틸 모루",
["ENEMY_DARKSTEEL_FIST_DESCRIPTION"] = "금속을 구부릴 수 있도록 기계적으로 강화되었으며, 대신 다른 사람을 때립니다.",
["ENEMY_DARKSTEEL_FIST_EXTRA"] = "- 특수 공격으로 플레이어 유닛을 기절시킴",
["ENEMY_DARKSTEEL_FIST_NAME"] = "다크스틸 주먹",
["ENEMY_DARKSTEEL_GUARDIAN_DESCRIPTION"] = "드워프 전사들이 조종하고 불타는 엔진으로 구동되는 튼튼한 전투 슈트. 끝내주게 차려입었습니다.",
["ENEMY_DARKSTEEL_GUARDIAN_EXTRA"] = "- 미니보스\n- 생명력이 낮을 때 광란 상태에 빠짐",
["ENEMY_DARKSTEEL_GUARDIAN_NAME"] = "다크스틸 가디언",
["ENEMY_DARKSTEEL_HAMMERER_DESCRIPTION"] = "자신이 선호하는 무기만큼이나 둔탁한 전사들입니다.",
["ENEMY_DARKSTEEL_HAMMERER_EXTRA"] = " ",
["ENEMY_DARKSTEEL_HAMMERER_NAME"] = "다크스틸 해머러",
["ENEMY_DARKSTEEL_HULK_DESCRIPTION"] = "짜증이 많고 용해된 강철이 혈관을 흐르는, 드워프가 도달할 수 있는 가장 무거운 형태입니다.",
["ENEMY_DARKSTEEL_HULK_EXTRA"] = "- 미니보스\n- 생명력이 낮을 때 경로를 따라 돌진하여 피해를 입힘",
["ENEMY_DARKSTEEL_HULK_NAME"] = "다크스틸 헐크",
["ENEMY_DARKSTEEL_SHIELDER_DESCRIPTION"] = "거대한 방패로 보호하고, 전진하면서 적을 밀쳐냅니다.",
["ENEMY_DARKSTEEL_SHIELDER_EXTRA"] = "- 죽으면 해머러로 변함",
["ENEMY_DARKSTEEL_SHIELDER_NAME"] = "다크스틸 실더",
["ENEMY_DEATHWOOD_DESCRIPTION"] = "어둠의 영들에 의해 타락한 기괴한 나무들은 이제 숲을 돌아다니며 혼란을 일으키고 있습니다.",
["ENEMY_DEATHWOOD_EXTRA"] = "- 미니 보스\n- 저주받은 도토리를 던져 일정 지역에 피해를 입힘",
["ENEMY_DEATHWOOD_NAME"] = "죽음의 나무",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_DESCRIPTION"] = "그림비어드의 억제할 수 없는 오만함의 결과물. 그의 지적 능력은 그의 끔찍함과 동등합니다.",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_EXTRA"] = "- 비행\n- 높은 마법 저항을 가진 방패",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_NAME"] = "기형의 클론",
["ENEMY_DEMON_MINOTAUR_DESCRIPTION"] = "지옥에서 온 집요한 반인반수 크리처.",
["ENEMY_DEMON_MINOTAUR_EXTRA"] = "- 높은 피해\n- 돌진 공격\n- 즉사 불가",
["ENEMY_DEMON_MINOTAUR_NAME"] = "Demon Minotaur",
["ENEMY_DOOM_BRINGER_DESCRIPTION"] = "무슨 수를 써서라도 파멸을 가져오는 두려운 전사들。",
["ENEMY_DOOM_BRINGER_EXTRA"] = "- 타워를 기절시킴",
["ENEMY_DOOM_BRINGER_NAME"] = "Doombringer",
["ENEMY_DRAINBROOD_DESCRIPTION"] = "치명적인 독니를 가진 고대 거미. 다른 거미들의 결정화를 초래한 주범이라는 추측이 있다.",
["ENEMY_DRAINBROOD_EXTRA"] = "- 적을 결정화시키며 생명을 흡수함",
["ENEMY_DRAINBROOD_NAME"] = "생명력 흡수 거미",
["ENEMY_DREADEYE_VIPER_DESCRIPTION"] = "자신들의 화살에 독을 묻혀 쏘는, 치명적인 원거리 적입니다.",
["ENEMY_DREADEYE_VIPER_EXTRA"] = "- 낮은 마법 저항력\n- 독성 공격",
["ENEMY_DREADEYE_VIPER_NAME"] = "독뱀 궁수",
["ENEMY_DREADEYE_VIPER_SPECIAL"] = "화살은 대상에게 독을 적용합니다.",
["ENEMY_DUST_CRYPTID_DESCRIPTION"] = "이전에는 놀라운 광경이었지만 이제는 너무 멀리 돌아다니는 사람들에게 잊혀지지 않는 모습이 되었습니다.",
["ENEMY_DUST_CRYPTID_EXTRA"] = "- 비행\n- 꽃가루 구름을 남겨 적을 피해에 무적 상태로 만듦",
["ENEMY_DUST_CRYPTID_NAME"] = "더스트 크립티드",
["ENEMY_EVOLVING_SCOURGE_DESCRIPTION"] = "처음에는 귀엽게 보일 수 있지만, 스커지가 쓰러진 유닛을 먹기 시작하면 상황이 급격히 나빠질 것입니다.",
["ENEMY_EVOLVING_SCOURGE_EXTRA"] = "- 쓰러진 유닛을 먹어 강한 형태로 진화함.\n - 성난 눈의 영향을 받으면 즉시 최종 형태로 진화함.",
["ENEMY_EVOLVING_SCOURGE_NAME"] = "진화하는 스커지",
["ENEMY_FAN_GUARD_DESCRIPTION"] = "강하고 다재다능한 여성 전사들로, 고통을 주는 것과 마법 부채로 스스로를 보호하는 데 능숙하다.",
["ENEMY_FAN_GUARD_EXTRA"] = "- 차단되지 않은 동안 중간 방어력과 마법 저항을 가진다.",
["ENEMY_FAN_GUARD_NAME"] = "Fan Guard",
["ENEMY_FIRE_FOX_DESCRIPTION"] = "불에서 태어난, 귀엽고 잡히지 않는 여우. 너무 빠르고 변덕스러워 길들일 수 없어.",
["ENEMY_FIRE_FOX_EXTRA"] = "- 낮은 마법 저항력\n- 불타는 땅에서 더 빠름\n- 죽을 때 불타는 땅을 남김",
["ENEMY_FIRE_FOX_NAME"] = "화염 여우",
["ENEMY_FIRE_PHOENIX_DESCRIPTION"] = "불 그 자체를 먹고 사는 신화 속 비행 생물들. 그들은 타오르는 불꽃 속에서 살고 죽는다.",
["ENEMY_FIRE_PHOENIX_EXTRA"] = "- 비행\n- 죽을 때 불타는 땅을 남김",
["ENEMY_FIRE_PHOENIX_NAME"] = "화염의 피닉스",
["ENEMY_FLAME_GUARD_DESCRIPTION"] = "주인의 인정을 받기 위해 애쓰는 하급 제자들은 작은 칼을 능숙하게 다룬다.",
["ENEMY_FLAME_GUARD_EXTRA"] = "- 불타는 지면에서 특수 공격",
["ENEMY_FLAME_GUARD_NAME"] = "화염의 수호자",
["ENEMY_GALE_WARRIOR_DESCRIPTION"] = "우아하고 세련된 이 전사들은 공주가 선택한 이들이며 그녀를 위해 죽을 각오가 되어 있다.",
["ENEMY_GALE_WARRIOR_EXTRA"] = "- 중간 방어력\n- 3번 공격마다 출혈 유발",
["ENEMY_GALE_WARRIOR_NAME"] = "Gale Warrior",
["ENEMY_GLAREBROOD_CRYSTAL_NAME"] = "글레어브루드 수정",
["ENEMY_GLARELING_DESCRIPTION"] = "제지하지 않으면 이 온순한 생물은 가장 강한 군대도 압도할 수 있습니다.",
["ENEMY_GLARELING_EXTRA"] = "- 높은 속도",
["ENEMY_GLARELING_NAME"] = "조막눈",
["ENEMY_GLARENWARDEN_DESCRIPTION"] = "이 끔찍한 거미들은 글레어브루드의 융합으로 탄생하여 그 어느 때보다 강하고 단단해졌다.",
["ENEMY_GLARENWARDEN_EXTRA"] = "- 높은 방어력\n- 공격 시 생명력 흡수",
["ENEMY_GLARENWARDEN_NAME"] = "빛의 감시자",
["ENEMY_GOLDEN_EYED_DESCRIPTION"] = "포효로 적들의 마음에 공포를 심어주는 거대한 야수.",
["ENEMY_GOLDEN_EYED_EXTRA"] = "- 미니보스\n- 차단 시 범위 내 유닛 기절\n- 타워 기절",
["ENEMY_GOLDEN_EYED_NAME"] = "Golden-Eyed Beast",
["ENEMY_HARDENED_HORROR_DESCRIPTION"] = "이 호러 종족은 손에 날카로운 칼날이 있어 적진을 뚫고 길을 만듭니다.",
["ENEMY_HARDENED_HORROR_EXTRA"] = "- 성난 눈의 영향을 받으면 빠른 속도로 구르며 저지할 수 없음.",
["ENEMY_HARDENED_HORROR_NAME"] = "칼날발톱 호러",
["ENEMY_HELLFIRE_WARLOCK_DESCRIPTION"] = "지옥 깊은 곳에서 괴물과 불꽃을 소환하는 데 능한 극도로 위험한 흑마법사들.",
["ENEMY_HELLFIRE_WARLOCK_EXTRA"] = "- 불꽃을 던짐\n- 구미호 소환",
["ENEMY_HELLFIRE_WARLOCK_NAME"] = "Hellfire Warlock",
["ENEMY_HOG_INVADER_DESCRIPTION"] = "야수 군대의 다수를 차지하는 더럽고 무질서한 말썽꾼들입니다.",
["ENEMY_HOG_INVADER_EXTRA"] = "- 낮은 생명력",
["ENEMY_HOG_INVADER_NAME"] = "돼지 침략군",
["ENEMY_HYENA5_DESCRIPTION"] = "쓰러진 적들을 잡아먹는 것을 좋아하는 무시무시한 전사들입니다.",
["ENEMY_HYENA5_EXTRA"] = "- 보통 방어력\n- 쓰러진 플레이어 유닛을 먹고 치유함",
["ENEMY_HYENA5_NAME"] = "썩은송곳니 하이에나",
["ENEMY_HYENA5_SPECIAL"] = "적의 시체를 먹고 치유합니다.",
["ENEMY_KILLERTILE_DESCRIPTION"] = "강력한 파괴자들인 그들은 입니다. 수년간의 전투 경험(또는 닭)으로 강력하고 치명적인 무는 힘을 얻었습니다. ",
["ENEMY_KILLERTILE_EXTRA"] = "- 높은 생명력\n- 높은 피해",
["ENEMY_KILLERTILE_NAME"] = "킬러타일 ",
["ENEMY_LESSER_EYE_DESCRIPTION"] = "전장 위를 떠다니는 악한 눈들로서 사악한 스포너들의 정찰병 역할을 합니다.",
["ENEMY_LESSER_EYE_EXTRA"] = "- 비행",
["ENEMY_LESSER_EYE_NAME"] = "눈알 졸개",
["ENEMY_LESSER_SISTER_DESCRIPTION"] = "그들의 사악한 마법으로, 뒤틀린 자매들은 악몽이 현실 세계로 들어오기 쉽게 합니다.",
["ENEMY_LESSER_SISTER_EXTRA"] = "- 높은 마법 저항력\n- 악몽 소환",
["ENEMY_LESSER_SISTER_NAME"] = "뒤틀린 자매",
["ENEMY_LESSER_SISTER_NIGHTMARE_DESCRIPTION"] = "이교도 자매들의 성가집에서 엮어낸 기묘한 그림자들입니다.",
["ENEMY_LESSER_SISTER_NIGHTMARE_EXTRA"] = "- 근접 유닛에 의해 저지되지 않는 한 타겟이 되지 않음.",
["ENEMY_LESSER_SISTER_NIGHTMARE_NAME"] = "악몽",
["ENEMY_LESSER_SISTER_SPECIAL"] = "악몽을 소환합니다.",
["ENEMY_MACHINIST_DESCRIPTION"] = "톱니바퀴와 엔진에 집착하는, 이 드워프는 산업 자동화와 전쟁을 위해 살아갑니다.",
["ENEMY_MACHINIST_EXTRA"] = "- 센트리를 생성하는 조립 라인을 운영",
["ENEMY_MACHINIST_NAME"] = "그림비어드",
["ENEMY_MAD_TINKERER_DESCRIPTION"] = "어설픈 수리공들은 고물로 물건을 만드는 것 외에는 아무것도 신경 쓰지 않습니다.",
["ENEMY_MAD_TINKERER_EXTRA"] = "- 다른 유닛이 남긴 고철을 사용하여 드론을 생성합니다",
["ENEMY_MAD_TINKERER_NAME"] = "미친 수리공",
["ENEMY_MINDLESS_HUSK_DESCRIPTION"] = "그들의 외모 때문에 껍데기들은 약해 보이지만, 하나 하나가 전장에 놀라움을 안겨줍니다.",
["ENEMY_MINDLESS_HUSK_EXTRA"] = "- 죽으면 조막눈을 생성함",
["ENEMY_MINDLESS_HUSK_NAME"] = "정신나간 껍데기",
["ENEMY_NINE_TAILED_FOX_DESCRIPTION"] = "아름답고 강력한 신비로운 생물. 타오르는 모닥불처럼 적을 휩쓸 것이다.",
["ENEMY_NINE_TAILED_FOX_EXTRA"] = "- 중간 마법 저항\n- 전방으로 순간이동하여 도착 시 적 기절\n- 범위 피해",
["ENEMY_NINE_TAILED_FOX_NAME"] = "구미호",
["ENEMY_NOXIOUS_HORROR_DESCRIPTION"] = "먹잇감에게 유독한 담즙을 뱉는 양서류처럼 생긴 생명체입니다. 가까이 다가가는 것도 위험합니다.",
["ENEMY_NOXIOUS_HORROR_EXTRA"] = "- 성난 눈의 영향을 받으면 마법 저향력을 얻고 독성 오라를 방출함.",
["ENEMY_NOXIOUS_HORROR_NAME"] = "독성 물뭍괴물",
["ENEMY_PALACE_GUARD_DESCRIPTION"] = "공주님의 소원을 이루는 것만이 유일한 동기인 재능 없는 훈련병들。",
["ENEMY_PALACE_GUARD_EXTRA"] = "- 근접\n- 낮은 방어력",
["ENEMY_PALACE_GUARD_NAME"] = "궁전 경비병",
["ENEMY_PUMPKIN_WITCH_DESCRIPTION"] = "작은 호박이 되어버린 적입니다. 쉽게 밟을 수 있습니다.",
["ENEMY_PUMPKIN_WITCH_EXTRA"] = "- 저지 불가",
["ENEMY_PUMPKIN_WITCH_FLYING_DESCRIPTION"] = "작은 호박이 되어버린 적입니다. 쉽게 밟을 수 있습니다.",
["ENEMY_PUMPKIN_WITCH_FLYING_EXTRA"] = "- 저지 불가",
["ENEMY_PUMPKIN_WITCH_FLYING_NAME"] = "작은 호박",
["ENEMY_PUMPKIN_WITCH_NAME"] = "작은 호박",
["ENEMY_QIONGQI_DESCRIPTION"] = "번개의 힘으로 공격하는 사나운 날아다니는 사자들. 폭풍의 왕들.",
["ENEMY_QIONGQI_EXTRA"] = "- 비행\n- 매우 높은 피해\n- 중간 수준의 마법 저항력",
["ENEMY_QIONGQI_NAME"] = "Qiongqi",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_DESCRIPTION"] = "수년 동안 형제들에게 닭을 배달해면서, 그들은 너무 빨라져 때로는 닭을 가져오는 것조차 잊어버리게 되었습니다.",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_EXTRA"] = "- 빠름\n- 원거리\n- 조심하세요! 닭다리를 게이터에게 전달하여 진화시킬 수 있습니다.",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_NAME"] = "퀵피트",
["ENEMY_QUICKFEET_GATOR_DESCRIPTION"] = "수년 동안 형제들에게 닭을 배달해면서, 그들은 너무 빨라져 때로는 닭을 가져오는 것조차 잊어버리게 되었습니다.",
["ENEMY_QUICKFEET_GATOR_EXTRA"] = "- 빠름\n- 원거리\n- 조심하세요! 닭다리를 게이터에게 전달하여 진화시킬 수 있습니다.",
["ENEMY_QUICKFEET_GATOR_NAME"] = "퀵피트",
["ENEMY_REVENANT_HARVESTER_DESCRIPTION"] = "옛 여사제들은 이제 숲 속을 돌아다니며 혼령을 통해 영향력을 퍼트립니다.",
["ENEMY_REVENANT_HARVESTER_EXTRA"] = "- 주변의 혼령을 레버넌트 수확자로 만듦",
["ENEMY_REVENANT_HARVESTER_NAME"] = "레버넌트 추수꾼",
["ENEMY_REVENANT_SOULCALLER_DESCRIPTION"] = "죽음의 마법에 이끌린 엘프 마법사들이 지하에서 부활하여 쓰러진 자의 혼령을 소환합니다.",
["ENEMY_REVENANT_SOULCALLER_EXTRA"] = "- 타워 무력화\n- 혼령 소환",
["ENEMY_REVENANT_SOULCALLER_NAME"] = "레버넌트 영혼소환사",
["ENEMY_RHINO_DESCRIPTION"] = "전장을 아랑곳하지 않고 짓밟는 살아있는 공성추입니다.",
["ENEMY_RHINO_EXTRA"] = "- 미니 보스\n- 적에게 돌진",
["ENEMY_RHINO_NAME"] = "맹렬 코뿔소",
["ENEMY_RHINO_SPECIAL"] = "적에게 돌진합니다.",
["ENEMY_ROLLING_SENTRY_DESCRIPTION"] = "격추된 후에도 지상에서 적을 사냥합니다.",
["ENEMY_ROLLING_SENTRY_EXTRA"] = "- 죽으면 고철로 변함\n- 원거리",
["ENEMY_ROLLING_SENTRY_NAME"] = "롤링 센트리",
["ENEMY_SCRAP_DRONE_DESCRIPTION"] = "군대를 괴롭히는 것을 목적으로 대충 조립된 기계.",
["ENEMY_SCRAP_DRONE_EXTRA"] = "- 비행",
["ENEMY_SCRAP_DRONE_NAME"] = "고철 드론",
["ENEMY_SCRAP_SPEEDSTER_DESCRIPTION"] = "시끄럽고 짜증나며, 속도에 집착합니다.",
["ENEMY_SCRAP_SPEEDSTER_EXTRA"] = "- 죽으면 고철로 변함",
["ENEMY_SCRAP_SPEEDSTER_NAME"] = "고철 속도광",
["ENEMY_SKUNK_BOMBARDIER_DESCRIPTION"] = "스컹크는 천연 독소를 한 단계 더 발전시켜 적진에 혼란을 퍼뜨립니다.",
["ENEMY_SKUNK_BOMBARDIER_EXTRA"] = "- 낮은 속도\n- 보통 마법 저항력\n- 공격이 플레이어 유닛을 약화시킴\n- 죽을 때 폭발",
["ENEMY_SKUNK_BOMBARDIER_NAME"] = "스컹크 폭격수",
["ENEMY_SKUNK_BOMBARDIER_SPECIAL"] = "공격이 플레이어 유닛을 약화시키고, 죽을 때 폭발하여 피해를 줍니다.",
["ENEMY_SMALL_STALKER_DESCRIPTION"] = "이교도의 마법에 의해 타락한 이 구름 추적자는 전장 위로 순간이동하여 혼란을 야기합니다.",
["ENEMY_SMALL_STALKER_EXTRA"] = "- 공격받을 때 앞으로 순간이동.",
["ENEMY_SMALL_STALKER_NAME"] = "타락한 추적자",
["ENEMY_SMALL_STALKER_SPECIAL"] = "짧은 거리를 순간이동하여 공격을 회피합니다.",
["ENEMY_SPECTER_DESCRIPTION"] = "죽은 후에도 노예가 된 , 산 자를 괴롭힙니다.",
["ENEMY_SPECTER_EXTRA"] = "- 다른 적 및 요소와 상호작용 가능",
["ENEMY_SPECTER_NAME"] = "혼령",
["ENEMY_SPIDEAD_DESCRIPTION"] = "거미 여왕 미갈레의 직계 후손인 이 거미들은 죽어서도 성가시게 굴 방법을 찾는다.",
["ENEMY_SPIDEAD_EXTRA"] = "- 마법 저항\n- 사망 시 거미줄 생성",
["ENEMY_SPIDEAD_NAME"] = "죽음의 후예",
["ENEMY_SPIDERLING_DESCRIPTION"] = "이교도의 마법으로 강화된 거미들. 빠르고 사납습니다. 물 수 있습니다.",
["ENEMY_SPIDERLING_EXTRA"] = "- 빠른 속도\n- 낮은 마법 저항력",
["ENEMY_SPIDERLING_NAME"] = "눈길 새끼거미",
["ENEMY_SPIDER_PRIEST_DESCRIPTION"] = "새로운 신에게 얽매인 사제들은 어둠의 마법을 휘두르며 전장으로 나아간다.",
["ENEMY_SPIDER_PRIEST_EXTRA"] = "- 높은 마법 저항\n- 빈사 상태에서 글레어워든으로 변신",
["ENEMY_SPIDER_PRIEST_NAME"] = "거미의 사제",
["ENEMY_SPIDER_SISTER_DESCRIPTION"] = "거미 여왕을 신봉하는 이들은 그녀의 친족을 소환하기 위해 마법을 사용한다.",
["ENEMY_SPIDER_SISTER_EXTRA"] = "- 마법 저항\n- 글레어브루드를 소환",
["ENEMY_SPIDER_SISTER_NAME"] = "거미 자매",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_DESCRIPTION"] = "전투에 개입하기 위해 미드리아스가 사용하는 그림자 도플갱어입니다.",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_EXTRA"] = "- 적을 피해로부터 보호함\n- 어두운 촉수로 타워를 가둠.",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_NAME"] = "미드리아스의 환영",
["ENEMY_STORM_ELEMENTAL_DESCRIPTION"] = "태풍, 번개, 분노에서 태어난 강력한 정령들. 재의 정령과 먼 친척.",
["ENEMY_STORM_ELEMENTAL_EXTRA"] = "- 높은 방어력\n- 원거리\n- 사망 시 근처 타워 기절시킴",
["ENEMY_STORM_ELEMENTAL_NAME"] = "폭풍의 정령",
["ENEMY_STORM_SPIRIT_DESCRIPTION"] = "폭풍 구름 속을 뛰어다니며 위험과 적을 능숙하게 피하는 작은 용들.",
["ENEMY_STORM_SPIRIT_EXTRA"] = "- 비행\n- 낮은 마법 저항\n- 피해를 입으면 돌진",
["ENEMY_STORM_SPIRIT_NAME"] = "폭풍 드레이클링",
["ENEMY_SURVEILLANCE_SENTRY_DESCRIPTION"] = "하늘에서 적을 감시하도록 드워프에 의해 설계되었습니다.",
["ENEMY_SURVEILLANCE_SENTRY_EXTRA"] = "- 비행\n- 죽으면 롤링 센트리로 변함",
["ENEMY_SURVEILLANCE_SENTRY_NAME"] = "비행 센트리",
["ENEMY_SURVEYOR_HARPY_DESCRIPTION"] = "썩은 고기를 찾아, 독수리는 야수들을 따라 곳곳을 따라다닙니다.",
["ENEMY_SURVEYOR_HARPY_EXTRA"] = "- 비행",
["ENEMY_SURVEYOR_HARPY_NAME"] = "정찰 독수리",
["ENEMY_SURVEYOR_HARPY_SPECIAL"] = "비행.",
["ENEMY_TERRACOTA_DESCRIPTION"] = "주의를 분산시키는 그림자들.",
["ENEMY_TERRACOTA_EXTRA"] = "- 근접",
["ENEMY_TERRACOTA_NAME"] = "환상의 미끼",
["ENEMY_TOWER_RAY_SHEEP_DESCRIPTION"] = "메에에에에.",
["ENEMY_TOWER_RAY_SHEEP_EXTRA"] = "- 저지 불가",
["ENEMY_TOWER_RAY_SHEEP_FLYING_DESCRIPTION"] = "메에에에에.",
["ENEMY_TOWER_RAY_SHEEP_FLYING_EXTRA"] = "- 비행",
["ENEMY_TOWER_RAY_SHEEP_FLYING_NAME"] = "날으는 양",
["ENEMY_TOWER_RAY_SHEEP_NAME"] = "양",
["ENEMY_TURTLE_SHAMAN_DESCRIPTION"] = "평화적으로 보이지만 비열한 마음의 주술사들은 야수들을 치료해 전투 태세를 갖추게 합니다.",
["ENEMY_TURTLE_SHAMAN_EXTRA"] = "- 느린 속도\n- 높은 HP\n- 높은 마법 저항력\n- 적 유닛을 치료",
["ENEMY_TURTLE_SHAMAN_NAME"] = "거북 주술사",
["ENEMY_TURTLE_SHAMAN_SPECIAL"] = "적 유닛을 치료합니다.",
["ENEMY_TUSKED_BRAWLER_DESCRIPTION"] = "돼지 침략군보다 끈질기며 조잡한 갑옷을 입고 있습니다. 항상 싸울 준비가 되어 있습니다.",
["ENEMY_TUSKED_BRAWLER_EXTRA"] = "- 낮은 방어력",
["ENEMY_TUSKED_BRAWLER_NAME"] = "엄니 싸움꾼",
["ENEMY_UNBLINDED_ABOMINATION_DESCRIPTION"] = "전투에서의 야만성으로 알려진, 완전히 타락한 이교도 사제들입니다.",
["ENEMY_UNBLINDED_ABOMINATION_EXTRA"] = "- 생명력이 낮은 유닛을 포식함",
["ENEMY_UNBLINDED_ABOMINATION_NAME"] = "이교도 흉물",
["ENEMY_UNBLINDED_ABOMINATION_SPECIAL"] = "가끔 생명력이 낮은 유닛을 포식합니다.",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_DESCRIPTION"] = "엘프를 노예로 만든 후, 일부 골렘은 광산에서 작업이 원활하게 진행되도록 하기 위해 임명되었습니다.",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_EXTRA"] = "- 엘프를 해방하려면 죽여야 합니다",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_NAME"] = "흉물 감독관",
["ENEMY_UNBLINDED_PRIEST_DESCRIPTION"] = "기도와 오컬트 사이에서 사제들은 어둠의 마법을 휘두르며 전투에 나섭니다.",
["ENEMY_UNBLINDED_PRIEST_EXTRA"] = "- 높은 마법 저항력\n- 죽음에 가까워질 때 흉물로 변함",
["ENEMY_UNBLINDED_PRIEST_NAME"] = "이교도 사제",
["ENEMY_UNBLINDED_PRIEST_SPECIAL"] = "생명력이 낮을 때, 흉물로 변합니다.",
["ENEMY_UNBLINDED_SHACKLER_DESCRIPTION"] = "팔에 박힌 크리스탈을 통해 부패한 마법을 전달하는 섀클러는 근접전에서 무시무시한 적입니다.",
["ENEMY_UNBLINDED_SHACKLER_EXTRA"] = "- 중간 마법 저항\n- 생명력이 낮을 때 타워를 비활성화함",
["ENEMY_UNBLINDED_SHACKLER_NAME"] = "섀클러",
["ENEMY_UNBLINDED_SHACKLER_SPECIAL"] = "탑을 묶어 공격하지 못하게 합니다.",
["ENEMY_VILE_SPAWNER_DESCRIPTION"] = "적들 위로 많은 날아다니는 눈을 던지며, 사악한 스포너들은 항상 모든 방향을 주시합니다.",
["ENEMY_VILE_SPAWNER_EXTRA"] = "- 눈알 졸개를 생성함.",
["ENEMY_VILE_SPAWNER_NAME"] = "사악한 스포너",
["ENEMY_WATER_SORCERESS_DESCRIPTION"] = "물의 힘을 다루어 아군을 치유하고 멀리 있는 적을 쓰러뜨리는 노련한 원소 마법사들.",
["ENEMY_WATER_SORCERESS_EXTRA"] = "- 원거리\n- 중간 마법 저항력\n- 아군을 치유",
["ENEMY_WATER_SORCERESS_NAME"] = "Water Master",
["ENEMY_WATER_SPIRIT_DESCRIPTION"] = "영혼 없는 물의 존재들이 끊임없는 파도로 몰려와 해안을 분노로 휩쓴다.",
["ENEMY_WATER_SPIRIT_EXTRA"] = "- 낮은 마법 저항력\n- 물에서 생성될 수 있음",
["ENEMY_WATER_SPIRIT_NAME"] = "Water Spirit",
["ENEMY_WATER_SPIRIT_SPAWNLESS_DESCRIPTION"] = "영혼 없는 물의 존재들이 끊임없는 파도로 몰려와 해안을 분노로 휩쓴다.",
["ENEMY_WATER_SPIRIT_SPAWNLESS_EXTRA"] = "- 낮은 마법 저항력\n- 물에서 생성될 수 있음",
["ENEMY_WATER_SPIRIT_SPAWNLESS_NAME"] = "Water Spirit",
["ENEMY_WUXIAN_DESCRIPTION"] = "강력하고 내구성이 뛰어난 마법사로, 마법으로 적을 소멸시킨다.",
["ENEMY_WUXIAN_EXTRA"] = "- 원거리\n- 중간 방어구\n- 불타는 지면에서 특수 공격",
["ENEMY_WUXIAN_NAME"] = "Wuxian",
["ERROR_MESSAGE_GENERIC"] = "이런! 오류가 발생했어요.",
["Earn huge bonus points and gold by calling waves earlier!"] = "공격을 조기 시작해 보너스 점수와 골드를 잔뜩 버세요!",
["FIRST_WEEK_PACK"] = "선물 ",
["FULLADS_BONUS_REWARDS_TITLE"] = "보너스 보상!",
["FULLADS_BUTTON_BUY"] = "구매",
["FULLADS_BUTTON_CLAIM"] = "광고를 보고 이 보상들을 받으세요!",
["FULLADS_BUTTON_CLAIM_SHORT"] = "획득!",
["FULLADS_BUTTON_HIRE"] = "고용",
["FULLADS_BUTTON_INFO"] = "정보",
["FULLADS_BUTTON_PLAY"] = "광고를 보고 이 보상들을 받으세요!",
["FULLADS_BUTTON_PLAY_SHORT"] = "플레이!",
["FULLADS_BUTTON_SPIN"] = "광고를 보고 룰렛을 돌리세요!",
["FULLADS_BUTTON_SPIN_SHORT"] = "돌리기!",
["FULLADS_BUTTON_UNLOCK"] = "잠금 해제",
["FULLADS_DEFEAT_ENDLESS_REWARDS_TITLE"] = "보상!",
["FULLADS_DEFEAT_REWARDS_TITLE"] = "보상!",
["FULLADS_GNOME_REWARDS_TITLE"] = "노움 보상!",
["FULLADS_MAP_CROWNS_DESCRIPTION"] = "왕관을 사용하여 하루 동안 영웅을 고용하세요.",
["FULLADS_MAP_GEMS_DESCRIPTION"] = "보석을 사용하여 아이템을 구매하고 영웅을 영구적으로 잠금 해제하세요.",
["FULLADS_MAP_HEROROOM_HELP_CROWNS"] = "1일 고용",
["FULLADS_MAP_HEROROOM_HELP_GEMS"] = "영구 구매",
["FULLADS_MAP_STARS_DESCRIPTION"] = "별을 사용하여 업그레이드를 구매하세요.",
["FULLADS_VICTORY_CLAIM_BONUS"] = "광고를 보고 %sX 보너스를 받으세요",
["FULLADS_VICTORY_REWARDS_TITLE"] = "승리 보상!",
["FULLADS_WHEEL_PROBABILITIES_TITLE"] = "보상 확률",
["FULLADS_WHEEL_REWARDS_TITLE"] = "행운의 룰렛!",
["FULLADS_YOUR_REWARDS_TITLE"] = "내 보상",
["FULLADS_YOUR_REWARD_TITLE"] = "내 보상",
["Face an endless unrelenting enemy force and try to defeat as many as possible to comete for the best score!"] = "끝없이 밀려오는 적군을 상대하고 최대한 많이 무찔러서 최고 점수를 획득하세요!",
["Face an endless unrelenting enemy force and try to defeat as many as possible to compete for the best score!"] = "끝없이 밀려오는 적군을 상대하고 최대한 많이 무찔러서 최고 점수를 획득하세요!",
["Failed to load Rewarded Video, first session !"] = "보상 영상을 불러오지 못했습니다",
["Failed to load Rewarded Video, internal error !"] = "보상 영상을 불러오지 못했습니다",
["Failed to load Rewarded Video, missing location parameter !"] = "보상 영상을 불러오지 못했습니다",
["Failed to load Rewarded Video, network error !"] = "보상 영상을 불러오지 못했습니다",
["Failed to load Rewarded Video, no Internet connection !"] = "보상 영상을 불러오지 못했습니다",
["Failed to load Rewarded Video, no ad found !"] = "보상 영상을 불러오지 못했습니다",
["Failed to load Rewarded Video, session not started !"] = "보상 영상을 불러오지 못했습니다",
["Failed to load Rewarded Video, too many connections !"] = "보상 영상을 불러오지 못했습니다",
["Failed to load Rewarded Video, unknown error !"] = "보상 영상을 불러오지 못했습니다",
["Failed to load Rewarded Video, wrong orientation !"] = "보상 영상을 불러오지 못했습니다",
["GAME PAUSED"] = "일시 중지됨",
["GAME_TITLE_KR5"] = "킹덤 러시 5: 동맹",
["GEMS_BARREL_NAME"] = "보석 한 통",
["GEMS_CHEST_NAME"] = "보석 한 상자",
["GEMS_HANDFUL_NAME"] = "보석 한 줌",
["GEMS_MOUNTAIN_NAME"] = "보석 산더미",
["GEMS_POUCH_NAME"] = "보석 주머니",
["GEMS_WAGON_NAME"] = "보석 수레",
["GET_ALL_AWESOME_HEROES"] = "이 멋진 영웅들을 모두 영입하세요.",
["GET_THIS_AWESOME"] = "이 멋진 영웅을\n영입하세요.",
["GET_THIS_AWESOME_2"] = "이 멋진 영웅들을\n영입하세요.",
["GET_THIS_AWESOME_3"] = "이 멋진 영웅들을\n영입하세요.",
["GIFT_CLAIMED"] = "선물 받음! ",
["GOOGLE_PLAY"] = "Google Play",
["Got it!"] = "알겠습니다!",
["HERO LEVEL UP!"] = "영웅 레벨 업!",
["HERO ROOM"] = "영웅들",
["HERO UNLOCKED!"] = "영웅 개방됨!",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_1"] = "%$heroes.hero_bird.ultimate.bird.duration[2]%$ 초 동안 지역 위를 날며 적을 공격하는 그리폰을 소환하여 매번 %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[2]%$의 피해를 줍니다.",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_2"] = "%$heroes.hero_bird.ultimate.bird.duration[3]%$ 초 동안 지역 위를 날며 적을 공격하는 그리폰을 소환하여 매번 %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[3]%$의 피해를 줍니다.",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_3"] = "%$heroes.hero_bird.ultimate.bird.duration[4]%$ 초 동안 지역 위를 날며 적을 공격하는 그리폰을 소환하여 매번 %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[4]%$의 피해를 줍니다.",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_DESCRIPTION"] = "지역 위를 날며 적을 공격하는 그리폰을 소환합니다.",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_NAME"] = "맹금류",
["HERO_BIRD_BIRDS_OF_PREY_TITLE"] = "전투의 새들",
["HERO_BIRD_CLASS"] = "에이스 라이더",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_1"] = "적들 위에서 흩어지는 폭발물을 던져 각각 %$heroes.hero_bird.cluster_bomb.explosion_damage_min[1]%$의 피해를 주고 바닥을 %$heroes.hero_bird.cluster_bomb.fire_duration[1]%$ 초 동안 불태워 3 초 동안 %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$의 피해를 입힙니다.",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_2"] = "적들 위에서 흩어지는 폭발물을 던져 각각 %$heroes.hero_bird.cluster_bomb.explosion_damage_min[2]%$의 피해를 주고 바닥을 %$heroes.hero_bird.cluster_bomb.fire_duration[2]%$ 초 동안 불태워 3 초 동안 %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$의 피해를 입힙니다.",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_3"] = "적들 위에서 흩어지는 폭발물을 던져 각각 %$heroes.hero_bird.cluster_bomb.explosion_damage_min[3]%$의 피해를 주고 바닥을 %$heroes.hero_bird.cluster_bomb.fire_duration[3]%$ 초 동안 불태워 3 초 동안 %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$의 피해를 입힙니다.",
["HERO_BIRD_CLUSTER_BOMB_TITLE"] = "카펫 폭격",
["HERO_BIRD_DESC"] = "용감한 그리폰 기수는 강철과 불로 가득한 무기를 휘두르며 전투에 뛰어듭니다. 암흑 군대가 자신의 집을 침략한 이후로 그는 마지못해 얼라이언스에 합류했지만, 브로든은 리니리아의 현 상태를 회복하기 위한 수단으로 이교도를 파괴하는 것에 동의했습니다.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_1"] = "그리폰이 급강하해 최대 %$heroes.hero_bird.eat_instakill.hp_max[1]%$의 생명력을 가진 적을 집어삼킵니다.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_2"] = "그리폰이 급강하해 최대 %$heroes.hero_bird.eat_instakill.hp_max[2]%$의 생명력을 가진 적을 집어삼킵니다.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_3"] = "그리폰이 급강하해 최대 %$heroes.hero_bird.eat_instakill.hp_max[3]%$의 생명력을 가진 적을 집어삼킵니다.",
["HERO_BIRD_EAT_INSTAKILL_TITLE"] = "헌팅 다이브",
["HERO_BIRD_GATTLING_DESCRIPTION_1"] = "적에게 총알을 퍼부어 %$heroes.hero_bird.gattling.s_damage_min[1]%$-%$heroes.hero_bird.gattling.s_damage_max[1]%$의 물리 피해를 줍니다.",
["HERO_BIRD_GATTLING_DESCRIPTION_2"] = "적에게 총알을 퍼부어 %$heroes.hero_bird.gattling.s_damage_min[2]%$-%$heroes.hero_bird.gattling.s_damage_max[2]%$의 물리 피해를 줍니다.",
["HERO_BIRD_GATTLING_DESCRIPTION_3"] = "적에게 총알을 퍼부어 %$heroes.hero_bird.gattling.s_damage_min[3]%$-%$heroes.hero_bird.gattling.s_damage_max[3]%$의 물리 피해를 줍니다.",
["HERO_BIRD_GATTLING_TITLE"] = "모범적인 리드",
["HERO_BIRD_NAME"] = "브로든",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_1"] = "그리폰은 귀청이 터질 듯한 비명을 내어 적을 %$heroes.hero_bird.shout_stun.stun_duration[1]%$ 초 동안 기절시키고 이후 %$heroes.hero_bird.shout_stun.slow_duration[1]%$ 초 동안 느려지게 합니다.",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_2"] = "그리폰은 귀청이 터질 듯한 비명을 내어 적을 %$heroes.hero_bird.shout_stun.stun_duration[2]%$ 초 동안 기절시키고 이후 %$heroes.hero_bird.shout_stun.slow_duration[2]%$ 초 동안 느려지게 합니다.",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_3"] = "그리폰은 귀청이 터질 듯한 비명을 내어 적을 %$heroes.hero_bird.shout_stun.stun_duration[3]%$ 초 동안 기절시키고 이후 %$heroes.hero_bird.shout_stun.slow_duration[3]%$ 초 동안 느려지게 합니다.",
["HERO_BIRD_SHOUT_STUN_TITLE"] = "공포의 비명",
["HERO_BUILDER_CLASS"] = "현장 감독관",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_1"] = "임시 타워를 건설하여 %$heroes.hero_builder.defensive_turret.duration[1]%$ 초 동안 지나가는 적을 공격하며, 공격 당 %$heroes.hero_builder.defensive_turret.attack.damage_min[1]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[1]%$의 물리 피해를 줍니다",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_2"] = "임시 타워를 건설하여 %$heroes.hero_builder.defensive_turret.duration[2]%$ 초 동안 지나가는 적을 공격하며, 공격 당 %$heroes.hero_builder.defensive_turret.attack.damage_min[2]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[2]%$의 물리 피해를 줍니다.",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_3"] = "임시 타워를 건설하여 %$heroes.hero_builder.defensive_turret.duration[3]%$ 초 동안 지나가는 적을 공격하며, 공격 당 %$heroes.hero_builder.defensive_turret.attack.damage_min[3]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[3]%$의 물리 피해를 줍니다.",
["HERO_BUILDER_DEFENSIVE_TURRET_TITLE"] = "방어 터렛",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_1"] = "나무 배트를 빠르게 회전해 주변의 적들에게 %$heroes.hero_builder.demolition_man.s_damage_min[1]%$-%$heroes.hero_builder.demolition_man.s_damage_max[1]%$의 물리 피해를 줍니다.",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_2"] = "나무 배트를 빠르게 회전해 주변의 적들에게 %$heroes.hero_builder.demolition_man.s_damage_min[2]%$-%$heroes.hero_builder.demolition_man.s_damage_max[2]%$의 물리 피해를 줍니다.",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_3"] = "나무 배트를 빠르게 회전해 주변의 적들에게 %$heroes.hero_builder.demolition_man.s_damage_min[3]%$-%$heroes.hero_builder.demolition_man.s_damage_max[3]%$의 물리 피해를 줍니다.",
["HERO_BUILDER_DEMOLITION_MAN_TITLE"] = "철거 전문가",
["HERO_BUILDER_DESC"] = "수년 간 리니리아 방어체계를 구축하면서 토레스는 전투에 대한 한 두 가지 가르침을 얻었습니다. 이제 온 왕국이 위험에 처해 있기에 (그리고 옆에서 지켜보는 것이 지루하기 때문에) 토레스는 자신의 모든 도구와 지식을 전투에 사용합니다.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_1"] = "토레스가 간식을 먹고 %$heroes.hero_builder.lunch_break.heal_hp[1]%$의 생명력을 회복합니다.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_2"] = "토레스가 간식을 먹고 %$heroes.hero_builder.lunch_break.heal_hp[2]%$의 생명력을 회복합니다.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_3"] = "토레스가 간식을 먹고 %$heroes.hero_builder.lunch_break.heal_hp[3]%$의 생명력을 회복합니다.",
["HERO_BUILDER_LUNCH_BREAK_TITLE"] = "간식 시간",
["HERO_BUILDER_NAME"] = "토레스",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_1"] = "그의 편에서 싸우는 두 명의 건축가를 %$heroes.hero_builder.overtime_work.soldier.duration%$ 초 동안 호출합니다.",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_2"] = "건축가들은 %$heroes.hero_builder.overtime_work.soldier.hp_max[2]%$의 생명력을 가지며 %$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[2]%$의 물리 피해를 입힙니다. 그들은 %$heroes.hero_builder.overtime_work.soldier.duration%$ 초 동안 싸웁니다.",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_3"] = "건축가들은 %$heroes.hero_builder.overtime_work.soldier.hp_max[3]%$의 생명력을 가지며 %$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[3]%$의 물리 피해를 입니다. 건축가들은 %$heroes.hero_builder.overtime_work.soldier.duration%$ 초 동안 싸웁니다.",
["HERO_BUILDER_OVERTIME_WORK_TITLE"] = "일하는 남자들",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_1"] = "길 위에 거대한 강철 공을 떨어뜨려 %$heroes.hero_builder.ultimate.damage[2]%$의 물리 피해를 주고 적을 %$heroes.hero_builder.ultimate.stun_duration[2]%$ 초 동안 기절시킵니다.",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_2"] = "길 위에 거대한 강철 공을 떨어뜨려 %$heroes.hero_builder.ultimate.damage[3]%$의 물리 피해를 주고 적을 %$heroes.hero_builder.ultimate.stun_duration[3]%$ 초 동안 기절시킵니다.",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_3"] = "길 위에 거대한 강철 공을 떨어뜨려 %$heroes.hero_builder.ultimate.damage[4]%$의 물리 피해를 주고 적을 %$heroes.hero_builder.ultimate.stun_duration[4]%$ 초 동안 기절시킵니다.",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_DESCRIPTION"] = "경로 위에 레킹볼을 떨어뜨려 적에게 피해를 줍니다.",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_NAME"] = "레킹볼",
["HERO_BUILDER_WRECKING_BALL_TITLE"] = "레킹볼",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_1"] = "실바라는 %$heroes.hero_dragon_arb.ultimate.duration[2]%$ 초 동안 그녀의 진정한 모습을 해방하여, %$heroes.hero_dragon_arb.ultimate.s_bonuses[2]%$%의 피해, 속도, 저항력을 얻고 일부 능력이 진화합니다.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_2"] = "실바라는 %$heroes.hero_dragon_arb.ultimate.duration[3]%$ 초 동안 그녀의 진정한 모습을 해방하여, %$heroes.hero_dragon_arb.ultimate.s_bonuses[3]%$%의 피해, 속도, 저항력을 얻고 일부 능력이 진화합니다.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_3"] = "실바라는 %$heroes.hero_dragon_arb.ultimate.duration[4]%$ 초 동안 그녀의 진정한 모습을 해방하여, %$heroes.hero_dragon_arb.ultimate.s_bonuses[4]%$%의 피해, 속도, 저항력을 얻고 일부 능력이 진화합니다.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_DESCRIPTION"] = "실바라의 진정한 모습을 해방시킵니다.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_NAME"] = "내면의 본성",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_TITLE"] = "내면의 본성",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_1"] = "녹조류를 %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[1]%$ 초 동안 아르보리아인으로 탈바꿈시킵니다. 내면의 본성 동안에는 더 강한 아르보리아인을 소환합니다.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_2"] = "녹조류를 %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[2]%$ 초 동안 아르보리아인으로 탈바꿈시킵니다. 내면의 본성 동안에는 더 강한 아르보리아인을 소환합니다.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_3"] = "녹조류를 %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[3]%$ 초 동안 아르보리아인으로 탈바꿈시킵니다. 내면의 본성 동안에는 더 강한 아르보리아인을 소환합니다.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_TITLE"] = "숲의 부름",
["HERO_DRAGON_ARB_CLASS"] = "자연의 힘",
["HERO_DRAGON_ARB_DESC"] = "자연의 드래곤이자 아르보리아의 수호자인 그녀는 숨결로 숲을 엮고 날개로 바람을 춤추게 합니다. 자연 그 자체와 마찬가지로 그녀는 배려도 하지만 벌도 내립니다. 쓰레기를 버리지 않도록 하십시오!",
["HERO_DRAGON_ARB_NAME"] = "실바라",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_1"] = "매 %$heroes.hero_dragon_arb.thorn_bleed.cooldown[1]%$ 초마다 실바라는 다음 호흡을 강화하여 적의 속도에 따라 피해를 줍니다. 내면의 본성 동안에는 %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[1]%$%의 확률로 즉사시킵니다.",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_2"] = "매 %$heroes.hero_dragon_arb.thorn_bleed.cooldown[2]%$ 초마다 실바라는 다음 호흡을 강화하여 적의 속도에 따라 피해를 줍니다. 내면의 본성 동안에는 %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[2]%$%의 확률로 즉사시킵니다.",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_3"] = "매 %$heroes.hero_dragon_arb.thorn_bleed.cooldown[3]%$ 초마다 실바라는 다음 호흡을 강화하여 적의 속도에 따라 피해를 줍니다. 내면의 본성 동안에는 %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[3]%$%의 확률로 즉사시킵니다.",
["HERO_DRAGON_ARB_THORN BLEED_TITLE"] = "가시 돋친 숨결",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_1"] = "%$heroes.hero_dragon_arb.tower_runes.duration[1]%$ 초 동안 주변 타워의 피해를 %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[1]%$% 증가시킵니다.",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_2"] = "%$heroes.hero_dragon_arb.tower_runes.duration[2]%$ 초 동안 주변 타워의 피해를 %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[2]%$% 증가시킵니다.",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_3"] = "%$heroes.hero_dragon_arb.tower_runes.duration[3]%$ 초 동안 주변 타워의 피해를 %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[3]%$% 증가시킵니다.",
["HERO_DRAGON_ARB_TOWER RUNES_TITLE"] = "깊은 뿌리",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_1"] = "탑 근처에 %$heroes.hero_dragon_arb.tower_plants.duration[1]%$ 초 동안 지속되는 식물을 소환합니다. 그들의 충성심에 따라, 피해를 주고 속도를 늦추는 독성 식물이 되거나 아군을 치유하는 치유 식물이 됩니다.",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_2"] = "탑 근처에 %$heroes.hero_dragon_arb.tower_plants.duration[2]%$ 초 동안 지속되는 식물을 소환합니다. 그들의 충성심에 따라, 피해를 주고 속도를 늦추는 독성 식물이 되거나 아군을 치유하는 치유 식물이 됩니다.",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_3"] = "탑 근처에 %$heroes.hero_dragon_arb.tower_plants.duration[3]%$ 초 동안 지속되는 식물을 소환합니다. 그들의 충성심에 따라, 피해를 주고 속도를 늦추는 독성 식물이 되거나 아군을 치유하는 치유 식물이 됩니다.",
["HERO_DRAGON_ARB_TOWER_PLANTS_TITLE"] = "생명의 인도자",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_1"] = "%$heroes.hero_dragon_bone.burst.proj_count[1]%$ 개의 마법탄을 발사하여 각각 %$heroes.hero_dragon_bone.burst.damage_min[1]%$-%$heroes.hero_dragon_bone.burst.damage_max[1]%$의 무속성 피해를 입히고 역병을 퍼트립니다.",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_2"] = "%$heroes.hero_dragon_bone.burst.proj_count[2]%$ 개의 마법탄을 발사하여 각각 %$heroes.hero_dragon_bone.burst.damage_min[2]%$-%$heroes.hero_dragon_bone.burst.damage_max[2]%$의 무속성 피해를 입히고 역병을 퍼트립니다.",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_3"] = "%$heroes.hero_dragon_bone.burst.proj_count[3]%$ 개의 마법탄을 발사하여 각각 %$heroes.hero_dragon_bone.burst.damage_min[3]%$-%$heroes.hero_dragon_bone.burst.damage_max[3]%$의 무속성 피해를 입히고 역병을 퍼트립니다.",
["HERO_DRAGON_BONE_BURST_TITLE"] = "폭발 확산",
["HERO_DRAGON_BONE_CLASS"] = "드라콜리치",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_1"] = "%$heroes.hero_dragon_bone.cloud.duration[1]%$ 초 동안 전염성 구름으로 지역을 덮어 적들을 느리게 만들고 역병을 퍼트립니다.",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_2"] = "%$heroes.hero_dragon_bone.cloud.duration[2]%$ 초 동안 전염성 구름으로 지역을 덮어 적들을 느리게 만들고 역병을 퍼트립니다.",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_3"] = "%$heroes.hero_dragon_bone.cloud.duration[3]%$ 초 동안 전염성 구름으로 지역을 덮어 적들을 느리게 만들고 역병을 퍼트립니다.",
["HERO_DRAGON_BONE_CLOUD_TITLE"] = "역병 구름",
["HERO_DRAGON_BONE_DESC"] = "베즈난의 정복 전쟁 중 그에 의해 자유를 찾은 후, 본하트는 자신의 힘을 사용하여 땅을 샅샅이 뒤져 암흑 마법사의 계획에 위협이 될 수 있는 마법사들을 찾아 그 빚을 갚겠다고 제안했습니다.",
["HERO_DRAGON_BONE_NAME"] = "본하트",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_1"] = "경로에 돌진하여, %$heroes.hero_dragon_bone.nova.damage_min[1]%$-%$heroes.hero_dragon_bone.nova.damage_max[1]%$의 폭발 피해를 적에게 가하고 역병을 퍼트립니다.",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_2"] = "경로에 돌진하여, %$heroes.hero_dragon_bone.nova.damage_min[2]%$-%$heroes.hero_dragon_bone.nova.damage_max[2]%$의 폭발 피해를 적에게 가하고 역병을 퍼트립니다.",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_3"] = "경로에 돌진하여, %$heroes.hero_dragon_bone.nova.damage_min[3]%$-%$heroes.hero_dragon_bone.nova.damage_max[3]%$의 폭발 피해를 적에게 가하고 역병을 퍼트립니다.",
["HERO_DRAGON_BONE_NOVA_TITLE"] = "질병 폭발",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_1"] = "적에게 %$heroes.hero_dragon_bone.rain.bones_count[1]%$ 개의 등뼈를 발사해 %$heroes.hero_dragon_bone.rain.damage_min[1]%$-%$heroes.hero_dragon_bone.rain.damage_max[1]%$의 무속성 피해를 입히고 잠시 동안 기절시킵니다.",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_2"] = "적에게 %$heroes.hero_dragon_bone.rain.bones_count[2]%$ 개의 등뼈를 발사해 %$heroes.hero_dragon_bone.rain.damage_min[2]%$-%$heroes.hero_dragon_bone.rain.damage_max[2]%$의 무속성 피해를 입히고 잠시 동안 기절시킵니다.",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_3"] = "적에게 %$heroes.hero_dragon_bone.rain.bones_count[3]%$ 개의 등뼈를 발사해 %$heroes.hero_dragon_bone.rain.damage_min[3]%$-%$heroes.hero_dragon_bone.rain.damage_max[3]%$의 무속성 피해를 입히고 잠시 동안 기절시킵니다.",
["HERO_DRAGON_BONE_RAIN_TITLE"] = "등뼈 소나기",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_1"] = "해골 비룡 두 마리를 소환합니다. 각각의 비룡은 %$heroes.hero_dragon_bone.ultimate.dog.hp[2]%$의 생명력을 가지고 있으며, %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[2]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[2]%$의 물리 피해를 줍니다.",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_2"] = "해골 비룡 두 마리를 소환합니다. 각각의 비룡은 %$heroes.hero_dragon_bone.ultimate.dog.hp[3]%$의 생명력을 가지고 있으며, %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[3]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[3]%$의 물리 피해를 줍니다.",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_3"] = "해골 비룡 두 마리를 소환합니다. 각각의 비룡은 %$heroes.hero_dragon_bone.ultimate.dog.hp[4]%$의 생명력을 가지고 있으며, %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[4]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[4]%$의 물리 피해를 줍니다.",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_DESCRIPTION"] = "해골 비룡 두 마리를 소환합니다. ",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_NAME"] = "드레이크 소환",
["HERO_DRAGON_BONE_RAISE_DRAKES_TITLE"] = "비룡 소환",
["HERO_DRAGON_GEM_CLASS"] = "흔들리지 않는 자",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_1"] = "몇 초 동안 적을 크리스탈로 가둡니다. 크리스탈이 폭발하여 대상을 즉시 죽이고 주변에 %$heroes.hero_dragon_gem.crystal_instakill.s_damage[1]%$의 무속성 피해를 줍니다.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_2"] = "몇 초 동안 적을 크리스탈로 가둡니다. 크리스탈이 폭발하여 대상을 즉시 죽이고 주변에 %$heroes.hero_dragon_gem.crystal_instakill.s_damage[2]%$의 무속성 피해를 줍니다.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_3"] = "몇 초 동안 적을 크리스탈로 가둡니다. 크리스탈이 폭발하여 대상을 즉시 죽이고 주변에 %$heroes.hero_dragon_gem.crystal_instakill.s_damage[3]%$의 무속성 피해를 줍니다.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_TITLE"] = "가넷 무덤",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_1"] = "길 위에 크리스탈을 던져 적의 속도를 %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% 감소시키고 주변에 매 1 초마다 %$heroes.hero_dragon_gem.crystal_totem.s_damage[1]%$의 마법 피해를 입힙니다. %$heroes.hero_dragon_gem.crystal_totem.duration[1]%$ 초 동안 지속됩니다.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_2"] = "길 위에 크리스탈을 던져 적의 속도를 %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% 감소시키고 주변에 매 1 초마다 %$heroes.hero_dragon_gem.crystal_totem.s_damage[2]%$의 마법 피해를 입힙니다. %$heroes.hero_dragon_gem.crystal_totem.duration[2]%$ 초 동안 지속됩니다.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_3"] = "길 위에 크리스탈을 던져 적의 속도를 %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% 감소시키고 주변에 매 1 초마다 %$heroes.hero_dragon_gem.crystal_totem.s_damage[3]%$의 마법 피해를 입힙니다. %$heroes.hero_dragon_gem.crystal_totem.duration[3]%$ 초 동안 지속됩니다.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_TITLE"] = "전력 도관",
["HERO_DRAGON_GEM_DESC"] = "코즈미어의 고립된 생활은 교단이 포세이큰 협곡에서 활동을 시작하면서 중단되었습니다. 침입자를 제거하기 위해 드래곤은 베즈난과 계약을 맺고 적에 맞서 얼라이언스에 합류했습니다.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_1"] = "%$heroes.hero_dragon_gem.ultimate.max_shards[2]%$ 크리스탈 포격을 내려, 해당 지역의 적에게 %$heroes.hero_dragon_gem.ultimate.damage_min[2]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[2]%$의 무속성 피해를 줍니다.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_2"] = "%$heroes.hero_dragon_gem.ultimate.max_shards[3]%$ 크리스탈 포격을 내려, 해당 지역의 적에게 %$heroes.hero_dragon_gem.ultimate.damage_min[3]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[3]%$의 무속성 피해를 줍니다.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_3"] = "%$heroes.hero_dragon_gem.ultimate.max_shards[4]%$ 크리스탈 포화를 내려, 해당 지역의 적에게 %$heroes.hero_dragon_gem.ultimate.damage_min[4]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[4]%$의 무속성 피해를 줍니다.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_DESCRIPTION"] = "적에게 크리스탈 포격을 내립니다.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_NAME"] = "크리스탈 눈사태",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_TITLE"] = "크리스탈 눈사태",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_1"] = "길 위에 크리스탈 가시가 솟아나 각 적에게 %$heroes.hero_dragon_gem.floor_impact.damage_min[1]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[1]%$의 물리 피해를 줍니다.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_2"] = "길 위에 크리스탈 가시가 솟아나 각 적에게 %$heroes.hero_dragon_gem.floor_impact.damage_min[2]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[2]%$의 물리 피해를 줍니다.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_3"] = "길 위에 크리스탈 가시가 솟아나 각 적에게 %$heroes.hero_dragon_gem.floor_impact.damage_min[3]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[3]%$의 물리 피해를 줍니다.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_TITLE"] = "프리즘 파편",
["HERO_DRAGON_GEM_NAME"] = "코즈미어",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_1"] = "적 그룹을 결정화하여 %$heroes.hero_dragon_gem.stun.duration[1]%$ 초 동안 기절시킵니다.",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_2"] = "적 그룹을 결정화하여 %$heroes.hero_dragon_gem.stun.duration[2]%$ 초 동안 기절시킵니다.",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_3"] = "적 그룹을 결정화하여 %$heroes.hero_dragon_gem.stun.duration[3]%$ 초 동안 기절시킵니다.",
["HERO_DRAGON_GEM_STUN_TITLE"] = "마비의 숨결",
["HERO_HUNTER_BEASTS_DESCRIPTION_1"] = "%$heroes.hero_hunter.beasts.duration[1]%$ 초 동안 근처의 적을 공격하는 2 마리의 박쥐를 소환하여 %$heroes.hero_hunter.beasts.damage_min[1]%$-%$heroes.hero_hunter.beasts.damage_max[1]%$의 물리 피해를 줍니다. 각 박쥐는 대상에게 %$heroes.hero_hunter.beasts.gold_to_steal[1]%$ 골드를 훔칠 확률이 있습니다.",
["HERO_HUNTER_BEASTS_DESCRIPTION_2"] = "%$heroes.hero_hunter.beasts.duration[2]%$ 초 동안 근처의 적을 공격하는 2 마리의 박쥐를 소환하여 %$heroes.hero_hunter.beasts.damage_min[2]%$-%$heroes.hero_hunter.beasts.damage_max[2]%$의 물리 피해를 줍니다. 각 박쥐는 대상에게 %$heroes.hero_hunter.beasts.gold_to_steal[2]%$ 골드를 훔칠 확률이 있습니다.",
["HERO_HUNTER_BEASTS_DESCRIPTION_3"] = "%$heroes.hero_hunter.beasts.duration[3]%$ 초 동안 근처의 적을 공격하는 2 마리의 박쥐를 소환하여 %$heroes.hero_hunter.beasts.damage_min[3]%$-%$heroes.hero_hunter.beasts.damage_max[3]%$의 물리적 피해를 줍니다. 각 박쥐는 대상에게 %$heroes.hero_hunter.beasts.gold_to_steal[3]%$ 골드를 훔칠 확률이 있습니다.",
["HERO_HUNTER_BEASTS_TITLE"] = "황혼의 야수들",
["HERO_HUNTER_CLASS"] = "은빛 여사냥꾼",
["HERO_HUNTER_DESC"] = "뱀파이어와 유명한 사냥꾼 사이에서 태어난 안야는 어둠의 존재에 맞서 싸우는 아버지의 발자취를 따릅니다. 이교도를 끊임없이 사냥하다가 그녀는 남쪽 땅에 이르게 되었고 얼라이언스에 합류했습니다.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_1"] = "매 일곱 번째 근접 공격은 %$heroes.hero_hunter.heal_strike.damage_min[1]%$-%$heroes.hero_hunter.heal_strike.damage_max[1]%$의 무속성 피해를 주고 대상의 최대 생명력의 %$heroes.hero_hunter.heal_strike.heal_factor[1]%$%만큼 안야를 치유합니다.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_2"] = "매 일곱 번째 근접 공격은 %$heroes.hero_hunter.heal_strike.damage_min[2]%$-%$heroes.hero_hunter.heal_strike.damage_max[2]%$의 무속성 피해를 주고 대상의 최대 생명력의 %$heroes.hero_hunter.heal_strike.heal_factor[2]%$%만큼 안야를 치유합니다.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_3"] = "매 일곱 번째 근접 공격은 %$heroes.hero_hunter.heal_strike.damage_min[3]%$-%$heroes.hero_hunter.heal_strike.damage_max[3]%$의 무속성 피해를 주고 대상의 최대 생명력의 %$heroes.hero_hunter.heal_strike.heal_factor[3]%$%만큼 안야를 치유합니다.",
["HERO_HUNTER_HEAL_STRIKE_TITLE"] = "뱀파이어의 발톱",
["HERO_HUNTER_NAME"] = "아냐",
["HERO_HUNTER_RICOCHET_DESCRIPTION_1"] = "아냐는 안개로 변하여 %$heroes.hero_hunter.ricochet.s_bounces[1]%$ 명의 적 사이를 튕기며 각각에게 %$heroes.hero_hunter.ricochet.damage_min[1]%$-%$heroes.hero_hunter.ricochet.damage_max[1]%$의 물리 피해를 줍니다.",
["HERO_HUNTER_RICOCHET_DESCRIPTION_2"] = "아냐는 안개로 변하여 %$heroes.hero_hunter.ricochet.s_bounces[2]%$ 명의 적 사이를 튕기며 각각에게 %$heroes.hero_hunter.ricochet.damage_min[2]%$-%$heroes.hero_hunter.ricochet.damage_max[2]%$의 물리 피해를 줍니다.",
["HERO_HUNTER_RICOCHET_DESCRIPTION_3"] = "아냐는 안개로 변하여 %$heroes.hero_hunter.ricochet.s_bounces[3]%$ 명의 적 사이를 튕기며 각각에게 %$heroes.hero_hunter.ricochet.damage_min[3]%$-%$heroes.hero_hunter.ricochet.damage_max[3]%$의 물리 피해를 줍니다.",
["HERO_HUNTER_RICOCHET_TITLE"] = "안개 걸음",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_1"] = "주위의 모든 적에게 총을 발사하여 각 적에게 초당 %$heroes.hero_hunter.shoot_around.s_damage_min[1]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[1]%$의 무속성 피해를 줍니다.",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_2"] = "주위의 모든 적에게 총을 발사하여 각 적에게 초당 %$heroes.hero_hunter.shoot_around.s_damage_min[2]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[2]%$의 무속성 피해를 줍니다.",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_3"] = "주위의 모든 적에게 총을 발사하여 각 적에게 초당 %$heroes.hero_hunter.shoot_around.s_damage_min[3]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[3]%$의 무속성 피해를 줍니다.",
["HERO_HUNTER_SHOOT_AROUND_TITLE"] = "은빛 폭풍",
["HERO_HUNTER_SPIRIT_DESCRIPTION_1"] = "단테의 투영을 소환합니다. %$heroes.hero_hunter.ultimate.duration%$ 초 동안 초당 %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[2]%$의 무속성 피해를 주고, 안야가 죽은 곳이 가까우면 딸을 되살립니다.",
["HERO_HUNTER_SPIRIT_DESCRIPTION_2"] = "단테의 투영을 소환합니다. %$heroes.hero_hunter.ultimate.duration%$ 초 동안 초당 %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[3]%$의 무속성 피해를 주고, 안야가 죽은 곳이 가까우면 딸을 되살립니다.",
["HERO_HUNTER_SPIRIT_DESCRIPTION_3"] = "단테의 투영을 소환합니다. %$heroes.hero_hunter.ultimate.duration%$ 초 동안 초당 %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[4]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[4]%$의 무속성 피해를 주고, 안야가 죽은 곳이 가까우면 딸을 되살립니다.",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_DESCRIPTION"] = "단테의 투영을 소환하여 적을 느리게 하고 공격합니다.",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_NAME"] = "사냥꾼의 도움",
["HERO_HUNTER_SPIRIT_TITLE"] = "사냥꾼의 도움",
["HERO_HUNTER_ULTIMATE_ENTITY_NAME"] = "단테의 투영",
["HERO_LAVA_CLASS"] = "용암의 분노",
["HERO_LAVA_DESC"] = "그림비어드의 활동으로 불길하고 파괴적인 존재가 깊은 잠에서 깨어났습니다. 대화는 그의 강점이 아니기 때문에 크라토아는 다시 잠을 잘 수 있을 정도로 진정될 때까지 적진을 강타할 것입니다.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_1"] = "용암 덩어리를 던져 %$heroes.hero_lava.double_trouble.s_damage[1]%$의 폭발 피해를 적에게 입히고, %$heroes.hero_lava.double_trouble.soldier.hp_max[1]%$의 생명력을 가진 마그마이트를 %$heroes.hero_lava.double_trouble.soldier.duration%$ 초 동안 소환합니다.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_2"] = "용암 덩어리를 던져 %$heroes.hero_lava.double_trouble.s_damage[2]%$의 폭발 피해를 적에게 입히고, %$heroes.hero_lava.double_trouble.soldier.hp_max[2]%$의 생명력을 가진 마그마이트를 %$heroes.hero_lava.double_trouble.soldier.duration%$ 초 동안 소환합니다.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_3"] = "용암 덩어리를 던져 %$heroes.hero_lava.double_trouble.s_damage[3]%$의 폭발 피해를 적에게 입히고, %$heroes.hero_lava.double_trouble.soldier.hp_max[3]%$의 생명력을 가진 마그마이트를 %$heroes.hero_lava.double_trouble.soldier.duration%$ 초 동안 소환합니다.",
["HERO_LAVA_DOUBLE_TROUBLE_SOLDIER_NAME"] = "마그마이트",
["HERO_LAVA_DOUBLE_TROUBLE_TITLE"] = "심각한 골칫거리",
["HERO_LAVA_HOTHEADED_DESCRIPTION_1"] = "크라토아가 부활할 때, 근처의 타워에 %$heroes.hero_lava.hotheaded.s_damage_factors[1]%$%의 피해 버프를 %$heroes.hero_lava.hotheaded.durations[1]%$ 초 동안 부여합니다.",
["HERO_LAVA_HOTHEADED_DESCRIPTION_2"] = "크라토아가 부활할 때, 근처의 타워에 %$heroes.hero_lava.hotheaded.s_damage_factors[2]%$%의 피해 버프를 %$heroes.hero_lava.hotheaded.durations[2]%$ 초 동안 부여합니다.",
["HERO_LAVA_HOTHEADED_DESCRIPTION_3"] = "크라토아가 부활할 때, 근처의 타워에 %$heroes.hero_lava.hotheaded.s_damage_factors[3]%$%의 피해 버프를 %$heroes.hero_lava.hotheaded.durations[3]%$ 초 동안 부여합니다.",
["HERO_LAVA_HOTHEADED_TITLE"] = "성급한 성격",
["HERO_LAVA_NAME"] = "크라토아",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_1"] = "적을 반복적으로 때려 %$heroes.hero_lava.temper_tantrum.s_damage_min[1]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[1]%$의 물리 피해를 입히고, %$heroes.hero_lava.temper_tantrum.duration[1]%$ 초 동안 기절시킵니다.",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_2"] = "적을 반복적으로 때려 %$heroes.hero_lava.temper_tantrum.s_damage_min[2]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[2]%$의 물리 피해를 입히고, %$heroes.hero_lava.temper_tantrum.duration[2]%$ 초 동안 기절시킵니다.",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_3"] = "적을 반복적으로 때려 %$heroes.hero_lava.temper_tantrum.s_damage_min[3]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[3]%$의 물리 피해를 입히고, %$heroes.hero_lava.temper_tantrum.duration[3]%$ 초 동안 기절시킵니다.",
["HERO_LAVA_TEMPER_TANTRUM_TITLE"] = "성질 부리기",
["HERO_LAVA_ULTIMATE_DESCRIPTION_1"] = "길에 %$heroes.hero_lava.ultimate.fireball_count[2]%$ 개의 용암 폭발을 던져, 각각 %$heroes.hero_lava.ultimate.bullet.s_damage[2]%$의 무속성 피해를 입히고, %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ 초 동안 적을 불태웁니다.",
["HERO_LAVA_ULTIMATE_DESCRIPTION_2"] = "길에 %$heroes.hero_lava.ultimate.fireball_count[3]%$ 개의 용암 폭발을 던져, 각각 %$heroes.hero_lava.ultimate.bullet.s_damage[3]%$의 무속성 피해를 입히고, %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ 초 동안 적을 불태웁니다.",
["HERO_LAVA_ULTIMATE_DESCRIPTION_3"] = "길에 %$heroes.hero_lava.ultimate.fireball_count[4]%$ 개의 용암 폭발을 던져, 각각 %$heroes.hero_lava.ultimate.bullet.s_damage[4]%$의 무속성 피해를 입히고, %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ 초 동안 적을 불태웁니다.",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "길에 용암 폭발을 던져, 땅을 불태웁니다.",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_NAME"] = "분노 폭발",
["HERO_LAVA_ULTIMATE_TITLE"] = "분노 폭발",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_1"] = "적에게 용암을 뿌려 %$heroes.hero_lava.wild_eruption.duration[1]%$ 초 동안 적을 불태우고, 매 초마다 %$heroes.hero_lava.wild_eruption.s_damage[1]%$의 무속성 피해를 입힙니다.",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_2"] = "적에게 용암을 뿌려 %$heroes.hero_lava.wild_eruption.duration[2]%$ 초 동안 적을 불태우고, 매 초마다 %$heroes.hero_lava.wild_eruption.s_damage[2]%$ 무속성 피해를 입힙니다.",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_3"] = "적에게 용암을 뿌려 %$heroes.hero_lava.wild_eruption.duration[3]%$ 초 동안 적을 불태우고, 매 초마다 %$heroes.hero_lava.wild_eruption.s_damage[3]%$의 무속성 피해를 입힙니다.",
["HERO_LAVA_WILD_ERUPTION_TITLE"] = "격렬한 분출",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_1"] = "%$heroes.hero_lumenir.ultimate.soldier_count[1]%$ 명의 빛의 전사를 소환하여 근처의 적을 잠시 기절시키고 %$heroes.hero_lumenir.ultimate.damage_min[1]%$-%$heroes.hero_lumenir.ultimate.damage_max[1]%$의 무속성 피해를 줍니다.",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_2"] = "%$heroes.hero_lumenir.ultimate.soldier_count[2]%$ 명의 빛의 전사를 소환하여 근처의 적을 잠시 기절시키고 %$heroes.hero_lumenir.ultimate.damage_min[2]%$-%$heroes.hero_lumenir.ultimate.damage_max[2]%$의 무속성 피해를 줍니다.",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_3"] = "%$heroes.hero_lumenir.ultimate.soldier_count[3]%$ 명의 빛의 전사를 소환하여 근처의 적을 잠시 기절시키고 %$heroes.hero_lumenir.ultimate.damage_min[3]%$-%$heroes.hero_lumenir.ultimate.damage_max[3]%$의 무속성 피해를 줍니다.",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "적들과 맞서 싸우는 신성한 전사들을 소환합니다.",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_NAME"] = "승리의 부름",
["HERO_LUMENIR_ARROW_STORM_TITLE"] = "승리의 부름",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_1"] = "주변의 가장 강력한 적에게 신성한 빛의 검을 내리꽂아 %$heroes.hero_lumenir.celestial_judgement.damage[1]%$의 무속성 피해를 주고 %$heroes.hero_lumenir.celestial_judgement.stun_duration[1]%$ 초 동안 기절시킵니다.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_2"] = "주변의 가장 강력한 적에게 신성한 빛의 검을 내리꽂아 %$heroes.hero_lumenir.celestial_judgement.damage[2]%$의 무속성 피해를 주고 %$heroes.hero_lumenir.celestial_judgement.stun_duration[2]%$ 초 동안 기절시킵니다.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_3"] = "주변의 가장 강력한 적에게 신성한 빛의 검을 내리꽂아 %$heroes.hero_lumenir.celestial_judgement.damage[3]%$의 무속성 피해를 주고 %$heroes.hero_lumenir.celestial_judgement.stun_duration[3]%$ 초 동안 기절시킵니다.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_TITLE"] = "천상의 심판",
["HERO_LUMENIR_CLASS"] = "빛의 인도자",
["HERO_LUMENIR_DESC"] = "왕국 사이를 오가는 루메니어는 정의와 결의의 상징입니다. 그녀는 리니리아 성기사들의 존경을 받는 전설적인 빛의 인도자이며, 그녀는 그들에게 축복을 내려 악에 맞서 싸우는 데 도움이 되는 강력한 힘을 부여합니다.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_1"] = "길을 따라 이동하며 적에게 피해를 주는 신성한 빛의 구체 %$heroes.hero_lumenir.fire_balls.flames_count[1]%$ 개를 내뱉습니다. 각 구체는 지나가는 적마다 %$heroes.hero_lumenir.fire_balls.flame_damage_min[1]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[1]%$의 무속성 피해를 입힙니다.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_2"] = "길을 따라 이동하며 적에게 피해를 주는 신성한 빛의 구체 %$heroes.hero_lumenir.fire_balls.flames_count[2]%$ 개를 내뱉습니다. 각 구체는 지나가는 적마다 %$heroes.hero_lumenir.fire_balls.flame_damage_min[2]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[2]%$의 무속성 피해를 입힙니다.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_3"] = "길을 따라 이동하며 적에게 피해를 주는 신성한 빛의 구체 %$heroes.hero_lumenir.fire_balls.flames_count[3]%$ 개를 내뱉습니다. 각 구체는 지나가는 적마다 %$heroes.hero_lumenir.fire_balls.flame_damage_min[3]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[3]%$의 무속성 피해를 입힙니다.",
["HERO_LUMENIR_FIRE_BALLS_TITLE"] = "빛의 파동",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_1"] = "다른 영웅을 %$heroes.hero_lumenir.mini_dragon.dragon.duration[1]%$ 초 동안 따르는 작은 빛의 용을 소환합니다. 각 용은 공격마다 %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[1]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[1]%$의 물리 피해를 줍니다.",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_2"] = "다른 영웅을 %$heroes.hero_lumenir.mini_dragon.dragon.duration[2]%$ 초 동안 따르는 작은 빛의 용을 소환합니다. 각 용은 공격마다 %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[2]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[2]%$의 물리 피해를 줍니다.",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_3"] = "다른 영웅을 %$heroes.hero_lumenir.mini_dragon.dragon.duration[3]%$ 초 동안 따르는 작은 빛의 용을 소환합니다. 각 용은 공격마다 %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[3]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[3]%$의 물리 피해를 줍니다.",
["HERO_LUMENIR_MINI_DRAGON_TITLE"] = "빛의 동반자",
["HERO_LUMENIR_NAME"] = "루메니어",
["HERO_LUMENIR_SHIELD_DESCRIPTION_1"] = "동맹 유닛에게 %$heroes.hero_lumenir.shield.armor[1]%$%의 방어막을 부여하여 %$heroes.hero_lumenir.shield.spiked_armor[1]%$%의 피해를 적에게 반사합니다.",
["HERO_LUMENIR_SHIELD_DESCRIPTION_2"] = "동맹 유닛에게 %$heroes.hero_lumenir.shield.armor[2]%$%의 방어막을 부여하여 %$heroes.hero_lumenir.shield.spiked_armor[2]%$%의 피해를 적에게 반사합니다.",
["HERO_LUMENIR_SHIELD_DESCRIPTION_3"] = "동맹 유닛에게 %$heroes.hero_lumenir.shield.armor[3]%$%의 방어막을 부여하여 %$heroes.hero_lumenir.shield.spiked_armor[3]%$%의 피해를 적에게 반사합니다.",
["HERO_LUMENIR_SHIELD_TITLE"] = "응징의 축복",
["HERO_MECHA_CLASS"] = "기동성 위험물",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_1"] = "고블린 비행선을 호출하여 목표 지역 주변의 적들을 폭격합니다. 공격당 %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[2]%$의 무속성 범위 피해를 줍니다.",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_2"] = "고블린 비행선을 호출하여 목표 지역 주변의 적들을 폭격합니다. 공격당 %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[3]%$의 무속성 범위 피해를 줍니다.",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_3"] = "고블린 비행선을 호출하여 목표 지역 주변의 적들을 폭격합니다. 공격당 %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[4]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[4]%$의 무속성 범위 피해를 줍니다.",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_DESCRIPTION"] = "지역 내의 적을 폭격하는 비행선을 소환합니다.",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_NAME"] = "공중 폭격",
["HERO_MECHA_DEATH_FROM_ABOVE_TITLE"] = "공중 폭격",
["HERO_MECHA_DESC"] = "두 정신나간 고블린 발명가들에 의해 고안되어 훔친 드워프 기술의 기반 위에 만들어진, 오나그로는 궁극의 그린스킨 전쟁기계이자 암흑 군의 적들에게는 두려운 광경입니다.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_1"] = "드론 %$heroes.hero_mecha.goblidrones.units%$ 대를 호출해 %$heroes.hero_mecha.goblidrones.drone.duration[1]%$ 초 동안 적을 공격하고, 공격당 %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[1]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[1]%$의 물리 피해를 줍니다.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_2"] = "드론 %$heroes.hero_mecha.goblidrones.units%$ 대를 호출해 %$heroes.hero_mecha.goblidrones.drone.duration[2]%$ 초 동안 적을 공격하고, 공격당 %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[2]%$의 물리 피해를 줍니다.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_3"] = "드론 %$heroes.hero_mecha.goblidrones.units%$ 대를 호출해 %$heroes.hero_mecha.goblidrones.drone.duration[3]%$ 초 동안 적을 공격하고, 공격당 %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[3]%$의 물리 피해를 줍니다.",
["HERO_MECHA_GOBLIDRONES_TITLE"] = "고블리드론",
["HERO_MECHA_MINE_DROP_DESCRIPTION_1"] = "멈춰 서 있을 때, 주기적으로 최대 %$heroes.hero_mecha.mine_drop.max_mines[1]%$ 개의 폭발성 지뢰를 설치합니다. 지뢰는 각각 %$heroes.hero_mecha.mine_drop.damage_min[1]%$-%$heroes.hero_mecha.mine_drop.damage_max[1]%$의 폭발 피해를 줍니다.",
["HERO_MECHA_MINE_DROP_DESCRIPTION_2"] = "멈춰 서 있을 때, 주기적으로 최대 %$heroes.hero_mecha.mine_drop.max_mines[2]%$ 개의 폭발성 지뢰를 설치합니다. 지뢰는 각각 %$heroes.hero_mecha.mine_drop.damage_min[2]%$-%$heroes.hero_mecha.mine_drop.damage_max[2]%$의 폭발 피해를 줍니다.",
["HERO_MECHA_MINE_DROP_DESCRIPTION_3"] = "멈춰 서 있을 때, 주기적으로 최대 %$heroes.hero_mecha.mine_drop.max_mines[3]%$ 개의 폭발성 지뢰를 설치합니다. 지뢰는 각각 %$heroes.hero_mecha.mine_drop.damage_min[3]%$-%$heroes.hero_mecha.mine_drop.damage_max[3]%$의 폭발 피해를 줍니다.",
["HERO_MECHA_MINE_DROP_TITLE"] = "지뢰 투하",
["HERO_MECHA_NAME"] = "오나그로",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_1"] = "땅을 내려쳐 잠시 동안 주변의 적을 스턴시키고 %$heroes.hero_mecha.power_slam.s_damage[1]%$의 물리 피해를 줍니다.",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_2"] = "땅을 내려쳐 잠시 동안 주변의 적을 스턴시키고 %$heroes.hero_mecha.power_slam.s_damage[2]%$의 물리 피해를 줍니다.",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_3"] = "땅을 내려쳐 잠시 동안 주변의 적을 스턴시키고 %$heroes.hero_mecha.power_slam.s_damage[3]%$의 물리 피해를 줍니다.",
["HERO_MECHA_POWER_SLAM_TITLE"] = "파워 슬램",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_1"] = "길 위에 타르를 쏟는 폭탄을 던져 %$heroes.hero_mecha.tar_bomb.duration[1]%$ 초 동안 적의 속도를 %$heroes.hero_mecha.tar_bomb.slow_factor%$% 늦춥니다.",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_2"] = "길 위에 타르를 쏟는 폭탄을 던져 %$heroes.hero_mecha.tar_bomb.duration[2]%$ 초 동안 적의 속도를 %$heroes.hero_mecha.tar_bomb.slow_factor%$% 늦춥니다.",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_3"] = "길 위에 타르를 쏟는 폭탄을 던져 %$heroes.hero_mecha.tar_bomb.duration[3]%$ 초 동안 적의 속도를 %$heroes.hero_mecha.tar_bomb.slow_factor%$% 늦춥니다.",
["HERO_MECHA_TAR_BOMB_TITLE"] = "타르 폭탄",
["HERO_MUYRN_CLASS"] = "숲의 수호자",
["HERO_MUYRN_DESC"] = "어린 외모와 달리, 트릭스터 니루는 자연의 힘을 통해 수백 년 동안 숲을 보호해 왔습니다. 그녀는 자신의 고향을 위협하는 침략자들의 점점 커지는 공세를 막기 위해 동맹에 가입했습니다.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_1"] = "지역 내 모든 적에게 주문을 걸어 %$heroes.hero_muyrn.faery_dust.duration[1]%$ 초 동안 그들의 공격력을 %$heroes.hero_muyrn.faery_dust.s_damage_factor[1]%$% 감소시킵니다.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_2"] = "지역 내 모든 적에게 주문을 걸어 %$heroes.hero_muyrn.faery_dust.duration[2]%$ 초 동안 그들의 공격력을 %$heroes.hero_muyrn.faery_dust.s_damage_factor[2]%$% 감소시킵니다.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_3"] = "지역 내 모든 적에게 주문을 걸어 %$heroes.hero_muyrn.faery_dust.duration[3]%$ 초 동안 그들의 공격력을 %$heroes.hero_muyrn.faery_dust.s_damage_factor[3]%$% 감소시킵니다.",
["HERO_MUYRN_FAERY_DUST_TITLE"] = "쇠약의 주문",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_1"] = "전투 중에 니루는 자신 주위에 나뭇잎 보호막을 만듭니다. 보호막은 초당 %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[1]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[1]%$의 마법 피해를 주고, %$heroes.hero_muyrn.leaf_whirlwind.duration[1]%$ 초 동안 니루를 치유합니다.",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_2"] = "전투 중에 니루는 자신 주위에 나뭇잎 보호막을 만듭니다. 보호막은 초당 %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[2]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[2]%$의 마법 피해를 주고, %$heroes.hero_muyrn.leaf_whirlwind.duration[2]%$ 초 동안 니루를 치유합니다.",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_3"] = "전투 중에 니루는 자신 주위에 나뭇잎 보호막을 만듭니다. 보호막은 초당 %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[3]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[3]%$의 마법 피해를 주고, %$heroes.hero_muyrn.leaf_whirlwind.duration[3]%$ 초 동안 니루를 치유합니다.",
["HERO_MUYRN_LEAF_WHIRLWIND_TITLE"] = "나뭇잎 회오리바람",
["HERO_MUYRN_NAME"] = "니루",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_1"] = "%$heroes.hero_muyrn.ultimate.duration[2]%$ 초 동안 지역에 뿌리를 생성하여 적을 느려지게 하고 초당 %$heroes.hero_muyrn.ultimate.s_damage_min[2]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[2]%$의 무속성 피해를 줍니다.",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_2"] = "%$heroes.hero_muyrn.ultimate.duration[3]%$ 초 동안 지역에 뿌리를 생성하여 적을 느려지게 하고 초당 %$heroes.hero_muyrn.ultimate.s_damage_min[3]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[3]%$의 무속성 피해를 줍니다.",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_3"] = "%$heroes.hero_muyrn.ultimate.duration[4]%$ 초 동안 지역에 뿌리를 생성하여 적을 느려지게 하고 초당 %$heroes.hero_muyrn.ultimate.s_damage_min[4]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[4]%$의 무속성 피해를 줍니다.",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_DESCRIPTION"] = "적에게 피해를 주고 속도를 늦추는 뿌리를 생성합니다.",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_NAME"] = "뿌리 방어",
["HERO_MUYRN_ROOT_DEFENDER_TITLE"] = "뿌리 방어",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_1"] = "%$heroes.hero_muyrn.sentinel_wisps.wisp.duration[1]%$ 초 동안 니루를 따르는 위스프 %$heroes.hero_muyrn.sentinel_wisps.max_summons[1]%$ 마리를 소환합니다. 위스프는 %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[1]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[1]%$의 마법 피해를 줍니다.",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_2"] = "%$heroes.hero_muyrn.sentinel_wisps.wisp.duration[2]%$ 초 동안 니루를 따르는 위스프 %$heroes.hero_muyrn.sentinel_wisps.max_summons[2]%$ 마리를 소환합니다. 위스프는 %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[2]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[2]%$의 마법 피해를 줍니다.",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_3"] = "%$heroes.hero_muyrn.sentinel_wisps.wisp.duration[3]%$ 초 동안 니루를 따르는 위스프 %$heroes.hero_muyrn.sentinel_wisps.max_summons[3]%$ 마리를 소환합니다. 위스프는 %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[3]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[3]%$의 마법 피해를 줍니다.",
["HERO_MUYRN_SENTINEL_WISPS_TITLE"] = "센티넬 위스프",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_1"] = "강력한 녹색 에너지탄을 발사해 %$heroes.hero_muyrn.verdant_blast.s_damage[1]%$의 마법 피해를 입힙니다.",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_2"] = "강력한 녹색 에너지탄을 발사해 %$heroes.hero_muyrn.verdant_blast.s_damage[2]%$의 마법 피해를 입힙니다.",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_3"] = "강력한 녹색 에너지탄을 발사해 %$heroes.hero_muyrn.verdant_blast.s_damage[3]%$의 마법 피해를 입힙니다.",
["HERO_MUYRN_VERDANT_BLAST_TITLE"] = "신록의 폭발",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_1"] = "검으로 적을 잔인하게 베어 %$heroes.hero_raelyn.brutal_slash.s_damage[1]%$의 무속성 피해를 입힙니다.",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_2"] = "검으로 적을 잔인하게 베어 %$heroes.hero_raelyn.brutal_slash.s_damage[2]%$의 무속성 피해를 입힙니다.",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_3"] = "검으로 적을 잔인하게 베어 %$heroes.hero_raelyn.brutal_slash.s_damage[3]%$의 무속성 피해를 입힙니다.",
["HERO_RAELYN_BRUTAL_SLASH_TITLE"] = "잔인한 참격",
["HERO_RAELYN_CLASS"] = "암흑 중위",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_1"] = "%$heroes.hero_raelyn.ultimate.entity.hp_max[2]%$의 생명력을 가진 암흑 기사를 소환하여 %$heroes.hero_raelyn.ultimate.entity.damage_min[2]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[2]%$의 무속성 피해를 입힙니다.",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_2"] = "암흑 기사는 %$heroes.hero_raelyn.ultimate.entity.hp_max[3]%$의 생명력을 가지며 %$heroes.hero_raelyn.ultimate.entity.damage_min[3]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[3]%$의 무속성 피해를 입힙니다.",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_3"] = "암흑 기사는 %$heroes.hero_raelyn.ultimate.entity.hp_max[4]%$의 생명력을 가지며 %$heroes.hero_raelyn.ultimate.entity.damage_min[4]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[4]%$의 무속성 피해를 입힙니다.",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_DESCRIPTION"] = "전장에 암흑 기사를 소환합니다.",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_NAME"] = "지휘 명령",
["HERO_RAELYN_COMMAND_ORDERS_TITLE"] = "지휘 명령",
["HERO_RAELYN_DESC"] = "위압적인 레일린은 최전방에서 암흑 기사들을 이끕니다. 그녀의 잔혹함과 집요함로, 베즈난의 인정을 받고 리니리아인들의 두려움 샀습니다. 언제나 훌륭한 전투를 할 준비가 된 그녀는 암흑 마법사의 대열에 합류한 최초의 자원자였습니다.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_1"] = "주변의 적을 %$heroes.hero_raelyn.inspire_fear.stun_duration[1]%$ 초 동안 기절시키고 %$heroes.hero_raelyn.inspire_fear.damage_duration[1]%$ 초 동안 그들의 공격력을 %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[1]%$% 감소시킵니다.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_2"] = "주변의 적을 %$heroes.hero_raelyn.inspire_fear.stun_duration[2]%$ 초 동안 기절시키고 %$heroes.hero_raelyn.inspire_fear.damage_duration[2]%$ 초 동안 그들의 공격력을 %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[2]%$% 감소시킵니다.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_3"] = "주변의 적을 %$heroes.hero_raelyn.inspire_fear.stun_duration[3]%$ 초 동안 기절시키고 %$heroes.hero_raelyn.inspire_fear.damage_duration[3]%$ 초 동안 그들의 공격력을 %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[3]%$% 감소시킵니다.",
["HERO_RAELYN_INSPIRE_FEAR_TITLE"] = "공포 조장",
["HERO_RAELYN_NAME"] = "레일린",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_1"] = "%$heroes.hero_raelyn.onslaught.duration[1]%$ 초 동안 레일린은 더 빠르게 공격하고, 주 대상 주변에 공격력의 %$heroes.hero_raelyn.onslaught.damage_factor[1]%$%에 해당하는 범위 피해를 줍니다.",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_2"] = "%$heroes.hero_raelyn.onslaught.duration[2]%$ 초 동안 레일린은 더 빠르게 공격하고, 주 대상 주변에 공격력의 %$heroes.hero_raelyn.onslaught.damage_factor[2]%$%에 해당하는 범위 피해를 줍니다.",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_3"] = "%$heroes.hero_raelyn.onslaught.duration[3]%$ 초 동안 레일린은 더 빠르게 공격하고, 주 대상 주변에 공격력의 %$heroes.hero_raelyn.onslaught.damage_factor[3]%$%에 해당하는 범위 피해를 줍니다.",
["HERO_RAELYN_ONSLAUGHT_TITLE"] = "맹공",
["HERO_RAELYN_ULTIMATE_ENTITY_NAME"] = "암흑 기사",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_1"] = "전투 중에 레일린은 그녀 주변의 적의 수에 기반한 보호막을 생성합니다. (최대 %$heroes.hero_raelyn.unbreakable.max_targets%$ 명의 적 각각에 대해 그녀의 총 생명력의 %$heroes.hero_raelyn.unbreakable.shield_per_enemy[1]%$%).",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_2"] = "전투 중에 레일린은 그녀 주변의 적의 수에 기반한 보호막을 생성합니다. (최대 %$heroes.hero_raelyn.unbreakable.max_targets%$ 명의 적 각각에 대해 그녀의 총 생명력의 %$heroes.hero_raelyn.unbreakable.shield_per_enemy[2]%$%)",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_3"] = "전투 중에 레일린은 그녀 주변의 적의 수에 기반한 보호막을 생성합니다. (최대 %$heroes.hero_raelyn.unbreakable.max_targets%$ 명의 적 각각에 대해 그녀의 총 생명력의 %$heroes.hero_raelyn.unbreakable.shield_per_enemy[3]%$%)",
["HERO_RAELYN_UNBREAKABLE_TITLE"] = "불굴의 의지",
["HERO_ROBOT_CLASS"] = "공성 골렘",
["HERO_ROBOT_DESC"] = "암흑 군대 대장장이들은 워헤드라고 적절하게 이름 붙인 전투용 오토마톤을 만들어내는 성과를 거두었습니다. 감정에 구애받지 않으며 불같은 엔진에 힘입은 워헤드는 피아 구분없이 전투에 뛰어듭니다.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_1"] = "화염 폭발을 생성하여 적에게 %$heroes.hero_robot.explode.damage_min[1]%$-%$heroes.hero_robot.explode.damage_max[1]%$의 폭발 피해를 주고 %$heroes.hero_robot.explode.burning_duration%$ 초 동안 불태웁니다. 화상은 초당 %$heroes.hero_robot.explode.s_burning_damage[1]%$의 피해를 줍니다.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_2"] = "화염 폭발을 생성하여 적에게 %$heroes.hero_robot.explode.damage_min[2]%$-%$heroes.hero_robot.explode.damage_max[2]%$의 폭발 피해를 주고 %$heroes.hero_robot.explode.burning_duration%$ 초 동안 불태웁니다. 화상은 초당 %$heroes.hero_robot.explode.s_burning_damage[2]%$의 피해를 줍니다.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_3"] = "화염 폭발을 생성하여 적에게 %$heroes.hero_robot.explode.damage_min[3]%$-%$heroes.hero_robot.explode.damage_max[3]%$의 폭발 피해를 주고 %$heroes.hero_robot.explode.burning_duration%$ 초 동안 불태웁니다. 화상은 초당 %$heroes.hero_robot.explode.s_burning_damage[3]%$의 피해를 줍니다.",
["HERO_ROBOT_EXPLODE_TITLE"] = "제물",
["HERO_ROBOT_FIRE_DESCRIPTION_1"] = "불타는 불씨가 가득한 대포를 발사하여 %$heroes.hero_robot.fire.damage_min[1]%$-%$heroes.hero_robot.fire.damage_max[1]%$의 물리 피해를 주고, 적을 %$heroes.hero_robot.fire.s_slow_duration[1]%$ 초 동안 느리게 합니다.",
["HERO_ROBOT_FIRE_DESCRIPTION_2"] = "불타는 불씨가 가득한 대포를 발사하여 %$heroes.hero_robot.fire.damage_min[2]%$-%$heroes.hero_robot.fire.damage_max[2]%$의 물리 피해를 주고, 적을 %$heroes.hero_robot.fire.s_slow_duration[1]%$ 초 동안 느리게 합니다.",
["HERO_ROBOT_FIRE_DESCRIPTION_3"] = "불타는 불씨가 가득한 대포를 발사하여 %$heroes.hero_robot.fire.damage_min[3]%$-%$heroes.hero_robot.fire.damage_max[3]%$의 물리 피해를 주고, 적을 %$heroes.hero_robot.fire.s_slow_duration[1]%$ 초 동안 느리게 합니다.",
["HERO_ROBOT_FIRE_TITLE"] = "연막",
["HERO_ROBOT_JUMP_DESCRIPTION_1"] = "적 위로 점프해 %$heroes.hero_robot.jump.stun_duration[1]%$ 초 동안 기철시키고, 범위 내에서 %$heroes.hero_robot.jump.s_damage[1]%$의 물리 피해를 줍니다.",
["HERO_ROBOT_JUMP_DESCRIPTION_2"] = "적 위로 점프해 %$heroes.hero_robot.jump.stun_duration[2]%$ 초 동안 기절시키고, 범위 내에서 %$heroes.hero_robot.jump.s_damage[2]%$의 물리적 피해를 줍니다.",
["HERO_ROBOT_JUMP_DESCRIPTION_3"] = "적 위로 점프해 %$heroes.hero_robot.jump.stun_duration[3]%$ 초 동안 기절시키고, 범위 내에서 %$heroes.hero_robot.jump.s_damage[3]%$의 물리 피해를 줍니다.",
["HERO_ROBOT_JUMP_TITLE"] = "딥 임팩트",
["HERO_ROBOT_NAME"] = "워헤드",
["HERO_ROBOT_TRAIN_DESCRIPTION_1"] = "길을 따라 이동하며 적에게 %$heroes.hero_robot.ultimate.s_damage[2]%$의 피해를 주고 %$heroes.hero_robot.ultimate.burning_duration%$초 동안 화상을 입히는 전쟁 왜건을 소환합니다. 화상은 초당 %$heroes.hero_robot.ultimate.s_burning_damage%$의 피해를 줍니다.",
["HERO_ROBOT_TRAIN_DESCRIPTION_2"] = "길을 따라 이동하며 적에게 %$heroes.hero_robot.ultimate.s_damage[3]%$의 피해를 주고 %$heroes.hero_robot.ultimate.burning_duration%$초 동안 화상을 입히는 전쟁 왜건을 소환합니다. 화상은 초당 %$heroes.hero_robot.ultimate.s_burning_damage%$의 피해를 줍니다.",
["HERO_ROBOT_TRAIN_DESCRIPTION_3"] = "길을 따라 이동하며 적에게 %$heroes.hero_robot.ultimate.s_damage[4]%$의 피해를 주고 %$heroes.hero_robot.ultimate.burning_duration%$초 동안 화상을 입히는 전쟁 왜건을 소환합니다. 화상은 초당 %$heroes.hero_robot.ultimate.s_burning_damage%$의 피해를 줍니다.",
["HERO_ROBOT_TRAIN_MENUBOTTOM_DESCRIPTION"] = "적을 짓밟는 전쟁 왜건을 소환합니다.",
["HERO_ROBOT_TRAIN_MENUBOTTOM_NAME"] = "모터 헤드",
["HERO_ROBOT_TRAIN_TITLE"] = "모터 헤드",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_1"] = "생명력이 %$heroes.hero_robot.uppercut.s_life_threshold[1]%$% 미만인 적을 공격하여 즉시 처치합니다.",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_2"] = "생명력이 %$heroes.hero_robot.uppercut.s_life_threshold[2]%$% 미만인 적을 공격하여 즉시 처치합니다.",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_3"] = "생명력이 %$heroes.hero_robot.uppercut.s_life_threshold[3]%$% 미만인 적을 공격하여 즉시 처치합니다.",
["HERO_ROBOT_UPPERCUT_TITLE"] = "아이언 어퍼컷",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "이 영웅은 거대한 위협 캠페인에 포함되어 있습니다.",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "이 영웅은 ‘오공의 여정’ 캠페인에 포함됩니다.",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "거대한 위협 캠페인",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "오공의 여정 캠페인",
["HERO_ROOM_EQUIPPED_HEROES"] = "선택된 영웅들",
["HERO_ROOM_GET_DLC"] = "가져가세요",
["HERO_ROOM_LABEL_ROSTER_THUMB_NEW"] = "신규!",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_1"] = "테리엔의 마법 환영을 소환합니다. 적을 공격하여 %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[1]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[1]%$의 마법 피해를 입힙니다.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_2"] = "테리엔의 마법 환영을 소환합니다. 적을 공격하여 %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[2]%$의 마법 피해를 입힙니다.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_3"] = "테리엔의 마법 환영을 소환합니다. 적을 공격하여 %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[3]%$의 마법 피해를 입힙니다.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_ENTITY_NAME"] = "천상의 환영",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_TITLE"] = "천상의 환영",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_1"] = "동맹 유닛을 보호하여 최대 %$heroes.hero_space_elf.black_aegis.shield_base[1]%$의 피해를 방지합니다. 방패는 잠시 후 폭발하여 영역 내에 %$heroes.hero_space_elf.black_aegis.explosion_damage[1]%$의 마법 피해를 줍니다.",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_2"] = "동맹 유닛을 보호하여 최대 %$heroes.hero_space_elf.black_aegis.shield_base[2]%$의 피해를 방지합니다. 이제 폭발적인 방패는 영역 내에 %$heroes.hero_space_elf.black_aegis.explosion_damage[2]%$의 마법 피해를 줍니다.",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_3"] = "동맹 유닛을 보호하여 최대 %$heroes.hero_space_elf.black_aegis.shield_base[3]%$의 피해를 방지합니다. 이제 폭발적인 방패는 영역 내에 %$heroes.hero_space_elf.black_aegis.explosion_damage[3]%$의 마법 피해를 줍니다.",
["HERO_SPACE_ELF_BLACK_AEGIS_TITLE"] = "블랙 이지스",
["HERO_SPACE_ELF_CLASS"] = "공허술사",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_1"] = "%$heroes.hero_space_elf.ultimate.duration[2]%$ 초 동안 적군 그룹을 공허 속에 가두어 %$heroes.hero_space_elf.ultimate.damage[2]%$의 피해를 줍니다.",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_2"] = "%$heroes.hero_space_elf.ultimate.duration[3]%$ 초 동안 적군 그룹을 공허 속에 가두어 %$heroes.hero_space_elf.ultimate.damage[3]%$의 피해를 줍니다.",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_3"] = "%$heroes.hero_space_elf.ultimate.duration[4]%$ 초 동안 적군 그룹을 공허 속에 가두어 %$heroes.hero_space_elf.ultimate.damage[4]%$의 피해를 줍니다.",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_DESCRIPTION"] = "일정 지역의 적을 포획하여 피해를 줍니다.",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_NAME"] = "우주 감옥",
["HERO_SPACE_ELF_COSMIC_PRISON_TITLE"] = "우주 감옥",
["HERO_SPACE_ELF_DESC"] = "알려지지 않은 다른 세계의 세력에 개입했다는 이유로 동료들로부터 수년 동안 외면당한 공허술사, 테리엔은 이제 오버시어와 이 차원을 넘어선 모든 힘을 이해하기 위한 얼라이언스의 가장 큰 자산 중 하나입니다.",
["HERO_SPACE_ELF_NAME"] = "테리엔",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_1"] = "모든 타워 주변의 공간을 %$heroes.hero_space_elf.spatial_distortion.duration[1]%$ 초 동안 왜곡하여 사거리를 %$heroes.hero_space_elf.spatial_distortion.s_range_factor[1]%$% 증가시킵니다.",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_2"] = "모든 타워 주변의 공간을 %$heroes.hero_space_elf.spatial_distortion.duration[2]%$ 초 동안 왜곡하여 사거리를 %$heroes.hero_space_elf.spatial_distortion.s_range_factor[2]%$% 증가시킵니다.",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_3"] = "모든 타워 주변의 공간을 %$heroes.hero_space_elf.spatial_distortion.duration[3]%$ 초 동안 왜곡하여 사거리를 %$heroes.hero_space_elf.spatial_distortion.s_range_factor[3]%$% 증가시킵니다.",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_TITLE"] = "공간 왜곡",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_1"] = "%$heroes.hero_space_elf.void_rift.cracks_amount[1]%$ 개의 균열을 %$heroes.hero_space_elf.void_rift.duration[1]%$ 초 동안 만들어, 그 위에 서 있는 각 적에게 초당 %$heroes.hero_space_elf.void_rift.s_damage_min[1]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[1]%$의 피해를 줍니다.",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_2"] = "%$heroes.hero_space_elf.void_rift.cracks_amount[2]%$ 개의 균열을 %$heroes.hero_space_elf.void_rift.duration[2]%$ 초 동안 만들어, 그 위에 서 있는 각 적에게 초당 %$heroes.hero_space_elf.void_rift.s_damage_min[2]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[2]%$의 피해를 줍니다.",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_3"] = "%$heroes.hero_space_elf.void_rift.cracks_amount[3]%$ 개의 균열을 %$heroes.hero_space_elf.void_rift.duration[3]%$ 초 동안 만들어, 그 위에 서 있는 각 적에게 초당 %$heroes.hero_space_elf.void_rift.s_damage_min[3]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[3]%$의 피해를 줍니다.",
["HERO_SPACE_ELF_VOID_RIFT_TITLE"] = "공허의 균열",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_1"] = "%$heroes.hero_spider.ultimate.spawn_amount[2]%$ 마리의 거미를 소환하여 %$heroes.hero_spider.ultimate.spider.duration[2]%$ 초 동안 싸우게 하며, 적을 공격할 때 기절시킨다.",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_2"] = "%$heroes.hero_spider.ultimate.spawn_amount[3]%$ 마리의 거미를 소환하여 %$heroes.hero_spider.ultimate.spider.duration[3]%$ 초 동안 싸우게 하며, 적을 공격할 때 기절시킨다.",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_3"] = "%$heroes.hero_spider.ultimate.spawn_amount[4]%$ 마리의 거미를 소환하여 %$heroes.hero_spider.ultimate.spider.duration[4]%$ 초 동안 싸우게 하며, 적을 공격할 때 기절시킨다.",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_DESCRIPTION"] = "기절 거미 무리를 소환합니다.",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_NAME"] = "사냥꾼의 부름",
["HERO_SPIDER_ARACNID_SPAWNER_TITLE"] = "사냥꾼의 부름",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_1"] = "매 %$heroes.hero_spider.area_attack.cooldown[1]%$초마다 스파이더는 존재감을 드러내며, 주변 적을 %$heroes.hero_spider.area_attack.s_stun_time[1]%$초 동안 기절시킨다.",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_2"] = "매 %$heroes.hero_spider.area_attack.cooldown[2]%$초마다 스파이더는 존재감을 드러내며, 주변 적을 %$heroes.hero_spider.area_attack.s_stun_time[2]%$초 동안 기절시킨다.",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_3"] = "%$heroes.hero_spider.area_attack.cooldown[3]%$초마다 스파이더는 존재감을 드러내며, 주변의 적을 %$heroes.hero_spider.area_attack.s_stun_time[3]%$초 동안 기절시킨다.",
["HERO_SPIDER_AREA_ATTACK_TITLE"] = "압도적인 존재",
["HERO_SPIDER_DESC"] = "스파이더는 거미 여왕의 컬트를 말살하라는 임무를 받은 황혼 엘프 그룹의 마지막 생존자이다. 그림자 마법과 사냥 기술을 이용해, 리니에라에서 가장 치명적인 암살자 중 하나로 두려움의 대상이 되고 있다.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_1"] = "%$heroes.hero_spider.instakill_melee.cooldown[1]%$초마다 스파이더는 기절한 적을 처형할 수 있으며, 체력이 %$heroes.hero_spider.instakill_melee.life_threshold[1]%$ 이하일 때 가능하다.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_2"] = "%$heroes.hero_spider.instakill_melee.cooldown[2]%$초마다 스파이더는 기절한 적을 처형할 수 있으며, 해당 적의 체력이 %$heroes.hero_spider.instakill_melee.life_threshold[2]%$ 이하일 경우 가능하다.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_3"] = "%$heroes.hero_spider.instakill_melee.cooldown[3]%$초마다 스파이더가 기절한 적을 처형할 수 있으며, 체력이 %$heroes.hero_spider.instakill_melee.life_threshold[3]%$ 이하일 경우이다.",
["HERO_SPIDER_INSTAKILL_MELEE_TITLE"] = "죽음의 손아귀",
["HERO_SPIDER_NAME"] = "스파이더",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_1"] = "눈 깜짝할 사이에 스파이더가 가장 체력이 높은 적에게 순간이동하여 %$heroes.hero_spider.supreme_hunter.damage_min[1]%$-%$heroes.hero_spider.supreme_hunter.damage_max[1]%$의 피해를 입힌다.",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_2"] = "눈 깜짝할 사이에 스파이더가 가장 체력이 높은 적에게 순간이동하여 %$heroes.hero_spider.supreme_hunter.damage_min[2]%$-%$heroes.hero_spider.supreme_hunter.damage_max[2]%$의 피해를 입힌다.",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_3"] = "눈 깜짝할 사이에 스파이더가 가장 체력이 높은 적에게 순간이동하여 %$heroes.hero_spider.supreme_hunter.damage_min[3]%$-%$heroes.hero_spider.supreme_hunter.damage_max[3]%$의 피해를 입힌다.",
["HERO_SPIDER_SUPREME_HUNTER_TITLE"] = "그림자 밟기",
["HERO_SPIDER_TUNNELING_DESCRIPTION_1"] = "스파이더의 굴착이 이제 재등장 시 %$heroes.hero_spider.tunneling.damage_min[1]%$-%$heroes.hero_spider.tunneling.damage_max[1]%$의 피해를 입힙니다.",
["HERO_SPIDER_TUNNELING_DESCRIPTION_2"] = "스파이더의 굴착이 이제 재등장 시 %$heroes.hero_spider.tunneling.damage_min[2]%$-%$heroes.hero_spider.tunneling.damage_max[2]%$의 피해를 입힙니다.",
["HERO_SPIDER_TUNNELING_DESCRIPTION_3"] = "스파이더의 굴착이 이제 재등장 시 %$heroes.hero_spider.tunneling.damage_min[3]%$-%$heroes.hero_spider.tunneling.damage_max[3]%$의 피해를 입힙니다.",
["HERO_SPIDER_TUNNELING_TITLE"] = "터널 굴착",
["HERO_VENOM_CLASS"] = "더럽혀진 학살자",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_1"] = "적의 속도를 늦춘 뒤 날카로운 가시로 변하는 끈끈한 물질로 지역을 덮어 %$heroes.hero_venom.ultimate.s_damage[2]%$의 무속성 피해를 입힙니다.",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_2"] = "적의 속도를 늦춘 뒤 날카로운 가시로 변하는 끈끈한 물질로 지역을 덮어 %$heroes.hero_venom.ultimate.s_damage[3]%$의 무속성 피해를 입힙니다.",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_3"] = "적의 속도를 늦춘 뒤 날카로운 가시로 변하는 끈끈한 물질로 지역을 덮어 %$heroes.hero_venom.ultimate.s_damage[4]%$의 무속성 피해를 입힙니다.",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_DESCRIPTION"] = "경로에 점성 물질을 소환하여 적을 느리게 하고 피해를 줍니다.",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_NAME"] = "잠식하는 죽음",
["HERO_VENOM_CREEPING_DEATH_TITLE"] = "잠식하는 죽음",
["HERO_VENOM_DESC"] = "용병 그림슨은 이교도에 의해 흉물로 전락하는 것에 저항한 끝에 감옥에서 썩게 되었습니다. 고통스러운 과정을 통해 얻은 변신 능력을 사용하여 탈출한 그림슨은 복수를 위해 돌아올 것을 맹세했습니다.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_1"] = "그림슨은 %$heroes.hero_venom.eat_enemy.hp_trigger%$% 미만의 생명력을 가진 적을 삼켜, 과정에서 자신의 전체 생명력의 %$heroes.hero_venom.eat_enemy.regen[1]%$%를 회복합니다.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_2"] = "그림슨은 %$heroes.hero_venom.eat_enemy.hp_trigger%$% 미만의 생명력을 가진 적을 삼켜, 과정에서 자신의 전체 생명력의 %$heroes.hero_venom.eat_enemy.regen[2]%$%를 회복합니다.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_3"] = "그림슨은 %$heroes.hero_venom.eat_enemy.hp_trigger%$% 미만의 생명력을 가진 적을 삼켜, 과정에서 자신의 전체 생명력의 %$heroes.hero_venom.eat_enemy.regen[3]%$%를 회복합니다.",
["HERO_VENOM_EAT_ENEMY_TITLE"] = "새살 돋우기",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_1"] = "가시덩굴손을 펼쳐, 근처의 적에게 가시당 %$heroes.hero_venom.floor_spikes.s_damage[1]%$의 무속성 피해를 줍니다.",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_2"] = "가시덩굴손을 펼쳐, 근처의 적에게 가시당 %$heroes.hero_venom.floor_spikes.s_damage[2]%$의 무속성 피해를 줍니다.",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_3"] = "가시덩굴손을 펼쳐, 근처의 적에게 가시당 %$heroes.hero_venom.floor_spikes.s_damage[3]%$의 무속성 피해를 줍니다.",
["HERO_VENOM_FLOOR_SPIKES_TITLE"] = "치명적인 가시",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_1"] = "생명력이 %$heroes.hero_venom.inner_beast.trigger_hp%$% 이하로 떨어지면 그림슨이 완전히 변모하여 %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[1]%$%의 추가 피해를 얻고 %$heroes.hero_venom.inner_beast.duration%$ 초 동안 타격당 전체 생명력의 %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$%를 회복합니다.",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_2"] = "생명력이 %$heroes.hero_venom.inner_beast.trigger_hp%$% 이하로 떨어지면 그림슨이 완전히 변모하여 %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[2]%$%의 추가 피해를 얻고 %$heroes.hero_venom.inner_beast.duration%$ 초 동안 타격당 전체 생명력의 %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$%를 회복합니다.",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_3"] = "생명력이 %$heroes.hero_venom.inner_beast.trigger_hp%$% 이하로 떨어지면 그림슨이 완전히 변모하여 %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[3]%$%의 추가 피해를 얻고 %$heroes.hero_venom.inner_beast.duration%$ 초 동안 타격당 전체 생명력의 %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$%를 회복합니다.",
["HERO_VENOM_INNER_BEAST_TITLE"] = "내면의 야수",
["HERO_VENOM_NAME"] = "그림슨",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_1"] = "멀리 있는 적을 공격하여 %$heroes.hero_venom.ranged_tentacle.s_damage[1]%$의 물리 피해를 주며 %$heroes.hero_venom.ranged_tentacle.bleed_chance[1]%$%의 확률로 출혈을 유발합니다. 출혈은 %$heroes.hero_venom.ranged_tentacle.bleed_duration[1]%$ 초 동안 매 초 %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$의 피해를 줍니다.",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_2"] = "멀리 있는 적을 공격하여 %$heroes.hero_venom.ranged_tentacle.s_damage[2]%$의 물리 피해를 주며 %$heroes.hero_venom.ranged_tentacle.bleed_chance[2]%$%의 확률로 출혈을 유발합니다. 출혈은 %$heroes.hero_venom.ranged_tentacle.bleed_duration[2]%$ 초 동안 매 초 %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$의 피해를 줍니다.",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_3"] = "멀리 있는 적을 공격하여 %$heroes.hero_venom.ranged_tentacle.s_damage[3]%$의 물리 피해를 주며 %$heroes.hero_venom.ranged_tentacle.bleed_chance[3]%$%의 확률로 출혈을 유발합니다. 출혈은 %$heroes.hero_venom.ranged_tentacle.bleed_duration[3]%$ 초 동안 매 초 %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$의 피해를 줍니다.",
["HERO_VENOM_RANGED_TENTACLE_TITLE"] = "하트시커",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_1"] = "%$heroes.hero_vesper.ultimate.s_spread[2]%$ 개의 화살로 지역을 덮어, 적에게 각각 %$heroes.hero_vesper.ultimate.damage[2]%$의 물리 피해를 입힙니다.",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_2"] = "%$heroes.hero_vesper.ultimate.s_spread[3]%$ 개의 화살로 지역을 덮어, 적에게 각각 %$heroes.hero_vesper.ultimate.damage[3]%$의 물리 피해를 입힙니다.",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_3"] = "%$heroes.hero_vesper.ultimate.s_spread[4]%$ 개의 화살로 지역을 덮어, 적에게 각각 %$heroes.hero_vesper.ultimate.damage[4]%$의 물리 피해를 입힙니다.",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "화살로 지역을 덮어 적에게 피해를 입힙니다.",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_NAME"] = "화살폭풍",
["HERO_VESPER_ARROW_STORM_TITLE"] = "화살폭풍",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_1"] = "적을 %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[1]%$ 초 동안 기절시키는 화살을 발사하여, %$heroes.hero_vesper.arrow_to_the_knee.s_damage[1]%$의 물리 피해를 입힙니다.",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_2"] = "적을 %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[2]%$ 초 동안 기절시키는 화살을 발사하여, %$heroes.hero_vesper.arrow_to_the_knee.s_damage[2]%$의 물리 피해를 입힙니다.",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_3"] = "적을 %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[3]%$ 초 동안 기절시키는 화살을 발사하여, %$heroes.hero_vesper.arrow_to_the_knee.s_damage[3]%$의 물리 피해를 입힙니다.",
["HERO_VESPER_ARROW_TO_THE_KNEE_TITLE"] = "무릎에 화살",
["HERO_VESPER_CLASS"] = "왕실 대위",
["HERO_VESPER_DESC"] = "검과 활에 능숙한 베스퍼는 리니리아 군대의 사령관 자리를 차지했습니다. 리니리아가 함락되고 데나스 왕이 사라진 후, 그는 가능한 모든 군대를 모아 전 통치자를 되찾기 위해 십자군을 창설했습니다.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_1"] = "생명력이 %$heroes.hero_vesper.disengage.hp_to_trigger%$% 이하로 떨어지면 베스퍼는 뒤로 점프해서 다음 근접 공격을 회피한 후, 근처의 적에게 각각 %$heroes.hero_vesper.disengage.s_damage[1]%$의 물리 피해를 입히는 세 개의 화살을 쏩니다.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_2"] = "생명력이 %$heroes.hero_vesper.disengage.hp_to_trigger%$% 이하로 떨어지면 베스퍼는 뒤로 점프해서 다음 근접 공격을 회피한 후, 근처의 적에게 각각 %$heroes.hero_vesper.disengage.s_damage[2]%$의 물리 피해를 입히는 세 개의 화살을 쏩니다.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_3"] = "생명력이 %$heroes.hero_vesper.disengage.hp_to_trigger%$% 이하로 떨어지면 베스퍼는 뒤로 점프해서 다음 근접 공격을 회피한 후, 근처의 적에게 각각 %$heroes.hero_vesper.disengage.s_damage[3]%$의 물리 피해를 입히는 세 개의 화살을 쏩니다.",
["HERO_VESPER_DISENGAGE_TITLE"] = "전술적 후퇴",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_1"] = "적을 세 번 공격하여 %$heroes.hero_vesper.martial_flourish.s_damage[1]%$의 물리 피해를 입힙니다.",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_2"] = "적을 세 번 공격하여 %$heroes.hero_vesper.martial_flourish.s_damage[2]%$의 물리 피해를 입힙니다.",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_3"] = "적을 세 번 공격하여 %$heroes.hero_vesper.martial_flourish.s_damage[3]%$의 물리 피해를 입힙니다.",
["HERO_VESPER_MARTIAL_FLOURISH_TITLE"] = "화려한 무술",
["HERO_VESPER_NAME"] = "베스퍼",
["HERO_VESPER_RICOCHET_DESCRIPTION_1"] = "%$heroes.hero_vesper.ricochet.s_bounces[1]%$ 명의 적 사이에서 튕기는 화살을 발사하여, 매번 %$heroes.hero_vesper.ricochet.s_damage[1]%$의 물리 피해를 입힙니다.",
["HERO_VESPER_RICOCHET_DESCRIPTION_2"] = "%$heroes.hero_vesper.ricochet.s_bounces[2]%$ 명의 적 사이에서 튕기는 화살을 발사하여, 매번 %$heroes.hero_vesper.ricochet.s_damage[2]%$의 물리 피해를 입힙니다.",
["HERO_VESPER_RICOCHET_DESCRIPTION_3"] = "%$heroes.hero_vesper.ricochet.s_bounces[3]%$ 명의 적 사이에서 튕기는 화살을 발사하여, 매번 %$heroes.hero_vesper.ricochet.s_damage[3]%$의 물리 피해를 입힙니다.",
["HERO_VESPER_RICOCHET_TITLE"] = "도탄 사격",
["HERO_WITCH_CLASS"] = "트릭스터 마녀",
["HERO_WITCH_DESC"] = "요정의 숲을 지나가는 낯선 이들에게 재미있고 해롭지 않은 장난을 치는 것을 좋아하지만, 숲이나 그녀의 동료 노움들에게 위협을 가하는 자들은 그녀의 장난기 가득한 미소 뒤에 냉혹한 마녀가 숨어 있다는 사실을 곧 알게 됩니다.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_1"] = "생명력이 %$heroes.hero_witch.disengage.hp_to_trigger%$% 이하로 떨어지면, 스트레기는 뒤로 순간이동하여 대신 싸울 미끼를 남깁니다. 미끼는 %$heroes.hero_witch.disengage.decoy.hp_max[1]%$의 생명력을 가지고 있으며, 파괴될 때 폭발하여 적들을 %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[1]%$ 초 동안 기절시킵니다.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_2"] = "생명력이 %$heroes.hero_witch.disengage.hp_to_trigger%$% 이하로 떨어지면, 스트레기는 뒤로 순간이동하여 대신 싸울 미끼를 남깁니다. 미끼는 %$heroes.hero_witch.disengage.decoy.hp_max[2]%$의 생명력을 가지고 있으며, 파괴될 때 폭발하여 적들을 %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[2]%$ 초 동안 기절시킵니다.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_3"] = "생명력이 %$heroes.hero_witch.disengage.hp_to_trigger%$% 이하로 떨어지면, 스트레기는 뒤로 순간이동하여 대신 싸울 미끼를 남깁니다. 미끼는 %$heroes.hero_witch.disengage.decoy.hp_max[3]%$의 생명력을 가지고 있으며, 파괴될 때 폭발하여 적들을 %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[3]%$ 초 동안 기절시킵니다.",
["HERO_WITCH_DISENGAGE_TITLE"] = "화려한 미끼",
["HERO_WITCH_NAME"] = "스트레기",
["HERO_WITCH_PATH_AOE_DESCRIPTION_1"] = "경로에 거대한 물약을 던져 %$heroes.hero_witch.skill_path_aoe.s_damage[1]%$의 마법 피해를 주고, 적들을 %$heroes.hero_witch.skill_path_aoe.duration[1]%$ 초 동안 느리게 만듭니다.",
["HERO_WITCH_PATH_AOE_DESCRIPTION_2"] = "경로에 거대한 물약을 던져 %$heroes.hero_witch.skill_path_aoe.s_damage[2]%$의 마법 피해를 주고, 적들을 %$heroes.hero_witch.skill_path_aoe.duration[2]%$ 초 동안 느리게 만듭니다.",
["HERO_WITCH_PATH_AOE_DESCRIPTION_3"] = "경로에 거대한 물약을 던져 %$heroes.hero_witch.skill_path_aoe.s_damage[3]%$의 마법 피해를 주고, 적들을 %$heroes.hero_witch.skill_path_aoe.duration[3]%$ 초 동안 느리게 만듭니다.",
["HERO_WITCH_PATH_AOE_TITLE"] = "휘익 그리고 꽝",
["HERO_WITCH_POLYMORPH_DESCRIPTION_1"] = "적을 %$heroes.hero_witch.skill_polymorph.duration[1]%$ 초 동안 작은 호박으로 만듭니다. 작은 호박은 대상의 %$heroes.hero_witch.skill_polymorph.pumpkin.hp[1]%$%의 생명력을 가집니다.",
["HERO_WITCH_POLYMORPH_DESCRIPTION_2"] = "적을 %$heroes.hero_witch.skill_polymorph.duration[2]%$ 초 동안 작은 호박으로 만듭니다. 작은 호박은 대상 체력의 %$heroes.hero_witch.skill_polymorph.pumpkin.hp[2]%$%를 가집니다.",
["HERO_WITCH_POLYMORPH_DESCRIPTION_3"] = "적을 %$heroes.hero_witch.skill_polymorph.duration[3]%$ 초 동안 작은 호박으로 만듭니다. 작은 호박은 대상 체력의 %$heroes.hero_witch.skill_polymorph.pumpkin.hp[3]%$%를 가집니다.",
["HERO_WITCH_POLYMORPH_TITLE"] = "채소화!",
["HERO_WITCH_SOLDIERS_DESCRIPTION_1"] = "적들과 싸울 고양이 %$heroes.hero_witch.skill_soldiers.soldiers_amount[1]%$ 마리를 소환합니다. 고양이는 %$heroes.hero_witch.skill_soldiers.soldier.hp_max[1]%$의 생명력을 가지고 있으며, %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[1]%$의 물리 피해를 줍니다.",
["HERO_WITCH_SOLDIERS_DESCRIPTION_2"] = "적들과 싸울 고양이 %$heroes.hero_witch.skill_soldiers.soldiers_amount[2]%$ 마리를 소환합니다. 고양이는 %$heroes.hero_witch.skill_soldiers.soldier.hp_max[2]%$의 생명력을 가지고 있으며, %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[2]%$의 물리 피해를 줍니다.",
["HERO_WITCH_SOLDIERS_DESCRIPTION_3"] = "적들과 싸울 고양이 %$heroes.hero_witch.skill_soldiers.soldiers_amount[3]%$ 마리를 소환합니다. 고양이는 %$heroes.hero_witch.skill_soldiers.soldier.hp_max[3]%$의 생명력을 가지고 있으며, %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[3]%$의 물리 피해를 줍니다.",
["HERO_WITCH_SOLDIERS_TITLE"] = "밤의 분노",
["HERO_WITCH_ULTIMATE_DESCRIPTION_1"] = "%$heroes.hero_witch.ultimate.max_targets[2]%$ 명의 적을 순간이동시키며, %$heroes.hero_witch.ultimate.duration[2]%$ 초 동안 잠들게 만듭니다.",
["HERO_WITCH_ULTIMATE_DESCRIPTION_2"] = "%$heroes.hero_witch.ultimate.max_targets[3]%$ 명의 적을 순간이동시키며, %$heroes.hero_witch.ultimate.duration[3]%$ 초 동안 잠들게 만듭니다.",
["HERO_WITCH_ULTIMATE_DESCRIPTION_3"] = "%$heroes.hero_witch.ultimate.max_targets[4]%$ 명의 적을 순간이동시키며, %$heroes.hero_witch.ultimate.duration[4]%$ 초 동안 잠들게 만듭니다.",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "적을 경로 뒤로 순간이동시키며, 잠시 동안 잠들게 만듭니다.",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_NAME"] = "졸린 귀환",
["HERO_WITCH_ULTIMATE_TITLE"] = "졸린 귀환",
["HERO_WUKONG_CLASS"] = "손오공 - 원숭이 왕",
["HERO_WUKONG_DESC"] = "음양의 천상의 돌에서 태어난 손오공은 힘과 민첩함, 불사의 능력을 부여받았다. 하지만 마왕들이 그에게서 힘의 구슬을 빼앗았다. 이제 전설적인 장난꾼이 그것들을 되찾기 위해 일어난다. 너무 늦기 전에.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_1"] = "적을 짓밟기 위해 여의봉을 키우며 굴러가 즉시 처치하고, 대상 주변에 %$heroes.hero_wukong.giant_staff.area_damage.damage_min[1]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[1]%$ 범위 피해를 입힙니다.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_2"] = "지구봉을 커지게 하여 적을 짓밟아 즉시 처치하고, 대상 주변에 %$heroes.hero_wukong.giant_staff.area_damage.damage_min[2]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[2]%$ 범위 피해를 줍니다.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_3"] = "적을 짓밟기 위해 여의봉을 굴리며 커지게 하여 즉시 처치하고, 대상 주변 범위에 %$heroes.hero_wukong.giant_staff.area_damage.damage_min[3]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[3]%$의 피해를 입힙니다.",
["HERO_WUKONG_GIANT_STAFF_TITLE"] = "여의봉 기술",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_1"] = "손오공의 머리카락에서 분신 2체를 소환해 함께 싸웁니다. 이들은 %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[1]%$의 피해를 주며 %$heroes.hero_wukong.hair_clones.soldier.duration[1]%$초 동안 유지됩니다.",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_2"] = "손오공의 머리카락 분신 2기를 소환하여 함께 싸웁니다. 이들은 %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[2]%$의 피해를 입히며, %$heroes.hero_wukong.hair_clones.soldier.duration[2]%$초 동안 지속됩니다.",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_3"] = "손오공의 머리카락 분신 2기를 소환하여 함께 싸웁니다. 이들은 %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[3]%$의 피해를 주며, %$heroes.hero_wukong.hair_clones.soldier.duration[3]%$초 동안 지속됩니다.",
["HERO_WUKONG_HAIR_CLONES_TITLE"] = "Hair Clones",
["HERO_WUKONG_NAME"] = "Sun Wukong",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_1"] = "여의봉을 공중으로 날려 %$heroes.hero_wukong.pole_ranged.pole_amounts[1]%$ 개로 분열시키고, 그것들이 적에게 떨어져 각각 %$heroes.hero_wukong.pole_ranged.damage_min[1]%$ 피해를 주며 작은 범위의 적을 기절시킨다.",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_2"] = "여의봉을 공중으로 날려 %$heroes.hero_wukong.pole_ranged.pole_amounts[2]%$ 개로 분열시키고, 그것들이 적에게 떨어져 각각 %$heroes.hero_wukong.pole_ranged.damage_min[2]%$ 피해를 주며 작은 범위의 적을 기절시킨다.",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_3"] = "여의봉을 공중으로 날려 %$heroes.hero_wukong.pole_ranged.pole_amounts[3]%$ 개로 분열시키고, 그것들이 적에게 떨어져 각각 %$heroes.hero_wukong.pole_ranged.damage_min[3]%$ 피해를 주며 작은 범위의 적을 기절시킨다.",
["HERO_WUKONG_POLE_RANGED_TITLE"] = "폴 연속 공격",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_1"] = "화이트 드래곤이 엄청난 힘으로 땅을 강타하여 %$heroes.hero_wukong.ultimate.damage_total[2]%$의 고정 피해를 주고 둔화 구역을 남깁니다.",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_2"] = "화이트 드래곤이 엄청난 힘으로 지면을 강타하여 %$heroes.hero_wukong.ultimate.damage_total[3]%$의 고정 피해를 입히고 느려지게 하는 구역을 남깁니다.",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_3"] = "화이트 드래곤이 엄청난 힘으로 땅을 강타하며 %$heroes.hero_wukong.ultimate.damage_total[4]%$의 고정 피해를 주고 감속 지대를 남깁니다.",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "백룡을 소환합니다",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_NAME"] = "화이트 드래곤",
["HERO_WUKONG_ULTIMATE_TITLE"] = "화이트 드래곤",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_1"] = "저팔계는 손오공의 충직한 동료로 어디든 그를 따라다닙니다. %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[1]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[1]%$의 피해를 입히며, 낮은 확률로 광역 대미지 공격을 가합니다.",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_2"] = "저팔계는 손오공의 충직한 동료로 어디든 그를 따라다닙니다. %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[2]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[2]%$의 피해를 입히며, 낮은 확률로 광역 대미지 공격을 가합니다.",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_3"] = "저팔계는 손오공의 충실한 동료로, 어디든 그를 따라다닌다. %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[3]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[3]%$의 피해를 주며, 낮은 확률로 강력한 광역 피해 공격을 가한다.",
["HERO_WUKONG_ZHU_APPRENTICE_TITLE"] = "Zhu의 제자",
["HOURS_ABBREVIATION"] = "시간",
["Hero at your command!"] = "영웅을 지휘할 수 있습니다!",
["Heroes are elite units that can face strong enemies and support your forces."] = "영웅은 강력한 적과 맞서고 아군을 지원할 수 있는 정예 용사입니다.",
["Heroes gain experience every time they damage an enemy or use an ability."] = "적에게 피해를 주거나 능력을 사용하면 영웅이 경험치를 얻습니다.",
["IAP_CONNECTION_ERROR"] = "%@에 연결할 수 없음",
["INCOMING NEXT WAVE!"] = "다음 부대 접근 중!",
["INCOMING WAVE"] = "다가오는 공격",
["INGAME_BALLOON_BUILD_HERE"] = "여기에 지으세요!",
["INGAME_BALLOON_GOAL"] = "적들이 이 지점을 통과해서는 안됩니다.",
["INGAME_BALLOON_GOLD"] = "적을 처치하면 골드를 얻습니다.",
["INGAME_BALLOON_INCOMING"] = "다음 웨이브가 다가옵니다!",
["INGAME_BALLOON_NEW_HERO"] = "새로운 영웅!",
["INGAME_BALLOON_NEW_POWER"] = "새로운 특수 능력!",
["INGAME_BALLOON_NOTIFICATION_TAP_HERE"] = "여기를 탭하세요!",
["INGAME_BALLOON_SELECT_HERO"] = "선택하려면 탭하세요!",
["INGAME_BALLOON_START_BATTLE"] = "전투 시작!",
["INGAME_BALLOON_TAP_HERE"] = "길 위를 탭하세요",
["INGAME_BALLOON_TAP_TO_CALL"] = "탭하여 웨이브 조기 시작",
["INGAME_BALLOON_TAP_TWICE_BUILD"] = "타워를 짓기 위해 두 번 탭하세요",
["INGAME_BALLOON_TAP_TWICE_START"] = "두 번 탭하여 전투 시작",
["INGAME_BALLOON_TAP_TWICE_WAVE"] = "두 번 탭하여 웨이브를 시작합니다.",
["INGAME_TUTORIAL1_HELP1"] = "적들이 이 지점을 통과해서는 안됩니다.",
["INGAME_TUTORIAL1_HELP2"] = "타워를 건설해 길을 방어하십시오.",
["INGAME_TUTORIAL1_HELP3"] = "적을 처치하면 골드를 얻습니다.",
["INGAME_TUTORIAL1_SUBTITLE1"] = "적의 공격으로부터 당신의 땅을 지키십시오.",
["INGAME_TUTORIAL1_SUBTITLE2"] = "길을 따라 방어 타워를 건설하여 적들을 막으십시오.",
["INGAME_TUTORIAL1_TITLE"] = "목표",
["INGAME_TUTORIAL_GOTCHA_1"] = "알겠습니다!",
["INGAME_TUTORIAL_GOTCHA_2"] = "준비됐어요, 이제 시작해요!",
["INGAME_TUTORIAL_HINT"] = "힌트",
["INGAME_TUTORIAL_INSTRUCTIONS"] = "설명",
["INGAME_TUTORIAL_NEW_TIP"] = "새로운 팁",
["INGAME_TUTORIAL_NEXT"] = "다음!",
["INGAME_TUTORIAL_OK"] = "확인!",
["INGAME_TUTORIAL_SKIP"] = "이것을 건너뛰세요!",
["INGAME_TUTORIAL_TIP_CHALLENGE"] = "경고",
["ITEM_CLUSTER_BOMB_BOTTOM_DESC"] = "팝콘같습니다. 하지만 보다 훨씬 더 재미있고 맛은 덜합니다.",
["ITEM_CLUSTER_BOMB_BOTTOM_INFO"] = "속에 작은 폭탄들이 들어있는 폭탄입니다.",
["ITEM_CLUSTER_BOMB_DESC"] = "지역에 범위 피해를 주고 작은 폭탄들을 흩뿌리는 폭탄입니다.",
["ITEM_CLUSTER_BOMB_NAME"] = "집속탄",
["ITEM_DEATHS_TOUCH_BOTTOM_DESC"] = "신이 된 듯한 기분을 느끼고 싶을 때에 좋습니다! 죽음의 신!",
["ITEM_DEATHS_TOUCH_BOTTOM_INFO"] = "선택하십시오. 대상을 탭하십시오. 죽이십시오.",
["ITEM_DEATHS_TOUCH_DESC"] = "죽음의 신의 힘으로 어떤 적이든 탭하면 즉시 제거됩니다. 보스나 미니 보스에게는 효과가 없습니다.",
["ITEM_DEATHS_TOUCH_NAME"] = "죽음의 손길",
["ITEM_LOOT_BOX_BOTTOM_DESC"] = "이것들을 몇 개만 가지고 있으면 평생을 보낼 수 있습니다.",
["ITEM_LOOT_BOX_BOTTOM_INFO"] = "경로에 상자를 떨어뜨려 적에게 피해를 입히고 즉시 골드를 얻습니다.",
["ITEM_LOOT_BOX_DESC"] = "경로에 상자를 떨어뜨려 적에게 피해를 입히고 즉시 300골드를 얻습니다.",
["ITEM_LOOT_BOX_NAME"] = "금맥 상자",
["ITEM_MEDICAL_KIT_BOTTOM_DESC"] = "이거면 치료에 충분해요.",
["ITEM_MEDICAL_KIT_BOTTOM_INFO"] = "플레이어에게 최대 3 개의 하트를 회복시켜 줍니다.",
["ITEM_MEDICAL_KIT_DESC"] = "플레이어의 하트를 최대 3 개까지 회복시켜주는 특수 키트입니다.",
["ITEM_MEDICAL_KIT_NAME"] = "의료 키트",
["ITEM_PORTABLE_COIL_BOTTOM_DESC"] = "지직! 파박! 쥐처럼 튀겨졌어!",
["ITEM_PORTABLE_COIL_BOTTOM_INFO"] = "지역 내 적에게 피해를 주고 기절시키는 함정을 설치하세요.",
["ITEM_PORTABLE_COIL_DESC"] = "적에게 피해를 입히고 기절시키는 트랩을 설치합니다. 그 효과는 근처의 적에게도 연쇄적으로 적용될 수 있습니다.",
["ITEM_PORTABLE_COIL_NAME"] = "휴대용 코일",
["ITEM_ROOM_EQUIP"] = "장비",
["ITEM_ROOM_EQUIPPED"] = "장비됨",
["ITEM_ROOM_EQUIPPED_ITEMS"] = "장비된 아이템",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_DESC"] = "적과 싸울 시간이 부족했던 적이 있나요? 이제 더 이상 걱정하지 마세요!",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_INFO"] = "한 무리의 적들을 경로를 따라 뒤로 순간이동시킵니다.",
["ITEM_SCROLL_OF_SPACESHIFT_DESC"] = "한 무리의 적들을 경로를 따라 뒤로 순간이동시킵니다.",
["ITEM_SCROLL_OF_SPACESHIFT_NAME"] = "공간이동의 두루마리",
["ITEM_SECOND_BREATH_BOTTOM_DESC"] = "무덤에서 일어나라, 언데드의 불리함 없이.",
["ITEM_SECOND_BREATH_BOTTOM_INFO"] = "쓰러진 영웅들을 되살리고, 부상당한 이들을 치유하며, 영웅 능력의 사용 대기 시간을 초기화합니다.",
["ITEM_SECOND_BREATH_DESC"] = "쓰러진 영웅들을 되살리고, 부상당한 이들을 치유하며, 영웅 능력의 사용 대기 시간을 초기화하는 신성한 축복입니다.",
["ITEM_SECOND_BREATH_NAME"] = "두 번째 삶",
["ITEM_SUMMON_BLACKBURN_BOTTOM_DESC"] = "유일무이. 따라할 수 없는 자.",
["ITEM_SUMMON_BLACKBURN_BOTTOM_INFO"] = "당신과 함께 싸울 강력한 블랙번을 소환합니다.",
["ITEM_SUMMON_BLACKBURN_DESC"] = "적을 물리칠 강력한 전사의 망령을 소환합니다.",
["ITEM_SUMMON_BLACKBURN_NAME"] = "블랙번의 투구",
["ITEM_VEZNAN_WRATH_BOTTOM_DESC"] = "암흑 마법사의 무한한 힘을 조금 맛보게 하라!",
["ITEM_VEZNAN_WRATH_BOTTOM_INFO"] = "전장의 모든 적을 죽입니다.",
["ITEM_VEZNAN_WRATH_DESC"] = "베즈난이 전장의 모든 적을 죽이는 강력한 주문을 시전합니다.",
["ITEM_VEZNAN_WRATH_NAME"] = "베즈난의 분노",
["ITEM_WINTER_AGE_BOTTOM_DESC"] = "여름을 정말로 싫어하는 경우에도 유용합니다.",
["ITEM_WINTER_AGE_BOTTOM_INFO"] = "화면상의 모든 적을 얼립니다.",
["ITEM_WINTER_AGE_DESC"] = "모든 적을 몇 초 동안 얼려버리는 차가운 바람을 만들어내는 강력한 주문입니다.",
["ITEM_WINTER_AGE_NAME"] = "겨울 시대",
["If you enjoy using %@, would you mind taking a moment to rate it? It won't take more than a minute. Thanks for your support!"] = "%@ 사용이 맘에 드셨나요? 잠시만 시간을 내서 평가를 부탁드리겠습니다. 감사합니다!",
["Impossible"] = "불가능",
["Iron Challenge"] = "강철 도전",
["KR5_NO_GEMS"] = "보석이 충분하지 않습니다.\n더 구매하시겠습니까?",
["KR5_PURCHASE_ERROR"] = "구매를 처리하는 동안 오류가 발생했습니다.",
["KR5_RATE_US"] = "게임을 즐기고 계신가요? 스토어에서 평가해주세요!",
["LEVEL_10_HEROIC"] = "영웅 모드 설명 10",
["LEVEL_10_HISTORY"] = "이교도들이 채굴한 크리스탈을 사용하여 협곡 바로 밖에 불길해 보이는 인공물을 만들고 있는 것으로 밝혀졌습니다. 이상한 에너지로 윙윙거리는 이 곳을 둘러싼 공기가 무겁게 느껴집니다. 나아가기 전에 반드시 파괴되었는지 확인해야 합니다.",
["LEVEL_10_IRON"] = "강철 모드 설명 10",
["LEVEL_10_IRON_UNLOCK"] = "미정",
["LEVEL_10_MODES_UPGRADES"] = "레벨 5 최대",
["LEVEL_10_TITLE"] = "10. 사원의 안뜰",
["LEVEL_11_HEROIC"] = "영웅 모드 설명 11",
["LEVEL_11_HISTORY"] = "우리는 마침내 협곡에서 벗어났만 아직 갈 길이 멉니다. 예언자 미드리아스가 의식을 마치는 동안 우리는 이제 크리스탈이 박힌 거대한 포탈 앞에 도착했습니다. 우리가 알지 못하는 저 너머에서 무엇이 나올지 모르지만 우리는 여전히 준비가 되어 있습니다. 마음 단단히 먹으십시오!",
["LEVEL_11_IRON"] = "강철 모드 설명 11",
["LEVEL_11_IRON_UNLOCK"] = "미정",
["LEVEL_11_MODES_UPGRADES"] = "레벨 5 최대",
["LEVEL_11_TITLE"] = "11. 협곡 고원",
["LEVEL_12_HEROIC"] = "영웅 모드 설명 12",
["LEVEL_12_HISTORY"] = "데나스가 다시 우리 편이 되어 우리는 포탈을 넘어 미지의 세계로 들어갔습니다. 이 이상한 세계는 리니리아의 뒤틀린 모습처럼 보이지만, 실은 황폐화된 것입니다. 이교도보다 더 나쁜 것이 어둠 속에 숨어 있으니 조심하십시오.",
["LEVEL_12_IRON"] = "강철 모드 설명 12",
["LEVEL_12_IRON_UNLOCK"] = "미정",
["LEVEL_12_MODES_UPGRADES"] = "레벨 5 최대",
["LEVEL_12_TITLE"] = "12. 황폐화된 농지",
["LEVEL_13_HEROIC"] = "영웅 모드 설명 13",
["LEVEL_13_HISTORY"] = "폭풍구름 사원의 낯익은 광경이 지평선에 선명히 다가옵니다. 악취와 부패가 자라난 곳을 따라가면 우리는 이 모든 것의 근원을 찾을 수 있을 것입니다. 우리는 땅 자체에서 나오는 것 같은 뒤틀린 공포로부터 살아남아야 합니다.",
["LEVEL_13_IRON"] = "강철 모드 설명 13",
["LEVEL_13_IRON_UNLOCK"] = "미정",
["LEVEL_13_MODES_UPGRADES"] = "레벨 5 최대",
["LEVEL_13_TITLE"] = "13. 더럽혀진 사원",
["LEVEL_14_HEROIC"] = "영웅 모드 설명 14",
["LEVEL_14_HISTORY"] = "이 망할 생물들은 난데없이 생겨나는 것 같습니다! 군대는 안절부절못하고 있고, 우리가 만지는 모든 것들이 살아있고 우리를 공격할 준비가 되어 있는 것처럼 보입니다. 마치 땅 자체가 우리를 상대로 온 힘을 다해 싸우고 있는 것처럼 말입니다. 예언자 미드리아스와 그녀의 부하들이 가까이에 있을 것입니다.",
["LEVEL_14_IRON"] = "강철 모드 설명 14",
["LEVEL_14_IRON_UNLOCK"] = "미정",
["LEVEL_14_MODES_UPGRADES"] = "레벨 5 최대",
["LEVEL_14_TITLE"] = "14. 부패의 계곡",
["LEVEL_15_HEROIC"] = "영웅 모드 설명 15",
["LEVEL_15_HISTORY"] = "우리는 계곡에서 승리를 거두었고 이제 우리와 오버시어 사이에 서 있는 유일한 것은 미드리아스 뿐입니다. 협곡에서 그녀가 어떤 능력을 가졌는지 보았지만, 여기선 그녀의 우두머리의 눈과 힘 아래서 그녀가 우위를 점하고 있습니다. 그러나 이전에 그런 역경이 우리를 막지는 못했습니다. 어서 빨리 움직이십시오!",
["LEVEL_15_IRON"] = "강철 모드 설명 15",
["LEVEL_15_IRON_UNLOCK"] = "미정",
["LEVEL_15_MODES_UPGRADES"] = "레벨 5 최대",
["LEVEL_15_TITLE"] = "15. 눈엣가시 타워",
["LEVEL_16_HEROIC"] = "영웅 모드 설명 16",
["LEVEL_16_HISTORY"] = "미드리아스는 사라졌으며 남은 최대의 적은 오버시어입니다. 이번이 이교도들의 침략을 종식시킬 수 있는 마지막 기회입니다. 우리가 마지막으로 한번 더 뭉치지 않는다면 미래는 없습니다. 모두 앞으로!",
["LEVEL_16_IRON"] = "강철 모드 설명 16",
["LEVEL_16_IRON_UNLOCK"] = "미정",
["LEVEL_16_MODES_UPGRADES"] = "레벨 5 최대",
["LEVEL_16_TITLE"] = "16. 배고픔의 절정",
["LEVEL_17_HISTORY"] = "한때 아름다웠던 요정 숲은 이제 낯설고 무시무시한 모습으로 변했습니다. 쓰러진 엘프 전사들과 유령들이 이 땅을 배회하며 여행자들을 공격하고 숲 자체를 타락시킨다는 소문이 돌고 있습니다. 더 조사해봐야 합니다, 사령관님.",
["LEVEL_17_TITLE"] = "17. 안개 낀 폐허",
["LEVEL_18_HISTORY"] = "딥리프 전초기지에서 일부 엘프들이 레버넌트 무리의 진격에 간신히 저항하고 있다는 전갈이 왔습니다. 너무 늦기 전에 서둘러 그들과 그들의 대장인 에리단을 도와야 합니다. 전초기지가 제대로 확보되면 이 침략의 근원을 향해 전진할 수 있습니다.",
["LEVEL_18_TITLE"] = "18. 딥리프 전초기지",
["LEVEL_19_HISTORY"] = "지친 에리단이 우리에게 가리킨 곳은 쓰러진 자의 신전이었습니다. 그곳에선 강령술사 나비라의 명령을 받아 대륙을 지배하기 위한 무리가 길러지고 있습니다. 무슨 수를 써서라도 그를 막아야 합니다!",
["LEVEL_19_TITLE"] = "19. 쓰러진 자의 신전",
["LEVEL_1_HEROIC"] = "영웅 모드 설명 1",
["LEVEL_1_HISTORY"] = "몇 달 동안 남쪽 숲을 뒤졌지만 데나스 왕은 어디에서도 찾을 수 없었습니다. 그 동안 우리는 자연의 정령인 아르보리아인들과 친구가 되었고, 그들과 전쟁 중인 야수들을 만났는데 닥치는 대로 우리를 계속 공격하고 있습니다. 왕을 계속 찾을 수 있도록 이 싸움을 끝냅시다.",
["LEVEL_1_IRON"] = "강철 모드 설명 1",
["LEVEL_1_IRON_UNLOCK"] = "왕실 궁수\n성기사 성약단",
["LEVEL_1_MODES_UPGRADES"] = "레벨 1 최대",
["LEVEL_1_TITLE"] = "1. 나무의 바다",
["LEVEL_20_HISTORY"] = "숲 가장자리에 있는 아르보리아인들로부터 긴급한 도움을 요청하는 신호를 받았습니다. 그들은 끊임없이 크록들의 공격을 받고 있습니다. 그들은 오래 버티지 못할 것입니다. 주의하십시오, 사령관. 크록들은 비늘 속에 많은 계략을 숨기고 있습니다.",
["LEVEL_20_TITLE"] = "20. 아르보리아의 햄릿",
["LEVEL_21_HISTORY"] = "마을의 안전을 확보한 후, 아르보리아인들은 공격 직전에 그들의 고대 봉인이 흔들리는 것을 느꼈다고 밝혔습니다. 크록들의 갑작스러운 침략에 대한 단서를 가지고 우리는 늪의 중심부로 뛰어들었습니다. 우리는 오래된 아르보리아의 스톤 서클을 우연히 발견했습니다, 그것은 굴처럼 보입니다... 어떤 거대한 무언가의 굴.",
["LEVEL_21_TITLE"] = "21. 가라앉은 폐허",
["LEVEL_22_HISTORY"] = "고대 사원에 도착했을 때, 우리의 최악의 두려움이 현실로 확인되었습니다. 우리 세계를 오랫동안 보호해 온 봉인, 아보미노르 — 영토의 포식자 — 는 거의 풀려나 있었고, 절박한 아르보리아 주술사들의 결속 마법에 의해서만 어렵게 유지되고 있었습니다. 장군님, 아보미노르를 멈추지 않으면 왕국은 그 만족할 줄 모르는 아귀에 삼켜질 것입니다.",
["LEVEL_22_TITLE"] = "22. 굶주린 홀로우",
["LEVEL_23_HISTORY"] = "정찰병들이 인근 산에서 비정상적인 산사태를 보고했습니다. 추가 조사를 통해 우리가 알지 못하는 드워프들이 이를 일으켰음을 발견했습니다. 그들은 산의 남쪽 면에서 거대한 자동기계를 조립하고 있습니다. 사령관님, 이를 조사해보셔야 합니다.",
["LEVEL_23_TITLE"] = "23. 다크스틸 관문",
["LEVEL_24_HISTORY"] = "드워프들은 항상 발명가로 알려져 왔지만, 이 자칭 다크스틸 일족은 금속에 대한 헌신이 지나쳐서 볼거의 사람들조차 창피를 줄 정도의 빠른 속도로 자신을 '개선'하기 위해 대장간을 사용합니다. 이런 광기의 배후에는 누가 있겠습니까? 우리는 알아내야 합니다!",
["LEVEL_24_TITLE"] = "24. 광란의 조립 라인",
["LEVEL_25_HISTORY"] = "우리가 우려했던 대로, 이만큼 큰 산의 내부만이 이 자동기계를 만들 수 있는 대장간을 유지할 수 있었습니다.. 여기에 드워프가 몇 명이나 있습니까? 그들은 우리의 전진을 저지하면서도 계속해서 담금질과 용접을 하고 있습니다. 그리고 더 이상한 것은, 그들 모두가 똑같이 보인다는 것입니다. 뭔가 이상합니다.",
["LEVEL_25_TITLE"] = "25. 거대한 코어",
["LEVEL_26_HISTORY"] = "산 안팎으로 이동하는 동안 우리는 액체 탱크로 가득 찬 방에 도달했습니다. 그 탱크는 비어 있지 않았습니다. 그들이 수적으로 강력하고 또한 기술과 외모를 가진 이유가 분명해졌습니다. 그들은 모두 같은 드워프, 그림비어드입니다! 그는 사악한 과학을 통해 자신의 클론을 만들어왔습니다. 사령관님, 이를 반드시 막아야 합니다!",
["LEVEL_26_TITLE"] = "26. 복제실",
["LEVEL_27_HISTORY"] = "우리는 산에서의 다크스틸 작전의 대부분을 혼란에 빠뜨리는 데 성공했지만, 그림비어드가 여전히 자유롭다면 모든 것이 헛수고가 될 것입니다. 그는 분명히 자동기계의 머리를 마무리하는 작업을 하고 있을 것입니다. 사령관님, 군대를 산 정상으로 보내 이번에는 제대로 된 드워프와 맞서길 바랍니다.",
["LEVEL_27_TITLE"] = "27. 도미니언 돔",
["LEVEL_28_HISTORY"] = "우리 정찰병들이 남긴 단서를 따라가다 보니 저주받은 광신도들이 남긴 흔적을 발견했다. 그들은 새로운 신을 찾은 듯하다—끔찍한 거미줄을 짜는 흉물스러운 존재를… 하지만 이제 누가 그들을 감싸든 상관없다. 이번에는 반드시 그들을 끝장낼 것이다.",
["LEVEL_28_TITLE"] = "28. 더럽혀진 사원",
["LEVEL_29_HISTORY"] = "우리가 더 깊이 들어갈수록, 이 공포가 오랫동안 지하에서 자라왔다는 것이 더욱 분명해진다. 이렇게 빠른 확장은 상상도 할 수 없는 일이었을 것이고, 주위를 감싸는 거미줄의 두터움, 피비린내, 그리고 목덜미를 타고 흐르는 듯한 어둠을 보건대, 우리는 분명 그들의 은신처 한가운데로 다가가고 있는 듯하다.",
["LEVEL_29_TITLE"] = "29. 번식의 방",
["LEVEL_2_HEROIC"] = "영웅 모드 설명 2",
["LEVEL_2_HISTORY"] = "경계경보! 숲의 심장이 공격받고 있다고 위스프가 알려왔습니다. 우리는 돌아가서 아르보리아인들을 도와야 합니다. 일부 어둠의 군대 병력이 전장에 합류할 것이므로 주시하십시오. 지금은 상황이 좋지 않지만, 언제든지 바뀔 수 있습니다.",
["LEVEL_2_IRON"] = "강철 모드 설명 2",
["LEVEL_2_IRON_UNLOCK"] = "비전 마법사\n트라이캐넌",
["LEVEL_2_MODES_UPGRADES"] = "레벨 2 최대",
["LEVEL_2_TITLE"] = "2. 수호자의 문",
["LEVEL_30_HISTORY"] = "마침내 그들이 신이라 부르는 존재의 은신처에 도착했다—오래 전에 버려져 퇴락한 신전이, 잊힌 과거의 무게를 견디지 못하고 무너지고 있다. 버림받은 신에게 걸맞은 왕좌다. 이제 이 해충들을 박멸하고 이 광기를 끝낼 때가 왔다.",
["LEVEL_30_TITLE"] = "30. 잊혀진 왕좌",
["LEVEL_31_HISTORY"] = "온갖 싸움과 고난 끝에 마침내 왕국에 평화가 찾아왔다. 이제는 파도 소리를 들으며, 오래된 친구를 기다리며 보드게임을 즐기는 것이 유일한 일이다. 하지만 모든 것이 이렇게 평온해 보여도, 이 평화가 얼마나 지속될지 궁금하다...",
["LEVEL_31_TITLE"] = "31. 천상의 원숭이 숲",
["LEVEL_32_HISTORY"] = "추격 끝에 우리는 화산의 중심부에 도달했다. 그곳에는 오래전에 잊힌 사원이 불꽃에 경의를 표하며 존재했었다. 하지만 한때 이 불타는 심연의 중립 수호자였던 대화룡이 이제는 부자연스러운 분노로 들끓고 있다. 모든 징후는 레드보이의 영향이 그의 의지를 타락시켰음을 가리킨다. 드래곤과 마주하는 건 결코 쉬운 일이 아니지만, 선택의 여지가 없다. 전투 준비!",
["LEVEL_32_TITLE"] = "32. 화염 용의 동굴",
["LEVEL_33_HISTORY"] = "레드보이와의 고된 전투 후, 우리는 폭풍의 섬으로 향했다. 발을 들이자마자 번개가 가득한 구름과 거센 돌풍이 기묘하고 뒤틀린 패턴으로 울부짖기 시작했다. 그래도 어쩔 수 없다. 이 섬은 공주의 궁전으로 들어가는 유일한 입구를 품고 있다. 각오해라... 폭풍이 몰려온다.",
["LEVEL_33_TITLE"] = "33. 폭풍의 섬",
["LEVEL_34_HISTORY"] = "우리가 견뎌온 고난은 공주와 그녀의 철 부채 덕분이다. 다리를 건너 가장 거센 폭풍을 뚫고 나서, 우리는 이제 모든 것의 중심에 서 있다. 이곳은 여전히 온전하다—겉보기엔 평온하고 아름답지만 속지 말아야 한다. 방심해선 안 된다. 마왕이라도 우리를 막을 수는 없다.",
["LEVEL_34_TITLE"] = "34. 폭풍의 눈",
["LEVEL_35_HISTORY"] = "드디어 때가 왔다. 우마왕은 화려하고 난공불락의 요새에서 우뚝 서 있다. 우리는 남은 병력을 모두 모아 그의 성벽을 정면으로 돌파할 준비를 마쳤다. 이 임무는 무력보다는 지략이 더 필요하다. 그가 오브의 힘을 완전히 해방하기 전에 우리가 먼저 공격해야 한다.\n이 아름다운 땅에서 당신이 소중히 여기는 모든 것을 위해… 일어서라, 리니리안들이여!",
["LEVEL_35_TITLE"] = "35. 마왕의 요새",
["LEVEL_3_HEROIC"] = "영웅 모드 설명 3",
["LEVEL_3_HISTORY"] = "우리는 아슬아슬하게 시간에 맞춰 숲의 심장으로 돌왔지만 야수들은 이미 지역을 지나고 있습니다. 빨리 움직여 전투태세를 강화하십시오. 무슨 수를 써서라도 심장을 지켜야 합니다. 그렇지 않으면 숲과 아르보리아인들이 반드시 파멸할 것입니다.",
["LEVEL_3_IRON"] = "강철 모드 설명 3",
["LEVEL_3_IRON_UNLOCK"] = "왕실 궁수\n성기사 성약단",
["LEVEL_3_MODES_UPGRADES"] = "레벨 3 최대",
["LEVEL_3_TITLE"] = "3. 숲의 심장",
["LEVEL_4_HEROIC"] = "영웅 모드 설명 4",
["LEVEL_4_HISTORY"] = "이제 숲의 심장이 안전해졌으니, 우리는 다시 집결해 이점을 활용해야 합니다. 야수들의 영역을 찾아내어 공격할 때입니다. 병력을 숲의 나무 꼭대기로 이동시켜, 위에서 그들의 진지를 찾으십시오.",
["LEVEL_4_IRON"] = "강철 모드 설명 4",
["LEVEL_4_IRON_UNLOCK"] = "트라이캐넌\n아르보리아 사절단",
["LEVEL_4_MODES_UPGRADES"] = "레벨 4 최대",
["LEVEL_4_TITLE"] = "4. 에메랄드 나무 꼭대기",
["LEVEL_5_HEROIC"] = "영웅 모드 설명 5",
["LEVEL_5_HISTORY"] = "고지를 점령하기 위한 노력 덕분에, 우리는 야수들의 진지를 숲의 경계 너머의 고대 유적지로 보내버릴 수 있었습니다. 그들의 전술을 경계하며 군대를 그들의 영토로 이끄십시오. 우리는 또 다른 전투에서 승리할지 모르지만, 아직 끝나려면 멀었습니다.",
["LEVEL_5_IRON"] = "강철 모드 설명 5",
["LEVEL_5_IRON_UNLOCK"] = "비전 마법사\n성기사 성약단",
["LEVEL_5_MODES_UPGRADES"] = "레벨 5 최대",
["LEVEL_5_TITLE"] = "5. 황폐화된 교외",
["LEVEL_6_HEROIC"] = "영웅 모드 설명 6",
["LEVEL_6_HISTORY"] = "우리가 야수들을 상대로 우위를 점할 수도 있겠지만, 여전히 그들의 지도자인 고어그라인드를 상대해야 합니다. 스스로를 야수의 왕이라 칭하는 그의 익살에 속아서는 안됩니다. 그렇지 않으면 그의 엄니 밑에서 최후를 맞이하게 될 것입니다.",
["LEVEL_6_IRON"] = "강철 모드 설명 6",
["LEVEL_6_IRON_UNLOCK"] = "왕실 궁수\n악마의 구덩이",
["LEVEL_6_MODES_UPGRADES"] = "레벨 5 최대",
["LEVEL_6_TITLE"] = "6. 야수들의 소굴",
["LEVEL_7_HEROIC"] = "영웅 모드 설명 7",
["LEVEL_7_HISTORY"] = "야수들을 도와 숲의 일부를 파괴한 이교도들의 흔적을 따라가다 한 황량한 장소에 도착했습니다.  조심하십시오, 우리가 정확히 무엇을 상대하고 있는지 모르지만... 그들은 몇 가지 속임수를 쓰고 있는 것 같습니다.",
["LEVEL_7_IRON"] = "강철 모드 설명 7",
["LEVEL_7_IRON_UNLOCK"] = "왕실 궁수 불가",
["LEVEL_7_MODES_UPGRADES"] = "레벨 5 최대",
["LEVEL_7_TITLE"] = "7. 황량한 계곡",
["LEVEL_8_HEROIC"] = "영웅 모드 설명 8",
["LEVEL_8_HISTORY"] = "우리가 교단의 영역으로 들어갈 때, 이상한 마법과 공명하는 크리스탈로 가득한 거대한 동굴군에 도착했습니다. 이교도들은 분명히 이 크리스탈을 동력원으로 사용하기 위해 채굴하고 있습니다. 목적이 무엇인지는 모르지만, 그들의 활동을 방해하는 것은 그들의 대열에 혼란을 야기하는 좋은 방법입니다.",
["LEVEL_8_IRON"] = "강철 모드 설명 8",
["LEVEL_8_IRON_UNLOCK"] = "트라이캐넌\n성기사 성약단",
["LEVEL_8_MODES_UPGRADES"] = "레벨 5 최대",
["LEVEL_8_TITLE"] = "8. 카민 광산",
["LEVEL_9_HEROIC"] = "영웅 모드 설명 9",
["LEVEL_9_HISTORY"] = "이 터널의 구불구불한 굴곡은 미칠 노릇이지만 이교도 활동이 계속 증가하는 것으로 보아 우리는 바른 길로 가고 있습니다. 우리가 점점 전진할수록 새로운 종류의 공포와 직면하게 되는데, 이는 교단의 계급구조 내에서 부패가 얼마나 깊이 뿌리내렸는지에 대한 의문이 들게 합니다.",
["LEVEL_9_IRON"] = "강철 모드 설명 9",
["LEVEL_9_IRON_UNLOCK"] = "악마의 구덩이\n비전 마법사",
["LEVEL_9_MODES_UPGRADES"] = "레벨 5 최대",
["LEVEL_9_TITLE"] = "9. 사악한 교차로",
["LEVEL_DEFEAT_ADVICE"] = "보석으로 특별한 아이템을 구매해 적들을 날려버리세요!",
["LEVEL_DEFEAT_GEMS_COLLECTED"] = "획득:",
["LEVEL_DEFEAT_GEMS_COUNT"] = "보석 %i개",
["LEVEL_DEFEAT_TITLE"] = "패배!",
["LEVEL_MODE_CAMPAIGN"] = "캠페인",
["LEVEL_MODE_HEROIC"] = "영웅 도전",
["LEVEL_MODE_HEROIC_DESCRIPTION"] = "적 정예 부대에 맞서 전술을 시험하세요. 영웅의 칭호가 어울리는 수호자만이 이 시련을 통과할 수 있습니다!",
["LEVEL_MODE_IRON"] = "강철 도전",
["LEVEL_MODE_IRON_DESCRIPTION"] = "강철 도전은 궁극의 수호자를 가리는 시련이며, 전술 능력을 극한까지 발휘해야 합니다.",
["LEVEL_SELECT_AVAILABLE_TOWERS"] = "이용 가능한 타워",
["LEVEL_SELECT_CHALLENGE_ONE_ELITE_WAVE"] = "정예 공세 1회",
["LEVEL_SELECT_CHALLENGE_ONE_LIFE"] = "라이프 1",
["LEVEL_SELECT_CHALLENGE_ONE_WAVE"] = "파상공세 1회",
["LEVEL_SELECT_CHALLENGE_RULES"] = "도전 규칙",
["LEVEL_SELECT_CHALLENGE_SIX_ELITE_WAVE"] = "정예 공세 6회",
["LEVEL_SELECT_DIFFICULTY_CASUAL"] = "캐주얼",
["LEVEL_SELECT_DIFFICULTY_IMPOSSIBLE"] = "불가능",
["LEVEL_SELECT_DIFFICULTY_NORMAL"] = "일반",
["LEVEL_SELECT_DIFFICULTY_VETERAN"] = "베테랑",
["LEVEL_SELECT_GAME_MODE"] = "게임 모드",
["LEVEL_SELECT_GET_DLC"] = "가져가세요",
["LEVEL_SELECT_HELP1"] = "여기서 플레이 모드를 선택하세요!",
["LEVEL_SELECT_HELP2"] = "난이도를 선택하세요!",
["LEVEL_SELECT_HELP3"] = "전투 시작!",
["LEVEL_SELECT_MODE_LOCKED1"] = "모드 잠김",
["LEVEL_SELECT_MODE_LOCKED2"] = "이 스테이지를 완료하여 이 모드를 잠금 해제하세요.",
["LEVEL_SELECT_TO_BATTLE"] = "전투\n개시",
["LOADING"] = "불러오는 중",
["LV22_BOSS_BEFORE_FIGHT_EAT_01"] = "맛있네! 하 하 하",
["LV22_BOSS_BEFORE_FIGHT_EAT_02"] = "난 식물을 싫어한다",
["LV22_BOSS_BEFORE_FIGHT_EAT_03"] = "네가 먹는 것이 곧 너다",
["LV22_BOSS_BEFORE_FIGHT_EAT_04"] = "이 한 입은 상쾌했다",
["LV22_BOSS_BEFORE_FIGHT_EAT_05"] = "벌써 지쳤나?",
["LV22_BOSS_BEFORE_FIGHT_EAT_06"] = "난 다시는 배고픔을 느끼지 않을거야",
["LV22_BOSS_BEFORE_FIGHT_EAT_07"] = "멋진 타워였어, 하하하",
["LV22_BOSS_BEFORE_FIGHT_EAT_08"] = "자유의 맛이야",
["LV22_BOSS_INTRO_01"] = "첫 식사로 간식을 가져오고 있어.",
["LV22_BOSS_INTRO_02"] = "바삭바삭해 보여...",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_01"] = "덩굴 말고는 안돼",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_02"] = "아르보리아인은 친구다, 음식이 아니라",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_03"] = "그리고 더는 아무것도 먹지 마라!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_04"] = "네 감옥으로 돌아가라, 괴물아!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_05"] = "너는 먹지 못할 것이다!!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_06"] = "난 아르보리아를 지키겠어!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_07"] = "넌 끝에 웃지 못할 것이다",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_08"] = "더 강해지고 있어! 도와줘!!",
["LV22_MAGE_INTRO_01"] = "입 닥쳐!",
["LV22_MAGE_INTRO_02"] = "서둘러! 나는 그를 더 이상 막을 수 없어!",
["Localization Manager"] = "현지화 관리자",
["Log in"] = "로그인",
["Log out?"] = "로그아웃하시겠습니까?",
["Login Required"] = "로그인",
["MAGES’ GUILD"] = "마법사 길드",
["MAGIC RESISTANT ENEMIES!"] = "마법 저항 적!",
["MAP_BALLON_BUY_UPGRADES_DESCRIPTION"] = "획득한 별을 사용해 타워와 능력을 강화하세요!",
["MAP_BALLON_BUY_UPGRADES_TITLE"] = "업그레이드를 구매하세요!",
["MAP_BALLON_HERO_LEVELUP_DESCRIPTION"] = "획득한 영웅 포인트로 영웅을 강화하세요!",
["MAP_BALLON_HERO_LEVELUP_TITLE"] = "영웅 레벨 업!",
["MAP_BALLON_HERO_UNLOCKED"] = "영웅 개방됨!",
["MAP_BALLON_START_HERE"] = "여기서 시작합니다!",
["MAP_BUTTON_ACHIEVEMENTS"] = "업적",
["MAP_BUTTON_CHALLENGES"] = "도전",
["MAP_BUTTON_ENCYCLOPEDIA"] = "백과사전",
["MAP_BUTTON_HERO_ROOM"] = "영웅들",
["MAP_BUTTON_ITEMS"] = "아이템",
["MAP_BUTTON_SHOP"] = "상점",
["MAP_BUTTON_TOWER_ROOM"] = "타워",
["MAP_BUTTON_UPGRADES"] = "업그레이드",
["MAP_ENCYCLOPEDIA_STRATEGY_GUIDE"] = "병법서",
["MAP_ENCYCLOPEDIA_TIPS"] = "팁",
["MAP_HEROROOM_HELP1"] = "선택해 능력을 훈련하세요!",
["MAP_HEROROOM_HELP2"] = "눌러서 선택",
["MAP_HEROROOM_HELP3"] = "영웅의 전투력을 향상시키세요!",
["MAP_HERO_ROOM_GET_IT_NOW"] = "지금 구매!",
["MAP_HERO_ROOM_SELECT"] = "장비",
["MAP_HERO_ROOM_SELECTED"] = "장비됨",
["MAP_HERO_ROOM_TRAIN"] = "훈련",
["MAP_HERO_ROOM_UNLOCK"] = "스테이지 %d에 개방",
["MAP_HERO_ROOM_UNLOCK_10"] = "스테이지 10에 개방",
["MAP_HERO_ROOM_UNLOCK_14"] = "스테이지 14에 개방",
["MAP_HERO_ROOM_UNLOCK_15"] = "스테이지 15에 개방",
["MAP_HERO_ROOM_UNLOCK_4"] = "스테이지 4에 개방",
["MAP_HERO_ROOM_UNLOCK_7"] = "스테이지 7에 개방",
["MAP_HERO_ROOM_UNLOCK_9"] = "스테이지 9에 개방",
["MAP_HERO_ROOM_UNLOCK_AFTER_CAMPAIGN"] = "게임 완료 시 잠금 해제 ",
["MAP_INAPPS_BUBBLE_INFO_1"] = "게임을 플레이해 보석을 모으세요.",
["MAP_INAPPS_BUBBLE_INFO_2"] = "보석을 사용해 특별한 아이템을 구매하세요!",
["MAP_INAPPS_BUBBLE_MORE_GEMS"] = "보석이 더 필요합니다!",
["MAP_INAPPS_BUBBLE_SUCCESSFUL"] = "구매\n성공!",
["MAP_INAPP_GEMS_GEM_SHOP_TITLE"] = "보석 상점",
["MAP_INAPP_GEM_PACK_1"] = "보석 한 줌",
["MAP_INAPP_GEM_PACK_2"] = "보석 주머니",
["MAP_INAPP_GEM_PACK_3"] = "보석 한 통",
["MAP_INAPP_GEM_PACK_4"] = "보석 한 상자",
["MAP_INAPP_GEM_PACK_5"] = "보석 수레",
["MAP_INAPP_GEM_PACK_6"] = "보석 산더미",
["MAP_INAPP_GEM_PACK_BAG"] = "보석 자루",
["MAP_INAPP_GEM_PACK_BARREL"] = "보석 배럴",
["MAP_INAPP_GEM_PACK_CHEST"] = "보석 상자",
["MAP_INAPP_GEM_PACK_FREE"] = "무료 보석",
["MAP_INAPP_GEM_PACK_HANDFUL"] = "보석 한 줌",
["MAP_INAPP_GEM_PACK_VAULT"] = "보석 금고",
["MAP_INAPP_GEM_PACK_WAGON"] = "보석 마차",
["MAP_INAPP_MORE_GEMS"] = "보석 구매",
["MAP_INAPP_TEXT_1"] = "보석 한 줌",
["MAP_INAPP_TEXT_2"] = "보석 자루",
["MAP_INAPP_TEXT_3"] = "보석 상자",
["MAP_INAPP_TEXT_4"] = "무료 보석",
["MAP_INAPP_TEXT_GEMS"] = "보석",
["MAP_NEW_GAMEMODE_UNLOCKED_DESCRIPTION"] = "무한으로 쏟아지는 적과 싸우며 최고 점수를 겨루세요!",
["MAP_NEW_GAMEMODE_UNLOCKED_TITLE"] = "새로운 도전!",
["MAP_NEW_HERO_ALERT"] = "새로운 영웅!",
["MAP_NEW_TOWER_ALERT"] = "새로운 타워!",
["MAP_TOWER_ROOM_SELECT"] = "장비",
["MAP_TOWER_ROOM_SELECTED"] = "장비됨",
["MEDIUM"] = "보통",
["MENU_HUD_WAVES"] = "%i/%i",
["MINUTES_ABBREVIATION"] = "분",
["MORE_GAMES"] = "다른 게임",
["Magic resistant enemies take less damage from mages."] = "마법 저항력이 있는 적은 마법사 공격의 피해를 더 적게 받습니다.",
["NEW"] = "신규",
["NEW SPECIAL POWER!"] = "새로운 특수 능력!",
["NEW TOWER UNLOCKED"] = "새 타워 사용 가능",
["NEW TOWER UPGRADES"] = "새 타워 업그레이드",
["NEWS"] = "새 소식",
["NEWS_ERROR"] = "연결할 수 없습니다. 인터넷 연결을 확인하고 나중에 다시 시도하세요.",
["NOTIFICATION_NEW_ENEMY_TITLE"] = "새로운 적",
["NOTIFICATION_NEW_SPECIAL_TITLE"] = "새로운 특수 능력!",
["NOTIFICATION_NEW_TOWERS_SUB_DESCRIPTION"] = "이제 타워를 레벨 %d까지 업그레이드할 수 있습니다.",
["NOTIFICATION_NEW_TOWERS_SUB_TITLE"] = "레벨 %d 타워 이용 가능",
["NOTIFICATION_NEW_TOWERS_TITLE"] = "새로운 타워 업그레이드",
["NOTIFICATION_NEW_TOWER_TITLE"] = "새로운 타워 해제",
["NOTIFICATION_armored_enemies_desc_body_1"] = "일부 적들은 다양한 강도의 갑옷을 착용하여 비마법 공격으로부터 보호받습니다.",
["NOTIFICATION_armored_enemies_desc_body_2"] = "이러한 피해에 저항합니다.",
["NOTIFICATION_armored_enemies_desc_body_3"] = "갑옷 입은 적은 궁수, 병영, 포병 타워로부터 피해를 덜 받습니다.",
["NOTIFICATION_armored_enemies_desc_title"] = "갑옷 입은 적!",
["NOTIFICATION_armored_enemies_enemy_name"] = "엄니 싸우꾼",
["NOTIFICATION_bottom_info_desc_body"] = "적의 정보는 유닛과 그 초상화를 탭하여 언제든지 확인할 수 있어요.",
["NOTIFICATION_bottom_info_desc_title"] = "적 정보",
["NOTIFICATION_bottom_info_tap_portrait_desc"] = "다시 열려면 여기를 탭하세요",
["NOTIFICATION_button_ok"] = "확인",
["NOTIFICATION_glare_desc_body"] = "오버시어는 전장을 타락한 눈으로 노려 보아 주변 적들에게 힘을 실어줍니다.",
["NOTIFICATION_glare_desc_bullets"] = "- 해당 지역에 있는 적을 치유합니다\n- 적의 고유 능력을 활성화합니다",
["NOTIFICATION_glare_desc_title"] = "오버시어의 성난 눈",
["NOTIFICATION_hero_desc"] = "레벨, 생명력, 경험치를 보여줍니다.",
["NOTIFICATION_hero_desc_baloon_1"] = "영웅이나 그의 초상화를 탭하여 선택하세요",
["NOTIFICATION_hero_desc_baloon_2"] = "길 위를 탭하거나 드래그하여 움직이세요",
["NOTIFICATION_hero_desc_body_1"] = "영웅은 강력한 적에 맞서고 아군을 지원하는 정예 용사입니다.",
["NOTIFICATION_hero_desc_body_2"] = "영웅은 적에게 피해를 주거나 능력을 사용하면 경험치를 얻습니다.",
["NOTIFICATION_hero_desc_title"] = "영웅을 지휘할 수 있습니다!",
["NOTIFICATION_magic_resistant_enemies_desc_body_1"] = "일부 적들은 다양한 수준의 마법 저항력을 가지고 있어 마법 공격으로부터 보호받습니다.",
["NOTIFICATION_magic_resistant_enemies_desc_body_2"] = "이러한 피해에 저항합니다.",
["NOTIFICATION_magic_resistant_enemies_desc_body_3"] = "마법저항 적들은 마법사 타워로부터 받는 피해가 적습니다.",
["NOTIFICATION_magic_resistant_enemies_desc_title"] = "마법저항 적!",
["NOTIFICATION_magic_resistant_enemies_enemy_name"] = "거북 주술사",
["NOTIFICATION_rally_point_desc_body_1"] = "병영의 집결지를 조정하여 유닛이 다른 지역을 방어하도록 만들 수 있습니다.",
["NOTIFICATION_rally_point_desc_body_2"] = "집결 지점을 선택하세요.",
["NOTIFICATION_rally_point_desc_body_3"] = "병사가 이동할 지점을 선택하세요.",
["NOTIFICATION_rally_point_desc_subtitle"] = "집결 범위",
["NOTIFICATION_rally_point_desc_title"] = "병력을 지휘하십시오!",
["NOTIFICATION_special_desc_body"] = "전장에서 당신을 도울 추가 병력을 소환할 수 있습니다.",
["NOTIFICATION_special_desc_bullets"] = "지원군은 적을 막는 데에 훌륭합니다.",
["NOTIFICATION_special_desc_title"] = "지원군 호출",
["NOTIFICATION_title_enemy"] = "적 정보",
["NOTIFICATION_title_glare"] = "새로운 팁!",
["NOTIFICATION_title_hint"] = "영웅 해제",
["NOTIFICATION_title_new_tip"] = "새로운 팁",
["NOTIFICATION_title_special"] = "특수 능력 해제",
["No"] = "아니요",
["No, Thanks"] = "평가하지 않겠습니다",
["None"] = "없음",
["Nope"] = "아니.",
["Normal"] = "일반",
["OFF!"] = "할인!",
["OFFERS_END"] = "혜택 만료까지:",
["OFFER_GET_IT_NOW"] = "지금 구매",
["OFFER_GET_THEM_NOW"] = "지금 구매",
["OFFER_ICON_BANNER"] = "판매",
["OFFER_OFF"] = "할인",
["OFFER_PACK_DESCRIPTION_ALL_HEROES"] = "모든 영웅을 지금 영입하십시오!",
["OFFER_PACK_DESCRIPTION_ALL_TOWERS"] = "모든 타워를 지금 획득하십시오!",
["OFFER_PACK_DESCRIPTION_TEXT_01"] = "이 특별한 기간 한정 혜택으로 부대를 강화하십시오!",
["OFFER_PACK_DESCRIPTION_TEXT_02"] = "이 특별 상품을 구매하십시오!",
["OFFER_PACK_TIMELEFT_TEXT"] = "혜택 만료까지:",
["OFFER_PACK_TITLE_01"] = "할로윈 특별 혜택",
["OFFER_PACK_TITLE_02"] = "블랙 프라이데이 특별 혜택",
["OFFER_PACK_TITLE_03"] = "크리스마스 특별 혜택",
["OFFER_PACK_TITLE_04"] = "새해 맞이 특별 혜택",
["OFFER_PACK_TITLE_05"] = "봄 축제 특별 혜택",
["OFFER_PACK_TITLE_06"] = "여름 할인 특별 혜택",
["OFFER_PACK_TITLE_07"] = "Ironhide의 날 특별 혜택",
["OFFER_PACK_TITLE_08"] = "스타터 팩 특별 혜택",
["OFFER_PACK_TITLE_09"] = "기간 한정 특별 혜택",
["OFFER_PACK_TITLE_ALL_HEROES"] = "메가 영웅 번들",
["OFFER_PACK_TITLE_ALL_TOWERS"] = "메가 타워 번들",
["OFFER_PACK_TITLE_STARTER_PACK"] = "스타터 팩 특별 혜택",
["OFFER_REGULAR"] = "일반 가격",
["ONE_TIME_OFFER"] = "1회 한정 특가!",
["ON_SALE"] = "세일",
["OPTIONS"] = "옵션",
["OPTIONS_PAGE_CONTROLS"] = "조작 설정",
["OPTIONS_PAGE_HELP"] = "도움말",
["OPTIONS_PAGE_SHORTCUTS"] = "키보드 도움말",
["OPTIONS_PAGE_VIDEO"] = "동영상",
["Objective"] = "목표",
["Over 50 stars are recommended to face this stage."] = "이 스테이지는 별 50개 이상을 획득한 후 도전하는 것이 좋습니다.",
["POPUP_CLEAR_PROGRESS_CONFIRM"] = "진행 상황을 초기화하시겠습니까?",
["POPUP_LABEL_MAIN_MENU"] = "메인 메뉴",
["POPUP_SETTINGS_LANGUAGE"] = "언어",
["POPUP_SETTINGS_MUSIC"] = "음악",
["POPUP_SETTINGS_SFX"] = "SFX",
["POPUP_label_error_msg"] = "이런! 오류가 발생했어요.",
["POPUP_label_error_msg2"] = "이런! 오류가 발생했어요.",
["POPUP_label_purchasing"] = "요청 처리 중",
["POPUP_label_title_options"] = "옵션",
["POPUP_label_version"] = "버전 0.0.8b",
["POWER_REINFORCEMENTS_NAME"] = "지원군",
["POWER_SUMMON_DESCRIPTION"] = "지원군을 부를 수 있습니다.",
["POWER_SUMMON_LARGE_DESCRIPTION"] = "전투를 도울 지원군을 부를 수 있습니다.\n\n지원군 소환은 무료이며 15초에 한 번씩 소환 가능합니다.",
["POWER_SUMMON_NAME"] = "지원군 호출",
["PRICE_FREE"] = "무료",
["PRIVACY_POLICY_ASK_AGE"] = "당신은 언제 태어났습니까?",
["PRIVACY_POLICY_BUTTON_LINK"] = "개인정보 취급방침",
["PRIVACY_POLICY_CONSENT_SHORT"] = "게임을 플레이하기 전에 귀하(귀하가 어린이 또는 청소년인 경우 귀하의 부모)가 당사의 개인정보 보호정책을 읽고 동의했는지 확인하십시오.",
["PRIVACY_POLICY_LINK"] = "개인정보 취급방침",
["PRIVACY_POLICY_WELCOME"] = "어서 오십시오!",
["PROCESSING ITEMS TO RESTORE"] = "복구 할 항목 처리",
["PROCESSING YOUR REQUEST"] = "요청 처리 중",
["PURCHASE_PENDING_MESSAGE"] = "구매가 보류 중이며 결제 또는 처리가 완료된 후 배송됩니다.",
["PUSH_NOTIFICATIONS_PERMISSION_RATIONALE"] = "아이언하이드의 제품 할인 및 신규 게임 알림을 받고 싶으신가요?",
["Produced by %s"] = "제작: %s",
["QUIT"] = "종료",
["Quit"] = "나가기",
["RESTORE"] = "복원",
["RESTORE_PURCHASES"] = "구매 복원",
["RESTORE_SLOT_ADD_GEMS_TITLE"] = "보석을 추가할 슬롯을 선택하세요",
["RESTORE_SLOT_PROGRESS_MSG"] = "서버에서 복원 데이터를 가져오는 중",
["RESTORE_SLOT_STATS_TITLE"] = "통계",
["RESTORE_SLOT_TITLE"] = "교체할 슬롯 선택",
["Rate %@"] = "%@ 평가하기",
["Remind me later"] = "다음에 평가하겠습니다",
["SALE_SCREEN_MAP_ROOMS"] = "세일",
["SECONDS_ABBREVIATION"] = "초",
["SETTINGS_LANGUAGE"] = "언어",
["SETTINGS_SUPPORT"] = "지원",
["SHOP_DESKTOP_GET_DLC_BUTTON"] = "가져가세요",
["SHOP_DESKTOP_TITLE"] = "상점",
["SHOP_ROOM_BEST_VALUE_TITLE"] = "최고 가치",
["SHOP_ROOM_DLC_1_DESCRIPTION"] = "이 새로운 서사적 모험을 시작하세요",
["SHOP_ROOM_DLC_1_TITLE"] = "거대한 위협 캠페인",
["SHOP_ROOM_DLC_1_TOOLTIP_DESCRIPTION"] = "5 신규 스테이지\n신규 타워\n신규 영웅\n10 가지 이상의 새로운 적들\n2 미니 보스\n장대한 보스전\n 그리고 더...",
["SHOP_ROOM_DLC_1_TOOLTIP_TITLE"] = "거대한 위협 캠페인",
["SHOP_ROOM_DLC_2_DESCRIPTION"] = "새로운 장대한 여정을 시작하세요",
["SHOP_ROOM_DLC_2_TITLE"] = "오공의 여정 캠페인",
["SHOP_ROOM_MOST_POPULAR_TITLE"] = "최고 인기",
["SLOT_CLOUD_DOWNLOADING"] = "다운로드 중...",
["SLOT_CLOUD_DOWNLOAD_FAILED"] = "iCloud에서 저장 데이터를 다운로드하지 못했습니다. 나중에 다시 시도하세요.",
["SLOT_CLOUD_DOWNLOAD_SUCCESSFUL"] = "다운로드를 마쳤습니다.",
["SLOT_CLOUD_UPLOADING"] = "업로드 중...",
["SLOT_CLOUD_UPLOAD_FAILED"] = "iCloud에 저장 데이터를 업로드하지 못했습니다. 나중에 다시 시도하세요.",
["SLOT_CLOUD_UPLOAD_ICLOUD_NOT_CONFIGURED"] = "기기에 iCloud가 구성되지 않았습니다.",
["SLOT_CLOUD_UPLOAD_SUCCESSFUL"] = "업로드를 마쳤습니다.",
["SLOT_DELETE_SLOT"] = "슬롯을 삭제할까요?",
["SLOT_NAME"] = "슬롯",
["SLOT_NEW_GAME"] = "새 게임",
["SOLDIER_ARBOREAN_BARRACK_NAME"] = "아르보리아 병사",
["SOLDIER_ARBOREAN_SENTINELS_1_NAME"] = "발루",
["SOLDIER_ARBOREAN_SENTINELS_2_NAME"] = "빌라",
["SOLDIER_ARBOREAN_SENTINELS_3_NAME"] = "이콘",
["SOLDIER_ARBOREAN_SENTINELS_4_NAME"] = "하비",
["SOLDIER_ARBOREAN_SENTINELS_5_NAME"] = "플룩",
["SOLDIER_ARBOREAN_SENTINELS_6_NAME"] = "굴드",
["SOLDIER_ARBOREAN_SENTINELS_7_NAME"] = "티나",
["SOLDIER_ARBOREAN_SENTINELS_8_NAME"] = "우즈키",
["SOLDIER_ARBOREAN_SENTINELS_9_NAME"] = "델루",
["SOLDIER_DRAGON_BONE_ULTIMATE_DOG_NAME"] = "본 드레이크",
["SOLDIER_EARTH_HOLDER_NAME"] = "석의 전사",
["SOLDIER_GHOST_TOWER_NAME"] = "망령",
["SOLDIER_HERO_BUILDER_WORKER_1_NAME"] = "헤마",
["SOLDIER_HERO_BUILDER_WORKER_2_NAME"] = "오툴",
["SOLDIER_HERO_BUILDER_WORKER_3_NAME"] = "크루즈",
["SOLDIER_HERO_BUILDER_WORKER_4_NAME"] = "버크",
["SOLDIER_HERO_BUILDER_WORKER_5_NAME"] = "라우크",
["SOLDIER_HERO_BUILDER_WORKER_6_NAME"] = "오네일",
["SOLDIER_HERO_BUILDER_WORKER_7_NAME"] = "호벨스",
["SOLDIER_HERO_BUILDER_WORKER_8_NAME"] = "우디",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL1_NAME"] = "아르보리아의 수호자",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL2_NAME"] = "아르보리아의 수호자",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL3_NAME"] = "아르보리아의 수호자",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL1_NAME"] = "아르보리아의 파라곤",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL2_NAME"] = "아르보리아의 파라곤",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL3_NAME"] = "아르보리아의 파라곤",
["SOLDIER_HERO_SPIDER_ULTIMATE_NAME"] = "거미 새끼",
["SOLDIER_HERO_WITCH_CAT_1_NAME"] = "코난",
["SOLDIER_HERO_WITCH_CAT_2_NAME"] = "알파호르",
["SOLDIER_HERO_WITCH_CAT_3_NAME"] = "바비에카",
["SOLDIER_HERO_WITCH_CAT_4_NAME"] = "펠루쉬",
["SOLDIER_HERO_WITCH_CAT_5_NAME"] = "파이퍼",
["SOLDIER_HERO_WITCH_CAT_6_NAME"] = "왓슨",
["SOLDIER_HERO_WITCH_CAT_7_NAME"] = "치미",
["SOLDIER_HERO_WITCH_CAT_8_NAME"] = "판투플라",
["SOLDIER_HERO_WITCH_DECOY_NAME"] = "랙돌",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_1_NAME"] = "산 위쿵",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_2_NAME"] = "손 워켕",
["SOLDIER_ITEM_SUMMON_BLACKBURN_NAME"] = "대군주 블랙번",
["SOLDIER_PALADINS_10_NAME"] = "요아킴 경",
["SOLDIER_PALADINS_11_NAME"] = "안드레 경",
["SOLDIER_PALADINS_12_NAME"] = "새밋 경",
["SOLDIER_PALADINS_13_NAME"] = "우도 경",
["SOLDIER_PALADINS_14_NAME"] = "에릭 경",
["SOLDIER_PALADINS_15_NAME"] = "브루스 경",
["SOLDIER_PALADINS_16_NAME"] = "롭 경",
["SOLDIER_PALADINS_17_NAME"] = "비프 경",
["SOLDIER_PALADINS_18_NAME"] = "보우즈 경",
["SOLDIER_PALADINS_1_NAME"] = "카이 경",
["SOLDIER_PALADINS_2_NAME"] = "핸시 경",
["SOLDIER_PALADINS_3_NAME"] = "루카 경",
["SOLDIER_PALADINS_4_NAME"] = "티모 경",
["SOLDIER_PALADINS_5_NAME"] = "랄프 경",
["SOLDIER_PALADINS_6_NAME"] = "토비아스 경",
["SOLDIER_PALADINS_7_NAME"] = "데리스 경",
["SOLDIER_PALADINS_8_NAME"] = "키스케 경",
["SOLDIER_PALADINS_9_NAME"] = "페쉬 경",
["SOLDIER_PRIESTS_BARRACK_1_NAME"] = "윌리",
["SOLDIER_PRIESTS_BARRACK_2_NAME"] = "헨리",
["SOLDIER_PRIESTS_BARRACK_3_NAME"] = "제프리",
["SOLDIER_PRIESTS_BARRACK_4_NAME"] = "니콜라스",
["SOLDIER_PRIESTS_BARRACK_5_NAME"] = "에드",
["SOLDIER_PRIESTS_BARRACK_6_NAME"] = "홉",
["SOLDIER_PRIESTS_BARRACK_7_NAME"] = "오도",
["SOLDIER_PRIESTS_BARRACK_8_NAME"] = "세드릭",
["SOLDIER_PRIESTS_BARRACK_9_NAME"] = "할",
["SOLDIER_RANDOM_10_NAME"] = "앨버스",
["SOLDIER_RANDOM_11_NAME"] = "보린",
["SOLDIER_RANDOM_12_NAME"] = "하드리안",
["SOLDIER_RANDOM_13_NAME"] = "토마스",
["SOLDIER_RANDOM_14_NAME"] = "헨리",
["SOLDIER_RANDOM_15_NAME"] = "브라이스",
["SOLDIER_RANDOM_16_NAME"] = "럴프",
["SOLDIER_RANDOM_17_NAME"] = "앨리스터",
["SOLDIER_RANDOM_18_NAME"] = "알테어",
["SOLDIER_RANDOM_19_NAME"] = "사이먼",
["SOLDIER_RANDOM_1_NAME"] = "더글러스",
["SOLDIER_RANDOM_20_NAME"] = "에그버트",
["SOLDIER_RANDOM_21_NAME"] = "엘든",
["SOLDIER_RANDOM_22_NAME"] = "개럿",
["SOLDIER_RANDOM_23_NAME"] = "고드윈",
["SOLDIER_RANDOM_24_NAME"] = "고든",
["SOLDIER_RANDOM_25_NAME"] = "제럴드",
["SOLDIER_RANDOM_26_NAME"] = "켈빈",
["SOLDIER_RANDOM_27_NAME"] = "랜도",
["SOLDIER_RANDOM_28_NAME"] = "매덕스",
["SOLDIER_RANDOM_29_NAME"] = "페이튼",
["SOLDIER_RANDOM_2_NAME"] = "댄 맥킬",
["SOLDIER_RANDOM_30_NAME"] = "램지",
["SOLDIER_RANDOM_31_NAME"] = "레이먼드",
["SOLDIER_RANDOM_32_NAME"] = "로버트",
["SOLDIER_RANDOM_33_NAME"] = "소여",
["SOLDIER_RANDOM_34_NAME"] = "실라스",
["SOLDIER_RANDOM_35_NAME"] = "스튜어트",
["SOLDIER_RANDOM_36_NAME"] = "태너",
["SOLDIER_RANDOM_37_NAME"] = "어셔",
["SOLDIER_RANDOM_38_NAME"] = "월레스",
["SOLDIER_RANDOM_39_NAME"] = "웨슬리",
["SOLDIER_RANDOM_3_NAME"] = "제임스 리",
["SOLDIER_RANDOM_40_NAME"] = "윌러드",
["SOLDIER_RANDOM_4_NAME"] = "자 조슨",
["SOLDIER_RANDOM_5_NAME"] = "필",
["SOLDIER_RANDOM_6_NAME"] = "로빈",
["SOLDIER_RANDOM_7_NAME"] = "윌리엄",
["SOLDIER_RANDOM_8_NAME"] = "마틴",
["SOLDIER_RANDOM_9_NAME"] = "아서",
["SOLDIER_REINFORCEMENTS_F_1_NAME"] = "아타이나",
["SOLDIER_REINFORCEMENTS_F_2_NAME"] = "마우실",
["SOLDIER_REINFORCEMENTS_F_3_NAME"] = "굴리카",
["SOLDIER_REINFORCEMENTS_F_4_NAME"] = "로가스",
["SOLDIER_REINFORCEMENTS_M_10_NAME"] = "포지",
["SOLDIER_REINFORCEMENTS_M_1_NAME"] = "가비니",
["SOLDIER_REINFORCEMENTS_M_2_NAME"] = "오벨",
["SOLDIER_REINFORCEMENTS_M_3_NAME"] = "켄트",
["SOLDIER_REINFORCEMENTS_M_4_NAME"] = "젠다스",
["SOLDIER_REINFORCEMENTS_M_5_NAME"] = "잘로스크",
["SOLDIER_REINFORCEMENTS_M_6_NAME"] = "아스통",
["SOLDIER_REINFORCEMENTS_M_7_NAME"] = "부이겔",
["SOLDIER_REINFORCEMENTS_M_8_NAME"] = "클레인",
["SOLDIER_REINFORCEMENTS_M_9_NAME"] = "마구스",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_1_NAME"] = "덴치",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_2_NAME"] = "스미스",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_3_NAME"] = "앤드류스",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_4_NAME"] = "톰슨",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_5_NAME"] = "테일러",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_1_NAME"] = "매카트니",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_2_NAME"] = "맥켈런",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_3_NAME"] = "홉킨스",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_4_NAME"] = "케인",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_5_NAME"] = "킹슬리",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_10_NAME"] = "바이퍼",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_1_NAME"] = "송곳니",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_2_NAME"] = "블레이드",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_3_NAME"] = "클로",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_4_NAME"] = "탈론",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_5_NAME"] = "엣지",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_6_NAME"] = "시브",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_7_NAME"] = "사이드",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_8_NAME"] = "대거",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_9_NAME"] = "스팅",
["SOLDIER_REINFORCEMENTS_SPECIAL_DARK_ARMY_1_NAME"] = "그림자 까마귀소환사",
["SOLDIER_REINFORCEMENTS_SPECIAL_LINIREA_1_NAME"] = "파라곤 기사",
["SOLDIER_STAGE_10_YMCA_BIKER_NAME"] = "글렌",
["SOLDIER_STAGE_10_YMCA_CONSTRUCTOR_NAME"] = "데이비드",
["SOLDIER_STAGE_10_YMCA_INDIO_NAME"] = "펠리페",
["SOLDIER_STAGE_10_YMCA_POLICIA_NAME"] = "빅터",
["SOLDIER_STAGE_15_DENAS_NAME"] = "킹 데나스",
["SOLDIER_TOWER_DARK_ELF_1_NAME"] = "필렌",
["SOLDIER_TOWER_DARK_ELF_2_NAME"] = "패릴",
["SOLDIER_TOWER_DARK_ELF_3_NAME"] = "구리나",
["SOLDIER_TOWER_DARK_ELF_4_NAME"] = "잘라스",
["SOLDIER_TOWER_DARK_ELF_5_NAME"] = "솔렌자르",
["SOLDIER_TOWER_DARK_ELF_6_NAME"] = "테브린",
["SOLDIER_TOWER_DARK_ELF_7_NAME"] = "비에르나",
["SOLDIER_TOWER_DARK_ELF_8_NAME"] = "진",
["SOLDIER_TOWER_DARK_ELF_9_NAME"] = "엘레라",
["SOLDIER_TOWER_DWARF_10_NAME"] = "바비",
["SOLDIER_TOWER_DWARF_1_NAME"] = "피피",
["SOLDIER_TOWER_DWARF_2_NAME"] = "기니",
["SOLDIER_TOWER_DWARF_3_NAME"] = "메리",
["SOLDIER_TOWER_DWARF_4_NAME"] = "로리",
["SOLDIER_TOWER_DWARF_5_NAME"] = "탈리",
["SOLDIER_TOWER_DWARF_6_NAME"] = "대니",
["SOLDIER_TOWER_DWARF_7_NAME"] = "게티",
["SOLDIER_TOWER_DWARF_8_NAME"] = "대피",
["SOLDIER_TOWER_DWARF_9_NAME"] = "비비",
["SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "엘렌딜",
["SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "퍽",
["SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "타스",
["SOLDIER_TOWER_ELVEN_BARRACK_4_NAME"] = "카스토어",
["SOLDIER_TOWER_ELVEN_BARRACK_5_NAME"] = "엘릭",
["SOLDIER_TOWER_ELVEN_BARRACK_6_NAME"] = "엘레이스",
["SOLDIER_TOWER_NECROMANCER_SKELETON_GOLEM_NAME"] = "뼈 골렘",
["SOLDIER_TOWER_NECROMANCER_SKELETON_NAME"] = "해골",
["SOLDIER_TOWER_PANDAS_FEMALE_1_NAME"] = "옌",
["SOLDIER_TOWER_PANDAS_FEMALE_2_NAME"] = "칭자오",
["SOLDIER_TOWER_PANDAS_FEMALE_3_NAME"] = "후이",
["SOLDIER_TOWER_PANDAS_FEMALE_4_NAME"] = "아이링",
["SOLDIER_TOWER_PANDAS_MALE_1_NAME"] = "쯔",
["SOLDIER_TOWER_PANDAS_MALE_2_NAME"] = "첸",
["SOLDIER_TOWER_PANDAS_MALE_3_NAME"] = "쉐친",
["SOLDIER_TOWER_PANDAS_MALE_4_NAME"] = "나이안",
["SOLDIER_TOWER_PANDAS_MALE_5_NAME"] = "쉰",
["SOLDIER_TOWER_PANDAS_MALE_6_NAME"] = "싱지앤",
["SOLDIER_TOWER_PANDAS_MALE_7_NAME"] = "웨이",
["SOLDIER_TOWER_PANDAS_MALE_8_NAME"] = "천",
["SOLDIER_TOWER_ROCKET_GUNNERS_10_NAME"] = "포터스",
["SOLDIER_TOWER_ROCKET_GUNNERS_1_NAME"] = "액슬",
["SOLDIER_TOWER_ROCKET_GUNNERS_2_NAME"] = "로즈",
["SOLDIER_TOWER_ROCKET_GUNNERS_3_NAME"] = "슬래시",
["SOLDIER_TOWER_ROCKET_GUNNERS_4_NAME"] = "허드슨",
["SOLDIER_TOWER_ROCKET_GUNNERS_5_NAME"] = "이지",
["SOLDIER_TOWER_ROCKET_GUNNERS_6_NAME"] = "더프",
["SOLDIER_TOWER_ROCKET_GUNNERS_7_NAME"] = "애들러",
["SOLDIER_TOWER_ROCKET_GUNNERS_8_NAME"] = "디지",
["SOLDIER_TOWER_ROCKET_GUNNERS_9_NAME"] = "페러",
["SOLDIER_ZHU_APPRENTICE_NAME"] = "Zhu Bajie",
["SPECIAL_ARBOREAN_BARRACK_DESCRIPTION"] = "길에 있는 적과 싸우는 3 명의 아르보리아 병사들을 소환합니다.",
["SPECIAL_ARBOREAN_BARRACK_NAME"] = "아르보리아 시민들",
["SPECIAL_ARBOREAN_HONEY_DESCRIPTION"] = "양봉가가 자리를 차지하여 그의 벌들이 끈적끈적한 꿀로 적을 느리게 하고 피해를 주도록 명령합니다!",
["SPECIAL_ARBOREAN_HONEY_NAME"] = "아르보리아 양봉가",
["SPECIAL_ARBOREAN_OLDTREE_DESCRIPTION"] = "심술궂은 친구가 적을 짓밟는 거대한 구르는 통나무를 길에 풉니다.",
["SPECIAL_ARBOREAN_OLDTREE_NAME"] = "오래된 나무",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_DESCRIPTION"] = "민첩한 숲의 보호자들입니다.",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_NAME"] = "아르보리아 가시창전사",
["SPECIAL_PRIESTS_SOLDIERS_DESCRIPTION"] = "눈먼 존재의 신봉자들。",
["SPECIAL_PRIESTS_SOLDIERS_NAME"] = "눈먼 광신도",
["SPECIAL_REPAIR_HOLDER_DRAGON_DESCRIPTION"] = "불길을 꺼서 즉시 타워를 해방시키세요.",
["SPECIAL_REPAIR_HOLDER_DRAGON_NAME"] = "불길에 휩싸인",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_DESCRIPTION"] = "타워 유닛의 체력을 증가시킴.\n주기적으로 돌 전사를 2명 소환함.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_NAME"] = "Elemental Holder: Earth",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_DESCRIPTION"] = "건설된 타워의 피해를 증가시킵니다.\n가끔 적을 즉시 처치합니다.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_NAME"] = "Elemental Holder: Fire",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_DESCRIPTION"] = "건설 비용을 줄입니다.\n적에게서 골드를 생성합니다.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_NAME"] = "Elemental Holder: Metal",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_DESCRIPTION"] = "근처 아군 유닛을 지속적으로 치유합니다.\n적을 경로 뒤쪽으로 순간이동시킵니다.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_NAME"] = "Elemental Holder: Water",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_DESCRIPTION"] = "건설된 타워의 사거리를 증가시킵니다.\n가끔 적을 느리게 만드는 뿌리가 잠시 생성됩니다.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_NAME"] = "Elemental Holder: Wood",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_DESCRIPTION"] = "잔해를 정리하면 이 전략 지점을 사용할 수 있습니다.",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_NAME"] = "잔해",
["SPECIAL_REPAIR_HOLDER_SPIDERS_DESCRIPTION"] = "거미줄에서 보유자를 해방하여 이 전략적 지점을 활성화하세요.",
["SPECIAL_REPAIR_HOLDER_SPIDERS_NAME"] = "거미줄에 갇힌 보유자",
["SPECIAL_REPAIR_OVERSEER_DESCRIPTION"] = "촉수를 물리치면 이 전략 지점을 해제할 수 있습니다.",
["SPECIAL_REPAIR_OVERSEER_NAME"] = "촉수",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_DESCRIPTION"] = "전투를 도와줄 엘프 용병을 고용할 수 있습니다. 10 초 마다 충원됩니다.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "엘프 용병대 I",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_DESCRIPTION"] = "전투를 도와줄 엘프 용병을 최대 2 명까지 고용할 수 있습니다. 10 초 마다 충원됩니다.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "엘프 용병대 II",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_DESCRIPTION"] = "전투를 도와줄 엘프 용병을 최대 3 명까지 고용할 수 있습니다. 10 초 마다 충원됩니다.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "엘프 용병대 III",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_1"] = "마법탄을 발사해 미드리아스의 환영을 파괴하고 몇 초 동안 그녀가 더 이상 만들어내지 못하게 합니다.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_2"] = "길을 걸으며 적과 싸우는 악마 파수병 2 명을 소환합니다.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_3"] = "데나스를 가두어, 움직이거나 공격하는 것을 막습니다.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_1"] = "영혼 충격",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_2"] = "지옥의 졸개들",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_3"] = "마법의 족쇄",
["START BATTLE!"] = "전투 시작!",
["START HERE!"] = "여기서 시작합니다!",
["STRATEGY BASICS!"] = "전략의 기본!",
["Select by tapping on the portrait or hero unit."] = "초상화나 영웅 유닛을 클릭해 선택합니다",
["Sell Tower"] = "타워 처분",
["Sell this tower and get a %s GP refund."] = "이 타워를 처분하면 %s 골드가 반환됩니다.",
["Shows level, health and experience."] = "레벨, 생명력, 경험치를 보여줍니다.",
["Special abilities"] = "특수 능력",
["Support your soldiers with ranged towers!"] = "원거리 타워로 병사들을 지원하세요!",
["Survival mode!"] = "생존 모드!",
["TAP_TO_START"] = "시작하려면 탭하세요",
["TAUNT_BOSS_PIG_FROM_POOL_0001"] = "널 비명 지르게 할 거야!",
["TAUNT_BOSS_PIG_FROM_POOL_0002"] = "‘베이컨’이라고 다시 말해봐. 어디 두 번!",
["TAUNT_BOSS_PIG_FROM_POOL_0003"] = "인간이 메뉴에 다시 올랐어, 친구들!",
["TAUNT_BOSS_PIG_FROM_POOL_0004"] = "빨리 해! 나 배고파.",
["TAUNT_BOSS_PIG_FROM_POOL_0005"] = "네가 죽는 걸 지켜보며 즐길 거야.",
["TAUNT_BOSS_PIG_FROM_POOL_0006"] = "알아, 나는 부어스트야.",
["TAUNT_LVL30_BOSS_ABILITY_01"] = "포식하라, 나의 자식들아!",
["TAUNT_LVL30_BOSS_ABILITY_02"] = "버텨봐! 무와하하하!",
["TAUNT_LVL30_BOSS_ABILITY_03"] = "교단을 위하여!",
["TAUNT_LVL30_BOSS_ABILITY_04"] = "모두에게 맛있는 식사를!",
["TAUNT_LVL30_BOSS_ABILITY_05"] = "내 거미 감각이 울리고 있어!",
["TAUNT_LVL30_BOSS_ABILITY_06"] = "내 앞에 무릎 꿇어라, 동맹이여!",
["TAUNT_LVL30_BOSS_ABILITY_07"] = "내 집, 내 규칙이다!",
["TAUNT_LVL30_BOSS_ABILITY_08"] = "아무도 내 거미줄을 벗어날 수 없어!",
["TAUNT_LVL30_BOSS_ABILITY_09"] = "죽어라, 인간 역병아!",
["TAUNT_LVL30_BOSS_ABILITY_10"] = "네 실을 당겨주지!",
["TAUNT_LVL30_BOSS_ABILITY_11"] = "전부 죽여라!",
["TAUNT_LVL30_BOSS_INTRO_01"] = "마침내 내 자매들의 학살자들이 왔군...",
["TAUNT_LVL30_BOSS_INTRO_02"] = "사렐가즈, 막탄즈... 복수의 시간이야...",
["TAUNT_LVL30_BOSS_INTRO_03"] = "이제 내가 이 세계의 새로운 신이다!",
["TAUNT_LVL30_BOSS_PREFIGHT_01"] = "이제 그만…",
["TAUNT_LVL30_BOSS_PREFIGHT_02"] = "너희는 하찮은 벌레에 불과하다…",
["TAUNT_LVL30_BOSS_PREFIGHT_03"] = "여왕의 거미줄에 걸렸군!",
["TAUNT_LVL32_BOSS_ABILITY_01"] = "멍청이들아! 나는 신의 불꽃, 삼매화를 다룬다!",
["TAUNT_LVL32_BOSS_ABILITY_02"] = "타오르는 불길이 하늘에서 솟구친다!",
["TAUNT_LVL32_BOSS_ABILITY_03"] = "진정한 불꽃의 순수한 형태를 두려워하라!",
["TAUNT_LVL32_BOSS_ABILITY_04"] = "육체도 영혼도 똑같이 탄다!",
["TAUNT_LVL32_BOSS_FIGHT_01"] = "내 안의 불꽃은 절대 꺼지지 않아!",
["TAUNT_LVL32_BOSS_FINAL_01"] = "내 불꽃은 꺼져가지만...\n아직 내 드래곤이 남아있다...",
["TAUNT_LVL32_BOSS_INTRO_01"] = "네가 군대를 가졌다고?",
["TAUNT_LVL32_BOSS_INTRO_02"] = "난 드래곤이 있어! 하하하하!",
["TAUNT_LVL32_BOSS_PREFIGHT_01"] = "그만! 이제 내가 이기는 부분이야!",
["TAUNT_LVL32_BOSS_PREFIGHT_02"] = "나의 진정한 모습을 보아라!",
["TAUNT_LVL34_BOSS_BOSSFIGHT_01"] = "좋아, 그럼 필요한 게 뭔지 알겠어. 나를 더! 나, 나, 나…",
["TAUNT_LVL34_BOSS_DEATH_01"] = "이럴 수가… 상관없어, 내 남편이 너희에게 대가를 치르게 할 거야…",
["TAUNT_LVL34_BOSS_INTRO_01"] = "이 원숭이들아! 내 아들에게 그런 짓을 해놓고 감히 여길 오다니?",
["TAUNT_LVL34_BOSS_WAVES_01"] = "내 힘을 맛보아라, 건방진 놈들아!",
["TAUNT_LVL34_BOSS_WAVES_02"] = "끝이 가까웠다!",
["TAUNT_STAGE02_RAELYN_0001"] = "이거 해보자.",
["TAUNT_STAGE02_VEZNAN_0001"] = "그들이 오고 있어. 내가 너의 하찮은 병력에 도움을 주겠다...",
["TAUNT_STAGE02_VEZNAN_0002"] = "...내 말은, 내 최고의 병사 중 한 명이 그럴 거라고. 하!",
["TAUNT_STAGE02_VEZNAN_0003"] = "하하하!",
["TAUNT_STAGE06_BOSS_PIG_PREBATTLE_0001"] = "좋아... 내가 직접 하겠어.",
["TAUNT_STAGE06_BOSS_PIG_RESPONSE_0001"] = "진정해, 모든 것이 통제 하에 있어.",
["TAUNT_STAGE06_CULTIST_GREETING_0001"] = "거기 아주 편안해 보이는군...",
["TAUNT_STAGE06_CULTIST_GREETING_0002"] = "...약속을 지키는 게 좋을 거야.",
["TAUNT_STAGE11_CULTIST_LEADER_0001"] = "여기까지 잘도 왔군...",
["TAUNT_STAGE11_CULTIST_LEADER_0002"] = "...하지만 필연을 막을 수는 없어!",
["TAUNT_STAGE11_CULTIST_LEADER_0003"] = "그만!!!",
["TAUNT_STAGE11_CULTIST_LEADER_0004"] = "우리 앞에서 네가 절을 할 시간이야!",
["TAUNT_STAGE11_CULTIST_LEADER_0005"] = "그르르... 이건 끝이 아니야!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0001"] = "우리를 기다리는 새로운 세계가 있어.",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0002"] = "내 힘을 얕보고 있군.",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0003"] = "오큘러스 포큘러스!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0004"] = "필연의 소리를 들어봐!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0005"] = "나는 악한가? 그래, 맞아!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0006"] = "오버시어께서 우리를 축복하신다!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0001"] = "너의 종말이 다가왔다!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0002"] = "나는 눈을 뜨게 되었다!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0003"] = "내 공허한 친구들에게 인사해!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0004"] = "오큘러스 포큘러스!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0005"] = "한심하고 약해빠진 쓰레기!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0006"] = "오버시어께서 우리를 축복하신다!",
["TAUNT_STAGE11_VEZNAN_0001"] = "데나스, 내 친구야. 오랜만이야!",
["TAUNT_STAGE15_CULTIST_0001"] = "가까워... 깨어나는 것을 느낄 수 있어!",
["TAUNT_STAGE15_CULTIST_0002"] = "새로운 시대가 다가오고 있다. 너의 노력은 헛되리라!",
["TAUNT_STAGE15_CULTIST_0003"] = "그르르... 너의 동맹은 강력하다.",
["TAUNT_STAGE15_CULTIST_0004"] = "하지만 진정한 힘이 무엇인지 보여주겠다!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0001"] = "바보들! 죽으러 왔구나.",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0002"] = "그 시선 앞에 항복하라!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0003"] = "너는 참된 신자가 될 것이다.",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0004"] = "동맹이든 아니든, 너는 끝장이다!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0005"] = "공허에는 생명이 없다. 오직 죽음뿐",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0006"] = "내 시간을 낭비하지 마!",
["TAUNT_STAGE15_DENAS_0001"] = "난 해결해야 할 문제가 있다. 이 싸움은 절대 놓치지 않을 거야!",
["TAUNT_STAGE16_DENAS_AFTER_BOSSFIGHT_0001"] = "예상 못했지, 그렇지? ",
["TAUNT_STAGE18_ERIDAN_FIGHT_0001"] = "오늘밤엔 유혈이 낭자하군.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0002"] = "우리는 엘리니님을 믿는다.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0003"] = "닐루르 스피크 에디노리!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0004"] = "나는 빗나가는 법이 없군.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0005"] = "아레델이 승리할 것이다!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0006"] = "이들은 단순한 레인저가 아니야!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0007"] = "점수를 기록하고 있나?",
["TAUNT_STAGE18_ERIDAN_FIGHT_0008"] = "오라고 해!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0001"] = "나의 활도 함께할 것이다!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0002"] = "신속히 행동해!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0003"] = "각자의 위치로!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0004"] = "눈을 크게 뜨고!",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0001"] = "워밍업은 이걸로 충분해!",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0002"] = "네 스스로 골칫거리임을 증명했군...",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0003"] = "진짜 싸움을 시작하지!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0001"] = "나는 모든 영혼을 지배한다!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0002"] = "엘프들이 다시 일어날 것이다!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0003"] = "나는 심지어... 죽은 자도 소생시키지!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0004"] = "고대의 불경한 힘으로!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0005"] = "무덤의 내 자식들을 두려워하라!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0006"] = "내 백성에게 영광을 되찾아 주리라!",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0001"] = "아, 강력한 얼라이언스가 방문했군.",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0002"] = "이제 베일을 벗을 시간이야!",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0003"] = "죽음의 힘을 보여주겠다!",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0001"] = "드디어 마음대로 먹겠군",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0002"] = "모든 것을!!!!!",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0001"] = "이제 그만 참견해라!",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0002"] = "그림비어드가 너희에게 예절을 가르쳐주겠다.",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0003"] = "전원 탑승, 아하하하!",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0001"] = "건방진 얼간이들!",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0002"] = "너희는 절대 날 잡을 수 없을 것이다, 하하하!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "안 돼! 아직 더 있어…",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "세상에 이럴수가!!!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0001"] = "너희는 이 군대와 맞설 수 없다!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0002"] = "그림비어드는 위험에 처해있지 않아.",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0003"] = "그림비어드가 바로 위험이다!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0004"] = "미치광이가 이런 일을 할 수 있을 것 같으냐?",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0005"] = "세상은 그림비어드 앞에 무릎을 꿇을 것이다!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0001"] = "그림비어드의 인내심이 한계에 다다랐다.",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0002"] = "이제 정말 대단한 것을 보게 될 것이다!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0003"] = "그림비어드는 자신만으로 충분하다!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0004"] = "서둘러 주시겠습니까?!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "너희와 그 빌어먹을 간섭하는 동맹!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "건드리면 안된다는 걸 알려주지...",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0003"] = "“메인” 드워프를!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0001"] = "클론들을 아무리 짓밟아도 상관없다, 나는 더 많이 만들 테니까.",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0002"] = "일을 제대로 하고 싶으면 스스로 해야 한다.",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0003"] = "오, 그림비어드, 너는 정말 천재야!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0004"] = "이대로 넘어갈 것 같나!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0005"] = "진심으로 노력은 하고 있는 건가?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0006"] = "내 창조물을 능가할 수 있다고 생각하나?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0001"] = "아무래도 나를 정말 좋아하는 것 같군…",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0002"] = "...이제 \"최고의 드워프\"에게 도전하고 싶다고?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0003"] = "마음껏 시도해 보아라.",
["TAUNT_TUTORIAL_ARBOREAN_ALL_0001"] = "계속하시게! 우리는 당신들을 믿소.",
["TAUNT_TUTORIAL_ARBOREAN_BARRACK_0001"] = "여기에 병영을 지으세요!",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "림블리엄",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "촉수 헨리",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "촉수 제프리",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "텐타클라스",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "테드태클",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "홀림",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "텐토도",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "림드릭",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "할림",
["TERMS_OF_SERVICE_LINK"] = "서비스 약관",
["TIP_TITLE"] = "팁:",
["TOWER_ARBOREAN_EMISSARY_1_DESCRIPTION"] = "아르보리아인들은 강력한 자연 마법을 사용하여 적을 더욱 취약하게 만듭니다.",
["TOWER_ARBOREAN_EMISSARY_1_NAME"] = "아르보리아 사절단 I",
["TOWER_ARBOREAN_EMISSARY_2_DESCRIPTION"] = "아르보리아인들은 강력한 자연 마법을 사용하여 적을 더욱 취약하게 만듭니다.",
["TOWER_ARBOREAN_EMISSARY_2_NAME"] = "아르보리아 사절단 II",
["TOWER_ARBOREAN_EMISSARY_3_DESCRIPTION"] = "아르보리아인들은 강력한 자연 마법을 사용하여 적을 더욱 취약하게 만듭니다.",
["TOWER_ARBOREAN_EMISSARY_3_NAME"] = "아르보리아 사절단 III",
["TOWER_ARBOREAN_EMISSARY_4_DESCRIPTION"] = "아르보리아인들은 강력한 자연 마법을 사용하여 적을 더욱 취약하게 만듭니다.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_DESCRIPTION"] = "지역 내의 아군의 체력을 초당 %$towers.arborean_emissary.gift_of_nature.s_heal[1]%$만큼 %$towers.arborean_emissary.gift_of_nature.duration[1]%$ 초 동안 치유하는 위스프들을 소환합니다. ",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_NAME"] = "자연의 선물",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_DESCRIPTION"] = "지역 내의 아군의 체력을 초당 %$towers.arborean_emissary.gift_of_nature.s_heal[2]%$만큼 %$towers.arborean_emissary.gift_of_nature.duration[2]%$ 초 동안 치유하는 위스프들을 소환합니다. ",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_NAME"] = "자연의 선물",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_DESCRIPTION"] = "지역 내의 아군의 체력을 초당 %$towers.arborean_emissary.gift_of_nature.s_heal[3]%$만큼 %$towers.arborean_emissary.gift_of_nature.duration[3]%$ 초 동안 치유하는 위스프들을 소환합니다. ",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_NAME"] = "자연의 선물",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NAME"] = "자연의 선물",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NOTE"] = "절대로 그린에게 간섭하지 마.",
["TOWER_ARBOREAN_EMISSARY_4_NAME"] = "아르보리아 사절단 IV",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_DESCRIPTION"] = "길을 따라 %$towers.arborean_emissary.wave_of_roots.max_targets[1]%$ 개의 뿌리가 자라나 %$towers.arborean_emissary.wave_of_roots.s_damage[1]%$의 무속성 피해를 주고 %$towers.arborean_emissary.wave_of_roots.mod_duration[1]%$ 초 동안 기절시킵니다.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_NAME"] = "가시덤불 손아귀",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_DESCRIPTION"] = "길을 따라 %$towers.arborean_emissary.wave_of_roots.max_targets[2]%$ 개의 뿌리가 자라나 %$towers.arborean_emissary.wave_of_roots.s_damage[2]%$의 무속성 피해를 주고 %$towers.arborean_emissary.wave_of_roots.mod_duration[2]%$ 초 동안 기절시킵니다.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_NAME"] = "가시덤불 손아귀",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_DESCRIPTION"] = "길을 따라 %$towers.arborean_emissary.wave_of_roots.max_targets[3]%$ 개의 뿌리가 자라나 %$towers.arborean_emissary.wave_of_roots.s_damage[3]%$의 무속성 피해를 주고 %$towers.arborean_emissary.wave_of_roots.mod_duration[3]%$ 초 동안 기절시킵니다.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_NAME"] = "가시덤불 손아귀",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NAME"] = "가시덤불 손아귀",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NOTE"] = "발 조심해.",
["TOWER_ARBOREAN_EMISSARY_DESC"] = "평화적인 아르보리아인들은 도발을 받으면 마법을 사용하여 적에게 표식을 남겨 약화시키는 것으로 알려져 있습니다.",
["TOWER_ARBOREAN_EMISSARY_NAME"] = "아르보리아 사절단",
["TOWER_ARBOREAN_SENTINELS_DESCRIPTION"] = "민첩한 숲의 보호자들입니다.",
["TOWER_ARBOREAN_SENTINELS_NAME"] = "아르보리아 가시창전사",
["TOWER_ARCANE_WIZARD_1_DESCRIPTION"] = "마법에 정통한 이 마법사들은 언제나 싸울 준비가 되어 있습니다.",
["TOWER_ARCANE_WIZARD_1_NAME"] = "비전 마법사 I",
["TOWER_ARCANE_WIZARD_2_DESCRIPTION"] = "마법에 정통한 이 마법사들은 언제나 싸울 준비가 되어 있습니다.",
["TOWER_ARCANE_WIZARD_2_NAME"] = "비전 마법사 II",
["TOWER_ARCANE_WIZARD_3_DESCRIPTION"] = "마법에 정통한 이 마법사들은 언제나 싸울 준비가 되어 있습니다.",
["TOWER_ARCANE_WIZARD_3_NAME"] = "비전 마법사 III",
["TOWER_ARCANE_WIZARD_4_DESCRIPTION"] = "마법에 정통한 이 마법사들은 언제나 싸울 준비가 되어 있습니다.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_DESCRIPTION"] = "대상을 순식간에 죽이는 광선을 발사합니다. 대신에 보스와 미니 보스에게는 %$towers.arcane_wizard.disintegrate.boss_damage[1]%$의 마법 피해를 입힙니다",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_NAME"] = "분해",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_DESCRIPTION"] = "사용 대기 시간이 %$towers.arcane_wizard.disintegrate.cooldown[2]%$ 초로 감소합니다. 보스와 미니 보스에게 %$towers.arcane_wizard.disintegrate.boss_damage[2]%$의 피해를 입힙니다.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_NAME"] = "분해",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_DESCRIPTION"] = "사용 대기 시간이 %$towers.arcane_wizard.disintegrate.cooldown[3]%$ 초로 감소합니다. 보스와 미니 보스에게 %$towers.arcane_wizard.disintegrate.boss_damage[3]%$의 피해를 입힙니다.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_NAME"] = "분해",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NAME"] = "분해",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NOTE"] = "먼지에서 먼지로.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_DESCRIPTION"] = "가까이 있는 타워들의 피해가 %$towers.arcane_wizard.empowerment.s_damage_factor[1]%$% 증가합니다.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_NAME"] = "역량 강화",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_DESCRIPTION"] = "가까이 있는 타워들의 피해가 %$towers.arcane_wizard.empowerment.s_damage_factor[2]%$% 증가합니다.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_NAME"] = "역량 강화",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_DESCRIPTION"] = "가까이 있는 타워들의 피해가 %$towers.arcane_wizard.empowerment.s_damage_factor[3]%$% 증가합니다.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_NAME"] = "역량 강화",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NAME"] = "역량 강화",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NOTE"] = "무한한 힘.",
["TOWER_ARCANE_WIZARD_4_NAME"] = "비전 마법사 IV",
["TOWER_ARCANE_WIZARD_DESC"] = "순수 마법을 활용하여 리니리아 마법사들은 적을 완전히 파괴할 만큼 충분한 힘을 발휘합니다.",
["TOWER_ARCANE_WIZARD_NAME"] = "비전 마법사",
["TOWER_BALLISTA_1_DESCRIPTION"] = "그린스킨들의 전투에 큰 힘이 됩니다, 아직 망가지지 않은 게 기적이지만.",
["TOWER_BALLISTA_1_NAME"] = "발리스타 초소 I",
["TOWER_BALLISTA_2_DESCRIPTION"] = "그린스킨들의 전투에 큰 힘이 됩니다, 아직 망가지지 않은 게 기적이지만.",
["TOWER_BALLISTA_2_NAME"] = "발리스타 초소 II",
["TOWER_BALLISTA_3_DESCRIPTION"] = "그린스킨들의 전투에 큰 힘이 됩니다, 아직 망가지지 않은 게 기적이지만.",
["TOWER_BALLISTA_3_NAME"] = "발리스타 초소 III",
["TOWER_BALLISTA_4_DESCRIPTION"] = "그린스킨들의 전투에 큰 힘이 됩니다, 아직 망가지지 않은 게 기적이지만.",
["TOWER_BALLISTA_4_NAME"] = "발리스타 초소 IV",
["TOWER_BALLISTA_4_SKILL_BOMB_1_DESCRIPTION"] = "고철로 만든 장거리 폭탄을 발사하여 %$towers.ballista.skill_bomb.damage_min[1]%$-%$towers.ballista.skill_bomb.damage_max[1]%$의 물리 피해를 주고 %$towers.ballista.skill_bomb.duration[1]%$ 초 동안 적의 속도를 늦춥니다.",
["TOWER_BALLISTA_4_SKILL_BOMB_1_NAME"] = "고철 폭탄",
["TOWER_BALLISTA_4_SKILL_BOMB_2_DESCRIPTION"] = "고철 폭탄은 %$towers.ballista.skill_bomb.damage_min[2]%$-%$towers.ballista.skill_bomb.damage_max[2]%$의 물리 피해를 주고 %$towers.ballista.skill_bomb.duration[1]%$ 초 동안 적의 속도를 늦춥니다.",
["TOWER_BALLISTA_4_SKILL_BOMB_2_NAME"] = "고철 폭탄",
["TOWER_BALLISTA_4_SKILL_BOMB_3_DESCRIPTION"] = "고철 폭탄은 %$towers.ballista.skill_bomb.damage_min[3]%$-%$towers.ballista.skill_bomb.damage_max[3]%$의 물리 피해를 주고 %$towers.ballista.skill_bomb.duration[1]%$ 초 동안 적의 속도를 늦춥니다.",
["TOWER_BALLISTA_4_SKILL_BOMB_3_NAME"] = "고철 폭탄",
["TOWER_BALLISTA_4_SKILL_BOMB_NOTE"] = "앞으로!",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_DESCRIPTION"] = "타워의 마지막 사격은 %$towers.ballista.skill_final_shot.s_damage_factor[1]%$% 더 많은 피해를 주며 대상을 %$towers.ballista.skill_final_shot.s_stun%$초 동안 기절시킵니다.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_NAME"] = "마지막 결정타",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_DESCRIPTION"] = "마지막 사격은 %$towers.ballista.skill_final_shot.s_damage_factor[2]%$% 더 많은 피해를 주며 대상을 %$towers.ballista.skill_final_shot.s_stun%$초 동안 기절시킵니다.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_NAME"] = "마지막 결정타",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_DESCRIPTION"] = "마지막 사격은 %$towers.ballista.skill_final_shot.s_damage_factor[3]%$% 더 많은 피해를 주며 대상을 %$towers.ballista.skill_final_shot.s_stun%$초 동안 기절시킵니다.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_NAME"] = "마지막 결정타",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_NOTE"] = "그건 백만 분의 일이었어, 애송이!",
["TOWER_BALLISTA_DESC"] = "전쟁에 대한 지나친 열정으로, 고블린들은 다시 활을 들 필요가 없도록 추가 노력을 기울였습니다.",
["TOWER_BALLISTA_NAME"] = "발리스타 초소",
["TOWER_BARREL_1_DESCRIPTION"] = "북방인들의 물약 상자는 적의 무리에 대항하는 강력한 무기입니다.",
["TOWER_BARREL_1_NAME"] = "전투 양조장 I",
["TOWER_BARREL_2_DESCRIPTION"] = "북방인들의 물약 상자는 적의 무리에 대항하는 강력한 무기입니다.",
["TOWER_BARREL_2_NAME"] = "전투 양조장 II",
["TOWER_BARREL_3_DESCRIPTION"] = "북방인들의 물약 상자는 적의 무리에 대항하는 강력한 무기입니다.",
["TOWER_BARREL_3_NAME"] = "전투 양조장 III",
["TOWER_BARREL_4_DESCRIPTION"] = "북방인들의 물약 상자는 적의 무리에 대항하는 강력한 무기입니다.",
["TOWER_BARREL_4_NAME"] = "전투 양조장 IV",
["TOWER_BARREL_4_SKILL_BARREL_1_DESCRIPTION"] = "독성 배럴을 던져 %$towers.barrel.skill_barrel.explosion.damage_min[1]%$-%$towers.barrel.skill_barrel.explosion.damage_max[1]%$의 물리 피해를 입히고 독을 남겨 %$towers.barrel.skill_barrel.poison.duration%$ 초 동안 초당 %$towers.barrel.skill_barrel.poison.s_damage%$의 무속성 피해를 줍니다.",
["TOWER_BARREL_4_SKILL_BARREL_1_NAME"] = "불량 제조",
["TOWER_BARREL_4_SKILL_BARREL_2_DESCRIPTION"] = "독 배럴의 폭발은 %$towers.barrel.skill_barrel.explosion.damage_min[2]%$-%$towers.barrel.skill_barrel.explosion.damage_max[2]%$의 물리 피해를 입힙니다. 배럴의 독은 %$towers.barrel.skill_barrel.poison.duration%$ 초 동안 매초 %$towers.barrel.skill_barrel.poison.s_damage%$의 무속성 피해를 줍니다.",
["TOWER_BARREL_4_SKILL_BARREL_2_NAME"] = "불량 제조",
["TOWER_BARREL_4_SKILL_BARREL_3_DESCRIPTION"] = "독 배럴의 폭발은 %$towers.barrel.skill_barrel.explosion.damage_min[3]%$-%$towers.barrel.skill_barrel.explosion.damage_max[3]%$의 물리 피해를 입힙니다. 배럴의 독은 %$towers.barrel.skill_barrel.poison.duration%$ 초 동안 매초 %$towers.barrel.skill_barrel.poison.s_damage%$의 무속성 피해를 줍니다.",
["TOWER_BARREL_4_SKILL_BARREL_3_NAME"] = "불량 제조",
["TOWER_BARREL_4_SKILL_BARREL_NOTE"] = "용감한 자만이!",
["TOWER_BARREL_4_SKILL_WARRIOR_1_DESCRIPTION"] = "경로에서 싸울 강화된 전사를 소환합니다. %$towers.barrel.skill_warrior.entity.hp_max[1]%$의 생명력을 가지며 %$towers.barrel.skill_warrior.entity.damage_min[1]%$-%$towers.barrel.skill_warrior.entity.damage_max[1]%$의 물리 피해를 입힙니다. ",
["TOWER_BARREL_4_SKILL_WARRIOR_1_NAME"] = "힘의 엘릭서",
["TOWER_BARREL_4_SKILL_WARRIOR_2_DESCRIPTION"] = "전사는 %$towers.barrel.skill_warrior.entity.hp_max[2]%$의 생명력을 가지며 %$towers.barrel.skill_warrior.entity.damage_min[2]%$-%$towers.barrel.skill_warrior.entity.damage_max[2]%$의 물리 피해를 입힙니다. 전사는 %$towers.barrel.skill_warrior.entity.duration%$ 초 동안 소환됩니다.",
["TOWER_BARREL_4_SKILL_WARRIOR_2_NAME"] = "힘의 엘릭서",
["TOWER_BARREL_4_SKILL_WARRIOR_3_DESCRIPTION"] = "전사는 %$towers.barrel.skill_warrior.entity.hp_max[3]%$의 생명력을 가지며 %$towers.barrel.skill_warrior.entity.damage_min[3]%$-%$towers.barrel.skill_warrior.entity.damage_max[3]%$의 물리 피해를 입힙니다. 전사는 %$towers.barrel.skill_warrior.entity.duration%$ 초 동안 소환됩니다.",
["TOWER_BARREL_4_SKILL_WARRIOR_3_NAME"] = "힘의 엘릭서",
["TOWER_BARREL_4_SKILL_WARRIOR_NOTE"] = "승리의 맛이야!",
["TOWER_BARREL_DESC"] = "북쪽 사람들은 포션 제작의 기술에 능숙하며, 전투에서 적과 싸우기 위해 그들의 음료를 사용합니다.",
["TOWER_BARREL_NAME"] = "전투 양조장",
["TOWER_BARREL_WARRIOR_NAME"] = "하프단 더 블런트",
["TOWER_BROKEN_DESCRIPTION"] = "이 타워는 손상되었습니다. 골드를 사용하여 수리하십시오.",
["TOWER_BROKEN_NAME"] = "손상된 타워",
["TOWER_CROCS_EATEN_DESCRIPTION"] = "마법으로 타워를 원래 형태로 복원합니다.",
["TOWER_CROCS_EATEN_NAME"] = "타워 잔해",
["TOWER_DARK_ELF_1_DESCRIPTION"] = "적의 거리나 힘에 상관없이 조준은 항상 정확합니다.",
["TOWER_DARK_ELF_1_NAME"] = "황혼의 장궁 I",
["TOWER_DARK_ELF_2_DESCRIPTION"] = "적의 거리나 힘에 상관없이 조준은 항상 정확합니다.",
["TOWER_DARK_ELF_2_NAME"] = "황혼의 장궁 II",
["TOWER_DARK_ELF_3_DESCRIPTION"] = "적의 거리나 힘에 상관없이 조준은 항상 정확합니다.",
["TOWER_DARK_ELF_3_NAME"] = "황혼의 장궁 III",
["TOWER_DARK_ELF_4_DESCRIPTION"] = "적의 거리나 힘에 상관없이 조준은 항상 정확합니다.",
["TOWER_DARK_ELF_4_NAME"] = "황혼의 장궁 IV",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_DESCRIPTION"] = "적을 처치할 때마다 타워의 공격력이 %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$만큼 증가합니다.",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_NAME"] = "사냥의 짜릿함",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_DESCRIPTION"] = "적을 처치할 때마다 타워의 공격력이 %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$만큼 증가합니다.",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_NAME"] = "사냥의 짜릿함",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_DESCRIPTION"] = "적을 처치할 때마다 타워의 공격력이 %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$만큼 증가합니다.",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_NAME"] = "사냥의 짜릿함",
["TOWER_DARK_ELF_4_SKILL_BUFF_NOTE"] = "탈리호!",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_DESCRIPTION"] = "황혼 암살자 두 명을 소환합니다. 이들은 %$towers.dark_elf.soldier.hp[1]%$의 생명력을 가지고 있으며 %$towers.dark_elf.soldier.basic_attack.damage_min[1]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[1]%$의 물리 피해를 입힙니다.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_NAME"] = "지원 칼날",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_DESCRIPTION"] = "황혼 암살자들은 이제 %$towers.dark_elf.soldier.hp[2]%$의 생명력을 가지며 %$towers.dark_elf.soldier.basic_attack.damage_min[2]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[2]%$ 물리 피해를 입힙니다.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_NAME"] = "지원 칼날",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_DESCRIPTION"] = "황혼 암살자들은 이제 %$towers.dark_elf.soldier.hp[3]%$의 생명력을 가지며 %$towers.dark_elf.soldier.basic_attack.damage_min[3]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[3]%$ 물리 피해를 입힙니다.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_NAME"] = "지원 칼날",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_NOTE"] = "그들이 놀러 온다.",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_DESCRIPTION"] = "타워의 초점을 출구에 가장 가까운 적으로 변경합니다.",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NAME"] = "적 초점: 맨 앞",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NOTE"] = "통과하지 못하게 하라!",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_DESCRIPTION"] = "타워의 초점을 생명력이 가장 많은 적으로 변경합니다.",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NAME"] = "적 초점: 최대 생명력",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NOTE"] = "큰 걸로 가자!",
["TOWER_DARK_ELF_DESC"] = "암흑 에너지로 강화된 사격으로 원거리에서 강력한 적을 사냥하는 데 특화되어 있는 궁수들입니다.",
["TOWER_DARK_ELF_NAME"] = "황혼의 장궁",
["TOWER_DEMON_PIT_1_DESCRIPTION"] = "짓궂고 위험한 이 악마들은 항상 골칫거리를 찾고 있습니다.",
["TOWER_DEMON_PIT_1_NAME"] = "악마 구덩이 I",
["TOWER_DEMON_PIT_2_DESCRIPTION"] = "짓궂고 위험한 이 악마들은 항상 골칫거리를 찾고 있습니다.",
["TOWER_DEMON_PIT_2_NAME"] = "악마 구덩이 II",
["TOWER_DEMON_PIT_3_DESCRIPTION"] = "짓궂고 위험한 이 악마들은 항상 골칫거리를 찾고 있습니다.",
["TOWER_DEMON_PIT_3_NAME"] = "악마 구덩이 III",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_DESCRIPTION"] = "%$towers.demon_pit.big_guy.hp_max[1]%$의 생명력을 가지고 %$towers.demon_pit.big_guy.melee_attack.damage_min[1]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[1]%$의 물리 피해를 주는 거대 임프를 생성합니다. 폭발 시 %$towers.demon_pit.big_guy.explosion_damage[1]%$의 피해를 줍니다.",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_NAME"] = "두목",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_DESCRIPTION"] = "빅 임프는 %$towers.demon_pit.big_guy.hp_max[2]%$의 생명력을 가지고 %$towers.demon_pit.big_guy.melee_attack.damage_min[2]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[2]%$의 물리 피해를 줍니다. 폭발은 %$towers.demon_pit.big_guy.explosion_damage[2]%$의 피해를 입힙다.",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_NAME"] = "두목",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_DESCRIPTION"] = "빅 임프는 %$towers.demon_pit.big_guy.hp_max[3]%$의 생명력을 가지고 %$towers.demon_pit.big_guy.melee_attack.damage_min[3]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[3]%$의 물리 피해를 줍니다. 폭발은 %$towers.demon_pit.big_guy.explosion_damage[3]%$의 피해를 입힙니다.",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_NAME"] = "두목",
["TOWER_DEMON_PIT_4_BIG_DEMON_NAME"] = "두목",
["TOWER_DEMON_PIT_4_BIG_DEMON_NOTE"] = "좀 진정하려고 노력중이야.",
["TOWER_DEMON_PIT_4_DESCRIPTION"] = "짓궂고 위험한 이 악마들은 항상 골칫거리를 찾고 있습니다.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_DESCRIPTION"] = "임프의 폭발이 %$towers.demon_pit.master_exploders.s_damage_increase[1]%$% 더 많은 피해를 주며 화상을 입혀 %$towers.demon_pit.master_exploders.s_burning_duration[1]%$ 초 동안 초당 %$towers.demon_pit.master_exploders.s_total_burning_damage_min[1]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[1]%$의 무속성 피해를 줍니다.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_NAME"] = "폭발 마스터",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_DESCRIPTION"] = "임프의 폭발이 %$towers.demon_pit.master_exploders.s_damage_increase[2]%$% 더 많은 피해를 줍니다. 화상은 %$towers.demon_pit.master_exploders.s_burning_duration[2]%$ 초 동안 초당 %$towers.demon_pit.master_exploders.s_total_burning_damage_min[2]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[2]%$의 무속성 피해를  줍니다.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_NAME"] = "폭발 마스터",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_DESCRIPTION"] = "임프의 폭발이 %$towers.demon_pit.master_exploders.s_damage_increase[3]%$% 더 많은 피해를 줍니다. 화상은 %$towers.demon_pit.master_exploders.s_burning_duration[3]%$ 초 동안 초당 %$towers.demon_pit.master_exploders.s_total_burning_damage_min[3]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[3]%$의 무속성 피해를 줍니다.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_NAME"] = "폭발 마스터",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NAME"] = "폭발 마스터",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NOTE"] = "이 일을 하는 건 바보뿐이다.",
["TOWER_DEMON_PIT_4_NAME"] = "악마 구덩이 IV",
["TOWER_DEMON_PIT_DESC"] = "용암의 저 깊은 곳에서 태어난 이 임프들은 주저 없이 적의 경로에 뛰어듭니다.",
["TOWER_DEMON_PIT_NAME"] = "악마 구덩이",
["TOWER_DEMON_PIT_SOLDIER_BIG_GUY_NAME"] = "빅 가이",
["TOWER_DEMON_PIT_SOLDIER_NAME"] = "임프",
["TOWER_DWARF_1_DESCRIPTION"] = "도화선만큼 짧지만, 그들의 방어선을 살아서 통과할 수 있는 것은 없습니다.",
["TOWER_DWARF_1_NAME"] = "포병 분대I",
["TOWER_DWARF_2_DESCRIPTION"] = "도화선만큼 짧지만, 그들의 방어선을 살아서 통과할 수 있는 것은 없습니다.",
["TOWER_DWARF_2_NAME"] = "포병 분대 II",
["TOWER_DWARF_3_DESCRIPTION"] = "도화선만큼 짧지만, 그들의 방어선을 살아서 통과할 수 있는 것은 없습니다.",
["TOWER_DWARF_3_NAME"] = "포병 분대 III",
["TOWER_DWARF_4_DESCRIPTION"] = "도화선만큼 짧지만, 그들의 방어선을 살아서 통과할 수 있는 것은 없습니다.",
["TOWER_DWARF_4_FORMATION_1_DESCRIPTION"] = "분대에 세 번째 포병을 추가합니다.",
["TOWER_DWARF_4_FORMATION_1_NAME"] = "전력 증강",
["TOWER_DWARF_4_FORMATION_2_DESCRIPTION"] = "분대에 네 번째 포병을 추가합니다.",
["TOWER_DWARF_4_FORMATION_2_NAME"] = "전력 증강",
["TOWER_DWARF_4_FORMATION_3_DESCRIPTION"] = "분대에 다섯 번째 포병을 추가합니다.",
["TOWER_DWARF_4_FORMATION_3_NAME"] = "전력 증강",
["TOWER_DWARF_4_FORMATION_NOTE"] = "여자들은 총을 갖고 싶어해.",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_DESCRIPTION"] = "%$towers.dwarf.incendiary_ammo.damages_min[1]%$ - %$towers.dwarf.incendiary_ammo.damages_max[1]%$의 피해를 입히는 폭발물을 발사하고, %$towers.dwarf.incendiary_ammo.burn.duration%$ 초 동안 범위 내 적을 불태워 %$towers.dwarf.incendiary_ammo.burn.s_damage[1]%$의 피해를 입힙니다.",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_NAME"] = "소이탄",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_DESCRIPTION"] = "%$towers.dwarf.incendiary_ammo.damages_min[2]%$ - %$towers.dwarf.incendiary_ammo.damages_max[2]%$의 피해를 입히는 폭발물을 발사하고, %$towers.dwarf.incendiary_ammo.burn.duration%$ 초 동안 범위 내 적을 불태워 %$towers.dwarf.incendiary_ammo.burn.s_damage[2]%$의 피해를 입힙니다.",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_NAME"] = "소이탄",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_DESCRIPTION"] = "%$towers.dwarf.incendiary_ammo.damages_min[3]%$ - %$towers.dwarf.incendiary_ammo.damages_max[3]%$의 피해를 입히는 폭발물을 발사하고, %$towers.dwarf.incendiary_ammo.burn.duration%$ 초 동안 범위 내 적을 불태워 %$towers.dwarf.incendiary_ammo.burn.s_damage[3]%$의 피해를 입힙니다.",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_NAME"] = "소이탄",
["TOWER_DWARF_4_INCENDIARY_AMMO_NOTE"] = "뜨겁게 간다!",
["TOWER_DWARF_4_NAME"] = "포병 분대 IV",
["TOWER_DWARF_DESC"] = "뛰어난 팀 정신을 가진 숙련된 사수들로, 기술의 부적절한 사용을 통제하기 위해 북쪽에서 파견되었습니다.",
["TOWER_DWARF_NAME"] = "포병 분대",
["TOWER_ELVEN_STARGAZERS_DESC"] = "엘프 스타게이저들은 우주의 에너지를 불러내어 동시에 많은 적들과 싸울 수 있습니다.",
["TOWER_ELVEN_STARGAZERS_NAME"] = "엘프 스타게이저",
["TOWER_FLAMESPITTER_1_DESCRIPTION"] = "악한 자들에게 공포를 퍼뜨리는 이 불은 드래곤의 것과 견줄 수 있습니다.",
["TOWER_FLAMESPITTER_1_NAME"] = "드워프 화염방사기 I",
["TOWER_FLAMESPITTER_2_DESCRIPTION"] = "악한 자들에게 공포를 퍼뜨리는 이 불은 드래곤의 것과 견줄 수 있습니다.",
["TOWER_FLAMESPITTER_2_NAME"] = "드워프 화염방사기 II",
["TOWER_FLAMESPITTER_3_DESCRIPTION"] = "악한 자들에게 공포를 퍼뜨리는 이 불은 드래곤의 것과 견줄 수 있습니다.",
["TOWER_FLAMESPITTER_3_NAME"] = "드워프 화염방사기 III",
["TOWER_FLAMESPITTER_4_DESCRIPTION"] = "악한 자들에게 공포를 퍼뜨리는 이 불은 드래곤의 것과 견줄 수 있습니다.",
["TOWER_FLAMESPITTER_4_NAME"] = "드워프 화염방사기 IV",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_DESCRIPTION"] = "화염 폭탄을 발사해 %$towers.flamespitter.skill_bomb.s_damage[1]%$의 물리 피해를 주고 화상을 입혀 %$towers.flamespitter.skill_bomb.burning.duration%$ 초 동안 초당 %$towers.flamespitter.skill_bomb.burning.s_damage%$의 무속성 피해를 줍니다.",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_NAME"] = "불길",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_DESCRIPTION"] = "화염 폭탄은 %$towers.flamespitter.skill_bomb.s_damage[2]%$의 물리 피해를 줍니다. 화상은 %$towers.flamespitter.skill_bomb.burning.duration%$ 초 동안 초당 %$towers.flamespitter.skill_bomb.burning.s_damage%$의 무속성 피해를 줍니다.",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_NAME"] = "불길",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_DESCRIPTION"] = "화염 폭탄은 %$towers.flamespitter.skill_bomb.s_damage[3]%$의 물리 피해를 줍니다. 화상은 %$towers.flamespitter.skill_bomb.burning.duration%$ 초 동안 초당 %$towers.flamespitter.skill_bomb.burning.s_damage%$의 무속성 피해를 줍니다.",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_NAME"] = "불길",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_NOTE"] = "야생처럼 타오르라.",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_DESCRIPTION"] = "지면에서 불기둥이 솟아올라 적에게 %$towers.flamespitter.skill_columns.s_damage_out[1]%$-%$towers.flamespitter.skill_columns.s_damage_in[1]%$의 물리 피해를 주고 %$towers.flamespitter.skill_columns.s_stun%$ 초 동안 적을 기절시킵니다.",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_NAME"] = "불꽃 토치",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_DESCRIPTION"] = "불기둥은 %$towers.flamespitter.skill_columns.s_damage_out[2]%$-%$towers.flamespitter.skill_columns.s_damage_in[2]%$의 물리 피해를 주고 %$towers.flamespitter.skill_columns.s_stun%$ 초 동안 적을 기절시킵니다.",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_NAME"] = "불꽃 토치",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_DESCRIPTION"] = "불기둥은 %$towers.flamespitter.skill_columns.s_damage_out[3]%$-%$towers.flamespitter.skill_columns.s_damage_in[3]%$의 물리 피해를 주고 %$towers.flamespitter.skill_columns.s_stun%$ 초 동안 적을 기절시킵니다.",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_NAME"] = "불꽃 토치",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_NOTE"] = "발 조심해!",
["TOWER_FLAMESPITTER_DESC"] = "대장간의 열기를 전투에 불러온 드워프들은 불타는 결의를 동맹에 보여줍니다.",
["TOWER_FLAMESPITTER_NAME"] = "드워프 화염방사기",
["TOWER_GHOST_1_DESCRIPTION"] = "지금 당신은 그들이 보이지만, 이제는 보이지 않습니다. 이제 당신은 죽었습니다.",
["TOWER_GHOST_1_NAME"] = "잔혹한 망령 I",
["TOWER_GHOST_2_DESCRIPTION"] = "지금 당신은 그들이 보이지만, 이제는 보이지 않습니다. 이제 당신은 죽었습니다.",
["TOWER_GHOST_2_NAME"] = "잔혹한 망령 II",
["TOWER_GHOST_3_DESCRIPTION"] = "지금 당신은 그들이 보이지만, 이제는 보이지 않습니다. 이제 당신은 죽었습니다.",
["TOWER_GHOST_3_NAME"] = "잔혹한 망령 III",
["TOWER_GHOST_4_DESCRIPTION"] = "지금 당신은 그들이 보이지만, 이제는 보이지 않습니다. 이제 당신은 죽었습니다.",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_DESCRIPTION"] = "망령은 %$towers.ghost.extra_damage.cooldown_start%$ 초 동안 싸운 뒤 %$towers.ghost.extra_damage.s_damage[1]%$%의 추가 피해를 줍니다.",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_NAME"] = "영혼 흡수",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_DESCRIPTION"] = "망령은 %$towers.ghost.extra_damage.cooldown_start%$ 초 동안 싸운 뒤 %$towers.ghost.extra_damage.s_damage[2]%$%의 추가 피해를 줍니다.",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_NAME"] = "영혼 흡수",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_DESCRIPTION"] = "망령은 %$towers.ghost.extra_damage.cooldown_start%$ 초 동안 싸운 뒤 %$towers.ghost.extra_damage.s_damage[3]%$%의 추가 피해를 줍니다.",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_NAME"] = "영혼 흡수",
["TOWER_GHOST_4_EXTRA_DAMAGE_NOTE"] = "노출은 권장되지 않습니다.",
["TOWER_GHOST_4_NAME"] = "잔혹한 망령 IV",
["TOWER_GHOST_4_SOUL_ATTACK_1_DESCRIPTION"] = "패배한 망령이 근처의 적에게 자신을 던져 %$towers.ghost.soul_attack.s_damage[1]%$의 무속성 피해를 주고, 적의 속도를 늦추고 공격력을 절반으로 감소시킵니다.",
["TOWER_GHOST_4_SOUL_ATTACK_1_NAME"] = "불멸의 공포",
["TOWER_GHOST_4_SOUL_ATTACK_2_DESCRIPTION"] = "패배한 망령이 근처의 적에게 자신을 던져 %$towers.ghost.soul_attack.s_damage[2]%$의 무속성 피해를 주고, 적의 속도를 늦추고 공격력을 절반으로 감소시킵니다.",
["TOWER_GHOST_4_SOUL_ATTACK_2_NAME"] = "불멸의 공포",
["TOWER_GHOST_4_SOUL_ATTACK_3_DESCRIPTION"] = "패배한 망령이 근처의 적에게 자신을 던져 %$towers.ghost.soul_attack.s_damage[3]%$의 무속성 피해를 주고, 적의 속도를 늦추고 공격력을 절반으로 감소시킵니다.",
["TOWER_GHOST_4_SOUL_ATTACK_3_NAME"] = "불멸의 공포",
["TOWER_GHOST_4_SOUL_ATTACK_NOTE"] = "너는 우리와 함께 간다!",
["TOWER_GHOST_DESC"] = "죽어서도 싸우는 유령들. 그들의 힘을 통해 그들은 그림자 사이로 이동하고 적을 놀라게 할 수 있습니다.",
["TOWER_GHOST_NAME"] = "잔혹한 망령",
["TOWER_HERMIT_TOAD_1_DESCRIPTION"] = "약간의 마법, 약간의 거친 힘, 성가신 침입자들을 제거하는데 필요한 모든 것.",
["TOWER_HERMIT_TOAD_1_NAME"] = "늪지 은둔자 I",
["TOWER_HERMIT_TOAD_2_DESCRIPTION"] = "약간의 마법, 약간의 거친 힘, 성가신 침입자들을 제거하는데 필요한 모든 것.",
["TOWER_HERMIT_TOAD_2_NAME"] = "늪지 은둔자 II",
["TOWER_HERMIT_TOAD_3_DESCRIPTION"] = "약간의 마법, 약간의 거친 힘, 성가신 침입자들을 제거하는데 필요한 모든 것.",
["TOWER_HERMIT_TOAD_3_NAME"] = "늪지 은둔자 III",
["TOWER_HERMIT_TOAD_4_DESCRIPTION"] = "약간의 마법, 약간의 거친 힘, 성가신 침입자들을 제거하는데 필요한 모든 것.",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_DESCRIPTION"] = "매 %$towers.hermit_toad.power_instakill.cooldown[1]%$ 초마다 적을 삼키기 위해 혀를 사용합니다.",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_NAME"] = "끈끈한 혀",
["TOWER_HERMIT_TOAD_4_JUMP_1_DESCRIPTION"] = "매 %$towers.hermit_toad.power_jump.cooldown[1]%$ 초마다 은둔자가 하늘 높이 뛰어올라 적과 충돌하여 %$towers.hermit_toad.power_jump.damage_min[1]%$의 피해를 주고 착지 시 %$towers.hermit_toad.power_jump.stun_duration[1]%$ 초 동안 그들을 기절시킵니다.",
["TOWER_HERMIT_TOAD_4_JUMP_1_NAME"] = "그라운드 파운더",
["TOWER_HERMIT_TOAD_4_NAME"] = "늪지 은둔자 IV",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_DESCRIPTION"] = "매 %$towers.hermit_toad.power_instakill.cooldown[1]%$ 초마다 적을 삼키기 위해 혀를 사용합니다.",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_NAME"] = "끈끈한 혀 I",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_NOTE"] = "끈끈한 사업.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_DESCRIPTION"] = "매 %$towers.hermit_toad.power_jump.cooldown[1]%$ 초마다 은둔자가 하늘 높이 뛰어올라 적과 충돌하여 %$towers.hermit_toad.power_jump.damage_min[1]%$의 피해를 주고 착지 시 %$towers.hermit_toad.power_jump.stun_duration[1]%$ 초 동안 그들을 기절시킵니다.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_NAME"] = "그라운드 파운더 I",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_DESCRIPTION"] = "매 %$towers.hermit_toad.power_jump.cooldown[2]%$ 초마다 은둔자가 하늘 높이 뛰어올라 적과 충돌하여 %$towers.hermit_toad.power_jump.damage_min[2]%$의 피해를 주고 착지 시 %$towers.hermit_toad.power_jump.stun_duration[2]%$ 초 동안 그들을 기절시킵니다.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_NAME"] = "그라운드 파운더 II",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_DESCRIPTION"] = "매 %$towers.hermit_toad.power_jump.cooldown[3]%$ 초마다 은둔자가 하늘 높이 뛰어올라 적과 충돌하여 %$towers.hermit_toad.power_jump.damage_min[3]%$의 피해를 주고 착지 시 %$towers.hermit_toad.power_jump.stun_duration[3]%$ 초 동안 그들을 기절시킵니다.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_NAME"] = "그라운드 파운더 III",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_NOTE"] = "늪지 배구팀 준비 완료",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_DESCRIPTION"] = "은둔자가 물리적 자세로 변합니다.",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NAME"] = "진흙탕 늪지",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NOTE"] = "더러워지는 중!",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_DESCRIPTION"] = "은둔자가 마법의 자세로 변합니다.",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NAME"] = "마법의 연못",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NOTE"] = "무한한 힘!!",
["TOWER_HERMIT_TOAD_DESC"] = "점액덩어리를 뱉는 재주를 지닌 거대 두꺼비 마법사. 그가 원하는 것은 연못 목욕을 위한 평화로움과 고요함뿐입니다. 그를 방해하지 마십시오.",
["TOWER_HERMIT_TOAD_NAME"] = "늪지 은둔자",
["TOWER_NECROMANCER_1_DESCRIPTION"] = "죽음에 대한 지배력으로 강령술사는 전장에 뿌린 혼란을 수확합니다.",
["TOWER_NECROMANCER_1_NAME"] = "강령술사 I",
["TOWER_NECROMANCER_2_DESCRIPTION"] = "죽음에 대한 지배력으로 강령술사는 전장에 뿌린 혼란을 수확합니다.",
["TOWER_NECROMANCER_2_NAME"] = "강령술사 II",
["TOWER_NECROMANCER_3_DESCRIPTION"] = "죽음에 대한 지배력으로 강령술사는 전장에 뿌린 혼란을 수확합니다.",
["TOWER_NECROMANCER_3_NAME"] = "강령술사 III",
["TOWER_NECROMANCER_4_DESCRIPTION"] = "죽음에 대한 지배력으로 강령술사는 전장에 뿌린 혼란을 수확합니다.",
["TOWER_NECROMANCER_4_NAME"] = "강령술사 IV",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_DESCRIPTION"] = "%$towers.necromancer.skill_debuff.aura_duration[1]%$ 초 동안 지속되는 토템을 배치하여 적을 저주하고 해골에게 %$towers.necromancer.skill_debuff.s_damage_factor[1]%$%의 추가 공격 피해를 부여합니다.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_NAME"] = "저주의 등화",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_DESCRIPTION"] = "토템은 해골에게 %$towers.necromancer.skill_debuff.s_damage_factor[2]%$%의 추가 공격 피해를 부여합니다. 사용 대기 시간이 %$towers.necromancer.skill_debuff.cooldown[2]%$ 초로 감소합니다.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_NAME"] = "저주의 등화",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_DESCRIPTION"] = "토템은 해골에게 %$towers.necromancer.skill_debuff.s_damage_factor[3]%$%의 추가 공격 피해를 부여합니다. 사용 대기 시간이 %$towers.necromancer.skill_debuff.cooldown[3]%$ 초로 감소합니다.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_NAME"] = "저주의 등화",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_NOTE"] = "뼈가 튼튼한 군대!",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_DESCRIPTION"] = "경로에 죽음의 기사를 소환하여 적들에게 %$towers.necromancer.skill_rider.s_damage[1]%$의 무속성 피해를 줍니다.",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_NAME"] = "죽음의 기사",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_DESCRIPTION"] = "죽음의 기사는 %$towers.necromancer.skill_rider.s_damage[2]%$의 무속성 피해를 줍니다.",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_NAME"] = "죽음의 기사",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_DESCRIPTION"] = "죽음의 기사는 %$towers.necromancer.skill_rider.s_damage[3]%$의 무속성 피해를 줍니다.",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_NAME"] = "죽음의 기사",
["TOWER_NECROMANCER_4_SKILL_RIDER_NOTE"] = "편도 티켓...",
["TOWER_NECROMANCER_DESC"] = "가장 어두운 형태의 마법을 사용하는 강령술사는 적을 끝없는 군대의 일원으로 활용합니다.",
["TOWER_NECROMANCER_NAME"] = "강령술사",
["TOWER_PALADIN_COVENANT_1_DESCRIPTION"] = "맹렬하고 헌신적인 성기사들은 왕국을 위험으로부터 보호하기 위해 열심히 노력합니다.",
["TOWER_PALADIN_COVENANT_1_NAME"] = "성기사 성약단 I",
["TOWER_PALADIN_COVENANT_2_DESCRIPTION"] = "맹렬하고 헌신적인 성기사들은 왕국을 위험으로부터 보호하기 위해 열심히 노력합니다.",
["TOWER_PALADIN_COVENANT_2_NAME"] = "성기사 성약단 III",
["TOWER_PALADIN_COVENANT_3_DESCRIPTION"] = "맹렬하고 헌신적인 성기사들은 왕국을 위험으로부터 보호하기 위해 열심히 노력합니다.",
["TOWER_PALADIN_COVENANT_3_NAME"] = "성기사 성약단 III",
["TOWER_PALADIN_COVENANT_4_DESCRIPTION"] = "맹렬하고 헌신적인 성기사들은 왕국을 위험으로부터 보호하기 위해 열심히 노력합니다.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_DESCRIPTION"] = "병사들의 생명력이 %$towers.paladin_covenant.healing_prayer.health_trigger_factor[1]%$%에 도달하면 %$towers.paladin_covenant.healing_prayer.duration%$ 초 동안 무적이 되며 초당 %$towers.paladin_covenant.healing_prayer.s_healing[1]%$ HP를 회복합니다.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_NAME"] = "치유의 기도",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_DESCRIPTION"] = "치유량이 초당 %$towers.paladin_covenant.healing_prayer.s_healing[2]%$ HP로 증가하여 %$towers.paladin_covenant.healing_prayer.duration%$ 초 동안 지속됩니다.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_NAME"] = "치유의 기도",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_DESCRIPTION"] = "치유량이 초당 %$towers.paladin_covenant.healing_prayer.s_healing[3]%$ HP로 증가하여 %$towers.paladin_covenant.healing_prayer.duration%$ 초 동안 지속됩니다.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_NAME"] = "치유의 기도",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NAME"] = "치유의 기도",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NOTE"] = "의무는 사력으로.",
["TOWER_PALADIN_COVENANT_4_LEAD_1_DESCRIPTION"] = "성기사 한 명을 베테랑 근위병으로 교체하여 성기사들에게 %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$% 추가 공격력을 부여합니다.",
["TOWER_PALADIN_COVENANT_4_LEAD_1_NAME"] = "솔선수범",
["TOWER_PALADIN_COVENANT_4_LEAD_2_DESCRIPTION"] = "성기사 한 명을 베테랑 근위병으로 교체하여 성기사들에게 %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$% 추가 공격력을 부여합니다.",
["TOWER_PALADIN_COVENANT_4_LEAD_2_NAME"] = "솔선수범",
["TOWER_PALADIN_COVENANT_4_LEAD_3_DESCRIPTION"] = "성기사 한 명을 베테랑 근위병으로 교체하여 성기사들에게 %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$% 추가 공격력을 부여합니다.",
["TOWER_PALADIN_COVENANT_4_LEAD_3_NAME"] = "솔선수범",
["TOWER_PALADIN_COVENANT_4_LEAD_NAME"] = "솔선수범",
["TOWER_PALADIN_COVENANT_4_LEAD_NOTE"] = "왕을 위해, 국토를 위해, 산천을 위해.",
["TOWER_PALADIN_COVENANT_4_NAME"] = "성기사 성약단 IV",
["TOWER_PALADIN_COVENANT_DESC"] = "성기사는 리니리아 정예 부대의 중추로서 신성한 힘을 사용하여 전투에서 자신을 보호하고 치유합니다.",
["TOWER_PALADIN_COVENANT_NAME"] = "성기사 성약단",
["TOWER_PANDAS_1_DESCRIPTION"] = "원소 마스터리와 흔들림 없는 결의를 갖춘 마스터들은 세상의 자연 균형을 지키기 위해 끊임없이 싸웁니다.",
["TOWER_PANDAS_1_NAME"] = "대나무 마스터 I",
["TOWER_PANDAS_2_DESCRIPTION"] = "원소 마스터리와 흔들림 없는 결의를 갖춘 마스터들은 세상의 자연 균형을 지키기 위해 끊임없이 싸웁니다.",
["TOWER_PANDAS_2_NAME"] = "대나무 마스터 II",
["TOWER_PANDAS_3_DESCRIPTION"] = "원소 마스터리와 흔들림 없는 결의를 갖춘 마스터들은 세상의 자연 균형을 지키기 위해 끊임없이 싸웁니다.",
["TOWER_PANDAS_3_NAME"] = "대나무 마스터 III",
["TOWER_PANDAS_4_DESCRIPTION"] = "원소 마스터리와 흔들림 없는 결의를 갖춘 마스터들은 세상의 자연 균형을 지키기 위해 끊임없이 싸웁니다.",
["TOWER_PANDAS_4_FIERY"] = "카우시",
["TOWER_PANDAS_4_FIERY_1_DESCRIPTION"] = "불꽃을 발사하여 %$towers.pandas.soldier.teleport.damage_min[1]%$-%$towers.pandas.soldier.teleport.damage_max[1]%$의 고정 피해를 입히고 적을 경로 뒤로 순간이동시킵니다.",
["TOWER_PANDAS_4_FIERY_1_NAME"] = "황천 불꽃",
["TOWER_PANDAS_4_FIERY_2_DESCRIPTION"] = "%$towers.pandas.soldier.teleport.damage_min[2]%$~%$towers.pandas.soldier.teleport.damage_max[2]%$의 고정 피해를 주는 화염 화살을 발사하고, 적중한 적을 경로 뒤쪽으로 순간이동시킵니다.",
["TOWER_PANDAS_4_FIERY_2_NAME"] = "황천 불꽃",
["TOWER_PANDAS_4_HAT"] = "모두를 때릴 모자",
["TOWER_PANDAS_4_HAT_1_DESCRIPTION"] = "날카로운 모자를 적에게 던져 적들 사이를 튕기며 각각 %$towers.pandas.soldier.hat.damage_levels[1].min%$-%$towers.pandas.soldier.hat.damage_levels[1].max%$의 피해를 입힙니다.",
["TOWER_PANDAS_4_HAT_1_NAME"] = "모자 묘기",
["TOWER_PANDAS_4_HAT_2_DESCRIPTION"] = "날카로운 모자를 적에게 던져 적들 사이를 튕기며 각각 %$towers.pandas.soldier.hat.damage_levels[2].min%$-%$towers.pandas.soldier.hat.damage_levels[2].max%$의 피해를 입힙니다.",
["TOWER_PANDAS_4_HAT_2_NAME"] = "모자 묘기",
["TOWER_PANDAS_4_NAME"] = "대나무 마스터 IV",
["TOWER_PANDAS_4_THUNDER"] = "판다 콤뱃",
["TOWER_PANDAS_4_THUNDER_1_DESCRIPTION"] = "작은 범위에 번개를 떨어뜨려 각각 %$towers.pandas.soldier.thunder.damage_min[1]%$-%$towers.pandas.soldier.thunder.damage_max[1]%$ 범위 피해를 입히고 적을 잠시 기절시킵니다.",
["TOWER_PANDAS_4_THUNDER_1_NAME"] = "과전류 폭주",
["TOWER_PANDAS_4_THUNDER_2_DESCRIPTION"] = "작은 범위에 번개를 떨어뜨려 각각 %$towers.pandas.soldier.thunder.damage_min[2]%$-%$towers.pandas.soldier.thunder.damage_max[2]%$ 범위 피해를 입히고 적을 잠시 기절시킵니다.",
["TOWER_PANDAS_4_THUNDER_2_NAME"] = "라이트닝 오버로드",
["TOWER_PANDAS_DESC"] = "무예와 원소의 친화력을 겸비한 이 판다 삼총사는 적을 쓸어버리며, 쓰러진 것처럼 보여도 여전히 위협적인 존재입니다.",
["TOWER_PANDAS_NAME"] = "대나무 마스터",
["TOWER_PANDAS_RETREAT_DESCRIPTION"] = "8초 동안 대기 중인 판다를 피난처로 후퇴시킵니다.",
["TOWER_PANDAS_RETREAT_NAME"] = "전술적 후퇴",
["TOWER_PANDAS_RETREAT_NOTE"] = "용기에서 가장 중요한 것은 신중함이다.",
["TOWER_RAY_1_DESCRIPTION"] = "위험하고 더렵혀진 형태의 마법은 악한 마법사들이 악의적인 목적을 추구하는 것을 결코 막지 못했습니다.",
["TOWER_RAY_1_NAME"] = "엘드리치 채널러 I",
["TOWER_RAY_2_DESCRIPTION"] = "위험하고 더렵혀진 형태의 마법은 악한 마법사들이 악의적인 목적을 추구하는 것을 결코 막지 못했습니다.",
["TOWER_RAY_2_NAME"] = "엘드리치 채널러 II",
["TOWER_RAY_3_DESCRIPTION"] = "위험하고 더렵혀진 형태의 마법은 악한 마법사들이 악의적인 목적을 추구하는 것을 결코 막지 못했습니다.",
["TOWER_RAY_3_NAME"] = "엘드리치 채널러 III",
["TOWER_RAY_4_CHAIN_1_DESCRIPTION"] = "마법의 광선이 이제 %$towers.ray.skill_chain.s_max_enemies%$ 명의 추가 적에게까지 확장되어, 그들의 속도를 늦추며 각 대상에게 총 마법 피해의 %$towers.ray.skill_chain.damage_mult[1]%$%를 입힙니다.",
["TOWER_RAY_4_CHAIN_1_NAME"] = "파워 오버플로",
["TOWER_RAY_4_CHAIN_2_DESCRIPTION"] = "마법의 광선이 이제 %$towers.ray.skill_chain.s_max_enemies%$ 명의 추가 적에게까지 확장되어, 그들의 속도를 늦추며 각 대상에게 총 마법 피해의 %$towers.ray.skill_chain.damage_mult[2]%$%를 입힙니다.",
["TOWER_RAY_4_CHAIN_2_NAME"] = "파워 오버플로",
["TOWER_RAY_4_CHAIN_3_DESCRIPTION"] = "마법의 광선이 이제 %$towers.ray.skill_chain.s_max_enemies%$ 명의 추가 적에게까지 확장되어, 그들의 속도를 늦추며 각 대상에게 총 마법 피해의 %$towers.ray.skill_chain.damage_mult[3]%$%를 입힙니다.",
["TOWER_RAY_4_CHAIN_3_NAME"] = "파워 오버플로",
["TOWER_RAY_4_CHAIN_NOTE"] = "모두에게 충분한 고통이 있어.",
["TOWER_RAY_4_DESCRIPTION"] = "위험하고 더렵혀진 형태의 마법은 악한 마법사들이 악의적인 목적을 추구하는 것을 결코 막지 못했습니다.",
["TOWER_RAY_4_NAME"] = "엘드리치 채널러 IV",
["TOWER_RAY_4_SHEEP_1_DESCRIPTION"] = "주변의 적을 무력한 양으로 변환합니다. 양은 대상의 생명력의 %$towers.ray.skill_sheep.sheep.hp_mult%$%를 가집니다.",
["TOWER_RAY_4_SHEEP_1_NAME"] = "돌연변이 주문",
["TOWER_RAY_4_SHEEP_2_DESCRIPTION"] = "주변의 적을 무력한 양으로 변환합니다. 양은 대상의 생명력의 %$towers.ray.skill_sheep.sheep.hp_mult%$%를 가집니다.",
["TOWER_RAY_4_SHEEP_2_NAME"] = "돌연변이 주문",
["TOWER_RAY_4_SHEEP_3_DESCRIPTION"] = "주변의 적을 무력한 양으로 변환합니다. 양은 대상의 생명력의 %$towers.ray.skill_sheep.sheep.hp_mult%$%를 가집니다.",
["TOWER_RAY_4_SHEEP_3_NAME"] = "돌연변이 주문",
["TOWER_RAY_4_SHEEP_NOTE"] = "솔직히, 지금이 더 나아 보여.",
["TOWER_RAY_DESC"] = "베즈난의 도제들은 그들의 부패한 힘을 이용해 적들에게 고통의 암흑 광선을 쏩니다.",
["TOWER_RAY_NAME"] = "엘드리치 채널러",
["TOWER_ROCKET_GUNNERS_1_DESCRIPTION"] = "최신 암흑 군대 기술로 무장한 포병들이 하늘을 순찰합니다.",
["TOWER_ROCKET_GUNNERS_1_NAME"] = "로켓 포병대 I",
["TOWER_ROCKET_GUNNERS_2_DESCRIPTION"] = "최신 암흑 군대 기술로 무장한 포병들이 하늘을 순찰합니다.",
["TOWER_ROCKET_GUNNERS_2_NAME"] = "로켓 포병대 II",
["TOWER_ROCKET_GUNNERS_3_DESCRIPTION"] = "최신 암흑 군대 기술로 무장한 포병들이 하늘을 순찰합니다.",
["TOWER_ROCKET_GUNNERS_3_NAME"] = "로켓 포병대 III",
["TOWER_ROCKET_GUNNERS_4_DESCRIPTION"] = "최신 암흑 군대 기술로 무장한 포병들이 하늘을 순찰합니다.",
["TOWER_ROCKET_GUNNERS_4_NAME"] = "로켓 포병대 IV",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_DESCRIPTION"] = "매 공격은 적의 방어력을 %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[1]%$% 감소시키고 범위 피해를 입힙니다.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_NAME"] = "인산염 코팅",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_DESCRIPTION"] = "매 공격은 적의 방어력을 %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[2]%$% 감소시키고 %$towers.rocket_gunners.soldier.phosphoric.damage_area_min[2]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[2]%$ 범위 피해를 입힙니다.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_NAME"] = "인산염 코팅",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_DESCRIPTION"] = "매 공격은 적의 방어력을 %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[3]%$% 감소시키고 %$towers.rocket_gunners.soldier.phosphoric.damage_area_min[3]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[3]%$ 범위 피해를 입힙니다.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_NAME"] = "인산염 코팅",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_NOTE"] = "사악하게 조미된 탄환.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_DESCRIPTION"] = "최대 %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[1]%$ 생명력의 대상을 즉시 사살하는 미사일을 발사합니다.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_NAME"] = "스팅 미사일",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_DESCRIPTION"] = "사용 대기 시간이 %$towers.rocket_gunners.sting_missiles.cooldown[2]%$ 초로 감소합니다. 이제 최대 %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[2]%$ 생명력의 적을 대상으로 삼을 수 있습니다.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_NAME"] = "스팅 미사일",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_DESCRIPTION"] = "사용 대기 시간이 %$towers.rocket_gunners.sting_missiles.cooldown[3]%$ 초로 감소합니다. 이제 최대 %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[3]%$ 생명력의 적을 대상으로 삼을 수 있습니다.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_NAME"] = "스팅 미사일",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_NOTE"] = "한번 피해봐!",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_DESCRIPTION"] = "로켓 포병대가 이륙하여 적을 저지할 수 없습니다.",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NAME"] = "이륙",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NOTE"] = "무한 그 너머로!",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_DESCRIPTION"] = "로켓 포병대가 착륙하여 적을 저지할 수 있습니다.",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NAME"] = "착륙",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NOTE"] = "독수리가 착륙했다!",
["TOWER_ROCKET_GUNNERS_DESC"] = "이 특수 부대는 방심한 적들에게 첨단 무기를 발사하며 지상과 공중 모두에서 물러서지 않습니다.",
["TOWER_ROCKET_GUNNERS_NAME"] = "로켓 포병대",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "이 타워는 거대한 위협 캠페인에 포함되어 있습니다.",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "이 타워는 ‘오공의 여정’ 캠페인에 포함됩니다.",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "거대한 위협 캠페인",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "오공의 여정 캠페인",
["TOWER_ROOM_EQUIPPED_TOWERS_TITLE"] = "장비된 타워",
["TOWER_ROOM_GET_DLC"] = "가져가세요",
["TOWER_ROOM_LABEL_ROSTER_THUMB_NEW"] = "신규!",
["TOWER_ROOM_SKILLS_TITLE"] = "기술",
["TOWER_ROYAL_ARCHERS_1_DESCRIPTION"] = "마지막까지 충성스러운 왕실 궁병대는 원거리에서 리니리아 군대를 보호합니다.",
["TOWER_ROYAL_ARCHERS_1_NAME"] = "왕실 궁병대 I",
["TOWER_ROYAL_ARCHERS_2_DESCRIPTION"] = "마지막까지 충성스러운 왕실 궁병대는 원거리에서 리니리아 군대를 보호합니다.",
["TOWER_ROYAL_ARCHERS_2_NAME"] = "왕실 궁병대 II",
["TOWER_ROYAL_ARCHERS_3_DESCRIPTION"] = "마지막까지 충성스러운 왕실 궁병대는 원거리에서 리니리아 군대를 보호합니다.",
["TOWER_ROYAL_ARCHERS_3_NAME"] = "왕실 궁병대 III",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_DESCRIPTION"] = "방어력을 %$towers.royal_archers.armor_piercer.armor_penetration[1]%$% 무시하는 강화된 화살 세 발을 발사하여 %$towers.royal_archers.armor_piercer.damage_min[1]%$-%$towers.royal_archers.armor_piercer.damage_max[1]%$의 물리 피해를 입힙니다.",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_NAME"] = "관통탄",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_DESCRIPTION"] = "강화된 화살 세 발을 쏘아 %$towers.royal_archers.armor_piercer.damage_min[2]%$에서 %$towers.royal_archers.armor_piercer.damage_max[2]%$까지의 물리 피해를 주며, 적의 방어력의 %$towers.royal_archers.armor_piercer.armor_penetration[2]%$%를 무시합니다.",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_NAME"] = "관통탄",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_DESCRIPTION"] = "강화된 화살 세 발을 쏘아 %$towers.royal_archers.armor_piercer.damage_min[3]%$에서 %$towers.royal_archers.armor_piercer.damage_max[3]%$까지의 물리 피해를 주며, 적의 방어력의 %$towers.royal_archers.armor_piercer.armor_penetration[3]%$%를 무시합니다.",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_NAME"] = "관통탄",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NAME"] = "관통탄",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NOTE"] = "너는 우리의 시야에 잡혔어.",
["TOWER_ROYAL_ARCHERS_4_DESCRIPTION"] = "마지막까지 충성스러운 왕실 궁병대는 원거리에서 리니리아 군대를 보호합니다.",
["TOWER_ROYAL_ARCHERS_4_NAME"] = "왕실 궁병대 IV",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_DESCRIPTION"] = "독수리를 소환해 적에게 %$towers.royal_archers.rapacious_hunter.damage_min[1]%$-%$towers.royal_archers.rapacious_hunter.damage_max[1]%$의 물리 피해를 입힙니다.",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_NAME"] = "굶주린 사냥꾼",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_DESCRIPTION"] = "독수리가 %$towers.royal_archers.rapacious_hunter.damage_min[2]%$-%$towers.royal_archers.rapacious_hunter.damage_max[2]%$의 물리 피해를 입힙니다.",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_NAME"] = "굶주린 사냥꾼",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_DESCRIPTION"] = "독수리가 %$towers.royal_archers.rapacious_hunter.damage_min[3]%$-%$towers.royal_archers.rapacious_hunter.damage_max[3]%$의 물리 피해를 입힙니다.",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_NAME"] = "굶주린 사냥꾼",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NAME"] = "굶주린 사냥꾼",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NOTE"] = "독수리의 눈은 비극적인 무언가를 감추고 있어.",
["TOWER_ROYAL_ARCHERS_DESC"] = "왕국에서 가장 강력한 사수들, 그들은 전쟁 독수리의 도움을 받는 것으로도 유명합니다.",
["TOWER_ROYAL_ARCHERS_NAME"] = "왕실 궁병대",
["TOWER_SAND_1_DESCRIPTION"] = "칼날을 던지는 그들의 기술은 자만에 가득 찬 어느 용병이라도 겁을 주기에 충분합니다.",
["TOWER_SAND_1_NAME"] = "듄 센티넬 I",
["TOWER_SAND_2_DESCRIPTION"] = "칼날을 던지는 그들의 기술은 자만에 가득 찬 어느 용병이라도 겁을 주기에 충분합니다.",
["TOWER_SAND_2_NAME"] = "듄 센티넬 II",
["TOWER_SAND_3_DESCRIPTION"] = "칼날을 던지는 그들의 기술은 자만에 가득 찬 어느 용병이라도 겁을 주기에 충분합니다.",
["TOWER_SAND_3_NAME"] = "듄 센티넬 III",
["TOWER_SAND_4_DESCRIPTION"] = "칼날을 던지는 그들의 기술은 자만에 가득 찬 어느 용병이라도 겁을 주기에 충분합니다.",
["TOWER_SAND_4_NAME"] = "듄 센티넬 IV",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_DESCRIPTION"] = "소용돌이치는 칼날을 경로에 발사하여 %$towers.sand.skill_big_blade.duration[1]%$ 초 동안 초당 %$towers.sand.skill_big_blade.s_damage_min[1]%$-%$towers.sand.skill_big_blade.s_damage_max[1]%$의 물리 피해를 줍니다.",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_NAME"] = "파멸의 소용돌이",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_DESCRIPTION"] = "소용돌이치는 칼날을 경로에 발사하여 %$towers.sand.skill_big_blade.duration[2]%$ 초 동안 초당 %$towers.sand.skill_big_blade.s_damage_min[2]%$-%$towers.sand.skill_big_blade.s_damage_max[2]%$의 물리 피해를 줍니다.",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_NAME"] = "파멸의 소용돌이",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_DESCRIPTION"] = "소용돌이치는 칼날을 경로에 발사하여 %$towers.sand.skill_big_blade.duration[3]%$ 초 동안 초당 %$towers.sand.skill_big_blade.s_damage_min[3]%$-%$towers.sand.skill_big_blade.s_damage_max[3]%$의 물리 피해를 줍니다.",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_NAME"] = "파멸의 소용돌이",
["TOWER_SAND_4_SKILL_BIG_BLADE_NOTE"] = "너는 나를 빙빙 돌게 해, 베이비.",
["TOWER_SAND_4_SKILL_GOLD_1_DESCRIPTION"] = "튕기는 칼날을 던져 대상에게 %$towers.sand.skill_gold.s_damage[1]%$의 물리 피해를 줍니다. 이로써 죽은 적으로부터 %$towers.sand.skill_gold.gold_extra[1]%$의 보너스 골드를 얻습니다.",
["TOWER_SAND_4_SKILL_GOLD_1_NAME"] = "현상금 사냥",
["TOWER_SAND_4_SKILL_GOLD_2_DESCRIPTION"] = "칼날이 %$towers.sand.skill_gold.s_damage[2]%$의 물리 피해를 줍니다. 처치 시 %$towers.sand.skill_gold.gold_extra[2]%$의 보너스 골드를 얻습니다.",
["TOWER_SAND_4_SKILL_GOLD_2_NAME"] = "현상금 사냥",
["TOWER_SAND_4_SKILL_GOLD_3_DESCRIPTION"] = "칼날이 %$towers.sand.skill_gold.s_damage[3]%$의 물리 피해를 줍니다. 처치 시 %$towers.sand.skill_gold.gold_extra[3]%$의 보너스 골드를 얻습니다.",
["TOWER_SAND_4_SKILL_GOLD_3_NAME"] = "현상금 사냥",
["TOWER_SAND_4_SKILL_GOLD_NOTE"] = "전단지에는 죽었든 살았든이라고 쓰여 있다.",
["TOWER_SAND_DESC"] = "해머홀드 출신인 듄 센티넬은 사막 주민 중 가장 치명적인 존재일 수 있습니다.",
["TOWER_SAND_NAME"] = "듄 센티넬",
["TOWER_SELL"] = "타워 처분",
["TOWER_SPARKING_GEODE_1_DESCRIPTION"] = "폭풍을 소환하고 혼돈을 가져오는 존재입니다. 에너지 소비에 주의하세요.",
["TOWER_SPARKING_GEODE_1_NAME"] = "번개의 거신 I",
["TOWER_SPARKING_GEODE_2_DESCRIPTION"] = "번개의 거신 III",
["TOWER_SPARKING_GEODE_2_NAME"] = "번개의 거신 II",
["TOWER_SPARKING_GEODE_3_DESCRIPTION"] = "결정화 I",
["TOWER_SPARKING_GEODE_3_NAME"] = "번개의 거신 IV",
["TOWER_SPARKING_GEODE_4_CRISTALIZE"] = "천둥 번개!",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_DESCRIPTION"] = "매 %$towers.sparking_geode.crystalize.cooldown[1]%$초마다 범위 내의 %$towers.sparking_geode.crystalize.max_targets[1]%$명의 적을 결정화하여 기절시키고, %$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$%의 추가 피해를 받게 한다.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_NAME"] = "결정화",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_DESCRIPTION"] = "%$towers.sparking_geode.crystalize.cooldown[2]%$초마다 범위 내 %$towers.sparking_geode.crystalize.max_targets[2]%$명의 적을 결정화하여 기절시키고, %$towers.sparking_geode.crystalize.s_received_damage_factor[2]%$% 추가 피해를 입힌다.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_NAME"] = "결정화",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_DESCRIPTION"] = "%$towers.sparking_geode.crystalize.cooldown[3]%$초마다 범위 내 %$towers.sparking_geode.crystalize.max_targets[3]%$명의 적을 결정화하여 기절시키고, %$towers.sparking_geode.crystalize.s_received_damage_factor[3]%$% 추가 피해를 입힌다.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_NAME"] = "결정화",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_DESCRIPTION"] = "매 %$towers.sparking_geode.crystalize.cooldown[1]%$초마다 범위 내 %$towers.sparking_geode.crystalize.max_targets[1]%$명의 적을 결정화시켜 기절시키고 %$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$% 추가 피해를 입힌다.",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_NAME"] = "결정화",
["TOWER_SPARKING_GEODE_4_DESCRIPTION"] = "폭풍을 소환하는 자이자 혼돈을 불러오는 존재. 에너지 소비량을 조심해야 한다.",
["TOWER_SPARKING_GEODE_4_NAME"] = "서지 콜로서스 IV",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST"] = "더 강하고, 더 좋고, 더 빠르고, 더 강하게.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_DESCRIPTION"] = "%$towers.sparking_geode.spike_burst.cooldown[1]%$초마다 파수병이 전기장을 소환하여 주변 적에게 피해를 주고 %$towers.sparking_geode.spike_burst.duration[1]%$초 동안 둔화시킨다.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_NAME"] = "전기 쇄도",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_DESCRIPTION"] = "%$towers.sparking_geode.spike_burst.cooldown[2]%$초마다 파수병이 전기장을 소환하여 주변 적에게 피해를 주고 %$towers.sparking_geode.spike_burst.duration[2]%$초 동안 둔화시킨다.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_NAME"] = "전기 쇄도",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_DESCRIPTION"] = "%$towers.sparking_geode.spike_burst.cooldown[3]%$초마다 센티넬이 전기장을 소환하여 주변 적에게 피해를 주고 %$towers.sparking_geode.spike_burst.duration[3]%$초 동안 둔화시킵니다.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_NAME"] = "전기 쇄도",
["TOWER_SPARKING_GEODE_DESC"] = "고대의 평화로운 종족에서 태어난 이 강력한 존재는 보호 본능을 따르며 번개의 힘을 이용해 동맹을 위해 싸우고, 폭풍의 분노로 적을 내리칩니다.",
["TOWER_SPARKING_GEODE_NAME"] = "번개의 거신",
["TOWER_STAGE_13_SUNRAY_NAME"] = "암흑광선 타워",
["TOWER_STAGE_13_SUNRAY_REPAIR_DESCRIPTION"] = "탑을 수리하여 그 파괴력을 사용하세요.",
["TOWER_STAGE_13_SUNRAY_REPAIR_NAME"] = "수리",
["TOWER_STAGE_17_WEIRDWOOD_NAME"] = "기괴한 나무",
["TOWER_STAGE_18_ELVEN_BARRACK_DESCRIPTION"] = "고용된 엘프들은 끝까지 싸울 것입니다.",
["TOWER_STAGE_18_ELVEN_BARRACK_NAME"] = "엘프 용병",
["TOWER_STAGE_20_ARBOREAN_BARRACK_DESCRIPTION"] = "아르보리아 사람들을 전투에 소환합니다.",
["TOWER_STAGE_20_ARBOREAN_BARRACK_NAME"] = "아르보리아 시민들",
["TOWER_STAGE_20_ARBOREAN_HONEY_DESCRIPTION"] = "벌들의 위대한 사령관을 소환합니다.",
["TOWER_STAGE_20_ARBOREAN_HONEY_NAME"] = "아르보리아 양봉가",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_DESCRIPTION"] = "오래된 나무에게 도움을 청하세요.",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_NAME"] = "오래된 나무",
["TOWER_STAGE_22_ARBOREAN_MAGES_NAME"] = "아르보리아 마법사",
["TOWER_STAGE_28_PRIESTS_BARRACK_DESCRIPTION"] = "눈 없는 자를 신봉하는 자들.",
["TOWER_STAGE_28_PRIESTS_BARRACK_NAME"] = "눈먼 광신도",
["TOWER_STARGAZER_1_DESCRIPTION"] = "스타게이저들은 지상의 영역을 넘는 강력한 마법을 활용합니다.",
["TOWER_STARGAZER_1_NAME"] = "엘프 스타게이저 I",
["TOWER_STARGAZER_2_DESCRIPTION"] = "스타게이저들은 지상의 영역을 넘는 강력한 마법을 활용합니다.",
["TOWER_STARGAZER_2_NAME"] = "엘프 스타게이저 II",
["TOWER_STARGAZER_3_DESCRIPTION"] = "스타게이저들은 지상의 영역을 넘는 강력한 마법을 활용합니다.",
["TOWER_STARGAZER_3_NAME"] = "엘프 스타게이저 III",
["TOWER_STARGAZER_4_DESCRIPTION"] = "스타게이저들은 지상의 영역을 넘는 강력한 마법을 활용합니다.",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_DESCRIPTION"] = "최대 %$towers.elven_stargazers.teleport.max_targets[1]%$ 명의 적을 뒤로 순간이동시킵니다.",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_NAME"] = "사상의 지평",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_DESCRIPTION"] = "최대 %$towers.elven_stargazers.teleport.max_targets[2]%$ 명의 적을 더 멀리 뒤로 순간이동시킵니다.",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_NAME"] = "사상의 지평",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_DESCRIPTION"] = "최대 %$towers.elven_stargazers.teleport.max_targets[3]%$ 명의 적을 더욱 더 멀리 뒤로 순간이동시킵니다.",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_NAME"] = "사상의 지평",
["TOWER_STARGAZER_4_EVENT_HORIZON_NAME"] = "사상의 지평",
["TOWER_STARGAZER_4_EVENT_HORIZON_NOTE"] = "페이즈 아웃, 페이즈 인.",
["TOWER_STARGAZER_4_NAME"] = "엘프 스타게이저 IV",
["TOWER_STARGAZER_4_RISING_STAR_1_DESCRIPTION"] = "타워에 의해 죽은 적들은 %$towers.elven_stargazers.stars_death.stars[1]%$개의 별의 폭발로 폭발하여 적들에게 %$towers.elven_stargazers.stars_death.damage_min[1]%$-%$towers.elven_stargazers.stars_death.damage_max[1]%$의 마법 피해를 입힙니다.",
["TOWER_STARGAZER_4_RISING_STAR_1_NAME"] = "떠오르는 별",
["TOWER_STARGAZER_4_RISING_STAR_2_DESCRIPTION"] = "별의 수가 %$towers.elven_stargazers.stars_death.stars[2]%$로 증가합니다. 별은 %$towers.elven_stargazers.stars_death.damage_min[2]%$-%$towers.elven_stargazers.stars_death.damage_max[2]%$의 마법 피해를 줍니다.",
["TOWER_STARGAZER_4_RISING_STAR_2_NAME"] = "떠오르는 별",
["TOWER_STARGAZER_4_RISING_STAR_3_DESCRIPTION"] = "별의 수가 %$towers.elven_stargazers.stars_death.stars[3]%$로 증가합니다. 별은 %$towers.elven_stargazers.stars_death.damage_min[3]%$-%$towers.elven_stargazers.stars_death.damage_max[3]%$의 마법 피해를 줍니다.",
["TOWER_STARGAZER_4_RISING_STAR_3_NAME"] = "떠오르는 별",
["TOWER_STARGAZER_4_RISING_STAR_NAME"] = "떠오르는 별",
["TOWER_STARGAZER_4_RISING_STAR_NOTE"] = "별먼지 혁명이야!",
["TOWER_TRICANNON_1_DESCRIPTION"] = "전쟁에 대한 파괴적인 사랑의 노래이자 적과 동맹 모두에게 두려운 광경입니다.",
["TOWER_TRICANNON_1_NAME"] = "트라이캐넌 I",
["TOWER_TRICANNON_2_DESCRIPTION"] = "전쟁에 대한 파괴적인 사랑의 노래이자 적과 동맹 모두에게 두려운 광경입니다.",
["TOWER_TRICANNON_2_NAME"] = "트라이캐넌 II",
["TOWER_TRICANNON_3_DESCRIPTION"] = "전쟁에 대한 파괴적인 사랑의 노래이자 적과 동맹 모두에게 두려운 광경입니다.",
["TOWER_TRICANNON_3_NAME"] = "트라이캐넌 III",
["TOWER_TRICANNON_4_BOMBARDMENT_1_DESCRIPTION"] = "넓은 지역에 빠르게 폭탄을 발사하여 각각 %$towers.tricannon.bombardment.damage_min[1]%$-%$towers.tricannon.bombardment.damage_max[1]%$의 물리 피해를 줍니다.",
["TOWER_TRICANNON_4_BOMBARDMENT_1_NAME"] = "폭격",
["TOWER_TRICANNON_4_BOMBARDMENT_2_DESCRIPTION"] = "더 넓은 지역에 더 많은 폭탄을 발사하여, 각각 %$towers.tricannon.bombardment.damage_min[2]%$-%$towers.tricannon.bombardment.damage_max[2]%$의 물리 피해를 줍니다.",
["TOWER_TRICANNON_4_BOMBARDMENT_2_NAME"] = "폭격",
["TOWER_TRICANNON_4_BOMBARDMENT_3_DESCRIPTION"] = "더 넓은 지역에 더 많은 폭탄을 발사하여, 각각 %$towers.tricannon.bombardment.damage_min[3]%$-%$towers.tricannon.bombardment.damage_max[3]%$의 물리 피해를 줍니다.",
["TOWER_TRICANNON_4_BOMBARDMENT_3_NAME"] = "폭격",
["TOWER_TRICANNON_4_BOMBARDMENT_NAME"] = "폭격",
["TOWER_TRICANNON_4_BOMBARDMENT_NOTE"] = "확장성에 대해 이야기해 봐.",
["TOWER_TRICANNON_4_DESCRIPTION"] = "전쟁에 대한 파괴적인 사랑의 노래이자 적과 동맹 모두에게 두려운 광경입니다.",
["TOWER_TRICANNON_4_NAME"] = "트라이캐넌 IV",
["TOWER_TRICANNON_4_OVERHEAT_1_DESCRIPTION"] = "트라이캐넌의 포신이 %$towers.tricannon.overheat.duration[1]%$ 초 동안 붉게 뜨거워져, 땅을 불태워 초당 %$towers.tricannon.overheat.decal.effect.s_damage[1]%$의 무속성 피해를 입히는 폭탄을 만듭니다.",
["TOWER_TRICANNON_4_OVERHEAT_1_NAME"] = "과열",
["TOWER_TRICANNON_4_OVERHEAT_2_DESCRIPTION"] = "각 불타는 지역은 초당 %$towers.tricannon.overheat.decal.effect.s_damage[2]%$의 무속성 피해를 줍니다. 지속 시간이 %$towers.tricannon.overheat.duration[2]%$ 초로 늘어납니다.",
["TOWER_TRICANNON_4_OVERHEAT_2_NAME"] = "과열",
["TOWER_TRICANNON_4_OVERHEAT_3_DESCRIPTION"] = "각 불타는 지역은 초당 %$towers.tricannon.overheat.decal.effect.s_damage[3]%$의 무속성 피해를 줍니다. 지속 시간이 %$towers.tricannon.overheat.duration[3]%$ 초로 늘어납니다.",
["TOWER_TRICANNON_4_OVERHEAT_3_NAME"] = "과열",
["TOWER_TRICANNON_4_OVERHEAT_NAME"] = "과열",
["TOWER_TRICANNON_4_OVERHEAT_NOTE"] = "우리는 엄청 뜨거워.",
["TOWER_TRICANNON_DESC"] = "다수의 대포 덕분에, 암흑 군대는 화염과 파괴를 비처럼 내리며 현대전에 새로운 정의를 내립니다.",
["TOWER_TRICANNON_NAME"] = "트라이캐넌",
["TUTORIAL_hero_room_hero_points_desc"] = "전투에서 각 영웅의 레벨을 올리면 영웅 포인트를 얻습니다.",
["TUTORIAL_hero_room_hero_points_title"] = "영웅 포인트",
["TUTORIAL_hero_room_power_desc"] = "영웅 포인트를 사용해 영웅의 능력을 구매하고 강화하십시오.",
["TUTORIAL_hero_room_power_title"] = "영웅의 능력",
["TUTORIAL_hero_room_tutorial_navigate_desc"] = "다양한 영웅들을 살펴보십시오.",
["TUTORIAL_hero_room_tutorial_select_desc"] = "전장에서 사용할 영웅들을 선택하십시오.",
["TUTORIAL_item_room_buy_desc"] = "보석을 사용해 전장에서 당신을 도울 아이템을 구매하십시오.",
["TUTORIAL_item_room_buy_title"] = "아이템 구매",
["TUTORIAL_item_room_tutorial_equip_desc"] = "각 슬롯을 사용하여 아이템을 장비하십시오. 순서를 바꾸려면 드래그하십시오.",
["TUTORIAL_item_room_tutorial_navigate_desc"] = "다양한 사용 가능한 아이템을 살펴보십시오.",
["TUTORIAL_tower_room_power_desc"] = "이 스킬들은 타워가 레벨 IV에 도달하면 사용할 수 있습니다.",
["TUTORIAL_tower_room_power_title"] = "레벨 IV 스킬",
["TUTORIAL_tower_room_tutorial_equip_desc"] = "새로운 타워를 장비하여 다른 조합을 사용해 볼 수 있습니다.",
["TUTORIAL_tower_room_tutorial_navigate_desc"] = "다양한 타워들을 살펴보십시오.",
["TUTORIAL_tower_room_tutorial_slots_desc"] = "각 슬롯에 타워를 장비하십시오. 순서를 바꾸려면 드래그하십시오.",
["TUTORIAL_upgrade_room_tooltip_buy_desc"] = "포인트를 사용하여 당신의 능력, 타워, 그리고 영웅을 위한 업그레이드를 구매하십시오.",
["TUTORIAL_upgrade_room_tooltip_souls_desc"] = "캠페인 스테이지를 완료하여 업그레이드 포인트를 획득하십시오.",
["TUTORIAL_upgrade_room_tooltip_souls_title"] = "업그레이드 포인트",
["Tap to continue"] = "눌러서 계속 진행",
["Touch on the path to move the hero."] = "길 위를 누르면 영웅이 이동합니다.",
["Tower construction"] = "타워 건설",
["Typography"] = "타이포그래피",
["UPDATE_POPUP"] = "업데이트하다",
["UPDATING_CLOUDSAVE_MESSAGE"] = "클라우드에 저장된 게임 업데이트 중 ...",
["UPGRADES"] = "업그레이드",
["UPGRADES AND HEROES RESTRICTIONS!"] = "업그레이드 및 영웅 제한!",
["Use the earned hero points to train your hero!"] = "획득한 영웅 포인트로 영웅을 강화하세요!",
["Use the earned stars to improve your towers and powers!"] = "획득한 별을 사용해 타워와 능력을 강화하세요!",
["VICTORY"] = "승리",
["Veteran"] = "베테랑",
["Victory!"] = "승리!",
["Voice Talent"] = "성우",
["WAVE_TOOLTIP_TAP_AGAIN"] = "다시 눌러서 일찍 부르기",
["WAVE_TOOLTIP_TITLE"] = "다가오는 공격",
["We would like to thank"] = "고마운 분들",
["Yes"] = "예",
["You must log in to Google Play game services to track achievements."] = "업적을 추적하려면 Google Play 게임 서비스에 로그인해야합니다.",
["You must watch the whole video."] = "전체 영상을 봐야 합니다",
["You will no longer be tracking achievements."] = "더 이상 도전 과제를 추적할 수 없게 됩니다.",
["_manually_included_characters"] = "$ ¥ ￥ ƒ ₩ € ™ × $ zł ¢ £ ¤ ¥ ƒ ден дин лв. ؋ ৳ ฿ ლ ₡ ₣ ₤ ₥ ₦ ₨ ₩ ₪ ₫ € ₭ ₮ ₱ ₲ ₴ ₵ ₹ ₺ ₽ ﷼",
["alliance_close_to_home_DESCRIPTION"] = "스테이지 시작 시 추가 골드를 제공합니다.",
["alliance_close_to_home_NAME"] = "공유 적립금",
["alliance_corageous_stand_DESCRIPTION"] = "리니리아 타워를 건설할수록 영웅의 생명력이 증가합니다.",
["alliance_corageous_stand_NAME"] = "용감한 스탠드",
["alliance_display_of_true_might_dark_DESCRIPTION"] = "어둠의 군대 영웅의 주문은 이제 화면의 모든 적을 느리게 합니다.",
["alliance_display_of_true_might_dark_NAME"] = "불길한 저주",
["alliance_display_of_true_might_linirea_DESCRIPTION"] = "리니리아 영웅의 주문은 이제 동맹 유닛을 모두 치유하고 재생합니다.",
["alliance_display_of_true_might_linirea_NAME"] = "활력의 축복",
["alliance_flux_altering_coils_DESCRIPTION"] = "모든 출구 깃발을 근처 적을 공간이동 시키는 비전 기둥으로 교체합니다.",
["alliance_flux_altering_coils_NAME"] = "비전 기둥",
["alliance_friends_of_the_crown_DESCRIPTION"] = "리니리아 영웅을 선택할수록 타워 건설 및 업그레이드 비용이 감소합니다.",
["alliance_friends_of_the_crown_NAME"] = "왕관의 친구들",
["alliance_merciless_DESCRIPTION"] = "암흑 군대 타워를 건설할수록 영웅의 공격력이 증가합니다.",
["alliance_merciless_NAME"] = "무자비한 방어",
["alliance_seal_of_punishment_DESCRIPTION"] = "방어 지점을 이를 밟는 적에게 피해를 주는 마법의 인장으로 대체합니다.",
["alliance_seal_of_punishment_NAME"] = "처벌의 인장",
["alliance_shady_company_DESCRIPTION"] = "암흑 군대 영웅을 선택할수록 타워의 공격력이 증가합니다.",
["alliance_shady_company_NAME"] = "수상한 회사",
["alliance_shared_reserves_DESCRIPTION"] = "스테이지 시작 시 추가 골드를 제공합니다.",
["alliance_shared_reserves_NAME"] = "공유 적립금",
["baloon start battle iphone"] = "두 번 눌러서 공격 시작",
["build defensive towers along the road to stop them."] = "길을 따라 방어 타워를 건설하고 적들을 막으세요.",
["build towers to defend the road."] = "타워를 건설해 길목을 차단하세요.",
["check the stage description to see:"] = "스테이지 설명을 확인하세요:",
["deals area damage"] = "범위 피해",
["don't let enemies past this point."] = "적들이 이곳을 지나가서는 안 됩니다.",
["earn gold by killing enemies."] = "적을 처치하면 골드를 얻습니다.",
["good rate of fire"] = "공격 속도가 빠름",
["heroes_desperate_effort_DESCRIPTION"] = "영웅의 공격은 적의 저항력의 10%를 무시합니다.",
["heroes_desperate_effort_NAME"] = "너의 적을 알라",
["heroes_lethal_focus_DESCRIPTION"] = "영웅들은 공격 시 20%의 확률로 치명적인 피해를 입힙니다.",
["heroes_lethal_focus_NAME"] = "치명적인 초점",
["heroes_limit_pushing_DESCRIPTION"] = "각 영웅의 주문을 5 회 사용하면, 사용 대기 시간이 즉시 초기화됩니다.",
["heroes_limit_pushing_NAME"] = "한계를 넓히다",
["heroes_lone_wolves_DESCRIPTION"] = "영웅들은 서로 멀리 떨어져 있을 때 더 많은 경험치를 얻습니다.",
["heroes_lone_wolves_NAME"] = "외로운 늑대들",
["heroes_nimble_physique_DESCRIPTION"] = "영웅들은 적의 공격의 20%를 회피합니다.",
["heroes_nimble_physique_NAME"] = "민첩한 체격",
["heroes_unlimited_vigor_DESCRIPTION"] = "모든 영웅 주문의 사용 대기 시간이 감소합니다.",
["heroes_unlimited_vigor_NAME"] = "무제한 활력",
["heroes_visual_learning_DESCRIPTION"] = "영웅들이 서로 가까이 있을 때 방어력이 10% 향상됩니다.",
["heroes_visual_learning_NAME"] = "도움의 손길",
["high damage, armor piercing"] = "피해 높음, 방어구 관통",
["iron and heroic challenges may have restrictions on upgrades!"] = "강철과 영웅 도전에는 업그레이드 제한이 적용될 수 있습니다!",
["max lvl allowed"] = "최고 레벨 허용됨",
["multi-shot, armor piercing"] = "다중 발사, 방어구 관통",
["no heroes"] = "영웅 없음",
["pause popup"] = "일시 중지됨",
["protect your lands from the enemy attacks."] = "적의 공격으로부터 왕국을 보호하세요.",
["rally range"] = "집결 거리",
["ready for action!"] = "준비 완료!",
["reinforcements_intense_workout_DESCRIPTION"] = "지원군의 생명력과 지속 시간이 향상됩니다.",
["reinforcements_intense_workout_NAME"] = "격렬한 운동",
["reinforcements_master_blacksmiths_DESCRIPTION"] = "지원군의 공격력과 방어력이 향상됩니다.",
["reinforcements_master_blacksmiths_NAME"] = "명장 대장장이",
["reinforcements_night_veil_DESCRIPTION"] = "그림자 궁수의 사거리와 공격 속도가 증가합니다.",
["reinforcements_night_veil_NAME"] = "잿빛 활",
["reinforcements_power_trio_DESCRIPTION"] = "지원군을 호출하면 이제 파라곤 기사도 함께 소환합니다.",
["reinforcements_power_trio_NAME"] = "리니리아 파라곤",
["reinforcements_power_trio_dark_DESCRIPTION"] = "지원군을 호출하면 그림자 까마귀소환사도 함께 소환합니다.",
["reinforcements_power_trio_dark_NAME"] = "그림자 까마귀소환사",
["reinforcements_rebel_militia_DESCRIPTION"] = "지원군을 튼튼한 갑옷을 입은 내구성 있는 전사들인 반란군으로 대체합니다.",
["reinforcements_rebel_militia_NAME"] = "리니리아 민병군",
["reinforcements_shadow_archer_DESCRIPTION"] = "지원군을 멀리서 공중 유닛을 공격할 수 있는 그림자 궁수로 대체합니다.",
["reinforcements_shadow_archer_NAME"] = "그림자 기사단",
["reinforcements_thorny_armor_DESCRIPTION"] = "리니리아 반란군은 적의 근접 공격으로부터 일부 피해를 반사합니다.",
["reinforcements_thorny_armor_NAME"] = "가시 갑옷",
["resists damage from"] = "피해 저항:",
["select the rally point control"] = "집결 지점을 선택하세요.",
["select the tower you want to build!"] = "건설할 타워를 선택하세요!",
["select where you want to move your soldiers"] = "병사가 이동할 지점을 선택하세요.",
["soldiers block enemies"] = "병사는 적의 이동을 차단합니다.",
["some enemies enjoy different levels of magic resistance that protects them against magical attacks."] = "일부 적들은 각자 다른 수준의 마법 저항력을 갖고 있어, 마법 공격의 피해를 더 적게 받습니다.",
["some enemies wear armor of different strengths that protects them against non-magical attacks."] = "일부 적들은 여러 성능의 갑옷을 입고 마법이 아닌 물리적 공격으로부터 몸을 보호합니다.",
["tap these!"] = "이들을 누르세요!",
["tap to continue..."] = "눌러서 계속 진행...",
["tap twice to call wave"] = "두 번 눌러서 공격 시작",
["this is a strategic point."] = "여기는 전략 지점입니다.",
["towers_favorite_customer_DESCRIPTION"] = "스킬의 마지막 레벨을 구매할 때, 비용이 50% 감소합니다.",
["towers_favorite_customer_NAME"] = "인기 고객",
["towers_golden_time_DESCRIPTION"] = "웨이브를 조기 시작할 때 얻는 보너스 골드가 증가합니다.",
["towers_golden_time_NAME"] = "골든 타임",
["towers_improved_formulas_DESCRIPTION"] = "모든 타워의 폭발로 인한 피해가 극대화되고 그 영향 범위가 증가합니다.",
["towers_improved_formulas_NAME"] = "개선된 공식들",
["towers_keen_accuracy_DESCRIPTION"] = "모든 타워 스킬의 사용 대기 시간이 20% 감소합니다.",
["towers_keen_accuracy_NAME"] = "전투 열정",
["towers_royal_training_DESCRIPTION"] = "타워 유닛의 생성 시간과 지원군의 재사용 대기 시간을 줄입니다.",
["towers_royal_training_NAME"] = "행동 촉구",
["towers_scoping_mechanism_DESCRIPTION"] = "모든 타워의 사거리가 10% 증가합니다.",
["towers_scoping_mechanism_NAME"] = "범위 결정 메커니즘",
["towers_war_rations_DESCRIPTION"] = "모든 타워 유닛의 생명력이 10% 증가합니다.",
["towers_war_rations_NAME"] = "전시 배급식량",
["towers_wise_investment_DESCRIPTION"] = "타워 판매 시 90%의 골드를 환급합니다.",
["towers_wise_investment_NAME"] = "현명한 투자",
["wOOt!"] = "앗!",
["you can adjust your soldiers rally point to make them defend a different area."] = "병사의 집결 지점을 바꿔 다른 지역을 방어하게 할 수 있습니다.",
}
