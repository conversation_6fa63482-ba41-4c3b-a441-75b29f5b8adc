-- ------------------------------------------------
-- -- WARNING: DO NOT EDIT BY HAND                 
-- -- Generated by kr-i18n/tools/strings-export.lua
-- ------------------------------------------------
return {
["!!!COMMENT_LOCALIZATION_SOURCE"] = "Transfluent",
["%d Life"] = "%d 點生命值",
["%d Lives"] = "%d 條生命",
["%i sec."] = "%i 秒。",
["- if heroes are allowed"] = "- 若允許英雄",
["- max lvl allowed"] = "- 允許最高等級",
["- max upgrade level allowed"] = "- 允許最高升級等級",
["- no heroes"] = "- 無英雄",
["A good challenge!"] = "不錯的挑戰！",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "不成人形的威利",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "不成人形的哈利",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "不成人形的傑佛瑞",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "便秘的尼可拉斯",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "艾德禾歲偶",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "霍布禾歲偶",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "奧多禾歲偶",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "不成人形的西追",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "哈爾禾歲偶",
["ACHIEVEMENT"] = "成就",
["ACHIEVEMENTS"] = "成就",
["ACHIEVEMENTS_TITLE"] = "成就",
["ACHIEVEMENT_AGE_OF_HEROES_DESCRIPTION"] = "贏下所有戰役關卡的英豪挑戰。",
["ACHIEVEMENT_AGE_OF_HEROES_NAME"] = "英豪時代",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_DESCRIPTION"] = "消滅182隻瞬視者。",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_NAME"] = "那些小事",
["ACHIEVEMENT_ARACHNED_DESCRIPTION"] = "擊敗蜘蛛女王\"狼蛛\"米迦爾。",
["ACHIEVEMENT_ARACHNED_NAME"] = "無蛛道",
["ACHIEVEMENT_A_COON_OF_SURPRISES_DESCRIPTION"] = "協助佛里多逃脫。",
["ACHIEVEMENT_A_COON_OF_SURPRISES_NAME"] = "充滿驚喜的繭",
["ACHIEVEMENT_A_TEST_OF_PROWESS_DESCRIPTION"] = "在任一關卡中取得3星。",
["ACHIEVEMENT_A_TEST_OF_PROWESS_NAME"] = "能力測試",
["ACHIEVEMENT_BREAKER_OF_CHAINS_DESCRIPTION"] = "在猩紅礦坑中拯救四名精靈。",
["ACHIEVEMENT_BREAKER_OF_CHAINS_NAME"] = "破鐐之人",
["ACHIEVEMENT_BUTTERTENTACLES_DESCRIPTION"] = "完成惡視魔塔，且魔蒂婭絲不曾逮住我方的單位。",
["ACHIEVEMENT_BUTTERTENTACLES_NAME"] = "滑溜士兵",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_DESCRIPTION"] = "擊敗泛視先知魔蒂婭絲。",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_NAME"] = "再見，美麗",
["ACHIEVEMENT_CIRCLE_OF_LIFE_DESCRIPTION"] = "參加新生樹靈的慶生會。",
["ACHIEVEMENT_CIRCLE_OF_LIFE_NAME"] = "生生不息",
["ACHIEVEMENT_CLEANSE_THE_KING_DESCRIPTION"] = "拯救利尼維亞國王。",
["ACHIEVEMENT_CLEANSE_THE_KING_NAME"] = "光耀吾王",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_DESCRIPTION"] = "在不清除戰略點障礙物的情況下完成荒蕪邊境。",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_NAME"] = "不打掃也沒差",
["ACHIEVEMENT_CONJUNTIVICTORY_DESCRIPTION"] = "擊敗全視魔眼。",
["ACHIEVEMENT_CONJUNTIVICTORY_NAME"] = "戰勝結膜炎",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_DESCRIPTION"] = "在遙遠虛空的所有關卡取得3星。",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_NAME"] = "虛空霸主",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_DESCRIPTION"] = "在野獸巢穴收集三塊豬肉。",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_NAME"] = "這個世界是我的",
["ACHIEVEMENT_CROWD_CONTROL_DESCRIPTION"] = "在沒有任何血壤巨獸從谷底被召喚的情況下完成魔化峽谷。",
["ACHIEVEMENT_CROWD_CONTROL_NAME"] = "場地控制",
["ACHIEVEMENT_CROW_SCARER_DESCRIPTION"] = "嚇跑蒼涼谷所有的烏鴉。",
["ACHIEVEMENT_CROW_SCARER_NAME"] = "趕鴉人",
["ACHIEVEMENT_CRYSTAL_CLEAR_DESCRIPTION"] = "在忘卻深谷的所有關卡取得3星。",
["ACHIEVEMENT_CRYSTAL_CLEAR_NAME"] = "晶瑩剔透",
["ACHIEVEMENT_DARK_LIEUTENANT_DESCRIPTION"] = "將芮琳升到10級。",
["ACHIEVEMENT_DARK_LIEUTENANT_NAME"] = "黑暗中尉",
["ACHIEVEMENT_DARK_RUTHLESSNESS_DESCRIPTION"] = "只使用黑暗大軍的防禦塔和英雄贏得任一關卡。",
["ACHIEVEMENT_DARK_RUTHLESSNESS_NAME"] = "黑暗無情",
["ACHIEVEMENT_DISTURBING_THE_PEACE_DESCRIPTION"] = "打斷在統治圓頂中工人的午休時間。",
["ACHIEVEMENT_DISTURBING_THE_PEACE_NAME"] = "打擾安寧",
["ACHIEVEMENT_DLC1_WIN_BOSS_DESCRIPTION"] = "擊敗詭鬚，阻止繼續建造戰爭機器。",
["ACHIEVEMENT_DLC1_WIN_BOSS_NAME"] = "我解僱我自己，這...",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_DESCRIPTION"] = "蒐集怒風嶼的8個紅包。",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_NAME"] = "恭喜發財，萬事如意",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_DESCRIPTION"] = "突入堡壘，擊敗牛魔王。",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_NAME"] = "神猴歸來",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_DESCRIPTION"] = "擊敗鐵扇公主與其麾下的妖水軍團。",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_NAME"] = "妖風之律動",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_DESCRIPTION"] = "擊敗紅孩兒與其麾下的焰火軍團。",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_NAME"] = "直到烈火國......",
["ACHIEVEMENT_DOMO_ARIGATO_DESCRIPTION"] = "讓巨型核心中的大拳頭壓扁20個敵人。",
["ACHIEVEMENT_DOMO_ARIGATO_NAME"] = "豆摸，阿里嘎多",
["ACHIEVEMENT_FACTORY_STRIKE_DESCRIPTION"] = "完成狂亂產線關卡，且詭鬚未曾啟動機械裝置。",
["ACHIEVEMENT_FACTORY_STRIKE_NAME"] = "工廠罷工",
["ACHIEVEMENT_FIELD_TRIP_RUINER_DESCRIPTION"] = "撲滅露營者的營火。",
["ACHIEVEMENT_FIELD_TRIP_RUINER_NAME"] = "田野調查毀滅者",
["ACHIEVEMENT_FOREST_PROTECTOR_DESCRIPTION"] = "將納魯升到10級。",
["ACHIEVEMENT_FOREST_PROTECTOR_NAME"] = "森林守護者",
["ACHIEVEMENT_GARBAGE_DISPOSAL_DESCRIPTION"] = "在10名瘋狂廢鐵匠製造廢鐵無人機前就消滅他們。",
["ACHIEVEMENT_GARBAGE_DISPOSAL_NAME"] = "垃圾處理",
["ACHIEVEMENT_GEM_SPILLER_DESCRIPTION"] = "弄壞所有的寶石籃子。",
["ACHIEVEMENT_GEM_SPILLER_NAME"] = "給我寶石",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_DESCRIPTION"] = "解開謎題並召喚樂隊。",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_NAME"] = "快開始派對吧",
["ACHIEVEMENT_GIFT_OF_LIFE_DESCRIPTION"] = "釋放複製室中的實驗體。",
["ACHIEVEMENT_GIFT_OF_LIFE_NAME"] = "都是存在著的",
["ACHIEVEMENT_GREENLIT_ALLIES_DESCRIPTION"] = "召喚10名樹靈棘矛手",
["ACHIEVEMENT_GREENLIT_ALLIES_NAME"] = "綠林豪傑",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_DESCRIPTION"] = "找到鱷魚國王。",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_NAME"] = "向K大王致意！",
["ACHIEVEMENT_HEARTLESS_VICTORY_DESCRIPTION"] = "在不使用樹靈心臟力量的情況下通過永光之心關卡。",
["ACHIEVEMENT_HEARTLESS_VICTORY_NAME"] = "無心流",
["ACHIEVEMENT_INTO_THE_OGREVERSE_DESCRIPTION"] = "發現神秘蜘蛛怪客的秘密。",
["ACHIEVEMENT_INTO_THE_OGREVERSE_NAME"] = "不友善的鄰居",
["ACHIEVEMENT_IRONCLAD_DESCRIPTION"] = "贏下所有戰役關卡的鐵腕挑戰。",
["ACHIEVEMENT_IRONCLAD_NAME"] = "鐵腕無懼",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_DESCRIPTION"] = "幫助蘭克釣到5顆盧比。",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_NAME"] = "不可以跟任何人說喔！",
["ACHIEVEMENT_KEPT_YOU_WAITING_DESCRIPTION"] = "在巨型核心中找到躲躲藏藏的士兵。",
["ACHIEVEMENT_KEPT_YOU_WAITING_NAME"] = "讓你久等了？",
["ACHIEVEMENT_LEARNING_THE_ROPES_DESCRIPTION"] = "完成教學並拿到3星。",
["ACHIEVEMENT_LEARNING_THE_ROPES_NAME"] = "基礎入門",
["ACHIEVEMENT_LINIREAN_RESISTANCE_DESCRIPTION"] = "只使用利尼維亞的防禦塔和英雄贏得任一關卡。",
["ACHIEVEMENT_LINIREAN_RESISTANCE_NAME"] = "利尼維亞的反抗",
["ACHIEVEMENT_LUCAS_SPIDER_DESCRIPTION"] = "陪盧咕斯玩個痛快！",
["ACHIEVEMENT_LUCAS_SPIDER_NAME"] = "小蜘蛛盧咕斯",
["ACHIEVEMENT_MASTER_TACTICIAN_DESCRIPTION"] = "在不可能難度下完成戰役。",
["ACHIEVEMENT_MASTER_TACTICIAN_NAME"] = "戰術大師",
["ACHIEVEMENT_MECHANICAL_BURNOUT_DESCRIPTION"] = "過度餵食黑鋼之門關卡中的機器。",
["ACHIEVEMENT_MECHANICAL_BURNOUT_NAME"] = "機器故障",
["ACHIEVEMENT_MIGHTY_III_DESCRIPTION"] = "擊殺10000名敵人。",
["ACHIEVEMENT_MIGHTY_III_NAME"] = "蓋世無雙",
["ACHIEVEMENT_MIGHTY_II_DESCRIPTION"] = "擊殺3000名敵人。",
["ACHIEVEMENT_MIGHTY_II_NAME"] = "勢不可擋",
["ACHIEVEMENT_MIGHTY_I_DESCRIPTION"] = "擊殺500名敵人。",
["ACHIEVEMENT_MIGHTY_I_NAME"] = "小試鋒芒",
["ACHIEVEMENT_MOST_DELICIOUS_DESCRIPTION"] = "餵樹靈大吉吃一些蜂蜜。",
["ACHIEVEMENT_MOST_DELICIOUS_NAME"] = "頂級美味",
["ACHIEVEMENT_NATURES_WRATH_DESCRIPTION"] = "使用樹靈心臟擊殺30名敵人。",
["ACHIEVEMENT_NATURES_WRATH_NAME"] = "自然之怒",
["ACHIEVEMENT_NONE_SHALL_PASS_DESCRIPTION"] = "通過野獸巢穴，勿放任何敵人過門。",
["ACHIEVEMENT_NONE_SHALL_PASS_NAME"] = "無人可過！",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_DESCRIPTION"] = "提前召喚15波敵人。",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_NAME"] = "一秒都不能浪費",
["ACHIEVEMENT_NO_FLY_ZONE_DESCRIPTION"] = "擊殺50隻天網蛛。",
["ACHIEVEMENT_NO_FLY_ZONE_NAME"] = "禁飛區",
["ACHIEVEMENT_OBLITERATE_DESCRIPTION"] = "在【巨大的威脅】的每個關卡中都找到被封印的機器人部件。",
["ACHIEVEMENT_OBLITERATE_NAME"] = "憤怒的業火！",
["ACHIEVEMENT_ONE_SHOT_TOWER_DESCRIPTION"] = "使用單一闇夜祭壇的光束消滅10名敵人。",
["ACHIEVEMENT_ONE_SHOT_TOWER_NAME"] = "巔峰紀錄",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_DESCRIPTION"] = "於不可能難度中擊敗野獸之王血輾，且不要讓牠有機會跳躍！",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_NAME"] = "絆腳石",
["ACHIEVEMENT_OVER_THE_EDGE_DESCRIPTION"] = "將樹靈族從樹頂推下去。",
["ACHIEVEMENT_OVER_THE_EDGE_NAME"] = "遊戲結束",
["ACHIEVEMENT_OVINE_JOURNALISM_DESCRIPTION"] = "在每一塊戰役區域中都找到霹靂羊，綿羊記者！",
["ACHIEVEMENT_OVINE_JOURNALISM_NAME"] = "羊視新聞",
["ACHIEVEMENT_PEST_CONTROL_DESCRIPTION"] = "擊敗300隻眼精。",
["ACHIEVEMENT_PEST_CONTROL_NAME"] = "害蟲防治",
["ACHIEVEMENT_PLAYFUL_FRIENDS_DESCRIPTION"] = "在永光之心與所有樹靈一起玩「挖洞」遊戲。",
["ACHIEVEMENT_PLAYFUL_FRIENDS_NAME"] = "快樂玩伴",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_DESCRIPTION"] = "擊敗野獸之王血輾。",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_NAME"] = "不想再看到豬了",
["ACHIEVEMENT_PROMOTION_DENIED_DESCRIPTION"] = "在其變成邪教穢偶前消滅30名邪教祭司。",
["ACHIEVEMENT_PROMOTION_DENIED_NAME"] = "升官遭拒",
["ACHIEVEMENT_ROCK_BEATS_ROCK_DESCRIPTION"] = "讓石像自己打敗自己。",
["ACHIEVEMENT_ROCK_BEATS_ROCK_NAME"] = "石頭打敗了...石頭？",
["ACHIEVEMENT_ROOM_achievement_claim"] = "領取獎勵！",
["ACHIEVEMENT_ROYAL_CAPTAIN_DESCRIPTION"] = "將法斯帕升到10級。",
["ACHIEVEMENT_ROYAL_CAPTAIN_NAME"] = "皇家隊長",
["ACHIEVEMENT_RUNEQUEST_DESCRIPTION"] = "在永光森林啟用全部六道符文。",
["ACHIEVEMENT_RUNEQUEST_NAME"] = "符石之謎",
["ACHIEVEMENT_RUST_IN_PEACE_DESCRIPTION"] = "完成任一關卡，且沒有任何附身鎧甲重新站起。",
["ACHIEVEMENT_RUST_IN_PEACE_NAME"] = "願鏽者安息",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_DESCRIPTION"] = "在不失去任何樹靈之花的情況下通過關卡。",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_NAME"] = "森之救世者",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_DESCRIPTION"] = "在永光森林的所有關卡取得3星。",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_NAME"] = "森林救世主",
["ACHIEVEMENT_SCRAMBLED_EGGS_DESCRIPTION"] = "在孵化前就殺死50顆鱷達出奇蛋。",
["ACHIEVEMENT_SCRAMBLED_EGGS_NAME"] = "炒鱷魚蛋",
["ACHIEVEMENT_SEASONED_GENERAL_DESCRIPTION"] = "在老兵難度下完成戰役。",
["ACHIEVEMENT_SEASONED_GENERAL_NAME"] = "戰場老將",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_DESCRIPTION"] = "打敗吞世者，噬界災鱷。",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_NAME"] = "掰掰，鱷魚",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_DESCRIPTION"] = "在不讓詭鬚點燃你的塔的情況下，完成統治圓頂。",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_NAME"] = "閉嘴！",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_DESCRIPTION"] = "使用500次英雄能力。",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_NAME"] = "招牌技能",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_DESCRIPTION"] = "幫助傑哈特殺死那個樹的怪物。",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_NAME"] = "狩魔之銀劍",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_DESCRIPTION"] = "幫助友好的鱷呱呱發動船隻。",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_NAME"] = "操作頂呱呱！",
["ACHIEVEMENT_SPECTRAL_FURY_DESCRIPTION"] = "擊敗汎里埃，阻止歸魂軍團的侵襲。",
["ACHIEVEMENT_SPECTRAL_FURY_NAME"] = "幽魂狂怒",
["ACHIEVEMENT_STARLIGHT_DESCRIPTION"] = "幫助佛里多和山米逃離大蜘蛛。",
["ACHIEVEMENT_STARLIGHT_NAME"] = "星光閃耀",
["ACHIEVEMENT_TAKE_ME_HOME_DESCRIPTION"] = "將哥布林里夫送回原本的次元。",
["ACHIEVEMENT_TAKE_ME_HOME_NAME"] = "帶我走",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_DESCRIPTION"] = "呼叫1000名援兵。",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_NAME"] = "救星來了！",
["ACHIEVEMENT_TIPPING_THE_SCALES_DESCRIPTION"] = "讓羅賓·函落水。",
["ACHIEVEMENT_TIPPING_THE_SCALES_NAME"] = "破壞平衡",
["ACHIEVEMENT_TREE_HUGGER_DESCRIPTION"] = "通過迷霧遺跡，且至少保有一棵怪異樹木幸免於難。",
["ACHIEVEMENT_TREE_HUGGER_NAME"] = "愛護樹木",
["ACHIEVEMENT_TURN_A_BLIND_EYE_DESCRIPTION"] = "擊殺100隻處於注目狀態下的邪惡召喚物。",
["ACHIEVEMENT_TURN_A_BLIND_EYE_NAME"] = "真的有看見？",
["ACHIEVEMENT_UNBOUND_VICTORY_DESCRIPTION"] = "在沒有任何夢魘變成武裝夢魘的情況下完成罪惡岔口。",
["ACHIEVEMENT_UNBOUND_VICTORY_NAME"] = "非武裝勝利",
["ACHIEVEMENT_UNENDING_RICHES_DESCRIPTION"] = "總共收集15萬枚金幣。",
["ACHIEVEMENT_UNENDING_RICHES_NAME"] = "無盡財富",
["ACHIEVEMENT_UNTAMED_BEAST_DESCRIPTION"] = "將格里姆森升到10級。",
["ACHIEVEMENT_UNTAMED_BEAST_NAME"] = "不羈野獸",
["ACHIEVEMENT_WAR_MASONRY_DESCRIPTION"] = "建造100座防禦塔。",
["ACHIEVEMENT_WAR_MASONRY_NAME"] = "防禦工事",
["ACHIEVEMENT_WEIRDER_THINGS_DESCRIPTION"] = "幫助厄尼和戴斯東擊退枯萎農地上的瞬移獸。",
["ACHIEVEMENT_WEIRDER_THINGS_NAME"] = "奇怪物語",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_DESCRIPTION"] = "在【不滅狂怒】的每個關卡中都找到難以捉摸的貓。",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_NAME"] = "我們每個都是瘋子",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_DESCRIPTION"] = "在其召喚夢魘前消滅15名扭曲姐妹。",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_NAME"] = "我們不再忍受",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_DESCRIPTION"] = "修復尼克和馬蒂的傳送槍。",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_NAME"] = "Wobba-Lubba-Dub-Dub！",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_DESCRIPTION"] = "在不可能難度中拯救魔化迪納斯，且不讓泛視先知魔蒂婭絲施放任何幻象。",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_NAME"] = "汝不得施法！",
["ADS_MESSAGE_OK"] = "OK的",
["ADS_MESSAGE_TITLE"] = "更多寶石",
["ALERT_VERSION"] = "遊戲版本已更新。請在商店內下載。",
["APPLY_SETTINGS_AND_RESTART"] = "重新啟動以應用更改？",
["ARCHER TOWER"] = "弓兵",
["ARE YOU SURE YOU WANT TO QUIT?"] = "確定要退出嗎？",
["ARMORED ENEMIES!"] = "裝甲敵人！",
["ARTILLERY"] = "火炮",
["Achievements"] = "成就",
["Advanced"] = "高級",
["Average"] = "平均",
["BARRACKS"] = "兵營",
["BOSS_BULL_KING_NAME"] = "牛魔王",
["BOSS_CORRUPTED_DENAS_DESCRIPTION"] = "利尼維亞前任國王，被全視魔眼教徒的黑暗力量，轉化為巨大又可憎的怪物。",
["BOSS_CORRUPTED_DENAS_EXTRA"] = "- 召喚眼精",
["BOSS_CORRUPTED_DENAS_NAME"] = "魔化迪納斯",
["BOSS_CROCS_DESCRIPTION"] = "活生生的「飢餓」本身，若是放任其自由行動，甚至能吞噬這整個世界的遠古存在。",
["BOSS_CROCS_EXTRA"] = "- 吞噬防禦塔\n- 吃飽時進化\n- 召喚鱷達出奇蛋",
["BOSS_CROCS_LVL1_DESCRIPTION"] = "活生生的「飢餓」本身，若是放任其自由行動，甚至能吞噬這整個世界的遠古存在。",
["BOSS_CROCS_LVL1_EXTRA"] = "- 吞噬防禦塔\n- 吃飽時進化\n- 召喚鱷達出奇蛋",
["BOSS_CROCS_LVL1_NAME"] = "噬界災鱷",
["BOSS_CROCS_LVL2_DESCRIPTION"] = "活生生的「飢餓」本身，若是放任其自由行動，甚至能吞噬這整個世界的遠古存在。",
["BOSS_CROCS_LVL2_EXTRA"] = "- 吞噬防禦塔\n- 吃飽時進化\n- 召喚鱷達出奇蛋",
["BOSS_CROCS_LVL2_NAME"] = "噬界災鱷",
["BOSS_CROCS_LVL3_DESCRIPTION"] = "活生生的「飢餓」本身，若是放任其自由行動，甚至能吞噬這整個世界的遠古存在。",
["BOSS_CROCS_LVL3_EXTRA"] = "- 吞噬防禦塔\n- 吃飽時進化\n- 召喚鱷達出奇蛋",
["BOSS_CROCS_LVL3_NAME"] = "噬界災鱷",
["BOSS_CROCS_LVL4_DESCRIPTION"] = "活生生的「飢餓」本身，若是放任其自由行動，甚至能吞噬這整個世界的遠古存在。",
["BOSS_CROCS_LVL4_EXTRA"] = "- 吞噬防禦塔\n- 吃飽時進化\n- 召喚鱷達出奇蛋",
["BOSS_CROCS_LVL4_NAME"] = "噬界災鱷",
["BOSS_CROCS_LVL5_DESCRIPTION"] = "活生生的「飢餓」本身，若是放任其自由行動，甚至能吞噬這整個世界的遠古存在。",
["BOSS_CROCS_LVL5_EXTRA"] = "- 吞噬防禦塔\n- 吃飽時進化\n- 召喚鱷達出奇蛋",
["BOSS_CROCS_LVL5_NAME"] = "噬界災鱷",
["BOSS_CROCS_NAME"] = "噬界災鱷",
["BOSS_CULT_LEADER_DESCRIPTION"] = "現任啟明邪教的領導者，魔蒂婭絲作為全視魔眼的左右手，負責制定侵入各個世界的計畫。",
["BOSS_CULT_LEADER_EXTRA"] = "- 未被阻擋時具有高護甲值和魔法抗性\n- 高範圍傷害",
["BOSS_CULT_LEADER_NAME"] = "泛視先知魔蒂婭絲",
["BOSS_GRYMBEARD_DESCRIPTION"] = "極度自我中心、狂妄自大的矮人。極其瘋狂，十分危險。",
["BOSS_GRYMBEARD_EXTRA"] = "- 向我方單位發射鋼鐵飛拳",
["BOSS_GRYMBEARD_NAME"] = "詭鬚",
["BOSS_MACHINIST_DESCRIPTION"] = "乘坐上他的最新發明，詭鬚用火雨流星追擊他的敵人。",
["BOSS_MACHINIST_EXTRA"] = "- 飛行\n- 對單位射出廢鐵",
["BOSS_MACHINIST_NAME"] = "詭鬚",
["BOSS_NAVIRA_DESCRIPTION"] = "失去恩典又觸及禁忌死亡魔法之人，汎里埃一心盼望奪回精靈族的榮光。",
["BOSS_NAVIRA_EXTRA"] = "- 使用火焰彈封禁防禦塔\n- 變形為無法阻擋的龍捲風",
["BOSS_NAVIRA_NAME"] = "汎里埃",
["BOSS_PIG_DESCRIPTION"] = "自封為萬獸頂點的野獸之王，擅長用巨大的連枷粉碎敵人。",
["BOSS_PIG_EXTRA"] = "－跳躍力強，可向前移動極長的距離",
["BOSS_PIG_NAME"] = "野獸之王血輾",
["BOSS_PRINCESS_IRON_FAN_DESCRIPTION"] = "集優雅與致命於一身，鐵扇公主可不僅是牛魔王的妻子，更是一位不容小覷的女妖。她沉靜的面容底下有著深藏不漏的謀略，手中碩大的鐵扇一揮便能吹熄焰火，亦能颳起風暴。",
["BOSS_PRINCESS_IRON_FAN_EXTRA"] = "- 創造分身\n- 以法器將英雄封印\n- 將防禦塔變化為敵軍出生點",
["BOSS_PRINCESS_IRON_FAN_NAME"] = "鐵扇公主",
["BOSS_REDBOY_TEEN_DESCRIPTION"] = "盛氣凜然的妖王之子，以其不可一世而為人所知；更是個性情火爆又野心勃勃的小子。他不僅執掌著「三昧真火」，更是槍術的高手。為鐵扇公主與牛魔王的兒子。",
["BOSS_REDBOY_TEEN_EXTRA"] = "- 巨大範圍攻擊\n- 指揮巨龍封鎖防禦塔",
["BOSS_REDBOY_TEEN_NAME"] = "紅孩兒",
["BOSS_SPIDER_QUEEN_DESCRIPTION"] = "古老的蜘蛛女王，她的力量既原始又強大。從沉眠中被喚醒的她，將會奪回屬於女王的一切。",
["BOSS_SPIDER_QUEEN_EXTRA"] = "- 封鎖防禦塔\n- 吸取周圍敵人生命值\n- 召喚吸血蜘蛛\n- 遮蔽視線的蛛網攻擊",
["BOSS_SPIDER_QUEEN_NAME"] = "米迦爾",
["BRIEFING_LEVEL_WARNING"] = "這個戰役的難度等級很高。",
["BUILD HERE!"] = "此處建造！",
["BUTTON_BUG_CRASH"] = "遊戲故障",
["BUTTON_BUG_OTHER"] = "其他",
["BUTTON_BUG_REPORT"] = "錯誤",
["BUTTON_BUY"] = "購買",
["BUTTON_BUY_UPGRADE"] = "購買升級",
["BUTTON_CLOSE"] = "關閉",
["BUTTON_CONFIRM"] = "確認",
["BUTTON_CONTINUE"] = "繼續",
["BUTTON_DISABLE"] = "停用",
["BUTTON_DONE"] = "完成",
["BUTTON_ENDLESS_QUIT"] = "退出",
["BUTTON_ENDLESS_TRYAGAIN"] = "重試",
["BUTTON_GET_GEMS"] = "獲取物品",
["BUTTON_LEVEL_SELECT_FIGHT"] = "戰鬥！",
["BUTTON_LOST_CONTENT"] = "遺失內容",
["BUTTON_MAIN_MENU"] = "主選單",
["BUTTON_NO"] = "否",
["BUTTON_OK"] = "好的！",
["BUTTON_OPEN"] = "開",
["BUTTON_QUIT"] = "退出",
["BUTTON_RESET"] = "重設",
["BUTTON_RESTART"] = "重新開始",
["BUTTON_RESUME"] = "返回",
["BUTTON_TO_BATTLE_1"] = "進入",
["BUTTON_TO_BATTLE_2"] = "戰鬥",
["BUTTON_UNDO"] = "取消",
["BUTTON_YES"] = "是",
["BUY UPGRADES!"] = "購買升級！",
["Basic"] = "基本",
["Basic Tower Types"] = "基本防禦塔類型",
["CARD_REWARDS_CAMPAIGN"] = "新戰役關卡！",
["CARD_REWARDS_DLC_1"] = "巨大的威脅",
["CARD_REWARDS_DLC_2"] = "大聖遊記",
["CARD_REWARDS_HERO"] = "新英雄！",
["CARD_REWARDS_TOWER"] = "新防禦塔！",
["CARD_REWARDS_TOWER_LEVEL"] = "新防禦塔升級！",
["CARD_REWARDS_TOWER_LEVEL_PREFIX"] = "等級",
["CARD_REWARDS_UPDATE_01"] = "不滅狂怒",
["CARD_REWARDS_UPDATE_02"] = "古代罪餓",
["CARD_REWARDS_UPDATE_03"] = "蜘蛛恐懼",
["CARD_REWARDS_UPGRADES"] = "升級點數！",
["CArmor0"] = "無",
["CArmor1"] = "低",
["CArmor2"] = "中",
["CArmor3"] = "高",
["CArmor4"] = "超高",
["CArmor9"] = "免疫",
["CArmorSmall0"] = "無",
["CArmorSmall1"] = "低",
["CArmorSmall2"] = "中",
["CArmorSmall3"] = "高",
["CArmorSmall4"] = "超高",
["CArmorSmall9"] = "免疫",
["CHANGE_LANGUAGE_QUESTION"] = "確定更改語言設置嗎？",
["CINEMATICS_TAP_TO_CONTINUE_KR1"] = "按一下以繼續...",
["CINEMATICS_TAP_TO_CONTINUE_KR2"] = "按一下以繼續...",
["CINEMATICS_TAP_TO_CONTINUE_KR3"] = "按一下以繼續...",
["CINEMATICS_TAP_TO_CONTINUE_KR5"] = "按一下以繼續...",
["CLAIM_GIFT"] = "領取禮物 ",
["CLICK HERE TO SKIP.\nPLEASE DON'T"] = "點我跳過 \n(拜託不要）",
["CLICK HERE!"] = "按這裡！",
["CLICK ON THE ROAD"] = "按一下道路",
["CLICK TO CALL IT EARLY"] = "按一下，提前召喚下一波",
["CLOUDSYNC_PLEASE_WAIT"] = "更新雲端存檔...",
["CLOUD_DIALOG_NO"] = "不",
["CLOUD_DIALOG_OK"] = "OK的",
["CLOUD_DIALOG_YES"] = "好",
["CLOUD_DOWNLOAD_QUESTION"] = "從iCloud下載遊戲存檔嗎？",
["CLOUD_DOWNLOAD_TITLE"] = "iCloud下載",
["CLOUD_SAVE"] = "雲端存檔",
["CLOUD_SAVE_DISABLE_EXTRA"] = "註：如果解除安裝遊戲，你可能會失去遊戲進度。",
["CLOUD_SAVE_DISABLE_GENERIC_DESCRIPTION"] = "你確定要停用在雲端儲存你的遊戲進度嗎？",
["CLOUD_SAVE_OFF"] = "雲存儲關閉",
["CLOUD_SAVE_ON"] = "雲存儲已開啟",
["CLOUD_UPLOAD_QUESTION"] = "將遊戲存檔上傳到iCloud嗎？",
["CLOUD_UPLOAD_TITLE"] = "上傳到iCloud",
["COMIC_10_1_KR5_KR5"] = "放開我！我是為了復興精靈的王國！",
["COMIC_10_2_KR5_KR5"] = "住手吧，哥哥。這種做法只是對艾納妮的褻瀆。",
["COMIC_10_3_KR5_KR5"] = "謝謝，我的老徒兒。接下來就交給我們吧。",
["COMIC_10_4_KR5_KR5"] = "之後，在營地。",
["COMIC_10_5_KR5_KR5"] = "所以......你確定佛則南信得過嗎？",
["COMIC_10_6_KR5_KR5"] = "我們一直盯著他的一舉一動...",
["COMIC_10_7_KR5_KR5"] = "...但目前為止他都表現相當良好。",
["COMIC_10_8_KR5_KR5"] = "呵，目前啦......",
["COMIC_11_1_KR5_KR5"] = "寧靜之沼，彷獲生機...",
["COMIC_11_2_KR5_KR5"] = "...無人之處，似有殺心...",
["COMIC_11_3_KR5_KR5"] = "...潛於幽沼，步步進逼...",
["COMIC_11_4_KR5_KR5"] = "...飢餓兇獸，悄然到來。",
["COMIC_11_5_KR5_KR5"] = "小心啊！",
["COMIC_11_6_KR5_KR5"] = "我們遭到包圍了！",
["COMIC_11_7_KR5_KR5"] = "快去啊，小仙靈！只有你樹我們的希望了！",
["COMIC_12_1_KR5_KR5"] = "只是封印你是我太天真了。我不會再犯同樣的錯了。",
["COMIC_12_2_KR5_KR5"] = "不－－－！！！",
["COMIC_12_3_KR5_KR5"] = "接受永恆的驅逐吧！！",
["COMIC_12_4_KR5_KR5"] = "咳！",
["COMIC_12_5_KR5_KR5"] = "咳、咳！",
["COMIC_12_6_KR5_KR5"] = "呃...我、我太久沒練習了啦！",
["COMIC_13_1_KR5_KR5"] = "有人說，這是荒誕的夢想。",
["COMIC_13_2_KR5_KR5"] = "根本不可能打造出這樣的武器。",
["COMIC_13_3_KR5_KR5"] = "但很快，他們就會知道自己錯得離譜...",
["COMIC_13_4_KR5_KR5"] = "…並為詭鬚大人的聰明才智俯首稱臣！",
["COMIC_14_1_KR5_KR5"] = "這些人該怎麼處置？",
["COMIC_14_2_KR5_KR5"] = "交給我吧！",
["COMIC_14_3_KR5_KR5"] = "我有個好地方正適合他們。",
["COMIC_14_4_KR5_KR5"] = "這就是你說的好地方？",
["COMIC_14_5_KR5_KR5"] = "讓詭鬚永遠在地牢裡腐朽？",
["COMIC_14_6_KR5_KR5"] = "完全相反，我的矮人朋友…",
["COMIC_14_7_KR5_KR5"] = "…你這顆大腦袋瓜，對我可大有用處呢！",
["COMIC_15_10_KR5_KR5"] = "…但狀況不太好。",
["COMIC_15_1_KR5_KR5"] = "在山中的某個地方。",
["COMIC_15_2_KR5_KR5"] = "喂，哥布林！",
["COMIC_15_3_KR5_KR5"] = "要幹活了！",
["COMIC_15_4_KR5_KR5"] = "你必須傳訊回去。",
["COMIC_15_5_KR5_KR5"] = "我們該派出更多的偵察兵。那幫邪教徒都還在四處遊蕩，怎麼安心入眠！",
["COMIC_15_6_KR5_KR5"] = "我們能多派些仙靈去支援，他們...",
["COMIC_15_7_KR5_KR5"] = "黑暗魔君！緊急情報！",
["COMIC_15_8_KR5_KR5"] = "這…",
["COMIC_15_9_KR5_KR5"] = "偵察兵是找到了…",
["COMIC_16_1_KR5_KR5"] = "我...一定會...復仇！",
["COMIC_16_2_KR5_KR5"] = "妳說我妹妹...她怎麼了？？？？",
["COMIC_17_10_KR5_KR5"] = "若是不阻止他們，天下諸國都會毀滅啊！",
["COMIC_17_11_KR5_KR5"] = "我們得幫幫他！",
["COMIC_17_12_KR5_KR5"] = "噢，那是當然。",
["COMIC_17_13_KR5_KR5"] = "當然了...",
["COMIC_17_1_KR5_KR5"] = "真是美好的下午！",
["COMIC_17_2_KR5_KR5"] = "我都快習慣這種和平了。",
["COMIC_17_3_KR5_KR5"] = "勸你不要。",
["COMIC_17_4_KR5_KR5"] = "孫猴兒？！怎麼來得這麼狼狽？",
["COMIC_17_5_KR5_KR5"] = "朋友們，有大事不妙了...",
["COMIC_17_6_KR5_KR5"] = "那天，老孫正在龜背上平靜地冥想...",
["COMIC_17_7_KR5_KR5"] = "突然之間，三魔王就出現哩！",
["COMIC_17_8_KR5_KR5"] = "不用說，本大聖自是無懼任何挑戰...",
["COMIC_17_9_KR5_KR5"] = "然那幫廝還是奪走了老孫的仙魂玉！",
["COMIC_1_1_KR5"] = "一個月前我們來到這片土地，尋找遭到黑巫師佛則南放逐…",
["COMIC_1_2B_KR5"] = "…至今下落不明的國王。",
["COMIC_1_4_KR5"] = "我們找了個地方紮營，休養生息……",
["COMIC_1_5_KR5"] = "…確保不受侵擾…",
["COMIC_1_8_KR5"] = "…但看來悠閒的好日子要結束了。",
["COMIC_2_1_KR5"] = "好耶！！",
["COMIC_2_3_KR5"] = "佛則南？！",
["COMIC_2_4a_KR5"] = "放鬆點…我是來和你們…",
["COMIC_2_4b_KR5"] = "…談場交易的。",
["COMIC_2_5_KR5"] = "在你對王國的所作所為之後？",
["COMIC_2_6_KR5"] = "迪納斯王，他需要睜開自己的雙眼。",
["COMIC_2_7_KR5"] = "一直以來，他拒絕去看見王國遭遇的危險",
["COMIC_2_8_1_KR5"] = "但讓我們去找回你的國王……",
["COMIC_2_8_2_KR5"] = "…然後結束這場危難吧。",
["COMIC_2_8b_KR5"] = "…同心協力。",
["COMIC_3_1_KR5"] = "哇！那是什麼…？",
["COMIC_3_2_KR5"] = "艾納妮的神劍！",
["COMIC_3_3_KR5"] = "痛痛！",
["COMIC_3_4a_KR5"] = "當然了…",
["COMIC_3_4b_KR5"] = "你省省吧！",
["COMIC_3_5a_KR5"] = "啊…但看來不會離得太遠了。",
["COMIC_3_5b_KR5"] = "吾王本人仍下落不明。",
["COMIC_3_6_KR5"] = "雖然可能會是場艱苦的戰役。",
["COMIC_4_10a_KR5"] = "哈！我哪次說得不對了？",
["COMIC_4_10b_KR5"] = "那…現在該怎麼辦？",
["COMIC_4_11_KR5"] = "雖然立場不同…",
["COMIC_4_12_KR5"] = "…但敵人的敵人，就是朋友。",
["COMIC_4_1_KR5"] = "艾納妮啊…",
["COMIC_4_2_KR5"] = "…賜予他力量吧！",
["COMIC_4_4_KR5"] = "啊啊啊啊！",
["COMIC_4_7a_KR5"] = "看來你「度假」完後大有長進啊！",
["COMIC_4_7b_KR5"] = "你！！！",
["COMIC_4_8_KR5"] = "我會讓你為惡行付出代價！",
["COMIC_4_9_KR5"] = "但你的忠告是對的。",
["COMIC_5_1_KR2"] = "勝利！",
["COMIC_5_1_KR5_KR5"] = "你們這些蛆蟲是無法阻止…",
["COMIC_5_2_KR2"] = "勝利！",
["COMIC_5_2_KR5_KR5"] = "…「新世界」到來的！！",
["COMIC_5_6_KR5_KR5"] = "它甦醒了！",
["COMIC_5_7a_KR5_KR5"] = "看來這就是…",
["COMIC_5_7b_KR5_KR5"] = "…最後的決戰。",
["COMIC_6_1a_KR5_KR5"] = "你們竟然膽敢挑戰我。",
["COMIC_6_1b_KR5_KR5"] = "但…這裡可容不下那東西！",
["COMIC_6_4_KR5_KR5"] = "嘿！",
["COMIC_6_5_KR5_KR5"] = "你這個，宇宙鼻涕蟲…",
["COMIC_6_6_KR5_KR5"] = "…少看不起「我的」力量！！！",
["COMIC_6_8_KR5_KR5"] = "做好準備。我沒辦法堅持太久！",
["COMIC_7_1_KR5_KR5"] = "不！！！這不…可能！",
["COMIC_7_3_KR5_KR5"] = "那…現在呢？",
["COMIC_7_4a_KR5_KR5"] = "這個嘛，我的任務已經結束了…",
["COMIC_7_4b_KR5_KR5"] = "…他們總不能一直沒有國王吧。",
["COMIC_7_5_2_KR2"] = "沒有",
["COMIC_7_6_KR5_KR5"] = "再會了，親愛的宿敵。",
["COMIC_7_7_KR5_KR5"] = "那之後，在永光之森…",
["COMIC_8_1_KR5_KR5"] = "啊，終於啊！",
["COMIC_8_2_KR5_KR5"] = "這份力量，如今再次......",
["COMIC_8_4_KR5_KR5"] = "...是我的了！",
["COMIC_8_5_KR5_KR5"] = "姆啊哈哈哈哈！",
["COMIC_9_1_KR5_KR5"] = "在不久的曾經，精靈族還為我等所受的恩典與魔法而受人崇敬...",
["COMIC_9_2_KR5_KR5"] = "...直到聖物遭受侵蝕，我等也化為了昔日輝煌的陰森暗影。",
["COMIC_9_3_KR5_KR5"] = "但憑著這支大軍，我會奪回精靈族的榮光...",
["COMIC_9_4_KR5_KR5"] = "...就由我來主宰精靈統治的全新世界！！！",
["COMIC_BALLOON_0002_KR1"] = "勝利！",
["COMIC_BALLOON_02_KR1"] = "勝利！",
["COMIC_balloon_0002_KR1"] = "勝利！",
["COMMAND YOUR TROOPS!"] = "統領你的部隊！",
["CONFIRM_EXIT"] = "是否離開？",
["CONFIRM_RESTART"] = "是否重新開始？",
["CONTROLLER_STAGE_16_OVERSEER_DESCRIPTION"] = "跨越次元，侵入各個世界吸收其能量的怪物。無論付出多大的代價都必須阻止它。",
["CONTROLLER_STAGE_16_OVERSEER_EXTRA"] = "- 替換玩家的防禦塔\n- 召喚眼精\n- 摧毀建地",
["CONTROLLER_STAGE_16_OVERSEER_NAME"] = "全視魔眼",
["CREDITS"] = "製作人員",
["CREDITS_COPYRIGHT"] = "© 2014 Ironhide Game Studio保留所有權利。",
["CREDITS_POWERED_BY"] = "技術支援",
["CREDITS_SUBTITLE_01"] = "（按字母順序）",
["CREDITS_SUBTITLE_07"] = "（按字母順序）",
["CREDITS_SUBTITLE_09"] = "（按字母順序）",
["CREDITS_SUBTITLE_16"] = "（按字母順序）",
["CREDITS_TEXT_18"] = "感謝我們的家人、朋友和社群",
["CREDITS_TEXT_18_2"] = "這些年不斷的支持。",
["CREDITS_TITLE_01"] = "創意總監與執行製作人",
["CREDITS_TITLE_01_CREATIVE_DIRECTORS"] = "創意總監",
["CREDITS_TITLE_01_EXECUTIVE_PRODUCERS"] = "執行製作人",
["CREDITS_TITLE_02"] = "首席遊戲設計師",
["CREDITS_TITLE_02_LEAD_GAME_DESIGNERS"] = "首席遊戲設計師",
["CREDITS_TITLE_03"] = "遊戲設計師",
["CREDITS_TITLE_03_GAME_DESIGNER"] = "遊戲設計師",
["CREDITS_TITLE_04"] = "故事作者",
["CREDITS_TITLE_04_STORY_WRITERS"] = "編劇",
["CREDITS_TITLE_05"] = "文本作者",
["CREDITS_TITLE_06"] = "首席程式師",
["CREDITS_TITLE_06_LEAD_PROGRAMMERS"] = "首席程式設計師",
["CREDITS_TITLE_07"] = "程式師",
["CREDITS_TITLE_08"] = "首席畫師",
["CREDITS_TITLE_09"] = "畫師",
["CREDITS_TITLE_10"] = "漫畫畫師",
["CREDITS_TITLE_11"] = "漫畫作者",
["CREDITS_TITLE_12"] = "技術美工",
["CREDITS_TITLE_13"] = "音效",
["CREDITS_TITLE_14"] = "原聲音樂",
["CREDITS_TITLE_15"] = "配音",
["CREDITS_TITLE_16"] = "品質檢驗與測試",
["CREDITS_TITLE_17"] = "測試版",
["CREDITS_TITLE_18"] = "特別鳴謝",
["CREDITS_TITLE_19_PMO"] = "專案管理辦公室",
["CREDITS_TITLE_20_PRODUCER"] = "製作人",
["CREDITS_TITLE_21_MARKETING"] = "行銷",
["CREDITS_TITLE_22_SPECIAL_COLLAB"] = "特別合作夥伴",
["CREDITS_TITLE_ANCIENT_HUNGER_UPDATE"] = "古代罪餓 / 蜘蛛恐懼 / 大聖遊記",
["CREDITS_TITLE_GAME_ENGINE_PROGRAMMER"] = "遊戲引擎程式員",
["CREDITS_TITLE_LOCALIZATION"] = "當地語系化",
["CREDITS_TITLE_LOGO"] = "遊戲出品",
["CRange0"] = "短",
["CRange1"] = "平均",
["CRange2"] = "遠",
["CRange3"] = "超高",
["CRange4"] = "超遠",
["CReload0"] = "非常慢",
["CReload1"] = "慢",
["CReload2"] = "平均",
["CReload3"] = "快",
["CReload4"] = "飛快",
["CSpeed0"] = "慢",
["CSpeed1"] = "中",
["CSpeed2"] = "快",
["C_DIFFICULTY_EASY"] = "已完成休閒模式",
["C_DIFFICULTY_HARD"] = "已完成老兵模式",
["C_DIFFICULTY_IMPOSSIBLE"] = "已完成不可能模式",
["C_DIFFICULTY_NORMAL"] = "已完成普通模式",
["C_REWARD"] = "獎勵：",
["Campaign"] = "戰役",
["Campaing"] = "戰役",
["Casual"] = "休閒",
["Challenge Rules"] = "挑戰規則",
["Clear_progress"] = "清除進度",
["Click on the path to move the hero."] = "按一下路徑來移動英雄。",
["Click to select"] = "按一下來選擇",
["Coming soon"] = "即將推出",
["Community Manager"] = "社區管理員",
["Continue"] = "繼續",
["Credits"] = "製作人員",
["DAYS_ABBREVIATION"] = "天",
["DEFEAT"] = "擊敗",
["DELETE SLOT?"] = "刪除存檔？",
["DIFFICULTY LEVEL"] = "難度",
["DIFFICULTY_SELECTION_EASY_DESCRIPTION"] = "策略遊戲新手！",
["DIFFICULTY_SELECTION_HARD_DESCRIPTION"] = "艱巨挑戰！有風險！",
["DIFFICULTY_SELECTION_IMPOSSIBLE_DESCRIPTION"] = "強者專屬，極致挑戰！",
["DIFFICULTY_SELECTION_IMPOSSIBLE_LOCKED_DESCRIPTION"] = "完成戰役 可解鎖此模式",
["DIFFICULTY_SELECTION_NORMAL_DESCRIPTION"] = "不錯的挑戰！",
["DIFFICULTY_SELECTION_NOTE"] = "選擇關卡的時候可以更改難度。",
["DIFFICULTY_SELECTION_TITLE"] = "選擇難度！",
["DISCOUNT"] = "折扣",
["DLC_OWNED"] = "買過的",
["Defeat"] = "擊敗",
["Difficulty Level"] = "難度",
["Done"] = "完成",
["ELITE STAGE!"] = "精英關卡！",
["ENEMY_ACOLYTE_DESCRIPTION"] = "身材矮小且溫順的僕從，常在戰鬥中使用人海戰術。",
["ENEMY_ACOLYTE_EXTRA"] = "- 死亡後會化為觸手",
["ENEMY_ACOLYTE_NAME"] = "邪教僕從",
["ENEMY_ACOLYTE_SPECIAL"] = "死亡後會化為觸手。",
["ENEMY_ACOLYTE_TENTACLE_DESCRIPTION"] = "走投無路之時，僕從會向全視魔眼大人獻祭自己的生命，召喚可怕的觸手。",
["ENEMY_ACOLYTE_TENTACLE_EXTRA"] = "- 在僕從死亡後產生",
["ENEMY_ACOLYTE_TENTACLE_NAME"] = "僕從觸手",
["ENEMY_AMALGAM_DESCRIPTION"] = "血壤巨獸是由遙遠虛空的血肉及土壤拼組而成的怪物。儘管行進速度緩慢，但仍然令人聞風喪膽。",
["ENEMY_AMALGAM_EXTRA"] = "- 小BOSS\n- 死亡時爆炸",
["ENEMY_AMALGAM_NAME"] = "血壤巨獸",
["ENEMY_ANIMATED_ARMOR_DESCRIPTION"] = "歷戰遺留的鎧甲，如今遭遊魂附身而再度突入戰場。",
["ENEMY_ANIMATED_ARMOR_EXTRA"] = "- 倒下後，可由其他遊魂附身而再次活動",
["ENEMY_ANIMATED_ARMOR_NAME"] = "附身鎧甲",
["ENEMY_ARMORED_NIGHTMARE_DESCRIPTION"] = "邪教魔法使這些夢魘護甲加身，成為戰場上的一員猛將。",
["ENEMY_ARMORED_NIGHTMARE_EXTRA"] = "- 高護甲值\n- 被擊敗後會化身為夢魘",
["ENEMY_ARMORED_NIGHTMARE_NAME"] = "武裝夢魘",
["ENEMY_ARMORED_NIGHTMARE_SPECIAL"] = "被擊敗後會化身為夢魘。",
["ENEMY_ASH_SPIRIT_DESCRIPTION"] = "化為可怕怪物的強大之靈，從岩漿、灰燼與悲傷中孕育而生。",
["ENEMY_ASH_SPIRIT_EXTRA"] = "- 高生命值\n- 高等護甲\n- 在燃燒地表上恢復生命值",
["ENEMY_ASH_SPIRIT_NAME"] = "悲燼靈",
["ENEMY_BALLOONING_SPIDER_DESCRIPTION"] = "快速又狡猾的蜘蛛，懂得避開麻煩的小伎倆。",
["ENEMY_BALLOONING_SPIDER_EXTRA"] = "- 危急時飛行\n- 中等護甲",
["ENEMY_BALLOONING_SPIDER_FLYER_DESCRIPTION"] = "快速又狡猾的蜘蛛，懂得避開麻煩的小伎倆。",
["ENEMY_BALLOONING_SPIDER_FLYER_EXTRA"] = "- 危急時飛行\n- 中等護甲",
["ENEMY_BALLOONING_SPIDER_FLYER_NAME"] = "天網蛛",
["ENEMY_BALLOONING_SPIDER_NAME"] = "天網蛛",
["ENEMY_BANE_WOLF_DESCRIPTION"] = "急奔狩獵愚鈍生物的扭曲之狼。",
["ENEMY_BANE_WOLF_EXTRA"] = "- 每次受到傷害時加速",
["ENEMY_BANE_WOLF_NAME"] = "禍狼",
["ENEMY_BEAR_VANGUARD_DESCRIPTION"] = "高大威猛且殘暴，能將敵方撕成無數碎屑。",
["ENEMY_BEAR_VANGUARD_EXTRA"] = "- 高護甲\n- 當周圍有熊死亡時進入暴怒狀態",
["ENEMY_BEAR_VANGUARD_NAME"] = "先鋒戰熊",
["ENEMY_BEAR_VANGUARD_SPECIAL"] = "當周圍有熊死亡時，會進入狂暴狀態。",
["ENEMY_BEAR_WOODCUTTER_DESCRIPTION"] = "喜歡在工作中偷懶打盹，但一旦醒來，事情就大條了。",
["ENEMY_BEAR_WOODCUTTER_EXTRA"] = "- 高護甲\n- 當周圍有熊死亡時進入暴怒狀態",
["ENEMY_BEAR_WOODCUTTER_NAME"] = "伐木熊",
["ENEMY_BIG_TERRACOTA_DESCRIPTION"] = "由蘊含殺意的靈魂混合而成，人形的泥塊怪物。",
["ENEMY_BIG_TERRACOTA_EXTRA"] = "- 近戰",
["ENEMY_BIG_TERRACOTA_NAME"] = "幻泥俑",
["ENEMY_BLAZE_RAIDER_DESCRIPTION"] = "驕傲強壯的隊長們，亦是踏上火之道的入門者。運用蛇矛的優勢來撂倒敵人。",
["ENEMY_BLAZE_RAIDER_EXTRA"] = "- 低等護甲\n- 在燃燒地表上發動特殊攻擊",
["ENEMY_BLAZE_RAIDER_NAME"] = "烈火隊正",
["ENEMY_BLINKER_DESCRIPTION"] = "瞬視者有銳利的眼神與蝙蝠般的翅膀，會在敵人放鬆戒備的瞬間襲擊。",
["ENEMY_BLINKER_EXTRA"] = "- 暈眩玩家單位",
["ENEMY_BLINKER_NAME"] = "虛空瞬視者",
["ENEMY_BLINKER_SPECIAL"] = "能夠暈眩玩家單位。",
["ENEMY_BOSS_BULL_KING_DESCRIPTION"] = "蠻橫專行的領袖、身經百戰的戰士、重視實際的戰略家。他雖曾是孫悟空的結拜兄弟，如今卻一心只想將其仙魂玉的力量奪為己用。以脾氣暴躁、力大無窮與武藝高強而聞名。",
["ENEMY_BOSS_BULL_KING_EXTRA"] = "-高護甲\n- 高魔法抗性\n- 對單位和防禦塔造成大範圍暈眩",
["ENEMY_BOSS_BULL_KING_NAME"] = "牛魔王",
["ENEMY_BOSS_CORRUPTED_DENAS_NAME"] = "魔化迪納斯",
["ENEMY_BOSS_CROCS_2_NAME"] = "噬界災鱷【酸蝕】",
["ENEMY_BOSS_CROCS_3_NAME"] = "噬界災鱷【炎靈】",
["ENEMY_BOSS_CROCS_NAME"] = "噬界災鱷",
["ENEMY_BOSS_CULT_LEADER_NAME"] = "泛視先知魔蒂婭絲",
["ENEMY_BOSS_DEFORMED_GRYMBEARD_NAME"] = "畸形詭鬚",
["ENEMY_BOSS_GRYMBEARD_NAME"] = "詭鬚",
["ENEMY_BOSS_MACHINIST_NAME"] = "詭鬚",
["ENEMY_BOSS_NAVIRA_NAME"] = "汎里埃",
["ENEMY_BOSS_OVERSEER_NAME"] = "全視魔眼",
["ENEMY_BOSS_PIG_NAME"] = "野獸之王血輾",
["ENEMY_BOSS_PRINCESS_IRON_FAN_CLONE_NAME"] = "鐵扇公主分身",
["ENEMY_BOSS_PRINCESS_IRON_FAN_NAME"] = "鐵扇公主",
["ENEMY_BOSS_REDBOY_TEEN_NAME"] = "紅孩兒",
["ENEMY_BOSS_SPIDER_QUEEN_NAME"] = "米迦爾",
["ENEMY_BRUTE_WELDER_DESCRIPTION"] = "一言不合就用焊槍開燙的暴躁工人。",
["ENEMY_BRUTE_WELDER_EXTRA"] = "- 死亡時，封鎖一座防禦塔",
["ENEMY_BRUTE_WELDER_NAME"] = "暴力焊工",
["ENEMY_BURNING_TREANT_DESCRIPTION"] = "誕生於焚燃的森林之中，滿溢邪念的樹木怪物。",
["ENEMY_BURNING_TREANT_EXTRA"] = "- 範圍傷害\n- 攻擊時留下燃燒地表",
["ENEMY_BURNING_TREANT_NAME"] = "焚火樹人",
["ENEMY_CITIZEN_1_DESCRIPTION"] = "在黑市中做非法走私的勾當，為公主效力的陰險漁夫。",
["ENEMY_CITIZEN_1_EXTRA"] = "- 孱弱",
["ENEMY_CITIZEN_1_NAME"] = "老魚販",
["ENEMY_CITIZEN_2_DESCRIPTION"] = "在黑市中做非法走私的勾當，為公主效力的陰險漁夫。",
["ENEMY_CITIZEN_2_EXTRA"] = "- 孱弱",
["ENEMY_CITIZEN_2_NAME"] = "黑水漁夫",
["ENEMY_CITIZEN_3_DESCRIPTION"] = "在黑市中做非法走私的勾當，為公主效力的陰險漁夫。",
["ENEMY_CITIZEN_3_EXTRA"] = "- 孱弱",
["ENEMY_CITIZEN_3_NAME"] = "墨水走私犯",
["ENEMY_CITIZEN_4_DESCRIPTION"] = "在黑市中做非法走私的勾當，為公主效力的陰險漁夫。",
["ENEMY_CITIZEN_4_EXTRA"] = "- 孱弱",
["ENEMY_CITIZEN_4_NAME"] = "潮汐偷獵者",
["ENEMY_CITIZEN_DESCRIPTION"] = "標準的無產階級，被迫為女王而戰。",
["ENEMY_CITIZEN_EXTRA"] = "- 孱弱",
["ENEMY_CITIZEN_NAME"] = "布衣",
["ENEMY_COMMON_CLONE_DESCRIPTION"] = "毫無特色，無關緊要，和原版十分相似。",
["ENEMY_COMMON_CLONE_EXTRA"] = "- 無意識地向前推進",
["ENEMY_COMMON_CLONE_NAME"] = "複製體",
["ENEMY_CORRUPTED_ELF_DESCRIPTION"] = "能從遠處狩獵敵人的復生精靈。就算死去後也不會減少半點威脅。",
["ENEMY_CORRUPTED_ELF_EXTRA"] = "- 死亡時生成遊魂",
["ENEMY_CORRUPTED_ELF_NAME"] = "歸魂遊俠",
["ENEMY_CORRUPTED_STALKER_DESCRIPTION"] = "受邪教僕從馴服並成為坐騎的潛雲獸。",
["ENEMY_CORRUPTED_STALKER_EXTRA"] = "- 飛行",
["ENEMY_CORRUPTED_STALKER_NAME"] = "馴化潛雲獸",
["ENEMY_CORRUPTED_STALKER_SPECIAL"] = "具飛行能力。",
["ENEMY_CROCS_BASIC_DESCRIPTION"] = "高傲鱷魚戰士的一員。仍是年幼的個體，但只要再多給牠幾卡路里，就能進化成牠朝思暮想的殺人機器。",
["ENEMY_CROCS_BASIC_EGG_DESCRIPTION"] = "才剛破殼就有無以阻擋的腳力。「孩子長得真快」說得正是這些驚奇的小傢伙。",
["ENEMY_CROCS_BASIC_EGG_EXTRA"] = "- 無法阻擋\n- 低護甲\n- 數秒後孵化為鱷呱呱",
["ENEMY_CROCS_BASIC_EGG_NAME"] = "鱷達出奇蛋",
["ENEMY_CROCS_BASIC_EXTRA"] = "- 近戰",
["ENEMY_CROCS_BASIC_NAME"] = "鱷呱呱",
["ENEMY_CROCS_EGG_SPAWNER_DESCRIPTION"] = "麻煩滿載，帶著巢穴移動的媽媽鱷魚！每走幾步就會有蛋蛋從她身上掉落，孵化成瘋狂的鱷達出奇蛋。就像是一間移動的托兒所，但孩子都超會咬咬咬咬咬！",
["ENEMY_CROCS_EGG_SPAWNER_EXTRA"] = "- 在路徑上生成鱷達出奇蛋",
["ENEMY_CROCS_EGG_SPAWNER_NAME"] = "孵巢鱷",
["ENEMY_CROCS_FLIER_DESCRIPTION"] = "蔑視自然的進化之道，自行鑄造翅膀而獲得空中優勢的狡猾鱷魚。",
["ENEMY_CROCS_FLIER_EXTRA"] = "- 飛行",
["ENEMY_CROCS_FLIER_NAME"] = "有翼飛鱷",
["ENEMY_CROCS_HYDRA_DESCRIPTION"] = "兩顆頭總比一顆更強，海德拉的存在證明了這點。古代神話中還曾描繪過像海德拉一樣，但三首的猛獸...這應該只是個捏造的謊言吧。",
["ENEMY_CROCS_HYDRA_EXTRA"] = "- 死亡時生成第三顆頭\n- 向地面噴吐毒液",
["ENEMY_CROCS_HYDRA_NAME"] = "海德拉",
["ENEMY_CROCS_QUICKFEET_GATOR_NAME"] = "毋餓吾鱷",
["ENEMY_CROCS_RANGED_DESCRIPTION"] = "高速奔馳的獵手蜥蜴，使用長距離的彈弓來攻擊。",
["ENEMY_CROCS_RANGED_EXTRA"] = "- 快速\n- 遠程",
["ENEMY_CROCS_RANGED_NAME"] = "彈弓蜥蜴",
["ENEMY_CROCS_SHAMAN_DESCRIPTION"] = "對鱷魚群體至關重要的魔法生物。畢竟對冷血動物來說，能夠預知天候變化可是生死攸關的能力。",
["ENEMY_CROCS_SHAMAN_EXTRA"] = "- 遠程魔法傷害\n- 高魔法抗性\n- 治療其他鱷魚\n- 封印防禦塔",
["ENEMY_CROCS_SHAMAN_NAME"] = "睿智鱷魚",
["ENEMY_CROCS_TANK_DESCRIPTION"] = "鱷魚軍團的基石。以「防禦就是最好的攻擊」為理念，牠們盜取了甲殼來武裝自己，貫徹守備之道。",
["ENEMY_CROCS_TANK_EXTRA"] = "- 高生命值\n- 高護甲\n- 受阻擋時迴旋",
["ENEMY_CROCS_TANK_NAME"] = "奇坦鱷",
["ENEMY_CRYSTAL_GOLEM_DESCRIPTION"] = "水晶中被注入異界魔法的石像，幾乎無法阻擋。",
["ENEMY_CRYSTAL_GOLEM_EXTRA"] = "- 小BOSS\n- 非常高的護甲",
["ENEMY_CRYSTAL_GOLEM_NAME"] = "水晶魔像",
["ENEMY_CULTBROOD_DESCRIPTION"] = "半蜘蛛、半邪教徒的怪物。無懼無情、肆意殺戮。",
["ENEMY_CULTBROOD_EXTRA"] = "- 快速\n- 中毒攻擊\n- 如果敵人在中毒期間死亡，會誕生另一隻邪蛛",
["ENEMY_CULTBROOD_NAME"] = "邪蛛",
["ENEMY_CUTTHROAT_RAT_DESCRIPTION"] = "天性老奸巨猾，是機敏的刺客與潛行者。",
["ENEMY_CUTTHROAT_RAT_EXTRA"] = "- 速度快\n- 擊中敵人後隱形",
["ENEMY_CUTTHROAT_RAT_NAME"] = "割喉鼠",
["ENEMY_CUTTHROAT_RAT_SPECIAL"] = "擊中敵人後會進入隱形狀態。",
["ENEMY_DARKSTEEL_ANVIL_DESCRIPTION"] = "回應戰爭呼喚，重裝上場的矮人。鐵砧越沉，敲得越響。",
["ENEMY_DARKSTEEL_ANVIL_EXTRA"] = "- 為敵方提供護甲和速度增益",
["ENEMY_DARKSTEEL_ANVIL_NAME"] = "黑鋼鐵砧",
["ENEMY_DARKSTEEL_FIST_DESCRIPTION"] = "配戴原是用來折曲金屬的強化機械手臂，現在用來痛毆對手。",
["ENEMY_DARKSTEEL_FIST_EXTRA"] = "- 可暈眩玩家單位的特殊攻擊",
["ENEMY_DARKSTEEL_FIST_NAME"] = "黑鋼之拳",
["ENEMY_DARKSTEEL_GUARDIAN_DESCRIPTION"] = "由矮人戰士操作，並以火焰引擎提供動力的堅固戰鬥裝甲。真是人要衣裝，矮人要「金」裝！",
["ENEMY_DARKSTEEL_GUARDIAN_EXTRA"] = "- 小BOSS\n- 低生命值時進入狂暴狀態",
["ENEMY_DARKSTEEL_GUARDIAN_NAME"] = "黑鋼守衛機甲",
["ENEMY_DARKSTEEL_HAMMERER_DESCRIPTION"] = "與他們偏好的武器一樣笨重的戰士。",
["ENEMY_DARKSTEEL_HAMMERER_EXTRA"] = " ",
["ENEMY_DARKSTEEL_HAMMERER_NAME"] = "黑鋼錘手",
["ENEMY_DARKSTEEL_HULK_DESCRIPTION"] = "不只性情暴躁，更有熔融鋼鐵在他們血液之中流動。這就是矮人的最「重」境界。",
["ENEMY_DARKSTEEL_HULK_EXTRA"] = "- 小BOSS\n- 低生命值時，沿路徑衝刺並造成傷害",
["ENEMY_DARKSTEEL_HULK_NAME"] = "黑鋼大矮人",
["ENEMY_DARKSTEEL_SHIELDER_DESCRIPTION"] = "受到巨大的盾牌保護，他們會在前進時推開敵人。",
["ENEMY_DARKSTEEL_SHIELDER_EXTRA"] = "- 被擊敗後變為錘手",
["ENEMY_DARKSTEEL_SHIELDER_NAME"] = "黑鋼盾衛",
["ENEMY_DEATHWOOD_DESCRIPTION"] = "受到黑暗遊魂魔化的怪異樹木，如今遊蕩在森林間帶來肆無忌憚的破壞。",
["ENEMY_DEATHWOOD_EXTRA"] = "- 小BOSS\n- 投擲詛咒橡果造成範圍傷害",
["ENEMY_DEATHWOOD_NAME"] = "怪異朽木",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_DESCRIPTION"] = "詭鬚無法無天的傲慢，最終導致的結果。這位天才的智慧，只有他的醜陋能夠與之匹敵吧。",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_EXTRA"] = "- 飛行\n- 高魔法抗性護盾",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_NAME"] = "畸形複製體",
["ENEMY_DEMON_MINOTAUR_DESCRIPTION"] = "半人、半牛的惡鬼，能使出毀滅性的衝鋒攻擊，其下殺手毫不留情。",
["ENEMY_DEMON_MINOTAUR_EXTRA"] = "- 高階傷害\n- 衝鋒攻擊",
["ENEMY_DEMON_MINOTAUR_NAME"] = "牛魔兵",
["ENEMY_DOOM_BRINGER_DESCRIPTION"] = "喪失了一切人性，一心只為屠戮而生的可怕殺手。",
["ENEMY_DOOM_BRINGER_EXTRA"] = "- 封鎖防禦塔",
["ENEMY_DOOM_BRINGER_NAME"] = "滅魔",
["ENEMY_DRAINBROOD_DESCRIPTION"] = "擁有致命蛛咬的古老物種。有人推測牠正是導致其他蜘蛛結晶化的罪魁禍首。",
["ENEMY_DRAINBROOD_EXTRA"] = "- 使敵人結晶化並吸取他們的生命",
["ENEMY_DRAINBROOD_NAME"] = "吸血蜘蛛",
["ENEMY_DREADEYE_VIPER_DESCRIPTION"] = "擅長運用塗滿自身毒液的箭矢，從遠處精準奪命。",
["ENEMY_DREADEYE_VIPER_EXTRA"] = "- 低魔法抗性\n- 毒性攻擊",
["ENEMY_DREADEYE_VIPER_NAME"] = "駭眼毒蛇",
["ENEMY_DREADEYE_VIPER_SPECIAL"] = "箭矢會使目標中毒。",
["ENEMY_DUST_CRYPTID_DESCRIPTION"] = "曾經能看到牠是難能一見的奇景，如今卻成了誤入森林深處者的噩夢。",
["ENEMY_DUST_CRYPTID_EXTRA"] = "- 飛行\n- 留下能使敵人免疫傷害的花粉團",
["ENEMY_DUST_CRYPTID_NAME"] = "粉塵幻獸",
["ENEMY_EVOLVING_SCOURGE_DESCRIPTION"] = "起初可能還看起來有點可愛，但在牠吞下陣亡的獵物後你很就會改觀了。",
["ENEMY_EVOLVING_SCOURGE_EXTRA"] = "- 吞下陣亡單位後進化為強化型態\n- 受到「注目」時立即進化為最終型態",
["ENEMY_EVOLVING_SCOURGE_NAME"] = "進化惡獸",
["ENEMY_FAN_GUARD_DESCRIPTION"] = "強大且多才多藝的女性戰士，手持的魔法鐵扇無論是用來攻擊還是防禦都運用自如。",
["ENEMY_FAN_GUARD_EXTRA"] = "- 未受阻擋時中等護甲／魔法抗性",
["ENEMY_FAN_GUARD_NAME"] = "鐵扇護衛",
["ENEMY_FIRE_FOX_DESCRIPTION"] = "誕生於火焰中，可愛又難以捉摸的狐狸。腳程飛快又喜怒無常，無以馴服。",
["ENEMY_FIRE_FOX_EXTRA"] = "- 低魔法抗性\n- 在燃燒地表上加速\n- 死亡時留下燃燒地表",
["ENEMY_FIRE_FOX_NAME"] = "火妖狐",
["ENEMY_FIRE_PHOENIX_DESCRIPTION"] = "以火焰為食，神話中的飛行生物。在熾熱的烈焰中誕生與死亡。",
["ENEMY_FIRE_PHOENIX_EXTRA"] = "- 飛行\n- 死亡時留下燃燒地表",
["ENEMY_FIRE_PHOENIX_NAME"] = "鳳雛",
["ENEMY_FLAME_GUARD_DESCRIPTION"] = "渴望得到大師的認可，擅長使用短匕的低階弟子。",
["ENEMY_FLAME_GUARD_EXTRA"] = "- 近戰\n- 在燃燒地表上發動特殊攻擊",
["ENEMY_FLAME_GUARD_NAME"] = "焰火護衛",
["ENEMY_GALE_WARRIOR_DESCRIPTION"] = "舉手投足皆透漏著優雅的氣息，經由公主親自遴選，為她出生入死的戰士。",
["ENEMY_GALE_WARRIOR_EXTRA"] = "- 中等護甲\n- 每3次攻擊造成出血",
["ENEMY_GALE_WARRIOR_NAME"] = "風魔將",
["ENEMY_GLAREBROOD_CRYSTAL_NAME"] = "眼蛛晶",
["ENEMY_GLARELING_DESCRIPTION"] = "這些溫和的生物如果不加注意，甚至連最強大的軍隊都能擊潰。",
["ENEMY_GLARELING_EXTRA"] = "- 高速度",
["ENEMY_GLARELING_NAME"] = "眼精",
["ENEMY_GLARENWARDEN_DESCRIPTION"] = "多隻眼蛛融合而成的怪物，擁有以往無可比擬的強大與堅韌。",
["ENEMY_GLARENWARDEN_EXTRA"] = "- 高護甲\n- 攻擊時吸取生命",
["ENEMY_GLARENWARDEN_NAME"] = "聚眼蛛",
["ENEMY_GOLDEN_EYED_DESCRIPTION"] = "巨碩的獅獸，威震天地的獅吼放諸四海皆無人能敵。",
["ENEMY_GOLDEN_EYED_EXTRA"] = "- 小BOSS\n- 被阻擋時暈眩範圍內的單位\n- 封鎖防禦塔",
["ENEMY_GOLDEN_EYED_NAME"] = "金睛獸",
["ENEMY_HARDENED_HORROR_DESCRIPTION"] = "這些恐怖的怪物手臂上長有鋒利的刀刃，能夠劈開敵人，殺出一條血路。",
["ENEMY_HARDENED_HORROR_EXTRA"] = "- 受到「注目」時，會以高速滾動且無法阻擋",
["ENEMY_HARDENED_HORROR_NAME"] = "駭人刃爪",
["ENEMY_HELLFIRE_WARLOCK_DESCRIPTION"] = "極度危險的術者，擅於召喚地獄的業火與猛獸。",
["ENEMY_HELLFIRE_WARLOCK_EXTRA"] = "- 施放火連彈\n- 召喚九尾妖狐",
["ENEMY_HELLFIRE_WARLOCK_NAME"] = "業炎邪士",
["ENEMY_HOG_INVADER_DESCRIPTION"] = "骯髒的烏合之眾。野獸軍團的主力部隊。",
["ENEMY_HOG_INVADER_EXTRA"] = "- 低生命值",
["ENEMY_HOG_INVADER_NAME"] = "豬頭兵",
["ENEMY_HYENA5_DESCRIPTION"] = "嗜殺成性的戰士，以敵軍的屍體為食。",
["ENEMY_HYENA5_EXTRA"] = "- 中等護甲\n- 吃掉陣亡的玩家單位可回復生命值",
["ENEMY_HYENA5_NAME"] = "腐牙鬣狗",
["ENEMY_HYENA5_SPECIAL"] = "吞食阻擋敵軍的屍體治療自身。",
["ENEMY_KILLERTILE_DESCRIPTION"] = "強大的毀滅者，多年的戰鬥經驗（或一塊雞肉）讓牠們擁有了兇惡又猛烈的咬合能力。",
["ENEMY_KILLERTILE_EXTRA"] = "- 高血量\n- 高傷害",
["ENEMY_KILLERTILE_NAME"] = "噬你鱷",
["ENEMY_LESSER_EYE_DESCRIPTION"] = "在戰場上漂浮的邪惡之眼，作為眼魔的偵察兵而活動。",
["ENEMY_LESSER_EYE_EXTRA"] = "- 飛行",
["ENEMY_LESSER_EYE_NAME"] = "魔眼仔",
["ENEMY_LESSER_SISTER_DESCRIPTION"] = "施展邪惡魔法的扭曲姐妹，能夠將夢魘拉入現實世界。",
["ENEMY_LESSER_SISTER_EXTRA"] = "- 高魔法抗性\n- 會召喚夢魘",
["ENEMY_LESSER_SISTER_NAME"] = "扭曲姊妹",
["ENEMY_LESSER_SISTER_NIGHTMARE_DESCRIPTION"] = "由邪教姐妹的咒文書所編織而成的幻霧暗影。",
["ENEMY_LESSER_SISTER_NIGHTMARE_EXTRA"] = "- 除非被近戰單位阻擋，否則無法成為攻擊目標",
["ENEMY_LESSER_SISTER_NIGHTMARE_NAME"] = "夢魘",
["ENEMY_LESSER_SISTER_SPECIAL"] = "會召喚夢魘",
["ENEMY_MACHINIST_DESCRIPTION"] = "痴迷於齒輪和引擎，為工業自動化和戰爭而活的矮人。",
["ENEMY_MACHINIST_EXTRA"] = "- 操作一條生成哨兵的生產線",
["ENEMY_MACHINIST_NAME"] = "詭鬚",
["ENEMY_MAD_TINKERER_DESCRIPTION"] = "啥都不在乎，只想用廢料敲敲打打，製造機械。",
["ENEMY_MAD_TINKERER_EXTRA"] = "- 使用其他單位留下的廢鐵製造無人機",
["ENEMY_MAD_TINKERER_NAME"] = "瘋狂廢鐵匠",
["ENEMY_MINDLESS_HUSK_DESCRIPTION"] = "空骸看起來非常脆弱，然而它們每一隻都會為戰場帶來驚喜。",
["ENEMY_MINDLESS_HUSK_EXTRA"] = "- 死亡後召喚眼精。",
["ENEMY_MINDLESS_HUSK_NAME"] = "無主空骸",
["ENEMY_NINE_TAILED_FOX_DESCRIPTION"] = "美麗、強大、神秘。九尾妖狐將如熊熊烈火般，衝陷敵陣。",
["ENEMY_NINE_TAILED_FOX_EXTRA"] = "- 高魔法抗性\n- 可向前傳送，並在抵達時暈眩敵人\n- 範圍攻擊",
["ENEMY_NINE_TAILED_FOX_NAME"] = "九尾妖狐",
["ENEMY_NOXIOUS_HORROR_DESCRIPTION"] = "外表像兩棲生物的怪物，會向獵物吐出有毒的膽汁。近距離對戰時也同樣危險。",
["ENEMY_NOXIOUS_HORROR_EXTRA"] = "- 受到「注目」時，會獲得魔法抗性並產生毒氣纏繞",
["ENEMY_NOXIOUS_HORROR_NAME"] = "毒液口水怪",
["ENEMY_PALACE_GUARD_DESCRIPTION"] = "資質平庸，一心為滿足公主盡心盡力的衛士。",
["ENEMY_PALACE_GUARD_EXTRA"] = "- 近戰\n- 低等護甲",
["ENEMY_PALACE_GUARD_NAME"] = "宮廷護衛",
["ENEMY_PUMPKIN_WITCH_DESCRIPTION"] = "變成小南瓜的敵人。很容易踩碎。",
["ENEMY_PUMPKIN_WITCH_EXTRA"] = "- 無法阻擋",
["ENEMY_PUMPKIN_WITCH_FLYING_DESCRIPTION"] = "將敵人變為小南瓜。很容易踩碎。",
["ENEMY_PUMPKIN_WITCH_FLYING_EXTRA"] = "- 無法阻擋",
["ENEMY_PUMPKIN_WITCH_FLYING_NAME"] = "小南瓜",
["ENEMY_PUMPKIN_WITCH_NAME"] = "小南瓜",
["ENEMY_QIONGQI_DESCRIPTION"] = "雷霆猛襲的兇猛飛獅，呼嘯狂怒的風暴之王。",
["ENEMY_QIONGQI_EXTRA"] = "- 飛行\n- 極高階傷害\n- 中魔法抗性",
["ENEMY_QIONGQI_NAME"] = "窮奇",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_DESCRIPTION"] = "多年為兄弟們外送雞肉的經驗，讓牠們變得如此快速，有時甚至連雞都忘了拿。",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_EXTRA"] = "- 快速\n- 遠程\n- 當心了！他會為鱷呱呱提供雞腿歡樂送，使其進化！",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_NAME"] = "毋餓吾鱷",
["ENEMY_QUICKFEET_GATOR_DESCRIPTION"] = "多年為兄弟們外送雞肉的經驗，讓牠們變得如此快速，有時甚至連雞都忘了拿。",
["ENEMY_QUICKFEET_GATOR_EXTRA"] = "- 快速\n- 遠程\n- 當心了！他會為鱷呱呱提供雞腿歡樂送，使其進化！",
["ENEMY_QUICKFEET_GATOR_NAME"] = "毋餓吾鱷",
["ENEMY_REVENANT_HARVESTER_DESCRIPTION"] = "她們曾是精靈的女祭司，如今卻遊蕩在森林之間，只為遊魂帶來「祝福」。",
["ENEMY_REVENANT_HARVESTER_EXTRA"] = "- 將附近的遊魂轉換為歸魂收割者",
["ENEMY_REVENANT_HARVESTER_NAME"] = "歸魂收割者",
["ENEMY_REVENANT_SOULCALLER_DESCRIPTION"] = "墮入魔道，深陷死亡魔法的精靈魔法師。能召喚死者的靈魂，離開大地。",
["ENEMY_REVENANT_SOULCALLER_EXTRA"] = "- 封鎖防禦塔\n- 召喚遊魂",
["ENEMY_REVENANT_SOULCALLER_NAME"] = "歸魂召靈師",
["ENEMY_RHINO_DESCRIPTION"] = "活體攻城車，無視戰場上的一切阻礙。",
["ENEMY_RHINO_EXTRA"] = "- 小BOSS\n- 衝向敵群",
["ENEMY_RHINO_NAME"] = "撼地犀牛",
["ENEMY_RHINO_SPECIAL"] = "會衝撞敵人。",
["ENEMY_ROLLING_SENTRY_DESCRIPTION"] = "就算遭擊落，也會在地面上繼續獵捕敵人。",
["ENEMY_ROLLING_SENTRY_EXTRA"] = "- 死亡時變為廢鐵\n- 遠程",
["ENEMY_ROLLING_SENTRY_NAME"] = "滾動哨兵",
["ENEMY_SCRAP_DRONE_DESCRIPTION"] = "只為擾亂部隊粗糙地組裝而成。",
["ENEMY_SCRAP_DRONE_EXTRA"] = "- 飛行",
["ENEMY_SCRAP_DRONE_NAME"] = "廢鐵無人機",
["ENEMY_SCRAP_SPEEDSTER_DESCRIPTION"] = "吵鬧又令人煩躁，速度的追求者。",
["ENEMY_SCRAP_SPEEDSTER_EXTRA"] = "- 死亡時變為廢鐵",
["ENEMY_SCRAP_SPEEDSTER_NAME"] = "廢鐵疾行者",
["ENEMY_SKUNK_BOMBARDIER_DESCRIPTION"] = "這些臭鼬對自身天然毒素的用法駕輕就熟，能夠輕易擾亂敵軍的陣線。",
["ENEMY_SKUNK_BOMBARDIER_EXTRA"] = "- 低速度\n- 中等魔法抗性\n- 攻擊會削弱玩家單位\n- 死亡後自爆",
["ENEMY_SKUNK_BOMBARDIER_NAME"] = "投彈臭鼬",
["ENEMY_SKUNK_BOMBARDIER_SPECIAL"] = "攻擊會削弱玩家單位。死後自爆並造成傷害。",
["ENEMY_SMALL_STALKER_DESCRIPTION"] = "受邪教影響魔化的的潛雲獸會在戰場上四處傳送，散播混亂。",
["ENEMY_SMALL_STALKER_EXTRA"] = "- 受到攻擊時會向前瞬移",
["ENEMY_SMALL_STALKER_NAME"] = "魔化潛雲獸",
["ENEMY_SMALL_STALKER_SPECIAL"] = "能夠瞬移一小段距離，躲避攻擊。",
["ENEMY_SPECTER_DESCRIPTION"] = "就算失去肉身也解脫不了無盡的束縛，只能永遠狩獵一切活著的事物。",
["ENEMY_SPECTER_EXTRA"] = "- 可與其他敵軍或物件互動",
["ENEMY_SPECTER_NAME"] = "遊魂",
["ENEMY_SPIDEAD_DESCRIPTION"] = "作為蜘蛛女王\"狼蛛\"米迦爾的直系後裔，她們總是少不了帶來麻煩－－就算死了也一樣。",
["ENEMY_SPIDEAD_EXTRA"] = "- 魔法抗性\n- 死亡時生成蜘蛛網",
["ENEMY_SPIDEAD_NAME"] = "絲之女",
["ENEMY_SPIDERLING_DESCRIPTION"] = "經過邪教魔法強化的蜘蛛。快速又兇猛。會咬人。",
["ENEMY_SPIDERLING_EXTRA"] = "- 高速度\n- 低魔法抗性",
["ENEMY_SPIDERLING_NAME"] = "眼蛛",
["ENEMY_SPIDER_PRIEST_DESCRIPTION"] = "在新神蛛網的層層束縛下，步上戰場施放黑魔法的祭司。",
["ENEMY_SPIDER_PRIEST_EXTRA"] = "- 高魔法抗性\n- 瀕死時會轉化為聚眼蛛",
["ENEMY_SPIDER_PRIEST_NAME"] = "蛛網祭司",
["ENEMY_SPIDER_SISTER_DESCRIPTION"] = "蜘蛛女王的忠實信徒，施展魔法召喚同胞。",
["ENEMY_SPIDER_SISTER_EXTRA"] = "- 魔法抗性\n- 召喚眼蛛",
["ENEMY_SPIDER_SISTER_NAME"] = "蜘蛛修女",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_DESCRIPTION"] = "魔蒂婭絲用於干預戰局的暗影分身。",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_EXTRA"] = "- 保護敵人免受傷害\n- 會用黑暗觸手困住防禦塔",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_NAME"] = "魔蒂婭絲的幻象",
["ENEMY_STORM_ELEMENTAL_DESCRIPTION"] = "颱風、雷霆、憤怒中誕生的強大元素靈。悲燼靈的遠親。",
["ENEMY_STORM_ELEMENTAL_EXTRA"] = "- 高等護甲\n- 遠程\n- 死亡時封鎖一座周邊防禦塔",
["ENEMY_STORM_ELEMENTAL_NAME"] = "怒風靈",
["ENEMY_STORM_SPIRIT_DESCRIPTION"] = "穿梭於暴風雲中的小龍，靈巧地躲避危險與敵人。",
["ENEMY_STORM_SPIRIT_EXTRA"] = "- 飛行\n- 低魔法抗性\n- 受傷時向前衝刺",
["ENEMY_STORM_SPIRIT_NAME"] = "風暴幼龍",
["ENEMY_SURVEILLANCE_SENTRY_DESCRIPTION"] = "矮人所造的空中監視者。",
["ENEMY_SURVEILLANCE_SENTRY_EXTRA"] = "- 飛行",
["ENEMY_SURVEILLANCE_SENTRY_NAME"] = "飛行哨兵",
["ENEMY_SURVEYOR_HARPY_DESCRIPTION"] = "禿鷲會緊隨野獸之後，伺機尋找腐肉。",
["ENEMY_SURVEYOR_HARPY_EXTRA"] = "- 飛行",
["ENEMY_SURVEYOR_HARPY_NAME"] = "偵查禿鷲",
["ENEMY_SURVEYOR_HARPY_SPECIAL"] = "具飛行能力。",
["ENEMY_TERRACOTA_DESCRIPTION"] = "逝去士兵的複製品，即便已身在陰曹地府，仍繼續為公主而效力。",
["ENEMY_TERRACOTA_EXTRA"] = "- 近戰",
["ENEMY_TERRACOTA_NAME"] = "幻俑",
["ENEMY_TOWER_RAY_SHEEP_DESCRIPTION"] = "咩－－－。",
["ENEMY_TOWER_RAY_SHEEP_EXTRA"] = "- 無法阻擋",
["ENEMY_TOWER_RAY_SHEEP_FLYING_DESCRIPTION"] = "咩－－－。",
["ENEMY_TOWER_RAY_SHEEP_FLYING_EXTRA"] = "- 飛行",
["ENEMY_TOWER_RAY_SHEEP_FLYING_NAME"] = "飛天羊",
["ENEMY_TOWER_RAY_SHEEP_NAME"] = "羊羊",
["ENEMY_TURTLE_SHAMAN_DESCRIPTION"] = "看似溫和，實則邪惡，龜薩滿能夠治療野獸，使其恢復戰鬥能力。",
["ENEMY_TURTLE_SHAMAN_EXTRA"] = "- 低速度\n- 高生命值\n- 高魔法抗性\n- 會治療敵方單位",
["ENEMY_TURTLE_SHAMAN_NAME"] = "龜薩滿",
["ENEMY_TURTLE_SHAMAN_SPECIAL"] = "會治療敵方單位。",
["ENEMY_TUSKED_BRAWLER_DESCRIPTION"] = "比豬頭兵更加頑強，身穿東拼西湊而成的盔甲。鬥毆愛好者。",
["ENEMY_TUSKED_BRAWLER_EXTRA"] = "- 低護甲值",
["ENEMY_TUSKED_BRAWLER_NAME"] = "獠牙鬥士",
["ENEMY_UNBLINDED_ABOMINATION_DESCRIPTION"] = "完全墮落的邪教祭司，對於敵人絲毫不會手下留情。",
["ENEMY_UNBLINDED_ABOMINATION_EXTRA"] = "- 會吞食低生命值的單位",
["ENEMY_UNBLINDED_ABOMINATION_NAME"] = "邪教穢偶",
["ENEMY_UNBLINDED_ABOMINATION_SPECIAL"] = "偶爾會吞食低生命值的單位。",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_DESCRIPTION"] = "成功奴役精靈之後，部分穢偶被任命為礦坑的監工主任。",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_EXTRA"] = "- 必須被殺死才能解放精靈",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_NAME"] = "穢偶領班",
["ENEMY_UNBLINDED_PRIEST_DESCRIPTION"] = "既是信眾也鑽研神秘，祭司會施展黑魔法來戰鬥。",
["ENEMY_UNBLINDED_PRIEST_EXTRA"] = "- 高魔法抗性\n- 瀕死時會轉化為穢偶",
["ENEMY_UNBLINDED_PRIEST_NAME"] = "邪教祭司",
["ENEMY_UNBLINDED_PRIEST_SPECIAL"] = "低生命值時會轉化為穢偶。",
["ENEMY_UNBLINDED_SHACKLER_DESCRIPTION"] = "囚鏈魔能夠透過植入手臂的水晶施展魔化法術，是近身戰中是令人生畏的對手。",
["ENEMY_UNBLINDED_SHACKLER_EXTRA"] = "- 中等魔法抗性\n- 低生命值時能夠封鎖防禦塔",
["ENEMY_UNBLINDED_SHACKLER_NAME"] = "囚鏈魔",
["ENEMY_UNBLINDED_SHACKLER_SPECIAL"] = "能夠封鎖防禦塔，使其無法攻擊",
["ENEMY_VILE_SPAWNER_DESCRIPTION"] = "眼魔會將漫天的魔眼仔拋向敵人，時時刻刻都從四面八方監視著你。",
["ENEMY_VILE_SPAWNER_EXTRA"] = "- 召喚魔眼仔",
["ENEMY_VILE_SPAWNER_NAME"] = "眼魔",
["ENEMY_WATER_SORCERESS_DESCRIPTION"] = "執掌水的年邁術者。其水之力既能從遠方治癒盟友，亦能擊潰敵軍。",
["ENEMY_WATER_SORCERESS_EXTRA"] = "- 遠程\n- 中魔法抗性\n- 治療友軍",
["ENEMY_WATER_SORCERESS_NAME"] = "水師範",
["ENEMY_WATER_SPIRIT_DESCRIPTION"] = "在無盡浪潮中奔騰，不具靈魂的實體。只顧伴隨怒濤，蹂躪海岸。",
["ENEMY_WATER_SPIRIT_EXTRA"] = "- 低魔法抗性\n- 可從水中生成",
["ENEMY_WATER_SPIRIT_NAME"] = "水靈",
["ENEMY_WATER_SPIRIT_SPAWNLESS_DESCRIPTION"] = "在無盡浪潮中奔騰，不具靈魂的實體。只顧伴隨怒濤，蹂躪海岸。",
["ENEMY_WATER_SPIRIT_SPAWNLESS_EXTRA"] = "- 低魔法抗性\n- 可從水中生成",
["ENEMY_WATER_SPIRIT_SPAWNLESS_NAME"] = "水靈",
["ENEMY_WUXIAN_DESCRIPTION"] = "星火與祝融的職掌者，這些巫師不僅身強體壯，更能把任何軍隊燒為灰燼。",
["ENEMY_WUXIAN_EXTRA"] = "- 遠程\n- 中等護甲\n- 在燃燒地表上發動特殊攻擊",
["ENEMY_WUXIAN_NAME"] = "巫咸",
["ERROR_MESSAGE_GENERIC"] = "哎呀！出錯了。",
["Earn huge bonus points and gold by calling waves earlier!"] = "提前召喚敵人能獲得大量更多分數和金幣！",
["Encyclopedia"] = "百科全書",
["Enemies"] = "敵人",
["Extreme"] = "超遠",
["FIRST_WEEK_PACK"] = "禮物",
["Face an endless unrelenting enemy force and try to defeat as many as possible to comete for the best score!"] = "對抗毫無止境，前仆後繼的敵人！擊殺越多越高分！",
["Face an endless unrelenting enemy force and try to defeat as many as possible to compete for the best score!"] = "對抗毫無止境，前仆後繼的敵人！擊殺越多越高分！",
["Fast"] = "快",
["For beginners to strategy games!"] = "策略遊戲新手！",
["GAME_TITLE_KR5"] = "王國保衛戰５：同盟",
["GEMS_BARREL_NAME"] = "一桶寶石",
["GEMS_CHEST_NAME"] = "一箱寶石",
["GEMS_HANDFUL_NAME"] = "一把寶石",
["GEMS_MOUNTAIN_NAME"] = "寶石山",
["GEMS_POUCH_NAME"] = "一袋寶石",
["GEMS_WAGON_NAME"] = "一車寶石",
["GET_ALL_AWESOME_HEROES"] = "獲得所有強大的英雄",
["GET_THIS_AWESOME"] = "獲得這位\n強大的英雄",
["GET_THIS_AWESOME_2"] = "獲得這些\n強大的英雄",
["GET_THIS_AWESOME_3"] = "獲得這些\n強大的英雄",
["GIFT_CLAIMED"] = "禮物已領取！",
["GOOGLE_PLAY"] = "GOOGLE PLAY",
["Got it!"] = "瞭解了！",
["Great"] = "超高",
["HERO LEVEL UP!"] = "英雄升級！",
["HERO ROOM"] = "英雄",
["HERO UNLOCKED!"] = "英雄解鎖！",
["HEROES"] = "英雄",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_1"] = "召喚獅鷲群在空中飛行%$heroes.hero_bird.ultimate.bird.duration[2]%$秒並攻擊敵人，每次造成%$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[2]%$傷害。",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_2"] = "召喚獅鷲群在空中飛行%$heroes.hero_bird.ultimate.bird.duration[3]%$秒並攻擊敵人，每次造成%$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[3]%$傷害。",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_3"] = "召喚獅鷲群在空中飛行%$heroes.hero_bird.ultimate.bird.duration[4]%$秒並攻擊敵人，每次造成%$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[4]%$傷害。",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_DESCRIPTION"] = "召喚獅鷲群在指定區域上空飛行，攻擊敵人。",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_NAME"] = "紛爭之翼",
["HERO_BIRD_BIRDS_OF_PREY_TITLE"] = "紛爭之翼",
["HERO_BIRD_CLASS"] = "王牌馭手",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_1"] = "投擲一枚會分裂的爆彈，對擊中的每個敵人造成%$heroes.hero_bird.cluster_bomb.explosion_damage_min[1]%$傷害，並使地面燃燒%$heroes.hero_bird.cluster_bomb.fire_duration[1]%$秒，對接觸的敵人在3秒內給予%$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$傷害。",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_2"] = "投擲一枚會分裂的爆彈，對擊中的每個敵人造成%$heroes.hero_bird.cluster_bomb.explosion_damage_min[2]%$傷害，並使地面燃燒%$heroes.hero_bird.cluster_bomb.fire_duration[2]%$秒，對接觸的敵人在3秒內給予%$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$傷害。",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_3"] = "投擲一枚會分裂的爆彈，對擊中的每個敵人造成%$heroes.hero_bird.cluster_bomb.explosion_damage_min[3]%$傷害，並使地面燃燒%$heroes.hero_bird.cluster_bomb.fire_duration[3]%$秒，對接觸的敵人在3秒內給予%$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$傷害。",
["HERO_BIRD_CLUSTER_BOMB_TITLE"] = "地毯式轟炸",
["HERO_BIRD_DESC"] = "手持重火力在天際翱翔，英勇的獅鷲騎手。雖說矮人族的家園曾遭黑暗大軍入侵，貝洛登還是忍痛加入同盟，只為摧毀啟明邪教，恢復利尼維亞的榮光。",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_1"] = "獅鷲衝向地面，吞噬一名生命值%$heroes.hero_bird.eat_instakill.hp_max[1]%$以下的敵人。",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_2"] = "獅鷲衝向地面，吞噬一名生命值%$heroes.hero_bird.eat_instakill.hp_max[2]%$以下的敵人。",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_3"] = "獅鷲衝向地面，吞噬一名生命值%$heroes.hero_bird.eat_instakill.hp_max[3]%$以下的敵人。",
["HERO_BIRD_EAT_INSTAKILL_TITLE"] = "獅鷲狩獵",
["HERO_BIRD_GATTLING_DESCRIPTION_1"] = "全開火力猛擊一名敵人，造成%$heroes.hero_bird.gattling.s_damage_min[1]%$-%$heroes.hero_bird.gattling.s_damage_max[1]%$物理傷害。",
["HERO_BIRD_GATTLING_DESCRIPTION_2"] = "全開火力猛擊一名敵人，造成%$heroes.hero_bird.gattling.s_damage_min[2]%$-%$heroes.hero_bird.gattling.s_damage_max[2]%$物理傷害。",
["HERO_BIRD_GATTLING_DESCRIPTION_3"] = "全開火力猛擊一名敵人，造成%$heroes.hero_bird.gattling.s_damage_min[3]%$-%$heroes.hero_bird.gattling.s_damage_max[3]%$物理傷害。",
["HERO_BIRD_GATTLING_TITLE"] = "駁火先鋒",
["HERO_BIRD_NAME"] = "貝洛登",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_1"] = "獅鷲發出震耳欲聾的尖嘯，使敵人暈眩%$heroes.hero_bird.shout_stun.stun_duration[1]%$秒，接著施加緩速效果，持續%$heroes.hero_bird.shout_stun.slow_duration[1]%$秒。",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_2"] = "獅鷲發出震耳欲聾的尖嘯，使敵人暈眩%$heroes.hero_bird.shout_stun.stun_duration[2]%$秒，接著施加緩速效果，持續%$heroes.hero_bird.shout_stun.slow_duration[2]%$秒。",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_3"] = "獅鷲發出震耳欲聾的尖嘯，使敵人暈眩%$heroes.hero_bird.shout_stun.stun_duration[3]%$秒，接著施加緩速效果，持續%$heroes.hero_bird.shout_stun.slow_duration[3]%$秒。",
["HERO_BIRD_SHOUT_STUN_TITLE"] = "恐怖尖嘯",
["HERO_BUILDER_CLASS"] = "首席工頭",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_1"] = "建造一座臨時防禦塔，在%$heroes.hero_builder.defensive_turret.duration[1]%$秒內對經過的敵人進行攻擊，每次攻擊造成%$heroes.hero_builder.defensive_turret.attack.damage_min[1]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[1]%$物理傷害。",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_2"] = "建造一座臨時防禦塔，在%$heroes.hero_builder.defensive_turret.duration[2]%$秒內對經過的敵人進行攻擊，每次攻擊造成%$heroes.hero_builder.defensive_turret.attack.damage_min[2]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[2]%$物理傷害。",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_3"] = "建造一座臨時防禦塔，在%$heroes.hero_builder.defensive_turret.duration[3]%$秒內對經過的敵人進行攻擊，每次攻擊造成%$heroes.hero_builder.defensive_turret.attack.damage_min[3]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[3]%$物理傷害。",
["HERO_BUILDER_DEFENSIVE_TURRET_TITLE"] = "防禦塔樓",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_1"] = "高速旋轉木樑，對周圍敵人造成%$heroes.hero_builder.demolition_man.s_damage_min[1]%$-%$heroes.hero_builder.demolition_man.s_damage_max[1]%$物理傷害。",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_2"] = "高速旋轉木樑，對周圍敵人造成%$heroes.hero_builder.demolition_man.s_damage_min[2]%$-%$heroes.hero_builder.demolition_man.s_damage_max[2]%$物理傷害。",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_3"] = "高速旋轉木樑，對周圍敵人造成%$heroes.hero_builder.demolition_man.s_damage_min[3]%$-%$heroes.hero_builder.demolition_man.s_damage_max[3]%$物理傷害。",
["HERO_BUILDER_DEMOLITION_MAN_TITLE"] = "超級拆遷工",
["HERO_BUILDER_DESC"] = "多年來負責建造利尼維亞防禦工事的托瑞斯對幹架也是略知一二。現在整個王國陷於危難之中（他也厭倦了袖手旁觀），托瑞斯決定親上火線，運用工具和知識在戰場活躍。",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_1"] = "托瑞斯停下戰鬥，吃口零食為自己恢復%$heroes.hero_builder.lunch_break.heal_hp[1]%$生命值。",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_2"] = "托瑞斯停下戰鬥，吃口零食為自己恢復%$heroes.hero_builder.lunch_break.heal_hp[2]%$生命值。",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_3"] = "托雷斯停下戰鬥，吃口零食為自己恢復%$heroes.hero_builder.lunch_break.heal_hp[3]%$生命值。",
["HERO_BUILDER_LUNCH_BREAK_TITLE"] = "放飯時間",
["HERO_BUILDER_NAME"] = "托瑞斯",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_1"] = "召喚兩名建築工與其並肩作戰，持續%$heroes.hero_builder.overtime_work.soldier.duration%$秒。",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_2"] = "建築工的生命值增為%$heroes.hero_builder.overtime_work.soldier.hp_max[2]%$，並會造成%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[2]%$物理傷害。持續時間%$heroes.hero_builder.overtime_work.soldier.duration%$秒。",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_3"] = "建築工的生命值增為%$heroes.hero_builder.overtime_work.soldier.hp_max[3]%$，並會造成%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[3]%$物理傷害。持續時間%$heroes.hero_builder.overtime_work.soldier.duration%$秒。",
["HERO_BUILDER_OVERTIME_WORK_TITLE"] = "勞工兄弟",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_1"] = "在路上投落巨型鋼球，對敵人造成%$heroes.hero_builder.ultimate.damage[2]%$物理傷害與%$heroes.hero_builder.ultimate.stun_duration[2]%$秒暈眩。",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_2"] = "在路上投落巨型鋼球，對敵人造成%$heroes.hero_builder.ultimate.damage[3]%$物理傷害與%$heroes.hero_builder.ultimate.stun_duration[3]%$秒暈眩。",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_3"] = "在路上投落巨型鋼球，對敵人造成%$heroes.hero_builder.ultimate.damage[4]%$物理傷害與%$heroes.hero_builder.ultimate.stun_duration[4]%$秒暈眩。",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_DESCRIPTION"] = "在路上投落毀滅巨球，對敵人造成傷害。",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_NAME"] = "毀滅巨球",
["HERO_BUILDER_WRECKING_BALL_TITLE"] = "毀滅巨球",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_1"] = "希瓦菈在%$heroes.hero_dragon_arb.ultimate.duration[2]%$秒間解放她的真實型態。在此期間她獲得%$heroes.hero_dragon_arb.ultimate.s_bonuses[2]%$%傷害、速度、抗性增幅，並進化她部分的能力。",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_2"] = "希瓦菈在%$heroes.hero_dragon_arb.ultimate.duration[3]%$秒間解放她的真實型態。在此期間她獲得%$heroes.hero_dragon_arb.ultimate.s_bonuses[3]%$%傷害、速度、抗性增幅，並進化她部分的能力。",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_3"] = "希瓦菈在%$heroes.hero_dragon_arb.ultimate.duration[4]%$秒間解放她的真實型態。在此期間她獲得%$heroes.hero_dragon_arb.ultimate.s_bonuses[4]%$%傷害、速度、抗性增幅，並進化她部分的能力。",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_DESCRIPTION"] = "解放希瓦菈的真實型態。",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_NAME"] = "自然解放",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_TITLE"] = "自然解放",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_1"] = "將綠種化為樹靈族人，持續戰鬥%$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[1]%$秒。在自然解放期間會召喚更強大的樹靈。",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_2"] = "將綠種化為樹靈族人，持續戰鬥%$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[2]%$秒。在自然解放期間會召喚更強大的樹靈。",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_3"] = "將綠種化為樹靈族人，持續戰鬥%$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[3]%$秒。在自然解放期間會召喚更強大的樹靈。",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_TITLE"] = "森林呼喚",
["HERO_DRAGON_ARB_CLASS"] = "自然之力",
["HERO_DRAGON_ARB_DESC"] = "自然之龍，樹靈族的守護者。她的龍息編織森林，她的龍翼讓清風飛舞。她正如自然，既是慈愛、也是無情。不許在森林裡亂丟垃圾！",
["HERO_DRAGON_ARB_NAME"] = "希瓦菈",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_1"] = "每隔%$heroes.hero_dragon_arb.thorn_bleed.cooldown[1]%$秒希瓦菈會強化她下一次的龍息，並根據敵人的移動速度提高傷害。在自然解放期間追加%$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[1]%$%機率即殺敵人。",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_2"] = "每隔%$heroes.hero_dragon_arb.thorn_bleed.cooldown[2]%$秒希瓦菈會強化她下一次的龍息，並根據敵人的移動速度提高傷害。在自然解放期間追加%$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[2]%$%機率即殺敵人。",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_3"] = "每隔%$heroes.hero_dragon_arb.thorn_bleed.cooldown[3]%$秒希瓦菈會強化她下一次的龍息，並根據敵人的移動速度提高傷害。在自然解放期間追加%$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[3]%$%機率即殺敵人。",
["HERO_DRAGON_ARB_THORN BLEED_TITLE"] = "荊棘之息",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_1"] = "提升周遭防禦塔的傷害%$heroes.hero_dragon_arb.tower_runes.s_damage_factor[1]%$%，持續%$heroes.hero_dragon_arb.tower_runes.duration[1]%$秒。",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_2"] = "提升周遭防禦塔的傷害%$heroes.hero_dragon_arb.tower_runes.s_damage_factor[2]%$%，持續%$heroes.hero_dragon_arb.tower_runes.duration[2]%$秒。",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_3"] = "提升周遭防禦塔的傷害%$heroes.hero_dragon_arb.tower_runes.s_damage_factor[3]%$%，持續%$heroes.hero_dragon_arb.tower_runes.duration[3]%$秒。",
["HERO_DRAGON_ARB_TOWER RUNES_TITLE"] = "深深樹根",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_1"] = "在防禦塔周邊召出植物，持續%$heroes.hero_dragon_arb.tower_plants.duration[1]%$秒。根據所屬陣營，將會變成造成傷害並緩速的劇毒植物，或者是治療周遭友軍的治癒植物。",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_2"] = "在防禦塔周邊召出植物，持續%$heroes.hero_dragon_arb.tower_plants.duration[2]%$秒。根據所屬陣營，將會變成造成傷害並緩速的劇毒植物，或者是治療周遭友軍的治癒植物。",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_3"] = "在防禦塔周邊召出植物，持續%$heroes.hero_dragon_arb.tower_plants.duration[3]%$秒。根據所屬陣營，將會變成造成傷害並緩速的劇毒植物，或者是治療周遭友軍的治癒植物。",
["HERO_DRAGON_ARB_TOWER_PLANTS_TITLE"] = "生命賦予",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_1"] = "放出%$heroes.hero_dragon_bone.burst.proj_count[1]%$顆法球，每顆都會造成%$heroes.hero_dragon_bone.burst.damage_min[1]%$-%$heroes.hero_dragon_bone.burst.damage_max[1]%$真實傷害並施加感染效果。",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_2"] = "放出%$heroes.hero_dragon_bone.burst.proj_count[2]%$顆法球，每顆都會造成%$heroes.hero_dragon_bone.burst.damage_min[2]%$-%$heroes.hero_dragon_bone.burst.damage_max[2]%$真實傷害並施加感染效果。",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_3"] = "放出%$heroes.hero_dragon_bone.burst.proj_count[3]%$顆法球，每顆都會造成%$heroes.hero_dragon_bone.burst.damage_min[3]%$-%$heroes.hero_dragon_bone.burst.damage_max[3]%$真實傷害並施加感染效果。",
["HERO_DRAGON_BONE_BURST_TITLE"] = "瘟疫肆虐",
["HERO_DRAGON_BONE_CLASS"] = "巫妖龍",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_1"] = "放出毒霧籠罩一片區域，對當中的敵人造成感染並緩速持續%$heroes.hero_dragon_bone.cloud.duration[1]%$秒。",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_2"] = "放出毒霧籠罩一片區域，對當中的敵人造成感染並緩速持續%$heroes.hero_dragon_bone.cloud.duration[2]%$秒。",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_3"] = "放出毒霧籠罩一片區域，對當中的敵人造成感染並緩速持續%$heroes.hero_dragon_bone.cloud.duration[3]%$秒。",
["HERO_DRAGON_BONE_CLOUD_TITLE"] = "災疫毒霧",
["HERO_DRAGON_BONE_DESC"] = "在黑暗魔君征途中得到了佛則南的救援。重拾自由的伯恩哈特，以其瘟疫災火予以回報，燃盡任何足以威脅黑巫師計畫的魔道之徒。",
["HERO_DRAGON_BONE_NAME"] = "伯恩哈特",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_1"] = "衝撞地面，造成%$heroes.hero_dragon_bone.nova.damage_min[1]%$-%$heroes.hero_dragon_bone.nova.damage_max[1]%$爆炸傷害，並對敵人施加感染效果。",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_2"] = "衝撞地面，造成%$heroes.hero_dragon_bone.nova.damage_min[2]%$-%$heroes.hero_dragon_bone.nova.damage_max[2]%$爆炸傷害，並對敵人施加感染效果。",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_3"] = "衝撞地面，造成%$heroes.hero_dragon_bone.nova.damage_min[3]%$-%$heroes.hero_dragon_bone.nova.damage_max[3]%$爆炸傷害，並對敵人施加感染效果。",
["HERO_DRAGON_BONE_NOVA_TITLE"] = "死亡爆發",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_1"] = "向敵人降下%$heroes.hero_dragon_bone.rain.bones_count[1]%$根脊雨，使其短暫暈眩並造成%$heroes.hero_dragon_bone.rain.damage_min[1]%$-%$heroes.hero_dragon_bone.rain.damage_max[1]%$真實傷害。",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_2"] = "向敵人降下%$heroes.hero_dragon_bone.rain.bones_count[2]%$根脊雨，使其短暫暈眩並造成%$heroes.hero_dragon_bone.rain.damage_min[2]%$-%$heroes.hero_dragon_bone.rain.damage_max[2]%$真實傷害。",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_3"] = "向敵人降下%$heroes.hero_dragon_bone.rain.bones_count[3]%$根脊雨，使其短暫暈眩並造成%$heroes.hero_dragon_bone.rain.damage_min[3]%$-%$heroes.hero_dragon_bone.rain.damage_max[3]%$真實傷害。",
["HERO_DRAGON_BONE_RAIN_TITLE"] = "脊雨",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_1"] = "召喚兩隻骨頭亞龍。亞龍各擁有%$heroes.hero_dragon_bone.ultimate.dog.hp[2]%$生命值，且可造成%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[2]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[2]%$物理傷害。",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_2"] = "召喚兩隻骨頭亞龍。亞龍各擁有%$heroes.hero_dragon_bone.ultimate.dog.hp[3]%$生命值，且可造成%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[3]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[3]%$物理傷害。",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_3"] = "召喚兩隻骨頭亞龍。亞龍各擁有%$heroes.hero_dragon_bone.ultimate.dog.hp[4]%$生命值，且可造成%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[4]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[4]%$物理傷害。",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_DESCRIPTION"] = "召喚兩隻骨頭亞龍。",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_NAME"] = "召喚亞龍",
["HERO_DRAGON_BONE_RAISE_DRAKES_TITLE"] = "召喚亞龍",
["HERO_DRAGON_GEM_CLASS"] = "不滅晶龍",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_1"] = "將一名敵人包裹在水晶中數秒。隨後水晶爆炸，即殺目標並對周圍造成%$heroes.hero_dragon_gem.crystal_instakill.s_damage[1]%$真實傷害。",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_2"] = "將一名敵人包裹在水晶中數秒。隨後水晶爆炸，即殺目標並對周圍造成%$heroes.hero_dragon_gem.crystal_instakill.s_damage[2]%$真實傷害。",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_3"] = "將一名敵人包裹在水晶中數秒。隨後水晶爆炸，即殺目標並對周圍造成%$heroes.hero_dragon_gem.crystal_instakill.s_damage[3]%$真實傷害。",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_TITLE"] = "石榴石棺",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_1"] = "朝路上投落一顆使敵人緩速%$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$%的水晶，且每1秒對周圍造成%$heroes.hero_dragon_gem.crystal_totem.s_damage[1]%$魔法傷害。持續%$heroes.hero_dragon_gem.crystal_totem.duration[1]%$秒。",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_2"] = "朝路上投落一顆使敵人緩速%$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$%的水晶，且每1秒對周圍造成%$heroes.hero_dragon_gem.crystal_totem.s_damage[2]%$魔法傷害。持續%$heroes.hero_dragon_gem.crystal_totem.duration[2]%$秒。",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_3"] = "朝路上投落一顆使敵人緩速%$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$%的水晶，且每1秒對周圍造成%$heroes.hero_dragon_gem.crystal_totem.s_damage[3]%$魔法傷害。持續%$heroes.hero_dragon_gem.crystal_totem.duration[3]%$秒。",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_TITLE"] = "能量寶石",
["HERO_DRAGON_GEM_DESC"] = "侵入忘卻深谷的啟明邪教打擾了凱冥爾與世隔絕的生活。為了處理掉這些入侵者，凱冥爾與佛則南達成協議，加入盟軍對抗共同敵人。",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_1"] = "召喚%$heroes.hero_dragon_gem.ultimate.max_shards[2]%$顆水晶彈，對範圍內命中的敵人造成%$heroes.hero_dragon_gem.ultimate.damage_min[2]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[2]%$真實傷害。",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_2"] = "召喚%$heroes.hero_dragon_gem.ultimate.max_shards[3]%$顆水晶彈，對範圍內命中的敵人造成%$heroes.hero_dragon_gem.ultimate.damage_min[3]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[3]%$真實傷害。",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_3"] = "召喚%$heroes.hero_dragon_gem.ultimate.max_shards[4]%$顆水晶彈，對範圍內命中的敵人造成%$heroes.hero_dragon_gem.ultimate.damage_min[4]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[4]%$真實傷害。",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_DESCRIPTION"] = "投落數批晶體襲擊敵人。",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_NAME"] = "紅晶崩落",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_TITLE"] = "紅晶崩落",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_1"] = "在周圍的道路上生長出水晶尖刺，對每個被擊中的敵人造成%$heroes.hero_dragon_gem.floor_impact.damage_min[1]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[1]%$物理傷害。",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_2"] = "在周圍的路上生長出水晶尖刺，對每個被擊中的敵人造成%$heroes.hero_dragon_gem.floor_impact.damage_min[2]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[2]%$物理傷害。",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_3"] = "在周圍的路上生長出水晶尖刺，對每個被擊中的敵人造成%$heroes.hero_dragon_gem.floor_impact.damage_min[3]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[3]%$物理傷害。",
["HERO_DRAGON_GEM_FLOOR_IMPACT_TITLE"] = "稜柱爆裂",
["HERO_DRAGON_GEM_NAME"] = "凱冥爾",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_1"] = "將一群敵人結晶化，使其暈眩%$heroes.hero_dragon_gem.stun.duration[1]%$秒。",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_2"] = "將一群敵人結晶化，使其暈眩%$heroes.hero_dragon_gem.stun.duration[2]%$秒。",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_3"] = "將一群敵人結晶化，使其暈眩%$heroes.hero_dragon_gem.stun.duration[3]%$秒。",
["HERO_DRAGON_GEM_STUN_TITLE"] = "結晶吐息",
["HERO_HUNTER_BEASTS_DESCRIPTION_1"] = "召喚2隻蝙蝠，在%$heroes.hero_hunter.beasts.duration[1]%$秒內攻擊周圍敵人，造成%$heroes.hero_hunter.beasts.damage_min[1]%$-%$heroes.hero_hunter.beasts.damage_max[1]%$物理傷害。每隻蝙蝠都有機會從目標身上竊取%$heroes.hero_hunter.beasts.gold_to_steal[1]%$金幣。",
["HERO_HUNTER_BEASTS_DESCRIPTION_2"] = "召喚2隻蝙蝠，在%$heroes.hero_hunter.beasts.duration[2]%$秒內攻擊周圍敵人，造成%$heroes.hero_hunter.beasts.damage_min[2]%$-%$heroes.hero_hunter.beasts.damage_max[2]%$物理傷害。每隻蝙蝠都有機會從目標身上竊取%$heroes.hero_hunter.beasts.gold_to_steal[2]%$金幣。",
["HERO_HUNTER_BEASTS_DESCRIPTION_3"] = "召喚2隻蝙蝠，在%$heroes.hero_hunter.beasts.duration[3]%$秒內攻擊周圍敵人，造成%$heroes.hero_hunter.beasts.damage_min[3]%$-%$heroes.hero_hunter.beasts.damage_max[3]%$物理傷害。每隻蝙蝠都有機會從目標身上竊取%$heroes.hero_hunter.beasts.gold_to_steal[3]%$金幣。",
["HERO_HUNTER_BEASTS_TITLE"] = "黃昏血妖",
["HERO_HUNTER_CLASS"] = "白銀獵手",
["HERO_HUNTER_DESC"] = "安雅是一對著名的獵人與吸血鬼所生下的女兒，她跟隨父親的腳步，亦成為了對抗黑暗的獵人。追循著邪教的蹤跡，她很快就來到南方，加入了盟軍。",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_1"] = "每7次近戰攻擊造成%$heroes.hero_hunter.heal_strike.damage_min[1]%$-%$heroes.hero_hunter.heal_strike.damage_max[1]%$真實傷害，並治療安雅，等同於其目標最大生命值的%$heroes.hero_hunter.heal_strike.heal_factor[1]%$%。",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_2"] = "每7次近戰攻擊造成%$heroes.hero_hunter.heal_strike.damage_min[2]%$-%$heroes.hero_hunter.heal_strike.damage_max[2]%$真實傷害，並治療安雅，等同於其目標最大生命值的%$heroes.hero_hunter.heal_strike.heal_factor[2]%$%。",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_3"] = "每7次近戰攻擊造成%$heroes.hero_hunter.heal_strike.damage_min[3]%$-%$heroes.hero_hunter.heal_strike.damage_max[3]%$真實傷害，並治療安雅，等同於其目標最大生命值的%$heroes.hero_hunter.heal_strike.heal_factor[3]%$%。",
["HERO_HUNTER_HEAL_STRIKE_TITLE"] = "吸血爪擊",
["HERO_HUNTER_NAME"] = "安雅",
["HERO_HUNTER_RICOCHET_DESCRIPTION_1"] = "安雅化作一團迷霧，在%$heroes.hero_hunter.ricochet.s_bounces[1]%$名敵人間跳躍，對每個敵人造成%$heroes.hero_hunter.ricochet.damage_min[1]%$-%$heroes.hero_hunter.ricochet.damage_max[1]%$物理傷害。",
["HERO_HUNTER_RICOCHET_DESCRIPTION_2"] = "安雅化作一團迷霧，在%$heroes.hero_hunter.ricochet.s_bounces[2]%$名敵人間跳躍，對每個敵人造成%$heroes.hero_hunter.ricochet.damage_min[2]%$-%$heroes.hero_hunter.ricochet.damage_max[2]%$物理傷害。",
["HERO_HUNTER_RICOCHET_DESCRIPTION_3"] = "安雅化作一團迷霧，在%$heroes.hero_hunter.ricochet.s_bounces[3]%$名敵人間跳躍，對每個敵人造成%$heroes.hero_hunter.ricochet.damage_min[3]%$-%$heroes.hero_hunter.ricochet.damage_max[3]%$物理傷害。",
["HERO_HUNTER_RICOCHET_TITLE"] = "迷霧步伐",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_1"] = "射擊周圍所有敵人，對每一名都造成%$heroes.hero_hunter.shoot_around.s_damage_min[1]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[1]%$真實傷害。",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_2"] = "射擊周圍所有敵人，每秒對每一名敵人造成%$heroes.hero_hunter.shoot_around.s_damage_min[2]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[2]%$真實傷害。",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_3"] = "對周圍所有敵人射擊，每秒對每一名敵人造成%$heroes.hero_hunter.shoot_around.s_damage_min[3]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[3]%$真實傷害。",
["HERO_HUNTER_SHOOT_AROUND_TITLE"] = "銀白風暴",
["HERO_HUNTER_SPIRIT_DESCRIPTION_1"] = "召喚但丁的靈魂，可造成%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[2]%$真實傷害，持續%$heroes.hero_hunter.ultimate.duration%$秒。若安雅的遺體在周圍會將其復活。",
["HERO_HUNTER_SPIRIT_DESCRIPTION_2"] = "召喚但丁的靈魂，可造成%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[3]%$真實傷害，持續%$heroes.hero_hunter.ultimate.duration%$秒。若安雅的遺體在周圍會將其復活。",
["HERO_HUNTER_SPIRIT_DESCRIPTION_3"] = "召喚但丁的靈魂，可造成%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[4]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[4]%$真實傷害，持續%$heroes.hero_hunter.ultimate.duration%$秒。若安雅的遺體在周圍會將其復活。",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_DESCRIPTION"] = "召喚但丁的靈魂，緩速並攻擊敵人。",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_NAME"] = "獵人援助",
["HERO_HUNTER_SPIRIT_TITLE"] = "獵人援助",
["HERO_HUNTER_ULTIMATE_ENTITY_NAME"] = "但丁之魂",
["HERO_LAVA_CLASS"] = "熔岩狂怒",
["HERO_LAVA_DESC"] = "性情暴戾，凶狠火爆的熔岩生物，因詭鬚的工程活動從沉眠中遭到喚醒。不善言詞的他，一往直前衝破所有阻擋他安穩睡眠的敵人，就是他前進的道路。",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_1"] = "投出一顆熔岩球，對數名敵人造成%$heroes.hero_lava.double_trouble.s_damage[1]%$爆炸傷害，並生成一隻擁有%$heroes.hero_lava.double_trouble.soldier.hp_max[1]%$生命值的熔岩仔，戰鬥%$heroes.hero_lava.double_trouble.soldier.duration%$秒。",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_2"] = "投出一顆熔岩球，對數名敵人造成%$heroes.hero_lava.double_trouble.s_damage[2]%$爆炸傷害，並生成一隻擁有%$heroes.hero_lava.double_trouble.soldier.hp_max[2]%$生命值的熔岩仔，戰鬥%$heroes.hero_lava.double_trouble.soldier.duration%$秒。",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_3"] = "投出一顆熔岩球，對數名敵人造成%$heroes.hero_lava.double_trouble.s_damage[3]%$爆炸傷害，並生成一隻擁有%$heroes.hero_lava.double_trouble.soldier.hp_max[3]%$生命值的熔岩仔，戰鬥%$heroes.hero_lava.double_trouble.soldier.duration%$秒。",
["HERO_LAVA_DOUBLE_TROUBLE_SOLDIER_NAME"] = "熔岩仔",
["HERO_LAVA_DOUBLE_TROUBLE_TITLE"] = "麻煩雙倍",
["HERO_LAVA_HOTHEADED_DESCRIPTION_1"] = "當喀拉托復活時，賦予周遭防禦塔%$heroes.hero_lava.hotheaded.s_damage_factors[1]%$%傷害增益，持續%$heroes.hero_lava.hotheaded.durations[1]%$秒。",
["HERO_LAVA_HOTHEADED_DESCRIPTION_2"] = "當喀拉托復活時，賦予周遭防禦塔%$heroes.hero_lava.hotheaded.s_damage_factors[2]%$%的傷害增益，持續%$heroes.hero_lava.hotheaded.durations[2]%$秒。",
["HERO_LAVA_HOTHEADED_DESCRIPTION_3"] = "當喀拉托復活時，賦予周遭防禦塔%$heroes.hero_lava.hotheaded.s_damage_factors[3]%$%的傷害增益，持續%$heroes.hero_lava.hotheaded.durations[3]%$秒。",
["HERO_LAVA_HOTHEADED_TITLE"] = "起床氣",
["HERO_LAVA_NAME"] = "喀拉托",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_1"] = "連續猛擊一名敵人，造成%$heroes.hero_lava.temper_tantrum.s_damage_min[1]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[1]%$物理傷害，並使目標暈眩%$heroes.hero_lava.temper_tantrum.duration[1]%$秒。",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_2"] = "連續猛擊一名敵人，造成%$heroes.hero_lava.temper_tantrum.s_damage_min[2]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[2]%$物理傷害，並使目標暈眩%$heroes.hero_lava.temper_tantrum.duration[2]%$秒。",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_3"] = "連續猛擊一名敵人，造成%$heroes.hero_lava.temper_tantrum.s_damage_min[3]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[3]%$物理傷害，並使目標暈眩%$heroes.hero_lava.temper_tantrum.duration[3]%$秒。",
["HERO_LAVA_TEMPER_TANTRUM_TITLE"] = "暴怒猛擊",
["HERO_LAVA_ULTIMATE_DESCRIPTION_1"] = "噴發%$heroes.hero_lava.ultimate.fireball_count[2]%$顆熔岩彈，每顆對每名擊中的敵人造成%$heroes.hero_lava.ultimate.bullet.s_damage[2]%$真實傷害，並灼傷%$heroes.hero_lava.ultimate.bullet.scorch.duration%$秒。",
["HERO_LAVA_ULTIMATE_DESCRIPTION_2"] = "噴發%$heroes.hero_lava.ultimate.fireball_count[3]%$顆熔岩彈，每發對每名擊中的敵人造成%$heroes.hero_lava.ultimate.bullet.s_damage[3]%$真實傷害，並灼傷%$heroes.hero_lava.ultimate.bullet.scorch.duration%$秒。",
["HERO_LAVA_ULTIMATE_DESCRIPTION_3"] = "噴發%$heroes.hero_lava.ultimate.fireball_count[4]%$顆熔岩彈，每發對每名擊中的敵人造成%$heroes.hero_lava.ultimate.bullet.s_damage[4]%$真實傷害，並灼傷%$heroes.hero_lava.ultimate.bullet.scorch.duration%$秒。",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "噴發數顆熔岩彈至路徑上，燒灼地面。",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_NAME"] = "憤怒爆發",
["HERO_LAVA_ULTIMATE_TITLE"] = "憤怒爆發",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_1"] = "對數名敵人散射熔岩，每秒造成%$heroes.hero_lava.wild_eruption.s_damage[1]%$真實傷害，並灼傷%$heroes.hero_lava.wild_eruption.duration[1]%$秒。",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_2"] = "對數名敵人散射熔岩，每秒造成%$heroes.hero_lava.wild_eruption.s_damage[2]%$真實傷害，並灼傷%$heroes.hero_lava.wild_eruption.duration[2]%$秒。",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_3"] = "對數名敵人散射熔岩，每秒造成%$heroes.hero_lava.wild_eruption.s_damage[3]%$真實傷害，並灼傷%$heroes.hero_lava.wild_eruption.duration[3]%$秒。",
["HERO_LAVA_WILD_ERUPTION_TITLE"] = "狂野噴濺",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_1"] = "召喚%$heroes.hero_lumenir.ultimate.soldier_count[1]%$名光明戰士，短暫暈眩周圍敵人並造成%$heroes.hero_lumenir.ultimate.damage_min[1]%$-%$heroes.hero_lumenir.ultimate.damage_max[1]%$真實傷害。",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_2"] = "召喚%$heroes.hero_lumenir.ultimate.soldier_count[2]%$名光明戰士，短暫暈眩周圍敵人並造成%$heroes.hero_lumenir.ultimate.damage_min[2]%$-%$heroes.hero_lumenir.ultimate.damage_max[2]%$真實傷害。",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_3"] = "召喚%$heroes.hero_lumenir.ultimate.soldier_count[3]%$名光明戰士，短暫暈眩周圍敵人並造成%$heroes.hero_lumenir.ultimate.damage_min[3]%$-%$heroes.hero_lumenir.ultimate.damage_max[3]%$真實傷害。",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "召喚聖戰士迎擊敵軍。",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_NAME"] = "勝利的化身",
["HERO_LUMENIR_ARROW_STORM_TITLE"] = "勝利的化身",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_1"] = "向周圍最強大的敵人投下一把聖光神劍，造成%$heroes.hero_lumenir.celestial_judgement.damage[1]%$真實傷害並使其暈眩%$heroes.hero_lumenir.celestial_judgement.stun_duration[1]%$秒。",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_2"] = "向周圍最強大的敵人投下一把聖光神劍，造成%$heroes.hero_lumenir.celestial_judgement.damage[2]%$真實傷害並使其暈眩%$heroes.lumenin.celestial_judgement.stun_duration[2]%$秒。",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_3"] = "向周圍最強大的敵人投下一把聖光神劍，造成%$heroes.hero_lumenir.celestial_judgement.damage[3]%$真實傷害並使其暈眩%$heroes.lumenin.celestial_judgement.stun_duration[3]%$秒。",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_TITLE"] = "聖龍的審判",
["HERO_LUMENIR_CLASS"] = "光明聖使",
["HERO_LUMENIR_DESC"] = "在國境間翱翔的露明妮亞是正義和決心的化身。身為利尼維亞聖騎士所敬拜的光明聖使，她帶來神聖祝福並賜予對抗邪惡的強大力量。",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_1"] = "發射%$heroes.hero_lumenir.fire_balls.flames_count[1]%$顆聖光球， 沿路對敵人造成傷害。每顆光球對其射穿的每名敵人造成%$heroes.hero_lumenir.fire_balls.flame_damage_min[1]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[1]%$真實傷害。",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_2"] = "發射%$heroes.hero_lumenir.fire_balls.flames_count[2]%$顆聖光球， 沿路對敵人造成傷害。每顆光球對其射穿的每名敵人造成%$heroes.hero_lumenir.fire_balls.flame_damage_min[2]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[2]%$真實傷害。",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_3"] = "發射%$heroes.hero_lumenir.fire_balls.flames_count[3]%$顆聖光球，沿路對敵人造成傷害。每顆光球對其射穿的每名敵人造成%$heroes.hero_lumenir.fire_balls.flame_damage_min[3]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[3]%$真實傷害。",
["HERO_LUMENIR_FIRE_BALLS_TITLE"] = "輝煌聖光波",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_1"] = "召喚一隻小型聖光龍並跟隨場上的另一位英雄，持續%$heroes.hero_lumenir.mini_dragon.dragon.duration[1]%$秒。龍每次攻擊可造成 %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[1]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[1]%$ 物理傷害。",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_2"] = "召喚一隻小型聖光龍並跟隨場上的另一位英雄，持續%$heroes.hero_lumenir.mini_dragon.dragon.duration[2]%$秒。龍每次攻擊可造成 %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[2]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[2]%$ 物理傷害。",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_3"] = "召喚一隻小型聖光龍並跟隨場上的另一位英雄，持續%$heroes.hero_lumenir.mini_dragon.dragon.duration[3]%$秒。龍每次攻擊可造成 %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[3]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[3]%$ 物理傷害。",
["HERO_LUMENIR_MINI_DRAGON_TITLE"] = "光芒的夥伴",
["HERO_LUMENIR_NAME"] = "露明妮亞",
["HERO_LUMENIR_SHIELD_DESCRIPTION_1"] = "賦予友方單位%$heroes.hero_lumenir.shield.armor[1]%$%護甲盾牌，能將%$heroes.hero_lumenir.shield.spiked_armor[1]%$%的所受傷害反射回敵方。",
["HERO_LUMENIR_SHIELD_DESCRIPTION_2"] = "賦予友方單位%$heroes.hero_lumenir.shield.armor[2]%$%護甲盾牌，能將%$heroes.hero_lumenir.shield.spiked_armor[2]%$% 的傷害反射回敵方。",
["HERO_LUMENIR_SHIELD_DESCRIPTION_3"] = "賦予友方單位%$heroes.hero_lumenir.shield.armor[3]%$%護甲盾牌，能將%$heroes.hero_lumenir.shield.spiked_armor[3]%$%的傷害反射回敵方。",
["HERO_LUMENIR_SHIELD_TITLE"] = "反射的祝福",
["HERO_MECHA_CLASS"] = "移動災害",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_1"] = "呼叫一艘哥布林飛艇轟炸目標區域附近的敵人，每次攻擊造成%$heroes.hero_mecha.ultimate.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[2]%$真實範圍傷害。",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_2"] = "呼叫一艘哥布林飛艇轟炸目標區域附近的敵人，每次攻擊造成%$heroes.hero_mecha.ultimate.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[3]%$真實範圍傷害。",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_3"] = "呼叫一艘哥布林飛艇轟炸目標區域附近的敵人，每次攻擊造成%$heroes.hero_mecha.ultimate.ranged_attack.damage_min[4]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[4]%$真實範圍傷害。",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_DESCRIPTION"] = "召喚飛艇轟炸區域內的敵人。",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_NAME"] = "死神天降",
["HERO_MECHA_DEATH_FROM_ABOVE_TITLE"] = "死神天降",
["HERO_MECHA_DESC"] = "得手矮人科技的兩名哥布林瘋狂發明家，以此為基礎打造出的綠皮族終極戰爭機器。奧納戈號無疑是黑暗大軍帶來恐懼的象徵。",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_1"] = "召喚%$heroes.hero_mecha.goblidrones.units%$架無人機，在%$heroes.hero_mecha.goblidrones.drone.duration[1]%$秒內對敵人進行攻擊， 每次攻擊造成%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[1]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[1]%$物理傷害。",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_2"] = "召喚%$heroes.hero_mecha.goblidrones.units%$架無人機，在%$heroes.hero_mecha.goblidrones.drone.duration[2]%$秒內對敵人進行攻擊， 每次攻擊造成%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[2]%$物理傷害。",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_3"] = "召喚%$heroes.hero_mecha.goblidrones.units%$架無人機，在%$heroes.hero_mecha.goblidrones.drone.duration[3]%$秒內對敵人進行攻擊， 每次攻擊造成%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[3]%$物理傷害。",
["HERO_MECHA_GOBLIDRONES_TITLE"] = "哥布林無人機",
["HERO_MECHA_MINE_DROP_DESCRIPTION_1"] = "當機甲靜止不動時，每隔一段時間就會在路上留下地雷，最多可達%$heroes.hero_mecha.mine_drop.max_mines[1]%$枚。每顆地雷爆炸時會造成%$heroes.hero_mecha.mine_drop.damage_min[1]%$-%$heroes.hero_mecha.mine_drop.damage_max[1]%$爆炸傷害。",
["HERO_MECHA_MINE_DROP_DESCRIPTION_2"] = "當機甲靜止不動時，每隔一段時間就會在路上留下地雷，最多可達%$heroes.hero_mecha.mine_drop.max_mines[2]%$枚。每顆地雷爆炸時會造成%$heroes.hero_mecha.mine_drop.damage_min[2]%$-%$heroes.hero_mecha.mine_drop.damage_max[2]%$爆炸傷害。",
["HERO_MECHA_MINE_DROP_DESCRIPTION_3"] = "當機甲靜止不動時，每隔一段時間就會在路上留下地雷，最多可達%$heroes.hero_mecha.mine_drop.max_mines[3]%$枚。每顆地雷爆炸時會造成%$heroes.hero_mecha.mine_drop.damage_min[3]%$-%$heroes.hero_mecha.mine_drop.damage_max[3]%$爆炸傷害。",
["HERO_MECHA_MINE_DROP_TITLE"] = "地雷佈置",
["HERO_MECHA_NAME"] = "奧納戈號",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_1"] = "機甲重擊地面，短暫暈眩周圍所有敵人，並造成%$heroes.hero_mecha.power_slam.s_damage[1]%$物理傷害。",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_2"] = "機甲重擊地面，短暫暈眩周圍所有敵人，並造成%$heroes.hero_mecha.power_slam.s_damage[2]%$物理傷害。",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_3"] = "機甲重擊地面，短暫暈眩周圍所有敵人，並造成%$heroes.hero_mecha.power_slam.s_damage[3]%$物理傷害。",
["HERO_MECHA_POWER_SLAM_TITLE"] = "強力重擊",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_1"] = "朝路上投擲一顆瀝青彈，使敵人緩速%$heroes.hero_mecha.tar_bomb.slow_factor%$%，持續%$heroes.hero_mecha.tar_bomb.duration[1]%$秒。",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_2"] = "朝路上投擲一顆瀝青彈，使敵人緩速%$heroes.hero_mecha.tar_bomb.slow_factor%$%，持續%$heroes.hero_mecha.tar_bomb.duration[2]%$秒。",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_3"] = "朝路上投擲一顆瀝青彈，使敵人緩速%$heroes.hero_mecha.tar_bomb.slow_factor%$%，持續%$heroes.hero_mecha.tar_bomb.duration[3]%$秒。",
["HERO_MECHA_TAR_BOMB_TITLE"] = "瀝青彈",
["HERO_MUYRN_CLASS"] = "森林守護者",
["HERO_MUYRN_DESC"] = "雖說看似小孩，但古靈精怪的自然之靈納魯其實已守護森林上百年之久。他之所以加入盟軍，是為了阻止數量漸增的入侵者繼續威脅家園。",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_1"] = "詛咒區域內所有敵人，降低其攻擊傷害%$heroes.hero_muyrn.faery_dust.s_damage_factor[1]%$%，持續%$heroes.hero_muyrn.faery_dust.duration[1]%$秒。",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_2"] = "詛咒區域內所有敵人，降低其攻擊傷害%$heroes.hero_muyrn.faery_dust.s_damage_factor[2]%$%，持續%$heroes.hero_muyrn.faery_dust.duration[2]%$秒。",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_3"] = "詛咒區域內所有敵人，降低其攻擊傷害%$heroes.hero_muyrn.faery_dust.s_damage_factor[3]%$%，持續%$heroes.hero_muyrn.faery_dust.duration[3]%$秒。",
["HERO_MUYRN_FAERY_DUST_TITLE"] = "衰弱魔咒",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_1"] = "納魯處於戰鬥狀態時會在自身周圍創造飛葉護盾。該護盾每秒造成%$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[1]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[1]%$魔法傷害，並為納魯提供%$heroes.hero_muyrn.leaf_whirlwind.duration[1]%$秒治療效果。",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_2"] = "納魯處於戰鬥狀態時會在自身周圍創造飛葉護盾。該護盾每秒造成%$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[2]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[2]%$魔法傷害，並為納魯提供%$heroes.hero_muyrn.leaf_whirlwind.duration[2]%$秒治療效果。",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_3"] = "納魯處於戰鬥狀態時會在自身周圍創造飛葉護盾。該護盾每秒造成%$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[3]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[3]%$魔法傷害，並為納魯提供%$heroes.hero_muyrn.leaf_whirlwind.duration[3]%$秒治療效果。",
["HERO_MUYRN_LEAF_WHIRLWIND_TITLE"] = "飛葉旋風",
["HERO_MUYRN_NAME"] = "納魯",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_1"] = "在一塊區域內泛佈樹根，持續%$heroes.hero_muyrn.ultimate.duration[2]%$秒，緩速敵人且每秒造成%$heroes.hero_muyrn.ultimate.s_damage_min[2]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[2]%$真實傷害。",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_2"] = "在一塊區域內泛佈樹根，持續%$heroes.hero_muyrn.ultimate.duration[3]%$秒，緩速敵人且每秒造成%$heroes.hero_muyrn.ultimate.s_damage_min[3]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[3]%$真實傷害。",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_3"] = "在一塊區域內泛佈樹根，持續%$heroes.hero_muyrn.ultimate.duration[4]%$秒，緩速敵人且每秒造成%$heroes.hero_muyrn.ultimate.s_damage_min[4]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[4]%$真實傷害。",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_DESCRIPTION"] = "生長出能夠對敵人造成傷害及緩速的樹根。",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_NAME"] = "守衛樹根",
["HERO_MUYRN_ROOT_DEFENDER_TITLE"] = "守衛樹根",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_1"] = "召喚%$heroes.hero_muyrn.sentinel_wisps.max_summons[1]%$隻仙靈好夥伴，跟隨納魯%$heroes.hero_muyrn.sentinel_wisps.wisp.duration[1]%$秒，並可造成%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[1]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[1]%$魔法傷害。",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_2"] = "召喚%$heroes.hero_muyrn.sentinel_wisps.max_summons[2]%$隻仙靈好夥伴，跟隨納魯%$heroes.hero_muyrn.sentinel_wisps.wisp.duration[2]%$秒，並可造成%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[2]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[2]%$魔法傷害。",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_3"] = "召喚%$heroes.hero_muyrn.sentinel_wisps.max_summons[3]%$隻仙靈好夥伴，跟隨納魯%$heroes.hero_muyrn.sentinel_wisps.wisp.duration[3]%$秒，並可造成%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[3]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[3]%$魔法傷害。",
["HERO_MUYRN_SENTINEL_WISPS_TITLE"] = "哨兵仙靈",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_1"] = "向敵人發射一道綠色能量光束，造成%$heroes.hero_muyrn.verdant_blast.s_damage[1]%$魔法傷害。",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_2"] = "向敵人發射一道綠色能量光束，造成%$heroes.hero_muyrn.verdant_blast.s_damage[2]%$魔法傷害。",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_3"] = "向敵人發射一道綠色能量光束，造成%$heroes.hero_muyrn.verdant_blast.s_damage[3]%$魔法傷害。",
["HERO_MUYRN_VERDANT_BLAST_TITLE"] = "翠綠光爆",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_1"] = "舉劍對敵人使出猛力一擊，造成%$heroes.hero_raelyn.brutal_slash.s_damage[1]%$真實傷害。",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_2"] = "舉劍對敵人使出猛力一擊，造成%$heroes.hero_raelyn.brutal_slash.s_damage[2]%$真實傷害。",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_3"] = "舉劍對敵人使出猛力一擊，造成%$heroes.hero_raelyn.brutal_slash.s_damage[3]%$真實傷害。",
["HERO_RAELYN_BRUTAL_SLASH_TITLE"] = "暴虐斬",
["HERO_RAELYN_CLASS"] = "黑暗中尉",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_1"] = "召喚一名黑暗騎士，其生命值為%$heroes.hero_raelyn.ultimate.entity.hp_max[2]%$並可造成%$heroes.hero_raelyn.ultimate.entity.damage_min[2]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[2]%$真實傷害。",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_2"] = "黑暗騎士的生命值為%$heroes.hero_raelyn.ultimate.entity.hp_max[3]%$並可造成%$heroes.hero_raelyn.ultimate.entity.damage_min[3]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[3]%$真實傷害。",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_3"] = "黑暗騎士的生命值為%$heroes.hero_raelyn.ultimate.entity.hp_max[4]%$並可造成%$heroes.hero_raelyn.ultimate.entity.damage_min[4]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[4]%$真實傷害。",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_DESCRIPTION"] = "在戰場上召喚一名黑暗騎士。",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_NAME"] = "指揮號令",
["HERO_RAELYN_COMMAND_ORDERS_TITLE"] = "指揮號令",
["HERO_RAELYN_DESC"] = "威風凜凜的芮琳中尉生來就是為了成為黑暗騎士的領袖。她殘暴無情的戰鬥風格贏得了佛則南的認可與利尼維亞人的恐懼。好勇鬥狠的她也是第一個自願加入黑巫師行列的人。",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_1"] = "使周圍敵人暈眩%$heroes.hero_raelyn.inspire_fear.stun_duration[1]%$秒，並在%$heroes.hero_raelyn.inspire_fear.damage_duration[1]%$秒內降低其攻擊傷害%$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[1]%$%。",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_2"] = "使周圍敵人暈眩%$heroes.hero_raelyn.inspire_fear.stun_duration[2]%$秒，並在%$heroes.hero_raelyn.inspire_fear.damage_duration[2]%$秒內降低其攻擊傷害%$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[2]%$%。",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_3"] = "使周圍敵人暈眩%$heroes.hero_raelyn.inspire_fear.stun_duration[3]%$秒，並在 %$heroes.hero_raelyn.inspire_fear.damage_duration[3]%$秒內降低其攻擊傷害%$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[3]%$%。",
["HERO_RAELYN_INSPIRE_FEAR_TITLE"] = "聞風喪膽",
["HERO_RAELYN_NAME"] = "芮琳",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_1"] = "在%$heroes.hero_raelyn.onslaught.duration[1]%$秒内，芮琳的攻擊速度加快，並在主要目標周圍的小範圍内造成她攻擊力%$heroes.hero_raelyn.onslaught.damage_factor[1]%$%的傷害。",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_2"] = "在%$heroes.hero_raelyn.onslaught.duration[2]%$秒内，芮琳的攻擊速度加快，並在主要目標周圍的小範圍内造成她攻擊力%$heroes.hero_raelyn.onslaught.damage_factor[2]%$%的傷害。",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_3"] = "在%$heroes.hero_raelyn.onslaught.duration[3]%$秒内，芮琳的攻擊速度加快，並在主要目標周圍的小範圍内造成她攻擊力%$heroes.hero_raelyn.onslaught.damage_factor[3]%$%的傷害。",
["HERO_RAELYN_ONSLAUGHT_TITLE"] = "無情連擊",
["HERO_RAELYN_ULTIMATE_ENTITY_NAME"] = "黑暗騎士",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_1"] = "芮琳會在戰鬥中根據附近敵人的數量產生生命護盾（每名產生她總生命值的%$heroes.hero_raelyn.unbreakable.shield_per_enemy[1]%$%護盾，上限%$heroes.hero_raelyn.unbreakable.max_targets%$名敵人）",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_2"] = "芮琳會在戰鬥中根據附近敵人的數量產生生命護盾（每名產生她總生命值的%$heroes.hero_raelyn.unbreakable.shield_per_enemy[2]%$%護盾，上限%$heroes.hero_raelyn.unbreakable.max_targets%$名敵人）",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_3"] = "芮琳會在戰鬥中根據附近敵人的數量產生生命護盾（每名產生她總生命值的%$heroes.hero_raelyn.unbreakable.shield_per_enemy[3]%$%護盾，上限 %$heroes.hero_raelyn.unbreakable.max_targets%$名敵人）",
["HERO_RAELYN_UNBREAKABLE_TITLE"] = "牢不可破",
["HERO_ROBOT_CLASS"] = "攻城魔像",
["HERO_ROBOT_DESC"] = "黑暗大軍的鍛造師再創巔峰，打造出這台威力極強的機器人並貼切地命名為「戰爭巨頭」。其以炙燃的爐心作為動力，不受情感干擾的戰爭巨頭將會不分敵我，摧毀視線所及的一切。",
["HERO_ROBOT_EXPLODE_DESCRIPTION_1"] = "發出火焰噴射，對敵人造成%$heroes.hero_robot.explode.damage_min[1]%$-%$heroes.hero_robot.explode.damage_max[1]%$爆炸傷害，並使其燃燒%$heroes.hero_robot.explode.burning_duration%$秒，每秒造成%$heroes.hero_robot.explode.s_burning_damage[1]%$傷害。",
["HERO_ROBOT_EXPLODE_DESCRIPTION_2"] = "發出火焰噴射，對敵人造成%$heroes.hero_robot.explode.damage_min[2]%$-%$heroes.hero_robot.explode.damage_max[2]%$爆炸傷害，並使其燃燒%$heroes.hero_robot.explode.burning_duration%$秒，每秒造成%$heroes.hero_robot.explode.s_burning_damage[2]%$傷害。",
["HERO_ROBOT_EXPLODE_DESCRIPTION_3"] = "發出火焰噴射，對敵人造成%$heroes.hero_robot.explode.damage_min[3]%$-%$heroes.hero_robot.explode.damage_max[3]%$爆炸傷害，並使其燃燒 %$heroes.hero_robot.explode.burning_duration%$秒，每秒造成%$heroes.hero_robot.explode.s_burning_damage[3]%$傷害。",
["HERO_ROBOT_EXPLODE_TITLE"] = "烈火焚身",
["HERO_ROBOT_FIRE_DESCRIPTION_1"] = "發射滿滿的燃焰殘灰，造成%$heroes.hero_robot.fire.damage_min[1]%$-%$heroes.hero_robot.fire.damage_max[1]%$物理傷害，並使敵人緩速%$heroes.hero_robot.fire.s_slow_duration[1]%$秒。",
["HERO_ROBOT_FIRE_DESCRIPTION_2"] = "發射滿滿的燃焰殘灰，造成%$heroes.hero_robot.fire.damage_min[2]%$-%$heroes.hero_robot.fire.damage_max[2]%$物理傷害，並使敵人緩速%$heroes.hero_robot.fire.s_slow_duration[1]%$秒。",
["HERO_ROBOT_FIRE_DESCRIPTION_3"] = "發射滿滿的燃焰殘灰，造成%$heroes.hero_robot.fire.damage_min[3]%$-%$heroes.hero_robot.fire.damage_max[3]%$物理傷害，並使敵人緩速%$heroes.hero_robot.fire.s_slow_duration[1]%$秒。",
["HERO_ROBOT_FIRE_TITLE"] = "餘燼煙幕",
["HERO_ROBOT_JUMP_DESCRIPTION_1"] = "大跳飛向一名敵人，使其暈眩%$heroes.hero_robot.jump.stun_duration[1]%$秒，並在範圍內造成%$heroes.hero_robot.jump.s_damage[1]%$物理傷害。",
["HERO_ROBOT_JUMP_DESCRIPTION_2"] = "大跳飛向一名敵人，使其暈眩%$heroes.hero_robot.jump.stun_duration[2]%$秒，並在範圍內造成%$heroes.hero_robot.jump.s_damage[2]%$物理傷害。",
["HERO_ROBOT_JUMP_DESCRIPTION_3"] = "大跳飛向一名敵人，使其暈眩%$heroes.hero_robot.jump.stun_duration[3]%$秒，並在範圍內造成%$heroes.hero_robot.jump.s_damage[3]%$物理傷害。",
["HERO_ROBOT_JUMP_TITLE"] = "震撼衝擊",
["HERO_ROBOT_NAME"] = "戰爭巨頭",
["HERO_ROBOT_TRAIN_DESCRIPTION_1"] = "召喚一輛戰車穿越道路，對敵人造成%$heroes.hero_robot.ultimate.s_damage[2]%$傷害並使其燃燒%$heroes.hero_robot.ultimate.burning_duration%$秒，每秒造成%$heroes.hero_robot.ultimate.s_burning_damage%$傷害。",
["HERO_ROBOT_TRAIN_DESCRIPTION_2"] = "召喚一輛戰車穿越道路，對敵人造成%$heroes.hero_robot.ultimate.s_damage[3]%$傷害並使其燃燒%$heroes.hero_robot.ultimate.burning_duration%$秒，每秒造成%$heroes.hero_robot.ultimate.s_burning_damage%$傷害。",
["HERO_ROBOT_TRAIN_DESCRIPTION_3"] = "召喚一輛戰車穿越道路，對敵人造成%$heroes.hero_robot.ultimate.s_damage[4]%$傷害並使其燃燒%$heroes.hero_robot.ultimate.burning_duration%$秒，每秒造成%$heroes.hero_robot.ultimate.s_burning_damage%$傷害。",
["HERO_ROBOT_TRAIN_MENUBOTTOM_DESCRIPTION"] = "召喚戰車，踏平敵軍。",
["HERO_ROBOT_TRAIN_MENUBOTTOM_NAME"] = "轟鳴火車",
["HERO_ROBOT_TRAIN_TITLE"] = "轟鳴火車",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_1"] = "痛毆一名生命值低於%$heroes.hero_robot.uppercut.s_life_threshold[1]%$%的敵人，一拳斃命。",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_2"] = "痛毆一名生命值低於%$heroes.hero_robot.uppercut.s_life_threshold[2]%$%的敵人，一拳斃命。",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_3"] = "痛毆一名生命值低於%$heroes.hero_robot.uppercut.s_life_threshold[3]%$%的敵人，一拳斃命。",
["HERO_ROBOT_UPPERCUT_TITLE"] = "重鐵上鉤拳",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "此英雄包含在【巨大的威脅】篇章中。",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "此英雄包含在【大聖遊記】篇章中。",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "【巨大的威脅】篇章",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "【大聖遊記】篇章",
["HERO_ROOM_EQUIPPED_HEROES"] = "已配置英雄",
["HERO_ROOM_GET_DLC"] = "拿到它",
["HERO_ROOM_LABEL_ROSTER_THUMB_NEW"] = "新的！",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_1"] = "召喚瑟莉安的魔法分身攻擊敵人， 造成%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[1]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[1]%$魔法傷害。",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_2"] = "召喚瑟莉安的魔法分身攻擊敵人， 造成%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[2]%$魔法傷害。",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_3"] = "召喚瑟莉安的魔法分身攻擊敵人， 造成%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[3]%$魔法傷害。",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_ENTITY_NAME"] = "星界幻象",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_TITLE"] = "星界幻象",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_1"] = "為一名友方單位施予護盾，最多可阻擋%$heroes.hero_space_elf.black_aegis.shield_base[1]%$傷害。護盾耗盡或時間結束將會爆炸，對周圍敵人造成 %$heroes.hero_space_elf.black_aegis.explosion_damage[1]%$的魔法傷害。",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_2"] = "為一名友方單位施予護盾，最多可阻擋%$heroes.hero_space_elf.black_aegis.shield_base[2]%$傷害。護盾耗盡或時間結束將會爆炸，對周圍敵人造成%$heroes.hero_space_elf.black_aegis.explosion_damage[2]%$魔法傷害。",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_3"] = "為一名友方單位施予護盾，最多可阻擋%$heroes.hero_space_elf.black_aegis.shield_base[3]%$傷害。護盾耗盡或時間結束將會爆炸，對周圍敵人造成%$heroes.hero_space_elf.black_aegis.explosion_damage[3]%$魔法傷害。",
["HERO_SPACE_ELF_BLACK_AEGIS_TITLE"] = "漆黑神盾",
["HERO_SPACE_ELF_CLASS"] = "虛空法師",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_1"] = "將一群敵人困在虛空中%$heroes.hero_space_elf.ultimate.duration[2]%$秒，並造成%$heroes.hero_space_elf.ultimate.damage[2]%$傷害。",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_2"] = "將一群敵人困在虛空中%$heroes.hero_space_elf.ultimate.duration[3]%$秒，並造成%$heroes.hero_space_elf.ultimate.damage[3]%$傷害。",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_3"] = "將一群敵人困在虛空中%$heroes.hero_space_elf.ultimate.duration[4]%$秒，並造成%$heroes.hero_space_elf.ultimate.damage[4]%$傷害。",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_DESCRIPTION"] = "困住一塊區域中的敵人並造成傷害。",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_NAME"] = "異域囚籠",
["HERO_SPACE_ELF_COSMIC_PRISON_TITLE"] = "異域囚籠",
["HERO_SPACE_ELF_DESC"] = "多年來因鑽研未知異界力量而受到同行排斥，虛空法師瑟莉安現在卻成為了盟軍最重要的人物之一，負責解析全視魔眼及任何來自異世界的勢力。",
["HERO_SPACE_ELF_NAME"] = "瑟莉安",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_1"] = "扭曲周圍所有塔的空間%$heroes.hero_space_elf.spatial_distortion.duration[1]%$秒，增加它們%$heroes.hero_space_elf.spatial_distortion.s_range_factor[1]%$%攻擊範圍。",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_2"] = "扭曲周圍所有塔的空間%$heroes.hero_space_elf.spatial_distortion.duration[2]%$秒，增加它們%$heroes.hero_space_elf.spatial_distortion.s_range_factor[2]%$%攻擊範圍。",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_3"] = "扭曲周圍所有塔的空間%$heroes.hero_space_elf.spatial_distortion.duration[3]%$秒，增加它們%$heroes.hero_space_elf.spatial_distortion.s_range_factor[3]%$%攻擊範圍。",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_TITLE"] = "空間畸變",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_1"] = "在路徑上撕裂出%$heroes.hero_space_elf.void_rift.cracks_amount[1]%$道空間裂隙，持續%$heroes.hero_space_elf.void_rift.duration[1]%$秒，每秒對其上每名敵人造成%$heroes.hero_space_elf.void_rift.s_damage_min[1]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[1]%$傷害。",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_2"] = "在路徑上撕裂出%$heroes.hero_space_elf.void_rift.cracks_amount[2]%$道空間裂隙，持續%$heroes.hero_space_elf.void_rift.duration[2]%$秒，每秒對其上每名敵人造成%$heroes.hero_space_elf.void_rift.s_damage_min[2]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[2]%$傷害。",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_3"] = "在路徑上撕裂出%$heroes.hero_space_elf.void_rift.cracks_amount[3]%$道空間裂隙，持續%$heroes.hero_space_elf.void_rift.duration[3]%$秒，每秒對其上每名敵人造成%$heroes.hero_space_elf.void_rift.s_damage_min[3]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[3]%$傷害。",
["HERO_SPACE_ELF_VOID_RIFT_TITLE"] = "虛空裂隙",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_1"] = "召喚%$heroes.hero_spider.ultimate.spawn_amount[2]%$隻蜘蛛戰鬥%$heroes.hero_spider.ultimate.spider.duration[2]%$秒，蜘蛛在命中時暈眩敵人。",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_2"] = "召喚%$heroes.hero_spider.ultimate.spawn_amount[3]%$隻蜘蛛戰鬥%$heroes.hero_spider.ultimate.spider.duration[3]%$秒，蜘蛛在命中時暈眩敵人。",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_3"] = "召喚%$heroes.hero_spider.ultimate.spawn_amount[4]%$隻蜘蛛戰鬥%$heroes.hero_spider.ultimate.spider.duration[4]%$秒，蜘蛛在命中時暈眩敵人。",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_DESCRIPTION"] = "召喚一批能暈眩敵人的蜘蛛。",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_NAME"] = "獵手呼喚",
["HERO_SPIDER_ARACNID_SPAWNER_TITLE"] = "獵手呼喚",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_1"] = "每%$heroes.hero_spider.area_attack.cooldown[1]%$秒，織珠釋放壓倒性的威壓，使周圍的敵人暈眩%$heroes.hero_spider.area_attack.s_stun_time[1]%$秒。",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_2"] = "每%$heroes.hero_spider.area_attack.cooldown[2]%$秒，織珠釋放壓倒性的威壓，使周圍的敵人暈眩%$heroes.hero_spider.area_attack.s_stun_time[2]%$秒。",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_3"] = "每%$heroes.hero_spider.area_attack.cooldown[3]%$秒，織珠釋放壓倒性的威壓，使周圍的敵人暈眩%$heroes.hero_spider.area_attack.s_stun_time[3]%$秒。",
["HERO_SPIDER_AREA_ATTACK_TITLE"] = "暗蜘威壓",
["HERO_SPIDER_DESC"] = "曾經有一支暮光精靈部族，以殲滅蜘蛛女王邪教為使命，織珠正是該族最後的倖存者。能使用暗影魔法再加上她超群的狩獵實力，放眼各國都是最讓人畏懼的危險刺客之一。",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_1"] = "每%$heroes.hero_spider.instakill_melee.cooldown[1]%$秒，織珠可以處決一名生命值在%$heroes.hero_spider.instakill_melee.life_threshold[1]%$以下，遭到暈眩的敵人。",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_2"] = "每%$heroes.hero_spider.instakill_melee.cooldown[2]%$秒，織珠可以處決一名生命值在%$heroes.hero_spider.instakill_melee.life_threshold[2]%$以下，遭到暈眩的敵人。",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_3"] = "每%$heroes.hero_spider.instakill_melee.cooldown[3]%$秒，織珠可以處決一名生命值在%$heroes.hero_spider.instakill_melee.life_threshold[3]%$以下，遭到暈眩的敵人。",
["HERO_SPIDER_INSTAKILL_MELEE_TITLE"] = "死亡掌握",
["HERO_SPIDER_NAME"] = "織珠",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_1"] = "眨眼之間，織珠瞬移至生命值最高的敵人，對其造成%$heroes.hero_spider.supreme_hunter.damage_min[1]%$-%$heroes.hero_spider.supreme_hunter.damage_max[1]%$傷害。",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_2"] = "眨眼之間，織珠瞬移至生命值最高的敵人，對其造成%$heroes.hero_spider.supreme_hunter.damage_min[2]%$-%$heroes.hero_spider.supreme_hunter.damage_max[2]%$傷害。",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_3"] = "眨眼之間，織珠瞬移至生命值最高的敵人，對其造成%$heroes.hero_spider.supreme_hunter.damage_min[3]%$-%$heroes.hero_spider.supreme_hunter.damage_max[3]%$傷害。",
["HERO_SPIDER_SUPREME_HUNTER_TITLE"] = "暗襲步",
["HERO_SPIDER_TUNNELING_DESCRIPTION_1"] = "織珠現在挖掘出土時能造成%$heroes.hero_spider.tunneling.damage_min[1]%$-%$heroes.hero_spider.tunneling.damage_max[1]%$傷害。",
["HERO_SPIDER_TUNNELING_DESCRIPTION_2"] = "織珠現在挖掘出土時能造成%$heroes.hero_spider.tunneling.damage_min[2]%$-%$heroes.hero_spider.tunneling.damage_max[2]%$傷害。",
["HERO_SPIDER_TUNNELING_DESCRIPTION_3"] = "織珠現在挖掘出土時能造成%$heroes.hero_spider.tunneling.damage_min[3]%$-%$heroes.hero_spider.tunneling.damage_max[3]%$傷害。",
["HERO_SPIDER_TUNNELING_TITLE"] = "強力出土",
["HERO_VENOM_CLASS"] = "混邪殺手",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_1"] = "在一片區域佈滿能使敵人緩速的黏液，一段時間後會轉變為可造成%$heroes.hero_venom.ultimate.s_damage[2]%$真實傷害的尖刺。",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_2"] = "在一片區域內佈滿能使敵人緩速的黏液，一段時間後會轉變為可造成%$heroes.hero_venom.ultimate.s_damage[3]%$真實傷害的尖刺。",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_3"] = "在一片區域內佈滿能使敵人緩速的黏液，一段時間後會轉變為可造成%$heroes.hero_venom.ultimate.s_damage[4]%$真實傷害的尖刺。",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_DESCRIPTION"] = "在路上施放能夠對敵人造成緩速並攻擊的黏液。",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_NAME"] = "步入死亡",
["HERO_VENOM_CREEPING_DEATH_TITLE"] = "步入死亡",
["HERO_VENOM_DESC"] = "落入邪教之手的傭兵格里姆森好不容易熬過了魔化儀式，卻被關在牢中等待死亡。這段慘痛的經歷賦予了格里姆森變形的能力，他也以此逃出了邪教，並誓言復仇。",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_1"] = "格里姆森吞噬一名生命值低於%$heroes.hero_venom.eat_enemy.hp_trigger%$%的敵人，同時恢復自身總生命值的%$heroes.hero_venom.eat_enemy.regen[1]%$%。",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_2"] = "格里姆森吞噬一名生命值低於%$heroes.hero_venom.eat_enemy.hp_trigger%$%的敵人，同時恢復自身總生命值的%$heroes.hero_venom.eat_enemy.regen[2]%$%。",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_3"] = "格里姆森吞噬一名生命值低於%$heroes.hero_venom.eat_enemy.hp_trigger%$%的敵人, 同時恢復自身總生命值的%$heroes.hero_venom.eat_enemy.regen[3]%$%。",
["HERO_VENOM_EAT_ENEMY_TITLE"] = "再生血肉",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_1"] = "沿路伸出尖蔓，每根對周圍的敵人造成%$heroes.hero_venom.floor_spikes.s_damage[1]%$真實傷害。",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_2"] = "沿路伸出尖蔓，每根對周圍的敵人造成%$heroes.hero_venom.floor_spikes.s_damage[2]%$真實傷害。",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_3"] = "沿路伸出尖蔓，每根對周圍的敵人造成%$heroes.hero_venom.floor_spikes.s_damage[3]%$真實傷害。",
["HERO_VENOM_FLOOR_SPIKES_TITLE"] = "危險刺棘",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_1"] = "當格里姆森的生命值低於%$heroes.hero_venom.inner_beast.trigger_hp%$%時將完全變身，獲得%$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[1]%$%額外傷害，並於每次攻擊命中時恢復自身總生命值的%$heroes.hero_venom.inner_beast.basic_melee.regen_health%$%，持續%$heroes.hero_venom.inner_beast.duration%$秒。",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_2"] = "當格里姆森生命值低於%$heroes.hero_venom.inner_beast.trigger_hp%$%時將完全變身，獲得%$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[2]%$%額外傷害，並於每次攻擊命中時恢復自身總生命值的%$heroes.hero_venom.inner_beast.basic_melee.regen_health%$%，持續%$heroes.hero_venom.inner_beast.duration%$秒。",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_3"] = "當格里姆森生命值低於%$heroes.hero_venom.inner_beast.trigger_hp%$%時將完全變身，獲得%$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[3]%$%額外傷害，並於每次攻擊命中時恢復自身總生命值的%$heroes.hero_venom.inner_beast.basic_melee.regen_health%$%，持續%$heroes.hero_venom.inner_beast.duration%$秒。",
["HERO_VENOM_INNER_BEAST_TITLE"] = "獸性化形",
["HERO_VENOM_NAME"] = "格里姆森",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_1"] = "遠程攻擊一名敵人，造成%$heroes.hero_venom.ranged_tentacle.s_damage[1]%$物理傷害，並有 %$heroes.hero_venom.ranged_tentacle.bleed_chance[1]%$%機率施加出血效果。出血每秒給予%$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$傷害，持續%$heroes.hero_venom.ranged_tentacle.bleed_duration[1]%$秒。",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_2"] = "遠程攻擊一名敵人，造成%$heroes.hero_venom.ranged_tentacle.s_damage[2]%$物理傷害，並有 %$heroes.hero_venom.ranged_tentacle.bleed_chance[2]%$%機率施加出血效果。出血每秒給予%$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$傷害，持續%$heroes.hero_venom.ranged_tentacle.bleed_duration[2]%$秒。",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_3"] = "遠程攻擊一名敵人，造成%$heroes.hero_venom.ranged_tentacle.s_damage[3]%$物理傷害，並有 %$heroes.hero_venom.ranged_tentacle.bleed_chance[3]%$%機率施加出血效果。出血每秒給予%$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$傷害，持續%$heroes.hero_venom.ranged_tentacle.bleed_duration[3]%$秒。",
["HERO_VENOM_RANGED_TENTACLE_TITLE"] = "椎心一擊",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_1"] = "在區域內施放%$heroes.hero_vesper.ultimate.s_spread[2]%$支箭矢，每支對敵人造成%$heroes.hero_vesper.ultimate.damage[2]%$真實傷害。",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_2"] = "在區域內施放%$heroes.hero_vesper.ultimate.s_spread[3]%$支箭矢，每支對敵人造成%$heroes.hero_vesper.ultimate.damage[3]%$真實傷害。",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_3"] = "在區域內施放%$heroes.hero_vesper.ultimate.s_spread[4]%$支箭矢，每支對敵人造成%$heroes.hero_vesper.ultimate.damage[4]%$真實傷害。",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "在區域內施放箭矢，對敵人造成物理傷害。",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_NAME"] = "箭雨",
["HERO_VESPER_ARROW_STORM_TITLE"] = "箭雨",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_1"] = "精準射出一箭，對敵人造成%$heroes.hero_vesper.arrow_to_the_knee.stun_duration[1]%$秒暈眩及%$heroes.hero_vesper.arrow_to_the_knee.s_damage[1]%$物理傷害。",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_2"] = "精準射出一箭，對敵人造成%$heroes.hero_vesper.arrow_to_the_knee.stun_duration[2]%$秒暈眩及%$heroes.hero_vesper.arrow_to_the_knee.s_damage[2]%$物理傷害。",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_3"] = "精準射出一箭，對敵人造成%$heroes.hero_vesper.arrow_to_the_knee.stun_duration[3]%$秒暈眩及%$heroes.hero_vesper.arrow_to_the_knee.s_damage[3]%$物理傷害。",
["HERO_VESPER_ARROW_TO_THE_KNEE_TITLE"] = "穿膝箭",
["HERO_VESPER_CLASS"] = "皇家隊長",
["HERO_VESPER_DESC"] = "法斯帕憑著精湛的劍法與弓術贏得了利尼維亞軍的指揮官之位。在利尼維亞淪陷、國王迪納斯離奇失蹤後，他集結殘軍展開遠征，踏上了尋找前任國王的旅程。",
["HERO_VESPER_DISENGAGE_DESCRIPTION_1"] = "當法斯帕的生命值低於%$heroes.hero_vesper.disengage.hp_to_trigger%$%時會向後跳躍避開下一次近身攻擊，接著射出三發箭矢，每發對周圍敵人造成%$heroes.hero_vesper.disengage.s_damage[1]%$物理傷害。",
["HERO_VESPER_DISENGAGE_DESCRIPTION_2"] = "當法斯帕的生命值低於%$heroes.hero_vesper.disengage.hp_to_trigger%$%時會向後跳躍避開下一次近身攻擊，接著射出三發箭矢，每發對周圍敵人造成%$heroes.hero_vesper.disengage.s_damage[2]%$物理傷害。",
["HERO_VESPER_DISENGAGE_DESCRIPTION_3"] = "當法斯帕的生命值低於%$heroes.hero_vesper.disengage.hp_to_trigger%$%時會向後跳躍避開下一次近身攻擊，接著射出三發箭矢，每發對周圍敵人造成%$heroes.hero_vesper.disengage.s_damage[3]%$物理傷害。",
["HERO_VESPER_DISENGAGE_TITLE"] = "金蟬脫殼",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_1"] = "對敵人發動三連擊，造成%$heroes.hero_vesper.martial_flourish.s_damage[1]%$物理傷害。",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_2"] = "對敵人發動三連擊，造成%$heroes.hero_vesper.martial_flourish.s_damage[2]%$物理傷害。",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_3"] = "對敵人發動三連擊，造成%$heroes.hero_vesper.martial_flourish.s_damage[3]%$物理傷害。",
["HERO_VESPER_MARTIAL_FLOURISH_TITLE"] = "刀武昇華",
["HERO_VESPER_NAME"] = "法斯帕",
["HERO_VESPER_RICOCHET_DESCRIPTION_1"] = "射出箭矢在%$heroes.hero_vesper.ricochet.s_bounces[1]%$名敵人之間彈跳，每次命中造成%$heroes.hero_vesper.ricochet.s_damage[1]%$物理傷害。",
["HERO_VESPER_RICOCHET_DESCRIPTION_2"] = "射出箭矢在%$heroes.hero_vesper.ricochet.s_bounces[2]%$名敵人之間彈跳，每次命中造成%$heroes.hero_vesper.ricochet.s_damage[2]%$物理傷害。",
["HERO_VESPER_RICOCHET_DESCRIPTION_3"] = "射出箭矢在%$heroes.hero_vesper.ricochet.s_bounces[3]%$名敵人之間彈跳，每次命中造成%$heroes.hero_vesper.ricochet.s_damage[3]%$物理傷害。",
["HERO_VESPER_RICOCHET_TITLE"] = "彈跳箭",
["HERO_WITCH_CLASS"] = "巧術女巫",
["HERO_WITCH_DESC"] = "司蕾吉平常是個成天愛捉弄妖精森林旅客的搗蛋鬼，但要是威脅森林或地精夥伴們的敵人出現，她天真無邪的笑容背後，可是一位心狠手辣、不容小覷的女巫。",
["HERO_WITCH_DISENGAGE_DESCRIPTION_1"] = "當生命值低於%$heroes.hero_witch.disengage.hp_to_trigger%$%時，司蕾吉會向後方傳送並留下一個玩偶代替戰鬥。誘餌具有%$heroes.hero_witch.disengage.decoy.hp_max[1]%$生命值，且遭破壞時會爆炸，暈眩敵人%$heroes.hero_witch.disengage.decoy.explotion.stun_duration[1]%$秒。",
["HERO_WITCH_DISENGAGE_DESCRIPTION_2"] = "當生命值低於%$heroes.hero_witch.disengage.hp_to_trigger%$%時，司蕾吉會向後方傳送並留下一個玩偶代替戰鬥。誘餌具有%$heroes.hero_witch.disengage.decoy.hp_max[2]%$生命值，且遭破壞時會爆炸，暈眩敵人%$heroes.hero_witch.disengage.decoy.explotion.stun_duration[2]%$秒。",
["HERO_WITCH_DISENGAGE_DESCRIPTION_3"] = "當生命值低於%$heroes.hero_witch.disengage.hp_to_trigger%$%時，司蕾吉會向後方傳送並留下一個玩偶代替戰鬥。誘餌具有%$heroes.hero_witch.disengage.decoy.hp_max[3]%$生命值，且遭破壞時會爆炸，暈眩敵人%$heroes.hero_witch.disengage.decoy.explotion.stun_duration[3]%$秒。",
["HERO_WITCH_DISENGAGE_TITLE"] = "亮亮玩偶",
["HERO_WITCH_NAME"] = "司蕾吉",
["HERO_WITCH_PATH_AOE_DESCRIPTION_1"] = "將一瓶巨型藥水砸落地面，對範圍內敵人造成%$heroes.hero_witch.skill_path_aoe.s_damage[1]%$魔法傷害，並給予%$heroes.hero_witch.skill_path_aoe.duration[1]%$秒緩速效果。",
["HERO_WITCH_PATH_AOE_DESCRIPTION_2"] = "將一瓶巨型藥水砸落地面，對範圍內敵人造成%$heroes.hero_witch.skill_path_aoe.s_damage[2]%$魔法傷害，並給予%$heroes.hero_witch.skill_path_aoe.duration[2]%$秒緩速效果。",
["HERO_WITCH_PATH_AOE_DESCRIPTION_3"] = "將一瓶巨型藥水砸落地面，對範圍內敵人造成%$heroes.hero_witch.skill_path_aoe.s_damage[3]%$魔法傷害，並給予%$heroes.hero_witch.skill_path_aoe.duration[3]%$秒緩速效果。",
["HERO_WITCH_PATH_AOE_TITLE"] = "揮和壓扁",
["HERO_WITCH_POLYMORPH_DESCRIPTION_1"] = "把敵人變成小南瓜，持續%$heroes.hero_witch.skill_polymorph.duration[1]%$秒。小南瓜擁有目標%$heroes.hero_witch.skill_polymorph.pumpkin.hp[1]%$%的生命值。",
["HERO_WITCH_POLYMORPH_DESCRIPTION_2"] = "把敵人變成小南瓜，持續%$heroes.hero_witch.skill_polymorph.duration[2]%$秒。小南瓜擁有目標%$heroes.hero_witch.skill_polymorph.pumpkin.hp[2]%$%的生命值。",
["HERO_WITCH_POLYMORPH_DESCRIPTION_3"] = "把敵人變成小南瓜，持續%$heroes.hero_witch.skill_polymorph.duration[3]%$秒。小南瓜擁有目標%$heroes.hero_witch.skill_polymorph.pumpkin.hp[3]%$%的生命值。",
["HERO_WITCH_POLYMORPH_TITLE"] = "變菜菜！",
["HERO_WITCH_SOLDIERS_DESCRIPTION_1"] = "召喚%$heroes.hero_witch.skill_soldiers.soldiers_amount[1]%$隻貓咪與敵人戰鬥。貓咪具有%$heroes.hero_witch.skill_soldiers.soldier.hp_max[1]%$生命值且可造成 %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[1]%$物理傷害。",
["HERO_WITCH_SOLDIERS_DESCRIPTION_2"] = "召喚%$heroes.hero_witch.skill_soldiers.soldiers_amount[2]%$隻貓咪與敵人戰鬥。貓咪具有%$heroes.hero_witch.skill_soldiers.soldier.hp_max[2]%$生命值且可造成 %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[2]%$物理傷害。",
["HERO_WITCH_SOLDIERS_DESCRIPTION_3"] = "召喚%$heroes.hero_witch.skill_soldiers.soldiers_amount[3]%$隻貓咪與敵人戰鬥。貓咪具有%$heroes.hero_witch.skill_soldiers.soldier.hp_max[3]%$生命值且可造成 %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[3]%$物理傷害。",
["HERO_WITCH_SOLDIERS_TITLE"] = "夜煞集合",
["HERO_WITCH_ULTIMATE_DESCRIPTION_1"] = "將最多%$heroes.hero_witch.ultimate.max_targets[2]%$名敵人往回傳送，並使其陷入沉睡%$heroes.hero_witch.ultimate.duration[2]%$秒。",
["HERO_WITCH_ULTIMATE_DESCRIPTION_2"] = "將最多%$heroes.hero_witch.ultimate.max_targets[3]%$名敵人往回傳送，並使其陷入沉睡%$heroes.hero_witch.ultimate.duration[3]%$秒。",
["HERO_WITCH_ULTIMATE_DESCRIPTION_3"] = "將最多%$heroes.hero_witch.ultimate.max_targets[4]%$名敵人往回傳送，並使其陷入沉睡%$heroes.hero_witch.ultimate.duration[4]%$秒。",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "將一批敵人傳送回路徑前段，並使他們暫時沉睡。",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_NAME"] = "昏昏欲退",
["HERO_WITCH_ULTIMATE_TITLE"] = "昏昏欲退",
["HERO_WUKONG_CLASS"] = "美猴王",
["HERO_WUKONG_DESC"] = "話說那孫悟空乃是從一塊吸收天地陰陽之氣，鐘日月之精華的仙石誕生而來，他不僅身懷舉世無雙的神力，更擁有長生不老之身。這傳說中的齊天大聖，今回卻被賊人奪走了他的神力玉！面對三魔王孫悟空可不能坐以待斃，他將重出江湖，再顯神威！",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_1"] = "騰空躍起，將金箍棒超巨大化後重壓一名敵人，將其即殺並在目標周圍造成%$heroes.hero_wukong.giant_staff.area_damage.damage_min[1]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[1]%$範圍傷害。",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_2"] = "騰空躍起，將金箍棒超巨大化後重壓一名敵人，將其即殺並在目標周圍造成%$heroes.hero_wukong.giant_staff.area_damage.damage_min[2]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[2]%$範圍傷害。",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_3"] = "騰空躍起，將金箍棒超巨大化後重壓一名敵人，將其即殺並在目標周圍造成%$heroes.hero_wukong.giant_staff.area_damage.damage_min[3]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[3]%$範圍傷害。",
["HERO_WUKONG_GIANT_STAFF_TITLE"] = "定海神珍",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_1"] = "拔下毫毛化為2名分身與孫悟空一同戰鬥。分身能給予%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[1]%$點傷害，且存在%$heroes.hero_wukong.hair_clones.soldier.melee_attack.duration[1]%$秒。",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_2"] = "拔下毫毛化為2名分身與孫悟空一同戰鬥。分身能給予%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[2]%$點傷害，且存在%$heroes.hero_wukong.hair_clones.soldier.melee_attack.duration[2]%$秒。",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_3"] = "拔下毫毛化為2名分身與孫悟空一同戰鬥。分身能給予%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[3]%$點傷害，且存在%$heroes.hero_wukong.hair_clones.soldier.melee_attack.duration[3]%$秒。",
["HERO_WUKONG_HAIR_CLONES_TITLE"] = "身外化身",
["HERO_WUKONG_NAME"] = "孫悟空",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_1"] = "把金箍棒丟到空中，讓其分裂為%$heroes.hero_wukong.pole_ranged.pole_amounts[1]%$支棍棒落向敵軍，每支都會在小範圍內造成%$heroes.hero_wukong.pole_ranged.damage_min[1]%$傷害並暈眩敵人。",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_2"] = "把金箍棒丟到空中，讓其分裂為%$heroes.hero_wukong.pole_ranged.pole_amounts[2]%$支棍棒落向敵軍，每支都會在小範圍內造成%$heroes.hero_wukong.pole_ranged.damage_min[2]%$傷害並暈眩敵人。",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_3"] = "把金箍棒丟到空中，讓其分裂為%$heroes.hero_wukong.pole_ranged.pole_amounts[3]%$支棍棒落向敵軍，每支都會在小範圍內造成%$heroes.hero_wukong.pole_ranged.damage_min[3]%$傷害並暈眩敵人。",
["HERO_WUKONG_POLE_RANGED_TITLE"] = "箍棒雨",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_1"] = "玉龍從天而降，鑽入地面造成%$heroes.hero_wukong.ultimate.damage_total[2]%$真實傷害，並產生緩速區域。",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_2"] = "玉龍從天而降，鑽入地面造成%$heroes.hero_wukong.ultimate.damage_total[3]%$真實傷害，並產生緩速區域。",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_3"] = "玉龍從天而降，鑽入地面造成%$heroes.hero_wukong.ultimate.damage_total[4]%$真實傷害，並產生緩速區域。",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "召喚玉龍",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_NAME"] = "玉龍降世",
["HERO_WUKONG_ULTIMATE_TITLE"] = "玉龍降世",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_1"] = "好夥伴豬八戒與孫悟空走遍天涯海角！八戒可造成%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[1]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[1]%$點傷害，且有小機率能使出大範圍攻擊。",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_2"] = "好夥伴豬八戒與孫悟空走遍天涯海角！八戒可造成%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[2]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[2]%$點傷害，且有小機率能使出大範圍攻擊。",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_3"] = "好夥伴豬八戒與孫悟空走遍天涯海角！八戒可造成%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[3]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[3]%$點傷害，且有小機率能使出大範圍攻擊。",
["HERO_WUKONG_ZHU_APPRENTICE_TITLE"] = "豬二弟",
["HINT"] = "提示",
["HOURS_ABBREVIATION"] = "小時",
["Hardcore! play at your own risk!"] = "艱巨挑戰！有風險!",
["Help"] = "幫助",
["Hero at your command!"] = "英雄聽候你的調遣！",
["Heroes"] = "英雄",
["Heroes are elite units that can face strong enemies and support your forces."] = "英雄是精英單位，能對抗強大的敵人，也能支援你的軍隊。",
["Heroes gain experience every time they damage an enemy or use an ability."] = "英雄通過擊傷敵人或使用技能而獲得經驗。",
["Heroic"] = "英雄模式",
["Heroic challenge"] = "英雄挑戰",
["High"] = "高",
["I'm ready. Now bring it on!"] = "我準備好了。現在繼續！",
["INCOMING NEXT WAVE!"] = "下一波敵人馬上要來了！",
["INCOMING WAVE"] = "一波敵人即將到來",
["INGAME_BALLOON_BUILD_HERE"] = "點此建造！",
["INGAME_BALLOON_GOAL"] = "別讓敵人通過這裡",
["INGAME_BALLOON_GOLD"] = "擊殺敵人即可獲得金幣",
["INGAME_BALLOON_INCOMING"] = "下一波敵人即將到來！",
["INGAME_BALLOON_NEW_HERO"] = "新英雄！",
["INGAME_BALLOON_NEW_POWER"] = "新能力！",
["INGAME_BALLOON_NOTIFICATION_TAP_HERE"] = "點擊這裡！",
["INGAME_BALLOON_SELECT_HERO"] = "點擊選擇！",
["INGAME_BALLOON_START_BATTLE"] = "開始戰鬥！",
["INGAME_BALLOON_TAP_HERE"] = "點擊道路",
["INGAME_BALLOON_TAP_TO_CALL"] = "點擊以提前召喚",
["INGAME_BALLOON_TAP_TWICE_BUILD"] = "點擊以建造防禦塔",
["INGAME_BALLOON_TAP_TWICE_START"] = "點擊兩次開始戰鬥",
["INGAME_BALLOON_TAP_TWICE_WAVE"] = "點擊以召喚敵人",
["INGAME_TUTORIAL1_HELP1"] = "別讓敵人通過這裡。",
["INGAME_TUTORIAL1_HELP2"] = "建造防禦塔以對抗路徑上的敵人。",
["INGAME_TUTORIAL1_HELP3"] = "擊殺敵人即可獲得金幣。",
["INGAME_TUTORIAL1_SUBTITLE1"] = "守護國土，抵禦敵襲。",
["INGAME_TUTORIAL1_SUBTITLE2"] = "沿路建造防禦塔，阻擋敵軍。",
["INGAME_TUTORIAL1_TITLE"] = "目標",
["INGAME_TUTORIAL_GOTCHA_1"] = "收到！",
["INGAME_TUTORIAL_GOTCHA_2"] = "我準備好了，開始吧！",
["INGAME_TUTORIAL_HINT"] = "訣竅",
["INGAME_TUTORIAL_INSTRUCTIONS"] = "說明",
["INGAME_TUTORIAL_NEW_TIP"] = "新提示",
["INGAME_TUTORIAL_NEXT"] = "下一步！",
["INGAME_TUTORIAL_OK"] = "好的！",
["INGAME_TUTORIAL_SKIP"] = "跳過！",
["INGAME_TUTORIAL_TIP_CHALLENGE"] = "警告",
["INSTRUCTIONS"] = "說明",
["ITEM_CLUSTER_BOMB_BOTTOM_DESC"] = "比較難吃，但更好玩的爆米花！",
["ITEM_CLUSTER_BOMB_BOTTOM_INFO"] = "一枚會分裂出小型爆彈的爆彈。",
["ITEM_CLUSTER_BOMB_DESC"] = "投擲一枚對範圍內敵人造成傷害的爆彈，並會再分裂出小型的爆彈。",
["ITEM_CLUSTER_BOMB_NAME"] = "星團爆彈",
["ITEM_DEATHS_TOUCH_BOTTOM_DESC"] = "讓你體會一下神的力量……死亡之神！",
["ITEM_DEATHS_TOUCH_BOTTOM_INFO"] = "選擇、點擊、死亡。",
["ITEM_DEATHS_TOUCH_DESC"] = "獲得死亡之力加持，點擊任何敵人可立即將之消滅。不適用於BOSS或小BOSS。",
["ITEM_DEATHS_TOUCH_NAME"] = "死亡之手",
["ITEM_LOOT_BOX_BOTTOM_DESC"] = "拿個幾箱就一輩子不愁吃穿了。",
["ITEM_LOOT_BOX_BOTTOM_INFO"] = "將一個箱子丟到路上，對敵人造成傷害並立即獲得金幣。",
["ITEM_LOOT_BOX_DESC"] = "將一個寶箱丟到路上，對敵人造成傷害並立即獲得300金幣。",
["ITEM_LOOT_BOX_NAME"] = "主礦脈箱",
["ITEM_MEDICAL_KIT_BOTTOM_DESC"] = "將軍，你只需要包紮一下就好。",
["ITEM_MEDICAL_KIT_BOTTOM_INFO"] = "最多可為玩家恢復3顆心心。",
["ITEM_MEDICAL_KIT_DESC"] = "特殊套件，最多可為玩家恢復3顆心心。",
["ITEM_MEDICAL_KIT_NAME"] = "醫療箱",
["ITEM_PORTABLE_COIL_BOTTOM_DESC"] = "劈里啪啦電老鼠！",
["ITEM_PORTABLE_COIL_BOTTOM_INFO"] = "佈下陷阱，對範圍內的敵人造成傷害並使其暈眩。",
["ITEM_PORTABLE_COIL_DESC"] = "佈下範圍陷阱，對誤觸的敵人造成傷害並使其暈眩。該效果可以對附近的敵人造成連鎖反應。",
["ITEM_PORTABLE_COIL_NAME"] = "攜帶式線圈",
["ITEM_ROOM_EQUIP"] = "裝備",
["ITEM_ROOM_EQUIPPED"] = "已裝備",
["ITEM_ROOM_EQUIPPED_ITEMS"] = "已裝備道具",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_DESC"] = "是否曾被來襲的敵軍搞得措手不及？就用這個！",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_INFO"] = "將一群敵人傳送回路徑前段。",
["ITEM_SCROLL_OF_SPACESHIFT_DESC"] = "將最多10名敵人傳送回路徑前段。",
["ITEM_SCROLL_OF_SPACESHIFT_NAME"] = "瞬動卷軸",
["ITEM_SECOND_BREATH_BOTTOM_DESC"] = "會從墓地中爬起，但不會產生不死族的弱點。",
["ITEM_SECOND_BREATH_BOTTOM_INFO"] = "復活陣亡的英雄，治療傷口並重置英雄奧義的冷卻時間。",
["ITEM_SECOND_BREATH_DESC"] = "神聖的祝福，能夠復活陣亡的英雄，治療傷口並重置英雄奧義的冷卻時間。",
["ITEM_SECOND_BREATH_NAME"] = "重獲新生",
["ITEM_SUMMON_BLACKBURN_BOTTOM_DESC"] = "獨一無二。無與倫比。",
["ITEM_SUMMON_BLACKBURN_BOTTOM_INFO"] = "召喚強大的布萊克本，與您並肩作戰。",
["ITEM_SUMMON_BLACKBURN_DESC"] = "召喚強大的亡靈戰士，殲滅敵人。",
["ITEM_SUMMON_BLACKBURN_NAME"] = "布萊克本頭盔",
["ITEM_VEZNAN_WRATH_BOTTOM_DESC"] = "讓他們嚐嚐黑巫師的無盡力量！",
["ITEM_VEZNAN_WRATH_BOTTOM_INFO"] = "殲滅戰場上的所有敵人。",
["ITEM_VEZNAN_WRATH_DESC"] = "佛則南施展強大的法術，一次殲滅戰場上所有敵人。",
["ITEM_VEZNAN_WRATH_NAME"] = "佛則南之怒",
["ITEM_WINTER_AGE_BOTTOM_DESC"] = "如果你真的很討厭夏天，也不妨試試看。",
["ITEM_WINTER_AGE_BOTTOM_INFO"] = "凍結畫面上的所有敵人。",
["ITEM_WINTER_AGE_DESC"] = "透過強大法術在戰場上颳起刺骨寒風，使所有敵人凍結數秒。",
["ITEM_WINTER_AGE_NAME"] = "冬河期",
["Impossible"] = "不可能",
["Iron"] = "鋼鐵",
["Iron Challenge"] = "鋼鐵挑戰",
["Iron challenge"] = "鋼鐵挑戰",
["JOYSTICK_CONFIG_AXIS_DEAD_ZONE"] = "控制桿死區",
["JOYSTICK_CONFIG_AXIS_DEAD_ZONE_XBOX"] = "首次重複延遲",
["JOYSTICK_CONFIG_FIRST_REPEAT_DELAY"] = "重複延遲",
["JOYSTICK_CONFIG_POINTER_ACCEL"] = "指標加速度",
["JOYSTICK_CONFIG_POINTER_MAX_ACCEL"] = "指標最大加速度",
["JOYSTICK_CONFIG_POINTER_SENS"] = "指標靈敏度",
["JOYSTICK_CONFIG_POINTER_SPEED"] = "速度指標",
["JOYSTICK_CONFIG_REPEAT_DELAY"] = "重複延遲",
["JOYSTICK_CONFIG_SWAP_ABXY"] = "交換 A/B 與 X/Y",
["JOYSTICK_HELP_INGAME_A"] = "選擇",
["JOYSTICK_HELP_INGAME_AXIS_LEFT"] = "移動",
["JOYSTICK_HELP_INGAME_AXIS_LEFT_BUTTON"] = "切換指針",
["JOYSTICK_HELP_INGAME_B"] = "取消/後退",
["JOYSTICK_HELP_INGAME_BACK"] = "顯示資訊卡",
["JOYSTICK_HELP_INGAME_DPAD_DOWN"] = "移動援軍",
["JOYSTICK_HELP_INGAME_DPAD_LEFT"] = "呼叫援軍",
["JOYSTICK_HELP_INGAME_DPAD_RIGHT"] = "英雄戰力 2",
["JOYSTICK_HELP_INGAME_DPAD_UP"] = "英雄戰力 1",
["JOYSTICK_HELP_INGAME_ESCAPE"] = "取消/後退",
["JOYSTICK_HELP_INGAME_LB"] = "主戰英雄",
["JOYSTICK_HELP_INGAME_MOVE_HEROES"] = "移動英雄",
["JOYSTICK_HELP_INGAME_MOVE_REINFORCEMENTS"] = "移動援軍",
["JOYSTICK_HELP_INGAME_NX_A"] = "選擇",
["JOYSTICK_HELP_INGAME_NX_AXIS_LEFT"] = "移動",
["JOYSTICK_HELP_INGAME_NX_AXIS_LEFT_BUTTON"] = "切換指針",
["JOYSTICK_HELP_INGAME_NX_B"] = "取消/後退",
["JOYSTICK_HELP_INGAME_NX_L"] = "主戰英雄",
["JOYSTICK_HELP_INGAME_NX_MINUS"] = "顯示資訊卡",
["JOYSTICK_HELP_INGAME_NX_PLUS"] = "暫停/恢復",
["JOYSTICK_HELP_INGAME_NX_R"] = "輔助英雄",
["JOYSTICK_HELP_INGAME_NX_X"] = "敵軍資送出",
["JOYSTICK_HELP_INGAME_NX_Y"] = "敵軍資訊",
["JOYSTICK_HELP_INGAME_POWERS"] = "能力",
["JOYSTICK_HELP_INGAME_RB"] = "輔助英雄",
["JOYSTICK_HELP_INGAME_START"] = "暫停/恢復",
["JOYSTICK_HELP_INGAME_X"] = "敵軍資送出",
["JOYSTICK_HELP_INGAME_Y"] = "敵軍資訊",
["JOYSTICK_HELP_MAP_A"] = "選擇",
["JOYSTICK_HELP_MAP_AXIS_LEFT"] = "移動",
["JOYSTICK_HELP_MAP_B"] = "取消/後退",
["JOYSTICK_HELP_MAP_BACK"] = "顯示/隱藏選項",
["JOYSTICK_HELP_MAP_LB"] = "上一級/頁",
["JOYSTICK_HELP_MAP_NX_A"] = "選擇",
["JOYSTICK_HELP_MAP_NX_AXIS_LEFT"] = "移動",
["JOYSTICK_HELP_MAP_NX_B"] = "取消/後退",
["JOYSTICK_HELP_MAP_NX_L"] = "上一級/頁",
["JOYSTICK_HELP_MAP_NX_MINUS"] = "顯示/隱藏選項",
["JOYSTICK_HELP_MAP_NX_PLUS"] = "顯示/隱藏選項",
["JOYSTICK_HELP_MAP_NX_R"] = "下一級/頁",
["JOYSTICK_HELP_MAP_RB"] = "下一級/頁",
["JOYSTICK_HELP_MAP_START"] = "顯示/隱藏選項",
["JOYSTICK_HELP_SLOTS_A"] = "選擇",
["JOYSTICK_HELP_SLOTS_AXIS_LEFT"] = "移動",
["JOYSTICK_HELP_SLOTS_B"] = "取消/後退",
["JOYSTICK_HELP_SLOTS_BACK"] = "顯示/隱藏選項",
["JOYSTICK_HELP_SLOTS_NX_A"] = "選擇",
["JOYSTICK_HELP_SLOTS_NX_AXIS_LEFT"] = "移動",
["JOYSTICK_HELP_SLOTS_NX_B"] = "取消/後退",
["JOYSTICK_HELP_SLOTS_NX_MINUS"] = "顯示/隱藏選項",
["JOYSTICK_HELP_SLOTS_NX_PLUS"] = "顯示/隱藏選項",
["JOYSTICK_HELP_SLOTS_START"] = "顯示/隱藏選項",
["KEYBOARD_KEY_ESCAPE"] = "逃逸",
["KEYBOARD_KEY_PAGE_DOWN"] = "翻頁向下",
["KEYBOARD_KEY_PAGE_UP"] = "翻頁向上",
["KEYBOARD_KEY_RETURN"] = "回車",
["KEYBOARD_KEY_SPACE"] = "空格",
["LEVEL_10_HEROIC"] = "英豪挑戰 10",
["LEVEL_10_HISTORY"] = "原來邪教徒開採這些水晶，是在深谷外建造著外型詭異的法器。它滿溢詭譎的能量，四周的空氣都感覺格外沉重。在我們繼續深入前可不能留此禍害。",
["LEVEL_10_IRON"] = "鐵腕挑戰10",
["LEVEL_10_IRON_UNLOCK"] = "待定",
["LEVEL_10_MODES_UPGRADES"] = "最高5級",
["LEVEL_10_TITLE"] = "10.神殿後庭",
["LEVEL_11_HEROIC"] = "英豪挑戰11",
["LEVEL_11_HISTORY"] = "終於離開了深谷，但還有很長的路要走。我們到達了一座嵌有水晶的巨大傳送門前，而泛視先知，魔蒂婭絲就要完成她的儀式。雖然不知道接下來要面臨什麼，但我們可不會在此退步。準備迎敵！",
["LEVEL_11_IRON"] = "鐵腕挑戰11",
["LEVEL_11_IRON_UNLOCK"] = "待定",
["LEVEL_11_MODES_UPGRADES"] = "最高5級",
["LEVEL_11_TITLE"] = "11.深谷高原",
["LEVEL_12_HEROIC"] = "英豪挑戰12",
["LEVEL_12_HISTORY"] = "我們迎回了迪納斯，並一起跨越這道通往未知的傳送門。門後的異世界看來簡直像是利尼維亞的扭曲倒影，但卻被破滅所吞沒。當心腳下，比邪教徒更可怕的「什麼」正在黑暗中蠢蠢欲動。",
["LEVEL_12_IRON"] = "鐵腕挑戰12",
["LEVEL_12_IRON_UNLOCK"] = "待定",
["LEVEL_12_MODES_UPGRADES"] = "最高5級",
["LEVEL_12_TITLE"] = "12.枯萎農地",
["LEVEL_13_HEROIC"] = "英豪挑戰13",
["LEVEL_13_HISTORY"] = "熟悉的雲暴寺院在地平線上若隱若現。我們的該去的方向非常明確，跟隨這股腐臭味和陰邪之氣，就能找到一切的源頭。只剩在這裡活下去是個問題，此處的恐怖扭曲怪物，簡直像從地上胡亂長出來的一樣。",
["LEVEL_13_IRON"] = "鐵腕挑戰13",
["LEVEL_13_IRON_UNLOCK"] = "待定",
["LEVEL_13_MODES_UPGRADES"] = "最高5級",
["LEVEL_13_TITLE"] = "13.褻瀆神殿",
["LEVEL_14_HEROIC"] = "英豪挑戰14",
["LEVEL_14_HISTORY"] = "這些該死的生物到底是從哪冒出來的？！士兵都被折磨得身心俱疲，眼前一草一木似乎都有生命，隨時會朝我們攻擊而來，簡直像整片大地都竭盡全力要消滅我們。先知魔蒂婭絲和她的爪牙肯定就在附近了。",
["LEVEL_14_IRON"] = "鐵腕挑戰14",
["LEVEL_14_IRON_UNLOCK"] = "待定",
["LEVEL_14_MODES_UPGRADES"] = "最高5級",
["LEVEL_14_TITLE"] = "14.魔化峽谷",
["LEVEL_15_HEROIC"] = "英豪挑戰15",
["LEVEL_15_HISTORY"] = "我們終於跨越了魔化峽谷，現在唯一阻擋在我們和全視魔眼之間的就只剩魔蒂婭絲了。我們在深谷中已經見識過她的能耐，但如今在她威武主君的注視下，她肯定還要更加強大。這種挑戰可從來沒有讓我們卻步過，要上了！",
["LEVEL_15_IRON"] = "鐵腕挑戰15",
["LEVEL_15_IRON_UNLOCK"] = "待定",
["LEVEL_15_MODES_UPGRADES"] = "最高5級",
["LEVEL_15_TITLE"] = "15.惡視魔塔",
["LEVEL_16_HEROIC"] = "英豪挑戰16",
["LEVEL_16_HISTORY"] = "魔蒂婭絲已遭消滅，但更強大的敵人全視魔眼還在等著我們。這是剷除啟明邪教與侵入者的最後機會。無論接下來會發生什麼，若我們不團結到最後一刻，一切就都沒有意義了。前進吧！",
["LEVEL_16_IRON"] = "鐵腕挑戰16",
["LEVEL_16_IRON_UNLOCK"] = "待定",
["LEVEL_16_MODES_UPGRADES"] = "最高5級",
["LEVEL_16_TITLE"] = "16.飢渴頂峰",
["LEVEL_17_HISTORY"] = "總是瀰漫著歡快古怪氣息的妖精森林，如今看來卻陰森又詭譎。傳聞有一大批逝去的精靈族戰士與幽魂在此遊蕩，不只襲擊旅人，他們的存在更汙染了這片林地。將軍，我們必須得去一探究竟。",
["LEVEL_17_TITLE"] = "17.迷霧廢墟",
["LEVEL_18_HISTORY"] = "我們從葉隱哨站得到了消息，有部分精靈還正在奮力抵抗歸魂軍團的侵壓。刻不容緩，我們得盡快前去營救殘存的精靈，以及他們的隊長－－阿爾登。成功確保哨站的安全後，下一步就是直搗侵襲的根源了。",
["LEVEL_18_TITLE"] = "18.葉隱哨站",
["LEVEL_19_HISTORY"] = "疲憊不已的阿爾登，向我們指明了通往墮魂寺院的道路。蹂躪這片大地的歸魂軍團正是從那裡而起，而在背後的統帥者則是一名魔法師，其名為汎里埃－－馭魂魔導。無論有何代價，都必須在此絕此禍害！",
["LEVEL_19_TITLE"] = "19.墮魂寺院",
["LEVEL_1_HEROIC"] = "英豪挑戰1",
["LEVEL_1_HISTORY"] = "我們已經在南方森林搜索了數月，但國王迪納斯仍然不見蹤影。與此同時，我們森林守護者的樹靈族交好，但其與惡鄰居的森林野獸積怨已久，野獸只要碰上我們就會二話不說發動襲擊。\n得先了結這份恩怨，才能專心尋找吾王。",
["LEVEL_1_IRON"] = "鐵腕挑戰1",
["LEVEL_1_IRON_UNLOCK"] = "皇家弓兵\n誓約聖騎士",
["LEVEL_1_MODES_UPGRADES"] = "最高1級",
["LEVEL_1_TITLE"] = "1.碧綠之海",
["LEVEL_20_HISTORY"] = "我們收到來自森林邊緣，樹靈村落的緊急求救。他們正遭到凶暴鱷魚族的猛攻，這樣下去堅守不了太久的。務必小心為上，將軍。在鱷魚鱗片底下藏著的，可是無數的陰謀詭計。",
["LEVEL_20_TITLE"] = "20.樹靈村落",
["LEVEL_21_HISTORY"] = "確保了村莊的安全後，樹靈族向我們透漏了就在鱷魚們展開侵襲前，他們感應到先祖設下的封印鬆動了。帶著這份線索與鱷魚族突然的入侵，我方深入沼澤的中央地帶。在那裡偶然發現了一座古老的樹靈族石環，它如今的模樣簡直像是座巢穴......某種巨大生物的，巢穴。",
["LEVEL_21_TITLE"] = "21.沉沒遺跡",
["LEVEL_22_HISTORY"] = "抵達了封印的古代寺廟，我們最擔憂的事情發生了。長久以來，封印著「吞世者，噬界災鱷」的束縛幾乎已經破除，僅剩幾名絕望的樹靈薩滿在勉強維持著法術。將軍，若是不能在此阻止牠，噬界災鱷無底的胃袋必將吞噬王國的一切。",
["LEVEL_22_TITLE"] = "22.罪餓空穴",
["LEVEL_23_HISTORY"] = "偵察兵指出鄰近的山脈頻頻發生不自然的山崩現象，在經調查後發現是由一批我們無法識別的矮人所引起，他們正在山的南面組裝某種巨大的人型機械。將軍，請您動身前去一探究竟吧。",
["LEVEL_23_TITLE"] = "23.黑鋼之門",
["LEVEL_24_HISTORY"] = "雖說矮人總以發明而著稱，但這支自稱為「黑鋼」的氏族對金屬的狂熱實在太過火了，甚至能用博爾格一派的矮人都相形見絀的瘋狂速度，鍛造金屬強化自身。這背後到底是誰在搞的鬼？探清事情的真相吧！",
["LEVEL_24_TITLE"] = "24.狂亂產線",
["LEVEL_25_HISTORY"] = "事情正如我們所擔心的，只有藏身在這座大山的內部，才有可能放進足以製造此等巨大機械人形的熔爐。這裡到底有多少矮人啊？他們不只邊抵禦我們的進攻，手邊更從未停止過鍛造與焊接。更詭異的是，他們全都長得一模一樣？這當中肯定有什麼異常之處。",
["LEVEL_25_TITLE"] = "25.巨型核心",
["LEVEL_26_HISTORY"] = "我軍在山內部探索時來到了一處放滿大缸的房間，而當中，並非空無一物。也難怪他們人數如此眾多，又都有相似的外貌與手藝了。在這當中的全是同一位矮人，其名「詭鬚」！他用邪惡的科技手段不斷生產著自己的複製體，將軍，可不能再讓他為所欲為！",
["LEVEL_26_TITLE"] = "26.複製室",
["LEVEL_27_HISTORY"] = "儘管已成功阻撓絕大多數黑鋼成員的行動，但若詭鬚仍然逍遙法外，這一切都只是徒勞。他現在肯定在人型機械的頭部做最後的收尾工作，將軍，領軍攻往頂峰，祈禱我們這次能夠逮到真正的「詭鬚」吧。",
["LEVEL_27_TITLE"] = "27.統治圓頂",
["LEVEL_28_HISTORY"] = "循著偵察兵留下的線索，我們終於發現了那些該死邪教徒的蹤跡。他們居然拜起了新神......？一個卑鄙邪惡、吐絲織網的汙穢怪物......蜘蛛加上邪教，這組合聽起來就不會有什麼好事。",
["LEVEL_28_TITLE"] = "28.玷污神廟",
["LEVEL_29_HISTORY"] = "隨著探查的深入，越發明朗的是這支可怕的蜘蛛大軍，肯定早在地下培養多時，等待一舉進攻的時刻。隨著周遭的蛛網越加稠密，與詭譎到近乎讓人窒息的黑暗，我敢說我們離巢穴中心已經不遠了。",
["LEVEL_29_TITLE"] = "29.繁殖室",
["LEVEL_2_HEROIC"] = "英豪挑戰2",
["LEVEL_2_HISTORY"] = "注意！仙靈來報，永光之心受到了攻擊，我們得回頭去幫助樹靈族們！黑暗大軍的部分成員會在戰場上與我們會合，還請保持戒備，現在或許我們在同一條船上，但這條船隨時都有翻覆的可能。",
["LEVEL_2_IRON"] = "鐵腕挑戰2",
["LEVEL_2_IRON_UNLOCK"] = "奧術巫師\n三管加農炮",
["LEVEL_2_MODES_UPGRADES"] = "最高2級",
["LEVEL_2_TITLE"] = "2.守護者之門",
["LEVEL_30_HISTORY"] = "我們最終，抵達了他們口中女神的巢穴——這是一座飽受時間摧殘，早被遺棄的神廟。被人遺忘的過去，隨著時光點滴的積累，成為了壓垮它的重擔；為神祇量身打造的王座，其神也早已消失在人們的記憶中。這次，絕不能再留活口，一定要徹底殲滅這些世界的害蟲。",
["LEVEL_30_TITLE"] = "30.被遺忘的王座",
["LEVEL_31_HISTORY"] = "征戰已久，和平的日子終於到來了......但哪裡能有天下太平呢？\n彼方，敵軍虎視眈眈，我方也蓄勢待發，緊迫的氛圍間，誰也互不相讓。結局究竟鹿死誰手，無人知曉......\n就在悄無聲息間，這份平衡打破了。",
["LEVEL_31_TITLE"] = "31.仙界猴林",
["LEVEL_32_HISTORY"] = "我方一路追擊到火山深處，此地雖廢棄已久，但曾是祭拜烈火的寺廟。\n\n然而不妙的在後頭......「焰火巨龍」，本應是這片熔淵中立的守護龍，如今卻異常狂暴，戾氣沖天。種種跡象都表明，這肯定是紅孩兒搞鬼讓牠失去了原先的神智。與龍為敵絕非易事，但我們別無選擇。上吧！",
["LEVEL_32_TITLE"] = "32.火龍洞穴",
["LEVEL_33_HISTORY"] = "在鏖戰紅孩兒後，接著抵達的是怒風嶼——謂之「怒風」，可絕非浪得虛名。甫一踏上岸的瞬間，猛烈的陣風就席捲而來，伴隨著狂怒的嘶吼，以詭譎的節拍陣陣咆嘯。黑暗、雷閃，轟鳴的烏雲遮蔽了青天。\n做好準備吧......風暴就要到來了。",
["LEVEL_33_TITLE"] = "33.怒風嶼",
["LEVEL_34_HISTORY"] = "在這風暴中我們所受的罪，可都多虧了這位公主和她手上巨大的鐵扇。在挺過襲捲的狂風後，如今我們來到了風暴的正中央處。這裡既無風也無雨，甚至是美得驚人——可別因此就放鬆戒備了，就算來的是皇室成員，也休想與我們為敵。",
["LEVEL_34_TITLE"] = "34.風暴之眼",
["LEVEL_35_HISTORY"] = "這就是最後一戰了。盛氣凌人的牛魔王，高傲地立於他金碧輝煌的要塞之上。他或許能有這份自視甚高的底氣，但那是因為牛魔王從未與我們一戰——這份自傲會讓他嘗到苦頭的！速速拿下牛魔王吧，等他完全掌握神力玉的力量，就為時已晚了。為了守護此世我們珍重的一切......奮勇迎敵吧，利尼維亞的同胞們！",
["LEVEL_35_TITLE"] = "35.魔王堡壘",
["LEVEL_3_HEROIC"] = "英豪挑戰3",
["LEVEL_3_HISTORY"] = "我們在最後關頭趕到了永光之心，但森林野獸已經陸續抵達了。保持警備，強化防陣！不惜一切代價都要守住心臟，否則整片森林和樹靈族都將毀滅。",
["LEVEL_3_IRON"] = "鐵腕挑戰3",
["LEVEL_3_IRON_UNLOCK"] = "皇家弓兵\n誓約聖騎士",
["LEVEL_3_MODES_UPGRADES"] = "最高3級",
["LEVEL_3_TITLE"] = "3.永光之心",
["LEVEL_4_HEROIC"] = "英豪挑戰4",
["LEVEL_4_HISTORY"] = "確保了永光之心的安全，我們該要重整旗鼓、乘勝追擊了。將戰線推向野獸的領土吧，帶領部隊登上森林的樹頂，從制高點尋找牠們的大本營。",
["LEVEL_4_IRON"] = "鐵腕挑戰4",
["LEVEL_4_IRON_UNLOCK"] = "三管加農炮\n樹靈使者",
["LEVEL_4_MODES_UPGRADES"] = "最高4級",
["LEVEL_4_TITLE"] = "4.翡翠樹冠",
["LEVEL_5_HEROIC"] = "英豪挑戰5",
["LEVEL_5_HISTORY"] = "有賴於您努力攻下了制高點，我們才得以在森林邊緣的古遺跡中找到野獸的營地。請密切注意對方的奇襲，並領軍進發吧。雖然我們已漸入佳境，但離能安心還早得很呢。",
["LEVEL_5_IRON"] = "鐵腕挑戰5",
["LEVEL_5_IRON_UNLOCK"] = "奧術巫師\n誓約聖騎士",
["LEVEL_5_MODES_UPGRADES"] = "最高5級",
["LEVEL_5_TITLE"] = "5.荒蕪邊境",
["LEVEL_6_HEROIC"] = "英豪挑戰6",
["LEVEL_6_HISTORY"] = "我們至今看似佔了上風，但野獸的領袖，「血輾」可尚未倒下。牠自詡為萬獸之王，實力自然無須多言。可別看牠長得有些搞笑就小看牠了，否則必將喪命於獠牙之下。",
["LEVEL_6_IRON"] = "鐵腕挑戰6",
["LEVEL_6_IRON_UNLOCK"] = "皇家弓兵\n惡魔浴池",
["LEVEL_6_MODES_UPGRADES"] = "最高5級",
["LEVEL_6_TITLE"] = "6.野獸巢穴",
["LEVEL_7_HEROIC"] = "英豪挑戰7",
["LEVEL_7_HISTORY"] = "阻止野獸們夷平永光森林後，我們追蹤在背後推波助瀾的啟明邪教，來到一片荒凉之地，他們似乎在這裡謀劃著什麼詭計。必須小心為上……邪教徒對我們仍是謎團重重，不會這麼簡單被打倒的。",
["LEVEL_7_IRON"] = "鐵腕挑戰7",
["LEVEL_7_IRON_UNLOCK"] = "禁止皇家弓兵",
["LEVEL_7_MODES_UPGRADES"] = "最高5級",
["LEVEL_7_TITLE"] = "7.蒼涼谷",
["LEVEL_8_HEROIC"] = "英豪挑戰8",
["LEVEL_8_HISTORY"] = "我們在啟明邪教的領地，發現了數個巨型洞窟，裡面滿是與詭異的魔法共鳴著的水晶。他們正在開採這些水晶，目的顯然是用作某種能量來源。雖然尚不清楚他們在搞什麼主意，但阻撓開採活動肯定能打亂他們的陣腳。",
["LEVEL_8_IRON"] = "鐵腕挑戰8",
["LEVEL_8_IRON_UNLOCK"] = "三管加農炮\n誓約聖騎士",
["LEVEL_8_MODES_UPGRADES"] = "最高5級",
["LEVEL_8_TITLE"] = "8.猩紅礦坑",
["LEVEL_9_HEROIC"] = "英豪挑戰9",
["LEVEL_9_HISTORY"] = "這錯綜複雜的隧道正常肯定要把人逼瘋，但我們很確信自己走在正確的道路上，因為越往前走，邪教徒的活動就越加頻繁。隨著我們深入敵營，眼前出現各種毛骨悚然的景象，實在讓人不禁好奇最深處究竟有什麼樣的怪物。",
["LEVEL_9_IRON"] = "鐵腕挑戰9",
["LEVEL_9_IRON_UNLOCK"] = "惡魔浴池\n奧術巫師",
["LEVEL_9_MODES_UPGRADES"] = "最高5級",
["LEVEL_9_TITLE"] = "9.罪惡岔口",
["LEVEL_DEFEAT_TITLE"] = "失敗！",
["LEVEL_MODE_CAMPAIGN"] = "戰役",
["LEVEL_MODE_HEROIC"] = "英雄挑戰",
["LEVEL_MODE_HEROIC_DESCRIPTION"] = "只有最強大的英雄才能擔得起這個挑戰！敵人的精英部隊將測試你的戰術技巧！",
["LEVEL_MODE_IRON"] = "鋼鐵挑戰",
["LEVEL_MODE_IRON_DESCRIPTION"] = "鋼鐵挑戰是為最強大的守護戰神提供的考驗，也是戰術技巧最嚴酷的測試！",
["LEVEL_MODE_LOCKED_DESCRIPTION"] = "3 星評價完成本關卡就能解鎖該模式。",
["LEVEL_SELECT_AVAILABLE_TOWERS"] = "可用防禦塔",
["LEVEL_SELECT_CHALLENGE_ONE_ELITE_WAVE"] = "1波精英部隊",
["LEVEL_SELECT_CHALLENGE_ONE_LIFE"] = "共1點生命",
["LEVEL_SELECT_CHALLENGE_RULES"] = "挑戰規則",
["LEVEL_SELECT_CHALLENGE_SIX_ELITE_WAVE"] = "6波精英部隊",
["LEVEL_SELECT_DIFFICULTY_CASUAL"] = "休閒",
["LEVEL_SELECT_DIFFICULTY_IMPOSSIBLE"] = "不可能",
["LEVEL_SELECT_DIFFICULTY_NORMAL"] = "普通",
["LEVEL_SELECT_DIFFICULTY_VETERAN"] = "老兵",
["LEVEL_SELECT_GET_DLC"] = "拿到它",
["LEVEL_SELECT_MODE_LOCKED1"] = "模式已鎖定",
["LEVEL_SELECT_MODE_LOCKED2"] = "完成此階段以解鎖此模式。",
["LEVEL_SELECT_TO_BATTLE"] = "進入\n戰鬥",
["LV22_BOSS_BEFORE_FIGHT_EAT_01"] = "美食我來啦！哈 哈 哈",
["LV22_BOSS_BEFORE_FIGHT_EAT_02"] = "本鱷、討厭、植物",
["LV22_BOSS_BEFORE_FIGHT_EAT_03"] = "吃什麼，就會長什麼",
["LV22_BOSS_BEFORE_FIGHT_EAT_04"] = "這口挺清爽的啊",
["LV22_BOSS_BEFORE_FIGHT_EAT_05"] = "這樣就沒力了？",
["LV22_BOSS_BEFORE_FIGHT_EAT_06"] = "本鱷，再也不會飢餓了",
["LV22_BOSS_BEFORE_FIGHT_EAT_07"] = "你的塔原本挺不錯啊，哈哈哈",
["LV22_BOSS_BEFORE_FIGHT_EAT_08"] = "這是...自由的滋味",
["LV22_BOSS_INTRO_01"] = "第一餐，是份小點啊...",
["LV22_BOSS_INTRO_02"] = "看上去口感倒是不錯",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_01"] = "你要嚐到的只有蔓生荊棘",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_02"] = "植物都是朋友，不是你的食物",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_03"] = "你休想再吃任何一口！",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_04"] = "滾回你的牢裡去，怪物！",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_05"] = "汝不得吞噬！！",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_06"] = "我會守護綠林！",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_07"] = "你休想笑到最後",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_08"] = "牠變得越來越強了！幫幫我！！",
["LV22_MAGE_INTRO_01"] = "住嘴，你這怪物！",
["LV22_MAGE_INTRO_02"] = "快點！我快撐不住了！",
["Level"] = "等級",
["Localization Manager"] = "本土化管理器",
["Long"] = "遠",
["Low"] = "低",
["MAGES’ GUILD"] = "法師公會",
["MAGIC RESISTANT ENEMIES!"] = "魔法抗性敵人！",
["MAP_BALLON_BUY_UPGRADES_DESCRIPTION"] = "用獲得的星星改良防禦塔和提升戰力！",
["MAP_BALLON_BUY_UPGRADES_TITLE"] = "購買升級！",
["MAP_BALLON_HERO_LEVELUP_DESCRIPTION"] = "用獲得的英雄點數來訓練英雄！",
["MAP_BALLON_HERO_LEVELUP_TITLE"] = "英雄升級！",
["MAP_BALLON_HERO_UNLOCKED"] = "英雄解鎖！",
["MAP_BALLON_START_HERE"] = "從這裡開始！",
["MAP_BUTTON_ACHIEVEMENTS"] = "成就",
["MAP_BUTTON_HERO_ROOM"] = "英雄",
["MAP_BUTTON_ITEMS"] = "道具",
["MAP_BUTTON_SHOP"] = "商店",
["MAP_BUTTON_TOWER_ROOM"] = "防禦塔",
["MAP_BUTTON_UPGRADES"] = "升級",
["MAP_HEROROOM_HELP1"] = "選擇並訓練技能！",
["MAP_HEROROOM_HELP2"] = "點擊以繼續",
["MAP_HEROROOM_HELP3"] = "提升英雄戰力！",
["MAP_HERO_ROOM_GET_IT_NOW"] = "立刻獲得！",
["MAP_HERO_ROOM_SELECT"] = "裝備",
["MAP_HERO_ROOM_SELECTED"] = "已裝備",
["MAP_HERO_ROOM_TRAIN"] = "訓練",
["MAP_HERO_ROOM_UNLOCK"] = "於第 %s 關解鎖",
["MAP_HERO_ROOM_UNLOCK_10"] = "第10關解鎖",
["MAP_HERO_ROOM_UNLOCK_14"] = "第14關解鎖",
["MAP_HERO_ROOM_UNLOCK_15"] = "第15關解鎖",
["MAP_HERO_ROOM_UNLOCK_4"] = "第4關解鎖",
["MAP_HERO_ROOM_UNLOCK_7"] = "第7關解鎖",
["MAP_HERO_ROOM_UNLOCK_9"] = "第9關解鎖",
["MAP_HERO_ROOM_UNLOCK_AFTER_CAMPAIGN"] = "遊戲結束後解鎖 ",
["MAP_INAPPS_BUBBLE_INFO_1"] = "進行遊戲就能收集寶石。",
["MAP_INAPPS_BUBBLE_INFO_2"] = "使用寶石可以購買特殊物品！",
["MAP_INAPPS_BUBBLE_MORE_GEMS"] = "你需要更多寶石！",
["MAP_INAPPS_BUBBLE_SUCCESSFUL"] = "購買\n成功！",
["MAP_INAPP_GEMS_GEM_SHOP_TITLE"] = "寶石商店",
["MAP_INAPP_GEM_PACK_1"] = "一把寶石",
["MAP_INAPP_GEM_PACK_2"] = "一袋寶石",
["MAP_INAPP_GEM_PACK_3"] = "一桶寶石",
["MAP_INAPP_GEM_PACK_4"] = "一箱寶石",
["MAP_INAPP_GEM_PACK_5"] = "一車寶石",
["MAP_INAPP_GEM_PACK_6"] = "寶石山",
["MAP_INAPP_GEM_PACK_BAG"] = "一袋寶石",
["MAP_INAPP_GEM_PACK_BARREL"] = "一桶寶石",
["MAP_INAPP_GEM_PACK_CHEST"] = "一箱寶石",
["MAP_INAPP_GEM_PACK_FREE"] = "免費寶石",
["MAP_INAPP_GEM_PACK_HANDFUL"] = "一把寶石",
["MAP_INAPP_GEM_PACK_VAULT"] = "一倉庫寶石",
["MAP_INAPP_GEM_PACK_WAGON"] = "一車寶石",
["MAP_INAPP_MORE_GEMS"] = "更多寶石",
["MAP_INAPP_TEXT_1"] = "一把寶石",
["MAP_INAPP_TEXT_2"] = "一袋寶石",
["MAP_INAPP_TEXT_3"] = "一箱寶石",
["MAP_INAPP_TEXT_4"] = "免費寶石",
["MAP_INAPP_TEXT_GEMS"] = "寶石",
["MAP_NEW_GAMEMODE_UNLOCKED_DESCRIPTION"] = "抵擋無盡的敵人，爭奪最高的分數！",
["MAP_NEW_GAMEMODE_UNLOCKED_TITLE"] = "新挑戰！",
["MAP_NEW_HERO_ALERT"] = "新\n英雄！",
["MAP_NEW_TOWER_ALERT"] = "新\n防禦塔！",
["MAP_TOWER_ROOM_SELECT"] = "裝備",
["MAP_TOWER_ROOM_SELECTED"] = "已裝備",
["MENU_HUD_WAVES"] = "%i/%i",
["MINUTES_ABBREVIATION"] = "分",
["MORE_GAMES"] = "更多遊戲",
["MUSIC"] = "音樂",
["Magic resistant enemies take less damage from mages."] = "針對具有護甲的敵人，射手、士兵和火炮會造成較少的傷害。",
["Medium"] = "中",
["Music"] = "音樂",
["NEW POWER!"] = "新能力！",
["NEW SPECIAL POWER!"] = "新特殊能力！",
["NEW TOWER UNLOCKED"] = "新防禦塔解鎖",
["NEW TOWER UPGRADES"] = "新防禦塔升級",
["NEW TOWERS UNLOCKED"] = "新防禦塔解鎖",
["NEWS"] = "消息",
["NEW_ENEMY_ALERT_ICON"] = "新敵人",
["NO HEROES"] = "無英雄",
["NOTIFICATION_NEW_ENEMY_TITLE"] = "新敵人",
["NOTIFICATION_NEW_SPECIAL_TITLE"] = "新特殊能力！",
["NOTIFICATION_NEW_TOWERS_SUB_DESCRIPTION"] = "您現在可以將防禦塔升至%d級。",
["NOTIFICATION_NEW_TOWERS_SUB_TITLE"] = "開放等級%d防禦塔",
["NOTIFICATION_NEW_TOWERS_TITLE"] = "新防禦塔升級",
["NOTIFICATION_NEW_TOWER_TITLE"] = "解鎖新防禦塔",
["NOTIFICATION_armored_enemies_desc_body_1"] = "部分敵人會穿戴不同強度的護甲，可以抵禦魔法攻擊以外的傷害。",
["NOTIFICATION_armored_enemies_desc_body_2"] = "抵抗傷害類型：",
["NOTIFICATION_armored_enemies_desc_body_3"] = "有護甲的敵人可以抵禦射手、兵營和炮台的傷害。",
["NOTIFICATION_armored_enemies_desc_title"] = "敵方配有護甲！",
["NOTIFICATION_armored_enemies_enemy_name"] = "獠牙鬥士",
["NOTIFICATION_bottom_info_desc_body"] = "您可隨時透過點擊單位或頭像來確認敵軍資訊。",
["NOTIFICATION_bottom_info_desc_title"] = "敵人資訊",
["NOTIFICATION_bottom_info_tap_portrait_desc"] = "點擊此處重新開啟。",
["NOTIFICATION_button_ok"] = "確定",
["NOTIFICATION_glare_desc_body"] = "全視魔眼凝視戰場，其邪惡的注目將會強化周圍的敵人。",
["NOTIFICATION_glare_desc_bullets"] = "- 治療區域內的敵人\n- 觸發敵人的特殊能力",
["NOTIFICATION_glare_desc_title"] = "全視魔眼的注目",
["NOTIFICATION_hero_desc"] = "顯示等級、生命值和經驗值。",
["NOTIFICATION_hero_desc_baloon_1"] = "按一下傳送門或英雄單位來選擇。快速鍵：空白鍵",
["NOTIFICATION_hero_desc_baloon_2"] = "按一下路徑來移動英雄。",
["NOTIFICATION_hero_desc_body_1"] = "英雄是菁英單位，可以對抗強大的敵人並支援您的部隊。",
["NOTIFICATION_hero_desc_body_2"] = "英雄每次攻擊敵人或使用技能都會獲得經驗值。",
["NOTIFICATION_hero_desc_title"] = "英雄任您號令！",
["NOTIFICATION_magic_resistant_enemies_desc_body_1"] = "部分敵人擁有不同等級的魔法抗性，可以抵禦魔法攻擊的傷害。",
["NOTIFICATION_magic_resistant_enemies_desc_body_2"] = "抵抗傷害類型：",
["NOTIFICATION_magic_resistant_enemies_desc_body_3"] = "具有魔法抗性的敵人可以抵禦魔法塔的傷害。",
["NOTIFICATION_magic_resistant_enemies_desc_title"] = "敵方具有魔法抗性！",
["NOTIFICATION_magic_resistant_enemies_enemy_name"] = "薩滿龜",
["NOTIFICATION_rally_point_desc_body_1"] = "您可以調整兵營的集結點，指揮部隊防守不同的區域。",
["NOTIFICATION_rally_point_desc_body_2"] = "選擇集結點控制",
["NOTIFICATION_rally_point_desc_body_3"] = "選擇你想讓士兵前往的位置",
["NOTIFICATION_rally_point_desc_subtitle"] = "可集結範圍",
["NOTIFICATION_rally_point_desc_title"] = "指揮您的部隊！",
["NOTIFICATION_special_desc_body"] = "您可以呼叫增援部隊前來助陣。",
["NOTIFICATION_special_desc_bullets"] = "援兵是拖住敵方兵線的絕佳選擇。",
["NOTIFICATION_special_desc_title"] = "呼叫援兵",
["NOTIFICATION_title_enemy"] = "敵人資訊",
["NOTIFICATION_title_glare"] = "新提示！",
["NOTIFICATION_title_hint"] = "已解鎖英雄",
["NOTIFICATION_title_new_tip"] = "新提示",
["NOTIFICATION_title_special"] = "已解鎖特殊能力",
["Next!"] = "下一波！",
["No"] = "不",
["None"] = "無",
["Nope"] = "沒有",
["Normal"] = "普通",
["OFFER_GET_IT_NOW"] = "立刻獲得",
["OFFER_GET_THEM_NOW"] = "立刻獲得",
["OFFER_OFF"] = "關閉",
["OFFER_REGULAR"] = "原價",
["OK!"] = "好的！",
["ONE_TIME_OFFER"] = "一次性優惠！",
["OPTIONS"] = "選項",
["OPTIONS_PAGE_CONTROLS"] = "操作設定",
["OPTIONS_PAGE_HELP"] = "操作說明",
["OPTIONS_PAGE_SHORTCUTS"] = "鍵盤操作說明",
["OPTIONS_PAGE_VIDEO"] = "影片",
["Objective"] = "目標",
["Options"] = "選項",
["Over 50 stars are recommended to face this stage."] = "推薦擁有至少 50 顆星星才挑戰本關。",
["POPUP_CLEAR_PROGRESS_CONFIRM"] = "是否確定清除遊戲進度？",
["POPUP_LABEL_MAIN_MENU"] = "主選單",
["POPUP_SETTINGS_LANGUAGE"] = "語言",
["POPUP_SETTINGS_MUSIC"] = "音樂",
["POPUP_SETTINGS_SFX"] = "音效",
["POPUP_label_error_msg"] = "哎呀！出錯了。",
["POPUP_label_error_msg2"] = "哎呀！出錯了。",
["POPUP_label_purchasing"] = "正在處理你的請求",
["POPUP_label_title_options"] = "選項",
["POPUP_label_version"] = "0.0.9版本",
["POWER_SUMMON_DESCRIPTION"] = "你可以在戰場上召喚部隊前來助陣。",
["POWER_SUMMON_LARGE_DESCRIPTION"] = "你可以在戰場上召喚援軍助陣。\n\n援軍是免費的，每 15 秒就可召喚一次。",
["POWER_SUMMON_NAME"] = "呼叫援軍",
["PRICE_FREE"] = "免費",
["PRIVACY_POLICY_ASK_AGE"] = "你什麼時候出生？",
["PRIVACY_POLICY_BUTTON_LINK"] = "隱私權政策",
["PRIVACY_POLICY_CONSENT_SHORT"] = "在玩我們的遊戲之前，請確認你（如果你是兒童或青少年，請和父母一起）已閱讀並認可我們的隱私政策。",
["PRIVACY_POLICY_LINK"] = "隱私權政策",
["PRIVACY_POLICY_WELCOME"] = "歡迎！",
["PROCESSING YOUR REQUEST"] = "正在處理你的請求",
["Produced by %s"] = "%s 出品",
["QUIT"] = "退出",
["Quit"] = "退出",
["RESTORE_PURCHASES"] = "恢復購買",
["Reset"] = "重設",
["Restart"] = "重新開始",
["Resume"] = "返回",
["SECONDS_ABBREVIATION"] = "秒",
["SETTINGS_DISPLAY"] = "顯示器",
["SETTINGS_FRAMES_PER_SECOND"] = "每秒幀數",
["SETTINGS_FULLSCREEN"] = "全屏",
["SETTINGS_FULLSCREEN_BORDERLESS"] = "無邊框全螢幕",
["SETTINGS_IMAGE_QUALITY"] = "畫面質量",
["SETTINGS_LANGUAGE"] = "語言",
["SETTINGS_LARGE_MOUSE_POINTER"] = "大鼠標指針",
["SETTINGS_LOW_IMAGE_QUALITY_LINK"] = "影像品質低？點這裡！",
["SETTINGS_RETINA_DISPLAY"] = "视网膜显示屏 (Mac)",
["SETTINGS_SCREEN_RESOLUTION"] = "屏幕分辨率",
["SETTINGS_SUPPORT"] = "支援",
["SETTINGS_VSYNC"] = "VSYNC",
["SFX"] = "音效",
["SHOP_DESKTOP_GET_DLC_BUTTON"] = "拿到它",
["SHOP_DESKTOP_TITLE"] = "商店",
["SHOP_ROOM_BEST_VALUE_TITLE"] = "最佳優惠",
["SHOP_ROOM_DLC_1_DESCRIPTION"] = "加入全新的史詩戰役",
["SHOP_ROOM_DLC_1_TITLE"] = "【巨大的威脅】篇章",
["SHOP_ROOM_DLC_1_TOOLTIP_DESCRIPTION"] = "5個全新關卡\n新防禦塔\n新英雄\n超過10種全新的敵人\n2場小BOSS戰\n史詩級的BOSS大戰\n還有更多......",
["SHOP_ROOM_DLC_1_TOOLTIP_TITLE"] = "【巨大的威脅】篇章",
["SHOP_ROOM_DLC_2_DESCRIPTION"] = "加入全新的史詩戰役",
["SHOP_ROOM_DLC_2_TITLE"] = "【大聖遊記】篇章",
["SHOP_ROOM_MOST_POPULAR_TITLE"] = "最熱門",
["SLOT_CLOUD_DOWNLOADING"] = "正在下載...",
["SLOT_CLOUD_DOWNLOAD_FAILED"] = "無法從iCloud下載遊戲存檔！請稍後再試。",
["SLOT_CLOUD_DOWNLOAD_SUCCESSFUL"] = "下載成功。",
["SLOT_CLOUD_UPLOADING"] = "上傳中...",
["SLOT_CLOUD_UPLOAD_FAILED"] = "無法將遊戲存檔上傳到 iCloud！請稍後再試。",
["SLOT_CLOUD_UPLOAD_ICLOUD_NOT_CONFIGURED"] = "您的設備未設置 iCloud。",
["SLOT_CLOUD_UPLOAD_SUCCESSFUL"] = "上傳成功。",
["SLOT_DELETE_SLOT"] = "刪除存檔？",
["SLOT_NAME"] = "存檔",
["SLOT_NEW_GAME"] = "新遊戲",
["SOLDIER_ARBOREAN_BARRACK_NAME"] = "樹靈戰士",
["SOLDIER_ARBOREAN_SENTINELS_1_NAME"] = "巴魯",
["SOLDIER_ARBOREAN_SENTINELS_2_NAME"] = "維拉",
["SOLDIER_ARBOREAN_SENTINELS_3_NAME"] = "伊科",
["SOLDIER_ARBOREAN_SENTINELS_4_NAME"] = "哈維",
["SOLDIER_ARBOREAN_SENTINELS_5_NAME"] = "波魯克",
["SOLDIER_ARBOREAN_SENTINELS_6_NAME"] = "古爾德",
["SOLDIER_ARBOREAN_SENTINELS_7_NAME"] = "蒂娜",
["SOLDIER_ARBOREAN_SENTINELS_8_NAME"] = "烏茲基",
["SOLDIER_ARBOREAN_SENTINELS_9_NAME"] = "德盧",
["SOLDIER_DRAGON_BONE_ULTIMATE_DOG_NAME"] = "骨頭亞龍",
["SOLDIER_EARTH_HOLDER_NAME"] = "岩土戰士",
["SOLDIER_GHOST_TOWER_NAME"] = "戰魂",
["SOLDIER_HERO_BUILDER_WORKER_1_NAME"] = "鐵鎚的赫瑪爾",
["SOLDIER_HERO_BUILDER_WORKER_2_NAME"] = "工具的奧圖",
["SOLDIER_HERO_BUILDER_WORKER_3_NAME"] = "螺釘的克魯斯",
["SOLDIER_HERO_BUILDER_WORKER_4_NAME"] = "紅磚的比爾科",
["SOLDIER_HERO_BUILDER_WORKER_5_NAME"] = "填補的勞克",
["SOLDIER_HERO_BUILDER_WORKER_6_NAME"] = "尖釘的奧尼爾",
["SOLDIER_HERO_BUILDER_WORKER_7_NAME"] = "鐵鏟的霍馮斯",
["SOLDIER_HERO_BUILDER_WORKER_8_NAME"] = "木匠的伍迪",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL1_NAME"] = "樹靈衛士",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL2_NAME"] = "樹靈衛士",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL3_NAME"] = "樹靈衛士",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL1_NAME"] = "樹靈楷模",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL2_NAME"] = "樹靈楷模",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL3_NAME"] = "樹靈楷模",
["SOLDIER_HERO_SPIDER_ULTIMATE_NAME"] = "小蜘蛛",
["SOLDIER_HERO_WITCH_CAT_1_NAME"] = "柯南",
["SOLDIER_HERO_WITCH_CAT_2_NAME"] = "派派",
["SOLDIER_HERO_WITCH_CAT_3_NAME"] = "比比",
["SOLDIER_HERO_WITCH_CAT_4_NAME"] = "毛毛",
["SOLDIER_HERO_WITCH_CAT_5_NAME"] = "枇杷",
["SOLDIER_HERO_WITCH_CAT_6_NAME"] = "華生",
["SOLDIER_HERO_WITCH_CAT_7_NAME"] = "奇米",
["SOLDIER_HERO_WITCH_CAT_8_NAME"] = "絨絨",
["SOLDIER_HERO_WITCH_DECOY_NAME"] = "玩偶",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_1_NAME"] = "筍捂空",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_2_NAME"] = "孫梧孔",
["SOLDIER_ITEM_SUMMON_BLACKBURN_NAME"] = "布萊克本領主",
["SOLDIER_PALADINS_10_NAME"] = "約亞辛爵士",
["SOLDIER_PALADINS_11_NAME"] = "安德烈爵士",
["SOLDIER_PALADINS_12_NAME"] = "薩米特爵士",
["SOLDIER_PALADINS_13_NAME"] = "烏多爵士",
["SOLDIER_PALADINS_14_NAME"] = "艾瑞克爵士",
["SOLDIER_PALADINS_15_NAME"] = "布魯斯爵士",
["SOLDIER_PALADINS_16_NAME"] = "羅伯爵士",
["SOLDIER_PALADINS_17_NAME"] = "比夫爵士",
["SOLDIER_PALADINS_18_NAME"] = "鮑斯爵士",
["SOLDIER_PALADINS_1_NAME"] = "凱爵士",
["SOLDIER_PALADINS_2_NAME"] = "漢西爵士",
["SOLDIER_PALADINS_3_NAME"] = "盧卡爵士",
["SOLDIER_PALADINS_4_NAME"] = "提莫爵士",
["SOLDIER_PALADINS_5_NAME"] = "拉爾夫爵士",
["SOLDIER_PALADINS_6_NAME"] = "托比亞斯爵士",
["SOLDIER_PALADINS_7_NAME"] = "德里斯爵士",
["SOLDIER_PALADINS_8_NAME"] = "奇斯克爵士",
["SOLDIER_PALADINS_9_NAME"] = "佩斯克爵士",
["SOLDIER_PRIESTS_BARRACK_1_NAME"] = "威利",
["SOLDIER_PRIESTS_BARRACK_2_NAME"] = "哈利",
["SOLDIER_PRIESTS_BARRACK_3_NAME"] = "傑弗瑞",
["SOLDIER_PRIESTS_BARRACK_4_NAME"] = "尼可拉斯",
["SOLDIER_PRIESTS_BARRACK_5_NAME"] = "艾德",
["SOLDIER_PRIESTS_BARRACK_6_NAME"] = "霍布",
["SOLDIER_PRIESTS_BARRACK_7_NAME"] = "奧多",
["SOLDIER_PRIESTS_BARRACK_8_NAME"] = "西追",
["SOLDIER_PRIESTS_BARRACK_9_NAME"] = "哈爾",
["SOLDIER_RANDOM_10_NAME"] = "阿爾維斯",
["SOLDIER_RANDOM_11_NAME"] = "博林",
["SOLDIER_RANDOM_12_NAME"] = "哈德里安",
["SOLDIER_RANDOM_13_NAME"] = "湯瑪士",
["SOLDIER_RANDOM_14_NAME"] = "亨利",
["SOLDIER_RANDOM_15_NAME"] = "布萊斯",
["SOLDIER_RANDOM_16_NAME"] = "魯爾夫",
["SOLDIER_RANDOM_17_NAME"] = "艾利斯特",
["SOLDIER_RANDOM_18_NAME"] = "阿爾泰",
["SOLDIER_RANDOM_19_NAME"] = "西蒙",
["SOLDIER_RANDOM_1_NAME"] = "道格拉斯",
["SOLDIER_RANDOM_20_NAME"] = "艾伯特",
["SOLDIER_RANDOM_21_NAME"] = "艾爾頓",
["SOLDIER_RANDOM_22_NAME"] = "加勒特",
["SOLDIER_RANDOM_23_NAME"] = "古德溫",
["SOLDIER_RANDOM_24_NAME"] = "戈登",
["SOLDIER_RANDOM_25_NAME"] = "杰羅德",
["SOLDIER_RANDOM_26_NAME"] = "凱爾文",
["SOLDIER_RANDOM_27_NAME"] = "蘭多",
["SOLDIER_RANDOM_28_NAME"] = "麥達克斯",
["SOLDIER_RANDOM_29_NAME"] = "皮頓",
["SOLDIER_RANDOM_2_NAME"] = "丹．麥基爾",
["SOLDIER_RANDOM_30_NAME"] = "拉姆齊",
["SOLDIER_RANDOM_31_NAME"] = "雷蒙德",
["SOLDIER_RANDOM_32_NAME"] = "羅伯特",
["SOLDIER_RANDOM_33_NAME"] = "索耶",
["SOLDIER_RANDOM_34_NAME"] = "瑟拉斯",
["SOLDIER_RANDOM_35_NAME"] = "史都華",
["SOLDIER_RANDOM_36_NAME"] = "泰納",
["SOLDIER_RANDOM_37_NAME"] = "厄謝爾",
["SOLDIER_RANDOM_38_NAME"] = "華勒斯",
["SOLDIER_RANDOM_39_NAME"] = "衛斯理",
["SOLDIER_RANDOM_3_NAME"] = "詹姆士．李",
["SOLDIER_RANDOM_40_NAME"] = "威拉德",
["SOLDIER_RANDOM_4_NAME"] = "賈爾．約翰遜",
["SOLDIER_RANDOM_5_NAME"] = "菲爾",
["SOLDIER_RANDOM_6_NAME"] = "羅賓",
["SOLDIER_RANDOM_7_NAME"] = "威廉",
["SOLDIER_RANDOM_8_NAME"] = "馬汀",
["SOLDIER_RANDOM_9_NAME"] = "亞瑟",
["SOLDIER_REINFORCEMENTS_F_1_NAME"] = "阿泰娜",
["SOLDIER_REINFORCEMENTS_F_2_NAME"] = "莫西爾",
["SOLDIER_REINFORCEMENTS_F_3_NAME"] = "古麗卡",
["SOLDIER_REINFORCEMENTS_F_4_NAME"] = "蘿卡絲",
["SOLDIER_REINFORCEMENTS_M_10_NAME"] = "波奇",
["SOLDIER_REINFORCEMENTS_M_1_NAME"] = "加賓尼",
["SOLDIER_REINFORCEMENTS_M_2_NAME"] = "奧貝爾",
["SOLDIER_REINFORCEMENTS_M_3_NAME"] = "肯特",
["SOLDIER_REINFORCEMENTS_M_4_NAME"] = "詹達斯",
["SOLDIER_REINFORCEMENTS_M_5_NAME"] = "賈洛克",
["SOLDIER_REINFORCEMENTS_M_6_NAME"] = "阿斯壯",
["SOLDIER_REINFORCEMENTS_M_7_NAME"] = "布伊格爾",
["SOLDIER_REINFORCEMENTS_M_8_NAME"] = "克萊恩",
["SOLDIER_REINFORCEMENTS_M_9_NAME"] = "馬格斯",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_1_NAME"] = "丹琴",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_2_NAME"] = "瑟米絲",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_3_NAME"] = "安杜瑞絲",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_4_NAME"] = "唐蒲森",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_5_NAME"] = "泰勒",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_1_NAME"] = "麥卡尼",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_2_NAME"] = "麥凱倫",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_3_NAME"] = "霍普金",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_4_NAME"] = "凱恩",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_5_NAME"] = "金斯利",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_10_NAME"] = "毒蛇",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_1_NAME"] = "尖牙",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_2_NAME"] = "利刃",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_3_NAME"] = "鉤爪",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_4_NAME"] = "利爪",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_5_NAME"] = "刀鋒",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_6_NAME"] = "小刀",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_7_NAME"] = "鐮刀",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_8_NAME"] = "匕首",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_9_NAME"] = "毒蠍",
["SOLDIER_REINFORCEMENTS_SPECIAL_DARK_ARMY_1_NAME"] = "暗影喚鴉者",
["SOLDIER_REINFORCEMENTS_SPECIAL_LINIREA_1_NAME"] = "騎士楷模",
["SOLDIER_STAGE_10_YMCA_BIKER_NAME"] = "格倫",
["SOLDIER_STAGE_10_YMCA_CONSTRUCTOR_NAME"] = "大衛",
["SOLDIER_STAGE_10_YMCA_INDIO_NAME"] = "菲利佩",
["SOLDIER_STAGE_10_YMCA_POLICIA_NAME"] = "維克多",
["SOLDIER_STAGE_15_DENAS_NAME"] = "國王迪納斯",
["SOLDIER_TOWER_DARK_ELF_1_NAME"] = "費勒倫",
["SOLDIER_TOWER_DARK_ELF_2_NAME"] = "菲亞莉",
["SOLDIER_TOWER_DARK_ELF_3_NAME"] = "葛瑞娜",
["SOLDIER_TOWER_DARK_ELF_4_NAME"] = "潔麗絲",
["SOLDIER_TOWER_DARK_ELF_5_NAME"] = "索林札",
["SOLDIER_TOWER_DARK_ELF_6_NAME"] = "泰布恩",
["SOLDIER_TOWER_DARK_ELF_7_NAME"] = "維爾娜",
["SOLDIER_TOWER_DARK_ELF_8_NAME"] = "京",
["SOLDIER_TOWER_DARK_ELF_9_NAME"] = "伊萊拉",
["SOLDIER_TOWER_DWARF_10_NAME"] = "芭比",
["SOLDIER_TOWER_DWARF_1_NAME"] = "皮皮",
["SOLDIER_TOWER_DWARF_2_NAME"] = "金妮",
["SOLDIER_TOWER_DWARF_3_NAME"] = "梅莉",
["SOLDIER_TOWER_DWARF_4_NAME"] = "洛莉",
["SOLDIER_TOWER_DWARF_5_NAME"] = "塔莉",
["SOLDIER_TOWER_DWARF_6_NAME"] = "丹妮",
["SOLDIER_TOWER_DWARF_7_NAME"] = "蓋蒂",
["SOLDIER_TOWER_DWARF_8_NAME"] = "達菲",
["SOLDIER_TOWER_DWARF_9_NAME"] = "比比",
["SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "伊蘭迪爾",
["SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "帕克",
["SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "達斯",
["SOLDIER_TOWER_ELVEN_BARRACK_4_NAME"] = "卡斯托爾",
["SOLDIER_TOWER_ELVEN_BARRACK_5_NAME"] = "埃里克",
["SOLDIER_TOWER_ELVEN_BARRACK_6_NAME"] = "伊拉薩",
["SOLDIER_TOWER_NECROMANCER_SKELETON_GOLEM_NAME"] = "骨骸魔像",
["SOLDIER_TOWER_NECROMANCER_SKELETON_NAME"] = "骷髏",
["SOLDIER_TOWER_PANDAS_FEMALE_1_NAME"] = "小雁",
["SOLDIER_TOWER_PANDAS_FEMALE_2_NAME"] = "清照",
["SOLDIER_TOWER_PANDAS_FEMALE_3_NAME"] = "小慧",
["SOLDIER_TOWER_PANDAS_FEMALE_4_NAME"] = "愛玲",
["SOLDIER_TOWER_PANDAS_MALE_1_NAME"] = "阿祖",
["SOLDIER_TOWER_PANDAS_MALE_2_NAME"] = "錢老大",
["SOLDIER_TOWER_PANDAS_MALE_3_NAME"] = "雪芹",
["SOLDIER_TOWER_PANDAS_MALE_4_NAME"] = "耐庵",
["SOLDIER_TOWER_PANDAS_MALE_5_NAME"] = "老勛",
["SOLDIER_TOWER_PANDAS_MALE_6_NAME"] = "行健",
["SOLDIER_TOWER_PANDAS_MALE_7_NAME"] = "阿偉",
["SOLDIER_TOWER_PANDAS_MALE_8_NAME"] = "老陳",
["SOLDIER_TOWER_ROCKET_GUNNERS_10_NAME"] = "佛特斯",
["SOLDIER_TOWER_ROCKET_GUNNERS_1_NAME"] = "阿克瑟爾",
["SOLDIER_TOWER_ROCKET_GUNNERS_2_NAME"] = "蘿絲",
["SOLDIER_TOWER_ROCKET_GUNNERS_3_NAME"] = "斯拉許",
["SOLDIER_TOWER_ROCKET_GUNNERS_4_NAME"] = "哈德遜",
["SOLDIER_TOWER_ROCKET_GUNNERS_5_NAME"] = "伊茲",
["SOLDIER_TOWER_ROCKET_GUNNERS_6_NAME"] = "道夫",
["SOLDIER_TOWER_ROCKET_GUNNERS_7_NAME"] = "阿德勒",
["SOLDIER_TOWER_ROCKET_GUNNERS_8_NAME"] = "迪茲",
["SOLDIER_TOWER_ROCKET_GUNNERS_9_NAME"] = "費勒",
["SOLDIER_ZHU_APPRENTICE_NAME"] = "豬八戒",
["SPECIAL_ARBOREAN_BARRACK_DESCRIPTION"] = "召喚3名樹靈戰士沿路徑戰鬥。",
["SPECIAL_ARBOREAN_BARRACK_NAME"] = "樹靈村民",
["SPECIAL_ARBOREAN_HONEY_DESCRIPTION"] = "馭蜂使大顯神威，號令蜂群用黏黏的蜂蜜緩速和螫傷敵人吧！",
["SPECIAL_ARBOREAN_HONEY_NAME"] = "樹靈馭蜂使",
["SPECIAL_ARBOREAN_OLDTREE_DESCRIPTION"] = "性情暴躁的古木，會讓樹幹滾落摧毀路徑上的敵人。",
["SPECIAL_ARBOREAN_OLDTREE_NAME"] = "古樹",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_DESCRIPTION"] = "身手矯健的森林守護者。",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_NAME"] = "樹靈棘矛手",
["SPECIAL_PRIESTS_SOLDIERS_DESCRIPTION"] = "痛改前非的教徒，在死亡時化為怪物。",
["SPECIAL_PRIESTS_SOLDIERS_NAME"] = "失明的邪教徒",
["SPECIAL_REPAIR_HOLDER_DRAGON_DESCRIPTION"] = "撲滅火焰，立即解開防禦塔的束縛。",
["SPECIAL_REPAIR_HOLDER_DRAGON_NAME"] = "焰火吞沒",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_DESCRIPTION"] = "增加防禦塔生成單位的生命值。\n定期生成2名岩土戰士。",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_NAME"] = "五行建地：土",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_DESCRIPTION"] = "為建造的塔增加額外傷害。\n偶爾能即殺一名敵人。",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_NAME"] = "五行建地：火",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_DESCRIPTION"] = "降低建造費用。\n對敵人造成傷害可獲得金錢。",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_NAME"] = "五行建地：金",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_DESCRIPTION"] = "持續治療周圍友方單位。\n能將敵人沿道路往回傳送。",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_NAME"] = "五行建地：水",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_DESCRIPTION"] = "為建造的塔增加額外射程。\n偶爾生成短暫存在的樹根緩速敵人。",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_NAME"] = "五行建地：木",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_DESCRIPTION"] = "清除障礙物，騰出此戰略位置。",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_NAME"] = "障礙物",
["SPECIAL_REPAIR_HOLDER_SPIDERS_DESCRIPTION"] = "解除纏繞的蜘蛛網啟用此戰略位置。",
["SPECIAL_REPAIR_HOLDER_SPIDERS_NAME"] = "蛛網纏繞的建地",
["SPECIAL_REPAIR_OVERSEER_DESCRIPTION"] = "擊退觸手以解鎖此戰略位置。",
["SPECIAL_REPAIR_OVERSEER_NAME"] = "觸手",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_DESCRIPTION"] = "雇用1名精靈傭兵協助戰鬥。每10秒重生。",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "精靈傭兵 I",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_DESCRIPTION"] = "雇用2名精靈傭兵協助戰鬥。每10秒重生。",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "精靈傭兵 II",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_DESCRIPTION"] = "雇用3名精靈傭兵協助戰鬥。每10秒重生。",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "精靈傭兵 III",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_1"] = "施放數道魔法摧毀魔蒂婭絲的幻象，並讓她在幾秒內無法施放該技能。",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_2"] = "召喚2名惡魔守衛，沿路行進與敵人戰鬥。",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_3"] = "束縛迪納斯，阻止其移動和攻擊。",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_1"] = "靈魂衝擊",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_2"] = "煉獄守衛",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_3"] = "魔法枷鎖",
["START"] = "開始",
["START BATTLE!"] = "開始戰鬥！",
["START HERE!"] = "從這裡開始！",
["STRATEGY BASICS!"] = "戰略的基本！",
["Select"] = "選擇",
["Select and train abilities"] = "選擇並訓練技能",
["Select by clicking on the portrait or hero unit. Hotkey: space bar"] = "按一下傳送門或英雄單位來選擇。快速鍵：空白鍵",
["Select hero"] = "選擇英雄",
["Selected"] = "已選擇",
["Sell Tower"] = "出售防禦塔",
["Sell this tower and get a %s GP refund."] = "出售該防禦塔並獲得%s金幣。",
["Short"] = "短",
["Shows level, health and experience."] = "顯示等級、生命值和經驗值。",
["Skills"] = "技能",
["Skip this!"] = "跳過！",
["Slow"] = "慢",
["Special abilities"] = "特殊技能",
["Support your soldiers with ranged towers!"] = "用遠程防禦塔支援你的士兵！",
["Survival mode!"] = "生存模式！",
["TAP_TO_START"] = "點擊開始",
["TAUNT_BOSS_PIG_FROM_POOL_0001"] = "換誰要像豬一樣慘叫啊？",
["TAUNT_BOSS_PIG_FROM_POOL_0002"] = "有種再說一次「培根」。敢不敢啊！",
["TAUNT_BOSS_PIG_FROM_POOL_0003"] = "小的們，我們又有人肉吃了！",
["TAUNT_BOSS_PIG_FROM_POOL_0004"] = "快點！我餓了。",
["TAUNT_BOSS_PIG_FROM_POOL_0005"] = "我要看著你死。",
["TAUNT_BOSS_PIG_FROM_POOL_0006"] = "我知道，我是個「壞心腸」。",
["TAUNT_LVL30_BOSS_ABILITY_01"] = "開宴吧，我的孩子們！",
["TAUNT_LVL30_BOSS_ABILITY_02"] = "掛緊了！姆哇哈哈哈！",
["TAUNT_LVL30_BOSS_ABILITY_03"] = "為了吾教！",
["TAUNT_LVL30_BOSS_ABILITY_04"] = "美味盛宴，人人有份！",
["TAUNT_LVL30_BOSS_ABILITY_05"] = "我的蜘蛛感知在發麻！",
["TAUNT_LVL30_BOSS_ABILITY_06"] = "向女王下跪啊，同盟！",
["TAUNT_LVL30_BOSS_ABILITY_07"] = "我的地盤，由我主宰！",
["TAUNT_LVL30_BOSS_ABILITY_08"] = "沒人能活著離開我的蛛網！",
["TAUNT_LVL30_BOSS_ABILITY_09"] = "死吧，人形瘟疫！",
["TAUNT_LVL30_BOSS_ABILITY_10"] = "讓我替你們操絲引線！",
["TAUNT_LVL30_BOSS_ABILITY_11"] = "殺光他們！",
["TAUNT_LVL30_BOSS_INTRO_01"] = "終於！殺害妹妹們的兇手現身了……",
["TAUNT_LVL30_BOSS_INTRO_02"] = "塞羅葛斯、瑪格袒斯...交給我吧...",
["TAUNT_LVL30_BOSS_INTRO_03"] = "你們通通要跪地俯首，成為蜘蛛女王的信徒！",
["TAUNT_LVL30_BOSS_PREFIGHT_01"] = "夠了……",
["TAUNT_LVL30_BOSS_PREFIGHT_02"] = "你們不過是一群落入女王網中...",
["TAUNT_LVL30_BOSS_PREFIGHT_03"] = "不值一提的小蟲子罷了！",
["TAUNT_LVL32_BOSS_ABILITY_01"] = "呆頭！我執掌的可是神火——三昧真火！",
["TAUNT_LVL32_BOSS_ABILITY_02"] = "大鬧天宮？那就讓小聖焚毀天宮！",
["TAUNT_LVL32_BOSS_ABILITY_03"] = "畏懼最純粹的真火吧！",
["TAUNT_LVL32_BOSS_ABILITY_04"] = "燒盡肉身、燒盡靈魂！",
["TAUNT_LVL32_BOSS_FIGHT_01"] = "紅孩熾焰，生生不熄！",
["TAUNT_LVL32_BOSS_FINAL_01"] = "紅焰被撲滅了啊...\n但我的龍還沒倒下呢…",
["TAUNT_LVL32_BOSS_INTRO_01"] = "這麼說，你有大軍在手？",
["TAUNT_LVL32_BOSS_INTRO_02"] = "但小聖有巨龍伴我！哈哈哈哈！",
["TAUNT_LVL32_BOSS_PREFIGHT_01"] = "夠了！該輪到小聖給你好看的時候了！",
["TAUNT_LVL32_BOSS_PREFIGHT_02"] = "見證我的真實樣貌吧！",
["TAUNT_LVL34_BOSS_BOSSFIGHT_01"] = "好極了，計畫如下：鐵扇公主、公主...更多的鐵扇公主！",
["TAUNT_LVL34_BOSS_DEATH_01"] = "豈會如此...無妨，我夫君會讓你們好看的...",
["TAUNT_LVL34_BOSS_INTRO_01"] = "一幫潑猴！欺負完我兒子還有膽到老娘這來？",
["TAUNT_LVL34_BOSS_WAVES_01"] = "嘗嘗老娘的厲害，無禮之徒！",
["TAUNT_LVL34_BOSS_WAVES_02"] = "末日將至！",
["TAUNT_STAGE02_RAELYN_0001"] = "來吧！",
["TAUNT_STAGE02_VEZNAN_0001"] = "他們來了。就由我來拯救你們這可憐的雜牌軍……",
["TAUNT_STAGE02_VEZNAN_0002"] = "…我是說，我手下的精銳會有人來拯救你們的。哈！",
["TAUNT_STAGE02_VEZNAN_0003"] = "哈哈哈！",
["TAUNT_STAGE06_BOSS_PIG_PREBATTLE_0001"] = "算了…我自己上。",
["TAUNT_STAGE06_BOSS_PIG_RESPONSE_0001"] = "放鬆，沒什麼大不了的。",
["TAUNT_STAGE06_CULTIST_GREETING_0001"] = "看來你過得挺舒適的…",
["TAUNT_STAGE06_CULTIST_GREETING_0002"] = "…你最好能給我信守承諾。",
["TAUNT_STAGE11_CULTIST_LEADER_0001"] = "真虧你能來到這裡…",
["TAUNT_STAGE11_CULTIST_LEADER_0002"] = "…但你無法改變「必然」！",
["TAUNT_STAGE11_CULTIST_LEADER_0003"] = "夠了！！！",
["TAUNT_STAGE11_CULTIST_LEADER_0004"] = "來瞧瞧現在是誰要「俯首稱臣」！",
["TAUNT_STAGE11_CULTIST_LEADER_0005"] = "啊啊…這可還沒完！",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0001"] = "新世界正在等著我們。",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0002"] = "你低估了我的實力。",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0003"] = "奧克魯斯，波克魯斯！",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0004"] = "傾聽偉大的「必然」吧！",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0005"] = "我很壞嗎？當然啊！",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0006"] = "保佑我吧！全觀全視的君主啊！",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0001"] = "你們的氣數已盡！",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0002"] = "我已獲得「啟明」！",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0003"] = "跟我的虛空朋友們打聲招呼吧！",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0004"] = "奧克魯斯，波克魯斯！",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0005"] = "可悲的弱小雜魚！",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0006"] = "保佑我吧！全觀全視的君主啊！",
["TAUNT_STAGE11_VEZNAN_0001"] = "迪納斯，老朋友，好久不見了！",
["TAUNT_STAGE15_CULTIST_0001"] = "就快了…我能感覺到那位大人就要甦醒了！",
["TAUNT_STAGE15_CULTIST_0002"] = "新的時代即將來臨。你們無法改變「必然」！",
["TAUNT_STAGE15_CULTIST_0003"] = "啊啊…你們的同盟確實有一套。",
["TAUNT_STAGE15_CULTIST_0004"] = "但我會讓你們看看什麼是真正的力量！",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0001"] = "愚蠢至極！你不過是來送死罷了。",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0002"] = "在祂的凝視下臣服吧！",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0003"] = "你們終將成為真正的信徒。",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0004"] = "管你們結成了什麼同盟，全都死定了！",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0005"] = "虛空中沒有生命。只有死亡！",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0006"] = "別再浪費我的時間了！",
["TAUNT_STAGE15_DENAS_0001"] = "算我一份，我可有仇未報！",
["TAUNT_STAGE16_DENAS_AFTER_BOSSFIGHT_0001"] = "看來這回，你沒看見啊？",
["TAUNT_STAGE18_ERIDAN_FIGHT_0001"] = "今夜鮮血淋漓。",
["TAUNT_STAGE18_ERIDAN_FIGHT_0002"] = "我們信仰艾納妮。",
["TAUNT_STAGE18_ERIDAN_FIGHT_0003"] = "律恆皮鐵！",
["TAUNT_STAGE18_ERIDAN_FIGHT_0004"] = "我好像不會失手。",
["TAUNT_STAGE18_ERIDAN_FIGHT_0005"] = "雅瑞希爾終將勝利！",
["TAUNT_STAGE18_ERIDAN_FIGHT_0006"] = "一般的遊俠可沒這種身手！",
["TAUNT_STAGE18_ERIDAN_FIGHT_0007"] = "比分還算數嗎？",
["TAUNT_STAGE18_ERIDAN_FIGHT_0008"] = "放馬過來！",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0001"] = "我的弓與你同在！",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0002"] = "迅捷行動！",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0003"] = "站好陣形！",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0004"] = "保持戒備！",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0001"] = "熱身到此為止！",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0002"] = "看來你確實是個麻煩人物......",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0003"] = "但真正的大戰現在才開始！",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0001"] = "我主宰了所有的靈魂！",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0002"] = "精靈族必將東山再起。",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0003"] = "我甚至掌控了......死亡！",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0004"] = "自古流傳的邪惡力量！",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0005"] = "為我的墓園之子恐懼吧！",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0006"] = "我會奪回一族的榮光。",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0001"] = "噢，實力堅強的盟軍來訪。",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0002"] = "該是嶄露真容的時候了！",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0003"] = "讓你們瞧瞧死亡的力量！",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0001"] = "終獲自由...本鱷吾乃...",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0002"] = "噬界災鱷！！！！！",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0001"] = "夠了，少來壞我的好事！",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0002"] = "就讓詭鬚大人來教教你們禮數。",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0003"] = "全員登艦，哇哈哈哈！",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0001"] = "一群學不懂禮貌的蠢蛋！",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0002"] = "你們永遠別想逮到我，哈哈哈！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "不！這還沒完…",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "我的天！！！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0001"] = "你們根本不是這支大軍的對手！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0002"] = "詭鬚豈會身處危險。",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0003"] = "詭鬚 我 就是危險！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0004"] = "瘋子能做得到這些嗎？",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0005"] = "世界會向詭鬚大人俯首稱臣！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0001"] = "詭鬚已經忍無可忍了。",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0002"] = "來，等等讓你看點好康的！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0003"] = "詭鬚需要的幫手？更多的詭鬚！",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0004"] = "你要讓我等多久？！",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "該死的，自找麻煩，多管閒事！",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "讓我教教你們，不要輕易惹火...",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0003"] = "...至尊矮人！",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0001"] = "不管你們摧毀多少，我都能做出更多的複製體。",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0002"] = "想把事情做好，就只有自己才信得過！",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0003"] = "噢！詭鬚，你真是個天才！",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0004"] = "壞我好事，可別想輕易走人！",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0005"] = "你們就這點能耐？",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0006"] = "以為自己能勝過我的造物？",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0001"] = "你們還沒嚐夠詭鬚大人的厲害啊...",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0002"] = "...現在還想要來挑戰我詭鬚本尊？",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0003"] = "那就儘管來試試看吧。",
["TAUNT_TUTORIAL_ARBOREAN_ALL_0001"] = "加油！您樹我們的希望！",
["TAUNT_TUTORIAL_ARBOREAN_BARRACK_0001"] = "來，在這裡建造兵營吧！",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "肢廉",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "觸手哈利",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "觸手傑弗瑞",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "觸可拉撕",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "角艾德手",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "霍肢布",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "角蜀奧多",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "肢追",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "哈爾肢",
["TERMS_OF_SERVICE_LINK"] = "使用條款",
["TIP!"] = "提示！",
["TIP_1"] = "點擊敵人資訊，與觀看提示來了解如何對付他們。",
["TIP_10"] = "飛行敵人無法阻擋，且不會成為大部分砲台的攻擊對象。",
["TIP_11"] = "您可以在戰鬥中使用商店裡的強大道具。它們能大大幫助您扭轉困境！",
["TIP_2"] = "魔法傷害對具有護甲的敵人非常有效。",
["TIP_3"] = "升級兵營時，會立刻產出新的士兵，來替換憔悴的戰友。",
["TIP_4"] = "提前召喚敵人可獲得額外金幣，並降低法術的冷卻時間。",
["TIP_5"] = "您可以調整兵營集結點，根據策略把士兵移動到有利的位置。",
["TIP_6"] = "注意下一波敵人的旗幟 ，點擊一下就能看到下波敵軍的資訊。做好準備吧！",
["TIP_7"] = "雖說可能無法成為攻擊目標，但絕大多數範圍攻擊都可以擊中飛行的敵人。",
["TIP_8"] = "有時出售目前用不上的防禦塔，可以幫助您獲得足夠的資金度過難關。",
["TIP_9"] = "升級防禦塔通常比再建造相同的防禦塔更好。",
["TIP_ALERT_ICON"] = "提示",
["TIP_TITLE"] = "提示：",
["TOWER_ARBOREAN_EMISSARY_1_DESCRIPTION"] = "樹靈族運用強力的自然魔法弱化敵人。",
["TOWER_ARBOREAN_EMISSARY_1_NAME"] = "樹靈使者 I",
["TOWER_ARBOREAN_EMISSARY_2_DESCRIPTION"] = "樹靈族運用強力的自然魔法弱化敵人。",
["TOWER_ARBOREAN_EMISSARY_2_NAME"] = "樹靈使者 II",
["TOWER_ARBOREAN_EMISSARY_3_DESCRIPTION"] = "樹靈族運用強力的自然魔法弱化敵人。",
["TOWER_ARBOREAN_EMISSARY_3_NAME"] = "樹靈使者 III",
["TOWER_ARBOREAN_EMISSARY_4_DESCRIPTION"] = "樹靈族運用強力的自然魔法弱化敵人。",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_DESCRIPTION"] = "召喚仙靈治療區域內的友軍，每秒恢復%$towers.arborean_emissary.gift_of_nature.s_heal[1]%$生命值，持續%$towers.arborean_emissary.gift_of_nature.duration[1]%$秒。",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_NAME"] = "自然贈禮",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_DESCRIPTION"] = "召喚仙靈治療區域內的友軍，每秒恢復%$towers.arborean_emissary.gift_of_nature.s_heal[2]%$生命值，持續 %$towers.arborean_emissary.gift_of_nature.duration[2]%$秒。",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_NAME"] = "自然贈禮",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_DESCRIPTION"] = "召喚仙靈治療區域內的友軍，每秒恢復%$towers.arborean_emissary.gift_of_nature.s_heal[3]%$生命值，持續%$towers.arborean_emissary.gift_of_nature.duration[3]%$秒。",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_NAME"] = "自然贈禮",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NAME"] = "自然贈禮",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NOTE"] = "切勿與綠林為敵。",
["TOWER_ARBOREAN_EMISSARY_4_NAME"] = "樹靈使者 IV",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_DESCRIPTION"] = "沿路扎下%$towers.arborean_emissary.wave_of_roots.max_targets[1]%$道樹根，對敵人造成%$towers.arborean_emissary.wave_of_roots.s_damage[1]%$真實傷害，並暈眩%$towers.arborean_emissary.wave_of_roots.mod_duration[1]%$秒。",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_NAME"] = "荊棘纏縛",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_DESCRIPTION"] = "沿路扎下%$towers.arborean_emissary.wave_of_roots.max_targets[2]%$道樹根，對敵人造成%$towers.arborean_emissary.wave_of_roots.s_damage[2]%$真實傷害，並暈眩%$towers.arborean_emissary.wave_of_roots.mod_duration[2]%$秒。",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_NAME"] = "荊棘纏縛",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_DESCRIPTION"] = "沿路扎下%$towers.arborean_emissary.wave_of_roots.max_targets[3]%$道樹根，對敵人造成%$towers.arborean_emissary.wave_of_roots.s_damage[3]%$真實傷害，並暈眩%$towers.arborean_emissary.wave_of_roots.mod_duration[3]%$秒。",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_NAME"] = "荊棘纏縛",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NAME"] = "荊棘纏縛",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NOTE"] = "當心腳下。",
["TOWER_ARBOREAN_EMISSARY_DESC"] = "當友善的樹靈族遭遇威脅時，他們會使用魔法標記並削弱敵人。",
["TOWER_ARBOREAN_EMISSARY_NAME"] = "樹靈使者",
["TOWER_ARBOREAN_SENTINELS_DESCRIPTION"] = "身手矯健的森林守護者。",
["TOWER_ARBOREAN_SENTINELS_NAME"] = "樹靈棘矛手",
["TOWER_ARCANE_WIZARD_1_DESCRIPTION"] = "精通魔法的巫师，隨時都能從容應戰。",
["TOWER_ARCANE_WIZARD_1_NAME"] = "奧術巫師 I",
["TOWER_ARCANE_WIZARD_2_DESCRIPTION"] = "精通魔法的巫师，隨時都能從容應戰。",
["TOWER_ARCANE_WIZARD_2_NAME"] = "奧術巫師 II",
["TOWER_ARCANE_WIZARD_3_DESCRIPTION"] = "精通魔法的巫师，隨時都能從容應戰。",
["TOWER_ARCANE_WIZARD_3_NAME"] = "奧術巫師 III",
["TOWER_ARCANE_WIZARD_4_DESCRIPTION"] = "精通魔法的巫师，隨時都能從容應戰。",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_DESCRIPTION"] = "發射一道能瞬間殺死目標的光線。BOSS和小BOSS改為承受%$towers.arcane_wizard.disintegrate.boss_damage[1]%$魔法傷害。",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_NAME"] = "分解",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_DESCRIPTION"] = "冷卻時間減少至%$towers.arcane_wizard.disintegrate.cooldown[2]%$秒。對BOSS和小BOSS變為%$towers.arcane_wizard.disintegrate.boss_damage[2]%$魔法傷害。",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_NAME"] = "分解",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_DESCRIPTION"] = "冷卻時間減少至%$towers.arcane_wizard.disintegrate.cooldown[3]%$秒。對BOSS和小BOSS變為%$towers.arcane_wizard.disintegrate.boss_damage[3]%$魔法傷害。",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_NAME"] = "分解",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NAME"] = "分解",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NOTE"] = "塵歸塵。",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_DESCRIPTION"] = "提升周圍塔的傷害%$towers.arcane_wizard.empowerment.s_damage_factor[1]%$%。",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_NAME"] = "賦能",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_DESCRIPTION"] = "提升周圍塔的傷害%$towers.arcane_wizard.empowerment.s_damage_factor[2]%$%。",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_NAME"] = "賦能",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_DESCRIPTION"] = "提升周圍塔的傷害%$towers.arcane_wizard.empowerment.s_damage_factor[3]%$%。",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_NAME"] = "賦能",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NAME"] = "賦能",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NOTE"] = "無盡之力。",
["TOWER_ARCANE_WIZARD_4_NAME"] = "奧術巫師 IV",
["TOWER_ARCANE_WIZARD_DESC"] = "運用純粹的魔力，利尼維亞的巫師擁有徹底毀滅敵人的強大力量。",
["TOWER_ARCANE_WIZARD_NAME"] = "奧術巫師",
["TOWER_BALLISTA_1_DESCRIPTION"] = "綠皮一族強大的新戰爭設備，至今還沒崩塌真是個奇蹟。",
["TOWER_BALLISTA_1_NAME"] = "巨弩哨站 I",
["TOWER_BALLISTA_2_DESCRIPTION"] = "綠皮一族強大的新戰爭設備，至今還沒崩塌真是個奇蹟。",
["TOWER_BALLISTA_2_NAME"] = "巨弩哨站 II",
["TOWER_BALLISTA_3_DESCRIPTION"] = "綠皮一族強大的新戰爭設備，至今還沒崩塌真是個奇蹟。",
["TOWER_BALLISTA_3_NAME"] = "巨弩哨站 III",
["TOWER_BALLISTA_4_DESCRIPTION"] = "綠皮一族強大的新戰爭設備，至今還沒崩塌真是個奇蹟。",
["TOWER_BALLISTA_4_NAME"] = "巨弩哨站 IV",
["TOWER_BALLISTA_4_SKILL_BOMB_1_DESCRIPTION"] = "以長距離發射一枚由廢料製成的炸彈，造成%$towers.ballista.skill_bomb.damage_min[1]%$-%$towers.ballista.skill_bomb.damage_max[1]%$物理傷害。使敵人緩速%$towers.ballista.skill_bomb.duration[1]%$秒。",
["TOWER_BALLISTA_4_SKILL_BOMB_1_NAME"] = "廢鐵炸彈",
["TOWER_BALLISTA_4_SKILL_BOMB_2_DESCRIPTION"] = "廢鐵炸彈造成%$towers.ballista.skill_bomb.damage_min[2]%$-%$towers.ballista.skill_bomb.damage_max[2]%$物理傷害，緩速持續%$towers.ballista.skill_bomb.duration[1]%$秒。",
["TOWER_BALLISTA_4_SKILL_BOMB_2_NAME"] = "廢鐵炸彈",
["TOWER_BALLISTA_4_SKILL_BOMB_3_DESCRIPTION"] = "廢鐵炸彈造成%$towers.ballista.skill_bomb.damage_min[3]%$-%$towers.ballista.skill_bomb.damage_max[3]%$物理傷害，緩速持續%$towers.ballista.skill_bomb.duration[1]%$秒。",
["TOWER_BALLISTA_4_SKILL_BOMB_3_NAME"] = "廢鐵炸彈",
["TOWER_BALLISTA_4_SKILL_BOMB_NOTE"] = "注意前方！",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_DESCRIPTION"] = "最後一發攻擊的傷害量提高%$towers.ballista.skill_final_shot.s_damage_factor[1]%$%，並對目標造成%$towers.ballista.skill_final_shot.s_stun%$秒暈眩。",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_NAME"] = "最後一釘",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_DESCRIPTION"] = "最後一發攻擊的傷害量提高%$towers.ballista.skill_final_shot.s_damage_factor[2]%$%，並對目標造成%$towers.ballista.skill_final_shot.s_stun%$秒暈眩。",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_NAME"] = "最後一釘",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_DESCRIPTION"] = "最後一發攻擊的傷害量提高%$towers.ballista.skill_final_shot.s_damage_factor[3]%$%，並對目標造成%$towers.ballista.skill_final_shot.s_stun%$秒暈眩。",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_NAME"] = "最後一釘",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_NOTE"] = "這可是壓箱寶的一發，小子！",
["TOWER_BALLISTA_DESC"] = "對戰爭極為狂熱的哥布林一族，為省去張弓射箭之力而研發了這項設備。",
["TOWER_BALLISTA_NAME"] = "巨弩哨站",
["TOWER_BARREL_1_DESCRIPTION"] = "北境民族的釀酒是能對抗大批敵人的強大武器。",
["TOWER_BARREL_1_NAME"] = "釀酒戰匠 I",
["TOWER_BARREL_2_DESCRIPTION"] = "北境民族的釀酒是能對抗大批敵人的強大武器。",
["TOWER_BARREL_2_NAME"] = "釀酒戰匠 II",
["TOWER_BARREL_3_DESCRIPTION"] = "北境民族的釀酒是能對抗大批敵人的強大武器。",
["TOWER_BARREL_3_NAME"] = "釀酒戰匠 III",
["TOWER_BARREL_4_DESCRIPTION"] = "北境民族的釀酒是能對抗大批敵人的強大武器。",
["TOWER_BARREL_4_NAME"] = "釀酒戰匠 IV",
["TOWER_BARREL_4_SKILL_BARREL_1_DESCRIPTION"] = "投擲一個有毒的酒桶，造成%$towers.barrel.skill_barrel.explosion.damage_min[1]%$-%$towers.barrel.skill_barrel.explosion.damage_max[1]%$物理傷害。酒桶留下毒藥每秒給予%$towers.barrel.skill_barrel.poison.s_damage%$真實傷害，持續%$towers.barrel.skill_barrel.poison.duration%$秒。",
["TOWER_BARREL_4_SKILL_BARREL_1_NAME"] = "這桶壞了",
["TOWER_BARREL_4_SKILL_BARREL_2_DESCRIPTION"] = "毒酒桶爆炸造成%$towers.barrel.skill_barrel.explosion.damage_min[2]%$-%$towers.barrel.skill_barrel.explosion.damage_max[2]%$物理傷害。毒藥則會每秒給予%$towers.barrel.skill_barrel.poison.s_damage%$真實傷害，持續%$towers.barrel.skill_barrel.poison.duration%$秒。",
["TOWER_BARREL_4_SKILL_BARREL_2_NAME"] = "這桶壞了",
["TOWER_BARREL_4_SKILL_BARREL_3_DESCRIPTION"] = "毒藥桶爆炸造成%$towers.barrel.skill_barrel.explosion.damage_min[3]%$-%$towers.barrel.skill_barrel.explosion.damage_max[3]%$物理傷害。毒藥則會每秒給予%$towers.barrel.skill_barrel.poison.s_damage%$真實傷害，持續%$towers.barrel.skill_barrel.poison.duration%$秒。",
["TOWER_BARREL_4_SKILL_BARREL_3_NAME"] = "這桶壞了",
["TOWER_BARREL_4_SKILL_BARREL_NOTE"] = "勇者專屬！",
["TOWER_BARREL_4_SKILL_WARRIOR_1_DESCRIPTION"] = "召喚一名得到強化的戰士在路徑上戰鬥。他擁有%$towers.barrel.skill_warrior.entity.hp_max[1]%$的生命值，並造成%$towers.barrel.skill_warrior.entity.damage_min[1]%$-%$towers.barrel.skill_warrior.entity.damage_max[1]%$物理傷害。",
["TOWER_BARREL_4_SKILL_WARRIOR_1_NAME"] = "神奇藥酒",
["TOWER_BARREL_4_SKILL_WARRIOR_2_DESCRIPTION"] = "戰士現在擁有%$towers.barrel.skill_warrior.entity.hp_max[2]%$的生命值，並造成%$towers.barrel.skill_warrior.entity.damage_min[2]%$-%$towers.barrel.skill_warrior.entity.damage_max[2]%$物理傷害。",
["TOWER_BARREL_4_SKILL_WARRIOR_2_NAME"] = "神奇藥酒",
["TOWER_BARREL_4_SKILL_WARRIOR_3_DESCRIPTION"] = "戰士現在擁有%$towers.barrel.skill_warrior.entity.hp_max[3]%$的生命值，並造成%$towers.barrel.skill_warrior.entity.damage_min[3]%$-%$towers.barrel.skill_warrior.entity.damage_max[3]%$物理傷害。",
["TOWER_BARREL_4_SKILL_WARRIOR_3_NAME"] = "神奇藥酒",
["TOWER_BARREL_4_SKILL_WARRIOR_NOTE"] = "這就是勝利的滋味！",
["TOWER_BARREL_DESC"] = "北境民族是釀造藥酒的專家，更能以此來與敵人戰鬥。",
["TOWER_BARREL_NAME"] = "釀酒戰匠",
["TOWER_BARREL_WARRIOR_NAME"] = "勇猛者哈爾丹",
["TOWER_BROKEN_DESCRIPTION"] = "這座塔已損壞，請花費金幣來修復它。",
["TOWER_BROKEN_NAME"] = "損壞的塔",
["TOWER_CROCS_EATEN_DESCRIPTION"] = "用不可思議的魔法讓防禦塔恢復原狀。",
["TOWER_CROCS_EATEN_NAME"] = "塔的殘骸",
["TOWER_DARK_ELF_1_DESCRIPTION"] = "無論遠近強弱，永是箭無虛發。",
["TOWER_DARK_ELF_1_NAME"] = "暮光長弓手 I",
["TOWER_DARK_ELF_2_DESCRIPTION"] = "無論遠近強弱，永是箭無虛發。",
["TOWER_DARK_ELF_2_NAME"] = "暮光長弓手 II",
["TOWER_DARK_ELF_3_DESCRIPTION"] = "無論遠近強弱，永是箭無虛發。",
["TOWER_DARK_ELF_3_NAME"] = "暮光長弓手 III",
["TOWER_DARK_ELF_4_DESCRIPTION"] = "無論遠近強弱，永是箭無虛發。",
["TOWER_DARK_ELF_4_NAME"] = "暮光長弓手 IV",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_DESCRIPTION"] = "每擊殺一名敵人，此塔的攻擊力就增加%$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$。",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_NAME"] = "狩獵的激昂",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_DESCRIPTION"] = "每擊殺一名敵人，此塔的攻擊力就增加%$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$。",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_NAME"] = "狩獵的激昂",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_DESCRIPTION"] = "每擊殺一名敵人，此塔的攻擊力就增加%$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$。",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_NAME"] = "狩獵的激昂",
["TOWER_DARK_ELF_4_SKILL_BUFF_NOTE"] = "發現獵物！",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_DESCRIPTION"] = "召喚兩名暮光侵擾者。他們擁有%$towers.dark_elf.soldier.hp[1]%$生命值且可造成%$towers.dark_elf.soldier.basic_attack.damage_min[1]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[1]%$物理傷害。",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_NAME"] = "援護利刃",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_DESCRIPTION"] = "暮光侵擾者現在擁有%$towers.dark_elf.soldier.hp[2]%$生命值且可造成%$towers.dark_elf.soldier.basic_attack.damage_min[2]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[2]%$物理傷害。",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_NAME"] = "援護利刃",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_DESCRIPTION"] = "暮光侵擾者現在擁有%$towers.dark_elf.soldier.hp[3]%$生命值且可造成%$towers.dark_elf.soldier.basic_attack.damage_min[3]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[3]%$物理傷害。",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_NAME"] = "援護利刃",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_NOTE"] = "他們降世來嬉戲。",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_DESCRIPTION"] = "變更攻擊的優先目標為最靠近出口的敵人。",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NAME"] = "目標：最高威脅性",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NOTE"] = "別讓他們通過！",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_DESCRIPTION"] = "變更攻擊的優先目標為生命總值最高的敵人。",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NAME"] = "目標：最大生命值",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NOTE"] = "專打大塊頭！",
["TOWER_DARK_ELF_DESC"] = "專精於從遠處狙殺強大敵人的射手，使用黑暗能量來強化射擊。",
["TOWER_DARK_ELF_NAME"] = "暮光長弓手",
["TOWER_DEMON_PIT_1_DESCRIPTION"] = "這些貪玩又危險的惡魔總是愛惹事生非。",
["TOWER_DEMON_PIT_1_NAME"] = "惡魔浴池 I",
["TOWER_DEMON_PIT_2_DESCRIPTION"] = "這些貪玩又危險的惡魔總是愛惹事生非。",
["TOWER_DEMON_PIT_2_NAME"] = "惡魔浴池 II",
["TOWER_DEMON_PIT_3_DESCRIPTION"] = "這些貪玩又危險的惡魔總是愛惹事生非。",
["TOWER_DEMON_PIT_3_NAME"] = "惡魔浴池 III",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_DESCRIPTION"] = "召喚一隻擁有%$towers.demon_pit.big_guy.hp_max[1]%$生命值的巨大小惡魔，能夠造成%$towers.demon_pit.big_guy.melee_attack.damage_min[1]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[1]%$物理傷害。爆炸時給予%$towers.demon_pit.big_guy.explosion_damage[1]%$傷害。",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_NAME"] = "大隻佬",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_DESCRIPTION"] = "大隻佬的生命值變為%$towers.demon_pit.big_guy.hp_max[2]%$點，並可造成%$towers.demon_pit.big_guy.melee_attack.damage_min[2]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[2]%$物理傷害。爆炸時給予%$towers.demon_pit.big_guy.explosion_damage[2]%$傷害。",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_NAME"] = "大隻佬",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_DESCRIPTION"] = "大隻佬的生命值變為%$towers.demon_pit.big_guy.hp_max[3]%$，並可造成%$towers.demon_pit.big_guy.melee_attack.damage_min[3]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[3]%$物理傷害。爆炸時給予%$towers.demon_pit.big_guy.explosion_damage[3]%$傷害。",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_NAME"] = "大隻佬",
["TOWER_DEMON_PIT_4_BIG_DEMON_NAME"] = "大隻佬",
["TOWER_DEMON_PIT_4_BIG_DEMON_NOTE"] = "我只是想放鬆一下。",
["TOWER_DEMON_PIT_4_DESCRIPTION"] = "這些貪玩又危險的惡魔總是愛惹事生非。",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_DESCRIPTION"] = "小惡魔的爆炸傷害提升%$towers.demon_pit.master_exploders.s_damage_increase[1]%$%，並灼傷敵人，每秒造成%$towers.demon_pit.master_exploders.s_total_burning_damage_min[1]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[1]%$真實傷害，持續%$towers.demon_pit.master_exploders.s_burning_duration[1]%$秒。",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_NAME"] = "爆炸大師",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_DESCRIPTION"] = "小惡魔的爆炸傷害提升%$towers.demon_pit.master_exploders.s_damage_increase[2]%$%，燃燒效果每秒造成%$towers.demon_pit.master_exploders.s_total_burning_damage_min[2]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[2]%$真實傷害，持續%$towers.demon_pit.master_exploders.s_burning_duration[2]%$秒。",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_NAME"] = "爆炸大師",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_DESCRIPTION"] = "小惡魔的爆炸傷害提升%$towers.demon_pit.master_exploders.s_damage_increase[3]%$%。燃燒效果每秒造成%$towers.demon_pit.master_exploders.s_total_burning_damage_min[3]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[3]%$真實傷害，持續%$towers.demon_pit.master_exploders.s_burning_duration[3]%$秒。",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_NAME"] = "爆炸大師",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NAME"] = "爆炸大師",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NOTE"] = "傻了才會幹這行。",
["TOWER_DEMON_PIT_4_NAME"] = "惡魔浴池 IV",
["TOWER_DEMON_PIT_DESC"] = "這些來自熔岩深處的小惡魔會毫不猶豫地「投入」戰場。",
["TOWER_DEMON_PIT_NAME"] = "惡魔浴池",
["TOWER_DEMON_PIT_SOLDIER_BIG_GUY_NAME"] = "大塊頭",
["TOWER_DEMON_PIT_SOLDIER_NAME"] = "小惡魔",
["TOWER_DWARF_1_DESCRIPTION"] = "雖然嬌小又火爆，但沒有人能夠活著通過她們的防線。",
["TOWER_DWARF_1_NAME"] = "炮兵小隊 I",
["TOWER_DWARF_2_DESCRIPTION"] = "雖然嬌小又火爆，但沒有人能夠活著通過她們的防線。",
["TOWER_DWARF_2_NAME"] = "炮兵小隊 II",
["TOWER_DWARF_3_DESCRIPTION"] = "雖然嬌小又火爆，但沒有人能夠活著通過她們的防線。",
["TOWER_DWARF_3_NAME"] = "炮兵小隊 III",
["TOWER_DWARF_4_DESCRIPTION"] = "雖然嬌小又火爆，但沒有人能夠活著通過她們的防線。",
["TOWER_DWARF_4_FORMATION_1_DESCRIPTION"] = "為小隊增加第三名炮兵。",
["TOWER_DWARF_4_FORMATION_1_NAME"] = "隊伍擴充",
["TOWER_DWARF_4_FORMATION_2_DESCRIPTION"] = "為小隊增加第四名炮兵。",
["TOWER_DWARF_4_FORMATION_2_NAME"] = "隊伍擴充",
["TOWER_DWARF_4_FORMATION_3_DESCRIPTION"] = "為小隊增加第五名炮兵。",
["TOWER_DWARF_4_FORMATION_3_NAME"] = "隊伍擴充",
["TOWER_DWARF_4_FORMATION_NOTE"] = "女孩只想擁有槍械。",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_DESCRIPTION"] = "發射造成%$towers.dwarf.incendiary_ammo.damages_min[1]%$-%$towers.dwarf.incendiary_ammo.damages_max[1]%$傷害的爆裂物，並在%$towers.dwarf.incendiary_ammo.burn.duration%$秒間給予範圍內的敵人%$towers.dwarf.incendiary_ammo.burn.s_damage[1]%$燃燒傷害。",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_NAME"] = "燃燒彈",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_DESCRIPTION"] = "發射造成%$towers.dwarf.incendiary_ammo.damages_min[2]%$-%$towers.dwarf.incendiary_ammo.damages_max[2]%$傷害的爆裂物，並在%$towers.dwarf.incendiary_ammo.burn.duration%$秒間給予範圍內的敵人%$towers.dwarf.incendiary_ammo.burn.s_damage[2]%$燃燒傷害。",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_NAME"] = "燃燒彈",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_DESCRIPTION"] = "發射造成%$towers.dwarf.incendiary_ammo.damages_min[3]%$-%$towers.dwarf.incendiary_ammo.damages_max[3]%$傷害的爆裂物，並在%$towers.dwarf.incendiary_ammo.burn.duration%$秒間給予範圍內的敵人%$towers.dwarf.incendiary_ammo.burn.s_damage[3]%$燃燒傷害。",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_NAME"] = "燃燒彈",
["TOWER_DWARF_4_INCENDIARY_AMMO_NOTE"] = "熱力全開！",
["TOWER_DWARF_4_NAME"] = "炮兵小隊 IV",
["TOWER_DWARF_DESC"] = "炮兵小隊不只各個都是精銳的炮手，更擁有無與倫比的團隊合作。她們從北方前來支援，應對不當的科技使用情事。",
["TOWER_DWARF_NAME"] = "炮兵小隊",
["TOWER_ELVEN_STARGAZERS_DESC"] = "精靈星月術士可以召喚宇宙能量，同時對付多名敵人。",
["TOWER_ELVEN_STARGAZERS_NAME"] = "精靈星月術士",
["TOWER_FLAMESPITTER_1_DESCRIPTION"] = "毫不遜色於龍之吐息的火焰，讓邪惡勢力聞之色變。",
["TOWER_FLAMESPITTER_1_NAME"] = "矮人噴火機 I",
["TOWER_FLAMESPITTER_2_DESCRIPTION"] = "毫不遜色於龍之吐息的火焰，讓邪惡勢力聞之色變。",
["TOWER_FLAMESPITTER_2_NAME"] = "矮人噴火機 II",
["TOWER_FLAMESPITTER_3_DESCRIPTION"] = "毫不遜色於龍之吐息的火焰，讓邪惡勢力聞之色變。",
["TOWER_FLAMESPITTER_3_NAME"] = "矮人噴火機 III",
["TOWER_FLAMESPITTER_4_DESCRIPTION"] = "毫不遜色於龍之吐息的火焰，讓邪惡勢力聞之色變。",
["TOWER_FLAMESPITTER_4_NAME"] = "矮人噴火機 IV",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_DESCRIPTION"] = "發射一顆火焰炸彈，對敵人造成%$towers.flamespitter.skill_bomb.s_damage[1]%$物理傷害，並灼燒敵人，在%$towers.flamespitter.skill_bomb.burning.duration%$秒內每秒造成%$towers.flamespitter.skill_bomb.burning.s_damage%$真實傷害。",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_NAME"] = "烈焰之路",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_DESCRIPTION"] = "燃燒彈造成%$towers.flamespitter.skill_bomb.s_damage[2]%$物理傷害。燃燒效果每秒給予%$towers.flamespitter.skill_bomb.burning.s_damage%$真實傷害，持續 %$towers.flamespitter.skill_bomb.burning.duration%$秒。",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_NAME"] = "烈焰之路",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_DESCRIPTION"] = "燃燒彈造成%$towers.flamespitter.skill_bomb.s_damage[3]%$物理傷害。燃燒效果每秒給予%$towers.flamespitter.skill_bomb.burning.s_damage%$真實傷害，持續 %$towers.flamespitter.skill_bomb.burning.duration%$秒。",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_NAME"] = "烈焰之路",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_NOTE"] = "為火烤而生。",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_DESCRIPTION"] = "路徑中噴發出火柱對敵人造成%$towers.flamespitter.skill_columns.s_damage_out[1]%$-%$towers.flamespitter.skill_columns.s_damage_in[1]%$物理傷害，並使敵人暈眩%$towers.flamespitter.skill_columns.s_stun%$秒。",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_NAME"] = "灼熱火炬",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_DESCRIPTION"] = "火柱造成%$towers.flamespitter.skill_columns.s_damage_out[2]%$-%$towers.flamespitter.skill_columns.s_damage_in[2]%$物理傷害，並使敵軍暈眩%$towers.flamespitter.skill_columns.s_stun%$秒。",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_NAME"] = "灼熱火炬",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_DESCRIPTION"] = "火柱造成%$towers.flamespitter.skill_columns.s_damage_out[3]%$-%$towers.flamespitter.skill_columns.s_damage_in[3]%$物理傷害，並使敵軍暈眩%$towers.flamespitter.skill_columns.s_stun%$秒。",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_NAME"] = "灼熱火炬",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_NOTE"] = "當心腳下！",
["TOWER_FLAMESPITTER_DESC"] = "善於鍛鐵的矮人族帶著熔爐焰火與熊熊燃燒的決心，全力奧援盟軍。",
["TOWER_FLAMESPITTER_NAME"] = "矮人噴火機",
["TOWER_GHOST_1_DESCRIPTION"] = "以為你看見了？別傻了，你已經死了。",
["TOWER_GHOST_1_NAME"] = "幽冥戰魂 I",
["TOWER_GHOST_2_DESCRIPTION"] = "以為你看見了？別傻了，你已經死了。",
["TOWER_GHOST_2_NAME"] = "幽冥戰魂 II",
["TOWER_GHOST_3_DESCRIPTION"] = "以為你看見了？別傻了，你已經死了。",
["TOWER_GHOST_3_NAME"] = "幽冥戰魂 III",
["TOWER_GHOST_4_DESCRIPTION"] = "以為你看見了？別傻了，你已經死了。",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_DESCRIPTION"] = "戰魂在戰鬥%$towers.ghost.extra_damage.cooldown_start%$秒後會造成%$towers.ghost.extra_damage.s_damage[1]%$%額外傷害。",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_NAME"] = "靈魂虹吸",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_DESCRIPTION"] = "戰魂在戰鬥%$towers.ghost.extra_damage.cooldown_start%$秒後會造成%$towers.ghost.extra_damage.s_damage[2]%$%額外傷害。",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_NAME"] = "靈魂虹吸",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_DESCRIPTION"] = "戰魂在戰鬥%$towers.ghost.extra_damage.cooldown_start%$秒後會造成%$towers.ghost.extra_damage.s_damage[3]%$%額外傷害。",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_NAME"] = "靈魂虹吸",
["TOWER_GHOST_4_EXTRA_DAMAGE_NOTE"] = "生人勿近。",
["TOWER_GHOST_4_NAME"] = "幽冥戰魂 IV",
["TOWER_GHOST_4_SOUL_ATTACK_1_DESCRIPTION"] = "戰魂被擊敗後會衝向一名周圍的敵人，造成%$towers.ghost.soul_attack.s_damage[1]%$真實傷害、緩速並使其攻擊傷害減半。",
["TOWER_GHOST_4_SOUL_ATTACK_1_NAME"] = "永恆恐懼",
["TOWER_GHOST_4_SOUL_ATTACK_2_DESCRIPTION"] = "戰魂被擊敗後會衝向一名周圍的敵人，造成%$towers.ghost.soul_attack.s_damage[2]%$真實傷害、緩速並使其攻擊傷害減半。",
["TOWER_GHOST_4_SOUL_ATTACK_2_NAME"] = "永恆恐懼",
["TOWER_GHOST_4_SOUL_ATTACK_3_DESCRIPTION"] = "戰魂被擊敗後會衝向一名周圍的敵人，造成%$towers.ghost.soul_attack.s_damage[3]%$真實傷害、緩速並使其攻擊傷害減半。",
["TOWER_GHOST_4_SOUL_ATTACK_3_NAME"] = "永恆恐懼",
["TOWER_GHOST_4_SOUL_ATTACK_NOTE"] = "與我等同歸於盡吧！",
["TOWER_GHOST_DESC"] = "超越死亡的狂戰幽魂，他們潛於暗影、殺於無形。",
["TOWER_GHOST_NAME"] = "幽冥戰魂",
["TOWER_HERMIT_TOAD_1_DESCRIPTION"] = "魔法也好，蠻力也罷。他只想將煩人的入侵者驅逐出去。",
["TOWER_HERMIT_TOAD_1_NAME"] = "酸沼隱士 I",
["TOWER_HERMIT_TOAD_2_DESCRIPTION"] = "魔法也好，蠻力也罷。他只想將煩人的入侵者驅逐出去。",
["TOWER_HERMIT_TOAD_2_NAME"] = "酸沼隱士 II",
["TOWER_HERMIT_TOAD_3_DESCRIPTION"] = "魔法也好，蠻力也罷。他只想將煩人的入侵者驅逐出去。",
["TOWER_HERMIT_TOAD_3_NAME"] = "酸沼隱士 III",
["TOWER_HERMIT_TOAD_4_DESCRIPTION"] = "魔法也好，蠻力也罷。他只想將煩人的入侵者驅逐出去。",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_DESCRIPTION"] = "每%$towers.hermit_toad.power_instakill.cooldown[1]%$秒，隱士用舌頭吞噬一名敵人。",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_NAME"] = "黏性之舌",
["TOWER_HERMIT_TOAD_4_JUMP_1_DESCRIPTION"] = "每%$towers.hermit_toad.power_jump.cooldown[1]%$秒隱士會高高跳起，重壓敵人。造成%$towers.hermit_toad.power_jump.damage_min[1]%$傷害，並在落地時暈眩他們%$towers.hermit_toad.power_jump.stun_duration[1]%$秒。",
["TOWER_HERMIT_TOAD_4_JUMP_1_NAME"] = "地面重壓",
["TOWER_HERMIT_TOAD_4_NAME"] = "酸沼隱士 IV",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_DESCRIPTION"] = "每%$towers.hermit_toad.power_instakill.cooldown[1]%$秒，隱士用舌頭吞噬一名敵人。",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_NAME"] = "黏性之舌 I",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_NOTE"] = "黏黏糊糊。",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_DESCRIPTION"] = "每%$towers.hermit_toad.power_jump.cooldown[1]%$秒隱士會高高跳起，重壓敵人。造成%$towers.hermit_toad.power_jump.damage_min[1]%$傷害，並在落地時暈眩他們%$towers.hermit_toad.power_jump.stun_duration[1]%$秒。",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_NAME"] = "地面重壓 I",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_DESCRIPTION"] = "每%$towers.hermit_toad.power_jump.cooldown[2]%$秒隱士會高高跳起，重壓敵人。造成%$towers.hermit_toad.power_jump.damage_min[2]%$傷害，並在落地時暈眩他們%$towers.hermit_toad.power_jump.stun_duration[2]%$秒。",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_NAME"] = "地面重壓 II",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_DESCRIPTION"] = "每%$towers.hermit_toad.power_jump.cooldown[3]%$秒隱士會高高跳起，重壓敵人。造成%$towers.hermit_toad.power_jump.damage_min[3]%$傷害，並在落地時暈眩他們%$towers.hermit_toad.power_jump.stun_duration[3]%$秒。",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_NAME"] = "地面重壓 III",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_NOTE"] = "沼澤排球隊在等著我。",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_DESCRIPTION"] = "隱士切換為物理攻擊。",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NAME"] = "泥沼",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NOTE"] = "弄髒手的時候到了！",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_DESCRIPTION"] = "隱士切換為魔法攻擊。",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NAME"] = "魔浴",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NOTE"] = "無盡的力量！！",
["TOWER_HERMIT_TOAD_DESC"] = "擅於吐出黏液球的巨大蟾蜍法師。他一心只想享受沼澤浴中的安寧與和平。切勿打擾。",
["TOWER_HERMIT_TOAD_NAME"] = "酸沼隱士",
["TOWER_NECROMANCER_1_DESCRIPTION"] = "掌握死亡之力的死靈法師會在戰場上播下混亂的種子，再收割豐碩的果實。",
["TOWER_NECROMANCER_1_NAME"] = "死靈法師 I",
["TOWER_NECROMANCER_2_DESCRIPTION"] = "掌握死亡之力的死靈法師會在戰場上播下混亂的種子，再收割豐碩的果實。",
["TOWER_NECROMANCER_2_NAME"] = "死靈法師 II",
["TOWER_NECROMANCER_3_DESCRIPTION"] = "掌握死亡之力的死靈法師會在戰場上播下混亂的種子，再收割豐碩的果實。",
["TOWER_NECROMANCER_3_NAME"] = "死靈法師 III",
["TOWER_NECROMANCER_4_DESCRIPTION"] = "掌握死亡之力的死靈法師會在戰場上播下混亂的種子，再收割豐碩的果實。",
["TOWER_NECROMANCER_4_NAME"] = "死靈法師 IV",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_DESCRIPTION"] = "放置一個圖騰，持續%$towers.necromancer.skill_debuff.aura_duration[1]%$秒，詛咒敵人並給予骷髏%$towers.necromancer.skill_debuff.s_damage_factor[1]%$%額外攻擊傷害。",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_NAME"] = "顫骨圖騰",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_DESCRIPTION"] = "圖騰給予骷髏%$towers.necromancer.skill_debuff.s_damage_factor[2]%$%額外攻擊傷害。冷卻時間減少至%$towers.necromancer.skill_debuff.cooldown[2]%$秒。",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_NAME"] = "顫骨圖騰",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_DESCRIPTION"] = "圖騰給予骷髏%$towers.necromancer.skill_debuff.s_damage_factor[3]%$%額外攻擊傷害。冷卻時間減少至%$towers.necromancer.skill_debuff.cooldown[3]%$秒。",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_NAME"] = "顫骨圖騰",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_NOTE"] = "強骨軍隊！",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_DESCRIPTION"] = "在路徑上召喚一名死靈騎士，對其途經的任何敵人造成%$towers.necromancer.skill_rider.s_damage[1]%$真實傷害。",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_NAME"] = "死靈騎士",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_DESCRIPTION"] = "死靈騎士造成%$towers.necromancer.skill_rider.s_damage[2]%$真實傷害。",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_NAME"] = "死靈騎士",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_DESCRIPTION"] = "死靈騎士造成%$towers.necromancer.skill_rider.s_damage[3]%$真實傷害。",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_NAME"] = "死靈騎士",
["TOWER_NECROMANCER_4_SKILL_RIDER_NOTE"] = "死亡的單程票……",
["TOWER_NECROMANCER_DESC"] = "死靈法師掌握了最黑暗的魔法，能將敵軍轉化為無盡不死軍團的一員。",
["TOWER_NECROMANCER_NAME"] = "死靈法師",
["TOWER_PALADIN_COVENANT_1_DESCRIPTION"] = "勇猛又忠誠的聖騎士，為王國的和平而奮戰。",
["TOWER_PALADIN_COVENANT_1_NAME"] = "誓約聖騎士 I",
["TOWER_PALADIN_COVENANT_2_DESCRIPTION"] = "勇猛又忠誠的聖騎士，為王國的和平而奮戰。",
["TOWER_PALADIN_COVENANT_2_NAME"] = "誓約聖騎士 II",
["TOWER_PALADIN_COVENANT_3_DESCRIPTION"] = "勇猛又忠誠的聖騎士，為王國的和平而奮戰。",
["TOWER_PALADIN_COVENANT_3_NAME"] = "誓約聖騎士 III",
["TOWER_PALADIN_COVENANT_4_DESCRIPTION"] = "勇猛又忠誠的聖騎士，為王國的和平而奮戰。",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_DESCRIPTION"] = "當士兵的生命值變為%$towers.paladin_covenant.healing_prayer.health_trigger_factor[1]%$%時，將進入無敵狀態，並且每秒恢復%$towers.paladin_covenant.healing_prayer.s_healing[1]%$生命值，持續%$towers.paladin_covenant.healing_prayer.duration%$秒。",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_NAME"] = "治療祝禱",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_DESCRIPTION"] = "治療量增加至每秒%$towers.paladin_covenant.healing_prayer.s_healing[2]%$生命值，持續%$towers.paladin_covenant.healing_prayer.duration%$秒。",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_NAME"] = "治療祝禱",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_DESCRIPTION"] = "治療量增加至每秒%$towers.paladin_covenant.healing_prayer.s_healing[3]%$生命值，持續%$towers.paladin_covenant.healing_prayer.duration%$秒。",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_NAME"] = "治療祝禱",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NAME"] = "治療祝禱",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NOTE"] = "鞠躬盡瘁。",
["TOWER_PALADIN_COVENANT_4_LEAD_1_DESCRIPTION"] = "將一名聖騎士替換為身經百戰的禁衛軍，為周圍盟友提供%$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$%攻擊傷害增益。",
["TOWER_PALADIN_COVENANT_4_LEAD_1_NAME"] = "以身作則",
["TOWER_PALADIN_COVENANT_4_LEAD_2_DESCRIPTION"] = "將一名聖騎士替換為身經百戰的禁衛軍，為周圍盟友提供%$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$%攻擊傷害增益。",
["TOWER_PALADIN_COVENANT_4_LEAD_2_NAME"] = "以身作則",
["TOWER_PALADIN_COVENANT_4_LEAD_3_DESCRIPTION"] = "將一名聖騎士替換為身經百戰的禁衛軍，為周圍盟友提供%$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$%攻擊傷害增益。",
["TOWER_PALADIN_COVENANT_4_LEAD_3_NAME"] = "以身作則",
["TOWER_PALADIN_COVENANT_4_LEAD_NAME"] = "以身作則",
["TOWER_PALADIN_COVENANT_4_LEAD_NOTE"] = "為了國王，為了故土，為了這片山河。",
["TOWER_PALADIN_COVENANT_4_NAME"] = "誓約聖騎士 IV",
["TOWER_PALADIN_COVENANT_DESC"] = "聖騎士是利尼維亞精銳部隊的中流砥柱，能用神聖力量在戰鬥中保護與治療自身。",
["TOWER_PALADIN_COVENANT_NAME"] = "誓約聖騎士",
["TOWER_PANDAS_1_DESCRIPTION"] = "不僅精通元素武藝，更身懷不屈不撓的決心。這竹林三「熊」會為守護自然世界的平衡而不懈奮戰。",
["TOWER_PANDAS_1_NAME"] = "竹林三熊 I",
["TOWER_PANDAS_2_DESCRIPTION"] = "不僅精通元素武藝，更身懷不屈不撓的決心。這竹林三「熊」會為守護自然世界的平衡而不懈奮戰。",
["TOWER_PANDAS_2_NAME"] = "竹林三熊 II",
["TOWER_PANDAS_3_DESCRIPTION"] = "不僅精通元素武藝，更身懷不屈不撓的決心。這竹林三「熊」會為守護自然世界的平衡而不懈奮戰。",
["TOWER_PANDAS_3_NAME"] = "竹林三熊 III",
["TOWER_PANDAS_4_DESCRIPTION"] = "不僅精通元素武藝，更身懷不屈不撓的決心。這竹林三「熊」會為守護自然世界的平衡而不懈奮戰。",
["TOWER_PANDAS_4_FIERY"] = "卡嗚咻",
["TOWER_PANDAS_4_FIERY_1_DESCRIPTION"] = "施放火焰彈，能給予%$towers.pandas.soldier.teleport.damage_min[1]%$-%$towers.pandas.soldier.teleport.damage_max[1]%$真實傷害，並將受影響的敵人沿路逕往回傳送。",
["TOWER_PANDAS_4_FIERY_1_NAME"] = "獄火劫",
["TOWER_PANDAS_4_FIERY_2_DESCRIPTION"] = "施放火焰彈，能給予%$towers.pandas.soldier.teleport.damage_min[2]%$-%$towers.pandas.soldier.teleport.damage_max[2]%$真實傷害，並將受影響的敵人沿路逕往回傳送。",
["TOWER_PANDAS_4_FIERY_2_NAME"] = "獄火劫",
["TOWER_PANDAS_4_HAT"] = "一帽打天下",
["TOWER_PANDAS_4_HAT_1_DESCRIPTION"] = "丟出鋒銳的斗笠在敵人間反彈，每次命中皆可造成%$towers.pandas.soldier.hat.damage_min[1]%$-%$towers.pandas.soldier.hat.damage_max[1]%$點傷害。",
["TOWER_PANDAS_4_HAT_1_NAME"] = "飛笠斬",
["TOWER_PANDAS_4_HAT_2_DESCRIPTION"] = "丟出鋒銳的斗笠在敵人間反彈，每次命中皆可造成%$towers.pandas.soldier.hat.damage_min[2]%$-%$towers.pandas.soldier.hat.damage_max[2]%$點傷害。",
["TOWER_PANDAS_4_HAT_2_NAME"] = "飛笠斬",
["TOWER_PANDAS_4_NAME"] = "竹林三熊 IV",
["TOWER_PANDAS_4_THUNDER"] = "熊貓快打",
["TOWER_PANDAS_4_THUNDER_1_DESCRIPTION"] = "在小範圍內召喚雷電，每道雷電造成%$towers.pandas.soldier.thunder.damage_min[1]%$-%$towers.pandas.soldier.thunder.damage_max[1]%$範圍傷害，並短暫暈眩命中的敵人。",
["TOWER_PANDAS_4_THUNDER_1_NAME"] = "天雷破",
["TOWER_PANDAS_4_THUNDER_2_DESCRIPTION"] = "在小範圍內召喚雷電，每道雷電造成%$towers.pandas.soldier.thunder.damage_min[2]%$-%$towers.pandas.soldier.thunder.damage_max[2]%$範圍傷害，並短暫暈眩命中的敵人。",
["TOWER_PANDAS_4_THUNDER_2_NAME"] = "天雷破",
["TOWER_PANDAS_DESC"] = "融合了武術與元素之力，這支熊貓三人組不只能在戰場上撕裂敵人，就算在敗下陣後也仍具威脅。",
["TOWER_PANDAS_NAME"] = "竹林三熊",
["TOWER_PANDAS_RETREAT_DESCRIPTION"] = "讓場上的熊貓撤離至安全區域，持續8秒。",
["TOWER_PANDAS_RETREAT_NAME"] = "戰術撤退",
["TOWER_PANDAS_RETREAT_NOTE"] = "有勇無謀，豈成大器",
["TOWER_RAY_1_DESCRIPTION"] = "儘管此種形式的法術汙穢又危險，卻還是攔不住這些紛紛向邪惡而去的魔導士。",
["TOWER_RAY_1_NAME"] = "異能魔導士 I",
["TOWER_RAY_2_DESCRIPTION"] = "儘管此種形式的法術汙穢又危險，卻還是攔不住這些紛紛向邪惡而去的魔導士。",
["TOWER_RAY_2_NAME"] = "異能魔導士 II",
["TOWER_RAY_3_DESCRIPTION"] = "儘管此種形式的法術汙穢又危險，卻還是攔不住這些紛紛向邪惡而去的魔導士。",
["TOWER_RAY_3_NAME"] = "異能魔導士 III",
["TOWER_RAY_4_CHAIN_1_DESCRIPTION"] = "擴張魔力射線，使其能額外攻擊%$towers.ray.skill_chain.s_max_enemies%$名敵人，且在攻擊時緩速，對每個目標的傷害為總魔法傷害的%$towers.ray.skill_chain.damage_mult[1]%$%。",
["TOWER_RAY_4_CHAIN_1_NAME"] = "力量滿溢",
["TOWER_RAY_4_CHAIN_2_DESCRIPTION"] = "擴張魔力射線，使其能額外攻擊%$towers.ray.skill_chain.s_max_enemies%$名敵人，且在攻擊時緩速，對每個目標的傷害為總魔法傷害的%$towers.ray.skill_chain.damage_mult[2]%$%。",
["TOWER_RAY_4_CHAIN_2_NAME"] = "力量滿溢",
["TOWER_RAY_4_CHAIN_3_DESCRIPTION"] = "擴張魔力射線，使其能額外攻擊%$towers.ray.skill_chain.s_max_enemies%$名敵人，且在攻擊時緩速，對每個目標的傷害為總魔法傷害的%$towers.ray.skill_chain.damage_mult[3]%$%。",
["TOWER_RAY_4_CHAIN_3_NAME"] = "力量滿溢",
["TOWER_RAY_4_CHAIN_NOTE"] = "苦痛無邊，人人有份。",
["TOWER_RAY_4_DESCRIPTION"] = "儘管此種形式的法術汙穢又危險，卻還是攔不住這些紛紛向邪惡而去的魔導士。",
["TOWER_RAY_4_NAME"] = "異能魔導士 IV",
["TOWER_RAY_4_SHEEP_1_DESCRIPTION"] = "將周圍的一名敵人突變為毫無力量可言的羊羊。羊的生命值為目標生命值的%$towers.ray.skill_sheep.sheep.hp_mult%$%。",
["TOWER_RAY_4_SHEEP_1_NAME"] = "突變邪咒",
["TOWER_RAY_4_SHEEP_2_DESCRIPTION"] = "將周圍的一名敵人突變為毫無力量可言的羊羊。羊的生命值為目標生命值的%$towers.ray.skill_sheep.sheep.hp_mult%$%。",
["TOWER_RAY_4_SHEEP_2_NAME"] = "突變邪咒",
["TOWER_RAY_4_SHEEP_3_DESCRIPTION"] = "將周圍的一名敵人突變為毫無力量可言的羊羊。羊的生命值為目標生命值的%$towers.ray.skill_sheep.sheep.hp_mult%$%。",
["TOWER_RAY_4_SHEEP_3_NAME"] = "突變邪咒",
["TOWER_RAY_4_SHEEP_NOTE"] = "說真的，你這樣比較好看。",
["TOWER_RAY_DESC"] = "佛則南的弟子繼承師傳的邪惡力量，施放黑暗射線為敵軍帶來痛苦折磨。",
["TOWER_RAY_NAME"] = "異能魔導士",
["TOWER_ROCKET_GUNNERS_1_DESCRIPTION"] = "配備黑暗大軍最先進軍武的射手，擔任著天際巡防員的職責。",
["TOWER_ROCKET_GUNNERS_1_NAME"] = "火箭射手隊 I",
["TOWER_ROCKET_GUNNERS_2_DESCRIPTION"] = "配備黑暗大軍最先進軍武的射手，擔任著天際巡防員的職責。",
["TOWER_ROCKET_GUNNERS_2_NAME"] = "火箭射手隊 II",
["TOWER_ROCKET_GUNNERS_3_DESCRIPTION"] = "配備黑暗大軍最先進軍武的射手，擔任著天際巡防員的職責。",
["TOWER_ROCKET_GUNNERS_3_NAME"] = "火箭射手隊 III",
["TOWER_ROCKET_GUNNERS_4_DESCRIPTION"] = "配備黑暗大軍最先進軍武的射手，擔任著天際巡防員的職責。",
["TOWER_ROCKET_GUNNERS_4_NAME"] = "火箭射手隊 IV",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_DESCRIPTION"] = "每次攻擊都會破壞敵軍%$towers.rocket_gunners.soldier.phosphoric.armor_reduction[1]%$%護甲， 並造成範圍傷害。",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_NAME"] = "磷化子彈",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_DESCRIPTION"] = "每次攻擊都會破壞敵軍%$towers.rocket_gunners.soldier.phosphoric.armor_reduction[2]%$%護甲，並造成%$towers.rocket_gunners.soldier.phosphoric.damage_area_min[2]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[2]%$範圍傷害。",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_NAME"] = "磷化子彈",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_DESCRIPTION"] = "每次攻擊都會破壞敵軍%$towers.rocket_gunners.soldier.phosphoric.armor_reduction[3]%$%護甲，並造成%$towers.rocket_gunners.soldier.phosphoric.damage_area_min[3]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[3]%$範圍傷害。",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_NAME"] = "磷化子彈",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_NOTE"] = "加入邪惡調劑的子彈。",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_DESCRIPTION"] = "發射飛彈，即殺一名生命值%$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[1]%$以下的目標。",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_NAME"] = "針刺導彈",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_DESCRIPTION"] = "將冷卻時間減至%$towers.rocket_gunners.sting_missiles.cooldown[2]%$秒。現在可以針對生命值%$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[2]%$以下的敵人。",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_NAME"] = "針刺導彈",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_DESCRIPTION"] = "將冷卻時間減少到%$towers.rocket_gunners.sting_missiles.cooldown[3]%$秒。現在可以針對生命值%$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[3]%$以下的敵人。",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_NAME"] = "針刺導彈",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_NOTE"] = "有本事就躲開啊！",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_DESCRIPTION"] = "火箭射手升空，變為無法阻擋敵軍。",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NAME"] = "升空",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NOTE"] = "飛向宇宙，浩瀚無垠！",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_DESCRIPTION"] = "火箭射手著陸，能夠阻擋敵軍。",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NAME"] = "著陸",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NOTE"] = "雄鷹落地！",
["TOWER_ROCKET_GUNNERS_DESC"] = "這支特殊部隊無論在地面或空中都能獨立作戰，運用先進的武器發動奇襲。",
["TOWER_ROCKET_GUNNERS_NAME"] = "火箭射手隊",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "此防禦塔包含在【巨大的威脅】篇章中。",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "此防禦塔包含在【大聖遊記】篇章中。",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "【巨大的威脅】篇章",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "【大聖遊記】篇章",
["TOWER_ROOM_EQUIPPED_TOWERS_TITLE"] = "已裝備防禦塔",
["TOWER_ROOM_GET_DLC"] = "拿到它",
["TOWER_ROOM_LABEL_ROSTER_THUMB_NEW"] = "新的！",
["TOWER_ROOM_SKILLS_TITLE"] = "技能",
["TOWER_ROYAL_ARCHERS_1_DESCRIPTION"] = "誓死效忠王國的弓兵，會從遠程掩護利尼維亞的軍隊。",
["TOWER_ROYAL_ARCHERS_1_NAME"] = "皇家弓兵 I",
["TOWER_ROYAL_ARCHERS_2_DESCRIPTION"] = "誓死效忠王國的弓兵，會從遠程掩護利尼維亞的軍隊。",
["TOWER_ROYAL_ARCHERS_2_NAME"] = "皇家弓兵 II",
["TOWER_ROYAL_ARCHERS_3_DESCRIPTION"] = "誓死效忠王國的弓兵，會從遠程掩護利尼維亞的軍隊。",
["TOWER_ROYAL_ARCHERS_3_NAME"] = "皇家弓兵 III",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_DESCRIPTION"] = "發射三支強化箭矢，造成%$towers.royal_archers.armor_piercer.damage_min[1]%$-%$towers.royal_archers.armor_piercer.damage_max[1]%$物理傷害，無視敵人%$towers.royal_archers.armor_piercer.armor_penetration[1]%$%護甲。",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_NAME"] = "穿透箭",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_DESCRIPTION"] = "發射三支強化箭矢，造成%$towers.royal_archers.armor_piercer.damage_min[2]%$-%$towers.royal_archers.armor_piercer.damage_max[2]%$物理傷害，無視敵人%$towers.royal_archers.armor_piercer.armor_penetration[2]%$%護甲。",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_NAME"] = "穿透箭",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_DESCRIPTION"] = "發射三支強化箭矢，造成%$towers.royal_archers.armor_piercer.damage_min[3]%$-%$towers.royal_archers.armor_piercer.damage_max[3]%$物理傷害，無視敵人%$towers.royal_archers.armor_piercer.armor_penetration[3]%$%護甲。",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_NAME"] = "穿透箭",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NAME"] = "穿透箭",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NOTE"] = "早就鎖定你了。",
["TOWER_ROYAL_ARCHERS_4_DESCRIPTION"] = "誓死效忠王國的弓兵，會從遠程掩護利尼維亞的軍隊。",
["TOWER_ROYAL_ARCHERS_4_NAME"] = "皇家弓兵 IV",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_DESCRIPTION"] = "召喚一隻戰鷹攻擊路徑上的敵人，造成%$towers.royal_archers.rapacious_hunter.damage_min[1]%$-%$towers.royal_archers.rapacious_hunter.damage_max[1]%$物理傷害。",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_NAME"] = "兇猛獵鷹",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_DESCRIPTION"] = "戰鷹造成%$towers.royal_archers.rapacious_hunter.damage_min[2]%$-%$towers.royal_archers.rapacious_hunter.damage_max[2]%$物理傷害。",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_NAME"] = "兇猛獵鷹",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_DESCRIPTION"] = "戰鷹造成%$towers.royal_archers.rapacious_hunter.damage_min[3]%$-%$towers.royal_archers.rapacious_hunter.damage_max[3]%$物理傷害。",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_NAME"] = "兇猛獵鷹",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NAME"] = "兇猛獵鷹",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NOTE"] = "鷹眼之中醞釀著莫大的悲劇。",
["TOWER_ROYAL_ARCHERS_DESC"] = "王國中最強大的射手，同時也以豢養戰鷹聞名。",
["TOWER_ROYAL_ARCHERS_NAME"] = "皇家弓兵",
["TOWER_SAND_1_DESCRIPTION"] = "他們爐火純青的飛刀功力足以震懾任何目中無人的自傲傭兵。",
["TOWER_SAND_1_NAME"] = "沙丘哨兵 I",
["TOWER_SAND_2_DESCRIPTION"] = "他們爐火純青的飛刀功力足以震懾任何目中無人的自傲傭兵。",
["TOWER_SAND_2_NAME"] = "沙丘哨兵 II",
["TOWER_SAND_3_DESCRIPTION"] = "他們爐火純青的飛刀功力足以震懾任何目中無人的自傲傭兵。",
["TOWER_SAND_3_NAME"] = "沙丘哨兵 III",
["TOWER_SAND_4_DESCRIPTION"] = "他們爐火純青的飛刀功力足以震懾任何目中無人的自傲傭兵。",
["TOWER_SAND_4_NAME"] = "沙丘哨兵 IV",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_DESCRIPTION"] = "向路徑發射數支旋轉飛刀，每秒造成%$towers.sand.skill_big_blade.s_damage_min[1]%$-%$towers.sand.skill_big_blade.s_damage_max[1]%$物理傷害，持續%$towers.sand.skill_big_blade.duration[1]%$秒。",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_NAME"] = "死亡旋刃",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_DESCRIPTION"] = "旋轉飛刀每秒造成%$towers.sand.skill_big_blade.s_damage_min[2]%$-%$towers.sand.skill_big_blade.s_damage_max[2]%$物理傷害，持續%$towers.sand.skill_big_blade.duration[2]%$秒。",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_NAME"] = "死亡旋刃",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_DESCRIPTION"] = "旋轉飛刀每秒造成%$towers.sand.skill_big_blade.s_damage_min[3]%$-%$towers.sand.skill_big_blade.s_damage_max[3]%$物理傷害，持續%$towers.sand.skill_big_blade.duration[3]%$秒。",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_NAME"] = "死亡旋刃",
["TOWER_SAND_4_SKILL_BIG_BLADE_NOTE"] = "妳迷得我團團轉，寶貝。",
["TOWER_SAND_4_SKILL_GOLD_1_DESCRIPTION"] = "投擲一把彈射飛刀，對目標敵人造成%$towers.sand.skill_gold.s_damage[1]%$物理傷害。飛刀殺死的任何目標都會產生%$towers.sand.skill_gold.gold_extra[1]%$額外金幣。",
["TOWER_SAND_4_SKILL_GOLD_1_NAME"] = "賞金狩獵",
["TOWER_SAND_4_SKILL_GOLD_2_DESCRIPTION"] = "飛刀造成%$towers.sand.skill_gold.s_damage[2]%$物理傷害。擊殺可獲得%$towers.sand.skill_gold.gold_extra[2]%$額外金幣。",
["TOWER_SAND_4_SKILL_GOLD_2_NAME"] = "賞金狩獵",
["TOWER_SAND_4_SKILL_GOLD_3_DESCRIPTION"] = "飛刀造成%$towers.sand.skill_gold.s_damage[3]%$物理傷害。擊殺可獲得%$towers.sand.skill_gold.gold_extra[3]%$額外金幣。",
["TOWER_SAND_4_SKILL_GOLD_3_NAME"] = "賞金狩獵",
["TOWER_SAND_4_SKILL_GOLD_NOTE"] = "懸賞有令：死活不論！",
["TOWER_SAND_DESC"] = "從護鎚城到來的沙丘哨兵，可說是沙漠居民中最致命的殺手。",
["TOWER_SAND_NAME"] = "沙丘哨兵",
["TOWER_SELL"] = "出售防禦塔",
["TOWER_SPARKING_GEODE_1_DESCRIPTION"] = "召來雷霆風暴，混沌的製造者。當心其能量消耗。",
["TOWER_SPARKING_GEODE_1_NAME"] = "電湧石魔 I",
["TOWER_SPARKING_GEODE_2_DESCRIPTION"] = "召來雷霆風暴，混沌的製造者。當心其能量消耗。",
["TOWER_SPARKING_GEODE_2_NAME"] = "電湧石魔 II",
["TOWER_SPARKING_GEODE_3_DESCRIPTION"] = "召來雷霆風暴，混沌的製造者。當心其能量消耗。",
["TOWER_SPARKING_GEODE_3_NAME"] = "電湧石魔 III",
["TOWER_SPARKING_GEODE_4_CRISTALIZE"] = "十萬伏特！",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_DESCRIPTION"] = "每%$towers.sparking_geode.crystalize.cooldown[1]%$秒，石魔會使範圍內的%$towers.sparking_geode.crystalize.max_targets[1]%$名敵人結晶化，暈眩並額外承受%$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$%傷害。",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_NAME"] = "結晶化",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_DESCRIPTION"] = "每%$towers.sparking_geode.crystalize.cooldown[2]%$秒，石魔會使範圍內的%$towers.sparking_geode.crystalize.max_targets[2]%$名敵人結晶化，暈眩並額外承受%$towers.sparking_geode.crystalize.s_received_damage_factor[2]%$%傷害。",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_NAME"] = "結晶化",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_DESCRIPTION"] = "每%$towers.sparking_geode.crystalize.cooldown[3]%$秒，石魔會使範圍內的%$towers.sparking_geode.crystalize.max_targets[3]%$名敵人結晶化，讓其暈眩並額外承受%$towers.sparking_geode.crystalize.s_received_damage_factor[3]%$%傷害。",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_NAME"] = "結晶化",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_DESCRIPTION"] = "每%$towers.sparking_geode.crystalize.cooldown[1]%$秒，石魔會使範圍內的%$towers.sparking_geode.crystalize.max_targets[1]%$名敵人結晶化，暈眩並額外承受%$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$%傷害。",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_NAME"] = "結晶化",
["TOWER_SPARKING_GEODE_4_DESCRIPTION"] = "召來雷霆風暴，混沌的製造者。當心其能量消耗。",
["TOWER_SPARKING_GEODE_4_NAME"] = "電湧石魔 IV",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST"] = "努力、精進、更快、更強。",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_DESCRIPTION"] = "每%$towers.sparking_geode.spike_burst.cooldown[1]%$秒，石魔會召喚電氣場地，對周圍的敵人造成傷害與緩速，持續%$towers.sparking_geode.spike_burst.duration[1]%$秒。",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_NAME"] = "電氣湧流",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_DESCRIPTION"] = "每%$towers.sparking_geode.spike_burst.cooldown[2]%$秒，石魔會召喚電氣場地，對周圍的敵人造成傷害與緩速，持續%$towers.sparking_geode.spike_burst.duration[2]%$秒。",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_NAME"] = "電氣湧流",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_DESCRIPTION"] = "每%$towers.sparking_geode.spike_burst.cooldown[3]%$秒，石魔會召喚電氣場地，對周圍的敵人造成傷害與緩速，持續%$towers.sparking_geode.spike_burst.duration[3]%$秒。",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_NAME"] = "電氣湧流",
["TOWER_SPARKING_GEODE_DESC"] = "源自一個愛好和平的古代種族，這尊強大的石魔遵循自身守衛本能而行動，以電閃雷鳴之力與盟軍並肩作戰，降下風暴無情的破壞。",
["TOWER_SPARKING_GEODE_NAME"] = "電湧石魔",
["TOWER_STAGE_13_SUNRAY_NAME"] = "闇夜祭壇",
["TOWER_STAGE_13_SUNRAY_REPAIR_DESCRIPTION"] = "修復防禦塔來使用它的毀滅力量。",
["TOWER_STAGE_13_SUNRAY_REPAIR_NAME"] = "修復",
["TOWER_STAGE_17_WEIRDWOOD_NAME"] = "怪異樹木",
["TOWER_STAGE_18_ELVEN_BARRACK_DESCRIPTION"] = "雇用精靈來為你奮戰至最後一刻。",
["TOWER_STAGE_18_ELVEN_BARRACK_NAME"] = "精靈傭兵",
["TOWER_STAGE_20_ARBOREAN_BARRACK_DESCRIPTION"] = "呼叫樹靈族人參戰。",
["TOWER_STAGE_20_ARBOREAN_BARRACK_NAME"] = "樹靈村民",
["TOWER_STAGE_20_ARBOREAN_HONEY_DESCRIPTION"] = "召喚強大的馭蜂使者。",
["TOWER_STAGE_20_ARBOREAN_HONEY_NAME"] = "樹靈馭蜂使",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_DESCRIPTION"] = "向古老的樹木求援。",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_NAME"] = "古樹",
["TOWER_STAGE_22_ARBOREAN_MAGES_NAME"] = "樹靈法師",
["TOWER_STAGE_28_PRIESTS_BARRACK_DESCRIPTION"] = "痛改前非的教徒。會施展巫術與敵人戰鬥，在死亡時化為怪物。",
["TOWER_STAGE_28_PRIESTS_BARRACK_NAME"] = "無眼的信徒",
["TOWER_STARGAZER_1_DESCRIPTION"] = "星月術士能夠巧妙利用來自地球境外的強大法力。",
["TOWER_STARGAZER_1_NAME"] = "精靈星月術士 I",
["TOWER_STARGAZER_2_DESCRIPTION"] = "星月術士能夠巧妙利用來自地球境外的強大法力。",
["TOWER_STARGAZER_2_NAME"] = "精靈星月術士 II",
["TOWER_STARGAZER_3_DESCRIPTION"] = "星月術士能夠巧妙利用來自地球境外的強大法力。",
["TOWER_STARGAZER_3_NAME"] = "精靈星月術士 III",
["TOWER_STARGAZER_4_DESCRIPTION"] = "星月術士能夠巧妙利用來自地球境外的強大法力。",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_DESCRIPTION"] = "將最多%$towers.elven_stargazers.teleport.max_targets[1]%$名敵人傳送回路徑前段。",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_NAME"] = "事件視界",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_DESCRIPTION"] = "將最多%$towers.elven_stargazers.teleport.max_targets[2]%$名敵人傳送回路徑更前段。",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_NAME"] = "事件視界",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_DESCRIPTION"] = "將最多%$towers.elven_stargazers.teleport.max_targets[3]%$名敵人傳送回更加遙遠的路徑前段。",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_NAME"] = "事件視界",
["TOWER_STARGAZER_4_EVENT_HORIZON_NAME"] = "事件視界",
["TOWER_STARGAZER_4_EVENT_HORIZON_NOTE"] = "來來，去去。",
["TOWER_STARGAZER_4_NAME"] = "精靈星月術士 IV",
["TOWER_STARGAZER_4_RISING_STAR_1_DESCRIPTION"] = "受此塔消滅的敵人會化為%$towers.elven_stargazers.stars_death.stars[1]%$顆星星，並飛向敵軍造成%$towers.elven_stargazers.stars_death.damage_min[1]%$-%$towers.elven_stargazers.stars_death.damage_max[1]%$傷害。",
["TOWER_STARGAZER_4_RISING_STAR_1_NAME"] = "星光湧現",
["TOWER_STARGAZER_4_RISING_STAR_2_DESCRIPTION"] = "星星的數量增加至%$towers.elven_stargazers.stars_death.stars[2]%$，並飛向敵軍造成%$towers.elven_stargazers.stars_death.damage_min[2]%$-%$towers.elven_stargazers.stars_death.damage_max[2]%$魔法傷害。",
["TOWER_STARGAZER_4_RISING_STAR_2_NAME"] = "星光湧現",
["TOWER_STARGAZER_4_RISING_STAR_3_DESCRIPTION"] = "星星的數量增加至%$towers.elven_stargazers.stars_death.stars[3]%$，並飛向敵軍造成%$towers.elven_stargazers.stars_death.damage_min[3]%$-%$towers.elven_stargazers.stars_death.damage_max[3]%$魔法傷害。",
["TOWER_STARGAZER_4_RISING_STAR_3_NAME"] = "星光湧現",
["TOWER_STARGAZER_4_RISING_STAR_NAME"] = "星光湧現",
["TOWER_STARGAZER_4_RISING_STAR_NOTE"] = "星塵革命！",
["TOWER_TRICANNON_1_DESCRIPTION"] = "一首歌頌戰爭的恐怖情歌，歌中描繪的景象無論在敵我眼裡都是人間煉獄。",
["TOWER_TRICANNON_1_NAME"] = "三管加農炮 I",
["TOWER_TRICANNON_2_DESCRIPTION"] = "一首歌頌戰爭的恐怖情歌，歌中描繪的景象無論在敵我眼裡都是人間煉獄。",
["TOWER_TRICANNON_2_NAME"] = "三管加農炮 II",
["TOWER_TRICANNON_3_DESCRIPTION"] = "一首歌頌戰爭的恐怖情歌，歌中描繪的景象無論在敵我眼裡都是人間煉獄。",
["TOWER_TRICANNON_3_NAME"] = "三管加農炮 III",
["TOWER_TRICANNON_4_BOMBARDMENT_1_DESCRIPTION"] = "在廣大的範圍內連續發射炸彈，每顆造成%$towers.tricannon.bombardment.damage_min[1]%$-%$towers.tricannon.bombardment.damage_max[1]%$物理傷害。",
["TOWER_TRICANNON_4_BOMBARDMENT_1_NAME"] = "狂轟濫炸",
["TOWER_TRICANNON_4_BOMBARDMENT_2_DESCRIPTION"] = "在更大的範圍內發射更多炸彈，每顆造成%$towers.tricannon.bombardment.damage_min[2]%$-%$towers.tricannon.bombardment.damage_max[2]%$物理傷害。",
["TOWER_TRICANNON_4_BOMBARDMENT_2_NAME"] = "狂轟濫炸",
["TOWER_TRICANNON_4_BOMBARDMENT_3_DESCRIPTION"] = "在更大的範圍內發射更更多的炸彈，每顆造成%$towers.tricannon.bombardment.damage_min[3]%$-%$towers.tricannon.bombardment.damage_max[3]%$物理傷害。",
["TOWER_TRICANNON_4_BOMBARDMENT_3_NAME"] = "狂轟濫炸",
["TOWER_TRICANNON_4_BOMBARDMENT_NAME"] = "狂轟濫炸",
["TOWER_TRICANNON_4_BOMBARDMENT_NOTE"] = "能屈能伸。",
["TOWER_TRICANNON_4_DESCRIPTION"] = "一首歌頌戰爭的恐怖情歌，歌中描繪的景象無論在敵我眼裡都是人間煉獄。",
["TOWER_TRICANNON_4_NAME"] = "三管加農炮 IV",
["TOWER_TRICANNON_4_OVERHEAT_1_DESCRIPTION"] = "三管加農炮的槍管在%$towers.tricannon.overheat.duration[1]%$秒內變得紅熱，使炸彈燒灼地面，每秒對敵人造成%$towers.tricannon.overheat.decal.effect.s_damage[1]%$真實傷害。",
["TOWER_TRICANNON_4_OVERHEAT_1_NAME"] = "過熱模式",
["TOWER_TRICANNON_4_OVERHEAT_2_DESCRIPTION"] = "每片燃燒區域每秒造成%$towers.tricannon.overheat.decal.effect.s_damage[2]%$真實傷害。持續時間增加為%$towers.tricannon.overheat.duration[2]%$秒。",
["TOWER_TRICANNON_4_OVERHEAT_2_NAME"] = "過熱模式",
["TOWER_TRICANNON_4_OVERHEAT_3_DESCRIPTION"] = "每片燃燒區域每秒造成%$towers.tricannon.overheat.decal.effect.s_damage[3]%$真實傷害。持續時間增加為%$towers.tricannon.overheat.duration[3]%$秒。",
["TOWER_TRICANNON_4_OVERHEAT_3_NAME"] = "過熱模式",
["TOWER_TRICANNON_4_OVERHEAT_NAME"] = "過熱模式",
["TOWER_TRICANNON_4_OVERHEAT_NOTE"] = "嗆辣槍管。",
["TOWER_TRICANNON_DESC"] = "黑暗大軍推出的多管火炮，其空前絕後的火力重新定義了現代戰爭。",
["TOWER_TRICANNON_NAME"] = "三管加農炮",
["TUTORIAL_hero_room_hero_points_desc"] = "在戰鬥中英雄升級時，即可獲得英雄點數。",
["TUTORIAL_hero_room_hero_points_title"] = "英雄點數",
["TUTORIAL_hero_room_power_desc"] = "使用英雄點數購買並升級英雄的能力。",
["TUTORIAL_hero_room_power_title"] = "英雄能力",
["TUTORIAL_hero_room_tutorial_navigate_desc"] = "瀏覽英雄清單。",
["TUTORIAL_hero_room_tutorial_select_desc"] = "裝備您想帶上場的英雄。",
["TUTORIAL_item_room_buy_desc"] = "使用寶石購買道具，在戰場上贏得優勢。",
["TUTORIAL_item_room_buy_title"] = "購買道具",
["TUTORIAL_item_room_tutorial_equip_desc"] = "在各個欄位中裝備道具。拖曳即可更換順序！",
["TUTORIAL_item_room_tutorial_navigate_desc"] = "瀏覽各式可用道具。",
["TUTORIAL_tower_room_power_desc"] = "這些技能將於防禦塔達到4級時開放。",
["TUTORIAL_tower_room_power_title"] = "4級技能",
["TUTORIAL_tower_room_tutorial_equip_desc"] = "裝備新的防禦塔，測試各種組合。",
["TUTORIAL_tower_room_tutorial_navigate_desc"] = "瀏覽防禦塔清單。",
["TUTORIAL_tower_room_tutorial_slots_desc"] = "在各個欄位中裝備防禦塔。拖曳即可更換順序！",
["TUTORIAL_upgrade_room_tooltip_buy_desc"] = "使用點數升級能力、防禦塔及英雄。",
["TUTORIAL_upgrade_room_tooltip_souls_desc"] = "完成戰役關卡可獲得升級點數。",
["TUTORIAL_upgrade_room_tooltip_souls_title"] = "升級點數",
["Tap the road!"] = "點擊道路！",
["Tip"] = "提示",
["Tower construction"] = "防禦塔建造",
["Towers"] = "防禦塔",
["Try again"] = "重試",
["Typography"] = "排版",
["UPDATE_POPUP"] = "更新",
["UPDATING_CLOUDSAVE_MESSAGE"] = "更新雲端存檔...",
["UPGRADES"] = "升級",
["UPGRADES AND HEROES RESTRICTIONS!"] = "升級與英雄限制！",
["UPGRADE_LEVEL"] = "提升等級",
["Undo"] = "取消",
["Unlocks at Level"] = "解鎖等級：",
["Upgrades"] = "升級",
["Use the earned hero points to train your hero!"] = "用獲得的英雄點數來訓練英雄！",
["Use the earned stars to improve your towers and powers!"] = "用獲得的星星改良防禦塔和提升戰力！",
["VICTORY"] = "勝利",
["Very fast"] = "飛快",
["Very slow"] = "非常慢",
["Veteran"] = "老兵",
["Victory!"] = "勝利！",
["Voice Talent"] = "配音",
["WARNING"] = "警告",
["WAVE_TOOLTIP_TAP_AGAIN"] = "按一下，提前召喚下一波",
["WAVE_TOOLTIP_TITLE"] = "一波敵人即將到來",
["We would like to thank"] = "特別鳴謝",
["Yes"] = "好",
["You can always change the difficulty in the options menu."] = "你可以隨時在選項功能表中更改難度。",
["_manually_included_characters"] = "$ ¥ ￥ ƒ ₩ € ™ × $ zł ¢ £ ¤ ¥ ƒ ден дин лв. ؋ ৳ ฿ ლ ₡ ₣ ₤ ₥ ₦ ₨ ₩ ₪ ₫ € ₭ ₮ ₱ ₲ ₴ ₵ ₹ ₺ ₽ ﷼",
["alliance_close_to_home_DESCRIPTION"] = "在關卡開始時提供額外金幣。",
["alliance_close_to_home_NAME"] = "共享儲蓄",
["alliance_corageous_stand_DESCRIPTION"] = "每建造一座利尼維亞的防禦塔都會增加英雄的生命值。",
["alliance_corageous_stand_NAME"] = "英勇挺立",
["alliance_display_of_true_might_dark_DESCRIPTION"] = "黑暗大軍的英雄奧義，現在會緩速畫面中的所有敵人。",
["alliance_display_of_true_might_dark_NAME"] = "災厄詛咒",
["alliance_display_of_true_might_linirea_DESCRIPTION"] = "利尼維亞的英雄奧義，現在會治療並復活所有的友軍單位。",
["alliance_display_of_true_might_linirea_NAME"] = "生命賜福",
["alliance_flux_altering_coils_DESCRIPTION"] = "把所有出口旗幟替換為奧術法柱，可將周圍的敵人往回傳送。",
["alliance_flux_altering_coils_NAME"] = "奧術法柱",
["alliance_friends_of_the_crown_DESCRIPTION"] = "每帶上一名利尼維亞的英雄都會降低建造和升級防禦塔的費用。",
["alliance_friends_of_the_crown_NAME"] = "王國之友",
["alliance_merciless_DESCRIPTION"] = "每建造一座黑暗大軍的防禦塔都會增加英雄的攻擊傷害。",
["alliance_merciless_NAME"] = "無情陣線",
["alliance_seal_of_punishment_DESCRIPTION"] = "將防禦點替換為魔法印記，對行經的敵人造成傷害。",
["alliance_seal_of_punishment_NAME"] = "懲戒印記",
["alliance_shady_company_DESCRIPTION"] = "每帶上一名黑暗大軍的英雄都會增加防禦塔的攻擊傷害。",
["alliance_shady_company_NAME"] = "暗影同盟",
["alliance_shared_reserves_DESCRIPTION"] = "在關卡開始時提供額外金幣。",
["alliance_shared_reserves_NAME"] = "共享儲蓄",
["build defensive towers along the road to stop them."] = "沿著道路建造防禦塔來阻止他們。",
["build towers to defend the road."] = "建造防禦塔來守衛道路。",
["check the stage description to see:"] = "查閱關卡描述來看：",
["click these!"] = "按一下這些！",
["click to continue..."] = "按一下以繼續...",
["deals area damage"] = "造成範圍傷害",
["don't let enemies past this point."] = "不要讓敵人通過這個點。",
["earn gold by killing enemies."] = "消滅敵人來獲取金幣。",
["good rate of fire"] = "良好的射速",
["heroes_desperate_effort_DESCRIPTION"] = "英雄的攻擊無視敵人10%抗性。",
["heroes_desperate_effort_NAME"] = "知己知彼",
["heroes_lethal_focus_DESCRIPTION"] = "英雄的攻擊有20%會造成暴擊傷害。",
["heroes_lethal_focus_NAME"] = "弱點打擊",
["heroes_limit_pushing_DESCRIPTION"] = "每個英雄奧義在使用五次後，其冷卻時間將立即重置。",
["heroes_limit_pushing_NAME"] = "超越極限",
["heroes_lone_wolves_DESCRIPTION"] = "英雄位置分散時能獲得更多經驗。",
["heroes_lone_wolves_NAME"] = "孤傲英雄",
["heroes_nimble_physique_DESCRIPTION"] = "英雄可閃避20%的敵方攻擊。",
["heroes_nimble_physique_NAME"] = "身手矯健",
["heroes_unlimited_vigor_DESCRIPTION"] = "減少所有英雄奧義冷卻時間10%。",
["heroes_unlimited_vigor_NAME"] = "活力無限",
["heroes_visual_learning_DESCRIPTION"] = "當彼此靠近時，英雄們獲得10%額外護甲。",
["heroes_visual_learning_NAME"] = "並肩作戰",
["high damage, armor piercing"] = "傷害高，護甲穿透",
["iron and heroic challenges may have restrictions on upgrades!"] = "鋼鐵和英雄挑戰會有升級限制！",
["max lvl allowed"] = "最大等級限制",
["multi-shot, armor piercing"] = "多重射擊，護甲穿刺",
["no heroes"] = "無英雄",
["protect your lands from the enemy attacks."] = "抵禦敵軍攻擊，保護你的土地！",
["rally range"] = "集結範圍",
["ready for action!"] = "準備就緒！",
["reinforcements_intense_workout_DESCRIPTION"] = "提升援兵的生命值及存在時間。",
["reinforcements_intense_workout_NAME"] = "強化特訓",
["reinforcements_master_blacksmiths_DESCRIPTION"] = "提升援兵的攻擊傷害及護甲值。",
["reinforcements_master_blacksmiths_NAME"] = "鍛造大師",
["reinforcements_night_veil_DESCRIPTION"] = "增加暗影弓箭手的射程和攻速。",
["reinforcements_night_veil_NAME"] = "灰燼之弓",
["reinforcements_power_trio_DESCRIPTION"] = "呼叫援兵的同時也會派出一名騎士楷模。",
["reinforcements_power_trio_NAME"] = "利尼維亞楷模",
["reinforcements_power_trio_dark_DESCRIPTION"] = "呼叫援兵的同時也會派出一名暗影喚鴉者。",
["reinforcements_power_trio_dark_NAME"] = "暗影喚鴉者",
["reinforcements_rebel_militia_DESCRIPTION"] = "援兵替換為利尼為亞義勇軍，他們是身著重甲的結實戰士。",
["reinforcements_rebel_militia_NAME"] = "利尼維亞義勇軍",
["reinforcements_shadow_archer_DESCRIPTION"] = "援兵替換為暗影弓箭手，能夠從遠處攻擊與瞄準飛行單位。",
["reinforcements_shadow_archer_NAME"] = "暗影部隊",
["reinforcements_thorny_armor_DESCRIPTION"] = "利尼維亞叛軍能夠反射部分敵方近戰攻擊傷害。",
["reinforcements_thorny_armor_NAME"] = "尖刺護甲",
["resist damage from"] = "抵抗傷害來源：",
["resists damage from"] = "抵抗傷害來源：",
["select the rally point control"] = "選擇集結點控制",
["select the tower you want to build!"] = "選擇你想建造的防禦塔",
["select where you want to move your soldiers"] = "選擇你想讓士兵前往的位置",
["soldiers block enemies"] = "士兵能攔截敵人",
["some enemies enjoy different levels of magic resistance that protects them against magical attacks."] = "有些敵人具備不同程度的魔法抗性，讓他們承受更少的魔法傷害。",
["some enemies wear armor of different strengths that protects them against non-magical attacks."] = "有些敵人身披不同強度的護甲，讓他們承受更少的非魔法傷害。",
["tap these!"] = "點擊這些！",
["this is a strategic point."] = "這是一個戰略點。",
["towers_favorite_customer_DESCRIPTION"] = "購買技能的最終等級時，將其費用降低50%。",
["towers_favorite_customer_NAME"] = "貴賓禮遇",
["towers_golden_time_DESCRIPTION"] = "增加提早召喚下一波敵人獲得的金幣獎勵。",
["towers_golden_time_NAME"] = "黃金時間",
["towers_improved_formulas_DESCRIPTION"] = "將所有來自塔的爆炸傷害最大化，並增加其爆炸範圍。",
["towers_improved_formulas_NAME"] = "改良配方",
["towers_keen_accuracy_DESCRIPTION"] = "所有防禦塔的技能冷卻時間減少20%。",
["towers_keen_accuracy_NAME"] = "戰鬥狂",
["towers_royal_training_DESCRIPTION"] = "減少防禦塔單位的生成時間和援兵的冷卻時間。",
["towers_royal_training_NAME"] = "全面動員",
["towers_scoping_mechanism_DESCRIPTION"] = "所有塔的攻擊範圍增加10%。",
["towers_scoping_mechanism_NAME"] = "瞄準裝置",
["towers_war_rations_DESCRIPTION"] = "所有塔單位的生命值提高10%。",
["towers_war_rations_NAME"] = "作戰口糧",
["towers_wise_investment_DESCRIPTION"] = "出售防禦塔時會返還其成本的90%。",
["towers_wise_investment_NAME"] = "明智投資",
["wOOt!"] = "讚讚讚！",
["you can adjust your soldiers rally point to make them defend a different area."] = "你可以更改士兵的集結點，讓他們防禦不同的區域。",
}
