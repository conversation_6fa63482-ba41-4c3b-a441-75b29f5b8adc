-- ------------------------------------------------
-- -- WARNING: DO NOT EDIT BY HAND                 
-- -- Generated by kr-i18n/tools/strings-export.lua
-- ------------------------------------------------
return {
["!!!COMMENT_LOCALIZATION_SOURCE"] = "billy",
["%d Life"] = "%d vida",
["%d Lives"] = "%d vidas",
["%i sec."] = "%i seg.",
["- if heroes are allowed"] = "- si los héroes están permitidos",
["- max lvl allowed"] = "- nivel máximo permitido",
["- max upgrade level allowed"] = "- nivel máximo de mejoras permitido",
["- no heroes"] = "- sin héroes",
["A good challenge!"] = "¡Un buen desafío!",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "Willy Abominado",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "Henry Abominado",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "Geoffrey Abominado",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "Nicholas Estreñido",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "Edominación",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "Hobominación",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "Odominación",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "Cedric Abominado",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "Halbominación",
["ACHIEVEMENT"] = "LOGRO",
["ACHIEVEMENTS"] = "LOGROS",
["ACHIEVEMENTS_TITLE"] = "LOGROS",
["ACHIEVEMENT_AGE_OF_HEROES_DESCRIPTION"] = "Gana todos los niveles de desafío Heróico.",
["ACHIEVEMENT_AGE_OF_HEROES_NAME"] = "Edad Heróica",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_DESCRIPTION"] = "Elimina a 182 Pestañeantes.",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_NAME"] = "Todas Las Cosas Pequeñas",
["ACHIEVEMENT_ARACHNED_DESCRIPTION"] = "Derrota a Mygale, la Reina Araña.",
["ACHIEVEMENT_ARACHNED_NAME"] = "Un adiós a las armas",
["ACHIEVEMENT_A_COON_OF_SURPRISES_DESCRIPTION"] = "Ayuda a Fredo a escapar.",
["ACHIEVEMENT_A_COON_OF_SURPRISES_NAME"] = "Un capullo de sorpresas",
["ACHIEVEMENT_A_TEST_OF_PROWESS_DESCRIPTION"] = "Termina un nivel con 3 estrellas.",
["ACHIEVEMENT_A_TEST_OF_PROWESS_NAME"] = "Prueba de Destreza",
["ACHIEVEMENT_BREAKER_OF_CHAINS_DESCRIPTION"] = "Salva a los cuatro elfos en las Minas Carmines.",
["ACHIEVEMENT_BREAKER_OF_CHAINS_NAME"] = "Rompecadenas",
["ACHIEVEMENT_BUTTERTENTACLES_DESCRIPTION"] = "Completa la Torre Abominable sin que Mydrias atrape a ninguna de tus unidades.",
["ACHIEVEMENT_BUTTERTENTACLES_NAME"] = "Soldados Escurridizos",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_DESCRIPTION"] = "Derrota a la Vidente Mydrias.",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_NAME"] = "Adiós, Adiós, Hermosa",
["ACHIEVEMENT_CIRCLE_OF_LIFE_DESCRIPTION"] = "Asiste a la presentación del Arborean recién nacido.",
["ACHIEVEMENT_CIRCLE_OF_LIFE_NAME"] = "El Cíclo de la Vida",
["ACHIEVEMENT_CLEANSE_THE_KING_DESCRIPTION"] = "Rescata al Rey de Linirea.",
["ACHIEVEMENT_CLEANSE_THE_KING_NAME"] = "Gloria al Rey",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_DESCRIPTION"] = "Completa el Linde Desolado sin limpiar escombros de los puntos estratégicos.",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_NAME"] = "La Limpieza es Opcional",
["ACHIEVEMENT_CONJUNTIVICTORY_DESCRIPTION"] = "Derrota al Omnividente.",
["ACHIEVEMENT_CONJUNTIVICTORY_NAME"] = "Conjuntivictoria",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_DESCRIPTION"] = "Obtén 3 estrellas en todos los niveles del Vacío Más Allá.",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_NAME"] = "Conquistador del Vacío",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_DESCRIPTION"] = "Recolecta las tres chuletas de puerco en la Guarida de las Bestias Salvajes.",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_NAME"] = "Manualidades en las Minas",
["ACHIEVEMENT_CROWD_CONTROL_DESCRIPTION"] = "Completa el Valle de la Corrupción sin que ningún Behemoth salga del estanque.",
["ACHIEVEMENT_CROWD_CONTROL_NAME"] = "Control de Masas",
["ACHIEVEMENT_CROW_SCARER_DESCRIPTION"] = "Asusta a todos los cuervos en el Valle Lúgubre.",
["ACHIEVEMENT_CROW_SCARER_NAME"] = "Asustador de Cuervos",
["ACHIEVEMENT_CRYSTAL_CLEAR_DESCRIPTION"] = "Obtén 3 estrellas en cada nivel del Cañón Abandonado.",
["ACHIEVEMENT_CRYSTAL_CLEAR_NAME"] = "Claro Como El Cristal",
["ACHIEVEMENT_DARK_LIEUTENANT_DESCRIPTION"] = "Alcanza el nivel 10 con Raelyn.",
["ACHIEVEMENT_DARK_LIEUTENANT_NAME"] = "Teniente Oscura",
["ACHIEVEMENT_DARK_RUTHLESSNESS_DESCRIPTION"] = "Completa un nivel utilizando solo torres y héroes del Ejército Oscuro.",
["ACHIEVEMENT_DARK_RUTHLESSNESS_NAME"] = "Crueldad Oscura",
["ACHIEVEMENT_DISTURBING_THE_PEACE_DESCRIPTION"] = "Interrumpe el descanso de los trabajores en Domo de Dominación.",
["ACHIEVEMENT_DISTURBING_THE_PEACE_NAME"] = "Perturbando la Paz",
["ACHIEVEMENT_DLC1_WIN_BOSS_DESCRIPTION"] = "Derrota a Grymbeard y detén la construcción de la máquina de guerra.",
["ACHIEVEMENT_DLC1_WIN_BOSS_NAME"] = "Despido Masivo",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_DESCRIPTION"] = "Recoge 8 hongbaos en la Isla Tempestad.",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_NAME"] = "Les deseamos riqueza y prosperidad.",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_DESCRIPTION"] = "Derrota al Rey Demonio Toro en su fortaleza.",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_NAME"] = "El Regreso del Rey Mono",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_DESCRIPTION"] = "Derrota a la Princesa Abanico de Hierro y su ejército acuático.",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_NAME"] = "Un Viento Maligno se Alza",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_DESCRIPTION"] = "Derrota al Chico Rojo y a su ejército de fuego.",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_NAME"] = "Todo cambió...",
["ACHIEVEMENT_DOMO_ARIGATO_DESCRIPTION"] = "Haz que el puño gigante aplaste 20 enemigos en Núcleo Colosal.",
["ACHIEVEMENT_DOMO_ARIGATO_NAME"] = "Domo Arigato",
["ACHIEVEMENT_FACTORY_STRIKE_DESCRIPTION"] = "Completa Ensamblaje Frenético sin dejar que Grymbeard opere la maquinaria.",
["ACHIEVEMENT_FACTORY_STRIKE_NAME"] = "Huelga de Fábrica",
["ACHIEVEMENT_FIELD_TRIP_RUINER_DESCRIPTION"] = "Apaga el fuego del campista.",
["ACHIEVEMENT_FIELD_TRIP_RUINER_NAME"] = "Arruina Paseos",
["ACHIEVEMENT_FOREST_PROTECTOR_DESCRIPTION"] = "Alcanza el nivel 10 con Nyru.",
["ACHIEVEMENT_FOREST_PROTECTOR_NAME"] = "Protector del Bosque",
["ACHIEVEMENT_GARBAGE_DISPOSAL_DESCRIPTION"] = "Elimina 10 Mecánicos Locos antes de que creen Drones de Chatarra.",
["ACHIEVEMENT_GARBAGE_DISPOSAL_NAME"] = "Eliminación de Basura",
["ACHIEVEMENT_GEM_SPILLER_DESCRIPTION"] = "Rompe todas las canastas de gemas.",
["ACHIEVEMENT_GEM_SPILLER_NAME"] = "Desparra Gemas",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_DESCRIPTION"] = "Resuelve el puzle y llama a la banda.",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_NAME"] = "Que Empiece la Fiesta",
["ACHIEVEMENT_GIFT_OF_LIFE_DESCRIPTION"] = "Libera al clon experimental en Cámara Replicadora.",
["ACHIEVEMENT_GIFT_OF_LIFE_NAME"] = "El Don de la Vida",
["ACHIEVEMENT_GREENLIT_ALLIES_DESCRIPTION"] = "Invoca a 10 Lanzaespinosas Arborean.",
["ACHIEVEMENT_GREENLIT_ALLIES_NAME"] = "Aliados Autorizados",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_DESCRIPTION"] = "Encuentra al rey cocodrilo.",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_NAME"] = "Gloria al K, bebé!",
["ACHIEVEMENT_HEARTLESS_VICTORY_DESCRIPTION"] = "Completa el Corazón del Bosque sin usar el poder del Corazón de los Arborean.",
["ACHIEVEMENT_HEARTLESS_VICTORY_NAME"] = "Victoria Descorazonada",
["ACHIEVEMENT_INTO_THE_OGREVERSE_DESCRIPTION"] = "Descubre los secretos de la misteriosa persona araña.",
["ACHIEVEMENT_INTO_THE_OGREVERSE_NAME"] = "Vecino hostil",
["ACHIEVEMENT_IRONCLAD_DESCRIPTION"] = "Gana todos los niveles de desafío de Hierro.",
["ACHIEVEMENT_IRONCLAD_NAME"] = "Acorazado",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_DESCRIPTION"] = "Ayuda a Lank a pescar 5 rupias.",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_NAME"] = "Es Un Secreto Que Nadie Conoce",
["ACHIEVEMENT_KEPT_YOU_WAITING_DESCRIPTION"] = "Encuentra al soldado sigiloso en Núcleo Colosal.",
["ACHIEVEMENT_KEPT_YOU_WAITING_NAME"] = "Te Hice Esperar, ¿Eh?",
["ACHIEVEMENT_LEARNING_THE_ROPES_DESCRIPTION"] = "Termina el Tutorial consiguiendo 3 estrellas.",
["ACHIEVEMENT_LEARNING_THE_ROPES_NAME"] = "Aprendiendo cómo se hace",
["ACHIEVEMENT_LINIREAN_RESISTANCE_DESCRIPTION"] = "Completa un nivel utilizando solo torres y héroes de Linirea.",
["ACHIEVEMENT_LINIREAN_RESISTANCE_NAME"] = "Resistencia Linireana",
["ACHIEVEMENT_LUCAS_SPIDER_DESCRIPTION"] = "Juega con Lucus hasta que esté feliz.",
["ACHIEVEMENT_LUCAS_SPIDER_NAME"] = "Lucus la Araña",
["ACHIEVEMENT_MASTER_TACTICIAN_DESCRIPTION"] = "Completa la campaña en la dificultad Imposible.",
["ACHIEVEMENT_MASTER_TACTICIAN_NAME"] = "Maestro Estratega",
["ACHIEVEMENT_MECHANICAL_BURNOUT_DESCRIPTION"] = "Alimenta de más la máquina en Puertas de la Forja.",
["ACHIEVEMENT_MECHANICAL_BURNOUT_NAME"] = "Agotamiento Mecánico",
["ACHIEVEMENT_MIGHTY_III_DESCRIPTION"] = "Mata a 10000 enemigos.",
["ACHIEVEMENT_MIGHTY_III_NAME"] = "Poderoso III",
["ACHIEVEMENT_MIGHTY_II_DESCRIPTION"] = "Mata a 3000 enemigos.",
["ACHIEVEMENT_MIGHTY_II_NAME"] = "Poderoso II",
["ACHIEVEMENT_MIGHTY_I_DESCRIPTION"] = "Mata a 500 enemigos.",
["ACHIEVEMENT_MIGHTY_I_NAME"] = "Poderoso I",
["ACHIEVEMENT_MOST_DELICIOUS_DESCRIPTION"] = "Dale un poco de miel a Biggie el Arborean.",
["ACHIEVEMENT_MOST_DELICIOUS_NAME"] = "Muy Delicioso",
["ACHIEVEMENT_NATURES_WRATH_DESCRIPTION"] = "Acaba con 30 enemigos utilizando el Corazón de los Arborean.",
["ACHIEVEMENT_NATURES_WRATH_NAME"] = "Ira de la Naturaleza",
["ACHIEVEMENT_NONE_SHALL_PASS_DESCRIPTION"] = "Completa la Guarida de las Bestias Salvajes sin dejar que pasen enemigos por la puerta levadiza.",
["ACHIEVEMENT_NONE_SHALL_PASS_NAME"] = "¡Nadie Pasará!",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_DESCRIPTION"] = "Adelanta 15 oleadas enemigas.",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_NAME"] = "No Pierdas Ni Un Segundo",
["ACHIEVEMENT_NO_FLY_ZONE_DESCRIPTION"] = "Mata 50 Arañas Planeadoras.",
["ACHIEVEMENT_NO_FLY_ZONE_NAME"] = "Zona de exclusión aérea",
["ACHIEVEMENT_OBLITERATE_DESCRIPTION"] = "Encuentra las partes del robot prohibido en cada escenario de Amenaza Colosal.",
["ACHIEVEMENT_OBLITERATE_NAME"] = "¡Manifiestate!",
["ACHIEVEMENT_ONE_SHOT_TOWER_DESCRIPTION"] = "Elimina a 10 enemigos con un solo disparo de la Torre Rayoscuro.",
["ACHIEVEMENT_ONE_SHOT_TOWER_NAME"] = "Única Oportunidad para la Gloria",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_DESCRIPTION"] = "Derrota a Goregrind antes de que salte por primera vez en la dificultad Imposible.",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_NAME"] = "Enredado",
["ACHIEVEMENT_OVER_THE_EDGE_DESCRIPTION"] = "Empuja a los Arborean de la copa de los árboles.",
["ACHIEVEMENT_OVER_THE_EDGE_NAME"] = "Juego Terminado",
["ACHIEVEMENT_OVINE_JOURNALISM_DESCRIPTION"] = "Encuentra a Sheepy en cada terreno de la campaña.",
["ACHIEVEMENT_OVINE_JOURNALISM_NAME"] = "Periodismo Ovino",
["ACHIEVEMENT_PEST_CONTROL_DESCRIPTION"] = "Mata a 300 Glarelings.",
["ACHIEVEMENT_PEST_CONTROL_NAME"] = "Control de Plagas",
["ACHIEVEMENT_PLAYFUL_FRIENDS_DESCRIPTION"] = "Juega a \"excavar\" con todos los Arboreans en el Corazón del Bosque.",
["ACHIEVEMENT_PLAYFUL_FRIENDS_NAME"] = "Amigos Divertidos",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_DESCRIPTION"] = "Derrota a Goregrind.",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_NAME"] = "Menú Sin Puerco",
["ACHIEVEMENT_PROMOTION_DENIED_DESCRIPTION"] = "Mata a 30 Sacerdotes del Culto antes de que se transformen en Abominaciones.",
["ACHIEVEMENT_PROMOTION_DENIED_NAME"] = "Ascenso Denegado",
["ACHIEVEMENT_ROCK_BEATS_ROCK_DESCRIPTION"] = "Haz que la estatua se derrote a sí misma.",
["ACHIEVEMENT_ROCK_BEATS_ROCK_NAME"] = "Piedra Vence a... Piedra?",
["ACHIEVEMENT_ROOM_achievement_claim"] = "¡Reclamar recompensa!",
["ACHIEVEMENT_ROYAL_CAPTAIN_DESCRIPTION"] = "Alcanza el nivel 10 con Vesper.",
["ACHIEVEMENT_ROYAL_CAPTAIN_NAME"] = "Capitán Real",
["ACHIEVEMENT_RUNEQUEST_DESCRIPTION"] = "Activa las seis runas del Bosque Radiante.",
["ACHIEVEMENT_RUNEQUEST_NAME"] = "Runequest",
["ACHIEVEMENT_RUST_IN_PEACE_DESCRIPTION"] = "Completa un nivel sin permitir que ninguna Armadura Animada reviva.",
["ACHIEVEMENT_RUST_IN_PEACE_NAME"] = "Oxídate en Paz",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_DESCRIPTION"] = "Gana la etapa sin perder ninguna flor arbórea.",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_NAME"] = "Salvador del bosque",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_DESCRIPTION"] = "Obtén 3 estrellas en cada nivel del Bosque Radiante.",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_NAME"] = "Salvador de lo Verde",
["ACHIEVEMENT_SCRAMBLED_EGGS_DESCRIPTION"] = "Mata a 50 crokinder antes de que eclosionen.",
["ACHIEVEMENT_SCRAMBLED_EGGS_NAME"] = "Huevos revueltos",
["ACHIEVEMENT_SEASONED_GENERAL_DESCRIPTION"] = "Completa la campaña en la dificultad Veterano.",
["ACHIEVEMENT_SEASONED_GENERAL_NAME"] = "General Avezado",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_DESCRIPTION"] = "Derrota a Abominor, el devorador.",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_NAME"] = "Hablamos en un rato lagarto",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_DESCRIPTION"] = "Completa Domo de Dominación evitando que Grymbeard incinere tus torres.",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_NAME"] = "¡Cierra Esa Boca!",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_DESCRIPTION"] = "Usa los Poderes de Héroe 500 veces.",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_NAME"] = "Técnicas Distintivas",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_DESCRIPTION"] = "Ayuda a Gerhart a acabar con el monstruo del árbol.",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_NAME"] = "Plata para los Monstruos",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_DESCRIPTION"] = "Ayuda al amigable caimán a arrancar su bote.",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_NAME"] = "Principe En-caiman-dor",
["ACHIEVEMENT_SPECTRAL_FURY_DESCRIPTION"] = "Derrota a Navira e impide la invasión de Apariciones.",
["ACHIEVEMENT_SPECTRAL_FURY_NAME"] = "Furia Espectral",
["ACHIEVEMENT_STARLIGHT_DESCRIPTION"] = "Ayuda a Fredo y Sammy a escapar de la Araña Gigante.",
["ACHIEVEMENT_STARLIGHT_NAME"] = "Luz Estelar",
["ACHIEVEMENT_TAKE_ME_HOME_DESCRIPTION"] = "Lleva a Riff, el goblin, de vuelta a su dimensión.",
["ACHIEVEMENT_TAKE_ME_HOME_NAME"] = "Take On Me",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_DESCRIPTION"] = "Invoca 1000 refuerzos.",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_NAME"] = "¡Llegó la caballería!",
["ACHIEVEMENT_TIPPING_THE_SCALES_DESCRIPTION"] = "Tira a Robin Wood al río.",
["ACHIEVEMENT_TIPPING_THE_SCALES_NAME"] = "Inclinando la Balanza",
["ACHIEVEMENT_TREE_HUGGER_DESCRIPTION"] = "Completa las Ruinas Brumosas con al menos un Bosquextraño en pie.",
["ACHIEVEMENT_TREE_HUGGER_NAME"] = "Amante de los Árboles",
["ACHIEVEMENT_TURN_A_BLIND_EYE_DESCRIPTION"] = "Mata a 100 Engendros Corruptos mientras están bajo la influencia del Resplandor.",
["ACHIEVEMENT_TURN_A_BLIND_EYE_NAME"] = "Haz la Vista Gorda",
["ACHIEVEMENT_UNBOUND_VICTORY_DESCRIPTION"] = "Completa el Cruce Retorcido sin que ninguna Pesadilla se convierta en Pesadilla Amarrada.",
["ACHIEVEMENT_UNBOUND_VICTORY_NAME"] = "Victoria Desatada",
["ACHIEVEMENT_UNENDING_RICHES_DESCRIPTION"] = "Recolecta un total de 150000 oro.",
["ACHIEVEMENT_UNENDING_RICHES_NAME"] = "Riqueza Sin Límites",
["ACHIEVEMENT_UNTAMED_BEAST_DESCRIPTION"] = "Alcanza el nivel 10 con Grimson.",
["ACHIEVEMENT_UNTAMED_BEAST_NAME"] = "Bestia Indomable",
["ACHIEVEMENT_WAR_MASONRY_DESCRIPTION"] = "Construye 100 torres.",
["ACHIEVEMENT_WAR_MASONRY_NAME"] = "Albañilería de Guerra",
["ACHIEVEMENT_WEIRDER_THINGS_DESCRIPTION"] = "Ayuda a Ernie y Daston ahuyentando a los Pestañeadores en las Planicies Corruptas.",
["ACHIEVEMENT_WEIRDER_THINGS_NAME"] = "Cosas más extrañas",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_DESCRIPTION"] = "Encuentra al gato escurridizo en cada nivel de la campaña de Furia Eterna.",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_NAME"] = "Aquí Estamos Todos Locos",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_DESCRIPTION"] = "Mata a 15 Hermanas Retorcidas antes de que puedan invocar a una Pesadilla.",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_NAME"] = "No Vamos a Tolerarlo",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_DESCRIPTION"] = "Repara el arma de portales de Nick y Marty.",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_NAME"] = "Wobba-Lubba-Dub-Dub!",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_DESCRIPTION"] = "Salva al Rey Denas sin dejar que la Vidente Mydrias invoque a sus proyecciones en la dificultad Imposible.",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_NAME"] = "¡No vas a lanzar hechizos!",
["ADS_MESSAGE_OK"] = "Aceptar",
["ADS_MESSAGE_TITLE"] = "MÁS GEMAS",
["ALERT_VERSION"] = "Una nueva versión del juego está disponible. Por favor descárgalo de la tienda.",
["APPLY_SETTINGS_AND_RESTART"] = "¿Reiniciar para aplicar los cambios?",
["ARCHER TOWER"] = "TORRE DE ARQUEROS",
["ARE YOU SURE YOU WANT TO QUIT?"] = "¿SEGURO QUE DESEAS SALIR?",
["ARMORED ENEMIES!"] = "¡ENEMIGOS CON ARMADURA!",
["ARTILLERY"] = "ARTILLERÍA",
["Achievements"] = "Logros",
["Advanced"] = "Avanzado",
["Average"] = "Medio",
["BARRACKS"] = "BARRACAS",
["BOSS_BULL_KING_NAME"] = "Rey Demonio Toro",
["BOSS_CORRUPTED_DENAS_DESCRIPTION"] = "El destronado Rey de Linirea, convertido en una imponente abominación por los oscuros poderes del Culto del Omnividente.",
["BOSS_CORRUPTED_DENAS_EXTRA"] = "- Engendra Glarelings",
["BOSS_CORRUPTED_DENAS_NAME"] = "Denas Corrupto",
["BOSS_CROCS_DESCRIPTION"] = "El hambre personificada, Un ser antiguo capaz de devorar el mundo mismo si se deja sin control.",
["BOSS_CROCS_EXTRA"] = "- Come torres\n- Evoluciona después de satisfacer su hambre\n- Invoca a corkinders",
["BOSS_CROCS_LVL1_DESCRIPTION"] = "El hambre personificada, Un ser antiguo capaz de devorar el mundo mismo si se deja sin control.",
["BOSS_CROCS_LVL1_EXTRA"] = "- Come torres\n- Evoluciona después de satisfacer su hambre\n- Invoca a corkinders",
["BOSS_CROCS_LVL1_NAME"] = "Abominor",
["BOSS_CROCS_LVL2_DESCRIPTION"] = "El hambre personificada, Un ser antiguo capaz de devorar el mundo mismo si se deja sin control.",
["BOSS_CROCS_LVL2_EXTRA"] = "- Come torres\n- Evoluciona después de satisfacer su hambre\n- Invoca a corkinders",
["BOSS_CROCS_LVL2_NAME"] = "Abominor",
["BOSS_CROCS_LVL3_DESCRIPTION"] = "El hambre personificada, Un ser antiguo capaz de devorar el mundo mismo si se deja sin control.",
["BOSS_CROCS_LVL3_EXTRA"] = "- Come torres\n- Evoluciona después de satisfacer su hambre\n- Invoca a corkinders",
["BOSS_CROCS_LVL3_NAME"] = "Abominor",
["BOSS_CROCS_LVL4_DESCRIPTION"] = "El hambre personificada, Un ser antiguo capaz de devorar el mundo mismo si se deja sin control.",
["BOSS_CROCS_LVL4_EXTRA"] = "- Come torres\n- Evoluciona después de satisfacer su hambre\n- Invoca a corkinders",
["BOSS_CROCS_LVL4_NAME"] = "Abominor",
["BOSS_CROCS_LVL5_DESCRIPTION"] = "El hambre personificada, Un ser antiguo capaz de devorar el mundo mismo si se deja sin control.",
["BOSS_CROCS_LVL5_EXTRA"] = "- Come torres\n- Evoluciona después de satisfacer su hambre\n- Invoca a corkinders",
["BOSS_CROCS_LVL5_NAME"] = "Abominor",
["BOSS_CROCS_NAME"] = "Abominor",
["BOSS_CULT_LEADER_DESCRIPTION"] = "La líder actual del Culto, Mydrias, es la mano derecha del Overseer, orquestando las invasiones a otros mundos.",
["BOSS_CULT_LEADER_EXTRA"] = "- Alta armadura y resistencia mágica mientras no está siendo bloqueada\n - Daño en área alto",
["BOSS_CULT_LEADER_NAME"] = "Seeress Mydrias",
["BOSS_GRYMBEARD_DESCRIPTION"] = "Un enano megalomaníaco con delirios de grandeza que es tan peligroso como desquiciado.",
["BOSS_GRYMBEARD_EXTRA"] = "- Lanza un puño cohete hacia las unidades del jugador.",
["BOSS_GRYMBEARD_NAME"] = "Grymbeard",
["BOSS_MACHINIST_DESCRIPTION"] = "Manejando su última invención, Grymbeard persigue a sus enemigos haciendo llover fuego y metal.",
["BOSS_MACHINIST_EXTRA"] = "- Volador\n- Dispara chatarra a los enemigos",
["BOSS_MACHINIST_NAME"] = "Grymbeard",
["BOSS_NAVIRA_DESCRIPTION"] = "Caído en desgracia y entrometiéndose con la magia oscura, Navira busca devolverle a los elfos a un estatus de gloria.",
["BOSS_NAVIRA_EXTRA"] = "- Bloquea torres con bolas de fuego\n- Se transforma en un tornado imbloqueable",
["BOSS_NAVIRA_NAME"] = "Navira",
["BOSS_PIG_DESCRIPTION"] = "El inimitable autoproclamado Rey de las Bestias Salvajes utiliza un gigantezco magual para aplastar a sus enemigos.",
["BOSS_PIG_EXTRA"] = "- Salta grandes distancias entre caminos",
["BOSS_PIG_NAME"] = "Goregrind",
["BOSS_PRINCESS_IRON_FAN_DESCRIPTION"] = "Combinando elegancia con poder letal, la Princesa Abanico de Hierro no es solo la esposa del Rey Demonio Toro, sino también una oponente formidable por derecho propio. Una demonio serena y calculadora. Portadora del legendario Abanico de Hierro, capaz de extinguir llamas y provocar tormentas.",
["BOSS_PRINCESS_IRON_FAN_EXTRA"] = "- Se clona a sí misma\n- Encierra héroes en un frasco\n- Convierte torres en generadores de enemigos",
["BOSS_REDBOY_TEEN_DESCRIPTION"] = "El joven y feroz príncipe demonio, orgulloso y conocido por su temperamento ardiente, actitud arrogante y ambición implacable. Comandante del Fuego Samadhi y maestro de artes marciales con la lanza. Hijo de la Princesa y del Rey Demonio Toro.",
["BOSS_REDBOY_TEEN_EXTRA"] = "- Gran ataque de daño en área\n- Ordena a su dragón que aturda torres",
["BOSS_REDBOY_TEEN_NAME"] = "Red Boy",
["BOSS_SPIDER_QUEEN_DESCRIPTION"] = "Una antigua Reina Araña, una fuerza primordial despertada de su letargo para reclamar lo que le pertenece por derecho.",
["BOSS_SPIDER_QUEEN_EXTRA"] = "- Aturde torres\n- Drena la vida de los enemigos cercanos\n- Invoca Arañas Robavida\n- Lanza telarañas a tus ojos",
["BOSS_SPIDER_QUEEN_NAME"] = "Mygale",
["BRIEFING_LEVEL_WARNING"] = "Esta campaña tiene un nivel de dificultad alto.",
["BUILD HERE!"] = "¡CONSTRUYE AQUÍ!",
["BUTTON_BUG_CRASH"] = "JUEGO FALLA",
["BUTTON_BUG_OTHER"] = "OTRO",
["BUTTON_BUG_REPORT"] = "ERROR",
["BUTTON_BUY"] = "COMPRAR",
["BUTTON_BUY_UPGRADE"] = "COMPRAR MEJORA",
["BUTTON_CLOSE"] = "CERRAR",
["BUTTON_CONFIRM"] = "CONFIRMAR",
["BUTTON_CONTINUE"] = "CONTINUAR",
["BUTTON_DISABLE"] = "Deshabilitar",
["BUTTON_DONE"] = "LISTO",
["BUTTON_ENDLESS_QUIT"] = "SALIR",
["BUTTON_ENDLESS_TRYAGAIN"] = "INTENTA DE NUEVO",
["BUTTON_GET_GEMS"] = "OBTÉN OBJETOS",
["BUTTON_LEVEL_SELECT_FIGHT"] = "¡A LUCHAR!",
["BUTTON_LOST_CONTENT"] = "PÉRDIDA DE CONTENIDO",
["BUTTON_MAIN_MENU"] = "MENÚ PRINCIPAL",
["BUTTON_NO"] = "NO",
["BUTTON_OK"] = "¡BIEN!",
["BUTTON_OPEN"] = "ABRIR",
["BUTTON_QUIT"] = "SALIR",
["BUTTON_RESET"] = "RESTAURAR",
["BUTTON_RESTART"] = "REINICIAR",
["BUTTON_RESUME"] = "REANUDAR",
["BUTTON_TO_BATTLE_1"] = "A LA",
["BUTTON_TO_BATTLE_2"] = "BATALLA",
["BUTTON_UNDO"] = "DESHACER",
["BUTTON_YES"] = "SÍ",
["BUY UPGRADES!"] = "¡COMPRA MEJORAS!",
["Basic"] = "Básicas",
["Basic Tower Types"] = "Tipo de Torres Básicas",
["CARD_REWARDS_CAMPAIGN"] = "¡Nueva campaña!",
["CARD_REWARDS_DLC_1"] = "Amenaza Colosal",
["CARD_REWARDS_DLC_2"] = "El Viaje de Wukong",
["CARD_REWARDS_HERO"] = "¡NUEVO HÉROE!",
["CARD_REWARDS_TOWER"] = "¡NUEVA TORRE!",
["CARD_REWARDS_TOWER_LEVEL"] = "¡NUEVO NIVEL DE TORRE!",
["CARD_REWARDS_TOWER_LEVEL_PREFIX"] = "NVL",
["CARD_REWARDS_UPDATE_01"] = "Furia Eterna",
["CARD_REWARDS_UPDATE_02"] = "Hambre Antigua",
["CARD_REWARDS_UPDATE_03"] = "Aracnofobia",
["CARD_REWARDS_UPGRADES"] = "¡PUNTOS DE MEJORA!",
["CArmor0"] = "Ninguna",
["CArmor1"] = "Baja",
["CArmor2"] = "Media",
["CArmor3"] = "Alta",
["CArmor4"] = "Grande",
["CArmor9"] = "Inmune",
["CArmorSmall0"] = "Nin.",
["CArmorSmall1"] = "Baja",
["CArmorSmall2"] = "Med.",
["CArmorSmall3"] = "Alta",
["CArmorSmall4"] = "Gra.",
["CArmorSmall9"] = "Inm.",
["CHANGE_LANGUAGE_QUESTION"] = "¿SEGURO QUE QUIERES CAMBIAR LA CONFIGURACIÓN DE IDIOMA?",
["CINEMATICS_TAP_TO_CONTINUE_KR1"] = "Haz clic para continuar...",
["CINEMATICS_TAP_TO_CONTINUE_KR2"] = "Haz clic para continuar...",
["CINEMATICS_TAP_TO_CONTINUE_KR3"] = "Haz clic para continuar...",
["CINEMATICS_TAP_TO_CONTINUE_KR5"] = "Haz clic para continuar...",
["CLAIM_GIFT"] = "Reclámalo",
["CLICK HERE TO SKIP.\nPLEASE DON'T"] = "CLIC AQUÍ PARA SALTEAR.\nNO, POR FAVOR.",
["CLICK HERE!"] = "¡CLIC AQUÍ!",
["CLICK ON THE ROAD"] = "CLIC EN EL CAMINO",
["CLICK TO CALL IT EARLY"] = "CLIC PARA LLAMAR ENEMIGOS ANTES",
["CLOUDSYNC_PLEASE_WAIT"] = "Actualizando partidas guardadas en la nube...",
["CLOUD_DIALOG_NO"] = "No",
["CLOUD_DIALOG_OK"] = "Aceptar",
["CLOUD_DIALOG_YES"] = "Sí",
["CLOUD_DOWNLOAD_QUESTION"] = "¿Descargar partida guardada desde iCloud?",
["CLOUD_DOWNLOAD_TITLE"] = "Descarga desde iCloud",
["CLOUD_SAVE"] = "Guardar en la nube",
["CLOUD_SAVE_DISABLE_EXTRA"] = "Nota: Si desinstalas el juego, podrías perder tu progreso.",
["CLOUD_SAVE_DISABLE_GENERIC_DESCRIPTION"] = "¿Seguro que quieres deshabilitar el guardado del progreso del juego en la nube?",
["CLOUD_SAVE_OFF"] = "Nube apagada",
["CLOUD_SAVE_ON"] = "Nube encendida",
["CLOUD_UPLOAD_QUESTION"] = "¿Subir partida guardada a iCloud?",
["CLOUD_UPLOAD_TITLE"] = "Subir a iCloud",
["COMIC_10_1_KR5_KR5"] = "¡Suéltenme! ¡Estoy haciendo lo mejor para el reino!",
["COMIC_10_2_KR5_KR5"] = "Termina con esta blasfemia, hermano. Ese no es el camino de los elfos.",
["COMIC_10_3_KR5_KR5"] = "Gracias, mi viejo aprendiz. Nosotros nos encargaremos.",
["COMIC_10_4_KR5_KR5"] = "Más tarde, en el campamento...",
["COMIC_10_5_KR5_KR5"] = "Entonces... ¿estás seguro de que se puede confiar en Vez'nan?",
["COMIC_10_6_KR5_KR5"] = "Lo tenemos vigilado...",
["COMIC_10_7_KR5_KR5"] = "...por ahora se está comportando bien.",
["COMIC_10_8_KR5_KR5"] = "Ja. Por ahora...",
["COMIC_11_1_KR5_KR5"] = "El pantano parece haber despertado...",
["COMIC_11_2_KR5_KR5"] = "...como si nos estuviera observando...",
["COMIC_11_3_KR5_KR5"] = "...avanzando y acechando...",
["COMIC_11_4_KR5_KR5"] = "...listo para devorarnos.",
["COMIC_11_5_KR5_KR5"] = "¡Ten cuidado!",
["COMIC_11_6_KR5_KR5"] = "¡Estamos bajo ataque!",
["COMIC_11_7_KR5_KR5"] = "¡Ve, pequeño fuego fatuo! ¡Nuestra seguridad reside en tu prisa!",
["COMIC_12_1_KR5_KR5"] = "Simplemente encerrarte fue un error. Uno que no repetiré.",
["COMIC_12_2_KR5_KR5"] = "¡NOOOOOO!!!",
["COMIC_12_3_KR5_KR5"] = "¡Te destierro para siempre!!",
["COMIC_12_4_KR5_KR5"] = "¡Cof!",
["COMIC_12_5_KR5_KR5"] = "¡Cof, Cof!",
["COMIC_12_6_KR5_KR5"] = "Eh-Supongo que debo estar falto de práctica.",
["COMIC_13_1_KR5_KR5"] = "Decían que era una locura.",
["COMIC_13_2_KR5_KR5"] = "Que un arma así era imposible.",
["COMIC_13_3_KR5_KR5"] = "Pero pronto verán que estaban errados...",
["COMIC_13_4_KR5_KR5"] = "...y caerán ante la genialidad de Grymbeard!",
["COMIC_14_1_KR5_KR5"] = "¿Qué haremos con ellos?",
["COMIC_14_2_KR5_KR5"] = "¡Déjamelos a mí!",
["COMIC_14_3_KR5_KR5"] = "Conozco un buen lugar.",
["COMIC_14_4_KR5_KR5"] = "¿Esto es todo?",
["COMIC_14_5_KR5_KR5"] = "¿Que Grymbeard se pudra en prisión?",
["COMIC_14_6_KR5_KR5"] = "Al contrario, amiguito...",
["COMIC_14_7_KR5_KR5"] = "...tengo grandes planes para ese cerebro tuyo!",
["COMIC_15_10_KR5_KR5"] = "...pero no en muy buen estado.",
["COMIC_15_1_KR5_KR5"] = "En algún lugar de la montaña.",
["COMIC_15_2_KR5_KR5"] = "¡Oye, Goblin!",
["COMIC_15_3_KR5_KR5"] = "¡Ponte a trabajar!",
["COMIC_15_4_KR5_KR5"] = "Debes entregar un mensaje.",
["COMIC_15_5_KR5_KR5"] = "Deberíamos enviar más exploradores. No podemos estar tranquilos con todos esos cultistas rondando por ahí.",
["COMIC_15_6_KR5_KR5"] = "Podríamos enviar algunos espíritus para ayudar; ellos...",
["COMIC_15_7_KR5_KR5"] = "¡Noticias urgentes!",
["COMIC_15_8_KR5_KR5"] = "Bueno...",
["COMIC_15_9_KR5_KR5"] = "Encontramos a nuestros exploradores...",
["COMIC_16_1_KR5_KR5"] = "¡Seré vengada!",
["COMIC_16_2_KR5_KR5"] = "Mi hermana...¿quuué?",
["COMIC_17_10_KR5_KR5"] = "¡Si no los detenemos, destruirán todos los reinos!",
["COMIC_17_11_KR5_KR5"] = "¡Tenemos que ayudarlo!",
["COMIC_17_12_KR5_KR5"] = "Oh, claro que sí.",
["COMIC_17_13_KR5_KR5"] = "Claro que sí…",
["COMIC_17_1_KR5_KR5"] = "Linda tarde, ¿verdad?",
["COMIC_17_2_KR5_KR5"] = "Podría acostumbrarme a esta paz.",
["COMIC_17_3_KR5_KR5"] = "Mejor no.",
["COMIC_17_4_KR5_KR5"] = "¿Sol, eres tú? ¡Podrías haber saludado, ya sabes...!",
["COMIC_17_5_KR5_KR5"] = "Amigos, ha ocurrido algo terrible...",
["COMIC_17_6_KR5_KR5"] = "Estaba meditando pacíficamente en mi tortuga cuando...",
["COMIC_17_7_KR5_KR5"] = "¡Los Tres Reyes Demonio aparecieron de la nada!",
["COMIC_17_8_KR5_KR5"] = "Ni que decir que luché valientemente, pero...",
["COMIC_17_9_KR5_KR5"] = "¡Han tomado deshonrosamente mis esferas celestiales!",
["COMIC_1_1_KR5"] = "Ha pasado un mes desde que llegamos a esta tierra buscando a nuestro Rey...",
["COMIC_1_2B_KR5"] = "...después de que fuera desterrado por Vez'nan, el hechicero oscuro.",
["COMIC_1_4_KR5"] = "Encontramos un lugar para acampar y recuperar nuestras fuerzas...",
["COMIC_1_5_KR5"] = "...en paz...",
["COMIC_1_8_KR5"] = "...pero parece que eso se terminó.",
["COMIC_2_1_KR5"] = "¡Hurra!",
["COMIC_2_3_KR5"] = "¡¿Vez'nan?!",
["COMIC_2_4a_KR5"] = "Tranquilos... vengo a proponerles...",
["COMIC_2_4b_KR5"] = "...un trato.",
["COMIC_2_5_KR5"] = "¡¿Después de lo que le has hecho a nuestro reino?!",
["COMIC_2_6_KR5"] = "Los ojos del Rey Denas debían ser abiertos.",
["COMIC_2_7_KR5"] = "Él se negó a ver la amenaza que infesta al reino.",
["COMIC_2_8_1_KR5"] = "Busquemos a su Rey...",
["COMIC_2_8_2_KR5"] = "...y pongamos un fin a los invasores.",
["COMIC_2_8b_KR5"] = "...juntos.",
["COMIC_3_1_KR5"] = "¡Oh cielos! ¿Qué tenemos aquí?",
["COMIC_3_2_KR5"] = "¡La poderosa espada de Elynie!",
["COMIC_3_3_KR5"] = "¡Ouch!",
["COMIC_3_4a_KR5"] = "Por supuesto...",
["COMIC_3_4b_KR5"] = "¡Deja de perder el tiempo!",
["COMIC_3_5a_KR5"] = "Ah... pero él está más cerca de lo que piensas.",
["COMIC_3_5b_KR5"] = "Nuestro rey sigue perdido.",
["COMIC_3_6_KR5"] = "Aunque esta batalla puede ir cuesta arriba.",
["COMIC_4_10a_KR5"] = "¡Ja! Siempre la tuve.",
["COMIC_4_10b_KR5"] = "Entonces... ¿qué pasará ahora?",
["COMIC_4_11_KR5"] = "Tendremos nuestras diferencias...",
["COMIC_4_12_KR5"] = "...pero ahora tenemos un enemigo en común.",
["COMIC_4_1_KR5"] = "Elynie...",
["COMIC_4_2_KR5"] = "...¡dale fuerzas!",
["COMIC_4_4_KR5"] = "Aaurrrgh!",
["COMIC_4_7a_KR5"] = "¡Veo que tus 'vacaciones' te han sentado de maravilla!",
["COMIC_4_7b_KR5"] = "¡¡¡TÚ!!!",
["COMIC_4_8_KR5"] = "¡Deberías pagar por tus acciones!",
["COMIC_4_9_KR5"] = "Pero tenías razón.",
["COMIC_5_1_KR2"] = "¡Victoria!",
["COMIC_5_1_KR5_KR5"] = "Gusanos, no detendrán...",
["COMIC_5_2_KR2"] = "¡Victoria!",
["COMIC_5_2_KR5_KR5"] = "...EL NUEVO MUNDO!",
["COMIC_5_6_KR5_KR5"] = "¡Ha despertado!",
["COMIC_5_7a_KR5_KR5"] = "Así que esto es...",
["COMIC_5_7b_KR5_KR5"] = "...el enfrentamiento final.",
["COMIC_6_1a_KR5_KR5"] = "Son valientes desafiándome.",
["COMIC_6_1b_KR5_KR5"] = "Pero... ¡eso no pertenece aquí!",
["COMIC_6_4_KR5_KR5"] = "¡Hey!",
["COMIC_6_5_KR5_KR5"] = "Tú, babosa...",
["COMIC_6_6_KR5_KR5"] = "...no subestimes MI poder!!!",
["COMIC_6_8_KR5_KR5"] = "Prepárense. ¡No aguantaré por mucho tiempo!.",
["COMIC_7_1_KR5_KR5"] = "¡¡¡NOOO!!! ¡No puede... ser!",
["COMIC_7_3_KR5_KR5"] = "Y... ¿ahora qué?",
["COMIC_7_4a_KR5_KR5"] = "Bueno, mi misión acabó...",
["COMIC_7_4b_KR5_KR5"] = "...y creo que precisan a su rey.",
["COMIC_7_5_2_KR2"] = "¡Nop!",
["COMIC_7_6_KR5_KR5"] = "Hasta la próxima, querido enemigo.",
["COMIC_7_7_KR5_KR5"] = "Luego, en el Bosque Radiante...",
["COMIC_8_1_KR5_KR5"] = "Ah, ¡al fin!",
["COMIC_8_2_KR5_KR5"] = "Este poder vuelve a ser...",
["COMIC_8_4_KR5_KR5"] = "... ¡MÍO!",
["COMIC_8_5_KR5_KR5"] = "¡MUA JA JA JA JA!",
["COMIC_9_1_KR5_KR5"] = "Hace no mucho, los elfos éramos admirados por nuestra magia y gracia...",
["COMIC_9_2_KR5_KR5"] = "...hasta que nuestra reliquia sagrada fue corrompida y nos convertimos en sombras de lo que fuimos.",
["COMIC_9_3_KR5_KR5"] = "Pero con este ejército, restableceré nuestra gloria...",
["COMIC_9_4_KR5_KR5"] = "...y los lideraré en un nuevo mundo gobernado por los elfos!!!",
["COMIC_BALLOON_0002_KR1"] = "¡Victoria!",
["COMIC_BALLOON_02_KR1"] = "¡Victoria!",
["COMIC_balloon_0002_KR1"] = "¡Victoria!",
["COMMAND YOUR TROOPS!"] = "¡DIRIGE TUS TROPAS!",
["CONFIRM_EXIT"] = "¿Salir?",
["CONFIRM_RESTART"] = "¿Reiniciar?",
["CONTROLLER_STAGE_16_OVERSEER_DESCRIPTION"] = "Una monstruosidad extradimensional que invida y conquista otros mundos para absorber su energía. Debe ser detenido a toda costa.",
["CONTROLLER_STAGE_16_OVERSEER_EXTRA"] = "- Cambia de lugar las torres del jugador\n- Engendra Glarelings\n- Destruye puntos estratégicos",
["CONTROLLER_STAGE_16_OVERSEER_NAME"] = "El Omnividente",
["CREDITS"] = "CRÉDITOS",
["CREDITS_COPYRIGHT"] = "© 2014 Ironhide Game Studio. Todos los derechos reservados.",
["CREDITS_POWERED_BY"] = "Powered by",
["CREDITS_SUBTITLE_01"] = "(en orden alfabético)",
["CREDITS_SUBTITLE_07"] = "(en orden alfabético)",
["CREDITS_SUBTITLE_09"] = "(en orden alfabético)",
["CREDITS_SUBTITLE_16"] = "(en orden alfabético)",
["CREDITS_TEXT_18"] = "A nuestras familias, amigos y la comunidad",
["CREDITS_TEXT_18_2"] = "por apoyarnos durante estos años.",
["CREDITS_TITLE_01"] = "Directores creativos & Productores ejecutivos",
["CREDITS_TITLE_01_CREATIVE_DIRECTORS"] = "Directores Creativos",
["CREDITS_TITLE_01_EXECUTIVE_PRODUCERS"] = "Productores Ejecutivos",
["CREDITS_TITLE_02"] = "Líder de Diseño del juego",
["CREDITS_TITLE_02_LEAD_GAME_DESIGNERS"] = "Líderes de Diseño del Juego",
["CREDITS_TITLE_03"] = "Diseñadores del juego",
["CREDITS_TITLE_03_GAME_DESIGNER"] = "Diseñador del Juego",
["CREDITS_TITLE_04"] = "Narrativa",
["CREDITS_TITLE_04_STORY_WRITERS"] = "Narrativa",
["CREDITS_TITLE_05"] = "Textos",
["CREDITS_TITLE_06"] = "Líder de Programación",
["CREDITS_TITLE_06_LEAD_PROGRAMMERS"] = "Líderes de Programación",
["CREDITS_TITLE_07"] = "Programadores",
["CREDITS_TITLE_08"] = "Líder de Arte",
["CREDITS_TITLE_09"] = "Artistas",
["CREDITS_TITLE_10"] = "Artista de comics",
["CREDITS_TITLE_11"] = "Guionista de comics",
["CREDITS_TITLE_12"] = "Artista técnico",
["CREDITS_TITLE_13"] = "Efectos de sonido",
["CREDITS_TITLE_14"] = "Música original por",
["CREDITS_TITLE_15"] = "Voces",
["CREDITS_TITLE_16"] = "QA y Testing",
["CREDITS_TITLE_17"] = "Beta Testing",
["CREDITS_TITLE_18"] = "Agradecimientos Especiales",
["CREDITS_TITLE_19_PMO"] = "PMO",
["CREDITS_TITLE_20_PRODUCER"] = "Productor",
["CREDITS_TITLE_21_MARKETING"] = "Marketing",
["CREDITS_TITLE_22_SPECIAL_COLLAB"] = "Colaboradores Especiales",
["CREDITS_TITLE_ANCIENT_HUNGER_UPDATE"] = "Hambre Antigua / Aracnofobia / El Viaje de Wukong",
["CREDITS_TITLE_GAME_ENGINE_PROGRAMMER"] = "Programador del motor de juego",
["CREDITS_TITLE_LOCALIZATION"] = "Localización",
["CREDITS_TITLE_LOGO"] = "UN JUEGO DE",
["CRange0"] = "Corto",
["CRange1"] = "Medio",
["CRange2"] = "Largo",
["CRange3"] = "Grande",
["CRange4"] = "Extremo",
["CReload0"] = "Muy lenta",
["CReload1"] = "Lenta",
["CReload2"] = "Media",
["CReload3"] = "Rápida",
["CReload4"] = "Muy rápida",
["CSpeed0"] = "Lenta",
["CSpeed1"] = "Media",
["CSpeed2"] = "Rápida",
["C_DIFFICULTY_EASY"] = "Completado en Casual",
["C_DIFFICULTY_HARD"] = "Completado en Veterano",
["C_DIFFICULTY_IMPOSSIBLE"] = "Completado en Imposible",
["C_DIFFICULTY_NORMAL"] = "Completado en Normal",
["C_REWARD"] = "Recompensa:",
["Campaign"] = "Campaña",
["Campaing"] = "Campaña",
["Casual"] = "Casual",
["Challenge Rules"] = "Reglas del desafío",
["Clear_progress"] = "Borrar progreso",
["Click on the path to move the hero."] = "Haz CLIC en el sendero para mover al Héroe.",
["Click to select"] = "Haz clic para seleccionar",
["Coming soon"] = "Próximamente...",
["Community Manager"] = "Gerente de Comunidad",
["Continue"] = "Continuar",
["Credits"] = "Créditos",
["DAYS_ABBREVIATION"] = "d",
["DEFEAT"] = "DERROTA",
["DELETE SLOT?"] = "¿Borrar la partida?",
["DIFFICULTY LEVEL"] = "NIVEL DE DIFICULTAD",
["DIFFICULTY_SELECTION_EASY_DESCRIPTION"] = "¡Para principiantes en juegos de estrategia!",
["DIFFICULTY_SELECTION_HARD_DESCRIPTION"] = "¡Extremo! ¡Juega a tu propio riesgo!",
["DIFFICULTY_SELECTION_IMPOSSIBLE_DESCRIPTION"] = "¡Solo los más fuertes podrán superarlo!",
["DIFFICULTY_SELECTION_IMPOSSIBLE_LOCKED_DESCRIPTION"] = "Completa la campaña para desbloquear este modo",
["DIFFICULTY_SELECTION_NORMAL_DESCRIPTION"] = "¡Un buen desafío!",
["DIFFICULTY_SELECTION_NOTE"] = "Siempre puedes cambiar la dificultad al elegir un nivel.",
["DIFFICULTY_SELECTION_TITLE"] = "¡Elige el nivel de dificultad!",
["DISCOUNT"] = "DESCUENTO",
["DLC_OWNED"] = "COMPRADO",
["Defeat"] = "Derrota",
["Difficulty Level"] = "Nivel de dificultad",
["Done"] = "Listo",
["ELITE STAGE!"] = "¡NIVEL ELITE!",
["ENEMY_ACOLYTE_DESCRIPTION"] = "Lánguidos y de baja estatura, los acólitos hacen sentir la superioridad numérica durante la batalla.",
["ENEMY_ACOLYTE_EXTRA"] = "- Genera un tentáculo al morir",
["ENEMY_ACOLYTE_NAME"] = "Acólito del Culto",
["ENEMY_ACOLYTE_SPECIAL"] = "Genera un tentáculo al morir",
["ENEMY_ACOLYTE_TENTACLE_DESCRIPTION"] = "Como último recurso los Acólitos sacrifican su vida al Omnividente para crear tentáculos mortales.",
["ENEMY_ACOLYTE_TENTACLE_EXTRA"] = "- Surge de los Acólitos muertos",
["ENEMY_ACOLYTE_TENTACLE_NAME"] = "Tentáculo de Acólito",
["ENEMY_AMALGAM_DESCRIPTION"] = "Monstruosidades hechas de carne y tierra del Vacío Más Allá",
["ENEMY_AMALGAM_EXTRA"] = "- Mini jefe\n- Explota cuando muere",
["ENEMY_AMALGAM_NAME"] = "Behemoth",
["ENEMY_ANIMATED_ARMOR_DESCRIPTION"] = "Reliquias maltrechas de batallas pasadas, ahora poseídas por espectros que las llevan hacia el combate.",
["ENEMY_ANIMATED_ARMOR_EXTRA"] = "- Cuando son derrotadas pueden ser reanimadas por un espectro",
["ENEMY_ANIMATED_ARMOR_NAME"] = "Armadura Animada",
["ENEMY_ARMORED_NIGHTMARE_DESCRIPTION"] = "Ataviadas con armadura gracias a la magia del culto, estas Pesadillas se lanzan sin cuidados a la batalla.",
["ENEMY_ARMORED_NIGHTMARE_EXTRA"] = "- Armadura alta\n- Se convierte en una Pesadilla común al ser derrotada",
["ENEMY_ARMORED_NIGHTMARE_NAME"] = "Pesadilla Amarrada",
["ENEMY_ARMORED_NIGHTMARE_SPECIAL"] = "Se convierte en una Pesadilla común al ser derrotada",
["ENEMY_ASH_SPIRIT_DESCRIPTION"] = "Espíritus poderosos convertidos en monstruos aterradores, nacidos de lava, ceniza y dolor.",
["ENEMY_ASH_SPIRIT_EXTRA"] = "- Salud alta\n- Armadura alta\n- Regenera salud mientras está en suelo en llamas",
["ENEMY_ASH_SPIRIT_NAME"] = "Ash Spirit",
["ENEMY_BALLOONING_SPIDER_DESCRIPTION"] = "Arañas rápidas y sigilosas con un talento especial para evitar problemas.",
["ENEMY_BALLOONING_SPIDER_EXTRA"] = "- Empieza a volar cuando está acorralada\n- Armadura media",
["ENEMY_BALLOONING_SPIDER_FLYER_DESCRIPTION"] = "Arañas rápidas y sigilosas con un talento especial para evitar problemas.",
["ENEMY_BALLOONING_SPIDER_FLYER_EXTRA"] = "- Empieza a volar cuando está acorralada\n- Armadura media",
["ENEMY_BALLOONING_SPIDER_FLYER_NAME"] = "Araña Planeadora",
["ENEMY_BALLOONING_SPIDER_NAME"] = "Araña Planeadora",
["ENEMY_BANE_WOLF_DESCRIPTION"] = "Lobos retorcidos que acechan a quienes son demasiado lentos para verlos venir.",
["ENEMY_BANE_WOLF_EXTRA"] = "- Se mueven más rápido cada vez que reciben daño",
["ENEMY_BANE_WOLF_NAME"] = "Lobo Maldito",
["ENEMY_BEAR_VANGUARD_DESCRIPTION"] = "Grandes, anchos y malos, destrozan a sus enemigos por docena.",
["ENEMY_BEAR_VANGUARD_EXTRA"] = "- Armadura alta\n- Entra en frenesí cuando otro oso cercano muere",
["ENEMY_BEAR_VANGUARD_NAME"] = "Oso de Vanguardia",
["ENEMY_BEAR_VANGUARD_SPECIAL"] = "Entra en frenesí cuando otro oso cercano muere.",
["ENEMY_BEAR_WOODCUTTER_DESCRIPTION"] = "Suele dormir en lugar de trabajar, pero cuando se despierta es porque las cosas van en serio.",
["ENEMY_BEAR_WOODCUTTER_EXTRA"] = "- Armadura alta\n- Entra en frenesí cuando otro oso cercano muere",
["ENEMY_BEAR_WOODCUTTER_NAME"] = "Oso Leñador",
["ENEMY_BIG_TERRACOTA_DESCRIPTION"] = "Un bulto de lodo antropomórfico nacido de la fusión de varias almas impulsadas por una intención asesina.",
["ENEMY_BIG_TERRACOTA_EXTRA"] = "- Cuerpo a cuerpo",
["ENEMY_BIG_TERRACOTA_NAME"] = "Cebo Ilusorio de Monstruo",
["ENEMY_BLAZE_RAIDER_DESCRIPTION"] = "Capitanes orgullosos y corpulentos, iniciados en la Senda del Fuego, que empuñan lanzas serpiente para superar a sus enemigos.",
["ENEMY_BLAZE_RAIDER_EXTRA"] = "- Armadura baja\n- Ataque especial en suelo llameante",
["ENEMY_BLAZE_RAIDER_NAME"] = "Saqueador Llameante",
["ENEMY_BLINKER_DESCRIPTION"] = "Con su mirada amenazante y alas de murciélago, los Pestañadores acechan a las presas indefensas.",
["ENEMY_BLINKER_EXTRA"] = "- Aturde a las unidades del jugador",
["ENEMY_BLINKER_NAME"] = "Pestañeador del Vacío",
["ENEMY_BLINKER_SPECIAL"] = "Aturde a las unidades del jugador.",
["ENEMY_BOSS_BULL_KING_DESCRIPTION"] = "Un líder despiadado y autoritario, veterano de guerra y estratega pragmático. Antes fue hermano jurado de Sun Wukong, pero ahora el Rey Demonio planea robar las esferas celestiales de su rival. Famoso por su inmensa fuerza, su carácter rencoroso y su destreza marcial.",
["ENEMY_BOSS_BULL_KING_EXTRA"] = "-Alta armadura\n- Alta resistencia mágica\n- Aturdimiento de área grande en unidades y torres",
["ENEMY_BOSS_BULL_KING_NAME"] = "Bull Demon King",
["ENEMY_BOSS_CORRUPTED_DENAS_NAME"] = "Denas Corrupto",
["ENEMY_BOSS_CROCS_2_NAME"] = "Abominor Veneno",
["ENEMY_BOSS_CROCS_3_NAME"] = "Abominor Fuego",
["ENEMY_BOSS_CROCS_NAME"] = "Abominor",
["ENEMY_BOSS_CULT_LEADER_NAME"] = "Seeress Mydrias",
["ENEMY_BOSS_DEFORMED_GRYMBEARD_NAME"] = "Grymbeard Deformado",
["ENEMY_BOSS_GRYMBEARD_NAME"] = "Grymbeard",
["ENEMY_BOSS_MACHINIST_NAME"] = "Grymbeard",
["ENEMY_BOSS_NAVIRA_NAME"] = "Navira",
["ENEMY_BOSS_OVERSEER_NAME"] = "El Omnividente",
["ENEMY_BOSS_PIG_NAME"] = "Goregrind",
["ENEMY_BOSS_PRINCESS_IRON_FAN_CLONE_NAME"] = "Clon de la Princesa Abanico de Hierro",
["ENEMY_BOSS_PRINCESS_IRON_FAN_NAME"] = "Princess Iron Fan",
["ENEMY_BOSS_REDBOY_TEEN_NAME"] = "Chico Rojo",
["ENEMY_BOSS_SPIDER_QUEEN_NAME"] = "Mygale",
["ENEMY_BRUTE_WELDER_DESCRIPTION"] = "Estos trabajadores usarán sus sopletes contra los enemigos aún sin ser provocados.",
["ENEMY_BRUTE_WELDER_EXTRA"] = "- Bloquea una torre cuando muere",
["ENEMY_BRUTE_WELDER_NAME"] = "Soldador Bruto",
["ENEMY_BURNING_TREANT_DESCRIPTION"] = "Criaturas de madera con intenciones maliciosas, nacidas en medio de un bosque en llamas.",
["ENEMY_BURNING_TREANT_EXTRA"] = "- Daño de área\n- Deja el suelo en llamas al atacar",
["ENEMY_BURNING_TREANT_NAME"] = "Burning Treant",
["ENEMY_CITIZEN_1_DESCRIPTION"] = "Pescadores siniestros al servicio de la Princesa, que se infiltran en el mercado negro.",
["ENEMY_CITIZEN_1_EXTRA"] = "- Débil",
["ENEMY_CITIZEN_1_NAME"] = "Viejo vendedor de pescado",
["ENEMY_CITIZEN_2_DESCRIPTION"] = "Pescadores siniestros que sirven a la Princesa, abriéndose paso por el mercado negro.",
["ENEMY_CITIZEN_2_EXTRA"] = "- Débil",
["ENEMY_CITIZEN_2_NAME"] = "Pescador de Blackwater",
["ENEMY_CITIZEN_3_DESCRIPTION"] = "Pescadores siniestros al servicio de la Princesa, que se infiltran en el mercado negro.",
["ENEMY_CITIZEN_3_EXTRA"] = "- Débil",
["ENEMY_CITIZEN_3_NAME"] = "Contrabandista de Tinta",
["ENEMY_CITIZEN_4_DESCRIPTION"] = "Pescadores siniestros al servicio de la Princesa, que se infiltran en el mercado negro.",
["ENEMY_CITIZEN_4_EXTRA"] = "- Débil",
["ENEMY_CITIZEN_4_NAME"] = "Saqueador de Mareas",
["ENEMY_COMMON_CLONE_DESCRIPTION"] = "Ni destacable, ni especial, como el original.",
["ENEMY_COMMON_CLONE_EXTRA"] = "- Avanza sin pensarlo mucho",
["ENEMY_COMMON_CLONE_NAME"] = "Clon",
["ENEMY_CORRUPTED_ELF_DESCRIPTION"] = "Elfos reanimados que cazan enemigos desde lejos. Incluso estando muertos son efectivos.",
["ENEMY_CORRUPTED_ELF_EXTRA"] = "- Invoca un espectro cuando muere",
["ENEMY_CORRUPTED_ELF_NAME"] = "Montaráz Renacido",
["ENEMY_CORRUPTED_STALKER_DESCRIPTION"] = "Acechanubes adiestrados por los Acólitos. Ahora sirven como monturas para el Culto.",
["ENEMY_CORRUPTED_STALKER_EXTRA"] = "- Volador",
["ENEMY_CORRUPTED_STALKER_NAME"] = "Acechanubes Adiestrado",
["ENEMY_CORRUPTED_STALKER_SPECIAL"] = "Volador",
["ENEMY_CROCS_BASIC_DESCRIPTION"] = "Orgulloso guerrero Crok, aún al principio de su vida y a solo unas calorías de transformarse en la máquina asesina que sabe que puede ser. ",
["ENEMY_CROCS_BASIC_EGG_DESCRIPTION"] = "Recién nacidos e imparables en sus pies, \"crecen tan rápido\" es una frase inventada gracias a estos pequeñines llenos de sorpresas. ",
["ENEMY_CROCS_BASIC_EGG_EXTRA"] = "- Inbloqueable\n- Poca Armadura\n- Se transforma en un Gator después de unos segundos ",
["ENEMY_CROCS_BASIC_EGG_NAME"] = "Crokinder ",
["ENEMY_CROCS_BASIC_EXTRA"] = "- Melee",
["ENEMY_CROCS_BASIC_NAME"] = "Gator",
["ENEMY_CROCS_EGG_SPAWNER_DESCRIPTION"] = "¡Este cocodrilo trae un nido de problemas! Suelta huevos que eclosionan en crokinders. ¡Es como una guardería, pero con mucho más mordisco!",
["ENEMY_CROCS_EGG_SPAWNER_EXTRA"] = "- Suelta Crokinders en el Camino",
["ENEMY_CROCS_EGG_SPAWNER_NAME"] = "Gator anidador",
["ENEMY_CROCS_FLIER_DESCRIPTION"] = "Crok astutos que, en su desprecio por la evolución natural, forjaron sus propias alas para obtener una ventaja aérea.",
["ENEMY_CROCS_FLIER_EXTRA"] = "- Volador",
["ENEMY_CROCS_FLIER_NAME"] = "Crok Alado",
["ENEMY_CROCS_HYDRA_DESCRIPTION"] = "Dos cabezas son mejor que una y las hidras lo demuestran. Hay un viejo mito sobre una bestia de tres cabezas como esta, pero probablemente sea una mentira.",
["ENEMY_CROCS_HYDRA_EXTRA"] = "- Genera una tercera cabeza al morir\n- Escupe veneno en el suelo.",
["ENEMY_CROCS_HYDRA_NAME"] = "Hidra",
["ENEMY_CROCS_QUICKFEET_GATOR_NAME"] = "Pies Ligeros",
["ENEMY_CROCS_RANGED_DESCRIPTION"] = "Rápidos y escurridizos, lagartos cazadores que se enfrentan a sus enemigos usando hondas de largo alcance. ",
["ENEMY_CROCS_RANGED_EXTRA"] = "- Rápido\n- A distancia ",
["ENEMY_CROCS_RANGED_NAME"] = "Lizardshot ",
["ENEMY_CROCS_SHAMAN_DESCRIPTION"] = "Seres mágicos cruciales para los Croks. Para una raza de sangre fría, prever los caprichos del cielo es cuestión de vida o muerte.",
["ENEMY_CROCS_SHAMAN_EXTRA"] = "- Daño Mágico a Distancia\n- Resistencia Mágica Alta\n- Cura a otros crocs\n- Aturde torres",
["ENEMY_CROCS_SHAMAN_NAME"] = "Crok Sabio",
["ENEMY_CROCS_TANK_DESCRIPTION"] = "Pilares de las fuerzas Croks, creen que \"una buena defensa es el mejor ataque\". Robaron conchas y las usaron a su manera.",
["ENEMY_CROCS_TANK_EXTRA"] = "- Salud Alta\n- Mucha armadura\n- Gira al bloquearse",
["ENEMY_CROCS_TANK_NAME"] = "Tankzard",
["ENEMY_CRYSTAL_GOLEM_DESCRIPTION"] = "Imbuidos con cristales llenos de magia extraña, estas efigies de piedra son casi imparables.",
["ENEMY_CRYSTAL_GOLEM_EXTRA"] = "- Mini jefe\n- Armadura muy alta",
["ENEMY_CRYSTAL_GOLEM_NAME"] = "Golem de Cristal",
["ENEMY_CULTBROOD_DESCRIPTION"] = "Abominaciones mitad araña, mitad cultista que se lanzan a la batalla sin miedo ni piedad.",
["ENEMY_CULTBROOD_EXTRA"] = "- Rápido \n- Ataque venenoso \n- Si una unidad muere envenenada, engendra otro Cultista Engendro",
["ENEMY_CULTBROOD_NAME"] = "Cultista Engendro",
["ENEMY_CUTTHROAT_RAT_DESCRIPTION"] = "Astutas y taimadas por naturaleza, las ratas son hábiles asesinas e infiltradoras.",
["ENEMY_CUTTHROAT_RAT_EXTRA"] = "- Velocidad alta\n- Se vuelve invisible después de golpear al enemigo",
["ENEMY_CUTTHROAT_RAT_NAME"] = "Rata Despiadada",
["ENEMY_CUTTHROAT_RAT_SPECIAL"] = "Se vuelve invisible después de golpear al enemigo.",
["ENEMY_DARKSTEEL_ANVIL_DESCRIPTION"] = "La respuesta enana a los martillos de guerra. Cuanto más pesado se ve, más alto canta.",
["ENEMY_DARKSTEEL_ANVIL_EXTRA"] = "- Mejora la armadura y velocidad de sus aliados",
["ENEMY_DARKSTEEL_ANVIL_NAME"] = "Yunque Aceroscuro",
["ENEMY_DARKSTEEL_FIST_DESCRIPTION"] = "Mejorado mecánicamente para doblar metales pero prefiere golpear a otras personas.",
["ENEMY_DARKSTEEL_FIST_EXTRA"] = "- Aturde unidades del jugador",
["ENEMY_DARKSTEEL_FIST_NAME"] = "Pugilista Aceroscuro",
["ENEMY_DARKSTEEL_GUARDIAN_DESCRIPTION"] = "Trajes de batalla robustos operados por guerreros enanos y alimentados por motores ardientes. Literalmente vestidos para matar.",
["ENEMY_DARKSTEEL_GUARDIAN_EXTRA"] = "- Mini jefe\n- Entra en frenesí cuando tiene poca vida",
["ENEMY_DARKSTEEL_GUARDIAN_NAME"] = "Guardián Aceroscuro",
["ENEMY_DARKSTEEL_HAMMERER_DESCRIPTION"] = "Guerreros tan simples como su arma de preferencia.",
["ENEMY_DARKSTEEL_HAMMERER_EXTRA"] = " ",
["ENEMY_DARKSTEEL_HAMMERER_NAME"] = "Martillador Aceroscuro",
["ENEMY_DARKSTEEL_HULK_DESCRIPTION"] = "Malhumorado y con acero fundido en las venas, es el peso pesado de los enanos.",
["ENEMY_DARKSTEEL_HULK_EXTRA"] = "- Mini jefe\n- Cuando tiene poca vida carga por el camino haciendo daño",
["ENEMY_DARKSTEEL_HULK_NAME"] = "Gigante Aceroscuro",
["ENEMY_DARKSTEEL_SHIELDER_DESCRIPTION"] = "Resguardados por escudos gigantescos, empujan a los enemigos a medida que avanzan.",
["ENEMY_DARKSTEEL_SHIELDER_EXTRA"] = "- Se convierte en un Martillador cuando es derrotado",
["ENEMY_DARKSTEEL_SHIELDER_NAME"] = "Protector Aceroscuro",
["ENEMY_DEATHWOOD_DESCRIPTION"] = "Bosquextraños corruptos por espectros oscuros que ahora deambulan buscando generar caos.",
["ENEMY_DEATHWOOD_EXTRA"] = "-Mini jefe\n- Lanza una bellota maldita que hace daño en área.",
["ENEMY_DEATHWOOD_NAME"] = "Bosquemuerte",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_DESCRIPTION"] = "El resultado de la arrogancia desenfrenada de Grymbeard. El poder de su mente solo es comparable a su fealdad.",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_EXTRA"] = "- Volador\n- Escudo de resistencia mágica alta",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_NAME"] = "Clon Deformado",
["ENEMY_DEMON_MINOTAUR_DESCRIPTION"] = "Criaturas implacables, mitad humanas, mitad toro, del inframundo.",
["ENEMY_DEMON_MINOTAUR_EXTRA"] = "- Daño alto\n- Ataque de carga\n- No puede ser asesinado instantáneamente",
["ENEMY_DEMON_MINOTAUR_NAME"] = "Demon Minotaur",
["ENEMY_DOOM_BRINGER_DESCRIPTION"] = "Temibles guerreros que traen la perdición a toda costa.",
["ENEMY_DOOM_BRINGER_EXTRA"] = "- Aturde torres",
["ENEMY_DOOM_BRINGER_NAME"] = "Doombringer",
["ENEMY_DRAINBROOD_DESCRIPTION"] = "Una araña ancestral con una mordedura letal. Algunos especulan que es la principal culpable de la cristalización de las demás arañas.",
["ENEMY_DRAINBROOD_EXTRA"] = "- Cristaliza a los enemigos mientras drena su vida",
["ENEMY_DRAINBROOD_NAME"] = "Araña Robavida",
["ENEMY_DREADEYE_VIPER_DESCRIPTION"] = "Empapando flechas con su propio veneno, son enemigas mortales desde lejos.",
["ENEMY_DREADEYE_VIPER_EXTRA"] = "- Resistencia mágica baja\n- Ataques venenosos",
["ENEMY_DREADEYE_VIPER_NAME"] = "Víbora Ojopavoroso",
["ENEMY_DREADEYE_VIPER_SPECIAL"] = "Las flechas envenenan a los objetivos.",
["ENEMY_DUST_CRYPTID_DESCRIPTION"] = "Antes, una criatura maravillosa. Ahora una visión atemorizante para aquellos que deambulan muy lejos.",
["ENEMY_DUST_CRYPTID_EXTRA"] = "- Volador\n- Deja una nube de polen que vuelve invulnerables a los enemigos al daño a los enemigos",
["ENEMY_DUST_CRYPTID_NAME"] = "Críptido Polvoriento",
["ENEMY_EVOLVING_SCOURGE_DESCRIPTION"] = "Pueden parecer apapachables a primera vista, pero si el Azote devora a una presa caida las cosas pueden ponerse peligrosas muy rápido.",
["ENEMY_EVOLVING_SCOURGE_EXTRA"] = "- Devora unidades para evolucionar a una forma más fuerte\n - Evoluciona instantáneamente a su forma más poderosa cuando es afectado por el Resplandor",
["ENEMY_EVOLVING_SCOURGE_NAME"] = "Azotador Evolutivo",
["ENEMY_FAN_GUARD_DESCRIPTION"] = "Guerreras fuertes y muy versátiles, expertas tanto en causar dolor como en protegerse con sus abanicos mágicos.",
["ENEMY_FAN_GUARD_EXTRA"] = "- Tiene armadura media y resistencia mágica mientras no esté bloqueado.",
["ENEMY_FAN_GUARD_NAME"] = "Fan Guard",
["ENEMY_FIRE_FOX_DESCRIPTION"] = "Zorros escurridizos y adorables nacidos del fuego. Demasiado rápidos e inestables para ser domesticados.",
["ENEMY_FIRE_FOX_EXTRA"] = "- Baja resistencia mágica\n- Más rápido sobre suelo en llamas\n- Deja el suelo en llamas al morir",
["ENEMY_FIRE_FOX_NAME"] = "Zorro de Fuego",
["ENEMY_FIRE_PHOENIX_DESCRIPTION"] = "Criaturas voladoras míticas que se alimentan del fuego mismo. Viven y mueren en una llama ardiente.",
["ENEMY_FIRE_PHOENIX_EXTRA"] = "- Volador\n- Deja el suelo en llamas al morir",
["ENEMY_FIRE_PHOENIX_NAME"] = "Zhuque",
["ENEMY_FLAME_GUARD_DESCRIPTION"] = "Esforzándose por la aprobación de sus maestros, estos discípulos de bajo rango destacan con cuchillas pequeñas.",
["ENEMY_FLAME_GUARD_EXTRA"] = "- Ataque especial en terreno en llamas",
["ENEMY_FLAME_GUARD_NAME"] = "Guardia de Llamas",
["ENEMY_GALE_WARRIOR_DESCRIPTION"] = "Gráciles y elegantes, estas guerreras fueron elegidas por su princesa y están dispuestas a morir por ella.",
["ENEMY_GALE_WARRIOR_EXTRA"] = "- Armadura media\n- Causa sangrado cada 3 ataques",
["ENEMY_GALE_WARRIOR_NAME"] = "Gale Warrior",
["ENEMY_GLAREBROOD_CRYSTAL_NAME"] = "Cristal de Resplandor",
["ENEMY_GLARELING_DESCRIPTION"] = "En su avance desenfrenado, estas débiles criaturas pueden sobrepasar incluso al ejército más fuerte.",
["ENEMY_GLARELING_EXTRA"] = "- Velocidad alta",
["ENEMY_GLARELING_NAME"] = "Glareling",
["ENEMY_GLARENWARDEN_DESCRIPTION"] = "Estas abominables arañas son el resultado de la fusión de las Arañas de Resplandor, haciéndolas más fuertes y resistentes que nunca.",
["ENEMY_GLARENWARDEN_EXTRA"] = "- Armadura alta\n- Roba vida al atacar",
["ENEMY_GLARENWARDEN_NAME"] = "Custodio de Resplandor",
["ENEMY_GOLDEN_EYED_DESCRIPTION"] = "Una bestia colosal cuyo rugido infunde miedo en los corazones de sus enemigos.",
["ENEMY_GOLDEN_EYED_EXTRA"] = "- Minijefe\n- Aturde unidades en un área cuando es bloqueado\n- Aturde torres",
["ENEMY_GOLDEN_EYED_NAME"] = "Golden-Eyed Beast",
["ENEMY_HARDENED_HORROR_DESCRIPTION"] = "Esta raza de Horrores tiene garras afiladas para despedazar a sus enemigos.",
["ENEMY_HARDENED_HORROR_EXTRA"] = "- Rueda a gran velocidad y no puede ser bloqueado cuando es afectado por el Resplandor.",
["ENEMY_HARDENED_HORROR_NAME"] = "Horror Garrafilada",
["ENEMY_HELLFIRE_WARLOCK_DESCRIPTION"] = "Hechiceros extremadamente peligrosos, expertos en invocar criaturas y llamas desde las profundidades del infierno.",
["ENEMY_HELLFIRE_WARLOCK_EXTRA"] = "- Lanza bolas de fuego\n- Invoca a un Zorro de Nueve Colas",
["ENEMY_HELLFIRE_WARLOCK_NAME"] = "Hellfire Warlock",
["ENEMY_HOG_INVADER_DESCRIPTION"] = "Alborotadores sucios y desorganizados. El grueso del ejército de Bestias Salvajes.",
["ENEMY_HOG_INVADER_EXTRA"] = "- Vida baja",
["ENEMY_HOG_INVADER_NAME"] = "Puerco Invasor",
["ENEMY_HYENA5_DESCRIPTION"] = "Luchadores repugnantes que se dan un festín con los enemigos caidos.",
["ENEMY_HYENA5_EXTRA"] = "- Armadura media\n- Se cura devorando a las unidades del jugador",
["ENEMY_HYENA5_NAME"] = "Hiena Colmillopodrido",
["ENEMY_HYENA5_SPECIAL"] = "Se cura devorando a los enemigos caidos.",
["ENEMY_KILLERTILE_DESCRIPTION"] = "Poderosos destructores, años de experiencia en combate (o un pollo) les han dejado un mordisco fuerte y letal. ",
["ENEMY_KILLERTILE_EXTRA"] = "- HP Alto\n- Daño Alto",
["ENEMY_KILLERTILE_NAME"] = "Killertile ",
["ENEMY_LESSER_EYE_DESCRIPTION"] = "Ojos malignos que flotan sobre el campo de batalla, actuando como avanzada de los Criadores Viles.",
["ENEMY_LESSER_EYE_EXTRA"] = "- Volador",
["ENEMY_LESSER_EYE_NAME"] = "Ojo Menor",
["ENEMY_LESSER_SISTER_DESCRIPTION"] = "Con su magia maliciosa, las Hermanas Retorcidas traen Pesadillas al mundo físico.",
["ENEMY_LESSER_SISTER_EXTRA"] = "- Resistencia mágica alta\n- Invoca Pesadillas",
["ENEMY_LESSER_SISTER_NAME"] = "Hermana Retorcida",
["ENEMY_LESSER_SISTER_NIGHTMARE_DESCRIPTION"] = "Sombras etéreas hilvanadas del libro de cánticos de las Hermanas del Culto.",
["ENEMY_LESSER_SISTER_NIGHTMARE_EXTRA"] = "- No puede ser objetivo de las torres a menos que sea bloqueada por unidades",
["ENEMY_LESSER_SISTER_NIGHTMARE_NAME"] = "Pesadilla",
["ENEMY_LESSER_SISTER_SPECIAL"] = "Invoca Pesadillas",
["ENEMY_MACHINIST_DESCRIPTION"] = "Obsesionado con motores y engranajes, este enano vive para la automatización industrial.",
["ENEMY_MACHINIST_EXTRA"] = "- Opera una línea de ensamblaje que produce Centinelas",
["ENEMY_MACHINIST_NAME"] = "Grymbeard",
["ENEMY_MAD_TINKERER_DESCRIPTION"] = "A los Reparadores no les importa mucho lo que no tenga que ver con construir cosas con chatarra.",
["ENEMY_MAD_TINKERER_EXTRA"] = "- Crea Drones utilizando chatarra de otras unidades",
["ENEMY_MAD_TINKERER_NAME"] = "Mecánico Loco",
["ENEMY_MINDLESS_HUSK_DESCRIPTION"] = "Por su apariencia, los Despojos parecen débiles pero cada uno lleva dentro una sorpresa para los enemigos.",
["ENEMY_MINDLESS_HUSK_EXTRA"] = "- Lanza un Glareling cuando muere",
["ENEMY_MINDLESS_HUSK_NAME"] = "Despojo Obediente",
["ENEMY_NINE_TAILED_FOX_DESCRIPTION"] = "Criaturas misteriosas, tan hermosas como poderosas. Arrasarán con los enemigos como una hoguera furiosa.",
["ENEMY_NINE_TAILED_FOX_EXTRA"] = "- Resistencia mágica media\n- Se teletransporta hacia adelante, aturdiendo a los enemigos al llegar\n- Daño de área",
["ENEMY_NINE_TAILED_FOX_NAME"] = "Zorro de Nueve Colas",
["ENEMY_NOXIOUS_HORROR_DESCRIPTION"] = "Criatura de aspecto anfibio que escupe bilis venenosa sobre su presa. También es peligrosa de cerca.",
["ENEMY_NOXIOUS_HORROR_EXTRA"] = "- Gana Resistencia Mágica y emite un aura venenosa cuando es afectado por el Resplandor.",
["ENEMY_NOXIOUS_HORROR_NAME"] = "Escupidor Nocivo",
["ENEMY_PALACE_GUARD_DESCRIPTION"] = "Reclutas poco talentosos cuya única motivación es cumplir los deseos de su Princesa.",
["ENEMY_PALACE_GUARD_EXTRA"] = "- Cuerpo a cuerpo\n- Armadura baja",
["ENEMY_PALACE_GUARD_NAME"] = "Guardia del Palacio",
["ENEMY_PUMPKIN_WITCH_DESCRIPTION"] = "Enemigo convertido en calabaza. Fácil de pisotear.",
["ENEMY_PUMPKIN_WITCH_EXTRA"] = "- Imbloqueable",
["ENEMY_PUMPKIN_WITCH_FLYING_DESCRIPTION"] = "Enemigo convertido en calabaza. Fácil de pisotear.",
["ENEMY_PUMPKIN_WITCH_FLYING_EXTRA"] = "- Imbloqueable",
["ENEMY_PUMPKIN_WITCH_FLYING_NAME"] = "Calabaza",
["ENEMY_PUMPKIN_WITCH_NAME"] = "Calabaza",
["ENEMY_QIONGQI_DESCRIPTION"] = "Leones voladores feroces que atacan con el poder del rayo. Los reyes de la tempestad.",
["ENEMY_QIONGQI_EXTRA"] = "- Volador\n- Daño muy alto\n- Resistencia mágica media",
["ENEMY_QIONGQI_NAME"] = "Qiongqi",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_DESCRIPTION"] = "Después de años de entregar pollos a sus hermanos, se han vuelto tan rápidos que a veces olvidan incluso traer el pollo.",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_EXTRA"] = "- Rápido\n- A distancia\n- Puede entregar una pata de pollo a un Gator, haciéndolo evolucionar",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_NAME"] = "Pies Ligeros",
["ENEMY_QUICKFEET_GATOR_DESCRIPTION"] = "Después de años de entregar pollos a sus hermanos, se han vuelto tan rápidos que a veces olvidan incluso traer el pollo.",
["ENEMY_QUICKFEET_GATOR_EXTRA"] = "- Rápido\n- A distancia\n- Puede entregar una pata de pollo a un Gator, haciéndolo evolucionar",
["ENEMY_QUICKFEET_GATOR_NAME"] = "Pies Ligeros",
["ENEMY_REVENANT_HARVESTER_DESCRIPTION"] = "Antiguas sacerdotizas que deambulan por el bosque, extendiendo su influencia a través de los espectros.",
["ENEMY_REVENANT_HARVESTER_EXTRA"] = "- Convierte a espectros cercanos en Segadoras",
["ENEMY_REVENANT_HARVESTER_NAME"] = "Segadora Renacida",
["ENEMY_REVENANT_SOULCALLER_DESCRIPTION"] = "Los magos elfos no resistieron el llamado de la magia oscura y se levantan de la tumba para invocar espectros de los caídos.",
["ENEMY_REVENANT_SOULCALLER_EXTRA"] = "- Bloquea torres\n- Invoca espectros",
["ENEMY_REVENANT_SOULCALLER_NAME"] = "Llamador Renacido",
["ENEMY_RHINO_DESCRIPTION"] = "Ariete viviente que arrolla todo a su paso dentro del campo de batalla.",
["ENEMY_RHINO_EXTRA"] = "- Mini jefe\n- Carga hacia los enemigos",
["ENEMY_RHINO_NAME"] = "Rinoceronte Arrasador",
["ENEMY_RHINO_SPECIAL"] = "Carga hacia los enemigos.",
["ENEMY_ROLLING_SENTRY_DESCRIPTION"] = "Una vez que los derriban, continúan la persecusión en el suelo.",
["ENEMY_ROLLING_SENTRY_EXTRA"] = "- Se convierte en chatarra cuando muere\n- A distancia",
["ENEMY_ROLLING_SENTRY_NAME"] = "Centinela Rodante",
["ENEMY_SCRAP_DRONE_DESCRIPTION"] = "Construido de forma rústica con el objetivo de molestar a las tropas.",
["ENEMY_SCRAP_DRONE_EXTRA"] = "- Volador",
["ENEMY_SCRAP_DRONE_NAME"] = "Dron de Chatarra",
["ENEMY_SCRAP_SPEEDSTER_DESCRIPTION"] = "Ruidoso y molesto, adicto a la velocidad.",
["ENEMY_SCRAP_SPEEDSTER_EXTRA"] = "- Se convierte en chatarra cuando muere",
["ENEMY_SCRAP_SPEEDSTER_NAME"] = "Chatarra Veloz",
["ENEMY_SKUNK_BOMBARDIER_DESCRIPTION"] = "Llevando sus toxinas naturales a otro nivel, los zorrillos desordenan las filas enemigas.",
["ENEMY_SKUNK_BOMBARDIER_EXTRA"] = "- Resistencia mágica media\n- Debilita a las unidades del jugador\n- Explota al morir",
["ENEMY_SKUNK_BOMBARDIER_NAME"] = "Zorrillo Bombardero",
["ENEMY_SKUNK_BOMBARDIER_SPECIAL"] = "Sus ataques debilitan a las unidades del jugador. Explota al morir, haciendo daño.",
["ENEMY_SMALL_STALKER_DESCRIPTION"] = "Corrompidas por la magia del Culto, estas Acechanubes se teletransportan por el campo de batalla sembrando el caos.",
["ENEMY_SMALL_STALKER_EXTRA"] = "- Se teletransporta hacia adelante cuando es atacado",
["ENEMY_SMALL_STALKER_NAME"] = "Acechanubes Corrupto",
["ENEMY_SMALL_STALKER_SPECIAL"] = "Se teletransporta una corta distancia, evadiendo ataques.",
["ENEMY_SPECTER_DESCRIPTION"] = "Esclavizados más allá de la muerte, atados a aterrorizar a los vivos.",
["ENEMY_SPECTER_EXTRA"] = "- Puede interactuar con otros enemigos y elementos",
["ENEMY_SPECTER_NAME"] = "Espectro",
["ENEMY_SPIDEAD_DESCRIPTION"] = "Descendientes directos de la Reina Araña Mygale, estas arañas siempre encuentran una forma de ser molestas, incluso desde más allá de la tumba.",
["ENEMY_SPIDEAD_EXTRA"] = "- Resistencia mágica\n- Genera una telaraña al morir",
["ENEMY_SPIDEAD_NAME"] = "Hija de Seda",
["ENEMY_SPIDERLING_DESCRIPTION"] = "Arañas mejoradas por la magia del Culto. Rápidas y furiosas. Muerden.",
["ENEMY_SPIDERLING_EXTRA"] = "- Velocidad alta\n- Resistencia mágica baja",
["ENEMY_SPIDERLING_NAME"] = "Araña de Resplandor",
["ENEMY_SPIDER_PRIEST_DESCRIPTION"] = "Atrapados por su nuevo dios, los sacerdotes entran al campo de batalla empuñando magia oscura.",
["ENEMY_SPIDER_PRIEST_EXTRA"] = "- Alta resistencia mágica\n- Se convierte en un Custodio de Resplandor al borde de la muerte",
["ENEMY_SPIDER_PRIEST_NAME"] = "Sacerdote de la Red",
["ENEMY_SPIDER_SISTER_DESCRIPTION"] = "Fervientes creyentes de la Reina Araña, emplean su magia para invocar a sus parientes.",
["ENEMY_SPIDER_SISTER_EXTRA"] = "- Resistencia mágica\n- Invoca Arañas de Resplandor",
["ENEMY_SPIDER_SISTER_NAME"] = "Hermana Araña",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_DESCRIPTION"] = "Duplicados sombríos que Mydrias utiliza para afectar el campo de batalla de diferentes formas.",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_EXTRA"] = "- Protege del daño a los enemigos\n- Atrapa torres con tentáculos oscuros",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_NAME"] = "Ilusión de Mydrias",
["ENEMY_STORM_ELEMENTAL_DESCRIPTION"] = "Elementales poderosos nacidos de tifones, rayos e ira. Un primo lejano del Espíritu de Ceniza.",
["ENEMY_STORM_ELEMENTAL_EXTRA"] = "- Armadura alta\n- A distancia\n- Aturde una torre cercana al morir",
["ENEMY_STORM_ELEMENTAL_NAME"] = "Espíritu de la Tempestad",
["ENEMY_STORM_SPIRIT_DESCRIPTION"] = "Pequeños dragones saltando entre nubes de tormenta, esquivando peligros y enemigos con agilidad.",
["ENEMY_STORM_SPIRIT_EXTRA"] = "- Volador\n- Baja resistencia mágica\n- Se lanza hacia adelante al recibir daño",
["ENEMY_STORM_SPIRIT_NAME"] = "Dracillo de Tormenta",
["ENEMY_SURVEILLANCE_SENTRY_DESCRIPTION"] = "Creado por los enanos para mantener un ojo en los enemigos desde lo alto.",
["ENEMY_SURVEILLANCE_SENTRY_EXTRA"] = "- Volador\n- Se convierte en un Centinela Rodante cuando es derrotado",
["ENEMY_SURVEILLANCE_SENTRY_NAME"] = "Centinela Volador",
["ENEMY_SURVEYOR_HARPY_DESCRIPTION"] = "Buscando carroña, los buitres siguen a las Bestias Salvajes a todos lados.",
["ENEMY_SURVEYOR_HARPY_EXTRA"] = "- Volador",
["ENEMY_SURVEYOR_HARPY_NAME"] = "Buitre Patrullero",
["ENEMY_SURVEYOR_HARPY_SPECIAL"] = "Volador.",
["ENEMY_TERRACOTA_DESCRIPTION"] = "Sombras manifestadas que sirven de distracción.",
["ENEMY_TERRACOTA_EXTRA"] = "- Cuerpo a cuerpo",
["ENEMY_TERRACOTA_NAME"] = "Cebo Ilusorio",
["ENEMY_TOWER_RAY_SHEEP_DESCRIPTION"] = "Baaaaaa.",
["ENEMY_TOWER_RAY_SHEEP_EXTRA"] = "- Inbloqueable",
["ENEMY_TOWER_RAY_SHEEP_FLYING_DESCRIPTION"] = "Baaaaaa.",
["ENEMY_TOWER_RAY_SHEEP_FLYING_EXTRA"] = "- Volador",
["ENEMY_TOWER_RAY_SHEEP_FLYING_NAME"] = "Oveja Voladora",
["ENEMY_TOWER_RAY_SHEEP_NAME"] = "Oveja",
["ENEMY_TURTLE_SHAMAN_DESCRIPTION"] = "De espíritu mezquino a pesar de verse pacíficos, los chamanes mantienen a las Bestias Salvajes saludables y listas para luchar.",
["ENEMY_TURTLE_SHAMAN_EXTRA"] = "- Resistencia mágica alta\n- Cura a las unidades enemigas",
["ENEMY_TURTLE_SHAMAN_NAME"] = "Tortuga Chamán",
["ENEMY_TURTLE_SHAMAN_SPECIAL"] = "Cura a las unidades enemigas.",
["ENEMY_TUSKED_BRAWLER_DESCRIPTION"] = "Más tenaces que los invasores, usan armaduras de mala calidad. Siempre listos para la trifulca.",
["ENEMY_TUSKED_BRAWLER_EXTRA"] = "- Armadura baja",
["ENEMY_TUSKED_BRAWLER_NAME"] = "Peleador Colmilludo",
["ENEMY_UNBLINDED_ABOMINATION_DESCRIPTION"] = "Sacerdotes del Culto completamente corrompidos. Conocidos por su brutalidad en combate.",
["ENEMY_UNBLINDED_ABOMINATION_EXTRA"] = "- Devora unidades con poca vida",
["ENEMY_UNBLINDED_ABOMINATION_NAME"] = "Abominación del Culto",
["ENEMY_UNBLINDED_ABOMINATION_SPECIAL"] = "Ocasionalmente devora a una unidad con poca vida.",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_DESCRIPTION"] = "Después de esclavizar a los elfos, algunas Abominaciones fueron seleccionadas para asegurarse de que el trabajo en las minas vaya sobre ruedas.",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_EXTRA"] = "- Debe morir para liberar a los elfos",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_NAME"] = "Capataz Abominación",
["ENEMY_UNBLINDED_PRIEST_DESCRIPTION"] = "Entre oraciones y sermones sobre lo oculto, los sacerdotes saltan a la batalla con magia oscura.",
["ENEMY_UNBLINDED_PRIEST_EXTRA"] = "- Resistencia mágica alta\n- Se convierte en una Abominación cuando está cerca de morir",
["ENEMY_UNBLINDED_PRIEST_NAME"] = "Sacerdote del Culto",
["ENEMY_UNBLINDED_PRIEST_SPECIAL"] = "Cuando tiene poca vida se convierte en una Abominación.",
["ENEMY_UNBLINDED_SHACKLER_DESCRIPTION"] = "Canalizando magia corrupta a través de los cristales de sus brazos, los Encadenadores son enemigos temibles a corta distancia.",
["ENEMY_UNBLINDED_SHACKLER_EXTRA"] = "- Resistencia mágica media\n- Inutiliza torres cuando tiene poca vida",
["ENEMY_UNBLINDED_SHACKLER_NAME"] = "Encadenador",
["ENEMY_UNBLINDED_SHACKLER_SPECIAL"] = "Inutiliza torres, previniendo sus ataques.",
["ENEMY_VILE_SPAWNER_DESCRIPTION"] = "Lanzando sus ojos voladores hacia los enemigos, los Criadores Viles ven siempre en todas direcciones.",
["ENEMY_VILE_SPAWNER_EXTRA"] = "- Engendra Ojos Menores",
["ENEMY_VILE_SPAWNER_NAME"] = "Criador Vil",
["ENEMY_WATER_SORCERESS_DESCRIPTION"] = "Hechiceros elementales veteranos que dominan el poder del agua para curar a sus aliados y derrotar enemigos desde lejos.",
["ENEMY_WATER_SORCERESS_EXTRA"] = "- A distancia\n- Resistencia mágica media\n- Cura a los aliados",
["ENEMY_WATER_SORCERESS_NAME"] = "Water Master",
["ENEMY_WATER_SPIRIT_DESCRIPTION"] = "Entidades acuáticas sin alma que avanzan en olas implacables, arrasando las costas con furia.",
["ENEMY_WATER_SPIRIT_EXTRA"] = "- Baja resistencia mágica\n- Puede surgir del agua",
["ENEMY_WATER_SPIRIT_NAME"] = "Water Spirit",
["ENEMY_WATER_SPIRIT_SPAWNLESS_DESCRIPTION"] = "Entidades acuáticas sin alma que avanzan en olas implacables, arrasando las costas con furia.",
["ENEMY_WATER_SPIRIT_SPAWNLESS_EXTRA"] = "- Baja resistencia mágica\n- Puede surgir del agua",
["ENEMY_WATER_SPIRIT_SPAWNLESS_NAME"] = "Water Spirit",
["ENEMY_WUXIAN_DESCRIPTION"] = "Poderosos y duraderos magos que aniquilan a sus enemigos con magia.",
["ENEMY_WUXIAN_EXTRA"] = "- A distancia\n- Armadura media\n- Ataque especial en suelo en llamas",
["ENEMY_WUXIAN_NAME"] = "Wuxian",
["ERROR_MESSAGE_GENERIC"] = "¡Ups! Algo salió mal.",
["Earn huge bonus points and gold by calling waves earlier!"] = "¡Gana una gran cantidad de puntos de bonificación y de oro por convocar oleadas con antelación!",
["Encyclopedia"] = "Enciclopedia",
["Enemies"] = "Enemigos",
["Extreme"] = "Extremo",
["FIRST_WEEK_PACK"] = "Regalo",
["Face an endless unrelenting enemy force and try to defeat as many as possible to comete for the best score!"] = "Enfrenta un ejército enemigo interminable e implacable y derrota a todas las tropas que puedas para competir por la mejor puntuación.",
["Face an endless unrelenting enemy force and try to defeat as many as possible to compete for the best score!"] = "Enfrenta un ejército enemigo interminable e implacable y derrota a todas las tropas que puedas para competir por la mejor puntuación.",
["Fast"] = "Rápida",
["For beginners to strategy games!"] = "¡Para principiantes en juegos de estrategia!",
["GAME_TITLE_KR5"] = "Kingdom Rush 5: Alliance",
["GEMS_BARREL_NAME"] = "TONEL DE GEMAS",
["GEMS_CHEST_NAME"] = "COFRE DE GEMAS",
["GEMS_HANDFUL_NAME"] = "PUÑADO DE GEMAS",
["GEMS_MOUNTAIN_NAME"] = "MONTAÑA DE GEMAS",
["GEMS_POUCH_NAME"] = "BOLSO DE GEMAS",
["GEMS_WAGON_NAME"] = "CARRO DE GEMAS",
["GET_ALL_AWESOME_HEROES"] = "OBTÉN TODOS ESTOS HÉROES INCREÍBLES",
["GET_THIS_AWESOME"] = "OBTÉN ESTE\nHÉROE INCREÍBLE",
["GET_THIS_AWESOME_2"] = "OBTÉN ESTOS\n HÉROES INCREÍBLES",
["GET_THIS_AWESOME_3"] = "OBTÉN ESTOS\n HÉROES INCREÍBLES",
["GIFT_CLAIMED"] = "Regalo reclamado!",
["GOOGLE_PLAY"] = "GOOGLE PLAY",
["Got it!"] = "¡Entendido!",
["Great"] = "Grande",
["HERO LEVEL UP!"] = "¡TU HÉROE SUBIÓ DE NIVEL!",
["HERO ROOM"] = "HÉROES",
["HERO UNLOCKED!"] = "¡HÉROE DESBLOQUEADO!",
["HEROES"] = "HÉROES",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_1"] = "Llama grifos que vuelan sobre el área durante %$heroes.hero_bird.ultimate.bird.duration[2]%$ segundos atacando a los enemigos, haciendo %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[2]%$ de daño con cada ataque.",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_2"] = "Llama grifos que vuelan sobre el área durante %$heroes.hero_bird.ultimate.bird.duration[3]%$ segundos atacando a los enemigos, haciendo %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[3]%$ de daño con cada ataque.",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_3"] = "Llama grifos que vuelan sobre el área durante %$heroes.hero_bird.ultimate.bird.duration[4]%$ segundos atacando a los enemigos, haciendo %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[4]%$ de daño con cada ataque.",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_DESCRIPTION"] = "Invoca grifos que vuelan sobre el área atacando a los enemigos.",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_NAME"] = "Aves de Combate",
["HERO_BIRD_BIRDS_OF_PREY_TITLE"] = "AVES DE COMBATE",
["HERO_BIRD_CLASS"] = "el Jinete Experto",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_1"] = "Lanza un proyectil que se abre sobre los enemigos, haciendo %$heroes.hero_bird.cluster_bomb.explosion_damage_min[1]%$ de daño por explosivo e incendiando el piso por %$heroes.hero_bird.cluster_bomb.fire_duration[1]%$ segundos, quemando a los enemigos por %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$ de daño a lo largo de 3 segundos",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_2"] = "Lanza un proyectil que se abre sobre los enemigos, haciendo %$heroes.hero_bird.cluster_bomb.explosion_damage_min[2]%$ de daño por explosivo e incendiando el piso por %$heroes.hero_bird.cluster_bomb.fire_duration[2]%$ segundos, quemando a los enemigos por %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$ de daño a lo largo de 3 segundos",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_3"] = "Lanza un proyectil que se abre sobre los enemigos, haciendo %$heroes.hero_bird.cluster_bomb.explosion_damage_min[3]%$ de daño por explosivo e incendiando el piso por %$heroes.hero_bird.cluster_bomb.fire_duration[3]%$ segundos, quemando a los enemigos por %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$ de daño a lo largo de 3 segundos",
["HERO_BIRD_CLUSTER_BOMB_TITLE"] = "BOMBARDEO DE SATURACIÓN",
["HERO_BIRD_DESC"] = "El valeroso jinete de grifos vuela hacia la batalla presumiendo su arsenal de fuego y hierro. Si bien se unió a la Alianza a regañadientes porque el Ejército Oscuro invadió su hogar, Broden aceptó bombardear a las fuerzas del culto en un intento de restaurar el status quo en Linirea.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_1"] = "El grifo se lanza en picado hacia el piso para devorar a un enemigo de hasta %$heroes.hero_bird.eat_instakill.hp_max[1]%$ vida.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_2"] = "El grifo se lanza en picado hacia el piso para devorar a un enemigo de hasta %$heroes.hero_bird.eat_instakill.hp_max[2]%$ vida.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_3"] = "El grifo se lanza en picado hacia el piso para devorar a un enemigo de hasta %$heroes.hero_bird.eat_instakill.hp_max[3]%$ vida.",
["HERO_BIRD_EAT_INSTAKILL_TITLE"] = "CACERÍA EN PICADO",
["HERO_BIRD_GATTLING_DESCRIPTION_1"] = "Acribilla a un enemigo a balazos, haciendo %$heroes.hero_bird.gattling.s_damage_min[1]%$-%$heroes.hero_bird.gattling.s_damage_max[1]%$ de daño físico.",
["HERO_BIRD_GATTLING_DESCRIPTION_2"] = "Acribilla a un enemigo a balazos, haciendo %$heroes.hero_bird.gattling.s_damage_min[2]%$-%$heroes.hero_bird.gattling.s_damage_max[2]%$ de daño físico.",
["HERO_BIRD_GATTLING_DESCRIPTION_3"] = "Acribilla a un enemigo a balazos, haciendo %$heroes.hero_bird.gattling.s_damage_min[3]%$-%$heroes.hero_bird.gattling.s_damage_max[3]%$ de daño físico.",
["HERO_BIRD_GATTLING_TITLE"] = "PLOMO EJEMPLAR",
["HERO_BIRD_NAME"] = "Broden",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_1"] = "El grifo produce un chillido ensordecedor, aturdiendo enemigos por %$heroes.hero_bird.shout_stun.stun_duration[1]%$ segundo y luego enlenteciéndolos por %$heroes.hero_bird.shout_stun.slow_duration[1]%$.",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_2"] = "El grifo produce un chillido ensordecedor, aturdiendo enemigos por %$heroes.hero_bird.shout_stun.stun_duration[2]%$ segundos y luego enlenteciéndolos por %$heroes.hero_bird.shout_stun.slow_duration[2]%$.",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_3"] = "El grifo produce un chillido ensordecedor, aturdiendo enemigos por %$heroes.hero_bird.shout_stun.stun_duration[3]%$ segundos y luego enlenteciéndolos por %$heroes.hero_bird.shout_stun.slow_duration[3]%$.",
["HERO_BIRD_SHOUT_STUN_TITLE"] = "CHILLIDO ATEMORIZANTE",
["HERO_BUILDER_CLASS"] = "Capataz Maestro",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_1"] = "Construye una torre provisoria desde la que un constructor ataca a los enemigos cercanos. La torre dura %$heroes.hero_builder.defensive_turret.duration[1]%$ segundos y hace %$heroes.hero_builder.defensive_turret.attack.damage_min[1]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[1]%$ de daño físico.",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_2"] = "Construye una torre provisoria desde la que un constructor ataca a los enemigos cercanos. La torre dura %$heroes.hero_builder.defensive_turret.duration[2]%$ segundos y hace %$heroes.hero_builder.defensive_turret.attack.damage_min[2]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[2]%$ de daño físico.",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_3"] = "Construye una torre provisoria desde la que un constructor ataca a los enemigos cercanos. La torre dura %$heroes.hero_builder.defensive_turret.duration[3]%$ segundos y hace %$heroes.hero_builder.defensive_turret.attack.damage_min[3]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[3]%$ de daño físico.",
["HERO_BUILDER_DEFENSIVE_TURRET_TITLE"] = "TORRETA DEFENSIVA",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_1"] = "Revolea rápidamente su viga de madera, haciendo %$heroes.hero_builder.demolition_man.s_damage_min[1]%$-%$heroes.hero_builder.demolition_man.s_damage_max[1]%$ de daño físico a los enemigos a su alrededor.",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_2"] = "Revolea rápidamente su viga de madera, haciendo %$heroes.hero_builder.demolition_man.s_damage_min[2]%$-%$heroes.hero_builder.demolition_man.s_damage_max[2]%$ de daño físico a los enemigos a su alrededor.",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_3"] = "Revolea rápidamente su viga de madera, haciendo %$heroes.hero_builder.demolition_man.s_damage_min[3]%$-%$heroes.hero_builder.demolition_man.s_damage_max[3]%$ de daño físico a los enemigos a su alrededor.",
["HERO_BUILDER_DEMOLITION_MAN_TITLE"] = "DEMOLEDOR",
["HERO_BUILDER_DESC"] = "Años a cargo de la construcción de las defensas de Linirea le enseñaron a Torres un par de cosas sobre la guerra. Cansado de ser espectador cada vez que el reino está en peligro, el capataz no duda en poner sus herramientas y conocimientos al servicio de la Alianza.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_1"] = "Torres deja de pelear para comer un bocadillo, curándose %$heroes.hero_builder.lunch_break.heal_hp[1]%$ de vida.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_2"] = "Torres deja de pelear para comer un bocadillo, curándose %$heroes.hero_builder.lunch_break.heal_hp[2]%$ de vida.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_3"] = "Torres deja de pelear para comer un bocadillo, curándose %$heroes.hero_builder.lunch_break.heal_hp[3]%$ de vida.",
["HERO_BUILDER_LUNCH_BREAK_TITLE"] = "HORA DEL ALMUERZO",
["HERO_BUILDER_NAME"] = "Torres",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_1"] = "Llama a dos constructores que luchan a su lado por %$heroes.hero_builder.overtime_work.soldier.duration%$ segundos.",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_2"] = "Los constructores tienen %$heroes.hero_builder.overtime_work.soldier.hp_max[2]%$ de vida y hacen %$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[2]%$ de daño físico. Luchan por %$heroes.hero_builder.overtime_work.soldier.duration%$ segundos.",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_3"] = "Los constructores tienen %$heroes.hero_builder.overtime_work.soldier.hp_max[3]%$ de vida y hacen %$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[3]%$ de daño físico. Luchan por %$heroes.hero_builder.overtime_work.soldier.duration%$ segundos.",
["HERO_BUILDER_OVERTIME_WORK_TITLE"] = "OBREROS TRABAJANDO",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_1"] = "Suelta una bola de acero gigante sobre el camino, haciendo %$heroes.hero_builder.ultimate.damage[2]%$ de daño y aturdiendo a los enemigos por %$heroes.hero_builder.ultimate.stun_duration[2]%$ segundos.",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_2"] = "Suelta una bola de acero gigante sobre el camino, haciendo %$heroes.hero_builder.ultimate.damage[3]%$ de daño y aturdiendo a los enemigos por %$heroes.hero_builder.ultimate.stun_duration[3]%$ segundos.",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_3"] = "Suelta una bola de acero gigante sobre el camino, haciendo %$heroes.hero_builder.ultimate.damage[4]%$ de daño y aturdiendo a los enemigos por %$heroes.hero_builder.ultimate.stun_duration[4]%$ segundos.",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_DESCRIPTION"] = "Suelta una bola de demolición sobre el camino, dañando a los enemigos.",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_NAME"] = "Bola de Demolición",
["HERO_BUILDER_WRECKING_BALL_TITLE"] = "BOLA DE DEMOLICIÓN",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_1"] = "Sylvara desbloquea su forma verdadera por %$heroes.hero_dragon_arb.ultimate.duration[2]%$ segundos, en los cuales obtiene %$heroes.hero_dragon_arb.ultimate.s_bonuses[2]%$% de daño, velocidad, resistencias y evoluciona algunos de sus poderes.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_2"] = "Sylvara desbloquea su forma verdadera por %$heroes.hero_dragon_arb.ultimate.duration[3]%$ segundos, en los cuales obtiene %$heroes.hero_dragon_arb.ultimate.s_bonuses[3]%$% de daño, velocidad, resistencias y evoluciona algunos de sus poderes.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_3"] = "Sylvara desbloquea su forma verdadera por %$heroes.hero_dragon_arb.ultimate.duration[4]%$ segundos, en los cuales obtiene %$heroes.hero_dragon_arb.ultimate.s_bonuses[4]%$% de daño, velocidad, resistencias y evoluciona algunos de sus poderes.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_DESCRIPTION"] = "Desata la verdadera forma de Sylvara.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_NAME"] = "Naturaleza Interior",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_TITLE"] = "Naturaleza Interior",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_1"] = "Transforma parches verdes en arbóreos que luchan durante %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[1]%$ segundos, durante la Naturaleza Interior, invoca arbóreos más fuertes.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_2"] = "Transforma parches verdes en arbóreos que luchan durante %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[2]%$ segundos, durante la Naturaleza Interior, invoca arbóreos más fuertes.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_3"] = "Transforma parches verdes en arbóreos que luchan durante %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[3]%$ segundos, durante la Naturaleza Interior, invoca arbóreos más fuertes.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_TITLE"] = "Llamada del bosque",
["HERO_DRAGON_ARB_CLASS"] = "Fuerza de la Naturaleza",
["HERO_DRAGON_ARB_DESC"] = "El dragón de la naturaleza y protector de los arbóreos, teje bosques con su aliento y hace bailar al viento con sus alas. Como la naturaleza misma, puede ser tanto cuidadosa como punitiva. ¡Asegúrate de no tirar basura!",
["HERO_DRAGON_ARB_NAME"] = "Sylvara",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_1"] = "Cada %$heroes.hero_dragon_arb.thorn_bleed.cooldown[1]%$ segundos, Sylvara potencia su próximo aliento para dañar a los enemigos dependiendo de su velocidad, durante la Naturaleza Interior tiene un %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[1]%$% de probabilidad de matar instantáneamente.",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_2"] = "Cada %$heroes.hero_dragon_arb.thorn_bleed.cooldown[2]%$ segundos, Sylvara potencia su próximo aliento para dañar a los enemigos dependiendo de su velocidad, durante la Naturaleza Interior tiene un %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[2]%$% de probabilidad de matar instantáneamente.",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_3"] = "Cada %$heroes.hero_dragon_arb.thorn_bleed.cooldown[3]%$ segundos, Sylvara potencia su próximo aliento para dañar a los enemigos dependiendo de su velocidad, durante la Naturaleza Interior tiene un %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[3]%$% de probabilidad de matar instantáneamente.",
["HERO_DRAGON_ARB_THORN BLEED_TITLE"] = "Aliento Espinoso",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_1"] = "Aumenta el daño de torres cercanas en un %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[1]%$% durante %$heroes.hero_dragon_arb.tower_runes.duration[1]%$ segundos.",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_2"] = "Aumenta el daño de torres cercanas en un %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[2]%$% durante %$heroes.hero_dragon_arb.tower_runes.duration[2]%$ segundos.",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_3"] = "Aumenta el daño de torres cercanas en un %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[3]%$% durante %$heroes.hero_dragon_arb.tower_runes.duration[3]%$ segundos.",
["HERO_DRAGON_ARB_TOWER RUNES_TITLE"] = "Raíces Profundas",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_1"] = "Invoca plantas cerca de torres que duran %$heroes.hero_dragon_arb.tower_plants.duration[1]%$ segundos. Dependiendo de su alianza, se convierten en plantas venenosas que causan daño y ralentizan, o plantas curativas que sanan a los aliados.",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_2"] = "Invoca plantas cerca de torres que duran %$heroes.hero_dragon_arb.tower_plants.duration[2]%$ segundos. Dependiendo de su alianza, se convierten en plantas venenosas que causan daño y ralentizan, o plantas curativas que sanan a los aliados.",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_3"] = "Invoca plantas cerca de torres que duran %$heroes.hero_dragon_arb.tower_plants.duration[3]%$ segundos. Dependiendo de su alianza, se convierten en plantas venenosas que causan daño y ralentizan, o plantas curativas que sanan a los aliados.",
["HERO_DRAGON_ARB_TOWER_PLANTS_TITLE"] = "Portador de Vida",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_1"] = "Lanza %$heroes.hero_dragon_bone.burst.proj_count[1]%$ proyectiles mágicos. Cada uno hace %$heroes.hero_dragon_bone.burst.damage_min[1]%$-%$heroes.hero_dragon_bone.burst.damage_max[1]%$ de daño verdadero y aplica plaga.",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_2"] = "Lanza %$heroes.hero_dragon_bone.burst.proj_count[2]%$ proyectiles mágicos. Cada uno hace %$heroes.hero_dragon_bone.burst.damage_min[2]%$-%$heroes.hero_dragon_bone.burst.damage_max[2]%$ de daño verdadero y aplica plaga.",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_3"] = "Lanza %$heroes.hero_dragon_bone.burst.proj_count[3]%$ proyectiles mágicos. Cada uno hace %$heroes.hero_dragon_bone.burst.damage_min[3]%$-%$heroes.hero_dragon_bone.burst.damage_max[3]%$ de daño verdadero y aplica plaga.",
["HERO_DRAGON_BONE_BURST_TITLE"] = "EXPLOSIÓN DE CONTAGIO",
["HERO_DRAGON_BONE_CLASS"] = "Dracolich",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_1"] = "Cubre un área con una nube pestilente que aplica plaga a los enemigos y los enlentece por %$heroes.hero_dragon_bone.cloud.duration[1]%$ segundos.",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_2"] = "Cubre un área con una nube pestilente que aplica plaga a los enemigos y los enlentece por %$heroes.hero_dragon_bone.cloud.duration[2]%$ segundos.",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_3"] = "Cubre un área con una nube pestilente que aplica plaga a los enemigos y los enlentece por %$heroes.hero_dragon_bone.cloud.duration[3]%$ segundos.",
["HERO_DRAGON_BONE_CLOUD_TITLE"] = "NUBE DE PLAGA",
["HERO_DRAGON_BONE_DESC"] = "Después de ser revivido por Vez'nan durante su campaña de conquista, Bonehart ofreció sus poderes para saldar su deuda persiguiendo a aquellos usuarios de magia que podrían amenazar los planes del Hechicero Oscuro.",
["HERO_DRAGON_BONE_NAME"] = "Bonehart",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_1"] = "Embiste hacia el camino, haciendo %$heroes.hero_dragon_bone.nova.damage_min[1]%$-%$heroes.hero_dragon_bone.nova.damage_max[1]%$ de daño explosivo a los enemigos y aplicándoles plaga.",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_2"] = "Embiste hacia el camino, haciendo %$heroes.hero_dragon_bone.nova.damage_min[2]%$-%$heroes.hero_dragon_bone.nova.damage_max[2]%$ de daño explosivo a los enemigos y aplicándoles plaga.",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_3"] = "Embiste hacia el camino, haciendo %$heroes.hero_dragon_bone.nova.damage_min[3]%$-%$heroes.hero_dragon_bone.nova.damage_max[3]%$ de daño explosivo a los enemigos y aplicándoles plaga.",
["HERO_DRAGON_BONE_NOVA_TITLE"] = "AZOTE ENFERMIZO",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_1"] = "Lanza %$heroes.hero_dragon_bone.rain.bones_count[1]%$ espinas de hueso hacia los enemigos, haciendo %$heroes.hero_dragon_bone.rain.damage_min[1]%$-%$heroes.hero_dragon_bone.rain.damage_max[1]%$ de daño verdadero y aturdiéndolos brevemente.",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_2"] = "Lanza %$heroes.hero_dragon_bone.rain.bones_count[2]%$ espinas de hueso hacia los enemigos, haciendo %$heroes.hero_dragon_bone.rain.damage_min[2]%$-%$heroes.hero_dragon_bone.rain.damage_max[2]%$ de daño verdadero y aturdiéndolos brevemente.",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_3"] = "Lanza %$heroes.hero_dragon_bone.rain.bones_count[3]%$ espinas de hueso hacia los enemigos, haciendo %$heroes.hero_dragon_bone.rain.damage_min[3]%$-%$heroes.hero_dragon_bone.rain.damage_max[3]%$ de daño verdadero y aturdiéndolos brevemente.",
["HERO_DRAGON_BONE_RAIN_TITLE"] = "LLUVIA DE ESPINAS",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_1"] = "Invoca dos dracos de hueso. Cada draco tiene %$heroes.hero_dragon_bone.ultimate.dog.hp[2]%$ vida y hace %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[2]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[2]%$ de daño físico.",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_2"] = "Invoca dos dracos de hueso. Cada draco tiene %$heroes.hero_dragon_bone.ultimate.dog.hp[3]%$ vida y hace %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[3]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[3]%$ de daño físico.",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_3"] = "Invoca dos dracos de hueso. Cada draco tiene %$heroes.hero_dragon_bone.ultimate.dog.hp[4]%$ vida y hace %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[4]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[4]%$ de daño físico.",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_DESCRIPTION"] = "Invoca dos dracos de hueso.",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_NAME"] = "Alzar Dracos",
["HERO_DRAGON_BONE_RAISE_DRAKES_TITLE"] = "ALZAR DRACOS",
["HERO_DRAGON_GEM_CLASS"] = "Inquebrantable",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_1"] = "Atrapa a un enemigo de hasta %$heroes.hero_dragon_gem.crystal_instakill.hp_max[1]%$ de vida en un cristal por unos segundos. El cristal explota eliminando al objetivo y haciendo %$heroes.hero_dragon_gem.crystal_instakill.s_damage[1]%$ de daño verdadero a su alrededor.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_2"] = "Atrapa a un enemigo de hasta %$heroes.hero_dragon_gem.crystal_instakill.hp_max[2]%$ de vida en un cristal por unos segundos. El cristal explota eliminando al objetivo y haciendo %$heroes.hero_dragon_gem.crystal_instakill.s_damage[2]%$ de daño verdadero a su alrededor.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_3"] = "Atrapa a un enemigo de hasta %$heroes.hero_dragon_gem.crystal_instakill.hp_max[3]%$ de vida en un cristal por unos segundos. El cristal explota eliminando al objetivo y haciendo %$heroes.hero_dragon_gem.crystal_instakill.s_damage[3]%$ de daño verdadero a su alrededor.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_TITLE"] = "TUMBA GRANATE",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_1"] = "Lanza un cristal en el camino, reduciendo la velocidad de los enemigos en un %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% y haciendo %$heroes.hero_dragon_gem.crystal_totem.s_damage[1]%$ de daño mágico cada 1 segundos. Dura %$heroes.hero_dragon_gem.crystal_totem.duration[1]%$ segundos.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_2"] = "Lanza un cristal en el camino, reduciendo la velocidad de los enemigos en un %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% y haciendo %$heroes.hero_dragon_gem.crystal_totem.s_damage[2]%$ de daño mágico cada 1 segundos. Dura %$heroes.hero_dragon_gem.crystal_totem.duration[2]%$ segundos.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_3"] = "Lanza un cristal en el camino, reduciendo la velocidad de los enemigos en un %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% y haciendo %$heroes.hero_dragon_gem.crystal_totem.s_damage[3]%$ de daño mágico cada 1 segundos. Dura %$heroes.hero_dragon_gem.crystal_totem.duration[3]%$ segundos.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_TITLE"] = "CONDUCTO DE PODER",
["HERO_DRAGON_GEM_DESC"] = "La tranquila vida de Kosmyr fue interrumpida cuando el Culto comenzó sus operaciones en el Cañón Abandonado. Para deshacerse de los invasores, el dragón hizo un trato con Vez'nan, uniéndose a la Alianza contra un enemigo mutuo.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_1"] = "Invoca %$heroes.hero_dragon_gem.ultimate.max_shards[2]%$ aluviones de cristales que hacen %$heroes.hero_dragon_gem.ultimate.damage_min[2]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[2]%$ de daño verdadero a los enemigos en el área.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_2"] = "Invoca %$heroes.hero_dragon_gem.ultimate.max_shards[3]%$ aluviones de cristales que hacen %$heroes.hero_dragon_gem.ultimate.damage_min[3]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[3]%$ de daño verdadero a los enemigos en el área.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_3"] = "Invoca %$heroes.hero_dragon_gem.ultimate.max_shards[4]%$ aluviones de cristales que hacen %$heroes.hero_dragon_gem.ultimate.damage_min[4]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[4]%$ de daño verdadero a los enemigos en el área.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_DESCRIPTION"] = "Lanza varias avalanchas de cristales hacia los enemigos.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_NAME"] = "Avalancha de Cristal",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_TITLE"] = "AVALANCHA DE CRISTAL",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_1"] = "Genera esquirlas de cristal a su alrededor sobre los caminos, haciendo %$heroes.hero_dragon_gem.floor_impact.damage_min[1]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[1]%$ de daño físico a los enemigos alcanzados.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_2"] = "Genera esquirlas de cristal a su alrededor sobre los caminos, haciendo %$heroes.hero_dragon_gem.floor_impact.damage_min[2]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[2]%$ de daño físico a los enemigos alcanzados.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_3"] = "Genera esquirlas de cristal a su alrededor sobre los caminos, haciendo %$heroes.hero_dragon_gem.floor_impact.damage_min[3]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[3]%$ de daño físico a los enemigos alcanzados.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_TITLE"] = "ESQUIRLAS PRISMÁTICAS",
["HERO_DRAGON_GEM_NAME"] = "Kosmyr",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_1"] = "Cristaliza a los enemigos, aturdiéndolos por %$heroes.hero_dragon_gem.stun.duration[1]%$ segundos.",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_2"] = "Cristaliza a los enemigos, aturdiéndolos por %$heroes.hero_dragon_gem.stun.duration[2]%$ segundos.",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_3"] = "Cristaliza a los enemigos, aturdiéndolos por %$heroes.hero_dragon_gem.stun.duration[3]%$ segundos.",
["HERO_DRAGON_GEM_STUN_TITLE"] = "ALIENTO PARALIZANTE",
["HERO_HUNTER_BEASTS_DESCRIPTION_1"] = "Invoca dos murciélagos que atacan a los enemigos cercanos por %$heroes.hero_hunter.beasts.duration[1]%$ segundos, haciendo %$heroes.hero_hunter.beasts.damage_min[1]%$-%$heroes.hero_hunter.beasts.damage_max[1]%$ de daño físico. Cada murciélago tiene chance de robar %$heroes.hero_hunter.beasts.gold_to_steal[1]%$ de oro del objetivo.",
["HERO_HUNTER_BEASTS_DESCRIPTION_2"] = "Invoca dos murciélagos que atacan a los enemigos cercanos por %$heroes.hero_hunter.beasts.duration[2]%$ segundos, haciendo %$heroes.hero_hunter.beasts.damage_min[2]%$-%$heroes.hero_hunter.beasts.damage_max[2]%$ de daño físico. Cada murciélago tiene chance de robar %$heroes.hero_hunter.beasts.gold_to_steal[2]%$ de oro del objetivo.",
["HERO_HUNTER_BEASTS_DESCRIPTION_3"] = "Invoca dos murciélagos que atacan a los enemigos cercanos por %$heroes.hero_hunter.beasts.duration[3]%$ segundos, haciendo %$heroes.hero_hunter.beasts.damage_min[3]%$-%$heroes.hero_hunter.beasts.damage_max[3]%$ de daño físico. Cada murciélago tiene chance de robar %$heroes.hero_hunter.beasts.gold_to_steal[3]%$ de oro del objetivo.",
["HERO_HUNTER_BEASTS_TITLE"] = "BESTIAS DEL CREPÚSCULO",
["HERO_HUNTER_CLASS"] = "Cazadora Argenta",
["HERO_HUNTER_DESC"] = "Nacida de la unión entre una vampiresa y un reconocido cazador, Anya sigue los pasos de su padre luchando contra criaturas de la oscuridad. Persiguiendo a los cultistas, la joven cazadora llegó a las tierras del sur y se unió a la Alianza.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_1"] = "El séptimo ataque melee hace %$heroes.hero_hunter.heal_strike.damage_min[1]%$-%$heroes.hero_hunter.heal_strike.damage_max[1]%$ daño vedadero y cura a Anya por un %$heroes.hero_hunter.heal_strike.heal_factor[1]%$% de la vida máxima de su objetivo.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_2"] = "El séptimo ataque melee hace %$heroes.hero_hunter.heal_strike.damage_min[2]%$-%$heroes.hero_hunter.heal_strike.damage_max[2]%$ daño vedadero y cura a Anya por un %$heroes.hero_hunter.heal_strike.heal_factor[2]%$% de la vida máxima de su objetivo.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_3"] = "El séptimo ataque melee hace %$heroes.hero_hunter.heal_strike.damage_min[3]%$-%$heroes.hero_hunter.heal_strike.damage_max[3]%$ daño vedadero y cura a Anya por un %$heroes.hero_hunter.heal_strike.heal_factor[3]%$% de la vida máxima de su objetivo.",
["HERO_HUNTER_HEAL_STRIKE_TITLE"] = "GARRA VAMPÍRICA",
["HERO_HUNTER_NAME"] = "Anya",
["HERO_HUNTER_RICOCHET_DESCRIPTION_1"] = "Anya toma forma de niebla y rebota entre %$heroes.hero_hunter.ricochet.s_bounces[1]%$  enemigos, haciendo %$heroes.hero_hunter.ricochet.damage_min[1]%$-%$heroes.hero_hunter.ricochet.damage_max[1]%$ de daño a cada uno.",
["HERO_HUNTER_RICOCHET_DESCRIPTION_2"] = "Anya toma forma de niebla y rebota entre %$heroes.hero_hunter.ricochet.s_bounces[2]%$  enemigos, haciendo %$heroes.hero_hunter.ricochet.damage_min[2]%$-%$heroes.hero_hunter.ricochet.damage_max[2]%$ de daño a cada uno.",
["HERO_HUNTER_RICOCHET_DESCRIPTION_3"] = "Anya toma forma de niebla y rebota entre %$heroes.hero_hunter.ricochet.s_bounces[3]%$  enemigos, haciendo %$heroes.hero_hunter.ricochet.damage_min[3]%$-%$heroes.hero_hunter.ricochet.damage_max[3]%$ de daño a cada uno.",
["HERO_HUNTER_RICOCHET_TITLE"] = "PASO BRUMOSO",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_1"] = "Dispara a los enemigos que la rodean, haciendo %$heroes.hero_hunter.shoot_around.s_damage_min[1]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[1]%$ de daño verdadero a cada uno.",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_2"] = "Dispara a los enemigos que la rodean, haciendo %$heroes.hero_hunter.shoot_around.s_damage_min[2]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[2]%$ de daño verdadero a cada uno.",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_3"] = "Dispara a los enemigos que la rodean, haciendo %$heroes.hero_hunter.shoot_around.s_damage_min[3]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[3]%$ de daño verdadero a cada uno.",
["HERO_HUNTER_SHOOT_AROUND_TITLE"] = "TORMENTA ARGENTA",
["HERO_HUNTER_SPIRIT_DESCRIPTION_1"] = "Invoca una proyección de Dante que ataca a los enemigos haciendo %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[2]%$ de daño verdadero por segundo por %$heroes.hero_hunter.ultimate.duration%$ segundos. Revive a Anya si su cuerpo está cerca.",
["HERO_HUNTER_SPIRIT_DESCRIPTION_2"] = "Invoca una proyección de Dante que ataca a los enemigos haciendo %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[3]%$ de daño verdadero por segundo por %$heroes.hero_hunter.ultimate.duration%$ segundos. Revive a Anya si su cuerpo está cerca.",
["HERO_HUNTER_SPIRIT_DESCRIPTION_3"] = "Invoca una proyección de Dante que ataca a los enemigos haciendo %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[4]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[4]%$ de daño verdadero por segundo por %$heroes.hero_hunter.ultimate.duration%$ segundos. Revive a Anya si su cuerpo está cerca.",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_DESCRIPTION"] = "Invoca una proyección de Dante que enlentece y ataca a los enemigos.",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_NAME"] = "Ayuda del Cazador",
["HERO_HUNTER_SPIRIT_TITLE"] = "AYUDA DEL CAZADOR",
["HERO_HUNTER_ULTIMATE_ENTITY_NAME"] = "Proyección de Dante",
["HERO_LAVA_CLASS"] = "Furia Fundida",
["HERO_LAVA_DESC"] = "Un ser ardiente y destructivo despertado de su sueño profundo por la actividad de Grymbeard. Como el diálogo no es su fuerte, Kratoa se abrirá paso a golpes entre las líneas enemigas hasta calmarse y poder retomar su sueño.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_1"] = "Lanza una bola de lava que hace %$heroes.hero_lava.double_trouble.s_damage[1]%$ de daño explosivo a enemigos e invoca a un magmito de %$heroes.hero_lava.double_trouble.soldier.hp_max[1]%$ de vida que lucha por %$heroes.hero_lava.double_trouble.soldier.duration%$ segundos.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_2"] = "Lanza una bola de lava que hace %$heroes.hero_lava.double_trouble.s_damage[2]%$ de daño explosivo a enemigos e invoca a un magmito de %$heroes.hero_lava.double_trouble.soldier.hp_max[2]%$ de vida que lucha por %$heroes.hero_lava.double_trouble.soldier.duration%$ segundos.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_3"] = "Lanza una bola de lava que hace %$heroes.hero_lava.double_trouble.s_damage[3]%$ de daño explosivo a enemigos e invoca a un magmito de %$heroes.hero_lava.double_trouble.soldier.hp_max[3]%$ de vida que lucha por %$heroes.hero_lava.double_trouble.soldier.duration%$ segundos.",
["HERO_LAVA_DOUBLE_TROUBLE_SOLDIER_NAME"] = "Magmito",
["HERO_LAVA_DOUBLE_TROUBLE_TITLE"] = "DOBLE MOLESTIA",
["HERO_LAVA_HOTHEADED_DESCRIPTION_1"] = "Cuando Kratoa revive, le da una mejora de %$heroes.hero_lava.hotheaded.s_damage_factors[1]%$% de daño a las torres cercanas por %$heroes.hero_lava.hotheaded.durations[1]%$ segundos.",
["HERO_LAVA_HOTHEADED_DESCRIPTION_2"] = "Cuando Kratoa revive, le da una mejora de %$heroes.hero_lava.hotheaded.s_damage_factors[2]%$% de daño a las torres cercanas por %$heroes.hero_lava.hotheaded.durations[2]%$ segundos.",
["HERO_LAVA_HOTHEADED_DESCRIPTION_3"] = "Cuando Kratoa revive, le da una mejora de %$heroes.hero_lava.hotheaded.s_damage_factors[3]%$% de daño a las torres cercanas por %$heroes.hero_lava.hotheaded.durations[3]%$ segundos.",
["HERO_LAVA_HOTHEADED_TITLE"] = "FURIBUNDO",
["HERO_LAVA_NAME"] = "Kratoa",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_1"] = "Golpea a un enemigo, haciendo %$heroes.hero_lava.temper_tantrum.s_damage_min[1]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[1]%$ de daño físico y aturdiendo al objetivo por %$heroes.hero_lava.temper_tantrum.duration[1]%$ segundos.",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_2"] = "Golpea a un enemigo, haciendo %$heroes.hero_lava.temper_tantrum.s_damage_min[2]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[2]%$ de daño físico y aturdiendo al objetivo por %$heroes.hero_lava.temper_tantrum.duration[2]%$ segundos.",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_3"] = "Golpea a un enemigo, haciendo %$heroes.hero_lava.temper_tantrum.s_damage_min[3]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[3]%$ de daño físico y aturdiendo al objetivo por %$heroes.hero_lava.temper_tantrum.duration[3]%$ segundos.",
["HERO_LAVA_TEMPER_TANTRUM_TITLE"] = "RABIETA",
["HERO_LAVA_ULTIMATE_DESCRIPTION_1"] = "Lanza %$heroes.hero_lava.ultimate.fireball_count[2]%$ bolas de lava al camino. Cada una hace %$heroes.hero_lava.ultimate.bullet.s_damage[2]%$ de daño verdadero a cada enemigo y los quema por %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ segundos.",
["HERO_LAVA_ULTIMATE_DESCRIPTION_2"] = "Lanza %$heroes.hero_lava.ultimate.fireball_count[3]%$ bolas de lava al camino. Cada una hace %$heroes.hero_lava.ultimate.bullet.s_damage[3]%$ de daño verdadero a cada enemigo y los quema por %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ segundos.",
["HERO_LAVA_ULTIMATE_DESCRIPTION_3"] = "Lanza %$heroes.hero_lava.ultimate.fireball_count[4]%$ bolas de lava al camino. Cada una hace %$heroes.hero_lava.ultimate.bullet.s_damage[4]%$ de daño verdadero a cada enemigo y los quema por %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ segundos.",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "Lanza chorros de lava al camino, quemando el suelo.",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_NAME"] = "Estallido Iracundo",
["HERO_LAVA_ULTIMATE_TITLE"] = "ESTALLIDO IRACUNDO",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_1"] = "Chorrea lava sobre los enemigos, haciendo %$heroes.hero_lava.wild_eruption.s_damage[1]%$ de daño verdadero y quemándolos por %$heroes.hero_lava.wild_eruption.duration[1]%$ segundos.",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_2"] = "Chorrea lava sobre los enemigos, haciendo %$heroes.hero_lava.wild_eruption.s_damage[2]%$ de daño verdadero y quemándolos por %$heroes.hero_lava.wild_eruption.duration[2]%$ segundos.",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_3"] = "Chorrea lava sobre los enemigos, haciendo %$heroes.hero_lava.wild_eruption.s_damage[3]%$ de daño verdadero y quemándolos por %$heroes.hero_lava.wild_eruption.duration[3]%$ segundos.",
["HERO_LAVA_WILD_ERUPTION_TITLE"] = "ERUPCIÓN SALVAJE",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_1"] = "Invoca %$heroes.hero_lumenir.ultimate.soldier_count[1]%$ guerreros luminosos que aturden momentáneamiente a los enemigos cercanos, haciendo %$heroes.hero_lumenir.ultimate.damage_min[1]%$-%$heroes.hero_lumenir.ultimate.damage_max[1]%$ de daño verdadero.",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_2"] = "Invoca %$heroes.hero_lumenir.ultimate.soldier_count[2]%$ guerreros luminosos que aturden momentáneamiente a los enemigos cercanos, haciendo %$heroes.hero_lumenir.ultimate.damage_min[2]%$-%$heroes.hero_lumenir.ultimate.damage_max[2]%$ de daño verdadero.",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_3"] = "Invoca %$heroes.hero_lumenir.ultimate.soldier_count[3]%$ guerreros luminosos que aturden momentáneamiente a los enemigos cercanos, haciendo %$heroes.hero_lumenir.ultimate.damage_min[3]%$-%$heroes.hero_lumenir.ultimate.damage_max[3]%$ de daño verdadero.",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "Invoca guerreros divinos que luchan contra los enemigos.",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_NAME"] = "LLAMADO TRIUNFAL",
["HERO_LUMENIR_ARROW_STORM_TITLE"] = "LLAMADO TRIUNFAL",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_1"] = "Lanza una espada de luz divina hacia el enemigo cercano más fuerte, aturdiéndolo por %$heroes.hero_lumenir.celestial_judgement.stun_duration[1]%$ segundos y haciendo %$heroes.hero_lumenir.celestial_judgement.damage[3]%$ de daño verdadero.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_2"] = "Lanza una espada de luz divina hacia el enemigo cercano más fuerte, aturdiéndolo por %$heroes.hero_lumenir.celestial_judgement.stun_duration[2]%$ segundos y haciendo %$heroes.hero_lumenir.celestial_judgement.damage[3]%$ de daño verdadero.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_3"] = "Lanza una espada de luz divina hacia el enemigo cercano más fuerte, aturdiéndolo por %$heroes.hero_lumenir.celestial_judgement.stun_duration[3]%$ segundos y haciendo %$heroes.hero_lumenir.celestial_judgement.damage[3]%$ de daño verdadero.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_TITLE"] = "JUICIO CELESTIAL",
["HERO_LUMENIR_CLASS"] = "Portadora de Luz",
["HERO_LUMENIR_DESC"] = "Volando entre mundos, Lumenir es el avatar de la justicia y la determinación. Es la mítica Portadora de Luz, reverenciada por los paladines de Linirea, a quienes bendice con grandes poderes en su lucha contra el mal.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_1"] = "Exhala %$heroes.hero_lumenir.fire_balls.flames_count[1]%$ orbes de luz divina que viajan por el camino dañando a los enemigos. Cada orbe hace %$heroes.hero_lumenir.fire_balls.flame_damage_min[1]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[1]%$ de daño verdadero a los enemigos que atraviesa.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_2"] = "Exhala %$heroes.hero_lumenir.fire_balls.flames_count[2]%$ orbes de luz divina que viajan por el camino dañando a los enemigos. Cada orbe hace %$heroes.hero_lumenir.fire_balls.flame_damage_min[2]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[2]%$ de daño verdadero a los enemigos que atraviesa.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_3"] = "Exhala %$heroes.hero_lumenir.fire_balls.flames_count[3]%$ orbes de luz divina que viajan por el camino dañando a los enemigos. Cada orbe hace %$heroes.hero_lumenir.fire_balls.flame_damage_min[3]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[3]%$ de daño verdadero a los enemigos que atraviesa.",
["HERO_LUMENIR_FIRE_BALLS_TITLE"] = "OLEADA RADIANTE",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_1"] = "Invoca a un pequeño dragón de luz que sigue al otro héroe por %$heroes.hero_lumenir.mini_dragon.dragon.duration[1]%$ segundos. El dragón hace %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[1]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[1]%$ de daño físico.",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_2"] = "Invoca a un pequeño dragón de luz que sigue al otro héroe por %$heroes.hero_lumenir.mini_dragon.dragon.duration[2]%$ segundos. El dragón hace %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[1]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[2]%$ de daño físico.",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_3"] = "Invoca a un pequeño dragón de luz que sigue al otro héroe por %$heroes.hero_lumenir.mini_dragon.dragon.duration[3]%$ segundos. El dragón hace %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[1]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[3]%$ de daño físico.",
["HERO_LUMENIR_MINI_DRAGON_TITLE"] = "COMPAÑERO LUMINOSO",
["HERO_LUMENIR_NAME"] = "Lumenir",
["HERO_LUMENIR_SHIELD_DESCRIPTION_1"] = "Otorga a sus aliados un escudo de %$heroes.hero_lumenir.shield.armor[1]%$% de armadura que refleja %$heroes.hero_lumenir.shield.spiked_armor[1]%$% del daño recibido hacia los enemigos atacantes.",
["HERO_LUMENIR_SHIELD_DESCRIPTION_2"] = "Otorga a sus aliados un escudo de %$heroes.hero_lumenir.shield.armor[2]%$% de armadura que refleja %$heroes.hero_lumenir.shield.spiked_armor[2]%$% del daño recibido hacia los enemigos atacantes.",
["HERO_LUMENIR_SHIELD_DESCRIPTION_3"] = "Otorga a sus aliados un escudo de %$heroes.hero_lumenir.shield.armor[3]%$% de armadura que refleja %$heroes.hero_lumenir.shield.spiked_armor[3]%$% del daño recibido hacia los enemigos atacantes.",
["HERO_LUMENIR_SHIELD_TITLE"] = "BENDICIÓN DE RETRIBUCIÓN",
["HERO_MECHA_CLASS"] = "Amenaza Móvil",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_1"] = "Invoca un zeppelin goblin que bombardea a los enemigos en el área objetivo, haciendo %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[2]%$ de daño verdadero.",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_2"] = "Invoca un zeppelin goblin que bombardea a los enemigos en el área objetivo, haciendo %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[3]%$ de daño verdadero.",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_3"] = "Invoca un zeppelin goblin que bombardea a los enemigos en el área objetivo, haciendo %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[4]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[4]%$ de daño verdadero.",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_DESCRIPTION"] = "Invoca un zeppelin que bombardea enemigos en el área.",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_NAME"] = "Muerte desde lo Alto.",
["HERO_MECHA_DEATH_FROM_ABOVE_TITLE"] = "MUERTE DESDE LO ALTO",
["HERO_MECHA_DESC"] = "Nacido de la mente de dos goblins soldadores y construido sobre las bases de tecnología enana robada, Onagro es la máquina de guerra definitiva de los pielesverde, sembrando el miedo en los enemigos del Ejército Oscuro.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_1"] = "Invoca %$heroes.hero_mecha.goblidrones.units%$ drones que atacan a los enemigos por %$heroes.hero_mecha.goblidrones.drone.duration[1]%$ segundos, haciendo %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[1]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[1]%$ de daño físico.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_2"] = "Invoca %$heroes.hero_mecha.goblidrones.units%$ drones que atacan a los enemigos por %$heroes.hero_mecha.goblidrones.drone.duration[2]%$ segundos, haciendo %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[2]%$ de daño físico.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_3"] = "Invoca %$heroes.hero_mecha.goblidrones.units%$ drones que atacan a los enemigos por %$heroes.hero_mecha.goblidrones.drone.duration[3]%$ segundos, haciendo %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[3]%$ de daño físico.",
["HERO_MECHA_GOBLIDRONES_TITLE"] = "GOBLIDRONES",
["HERO_MECHA_MINE_DROP_DESCRIPTION_1"] = "Mientras está quieto, deja periodicamente hasta %$heroes.hero_mecha.mine_drop.max_mines[1]%$ minas explosivas en el camino. Las minas explotan haciendo %$heroes.hero_mecha.mine_drop.damage_min[1]%$-%$heroes.hero_mecha.mine_drop.damage_max[1]%$ de daño explosivo cada una.",
["HERO_MECHA_MINE_DROP_DESCRIPTION_2"] = "Mientras está quieto, deja periodicamente hasta %$heroes.hero_mecha.mine_drop.max_mines[2]%$ minas explosivas en el camino. Las minas explotan haciendo %$heroes.hero_mecha.mine_drop.damage_min[2]%$-%$heroes.hero_mecha.mine_drop.damage_max[2]%$ de daño explosivo cada una.",
["HERO_MECHA_MINE_DROP_DESCRIPTION_3"] = "Mientras está quieto, deja periodicamente hasta %$heroes.hero_mecha.mine_drop.max_mines[3]%$ minas explosivas en el camino. Las minas explotan haciendo %$heroes.hero_mecha.mine_drop.damage_min[3]%$-%$heroes.hero_mecha.mine_drop.damage_max[3]%$ de daño explosivo cada una.",
["HERO_MECHA_MINE_DROP_TITLE"] = "MINAS EXPLOSIVAS",
["HERO_MECHA_NAME"] = "Onagro",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_1"] = "El mecha pisotea el camino, aturdiendo a los enemigos cercanos y haciendo %$heroes.hero_mecha.power_slam.s_damage[1]%$ de daño físico.",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_2"] = "El mecha pisotea el camino, aturdiendo a los enemigos cercanos y haciendo %$heroes.hero_mecha.power_slam.s_damage[2]%$ de daño físico.",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_3"] = "El mecha pisotea el camino, aturdiendo a los enemigos cercanos y haciendo %$heroes.hero_mecha.power_slam.s_damage[3]%$ de daño físico.",
["HERO_MECHA_POWER_SLAM_TITLE"] = "PISOTÓN PODEROSO",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_1"] = "Lanza una bomba de brea al camino, reduciendo la velocidad de los enemigos en un %$heroes.hero_mecha.tar_bomb.slow_factor%$% por %$heroes.hero_mecha.tar_bomb.duration[1]%$ segundos.",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_2"] = "Lanza una bomba de brea al camino, reduciendo la velocidad de los enemigos en un %$heroes.hero_mecha.tar_bomb.slow_factor%$% por %$heroes.hero_mecha.tar_bomb.duration[2]%$ segundos.",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_3"] = "Lanza una bomba de brea al camino, reduciendo la velocidad de los enemigos en un %$heroes.hero_mecha.tar_bomb.slow_factor%$% por %$heroes.hero_mecha.tar_bomb.duration[3]%$ segundos.",
["HERO_MECHA_TAR_BOMB_TITLE"] = "BOMBA DE BREA",
["HERO_MUYRN_CLASS"] = "Guardián del Bosque",
["HERO_MUYRN_DESC"] = "A pesar de su aspecto aniñado, el bromista Nyru ha protegido el bosque por cientos de años usando su conexión con las fuerzas de la naturaleza y decide unirse a la Alianza para poner un fin a las oleadas de invasores que amenazan su hogar.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_1"] = "Encanta a todos los enemigos en un área, reduciendo su daño de ataque en un %$heroes.hero_muyrn.faery_dust.s_damage_factor[1]%$% por %$heroes.hero_muyrn.faery_dust.duration[1]%$ segundos.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_2"] = "Encanta a todos los enemigos en un área, reduciendo su daño de ataque en un %$heroes.hero_muyrn.faery_dust.s_damage_factor[2]%$% por %$heroes.hero_muyrn.faery_dust.duration[2]%$ segundos.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_3"] = "Encanta a todos los enemigos en un área, reduciendo su daño de ataque en un %$heroes.hero_muyrn.faery_dust.s_damage_factor[3]%$% por %$heroes.hero_muyrn.faery_dust.duration[3]%$ segundos.",
["HERO_MUYRN_FAERY_DUST_TITLE"] = "ENCANTAMIENTO DEBILITADOR",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_1"] = "En combate, Nyru crea un escudo de hojas a su alrededor. El escudo hace %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[1]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[1]%$ de daño mágico y cura a Nyru por %$heroes.hero_muyrn.leaf_whirlwind.duration[1]%$ segundos.",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_2"] = "En combate, Nyru crea un escudo de hojas a su alrededor. El escudo hace %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[2]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[2]%$ de daño mágico y cura a Nyru por %$heroes.hero_muyrn.leaf_whirlwind.duration[2]%$ segundos.",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_3"] = "En combate, Nyru crea un escudo de hojas a su alrededor. El escudo hace %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[3]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[3]%$ de daño mágico y cura a Nyru por %$heroes.hero_muyrn.leaf_whirlwind.duration[3]%$ segundos.",
["HERO_MUYRN_LEAF_WHIRLWIND_TITLE"] = "TORNADO DE HOJARASCA",
["HERO_MUYRN_NAME"] = "Nyru",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_1"] = "Llena un área con raíces salvajes por %$heroes.hero_muyrn.ultimate.duration[2]%$ segundos, enlenteciendo a los enemigos y haciendo %$heroes.hero_muyrn.ultimate.s_damage_min[2]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[2]%$ de daño verdadero por segundo.",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_2"] = "Llena un área con raíces salvajes por %$heroes.hero_muyrn.ultimate.duration[3]%$ segundos, enlenteciendo a los enemigos y haciendo %$heroes.hero_muyrn.ultimate.s_damage_min[3]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[3]%$ de daño verdadero por segundo.",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_3"] = "Llena un área con raíces salvajes por %$heroes.hero_muyrn.ultimate.duration[4]%$ segundos, enlenteciendo a los enemigos y haciendo %$heroes.hero_muyrn.ultimate.s_damage_min[4]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[4]%$ de daño verdadero por segundo.",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_DESCRIPTION"] = "Invoca raíces que dañan y enlentecen a los enemigos.",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_NAME"] = "Raíces Defensoras",
["HERO_MUYRN_ROOT_DEFENDER_TITLE"] = "RAÍCES DEFENSORAS",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_1"] = "Invoca %$heroes.hero_muyrn.sentinel_wisps.max_summons[1]%$ fuego fatuo que sigue a Nyru por %$heroes.hero_muyrn.sentinel_wisps.wisp.duration[1]%$ segundos, haciendo %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[1]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[1]%$ de daño mágico.",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_2"] = "Invoca %$heroes.hero_muyrn.sentinel_wisps.max_summons[3]%$ fuegos fatuos que siguen a Nyru por %$heroes.hero_muyrn.sentinel_wisps.wisp.duration[3]%$ segundos, haciendo %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[3]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[3]%$ de daño mágico.",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_3"] = "Invoca 3 fuegos fatuos que siguen a Nyru por 6 segundos, haciendo 8-16 de daño.",
["HERO_MUYRN_SENTINEL_WISPS_TITLE"] = "FUEGO FATUO",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_1"] = "Dispara un proyectil de energía verde hacia un enemigo, haciendo %$heroes.hero_muyrn.verdant_blast.s_damage[1]%$ de daño.",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_2"] = "Dispara un proyectil de energía verde hacia un enemigo, haciendo %$heroes.hero_muyrn.verdant_blast.s_damage[2]%$ de daño.",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_3"] = "Dispara un proyectil de energía verde hacia un enemigo, haciendo %$heroes.hero_muyrn.verdant_blast.s_damage[3]%$ de daño.",
["HERO_MUYRN_VERDANT_BLAST_TITLE"] = "ESTALLIDO VERDE",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_1"] = "Golpea brutalmente a un enemigo con su espada, haciendo %$heroes.hero_raelyn.brutal_slash.s_damage[1]%$ de daño verdadero.",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_2"] = "Golpea brutalmente a un enemigo con su espada, haciendo %$heroes.hero_raelyn.brutal_slash.s_damage[2]%$ de daño verdadero.",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_3"] = "Golpea brutalmente a un enemigo con su espada, haciendo %$heroes.hero_raelyn.brutal_slash.s_damage[3]%$ de daño verdadero.",
["HERO_RAELYN_BRUTAL_SLASH_TITLE"] = "CORTE BRUTAL",
["HERO_RAELYN_CLASS"] = "Teniente Oscura",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_1"] = "Invoca a un Caballero Oscuro que tiene %$heroes.hero_raelyn.ultimate.entity.hp_max[2]%$ de vida y hace %$heroes.hero_raelyn.ultimate.entity.damage_min[2]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[2]%$ de daño verdadero.",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_2"] = "El Caballero Oscuro tiene %$heroes.hero_raelyn.ultimate.entity.hp_max[3]%$ de vida y hace %$heroes.hero_raelyn.ultimate.entity.damage_min[3]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[3]%$ de daño verdadero.",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_3"] = "El Caballero Oscuro tiene %$heroes.hero_raelyn.ultimate.entity.hp_max[4]%$ de vida y hace %$heroes.hero_raelyn.ultimate.entity.damage_min[4]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[4]%$ de daño verdadero.",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_DESCRIPTION"] = "Invoca un Caballero Oscuro al campo de batalla.",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_NAME"] = "Órdenes de Comando",
["HERO_RAELYN_COMMAND_ORDERS_TITLE"] = "ÓRDENES DE COMANDO",
["HERO_RAELYN_DESC"] = "La imponente Raelyn vive para liderar a los Caballeros Oscuros al frente de la batalla. Su brutalidad implacable le ganó el reconocimiento de Vez'nan y el miedo de los Linireanos. Siempre está lista para luchar y fue la primera en unirse voluntariamente a las filas del Hechicero Oscuro.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_1"] = "Aturde a los enemigos cercanos por %$heroes.hero_raelyn.inspire_fear.stun_duration[1]%$ segundos y reduce su daño de ataque en un %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[1]%$% por %$heroes.hero_raelyn.inspire_fear.damage_duration[1]%$ segundos.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_2"] = "Aturde a los enemigos cercanos por %$heroes.hero_raelyn.inspire_fear.stun_duration[2]%$ segundos y reduce su daño de ataque en un %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[2]%$% por %$heroes.hero_raelyn.inspire_fear.damage_duration[2]%$ segundos.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_3"] = "Aturde a los enemigos cercanos por %$heroes.hero_raelyn.inspire_fear.stun_duration[3]%$ segundos y reduce su daño de ataque en un %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[3]%$% por %$heroes.hero_raelyn.inspire_fear.damage_duration[3]%$ segundos.",
["HERO_RAELYN_INSPIRE_FEAR_TITLE"] = "INSPIRAR MIEDO",
["HERO_RAELYN_NAME"] = "Raelyn",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_1"] = "Por %$heroes.hero_raelyn.onslaught.duration[1]%$ segundos, Raelyn ataca más rápido y hace daño extra igual al %$heroes.hero_raelyn.onslaught.damage_factor[1]%$% de su daño de ataque en un área pequeña alrededor del objetivo principal.",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_2"] = "Por %$heroes.hero_raelyn.onslaught.duration[2]%$ segundos, Raelyn ataca más rápido y hace daño extra igual al %$heroes.hero_raelyn.onslaught.damage_factor[2]%$% de su daño de ataque en un área pequeña alrededor del objetivo principal.",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_3"] = "Por %$heroes.hero_raelyn.onslaught.duration[3]%$ segundos, Raelyn ataca más rápido y hace daño extra igual al %$heroes.hero_raelyn.onslaught.damage_factor[3]%$% de su daño de ataque en un área pequeña alrededor del objetivo principal.",
["HERO_RAELYN_ONSLAUGHT_TITLE"] = "ARREMETIDA",
["HERO_RAELYN_ULTIMATE_ENTITY_NAME"] = "Caballero Oscuro",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_1"] = "Cuando entra en combate, Raelyn genera un escudo de vitalidad en base a la cantidad de enemigos cerca de ella (%$heroes.hero_raelyn.unbreakable.shield_per_enemy[1]%$% del total de su vida por cada uno de hasta %$heroes.hero_raelyn.unbreakable.max_targets%$ enemigos).",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_2"] = "Cuando entra en combate, Raelyn genera un escudo de vitalidad en base a la cantidad de enemigos cerca de ella (%$heroes.hero_raelyn.unbreakable.shield_per_enemy[2]%$% del total de su vida por cada uno de hasta %$heroes.hero_raelyn.unbreakable.max_targets%$ enemigos).",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_3"] = "Cuando entra en combate, Raelyn genera un escudo de vitalidad en base a la cantidad de enemigos cerca de ella (%$heroes.hero_raelyn.unbreakable.shield_per_enemy[3]%$% del total de su vida por cada uno de hasta %$heroes.hero_raelyn.unbreakable.max_targets%$ enemigos).",
["HERO_RAELYN_UNBREAKABLE_TITLE"] = "INQUEBRANTABLE",
["HERO_ROBOT_CLASS"] = "Golem de Asedio",
["HERO_ROBOT_DESC"] = "Los maestros de la forja del Ejército Oscuro se superaron a si mismos creando a un autómata combativo al que llamaron Warhead. Sin emociones y alimentado por llameantes motores, Warhead se lanza a la batalla sin que le importen sus enemigos ni sus aliados.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_1"] = "Genera una explosión que hace %$heroes.hero_robot.explode.damage_min[1]%$-%$heroes.hero_robot.explode.damage_max[1]%$ de daño y quema a los enemigos por %$heroes.hero_robot.explode.burning_duration%$ segundos. El quemado hace %$heroes.hero_robot.explode.s_burning_damage[1]%$ de daño por segundo.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_2"] = "Genera una explosión que hace %$heroes.hero_robot.explode.damage_min[2]%$-%$heroes.hero_robot.explode.damage_max[2]%$ de daño y quema a los enemigos por %$heroes.hero_robot.explode.burning_duration%$ segundos. El quemado hace %$heroes.hero_robot.explode.s_burning_damage[2]%$ de daño por segundo.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_3"] = "Genera una explosión que hace %$heroes.hero_robot.explode.damage_min[3]%$-%$heroes.hero_robot.explode.damage_max[3]%$ de daño y quema a los enemigos por %$heroes.hero_robot.explode.burning_duration%$ segundos. El quemado hace %$heroes.hero_robot.explode.s_burning_damage[3]%$ de daño por segundo.",
["HERO_ROBOT_EXPLODE_TITLE"] = "INMOLACIÓN",
["HERO_ROBOT_FIRE_DESCRIPTION_1"] = "Dispara un cañón de brasas ardientes, haciendo %$heroes.hero_robot.fire.damage_min[1]%$-%$heroes.hero_robot.fire.damage_max[1]%$ de daño y enlenteciendo a los enemigos por %$heroes.hero_robot.fire.s_slow_duration[1]%$ segundos.",
["HERO_ROBOT_FIRE_DESCRIPTION_2"] = "Dispara un cañón de brasas ardientes, haciendo %$heroes.hero_robot.fire.damage_min[1]%$-%$heroes.hero_robot.fire.damage_max[1]%$ de daño y enlenteciendo a los enemigos por %$heroes.hero_robot.fire.s_slow_duration[1]%$ segundos.",
["HERO_ROBOT_FIRE_DESCRIPTION_3"] = "Dispara un cañón de brasas ardientes, haciendo %$heroes.hero_robot.fire.damage_min[1]%$-%$heroes.hero_robot.fire.damage_max[1]%$ de daño y enlenteciendo a los enemigos por %$heroes.hero_robot.fire.s_slow_duration[1]%$ segundos.",
["HERO_ROBOT_FIRE_TITLE"] = "PANTALLA DE HUMO",
["HERO_ROBOT_JUMP_DESCRIPTION_1"] = "Salta sobre un enemigo, aturdiéndolo por %$heroes.hero_robot.jump.stun_duration[1]%$ segundos y haciendo %$heroes.hero_robot.jump.s_damage[1]%$ de daño en área.",
["HERO_ROBOT_JUMP_DESCRIPTION_2"] = "Salta sobre un enemigo, aturdiéndolo por %$heroes.hero_robot.jump.stun_duration[2]%$ segundos y haciendo %$heroes.hero_robot.jump.s_damage[2]%$ de daño en área.",
["HERO_ROBOT_JUMP_DESCRIPTION_3"] = "Salta sobre un enemigo, aturdiéndolo por %$heroes.hero_robot.jump.stun_duration[3]%$ segundos y haciendo %$heroes.hero_robot.jump.s_damage[3]%$ de daño en área.",
["HERO_ROBOT_JUMP_TITLE"] = "IMPACTO PROFUNDO",
["HERO_ROBOT_NAME"] = "Warhead",
["HERO_ROBOT_TRAIN_DESCRIPTION_1"] = "Invoca un vagón de guerra que viaja por el camino haciendo %$heroes.hero_robot.ultimate.s_damage[2]%$ de daño a los enemigos y quemándolos por %$heroes.hero_robot.ultimate.burning_duration%$ segundos. El quemado hace %$heroes.hero_robot.ultimate.s_burning_damage%$ por segundo.",
["HERO_ROBOT_TRAIN_DESCRIPTION_2"] = "Invoca un vagón de guerra que viaja por el camino haciendo %$heroes.hero_robot.ultimate.s_damage[3]%$ de daño a los enemigos y quemándolos por %$heroes.hero_robot.ultimate.burning_duration%$ segundos. El quemado hace %$heroes.hero_robot.ultimate.s_burning_damage%$ por segundo.",
["HERO_ROBOT_TRAIN_DESCRIPTION_3"] = "Invoca un vagón de guerra que viaja por el camino haciendo %$heroes.hero_robot.ultimate.s_damage[4]%$ de daño a los enemigos y quemándolos por %$heroes.hero_robot.ultimate.burning_duration%$ segundos. El quemado hace %$heroes.hero_robot.ultimate.s_burning_damage%$ por segundo.",
["HERO_ROBOT_TRAIN_MENUBOTTOM_DESCRIPTION"] = "Invoca a un vagón de guerra que atropella enemigos.",
["HERO_ROBOT_TRAIN_MENUBOTTOM_NAME"] = "Vagón de guerra",
["HERO_ROBOT_TRAIN_TITLE"] = "VAGÓN DE GUERRA",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_1"] = "Golpea a un enemigo con menos de %$heroes.hero_robot.uppercut.s_life_threshold[1]%$% de vida, eliminándolo al instante.",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_2"] = "Golpea a un enemigo con menos de %$heroes.hero_robot.uppercut.s_life_threshold[2]%$% de vida, eliminándolo al instante.",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_3"] = "Golpea a un enemigo con menos de %$heroes.hero_robot.uppercut.s_life_threshold[3]%$% de vida, eliminándolo al instante.",
["HERO_ROBOT_UPPERCUT_TITLE"] = "GANCHO FÉRREO",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "Este héroe está incluido en la campaña de Amenaza Colosal.",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "Este héroe está incluido en la campaña «El Viaje de Wukong».",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "Campaña de Amenaza Colosal",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "Campaña El Viaje de Wukong",
["HERO_ROOM_EQUIPPED_HEROES"] = "Héroes Equipados",
["HERO_ROOM_GET_DLC"] = "CONSÍGUELO",
["HERO_ROOM_LABEL_ROSTER_THUMB_NEW"] = "¡Nuevo!",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_1"] = "Invoca un reflejo mágico de Therien que ataca a los enemigos, haciendo %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[1]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[1]%$ de daño mágico.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_2"] = "Invoca un reflejo mágico de Therien que ataca a los enemigos, haciendo %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[2]%$ de daño mágico.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_3"] = "Invoca un reflejo mágico de Therien que ataca a los enemigos, haciendo %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[3]%$ de daño mágico.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_ENTITY_NAME"] = "Reflejo Astral",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_TITLE"] = "REFLEJO ASTRAL",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_1"] = "Crea un escudo protector sobre una unidad aliada previniendo %$heroes.hero_space_elf.black_aegis.shield_base[1]%$ de daño. El escudo explota cuando expira o es destruido, haciendo %$heroes.hero_space_elf.black_aegis.explosion_damage[1]%$ de daño en un área.",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_2"] = "Crea un escudo protector sobre una unidad aliada previniendo %$heroes.hero_space_elf.black_aegis.shield_base[2]%$ de daño. El escudo explota cuando expira o es destruido, haciendo %$heroes.hero_space_elf.black_aegis.explosion_damage[2]%$ de daño en un área.",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_3"] = "Crea un escudo protector sobre una unidad aliada previniendo %$heroes.hero_space_elf.black_aegis.shield_base[3]%$ de daño. El escudo explota cuando expira o es destruido, haciendo %$heroes.hero_space_elf.black_aegis.explosion_damage[3]%$ de daño en un área.",
["HERO_SPACE_ELF_BLACK_AEGIS_TITLE"] = "PROTECCIÓN OSCURA",
["HERO_SPACE_ELF_CLASS"] = "Vaciomante",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_1"] = "Atrapa a un grupo de enemigos en el vacío por %$heroes.hero_space_elf.ultimate.duration[2]%$ segundos, haciendo %$heroes.hero_space_elf.ultimate.damage[2]%$ de daño verdadero.",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_2"] = "Atrapa a un grupo de enemigos en el vacío por %$heroes.hero_space_elf.ultimate.duration[3]%$ segundos, haciendo %$heroes.hero_space_elf.ultimate.damage[3]%$ de daño verdadero.",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_3"] = "Atrapa a un grupo de enemigos en el vacío por %$heroes.hero_space_elf.ultimate.duration[4]%$ segundos, haciendo %$heroes.hero_space_elf.ultimate.damage[4]%$ de daño verdadero.",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_DESCRIPTION"] = "Atrapa a los enemigos en un área, dañándolos.",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_NAME"] = "Prisión Cósmica",
["HERO_SPACE_ELF_COSMIC_PRISON_TITLE"] = "PRISIÓN CÓSMICA",
["HERO_SPACE_ELF_DESC"] = "Apartada por sus pares por entrometerse con fuerzas extrañas y de mundos desconocidos, la vaciomante Therien es la mejor opción de la Alianza a la hora de entender al Omnividente y las fuerzas que acechan desde otro plano.",
["HERO_SPACE_ELF_NAME"] = "Therien",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_1"] = "Distorsiona el espacio alrededor de todas las torres por %$heroes.hero_space_elf.spatial_distortion.duration[1]%$ segundos, incrementando su rango en un %$heroes.hero_space_elf.spatial_distortion.s_range_factor[1]%$%.",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_2"] = "Distorsiona el espacio alrededor de todas las torres por %$heroes.hero_space_elf.spatial_distortion.duration[2]%$ segundos, incrementando su rango en un %$heroes.hero_space_elf.spatial_distortion.s_range_factor[2]%$%.",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_3"] = "Distorsiona el espacio alrededor de todas las torres por %$heroes.hero_space_elf.spatial_distortion.duration[3]%$ segundos, incrementando su rango en un %$heroes.hero_space_elf.spatial_distortion.s_range_factor[3]%$%.",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_TITLE"] = "DISTORSIÓN ESPACIAL",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_1"] = "Abre %$heroes.hero_space_elf.void_rift.cracks_amount[1]%$ fisura en el camino por %$heroes.hero_space_elf.void_rift.duration[1]%$ segundos, haciendo %$heroes.hero_space_elf.void_rift.s_damage_min[1]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[1]%$ de daño a los enemigos que caminan por encima.",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_2"] = "Abre %$heroes.hero_space_elf.void_rift.cracks_amount[2]%$ fisuras en el camino por %$heroes.hero_space_elf.void_rift.duration[2]%$ segundos, haciendo %$heroes.hero_space_elf.void_rift.s_damage_min[2]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[2]%$ de daño a los enemigos que caminan por encima.",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_3"] = "Abre %$heroes.hero_space_elf.void_rift.cracks_amount[3]%$ fisuras en el camino por %$heroes.hero_space_elf.void_rift.duration[3]%$ segundos, haciendo %$heroes.hero_space_elf.void_rift.s_damage_min[3]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[3]%$ de daño a los enemigos que caminan por encima.",
["HERO_SPACE_ELF_VOID_RIFT_TITLE"] = "FISURA DEL VACÍO",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_1"] = "Invoca %$heroes.hero_spider.ultimate.spawn_amount[2]%$ arañas que luchan durante %$heroes.hero_spider.ultimate.spider.duration[2]%$ segundos, aturdiendo a los enemigos al golpearlos.",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_2"] = "Invoca %$heroes.hero_spider.ultimate.spawn_amount[3]%$ arañas que luchan durante %$heroes.hero_spider.ultimate.spider.duration[3]%$ segundos, aturdiendo a los enemigos al golpearlos.",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_3"] = "Invoca %$heroes.hero_spider.ultimate.spawn_amount[4]%$ arañas que luchan durante %$heroes.hero_spider.ultimate.spider.duration[4]%$ segundos, aturdiendo a los enemigos al golpearlos.",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_DESCRIPTION"] = "Invoca una manada de arañas aturdidoras.",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_NAME"] = "Llamado del Cazador",
["HERO_SPIDER_ARACNID_SPAWNER_TITLE"] = "Llamado del Cazador",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_1"] = "Cada %$heroes.hero_spider.area_attack.cooldown[1]%$ segundos, Spydyr impone su presencia, aturdiendo a los enemigos cercanos durante %$heroes.hero_spider.area_attack.s_stun_time[1]%$ segundos.",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_2"] = "Cada %$heroes.hero_spider.area_attack.cooldown[2]%$ segundos, Spydyr impone su presencia, aturdiendo a los enemigos cercanos durante %$heroes.hero_spider.area_attack.s_stun_time[2]%$ segundos.",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_3"] = "Cada %$heroes.hero_spider.area_attack.cooldown[3]%$ segundos, Spydyr impone su presencia, aturdiendo a los enemigos cercanos durante %$heroes.hero_spider.area_attack.s_stun_time[3]%$ segundos.",
["HERO_SPIDER_AREA_ATTACK_TITLE"] = "Presencia Abrumadora",
["HERO_SPIDER_DESC"] = "Último miembro vivo de un grupo de Elfos del Crepúsculo encargados de aniquilar el Culto de la Reina Araña. Sumando magia sombría a su destreza inigualable como cazadora, es temida como una de las asesinas más letales de todos los reinos.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_1"] = "Cada %$heroes.hero_spider.instakill_melee.cooldown[1]%$ segundos, Spydyr puede ejecutar a un enemigo aturdido cuya salud esté por debajo de %$heroes.hero_spider.instakill_melee.life_threshold[1]%$.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_2"] = "Cada %$heroes.hero_spider.instakill_melee.cooldown[2]%$ segundos, Spydyr puede ejecutar a un enemigo aturdido cuya salud esté por debajo de %$heroes.hero_spider.instakill_melee.life_threshold[2]%$.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_3"] = "Cada %$heroes.hero_spider.instakill_melee.cooldown[3]%$ segundos, Spydyr puede ejecutar a un enemigo aturdido cuya salud esté por debajo de %$heroes.hero_spider.instakill_melee.life_threshold[3]%$.",
["HERO_SPIDER_INSTAKILL_MELEE_TITLE"] = "Agarre de la Muerte",
["HERO_SPIDER_NAME"] = "Spydyr",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_1"] = "En un abrir y cerrar de ojos, Spydyr se teletransporta al enemigo con más salud, infligiéndole %$heroes.hero_spider.supreme_hunter.damage_min[1]%$-%$heroes.hero_spider.supreme_hunter.damage_max[1]%$ de daño.",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_2"] = "En un abrir y cerrar de ojos, Spydyr se teletransporta al enemigo con más salud, infligiéndole %$heroes.hero_spider.supreme_hunter.damage_min[2]%$-%$heroes.hero_spider.supreme_hunter.damage_max[2]%$ de daño.",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_3"] = "En un abrir y cerrar de ojos, Spydyr se teletransporta al enemigo con más salud, infligiéndole %$heroes.hero_spider.supreme_hunter.damage_min[3]%$-%$heroes.hero_spider.supreme_hunter.damage_max[3]%$ de daño.",
["HERO_SPIDER_SUPREME_HUNTER_TITLE"] = "Paso Sombrío",
["HERO_SPIDER_TUNNELING_DESCRIPTION_1"] = "El túnel de Spydyr ahora inflige %$heroes.hero_spider.tunneling.damage_min[1]%$-%$heroes.hero_spider.tunneling.damage_max[1]%$ de daño al resurgir.",
["HERO_SPIDER_TUNNELING_DESCRIPTION_2"] = "El túnel de Spydyr ahora inflige %$heroes.hero_spider.tunneling.damage_min[2]%$-%$heroes.hero_spider.tunneling.damage_max[2]%$ de daño al resurgir.",
["HERO_SPIDER_TUNNELING_DESCRIPTION_3"] = "El túnel de Spydyr ahora inflige %$heroes.hero_spider.tunneling.damage_min[3]%$-%$heroes.hero_spider.tunneling.damage_max[3]%$ de daño al resurgir.",
["HERO_SPIDER_TUNNELING_TITLE"] = "Tunelización",
["HERO_VENOM_CLASS"] = "Asesino Corrupto",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_1"] = "Llena un área con una sustancia viscosa que enlentence a los enemigos para después perforarlos con espinas que hacen %$heroes.hero_venom.ultimate.s_damage[2]%$ de daño verdadero.",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_2"] = "Llena un área con una sustancia viscosa que enlentence a los enemigos para después perforarlos con espinas que hacen %$heroes.hero_venom.ultimate.s_damage[3]%$ de daño verdadero.",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_3"] = "Llena un área con una sustancia viscosa que enlentence a los enemigos para después perforarlos con espinas que hacen %$heroes.hero_venom.ultimate.s_damage[4]%$ de daño verdadero.",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_DESCRIPTION"] = "Invoca una sustancia viscosa en el camino, enlenteciendo y dañando a los enemigos.",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_NAME"] = "Muerte Insidiosa",
["HERO_VENOM_CREEPING_DEATH_TITLE"] = "MUERTE INSIDIOSA",
["HERO_VENOM_DESC"] = "Después de que el Culto intentara convertirlo en una abominación sin éxito, el mercenario Grimson fue abandonado en una celda. Habiendo obtenido poderes de cambio de forma gracias al tortuoso proceso, Grimson escapó del culto jurando vengarse de sus captores.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_1"] = "Grimson devora a un enemigo con menos de %$heroes.hero_venom.eat_enemy.hp_trigger%$% de vida, recuperando el %$heroes.hero_venom.eat_enemy.regen[1]%$% de su vida total en el proceso.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_2"] = "Grimson devora a un enemigo con menos de %$heroes.hero_venom.eat_enemy.hp_trigger%$% de vida, recuperando el %$heroes.hero_venom.eat_enemy.regen[2]%$% de su vida total en el proceso.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_3"] = "Grimson devora a un enemigo con menos de %$heroes.hero_venom.eat_enemy.hp_trigger%$% de vida, recuperando el %$heroes.hero_venom.eat_enemy.regen[3]%$% de su vida total en el proceso.",
["HERO_VENOM_EAT_ENEMY_TITLE"] = "RENOVACIÓN DE LA CARNE",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_1"] = "Llena el camino de espinas puntiagudas. Cada espina hace %$heroes.hero_venom.floor_spikes.s_damage[1]%$ de daño verdadero a los enemigos cercanos.",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_2"] = "Llena el camino de espinas puntiagudas. Cada espina hace %$heroes.hero_venom.floor_spikes.s_damage[2]%$ de daño verdadero a los enemigos cercanos.",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_3"] = "Llena el camino de espinas puntiagudas. Cada espina hace %$heroes.hero_venom.floor_spikes.s_damage[3]%$ de daño verdadero a los enemigos cercanos.",
["HERO_VENOM_FLOOR_SPIKES_TITLE"] = "ESPINAS MORTALES",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_1"] = "Cuando tiene menos de %$heroes.hero_venom.inner_beast.trigger_hp%$% de vida, Grimson se transforma por %$heroes.hero_venom.inner_beast.duration%$ segundos, ganando %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[1]%$% de daño extra y curándose por %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$% de su vida total con cada ataque.",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_2"] = "Cuando tiene menos de %$heroes.hero_venom.inner_beast.trigger_hp%$% de vida, Grimson se transforma por %$heroes.hero_venom.inner_beast.duration%$ segundos, ganando %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[2]%$% de daño extra y curándose por %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$% de su vida total con cada ataque.",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_3"] = "Cuando tiene menos de %$heroes.hero_venom.inner_beast.trigger_hp%$% de vida, Grimson se transforma por %$heroes.hero_venom.inner_beast.duration%$ segundos, ganando %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[3]%$% de daño extra y curándose por %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$% de su vida total con cada ataque.",
["HERO_VENOM_INNER_BEAST_TITLE"] = "BESTIA INTERIOR",
["HERO_VENOM_NAME"] = "Grimson",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_1"] = "Golpea a un enemigo distante, haciendo %$heroes.hero_venom.ranged_tentacle.s_damage[1]%$ de daño físico con un %$heroes.hero_venom.ranged_tentacle.bleed_chance[1]%$% de chance de causar sangrado. El sangrado hace %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$ de daño por segundo por %$heroes.hero_venom.ranged_tentacle.bleed_duration[1]%$ segundos.",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_2"] = "Golpea a un enemigo distante, haciendo %$heroes.hero_venom.ranged_tentacle.s_damage[2]%$ de daño físico con un %$heroes.hero_venom.ranged_tentacle.bleed_chance[2]%$% de chance de causar sangrado. El sangrado hace %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$ de daño por segundo por %$heroes.hero_venom.ranged_tentacle.bleed_duration[2]%$ segundos.",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_3"] = "Golpea a un enemigo distante, haciendo %$heroes.hero_venom.ranged_tentacle.s_damage[3]%$ de daño físico con un %$heroes.hero_venom.ranged_tentacle.bleed_chance[3]%$% de chance de causar sangrado. El sangrado hace %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$ de daño por segundo por %$heroes.hero_venom.ranged_tentacle.bleed_duration[3]%$ segundos.",
["HERO_VENOM_RANGED_TENTACLE_TITLE"] = "BUSCACORAZONES",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_1"] = "Dispara %$heroes.hero_vesper.ultimate.s_spread[2]%$ flechas en un área. Cada una hace %$heroes.hero_vesper.ultimate.damage[2]%$ de daño físico a los enemigos impactados.",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_2"] = "Dispara %$heroes.hero_vesper.ultimate.s_spread[3]%$ flechas en un área. Cada una hace %$heroes.hero_vesper.ultimate.damage[3]%$ de daño físico a los enemigos impactados.",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_3"] = "Dispara %$heroes.hero_vesper.ultimate.s_spread[4]%$ flechas en un área. Cada una hace %$heroes.hero_vesper.ultimate.damage[4]%$ de daño físico a los enemigos impactados.",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "Dispara una gran cantidad de flechas en un área, haciendo daño a los enemigos.",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_NAME"] = "Tormenta de Flechas",
["HERO_VESPER_ARROW_STORM_TITLE"] = "TORMENTA DE FLECHAS",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_1"] = "Dispara una flecha que aturde al enemigo por %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[1]%$ segundos, haciendo %$heroes.hero_vesper.arrow_to_the_knee.s_damage[1]%$ de daño físico.",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_2"] = "Dispara una flecha que aturde al enemigo por %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[2]%$ segundos, haciendo %$heroes.hero_vesper.arrow_to_the_knee.s_damage[2]%$ de daño físico.",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_3"] = "Dispara una flecha que aturde al enemigo por %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[3]%$ segundo, haciendo %$heroes.hero_vesper.arrow_to_the_knee.s_damage[3]%$ de daño físico.",
["HERO_VESPER_ARROW_TO_THE_KNEE_TITLE"] = "FLECHAZO EN LA RODILLA",
["HERO_VESPER_CLASS"] = "Capitán de la Guardia",
["HERO_VESPER_DESC"] = "Hábil tanto con la espada como con el arco, Vesper se ganó su lugar como comandante de las fuerzas Linireanas. Después de que Linirea cayó y el Rey Denas desapareció, Vesper reunió a todos los soldados que pudo y comenzó una cruzada para encontrar al otrora regente.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_1"] = "Cuando tiene menos de %$heroes.hero_vesper.disengage.hp_to_trigger%$% de vida, Vesper esquiva el siguiente ataque cuerpo a cuerpo saltando hacia atrás, disparando tres flechas que hacen %$heroes.hero_vesper.disengage.s_damage[1]%$ de daño cada una a los enemigos cercanos.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_2"] = "Cuando tiene menos de %$heroes.hero_vesper.disengage.hp_to_trigger%$% de vida, Vesper esquiva el siguiente ataque cuerpo a cuerpo saltando hacia atrás, disparando tres flechas que hacen %$heroes.hero_vesper.disengage.s_damage[2]%$ de daño cada una a los enemigos cercanos.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_3"] = "Cuando tiene menos de %$heroes.hero_vesper.disengage.hp_to_trigger%$% de vida, Vesper esquiva el siguiente ataque cuerpo a cuerpo saltando hacia atrás, disparando tres flechas que hacen %$heroes.hero_vesper.disengage.s_damage[3]%$ de daño cada una a los enemigos cercanos.",
["HERO_VESPER_DISENGAGE_TITLE"] = "RETIRADA TÁCTICA",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_1"] = "Golpea a un enemigo tres veces haciendo un total de %$heroes.hero_vesper.martial_flourish.s_damage[1]%$ de daño físico.",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_2"] = "Golpea a un enemigo tres veces haciendo un total de %$heroes.hero_vesper.martial_flourish.s_damage[2]%$ de daño físico.",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_3"] = "Golpea a un enemigo tres veces haciendo un total de %$heroes.hero_vesper.martial_flourish.s_damage[3]%$ de daño físico.",
["HERO_VESPER_MARTIAL_FLOURISH_TITLE"] = "FLORITURA MARCIAL",
["HERO_VESPER_NAME"] = "Vesper",
["HERO_VESPER_RICOCHET_DESCRIPTION_1"] = "Dispara una flecha que rebota entre %$heroes.hero_vesper.ricochet.s_bounces[1]%$ enemigos haciendo %$heroes.hero_vesper.ricochet.s_damage[1]%$ de daño físico a cada uno.",
["HERO_VESPER_RICOCHET_DESCRIPTION_2"] = "Dispara una flecha que rebota entre %$heroes.hero_vesper.ricochet.s_bounces[2]%$ enemigos haciendo %$heroes.hero_vesper.ricochet.s_damage[2]%$ de daño físico a cada uno.",
["HERO_VESPER_RICOCHET_DESCRIPTION_3"] = "Dispara una flecha que rebota entre %$heroes.hero_vesper.ricochet.s_bounces[3]%$ enemigos haciendo %$heroes.hero_vesper.ricochet.s_damage[3]%$ de daño físico a cada uno.",
["HERO_VESPER_RICOCHET_TITLE"] = "FLECHA REBOTADORA",
["HERO_WITCH_CLASS"] = "Bruja Traviesa",
["HERO_WITCH_DESC"] = "Si bien gusta de sorprender a los extraños que pasan por el Bosque Faérico con trucos divertidos e inofensivos, aquellos que presentan una amenaza para el bosque o para otros gnomos pronto descubren que su sonrisa esconde una bruja implacable que temer.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_1"] = "Cuando tiene menos de %$heroes.hero_witch.disengage.hp_to_trigger%$% de vida, se teletransporta hacia atrás dejando un señuelo que pelea por ella. El señuelo tiene %$heroes.hero_witch.disengage.decoy.hp_max[1]%$ de vida y explota al ser destruido, aturdiendo enemigos por %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[1]%$ segundo.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_2"] = "Cuando tiene menos de %$heroes.hero_witch.disengage.hp_to_trigger%$% de vida, se teletransporta hacia atrás dejando un señuelo que pelea por ella. El señuelo tiene %$heroes.hero_witch.disengage.decoy.hp_max[2]%$ de vida y explota al ser destruido, aturdiendo enemigos por %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[2]%$ segundos.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_3"] = "Cuando tiene menos de %$heroes.hero_witch.disengage.hp_to_trigger%$% de vida, se teletransporta hacia atrás dejando un señuelo que pelea por ella. El señuelo tiene %$heroes.hero_witch.disengage.decoy.hp_max[3]%$ de vida y explota al ser destruido, aturdiendo enemigos por %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[3]%$ segundos.",
["HERO_WITCH_DISENGAGE_TITLE"] = "SEÑUELO RESPLANDECIENTE",
["HERO_WITCH_NAME"] = "Stregi",
["HERO_WITCH_PATH_AOE_DESCRIPTION_1"] = "Lanza una poción gigante sobre el camino, haciendo %$heroes.hero_witch.skill_path_aoe.s_damage[1]%$ de daño mágico en área y enlenteciendo enemigos por %$heroes.hero_witch.skill_path_aoe.duration[1]%$ segundos.",
["HERO_WITCH_PATH_AOE_DESCRIPTION_2"] = "Lanza una poción gigante sobre el camino, haciendo %$heroes.hero_witch.skill_path_aoe.s_damage[2]%$ de daño mágico en área y enlenteciendo enemigos por %$heroes.hero_witch.skill_path_aoe.duration[2]%$ segundos.",
["HERO_WITCH_PATH_AOE_DESCRIPTION_3"] = "Lanza una poción gigante sobre el camino, haciendo %$heroes.hero_witch.skill_path_aoe.s_damage[3]%$ de daño mágico en área y enlenteciendo enemigos por %$heroes.hero_witch.skill_path_aoe.duration[3]%$ segundos.",
["HERO_WITCH_PATH_AOE_TITLE"] = "AGITAR Y APLASTAR",
["HERO_WITCH_POLYMORPH_DESCRIPTION_1"] = "Convierte a un enemigo en un calabacín por %$heroes.hero_witch.skill_polymorph.duration[1]%$ segundos.",
["HERO_WITCH_POLYMORPH_DESCRIPTION_2"] = "Convierte a un enemigo en un calabacín por %$heroes.hero_witch.skill_polymorph.duration[2]%$ segundos.",
["HERO_WITCH_POLYMORPH_DESCRIPTION_3"] = "Convierte a un enemigo en un calabacín por %$heroes.hero_witch.skill_polymorph.duration[3]%$ segundos.",
["HERO_WITCH_POLYMORPH_TITLE"] = "¡VERDURACIÓN!",
["HERO_WITCH_SOLDIERS_DESCRIPTION_1"] = "Invoca %$heroes.hero_witch.skill_soldiers.soldiers_amount[1]%$ gato que lucha contra los enemigos. El gato tiene %$heroes.hero_witch.skill_soldiers.soldier.hp_max[1]%$ de vida y hace %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[1]%$ de daño físico. ",
["HERO_WITCH_SOLDIERS_DESCRIPTION_2"] = "Invoca %$heroes.hero_witch.skill_soldiers.soldiers_amount[2]%$ gatos que luchan contra los enemigos. Los gatos tienen %$heroes.hero_witch.skill_soldiers.soldier.hp_max[2]%$ de vida y hacen %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[2]%$ de daño físico. ",
["HERO_WITCH_SOLDIERS_DESCRIPTION_3"] = "Invoca %$heroes.hero_witch.skill_soldiers.soldiers_amount[3]%$ gatos que luchan contra los enemigos. Los gatos tienen %$heroes.hero_witch.skill_soldiers.soldier.hp_max[3]%$ de vida y hacen %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[3]%$ de daño físico. ",
["HERO_WITCH_SOLDIERS_TITLE"] = "FURIAS NOCTURNAS",
["HERO_WITCH_ULTIMATE_DESCRIPTION_1"] = "Teletransporta %$heroes.hero_witch.ultimate.max_targets[2]%$ enemigos hacia atrás, dejándolos dormidos por %$heroes.hero_witch.ultimate.duration[2]%$ segundos.",
["HERO_WITCH_ULTIMATE_DESCRIPTION_2"] = "Teletransporta %$heroes.hero_witch.ultimate.max_targets[3]%$ enemigos hacia atrás, dejándolos dormidos por %$heroes.hero_witch.ultimate.duration[3]%$ segundos.",
["HERO_WITCH_ULTIMATE_DESCRIPTION_3"] = "Teletransporta %$heroes.hero_witch.ultimate.max_targets[4]%$ enemigos hacia atrás, dejándolos dormidos por %$heroes.hero_witch.ultimate.duration[4]%$ segundos.",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "Teleporta enemigos hacia atrás en el camino, dejándolos dormidos por un momento.",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_NAME"] = "Retorno Dormilón",
["HERO_WITCH_ULTIMATE_TITLE"] = "RETORNO DORMILÓN",
["HERO_WUKONG_CLASS"] = "El Rey Mono",
["HERO_WUKONG_DESC"] = "Nacido de una piedra celestial de Yin y Yang, Sun Wukong fue dotado de fuerza, agilidad e inmortalidad. Pero los reyes demonio le robaron las esferas de poder. Ahora, el legendario embaucador se alza para recuperarlas antes de que sea demasiado tarde.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_1"] = "Cae y agranda el Jingu Bang para aplastar a un enemigo, matándolo instantáneamente y causando %$heroes.hero_wukong.giant_staff.area_damage.damage_min[1]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[1]%$ de daño en un área alrededor del objetivo.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_2"] = "Tropieza y agranda el Jingu Bang para aplastar a un enemigo, matándolo instantáneamente e infligiendo %$heroes.hero_wukong.giant_staff.area_damage.damage_min[2]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[2]%$ de daño en un área alrededor del objetivo.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_3"] = "Tropieza y agranda el Jingu Bang para pisotear a un enemigo, matándolo instantáneamente e infligiendo %$heroes.hero_wukong.giant_staff.area_damage.damage_min[3]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[3]%$ de daño en un área alrededor del objetivo.",
["HERO_WUKONG_GIANT_STAFF_TITLE"] = "Técnica del Jingu Bang",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_1"] = "Invoca 2 clones de cabello de Sun Wukong para luchar a su lado. Infligen %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[1]%$ de daño y duran %$heroes.hero_wukong.hair_clones.soldier.duration[1]%$ segundos.",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_2"] = "Invoca 2 clones de cabello de Sun Wukong para luchar a su lado. Infligen %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[2]%$ de daño y duran %$heroes.hero_wukong.hair_clones.soldier.duration[2]%$ segundos.",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_3"] = "Invoca 2 clones de cabello de Sun Wukong para luchar a su lado. Infligen %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[3]%$ de daño y duran %$heroes.hero_wukong.hair_clones.soldier.duration[3]%$ segundos.",
["HERO_WUKONG_HAIR_CLONES_TITLE"] = "Hair Clones",
["HERO_WUKONG_NAME"] = "Sun Wukong",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_1"] = "Lanza el Jingu Bang al aire, multiplicándolo en %$heroes.hero_wukong.pole_ranged.pole_amounts[1]%$ bastones que caen sobre los enemigos, cada uno causando %$heroes.hero_wukong.pole_ranged.damage_min[1]%$ de daño y aturdiendo a los enemigos en un área pequeña.",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_2"] = "Lanza el Jingu Bang al aire, multiplicándolo en %$heroes.hero_wukong.pole_ranged.pole_amounts[2]%$ bastones que caen sobre los enemigos, cada uno causando %$heroes.hero_wukong.pole_ranged.damage_min[2]%$ de daño y aturdiendo a los enemigos en un área pequeña.",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_3"] = "Lanza el Jingu Bang al aire, multiplicándolo en %$heroes.hero_wukong.pole_ranged.pole_amounts[3]%$ bastones que caen sobre los enemigos, cada uno causando %$heroes.hero_wukong.pole_ranged.damage_min[3]%$ de daño y aturdiendo a los enemigos en un área pequeña.",
["HERO_WUKONG_POLE_RANGED_TITLE"] = "Barricada de postes",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_1"] = "El Dragón Blanco se lanza al suelo con una fuerza tremenda, inflige %$heroes.hero_wukong.ultimate.damage_total[2]%$ de daño verdadero y deja un área que ralentiza.",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_2"] = "El Dragón Blanco irrumpe en el suelo con una fuerza tremenda, inflige %$heroes.hero_wukong.ultimate.damage_total[3]%$ de daño verdadero y deja un área de ralentización.",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_3"] = "El Dragón Blanco se lanza al suelo con una fuerza tremenda, inflige %$heroes.hero_wukong.ultimate.damage_total[4]%$ de daño verdadero y deja un área de ralentización.",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "Invoca al Dragón Blanco.",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_NAME"] = "El Dragón Blanco",
["HERO_WUKONG_ULTIMATE_TITLE"] = "El Dragón Blanco",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_1"] = "Zhu Bajie, el leal compañero de Sun Wukong, lo sigue a todas partes. Inflige %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[1]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[1]%$ de daño y tiene una pequeña probabilidad de realizar un ataque de gran daño en área.",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_2"] = "Zhu Bajie, el leal compañero de Sun Wukong, lo sigue a todas partes. Inflige %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[2]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[2]%$ de daño y tiene una pequeña probabilidad de realizar un ataque de gran daño en área.",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_3"] = "Zhu Bajie, el leal compañero de Sun Wukong, lo sigue a todas partes. Causa %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[3]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[3]%$ de daño y tiene una pequeña probabilidad de infligir un ataque de daño en área.",
["HERO_WUKONG_ZHU_APPRENTICE_TITLE"] = "Aprendiz de Zhu",
["HINT"] = "CONSEJO",
["HOURS_ABBREVIATION"] = "h",
["Hardcore! play at your own risk!"] = "¡Extremo! ¡Juega a tu propio riesgo!",
["Help"] = "Ayuda",
["Hero at your command!"] = "¡Héroe a su servicio!",
["Heroes"] = "Héroes",
["Heroes are elite units that can face strong enemies and support your forces."] = "Los héroes son unidades élite que pueden enfrentar enemigos poderosos y dar apoyo a tus tropas.",
["Heroes gain experience every time they damage an enemy or use an ability."] = "Los héroes ganan experiencia al causar daño a enemigos o al usar una habilidad.",
["Heroic"] = "Heroico",
["Heroic challenge"] = "Desafío heroico",
["High"] = "Alta",
["I'm ready. Now bring it on!"] = "Estoy preparado. ¡Adelante!",
["INCOMING NEXT WAVE!"] = "¡SE APROXIMA UNA NUEVA OLEADA!",
["INCOMING WAVE"] = "SE APROXIMA UNA OLEADA",
["INGAME_BALLOON_BUILD_HERE"] = "¡Construye aquí!",
["INGAME_BALLOON_GOAL"] = "No dejes que los enemigos pasen este punto",
["INGAME_BALLOON_GOLD"] = "Gana oro matando enemigos",
["INGAME_BALLOON_INCOMING"] = "¡SIGUIENTE OLEADA!",
["INGAME_BALLOON_NEW_HERO"] = "¡Héroe nuevo!",
["INGAME_BALLOON_NEW_POWER"] = "¡Poder nuevo!",
["INGAME_BALLOON_NOTIFICATION_TAP_HERE"] = "¡Toca aquí!",
["INGAME_BALLOON_SELECT_HERO"] = "¡Toca para seleccionar!",
["INGAME_BALLOON_START_BATTLE"] = "¡COMENZAR BATALLA!",
["INGAME_BALLOON_TAP_HERE"] = "¡Toca en el camino!",
["INGAME_BALLOON_TAP_TO_CALL"] = "TOCA PARA ADELANTAR",
["INGAME_BALLOON_TAP_TWICE_BUILD"] = "Click para construir torre",
["INGAME_BALLOON_TAP_TWICE_START"] = "TOCA DOS VECES PARA COMENZAR LA BATALLA",
["INGAME_BALLOON_TAP_TWICE_WAVE"] = "Click para adelantar oleada",
["INGAME_TUTORIAL1_HELP1"] = "No dejes que los enemigos pasen este punto.",
["INGAME_TUTORIAL1_HELP2"] = "Construye torres para defender el camino.",
["INGAME_TUTORIAL1_HELP3"] = "Gana oro matando enemigos.",
["INGAME_TUTORIAL1_SUBTITLE1"] = "Protege tus tierras de los ataques enemigos.",
["INGAME_TUTORIAL1_SUBTITLE2"] = "Construye torres defensivas en el camino para detenerlos.",
["INGAME_TUTORIAL1_TITLE"] = "Objetivo",
["INGAME_TUTORIAL_GOTCHA_1"] = "¡Listo!",
["INGAME_TUTORIAL_GOTCHA_2"] = "¡Estoy listo, vamos a ellos!",
["INGAME_TUTORIAL_HINT"] = "CONSEJO",
["INGAME_TUTORIAL_INSTRUCTIONS"] = "INSTRUCCIONES",
["INGAME_TUTORIAL_NEW_TIP"] = "NUEVO CONSEJO",
["INGAME_TUTORIAL_NEXT"] = "¡Siguiente!",
["INGAME_TUTORIAL_OK"] = "¡Ok!",
["INGAME_TUTORIAL_SKIP"] = "¡Saltear!",
["INGAME_TUTORIAL_TIP_CHALLENGE"] = "ADVERTENCIA",
["INSTRUCTIONS"] = "INSTRUCCIONES",
["ITEM_CLUSTER_BOMB_BOTTOM_DESC"] = "Como las palomitas de maíz pero más divertido y menos sabroso.",
["ITEM_CLUSTER_BOMB_BOTTOM_INFO"] = "Una bomba que crea bombas más pequeñas.",
["ITEM_CLUSTER_BOMB_DESC"] = "Lanza una bomba que daña a los enemigos en un área y explota en bombas más pequeñas.",
["ITEM_CLUSTER_BOMB_NAME"] = "Bomba Racimo",
["ITEM_DEATHS_TOUCH_BOTTOM_DESC"] = "Genial si quieres sentirte como un dios... ¡DE LA MUERTE!",
["ITEM_DEATHS_TOUCH_BOTTOM_INFO"] = "Selecciona. Toca al objetivo. Mata.",
["ITEM_DEATHS_TOUCH_DESC"] = "Usa el poder de la Muerte y toca sobre cualquier enemigo para eliminarlo instantáneamente. No funciona con jefes ni minijefes.",
["ITEM_DEATHS_TOUCH_NAME"] = "Toque de la Muerte",
["ITEM_LOOT_BOX_BOTTOM_DESC"] = "Un par de estas y estás hecho de por vida.",
["ITEM_LOOT_BOX_BOTTOM_INFO"] = "Lanza una caja sobre el camino, haciendo daño a los enemigos y obteniendo oro instantáneamente.",
["ITEM_LOOT_BOX_DESC"] = "Lanza una caja sobre el camino, haciendo daño a los enemigos y obteniendo 300 de oro instantáneamente.",
["ITEM_LOOT_BOX_NAME"] = "Premio Gordo",
["ITEM_MEDICAL_KIT_BOTTOM_DESC"] = "Todo lo que necesitas para emparcharte, general.",
["ITEM_MEDICAL_KIT_BOTTOM_INFO"] = "Restaura hasta 3 corazones para el jugador.",
["ITEM_MEDICAL_KIT_DESC"] = "Un kit especial que restaura hasta 3 corazones para el jugador.",
["ITEM_MEDICAL_KIT_NAME"] = "Botiquín",
["ITEM_PORTABLE_COIL_BOTTOM_DESC"] = "¡Zip! ¡Zap! ¡Freído como una rata!",
["ITEM_PORTABLE_COIL_BOTTOM_INFO"] = "Coloca una trampa que daña y aturde a los enemigos en un área.",
["ITEM_PORTABLE_COIL_DESC"] = "Prepara una trampa de área que daña y aturde a los enemigos que la activan. Sus efectos pueden extenderse a otros enemigos cercanos.",
["ITEM_PORTABLE_COIL_NAME"] = "Bobina Portatil",
["ITEM_ROOM_EQUIP"] = "Equipar",
["ITEM_ROOM_EQUIPPED"] = "Equipado",
["ITEM_ROOM_EQUIPPED_ITEMS"] = "Ítems equipados",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_DESC"] = "¿Alguna vez te quedaste sin tiempo para luchar con tus enemigos? ¡Se acabaron las preocupaciones!",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_INFO"] = "Teletransporta a un grupo de enemigos hacia atrás en el camino.",
["ITEM_SCROLL_OF_SPACESHIFT_DESC"] = "Teletransporta hasta 10 enemigos hacia atrás en el camino.",
["ITEM_SCROLL_OF_SPACESHIFT_NAME"] = "Pergamino de Desplazamiento",
["ITEM_SECOND_BREATH_BOTTOM_DESC"] = "Levantarse de la tumba, sin la desventaja de ser un no muerto.",
["ITEM_SECOND_BREATH_BOTTOM_INFO"] = "Revive a los héroes caídos, cura a los heridos y resetea el enfriamiento de los poderes de héroe.",
["ITEM_SECOND_BREATH_DESC"] = "Una bendición divina que revive a los héroes caídos, cura a los héroes heridos y resetea el enfriamiento de los poderes de héroe.",
["ITEM_SECOND_BREATH_NAME"] = "Segundo Aliento",
["ITEM_SUMMON_BLACKBURN_BOTTOM_DESC"] = "El mejor. El único. El inimitable.",
["ITEM_SUMMON_BLACKBURN_BOTTOM_INFO"] = "Invoca al poderoso Blackburn para que luche a tu lado.",
["ITEM_SUMMON_BLACKBURN_DESC"] = "Invoca al poderoso guerrero fantasmal para masacrar a tus enemigos.",
["ITEM_SUMMON_BLACKBURN_NAME"] = "Yelmo de Blackburn",
["ITEM_VEZNAN_WRATH_BOTTOM_DESC"] = "¡Deja que prueben un poco del poder ilimitado del Hechicero Oscuro!",
["ITEM_VEZNAN_WRATH_BOTTOM_INFO"] = "Destruye a cada enemigo en el campo de batalla.",
["ITEM_VEZNAN_WRATH_DESC"] = "Vez'nan lanza un poderoso hechizo que destruye a todos los enemigos en la pantalla.",
["ITEM_VEZNAN_WRATH_NAME"] = "Ira de Vez'nan",
["ITEM_WINTER_AGE_BOTTOM_DESC"] = "También es útil si REALMENTE odias el verano.",
["ITEM_WINTER_AGE_BOTTOM_INFO"] = "Congela a todos los enemigos en la pantalla.",
["ITEM_WINTER_AGE_DESC"] = "Un poderoso hechizo que crea vientos escalofriantes que congelan a todos los enemigos por varios segundos.",
["ITEM_WINTER_AGE_NAME"] = "Era del Hielo",
["Impossible"] = "Imposible",
["Iron"] = "Hierro",
["Iron Challenge"] = "Desafío de hierro",
["Iron challenge"] = "Desafío de hierro",
["JOYSTICK_CONFIG_AXIS_DEAD_ZONE"] = "Zona muerta palanca",
["JOYSTICK_CONFIG_AXIS_DEAD_ZONE_XBOX"] = "Zona muerta stick",
["JOYSTICK_CONFIG_FIRST_REPEAT_DELAY"] = "Retraso primera repetición",
["JOYSTICK_CONFIG_POINTER_ACCEL"] = "Acel. puntero",
["JOYSTICK_CONFIG_POINTER_MAX_ACCEL"] = "Acel max. puntero",
["JOYSTICK_CONFIG_POINTER_SENS"] = "Sensibilidad puntero",
["JOYSTICK_CONFIG_POINTER_SPEED"] = "Velocidad puntero",
["JOYSTICK_CONFIG_REPEAT_DELAY"] = "Retraso repetición",
["JOYSTICK_CONFIG_SWAP_ABXY"] = "Intercambiar A/B y X/Y",
["JOYSTICK_HELP_INGAME_A"] = "Seleccionar",
["JOYSTICK_HELP_INGAME_AXIS_LEFT"] = "Mover",
["JOYSTICK_HELP_INGAME_AXIS_LEFT_BUTTON"] = "Alternar puntero",
["JOYSTICK_HELP_INGAME_B"] = "Cancelar/Volver",
["JOYSTICK_HELP_INGAME_BACK"] = "Mostrar información",
["JOYSTICK_HELP_INGAME_DPAD_DOWN"] = "Mover refuerzos",
["JOYSTICK_HELP_INGAME_DPAD_LEFT"] = "Pedir refuerzos",
["JOYSTICK_HELP_INGAME_DPAD_RIGHT"] = "Poder de héroe 2",
["JOYSTICK_HELP_INGAME_DPAD_UP"] = "Poder de héroe 1",
["JOYSTICK_HELP_INGAME_ESCAPE"] = "Cancelar/Volver",
["JOYSTICK_HELP_INGAME_LB"] = "Héroe primario",
["JOYSTICK_HELP_INGAME_MOVE_HEROES"] = "Mover héroes",
["JOYSTICK_HELP_INGAME_MOVE_REINFORCEMENTS"] = "Mover refuerzos",
["JOYSTICK_HELP_INGAME_NX_A"] = "Seleccionar",
["JOYSTICK_HELP_INGAME_NX_AXIS_LEFT"] = "Mover",
["JOYSTICK_HELP_INGAME_NX_AXIS_LEFT_BUTTON"] = "Alternar puntero",
["JOYSTICK_HELP_INGAME_NX_B"] = "Cancelar/Volver",
["JOYSTICK_HELP_INGAME_NX_L"] = "Héroe primario",
["JOYSTICK_HELP_INGAME_NX_MINUS"] = "Mostrar información",
["JOYSTICK_HELP_INGAME_NX_PLUS"] = "Pausa/Continuar",
["JOYSTICK_HELP_INGAME_NX_R"] = "Héroe secundario",
["JOYSTICK_HELP_INGAME_NX_X"] = "Enviar oleada",
["JOYSTICK_HELP_INGAME_NX_Y"] = "Info oleada",
["JOYSTICK_HELP_INGAME_POWERS"] = "Poderes",
["JOYSTICK_HELP_INGAME_RB"] = "Héroe secundario",
["JOYSTICK_HELP_INGAME_START"] = "Pausa/Continuar",
["JOYSTICK_HELP_INGAME_X"] = "Enviar oleada",
["JOYSTICK_HELP_INGAME_Y"] = "Info oleada",
["JOYSTICK_HELP_MAP_A"] = "Seleccionar",
["JOYSTICK_HELP_MAP_AXIS_LEFT"] = "Mover",
["JOYSTICK_HELP_MAP_B"] = "Cancelar/Volver",
["JOYSTICK_HELP_MAP_BACK"] = "Mostrar/Ocultar opciones",
["JOYSTICK_HELP_MAP_LB"] = "Nivel/Página anterior",
["JOYSTICK_HELP_MAP_NX_A"] = "Seleccionar",
["JOYSTICK_HELP_MAP_NX_AXIS_LEFT"] = "Mover",
["JOYSTICK_HELP_MAP_NX_B"] = "Cancelar/Volver",
["JOYSTICK_HELP_MAP_NX_L"] = "Nivel/Página anterior",
["JOYSTICK_HELP_MAP_NX_MINUS"] = "Mostrar/Ocultar opciones",
["JOYSTICK_HELP_MAP_NX_PLUS"] = "Mostrar/Ocultar opciones",
["JOYSTICK_HELP_MAP_NX_R"] = "Nivel/Página siguiente",
["JOYSTICK_HELP_MAP_RB"] = "Nivel/Página siguiente",
["JOYSTICK_HELP_MAP_START"] = "Mostrar/Ocultar opciones",
["JOYSTICK_HELP_SLOTS_A"] = "Seleccionar",
["JOYSTICK_HELP_SLOTS_AXIS_LEFT"] = "Mover",
["JOYSTICK_HELP_SLOTS_B"] = "Cancelar/Volver",
["JOYSTICK_HELP_SLOTS_BACK"] = "Mostrar/Ocultar opciones",
["JOYSTICK_HELP_SLOTS_NX_A"] = "Seleccionar",
["JOYSTICK_HELP_SLOTS_NX_AXIS_LEFT"] = "Mover",
["JOYSTICK_HELP_SLOTS_NX_B"] = "Cancelar/Volver",
["JOYSTICK_HELP_SLOTS_NX_MINUS"] = "Mostrar/Ocultar opciones",
["JOYSTICK_HELP_SLOTS_NX_PLUS"] = "Mostrar/Ocultar opciones",
["JOYSTICK_HELP_SLOTS_START"] = "Mostrar/Ocultar opciones",
["KEYBOARD_KEY_ESCAPE"] = "ESCAPE",
["KEYBOARD_KEY_PAGE_DOWN"] = "AV.PÁG",
["KEYBOARD_KEY_PAGE_UP"] = "RE.PÁG",
["KEYBOARD_KEY_RETURN"] = "INTRO",
["KEYBOARD_KEY_SPACE"] = "ESPACIO",
["LEVEL_10_HEROIC"] = "Heroic Description 10",
["LEVEL_10_HISTORY"] = "Descubrimos que el Culto utiliza los cristales para construir un artefacto siniestro en las afueras del cañón. Resuena con una extraña energía y el aire alrededor se siente pesado. Tenemos que asegurarnos de destruirlo antes de seguir avanzando.",
["LEVEL_10_IRON"] = "Iron Description 10",
["LEVEL_10_IRON_UNLOCK"] = "To be defined",
["LEVEL_10_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_10_TITLE"] = "10. Patios del Templo",
["LEVEL_11_HEROIC"] = "Heroic Description 11",
["LEVEL_11_HISTORY"] = "Finalmente logramos salir de los cañones pero seguimos teniendo un largo camino por delante. Nos encontramos frente a un gran portal con cristales incrustados mientras la Vidente Mydrias termina con sus rituales. No sabemos qué puede venir del más allá, ¡pero estamos listos para luchar!",
["LEVEL_11_IRON"] = "Iron Description 11",
["LEVEL_11_IRON_UNLOCK"] = "To be defined",
["LEVEL_11_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_11_TITLE"] = "11. Meseta del Cañón",
["LEVEL_12_HEROIC"] = "Heroic Description 12",
["LEVEL_12_HISTORY"] = "Con Denas nuevamente de nuestro lado cruzamos el portal hacia lo desconocido. Este nuevo mundo se ve como un reflejo siniestro de Linirea sumido en la corrupción. Ten cuidado por donde vas, algo peor que el Culto acecha entre las sombras.",
["LEVEL_12_IRON"] = "Iron Description 12",
["LEVEL_12_IRON_UNLOCK"] = "To be defined",
["LEVEL_12_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_12_TITLE"] = "12. Planicies Corruptas",
["LEVEL_13_HEROIC"] = "Heroic Description 13",
["LEVEL_13_HISTORY"] = "El familiar paisaje del Templo Nubetormenta se vislumbra en el horizonte. El camino es bastante claro, seguir el olor cada vez más fuerte de la corrupción hasta llegar a su origen. Solo tenemos que sobrevivir a los horrores malformados que parecen salir de la tierra misma.",
["LEVEL_13_IRON"] = "Iron Description 13",
["LEVEL_13_IRON_UNLOCK"] = "To be defined",
["LEVEL_13_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_13_TITLE"] = "13. Ruinas Profanadas",
["LEVEL_14_HEROIC"] = "Heroic Description 14",
["LEVEL_14_HISTORY"] = "¡Estas malditas criaturas parecen salir de la nada! Las tropas están inquietas, cada cosa que tocamos parece estar viva y lista para atacarnos, como si la tierra misma estuviese peleando contra nosotros con todas sus fuerzas. La Vidente Mydrias y sus esbirros deben estar cerca.",
["LEVEL_14_IRON"] = "Iron Description 14",
["LEVEL_14_IRON_UNLOCK"] = "To be defined",
["LEVEL_14_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_14_TITLE"] = "14. Valle de la Corrupción",
["LEVEL_15_HEROIC"] = "Heroic Description 15",
["LEVEL_15_HISTORY"] = "Salimos victoriosos del valle y ahora lo único que se encuentra entre nosotros y el Omnividente es Mydrias. Ya vimos de lo que es capaz en el cañón, pero aquí, bajo el poder y la mirada de su maestro, ella tiene la ventaja. Las pocas chances de vencer no nos han parado antes. ¡A las armas!",
["LEVEL_15_IRON"] = "Iron Description 15",
["LEVEL_15_IRON_UNLOCK"] = "To be defined",
["LEVEL_15_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_15_TITLE"] = "15. La Torre Abominable",
["LEVEL_16_HEROIC"] = "Heroic Description 16",
["LEVEL_16_HISTORY"] = "Tras la derrota de Mydrias, el Omnividente es el último gran enemigo en pie. Esta es nuestra última oportunidad para terminar con el Culto y repeler la invasión. Lo que suceda después no tendrá importancia si no luchamos codo a codo una última vez. ¡Marchen!",
["LEVEL_16_IRON"] = "Iron Description 16",
["LEVEL_16_IRON_UNLOCK"] = "To be defined",
["LEVEL_16_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_16_TITLE"] = "16. Cima del Hambre",
["LEVEL_17_HISTORY"] = "Los alrededores del fantástico Bosque Faérico se están tornando hostiles y horripilantes. Se dice que hordas de guerreros elfos caídos y seres espectrales ahora deambulan por estas tierras, atacando a los viajeros y corrompiendo el bosque mismo con su presencia. General, debemos inspeccionar más a fondo.",
["LEVEL_17_TITLE"] = "17. Ruinas Brumosas",
["LEVEL_18_HISTORY"] = "Nos llegó un mensaje del Puesto Hojaprofunda, donde algunos elfos apenas resisten el avance de la horda renacida. Debemos apresurarnos a ayudarlos a ellos y a su capitán, Eridan, antes de que sea demasiado tarde. Una vez que el Puesto  esté debidamente asegurado, podremos avanzar para llegar a la raíz de esta invasión.",
["LEVEL_18_TITLE"] = "18. Puesto Hojaprofunda",
["LEVEL_19_HISTORY"] = "Un exhausto Eridan nos indicó el camino al Templo de los Caídos desde donde la horda es reanimada para invadir el continente, comandada por un hechicero que se hace llamar Navira, el Doblegador de Almas. ¡Hay que detenerlo a toda costa!",
["LEVEL_19_TITLE"] = "19. Templo de los Caídos",
["LEVEL_1_HEROIC"] = "Desafío Heroico",
["LEVEL_1_HISTORY"] = "Hemos explorado los bosques del sur por meses sin resultados. No hay rastros del Rey Denas. Durante la búsqueda nos hicimos amigos de los Arborean, espíritus de la naturaleza. También conocimos a sus problemáticos vecinos, las Bestias Salvajes, que nos atacan apenas nos ven. Terminemos con esto de una vez para seguir buscando al Rey.",
["LEVEL_1_IRON"] = "Desafío de Hierro",
["LEVEL_1_IRON_UNLOCK"] = "Royal Archers\nPaladin Covenant",
["LEVEL_1_MODES_UPGRADES"] = "lvl 1 max",
["LEVEL_1_TITLE"] = "1. Mar de Árboles",
["LEVEL_20_HISTORY"] = "Hemos recibido un susurro urgente de los Arbóreos en el borde del bosque, llamando desesperadamente por ayuda. Están bajo ataque por los incansables Croks. No podrán resistir mucho más tiempo. Recuerda ser cauteloso, General. Los Croks tienen muchos trucos bajo sus escamas.",
["LEVEL_20_TITLE"] = "20. Aldea Arbórea",
["LEVEL_21_HISTORY"] = "Después de garantizar la protección del pueblo, los Arbóreos revelaron que, justo antes del ataque, sintieron que su antiguo sello flaqueaba. Armados con una pista sobre la súbita invasión de los Croks, nos sumergimos en el corazón del pantano. Tropezamos con un antiguo círculo de piedras arbóreo, parece un escondite... el refugio de algo enorme.",
["LEVEL_21_TITLE"] = "21. Las Ruinas Hundidas",
["LEVEL_22_HISTORY"] = "Al llegar al antiguo templo, nuestros peores temores se confirmaron. El sello que había protegido nuestro mundo de Abominor, el Devorador de Reinos, estaba casi deshecho, sostenido únicamente por la magia de unión desesperada de algunos chamanes Arbóreos. General, detenga a Abominor o los reinos serán consumidos por su insaciable fauce.",
["LEVEL_22_TITLE"] = "22. Hueco Hambriento",
["LEVEL_23_HISTORY"] = "Los exploradores reportaron desprendimientos poco naturales en las montañas cercanas y descubrieron que fueron causadas por enanos que no podemos reconocer. Están armando un autómata gigante en la cara sur de la montaña. Deberías echarle un vistazo, General.",
["LEVEL_23_TITLE"] = "23. Puertas de la Forja",
["LEVEL_24_HISTORY"] = "Los enanos siempre fueron inventores extraños, pero este clan Aceroscuro lleva su devoción al metal demasiado lejos, más que la gente de Bolgur. Utilizan sus forjas para \"mejorarse\" a si mismos rápidamente. ¿Quién está detrás de esta locura? ¡Tenemos que averiguarlo!",
["LEVEL_24_TITLE"] = "24. Ensamblaje Frenético",
["LEVEL_25_HISTORY"] = "Tal como temíamos, el interior de la montaña alberga una forja tan grande como para crear este autómata. ¿Cuántos enanos hay? Resisten nuestros avances pero al mismo tiempo siguen trabajando. Y esto es más raro, ¿todos se ven igual? Algo no está bien.",
["LEVEL_25_TITLE"] = "25. Núcleo Colosal",
["LEVEL_26_HISTORY"] = "Entrando y saliendo de la montaña llegamos a una cámara llena de tinas, y no están vacías. Ahora entendemos por qué son tan numerosos y saben tanto de forjar y soldar. Son todos el mismo enano, Grymbeard, que ha estado creando clones de si mismo utilizando ciencia retorcida. General, ¡debemos detenerlo!",
["LEVEL_26_TITLE"] = "26. Cámara Replicadora",
["LEVEL_27_HISTORY"] = "Nos las arreglamos para interrumpir la mayor parte de la operación Aceroscuro en la montaña pero todo será en vano si Grymbeard sigue suelto. Seguramente está trabajando en los últimos detalles de la cabeza del autómata. General, lleva a las tropas a lo alto de la montaña. Esperemos que esta vez estemos peleando con el enano correcto.",
["LEVEL_27_TITLE"] = "27. Domo de Dominación",
["LEVEL_28_HISTORY"] = "Siguiendo las pistas dejadas por nuestros exploradores, hemos descubierto un rastro que conduce a los remanentes de esos malditos cultistas. Parece que han encontrado una nueva diosa a la que adorar: una vil abominación que teje telarañas... Cultistas Y arañas? Nada bueno puede salir de esa combinación.",
["LEVEL_28_TITLE"] = "28. Templo Profanado",
["LEVEL_29_HISTORY"] = "Cuanto más profundizamos, más claro queda que este terror ha estado creciendo debajo durante mucho tiempo, esperando el momento correcto para atacar. A juzgar por la espesura de las telarañas a nuestro alrededor y la oscuridad que nos respira en la nuca, apostaría que estamos cerca del corazón de su guarida.",
["LEVEL_29_TITLE"] = "29. Cámara de Cría",
["LEVEL_2_HEROIC"] = "Heroic Description 2",
["LEVEL_2_HISTORY"] = "¡Alerta! ¡Nos avisaron los fuegos fatuos! ¡El Corazón del Bosque está bajo ataque! Tenemos que volver y ayudar a los Arborean. Parte de las fuerzas del Ejército Oscuro se nos unirán en la batalla, asi que mantengan los ojos abiertos. Por ahora estamos en el mismo barco, pero eso puede cambiar en cualquier momento.",
["LEVEL_2_IRON"] = "Iron Description 2",
["LEVEL_2_IRON_UNLOCK"] = "Arcane Wizard\nTricannon",
["LEVEL_2_MODES_UPGRADES"] = "lvl 2 max",
["LEVEL_2_TITLE"] = "2. La Puerta Guardiana",
["LEVEL_30_HISTORY"] = "Por fin hemos llegado al escondite de su llamada diosa: un templo ruinoso, abandonado hace mucho tiempo y desmoronándose bajo el peso de su propio pasado olvidado. Un trono apropiado para una deidad abandonada. Esta vez nos aseguraremos de no dejar nada vivo y de exterminar a estas pestes de una vez por todas.",
["LEVEL_30_TITLE"] = "30. El Trono Olvidado",
["LEVEL_31_HISTORY"] = "Después de toda la lucha y el esfuerzo, la paz por fin ha regresado a los reinos. Ahora, escuchar el romper de las olas y jugar juegos de mesa mientras se espera a un viejo amigo es la única tarea pendiente. Y aun así, aunque todo parezca tan tranquilo, me pregunto cuánto durará esta paz...",
["LEVEL_31_TITLE"] = "31. Bosque de Monos Celestial",
["LEVEL_32_HISTORY"] = "Nuestra persecución nos ha llevado al corazón del volcán, donde un templo olvidado rendía tributo a las llamas. Pero el Gran Dragón de Fuego, antes un guardián neutral de estas profundidades ardientes, ahora se agita con una furia antinatural. Todo indica que la influencia de Chico Rojo ha corrompido su voluntad. Enfrentarse a un dragón no es tarea fácil, pero no tenemos otra opción. ¡A la batalla!",
["LEVEL_32_TITLE"] = "32. Cueva del Dragón de Fuego",
["LEVEL_33_HISTORY"] = "Tras un agotador enfrentamiento con el Chico Rojo, seguimos avanzando hacia la Isla Tempestad. En cuanto pusimos un pie aquí, nubes cargadas de relámpagos y ráfagas feroces comenzaron a aullar en patrones extraños y retorcidos. Aun así, no tenemos otra opción: la isla contiene la única entrada al palacio de la Princesa. Prepárense... se avecina una tormenta.",
["LEVEL_33_TITLE"] = "33. Isla Tempestuosa",
["LEVEL_34_HISTORY"] = "Podemos agradecer a la Princesa y su Abanico de Hierro por las dificultades que hemos soportado. Habiendo cruzado el puente y resistido las tormentas más feroces, ahora estamos en el ojo de todo. Este lugar permanece intacto—engañosamente tranquilo y hermoso. No podemos bajar la guardia. Ni siquiera la realeza demoníaca nos detendrá.",
["LEVEL_34_TITLE"] = "34. El Ojo de la Tormenta",
["LEVEL_35_HISTORY"] = "Este es el momento. El Rey Demonio Toro se alza imponente en su fastuosa e impenetrable fortaleza. Hemos reunido al resto de nuestras tropas para lanzarnos directamente contra sus muros, una tarea que requerirá más ingenio que fuerza bruta. Debemos atacar antes de que libere por completo el poder de los orbes.\nPor todo lo que valoráis en esta buena tierra... ¡Manteneos firmes, linireanos!",
["LEVEL_35_TITLE"] = "35. Bastión del Rey Demonio",
["LEVEL_3_HEROIC"] = "Heroic Description 3",
["LEVEL_3_HISTORY"] = "Llegamos al Corazón justo a tiempo, pero las Bestias Salvajes ya pasaron las defensas. ¡A fortificar posiciones! Hay que proteger el Corazón a toda costa o el bosque y los Arborean perecerán.",
["LEVEL_3_IRON"] = "Iron Description 3",
["LEVEL_3_IRON_UNLOCK"] = "Royal Archers\nPaladin Covenant",
["LEVEL_3_MODES_UPGRADES"] = "lvl 3 max",
["LEVEL_3_TITLE"] = "3. El Corazón del Bosque",
["LEVEL_4_HEROIC"] = "Heroic Description 4",
["LEVEL_4_HISTORY"] = "Ahora que el Corazón del Bosque está a salvo debemos reagruparnos y aprovechar nuestra ventaja. Es hora de avanzar hacia el territorio de las Bestias Salvajes. Lleva a las tropas a lo más alto del bosque y busca el campamento de las Bestias desde arriba.",
["LEVEL_4_IRON"] = "Iron Description 4",
["LEVEL_4_IRON_UNLOCK"] = "Tricannon\nArborean Emissary",
["LEVEL_4_MODES_UPGRADES"] = "lvl 4 max",
["LEVEL_4_TITLE"] = "4. Arboleda Esmeralda",
["LEVEL_5_HEROIC"] = "Heroic Description 5",
["LEVEL_5_HISTORY"] = "Gracias a tus esfuerzos en la altura de los árboles localizamos el campamento de las Bestias Salvajes en unas ruinas más allá de los límites del bosque. Lleva a las tropas hacia su territorio y ten cuidado con sus tácticas. Habremos ganado otra batalla, pero esto está lejos de terminarse.",
["LEVEL_5_IRON"] = "Iron Description 5",
["LEVEL_5_IRON_UNLOCK"] = "Arcane Wizard\nPaladin Covenant",
["LEVEL_5_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_5_TITLE"] = "5. Linde Desolado",
["LEVEL_6_HEROIC"] = "Heroic Description 6",
["LEVEL_6_HISTORY"] = "Le sacamos ventaja a las Bestias Salvajes, pero todavía tenemos que enfrentarnos a su lider, Goregrind. El autoproclamado Rey de las Bestias es un enemigo poderoso, no te dejes engañar por su comportamiento o terminarás atrevesado por sus colmillos.",
["LEVEL_6_IRON"] = "Iron Description 6",
["LEVEL_6_IRON_UNLOCK"] = "Royal Archers\nDemon Pit",
["LEVEL_6_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_6_TITLE"] = "6. Guarida de las Bestias Salvajes",
["LEVEL_7_HEROIC"] = "Heroic Description 7",
["LEVEL_7_HISTORY"] = "Siguiendo el rastro de los cultistas que ayudaron a las Bestias a destruir partes del bosque, llegamos a un lugar desolado donde sospechamos que se encuentra el Culto. Debemos tener cuidado, no sabemos bien contra qué nos enfrentamos... seguramente se guarden un par de trucos bajo sus capuchas.",
["LEVEL_7_IRON"] = "Iron Description 7",
["LEVEL_7_IRON_UNLOCK"] = "No royal archers",
["LEVEL_7_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_7_TITLE"] = "7. Valle Lúgubre",
["LEVEL_8_HEROIC"] = "Heroic Description 8",
["LEVEL_8_HISTORY"] = "A medida que nos acercamos a la base cultista, llegamos a una serie de cavernas llenas de cristales que resuenan con magia extraña. El Culto está minando estos cristales para utilizarlos como fuente de poder. No sabemos con qué fin, pero interrumpir sus actividades es una buena manera de sembrar el caos en sus filas.",
["LEVEL_8_IRON"] = "Iron Description 8",
["LEVEL_8_IRON_UNLOCK"] = "Tricannon\nPaladin Covenant",
["LEVEL_8_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_8_TITLE"] = "8. Minas Carmines",
["LEVEL_9_HEROIC"] = "Heroic Description 9",
["LEVEL_9_HISTORY"] = "Las idas y vueltas de estos túneles son enloquecedoras, pero sabemos que vamos por buen camino a medida que vemos más cultistas en actividad. A medida que avanzamos nos enfrentamos con nuevos horrores nunca vistos. ¿Qué tan profunda es esta corrupción que anida en el Culto?",
["LEVEL_9_IRON"] = "Iron Description 9",
["LEVEL_9_IRON_UNLOCK"] = "Demon Pit\nArcane Wizard",
["LEVEL_9_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_9_TITLE"] = "9. Cruce Retorcido",
["LEVEL_DEFEAT_TITLE"] = "¡DERROTA!",
["LEVEL_MODE_CAMPAIGN"] = "Campaña",
["LEVEL_MODE_HEROIC"] = "Desafío heroico",
["LEVEL_MODE_HEROIC_DESCRIPTION"] = "¡Pon a prueba tus habilidades tácticas contra una fuerza enemiga de élite en este desafío solo para los más heroicos defensores!",
["LEVEL_MODE_IRON"] = "Desafío de hierro",
["LEVEL_MODE_IRON_DESCRIPTION"] = "Una prueba para los mejores. El desafío de hierro pondrá tus habilidades al límite.",
["LEVEL_MODE_LOCKED_DESCRIPTION"] = "Completa el nivel con 3 estrellas para desbloquear este modo.",
["LEVEL_SELECT_AVAILABLE_TOWERS"] = "Torres disponibles",
["LEVEL_SELECT_CHALLENGE_ONE_ELITE_WAVE"] = "1 oleadas de élite",
["LEVEL_SELECT_CHALLENGE_ONE_LIFE"] = "1 vida en total",
["LEVEL_SELECT_CHALLENGE_RULES"] = "Reglas del desafío",
["LEVEL_SELECT_CHALLENGE_SIX_ELITE_WAVE"] = "6 oleadas de élite",
["LEVEL_SELECT_DIFFICULTY_CASUAL"] = "Casual",
["LEVEL_SELECT_DIFFICULTY_IMPOSSIBLE"] = "Imposible",
["LEVEL_SELECT_DIFFICULTY_NORMAL"] = "Normal",
["LEVEL_SELECT_DIFFICULTY_VETERAN"] = "Veterano",
["LEVEL_SELECT_GET_DLC"] = "CONSÍGUELO",
["LEVEL_SELECT_MODE_LOCKED1"] = "Modo bloqueado",
["LEVEL_SELECT_MODE_LOCKED2"] = "Desbloquea este modo al completar el nivel.",
["LEVEL_SELECT_TO_BATTLE"] = "A LA\nBATALLA",
["LV22_BOSS_BEFORE_FIGHT_EAT_01"] = "¡Sabroso! ¡Ja Ja Ja!",
["LV22_BOSS_BEFORE_FIGHT_EAT_02"] = "Odio las plantas",
["LV22_BOSS_BEFORE_FIGHT_EAT_03"] = "Eres lo que comes",
["LV22_BOSS_BEFORE_FIGHT_EAT_04"] = "Esta mordida fue refrescante",
["LV22_BOSS_BEFORE_FIGHT_EAT_05"] = "¿Ya cansado?",
["LV22_BOSS_BEFORE_FIGHT_EAT_06"] = "Nunca volveré a tener hambre",
["LV22_BOSS_BEFORE_FIGHT_EAT_07"] = "Era una gran torre, jajaja",
["LV22_BOSS_BEFORE_FIGHT_EAT_08"] = "Sabe a libertad",
["LV22_BOSS_INTRO_01"] = "Trayendo bocadillos para mi primer comida.",
["LV22_BOSS_INTRO_02"] = "Se ven... crujientes",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_01"] = "Solo probarás creepers.",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_02"] = "Los verdes son amigos, no comida",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_03"] = "¡Y no comerás nada más!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_04"] = "¡Regresa a tu prisión, monstruo!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_05"] = "¡No comerás!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_06"] = "¡Protegeré lo Verde!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_07"] = "No te reirás al final",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_08"] = "¡Se está haciendo más fuerte! ¡Ayuda!",
["LV22_MAGE_INTRO_01"] = "¡Cierra la boca!",
["LV22_MAGE_INTRO_02"] = "¡Apúrate! ¡No puedo detenerlo mucho más tiempo!",
["Level"] = "Nivel",
["Localization Manager"] = "Director de localización",
["Long"] = "Largo",
["Low"] = "Baja",
["MAGES’ GUILD"] = "GREMIO DE LOS MAGOS",
["MAGIC RESISTANT ENEMIES!"] = "¡ENEMIGOS RESISTENTES A LA MAGIA!",
["MAP_BALLON_BUY_UPGRADES_DESCRIPTION"] = "¡Usa las estrellas para mejorar tus torres y hechizos!",
["MAP_BALLON_BUY_UPGRADES_TITLE"] = "¡COMPRA MEJORAS!",
["MAP_BALLON_HERO_LEVELUP_DESCRIPTION"] = "¡Usa los puntos de héroe para entrenar a tu héroe!",
["MAP_BALLON_HERO_LEVELUP_TITLE"] = "¡TU HÉROE SUBIÓ DE NIVEL!",
["MAP_BALLON_HERO_UNLOCKED"] = "¡HÉROE DESBLOQUEADO!",
["MAP_BALLON_START_HERE"] = "¡COMIENZA AQUÍ!",
["MAP_BUTTON_ACHIEVEMENTS"] = "LOGROS",
["MAP_BUTTON_HERO_ROOM"] = "HÉROES",
["MAP_BUTTON_ITEMS"] = "OBJETOS",
["MAP_BUTTON_SHOP"] = "TIENDA",
["MAP_BUTTON_TOWER_ROOM"] = "TORRES",
["MAP_BUTTON_UPGRADES"] = "MEJORAS",
["MAP_HEROROOM_HELP1"] = "¡Selecciona y entrena habilidades!",
["MAP_HEROROOM_HELP2"] = "Toca para seleccionar",
["MAP_HEROROOM_HELP3"] = "¡Mejora el poder de tu héroe!",
["MAP_HERO_ROOM_GET_IT_NOW"] = "¡OBTENLO AHORA!",
["MAP_HERO_ROOM_SELECT"] = "EQUIPAR",
["MAP_HERO_ROOM_SELECTED"] = "EQUIPADO",
["MAP_HERO_ROOM_TRAIN"] = "ENTRENAR",
["MAP_HERO_ROOM_UNLOCK"] = "SE DESBLOQUEA EN EL NIVEL %s",
["MAP_HERO_ROOM_UNLOCK_10"] = "SE DESBLOQUEA EN EL NIVEL 10",
["MAP_HERO_ROOM_UNLOCK_14"] = "SE DESBLOQUEA EN EL NIVEL 14",
["MAP_HERO_ROOM_UNLOCK_15"] = "SE DESBLOQUEA EN EL NIVEL 15",
["MAP_HERO_ROOM_UNLOCK_4"] = "SE DESBLOQUEA EN EL NIVEL 4",
["MAP_HERO_ROOM_UNLOCK_7"] = "SE DESBLOQUEA EN EL NIVEL 7",
["MAP_HERO_ROOM_UNLOCK_9"] = "SE DESBLOQUEA EN EL NIVEL 9",
["MAP_HERO_ROOM_UNLOCK_AFTER_CAMPAIGN"] = "Se desbloquea al finalizar el juego",
["MAP_INAPPS_BUBBLE_INFO_1"] = "Juega para recopilar gemas.",
["MAP_INAPPS_BUBBLE_INFO_2"] = "¡Compra objetos especiales con las gemas!",
["MAP_INAPPS_BUBBLE_MORE_GEMS"] = "¡Necesitas más gemas!",
["MAP_INAPPS_BUBBLE_SUCCESSFUL"] = "¡Compra\ncompletada!",
["MAP_INAPP_GEMS_GEM_SHOP_TITLE"] = "TIENDA DE GEMAS",
["MAP_INAPP_GEM_PACK_1"] = "PUÑADO DE GEMAS",
["MAP_INAPP_GEM_PACK_2"] = "BOLSO DE GEMAS",
["MAP_INAPP_GEM_PACK_3"] = "TONEL DE GEMAS",
["MAP_INAPP_GEM_PACK_4"] = "COFRE DE GEMAS",
["MAP_INAPP_GEM_PACK_5"] = "CARRO DE GEMAS",
["MAP_INAPP_GEM_PACK_6"] = "MONTAÑA DE GEMAS",
["MAP_INAPP_GEM_PACK_BAG"] = "Bolsa de gemas",
["MAP_INAPP_GEM_PACK_BARREL"] = "Tonel de gemas",
["MAP_INAPP_GEM_PACK_CHEST"] = "Cofre de gemas",
["MAP_INAPP_GEM_PACK_FREE"] = "Gemas gratis",
["MAP_INAPP_GEM_PACK_HANDFUL"] = "Puñado de gemas",
["MAP_INAPP_GEM_PACK_VAULT"] = "Cámara de gemas",
["MAP_INAPP_GEM_PACK_WAGON"] = "Carro de gemas",
["MAP_INAPP_MORE_GEMS"] = "MÁS GEMAS",
["MAP_INAPP_TEXT_1"] = "Puñado de gemas",
["MAP_INAPP_TEXT_2"] = "Bolsa de gemas",
["MAP_INAPP_TEXT_3"] = "Cofre de gemas",
["MAP_INAPP_TEXT_4"] = "Gemas gratis",
["MAP_INAPP_TEXT_GEMS"] = "Gemas",
["MAP_NEW_GAMEMODE_UNLOCKED_DESCRIPTION"] = "¡Enfrenta interminables enemigos y compite por el máximo puntaje!",
["MAP_NEW_GAMEMODE_UNLOCKED_TITLE"] = "¡NUEVO DESAFÍO!",
["MAP_NEW_HERO_ALERT"] = "¡NUEVO\nHÉROE!",
["MAP_NEW_TOWER_ALERT"] = "¡NUEVA\nTORRE!",
["MAP_TOWER_ROOM_SELECT"] = "EQUIPAR",
["MAP_TOWER_ROOM_SELECTED"] = "EQUIPADO",
["MENU_HUD_WAVES"] = "%i/%i",
["MINUTES_ABBREVIATION"] = "m",
["MORE_GAMES"] = "Más juegos",
["MUSIC"] = "MÚSICA",
["Magic resistant enemies take less damage from mages."] = "Los enemigos resistentes a la magia reciben menos daño de los magos.",
["Medium"] = "Media",
["Music"] = "Música",
["NEW POWER!"] = "¡PODER NUEVO!",
["NEW SPECIAL POWER!"] = "¡PODER ESPECIAL NUEVO!",
["NEW TOWER UNLOCKED"] = "TORRE NUEVA DESBLOQUEADA",
["NEW TOWER UPGRADES"] = "MEJORAS NUEVAS PARA TORRES",
["NEW TOWERS UNLOCKED"] = "NUEVAS TORRES DESBLOQUEADAS",
["NEWS"] = "NOTICIAS",
["NEW_ENEMY_ALERT_ICON"] = "ENEMIGO NUEVO",
["NO HEROES"] = "SIN HÉROES",
["NOTIFICATION_NEW_ENEMY_TITLE"] = "ENEMIGO NUEVO",
["NOTIFICATION_NEW_SPECIAL_TITLE"] = "¡PODER NUEVO!",
["NOTIFICATION_NEW_TOWERS_SUB_DESCRIPTION"] = "Ahora puedes subir tus torres a nivel %d.",
["NOTIFICATION_NEW_TOWERS_SUB_TITLE"] = "TORRES DE NIVEL %d DISPONIBLES",
["NOTIFICATION_NEW_TOWERS_TITLE"] = "NUEVAS MEJORAS DE TORRE",
["NOTIFICATION_NEW_TOWER_TITLE"] = "NUEVAS TORRES DESBLOQUEADAS",
["NOTIFICATION_armored_enemies_desc_body_1"] = "Hay enemigos que usan armaduras de diferente resistencia que los protegen de los ataques no mágicos.",
["NOTIFICATION_armored_enemies_desc_body_2"] = "Resiste daño de",
["NOTIFICATION_armored_enemies_desc_body_3"] = "Los enemigos con armadura reciben menos daño de los arqueros, soldados y unidades no mágicas.",
["NOTIFICATION_armored_enemies_desc_title"] = "¡Enemigos con armadura!",
["NOTIFICATION_armored_enemies_enemy_name"] = "Peleador Colmilludo",
["NOTIFICATION_bottom_info_desc_body"] = "Puedes revisar la información del enemigo en cualquier momento haciendo clic en una unidad y su retrato.",
["NOTIFICATION_bottom_info_desc_title"] = "INFORMACIÓN DE ENEMIGOS",
["NOTIFICATION_bottom_info_tap_portrait_desc"] = "Click aquí para reabrir.",
["NOTIFICATION_button_ok"] = "Ok",
["NOTIFICATION_glare_desc_body"] = "El Omnividente posa su mirada sobre el campo de batalla, haciendo que los enemigos cercanos sean más poderosos gracias a su Resplandor corrupto.",
["NOTIFICATION_glare_desc_bullets"] = "- Cura a los enemigos que están en el área\n- Activa habilidades únicas de los enemigos",
["NOTIFICATION_glare_desc_title"] = "Resplandor del Omnividente",
["NOTIFICATION_hero_desc"] = "Muestra nivel, vida y experiencia.",
["NOTIFICATION_hero_desc_baloon_1"] = "Haz clic en el retrato o en la unidad del héroe para seleccionarlo. También puedes oprimir la barra espaciadora.",
["NOTIFICATION_hero_desc_baloon_2"] = "Haz CLIC en el sendero para mover al Héroe.",
["NOTIFICATION_hero_desc_body_1"] = "Los héroes son unidades de elite que pueden enfrentar a enemigos fuertes y ayudar a tus tropas.",
["NOTIFICATION_hero_desc_body_2"] = "Los héroes ganan experiencia cada vez que atacan a un enemigo o usan una habilidad.",
["NOTIFICATION_hero_desc_title"] = "¡Héroe a tus órdenes!",
["NOTIFICATION_magic_resistant_enemies_desc_body_1"] = "Hay enemigos que tienen distintos niveles de resistencia mágica que los protegen de los ataques mágicos.",
["NOTIFICATION_magic_resistant_enemies_desc_body_2"] = "Resiste daño de",
["NOTIFICATION_magic_resistant_enemies_desc_body_3"] = "Los enemigos resistentes a la magia reciben menos daño de los magos.",
["NOTIFICATION_magic_resistant_enemies_desc_title"] = "¡Enemigos con resistencia mágica!",
["NOTIFICATION_magic_resistant_enemies_enemy_name"] = "Tortuga Chamán",
["NOTIFICATION_rally_point_desc_body_1"] = "Puedes cambiar el punto de encuentro de tus barracas para que tus unidades defiendan un área diferente.",
["NOTIFICATION_rally_point_desc_body_2"] = "Selecciona el controlador del punto de encuentro.",
["NOTIFICATION_rally_point_desc_body_3"] = "¡Selecciona el lugar donde quieres mover tus tropas!",
["NOTIFICATION_rally_point_desc_subtitle"] = "Punto de Encuentro",
["NOTIFICATION_rally_point_desc_title"] = "¡Dirige a tus tropas!",
["NOTIFICATION_special_desc_body"] = "Puedes invocar tropas para que te ayuden en la batalla.",
["NOTIFICATION_special_desc_bullets"] = "Los refuerzos son muy buenos para demorar enemigos.",
["NOTIFICATION_special_desc_title"] = "Llamar Refuerzos",
["NOTIFICATION_title_enemy"] = "Info. Enemigo",
["NOTIFICATION_title_glare"] = "NUEVO CONSEJO",
["NOTIFICATION_title_hint"] = "HÉROE DESBLOQUEADO",
["NOTIFICATION_title_new_tip"] = "CONSEJO NUEVO",
["NOTIFICATION_title_special"] = "ESPECIAL DESBLOQUEADO",
["Next!"] = "¡Siguiente!",
["No"] = "No",
["None"] = "Ninguna",
["Nope"] = "¡Nop!",
["Normal"] = "Normal",
["OFFER_GET_IT_NOW"] = "OBTENLO AHORA",
["OFFER_GET_THEM_NOW"] = "OBTENLOS AHORA",
["OFFER_OFF"] = "EN DESCUENTO",
["OFFER_REGULAR"] = "PRECIO NORMAL",
["OK!"] = "¡BIEN!",
["ONE_TIME_OFFER"] = "¡OFERTA ÚNICA!",
["OPTIONS"] = "OPCIONES",
["OPTIONS_PAGE_CONTROLS"] = "CONTROLES",
["OPTIONS_PAGE_HELP"] = "AYUDA",
["OPTIONS_PAGE_SHORTCUTS"] = "AYUDA DEL TECLADO",
["OPTIONS_PAGE_VIDEO"] = "VIDEO",
["Objective"] = "Objetivo",
["Options"] = "Opciones",
["Over 50 stars are recommended to face this stage."] = "Se recomienda haber obtenido más de 50 estrellas antes de afrontar este nivel.",
["POPUP_CLEAR_PROGRESS_CONFIRM"] = "¿ESTÁS SEGURO DE QUERER BORRAR TU PROGRESO?",
["POPUP_LABEL_MAIN_MENU"] = "Menú Principal",
["POPUP_SETTINGS_LANGUAGE"] = "Idioma",
["POPUP_SETTINGS_MUSIC"] = "MÚSICA",
["POPUP_SETTINGS_SFX"] = "Sonidos",
["POPUP_label_error_msg"] = "¡Ups! Algo salió mal.",
["POPUP_label_error_msg2"] = "¡Ups! Algo salió mal.",
["POPUP_label_purchasing"] = "PROCESANDO SOLICITUD",
["POPUP_label_title_options"] = "Opciones",
["POPUP_label_version"] = "Version 0.0.9",
["POWER_SUMMON_DESCRIPTION"] = "Puedes convocar tropas para que te ayuden en la batalla.",
["POWER_SUMMON_LARGE_DESCRIPTION"] = "Puedes convocar tropas para que te ayuden en la batalla.\n\nLos refuerzos son gratis y los puedes pedir cada 15 segundos.",
["POWER_SUMMON_NAME"] = "Pedir refuerzos",
["PRICE_FREE"] = "Gratis",
["PRIVACY_POLICY_ASK_AGE"] = "¿Cuándo naciste?",
["PRIVACY_POLICY_BUTTON_LINK"] = "Política de privacidad",
["PRIVACY_POLICY_CONSENT_SHORT"] = "Antes de jugar nuestro juego, por favor confirma (y tus padres o tutores si eres un niño o adolescente) que has leído y aceptado nuestra política de privacidad.",
["PRIVACY_POLICY_LINK"] = "Política de privacidad",
["PRIVACY_POLICY_WELCOME"] = "¡Bienvenido!",
["PROCESSING YOUR REQUEST"] = "PROCESANDO SOLICITUD",
["Produced by %s"] = "Producido por %s",
["QUIT"] = "Salir",
["Quit"] = "Salir",
["RESTORE_PURCHASES"] = "Rest. compras",
["Reset"] = "Restaurar",
["Restart"] = "Reiniciar",
["Resume"] = "Reanudar",
["SECONDS_ABBREVIATION"] = "s",
["SETTINGS_DISPLAY"] = "Pantalla",
["SETTINGS_FRAMES_PER_SECOND"] = "FPS",
["SETTINGS_FULLSCREEN"] = "Pantalla completa",
["SETTINGS_FULLSCREEN_BORDERLESS"] = "Sin bordes",
["SETTINGS_IMAGE_QUALITY"] = "Calidad de la imagen",
["SETTINGS_LANGUAGE"] = "Lenguaje",
["SETTINGS_LARGE_MOUSE_POINTER"] = "Gran puntero del mouse",
["SETTINGS_LOW_IMAGE_QUALITY_LINK"] = "¿Baja calidad de imagen? Haga clic aquí.",
["SETTINGS_RETINA_DISPLAY"] = "Retina display (Mac)",
["SETTINGS_SCREEN_RESOLUTION"] = "Resolución de la pantalla",
["SETTINGS_SUPPORT"] = "Soporte",
["SETTINGS_VSYNC"] = "Vsync",
["SFX"] = "Sonidos",
["SHOP_DESKTOP_GET_DLC_BUTTON"] = "CONSÍGUELO",
["SHOP_DESKTOP_TITLE"] = "TIENDA",
["SHOP_ROOM_BEST_VALUE_TITLE"] = "MEJOR\nPRECIO",
["SHOP_ROOM_DLC_1_DESCRIPTION"] = "¡EMBÁRCATE EN ESTA NUEVA ÉPICA AVENTURA!",
["SHOP_ROOM_DLC_1_TITLE"] = "CAMPAÑA DE AMENAZA COLOSAL",
["SHOP_ROOM_DLC_1_TOOLTIP_DESCRIPTION"] = "5 Nuevas Pantallas\nNueva Torre\nNuevo Héroe\nMás de 10 nuevos enemigos\n2 Mini Boss Fights\nUn Épico Jefe Final\n Y más...",
["SHOP_ROOM_DLC_1_TOOLTIP_TITLE"] = "CAMPAÑA DE AMENAZA COLOSAL",
["SHOP_ROOM_DLC_2_DESCRIPTION"] = "EMPRENDE ESTA NUEVA AVENTURA ÉPICA",
["SHOP_ROOM_DLC_2_TITLE"] = "CAMPAÑA DEL VIAJE DE WUKONG",
["SHOP_ROOM_MOST_POPULAR_TITLE"] = "MÁS\nPOPULAR",
["SLOT_CLOUD_DOWNLOADING"] = "Descargando...",
["SLOT_CLOUD_DOWNLOAD_FAILED"] = "Error al descargar partida guardada de iCloud. Inténtalo más tarde.",
["SLOT_CLOUD_DOWNLOAD_SUCCESSFUL"] = "Descarga exitosa.",
["SLOT_CLOUD_UPLOADING"] = "Subiendo...",
["SLOT_CLOUD_UPLOAD_FAILED"] = "Error al subir partida guardada a iCloud. Inténtalo más tarde.",
["SLOT_CLOUD_UPLOAD_ICLOUD_NOT_CONFIGURED"] = "iCloud no está configurado en el dispositivo.",
["SLOT_CLOUD_UPLOAD_SUCCESSFUL"] = "Subida exitosa.",
["SLOT_DELETE_SLOT"] = "¿Borrar la partida?",
["SLOT_NAME"] = "Partida",
["SLOT_NEW_GAME"] = "JUEGO NUEVO",
["SOLDIER_ARBOREAN_BARRACK_NAME"] = "Soldado Arbóreo",
["SOLDIER_ARBOREAN_SENTINELS_1_NAME"] = "Baluu",
["SOLDIER_ARBOREAN_SENTINELS_2_NAME"] = "Vylla",
["SOLDIER_ARBOREAN_SENTINELS_3_NAME"] = "Ykkon",
["SOLDIER_ARBOREAN_SENTINELS_4_NAME"] = "Haavi",
["SOLDIER_ARBOREAN_SENTINELS_5_NAME"] = "Plook",
["SOLDIER_ARBOREAN_SENTINELS_6_NAME"] = "Guldd",
["SOLDIER_ARBOREAN_SENTINELS_7_NAME"] = "Teena",
["SOLDIER_ARBOREAN_SENTINELS_8_NAME"] = "Uuzky",
["SOLDIER_ARBOREAN_SENTINELS_9_NAME"] = "Deluu",
["SOLDIER_DRAGON_BONE_ULTIMATE_DOG_NAME"] = "Draco Huesudo",
["SOLDIER_EARTH_HOLDER_NAME"] = "Guerrero de Piedra",
["SOLDIER_GHOST_TOWER_NAME"] = "Espectro",
["SOLDIER_HERO_BUILDER_WORKER_1_NAME"] = "Hemmar",
["SOLDIER_HERO_BUILDER_WORKER_2_NAME"] = "O'Tool",
["SOLDIER_HERO_BUILDER_WORKER_3_NAME"] = "Crews",
["SOLDIER_HERO_BUILDER_WORKER_4_NAME"] = "Birck",
["SOLDIER_HERO_BUILDER_WORKER_5_NAME"] = "Lauck",
["SOLDIER_HERO_BUILDER_WORKER_6_NAME"] = "O'Nail",
["SOLDIER_HERO_BUILDER_WORKER_7_NAME"] = "Hovels",
["SOLDIER_HERO_BUILDER_WORKER_8_NAME"] = "Woody",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL1_NAME"] = "Guardián Arbóreo",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL2_NAME"] = "Guardián Arbóreo",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL3_NAME"] = "Guardián Arbóreo",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL1_NAME"] = "Paragón Arbóreo",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL2_NAME"] = "Paragón Arbóreo",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL3_NAME"] = "Paragón Arbóreo",
["SOLDIER_HERO_SPIDER_ULTIMATE_NAME"] = "Arañita",
["SOLDIER_HERO_WITCH_CAT_1_NAME"] = "Conan",
["SOLDIER_HERO_WITCH_CAT_2_NAME"] = "Alfajor",
["SOLDIER_HERO_WITCH_CAT_3_NAME"] = "Babieca",
["SOLDIER_HERO_WITCH_CAT_4_NAME"] = "Peluche",
["SOLDIER_HERO_WITCH_CAT_5_NAME"] = "Pipa",
["SOLDIER_HERO_WITCH_CAT_6_NAME"] = "Watson",
["SOLDIER_HERO_WITCH_CAT_7_NAME"] = "Chimi",
["SOLDIER_HERO_WITCH_CAT_8_NAME"] = "Pantufla",
["SOLDIER_HERO_WITCH_DECOY_NAME"] = "Trapito",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_1_NAME"] = "San Wikung",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_2_NAME"] = "Son Wokeng",
["SOLDIER_ITEM_SUMMON_BLACKBURN_NAME"] = "Lord Blackburn",
["SOLDIER_PALADINS_10_NAME"] = "Sir Joacim",
["SOLDIER_PALADINS_11_NAME"] = "Sir Andre",
["SOLDIER_PALADINS_12_NAME"] = "Sir Sammet",
["SOLDIER_PALADINS_13_NAME"] = "Sir Udo",
["SOLDIER_PALADINS_14_NAME"] = "Sir Eric",
["SOLDIER_PALADINS_15_NAME"] = "Sir Bruce",
["SOLDIER_PALADINS_16_NAME"] = "Sir Rob",
["SOLDIER_PALADINS_17_NAME"] = "Sir Biff",
["SOLDIER_PALADINS_18_NAME"] = "Sir Bowes",
["SOLDIER_PALADINS_1_NAME"] = "Sir Kai",
["SOLDIER_PALADINS_2_NAME"] = "Sir Hansi",
["SOLDIER_PALADINS_3_NAME"] = "Sir Luca",
["SOLDIER_PALADINS_4_NAME"] = "Sir Timo",
["SOLDIER_PALADINS_5_NAME"] = "Sir Ralf",
["SOLDIER_PALADINS_6_NAME"] = "Sir Tobias",
["SOLDIER_PALADINS_7_NAME"] = "Sir Deris",
["SOLDIER_PALADINS_8_NAME"] = "Sir Kiske",
["SOLDIER_PALADINS_9_NAME"] = "Sir Pesch",
["SOLDIER_PRIESTS_BARRACK_1_NAME"] = "Willy",
["SOLDIER_PRIESTS_BARRACK_2_NAME"] = "Henry",
["SOLDIER_PRIESTS_BARRACK_3_NAME"] = "Geoffrey",
["SOLDIER_PRIESTS_BARRACK_4_NAME"] = "Nicholas",
["SOLDIER_PRIESTS_BARRACK_5_NAME"] = "Ed",
["SOLDIER_PRIESTS_BARRACK_6_NAME"] = "Hob",
["SOLDIER_PRIESTS_BARRACK_7_NAME"] = "Odo",
["SOLDIER_PRIESTS_BARRACK_8_NAME"] = "Cedric",
["SOLDIER_PRIESTS_BARRACK_9_NAME"] = "Hal",
["SOLDIER_RANDOM_10_NAME"] = "Alvus",
["SOLDIER_RANDOM_11_NAME"] = "Borin",
["SOLDIER_RANDOM_12_NAME"] = "Hadrian",
["SOLDIER_RANDOM_13_NAME"] = "Thomas",
["SOLDIER_RANDOM_14_NAME"] = "Henry",
["SOLDIER_RANDOM_15_NAME"] = "Bryce",
["SOLDIER_RANDOM_16_NAME"] = "Rulf",
["SOLDIER_RANDOM_17_NAME"] = "Allister",
["SOLDIER_RANDOM_18_NAME"] = "Altair",
["SOLDIER_RANDOM_19_NAME"] = "Simon",
["SOLDIER_RANDOM_1_NAME"] = "Douglas",
["SOLDIER_RANDOM_20_NAME"] = "Egbert",
["SOLDIER_RANDOM_21_NAME"] = "Eldon",
["SOLDIER_RANDOM_22_NAME"] = "Garrett",
["SOLDIER_RANDOM_23_NAME"] = "Godwin",
["SOLDIER_RANDOM_24_NAME"] = "Gordon",
["SOLDIER_RANDOM_25_NAME"] = "Jerald",
["SOLDIER_RANDOM_26_NAME"] = "Kelvin",
["SOLDIER_RANDOM_27_NAME"] = "Lando",
["SOLDIER_RANDOM_28_NAME"] = "Maddox",
["SOLDIER_RANDOM_29_NAME"] = "Peyton",
["SOLDIER_RANDOM_2_NAME"] = "Dan McKill",
["SOLDIER_RANDOM_30_NAME"] = "Ramsey",
["SOLDIER_RANDOM_31_NAME"] = "Raymond",
["SOLDIER_RANDOM_32_NAME"] = "Robert",
["SOLDIER_RANDOM_33_NAME"] = "Sawyer",
["SOLDIER_RANDOM_34_NAME"] = "Silas",
["SOLDIER_RANDOM_35_NAME"] = "Stuart",
["SOLDIER_RANDOM_36_NAME"] = "Tanner",
["SOLDIER_RANDOM_37_NAME"] = "Usher",
["SOLDIER_RANDOM_38_NAME"] = "Wallace",
["SOLDIER_RANDOM_39_NAME"] = "Wesley",
["SOLDIER_RANDOM_3_NAME"] = "James Lee",
["SOLDIER_RANDOM_40_NAME"] = "Willard",
["SOLDIER_RANDOM_4_NAME"] = "Jar Johson",
["SOLDIER_RANDOM_5_NAME"] = "Phil",
["SOLDIER_RANDOM_6_NAME"] = "Robin",
["SOLDIER_RANDOM_7_NAME"] = "William",
["SOLDIER_RANDOM_8_NAME"] = "Martin",
["SOLDIER_RANDOM_9_NAME"] = "Arthur",
["SOLDIER_REINFORCEMENTS_F_1_NAME"] = "Ataina",
["SOLDIER_REINFORCEMENTS_F_2_NAME"] = "Maucil",
["SOLDIER_REINFORCEMENTS_F_3_NAME"] = "Gulica",
["SOLDIER_REINFORCEMENTS_F_4_NAME"] = "Rogas",
["SOLDIER_REINFORCEMENTS_M_10_NAME"] = "Podgie",
["SOLDIER_REINFORCEMENTS_M_1_NAME"] = "Gabini",
["SOLDIER_REINFORCEMENTS_M_2_NAME"] = "O'Bell",
["SOLDIER_REINFORCEMENTS_M_3_NAME"] = "Knet",
["SOLDIER_REINFORCEMENTS_M_4_NAME"] = "Jendars",
["SOLDIER_REINFORCEMENTS_M_5_NAME"] = "Jarlosc",
["SOLDIER_REINFORCEMENTS_M_6_NAME"] = "Astong",
["SOLDIER_REINFORCEMENTS_M_7_NAME"] = "Buigell",
["SOLDIER_REINFORCEMENTS_M_8_NAME"] = "Clane",
["SOLDIER_REINFORCEMENTS_M_9_NAME"] = "Magus",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_1_NAME"] = "Dench",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_2_NAME"] = "Smith",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_3_NAME"] = "Andrews",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_4_NAME"] = "Thompson",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_5_NAME"] = "Taylor",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_1_NAME"] = "McCartney",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_2_NAME"] = "McKellen",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_3_NAME"] = "Hopkins",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_4_NAME"] = "Caine",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_5_NAME"] = "Kingsley",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_10_NAME"] = "Viper",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_1_NAME"] = "Fang",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_2_NAME"] = "Blade",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_3_NAME"] = "Claw",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_4_NAME"] = "Talon",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_5_NAME"] = "Edgee",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_6_NAME"] = "Shiv",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_7_NAME"] = "Scythe",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_8_NAME"] = "Dagger",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_9_NAME"] = "Sting",
["SOLDIER_REINFORCEMENTS_SPECIAL_DARK_ARMY_1_NAME"] = "Llamacuervos Sombrío",
["SOLDIER_REINFORCEMENTS_SPECIAL_LINIREA_1_NAME"] = "Caballero Ejemplar",
["SOLDIER_STAGE_10_YMCA_BIKER_NAME"] = "Glenn",
["SOLDIER_STAGE_10_YMCA_CONSTRUCTOR_NAME"] = "David",
["SOLDIER_STAGE_10_YMCA_INDIO_NAME"] = "Felipe",
["SOLDIER_STAGE_10_YMCA_POLICIA_NAME"] = "Victor",
["SOLDIER_STAGE_15_DENAS_NAME"] = "Rey Denas",
["SOLDIER_TOWER_DARK_ELF_1_NAME"] = "Filraen",
["SOLDIER_TOWER_DARK_ELF_2_NAME"] = "Faeryl",
["SOLDIER_TOWER_DARK_ELF_3_NAME"] = "Gurina",
["SOLDIER_TOWER_DARK_ELF_4_NAME"] = "Jhalass",
["SOLDIER_TOWER_DARK_ELF_5_NAME"] = "Solenzar",
["SOLDIER_TOWER_DARK_ELF_6_NAME"] = "Tebryn",
["SOLDIER_TOWER_DARK_ELF_7_NAME"] = "Vierna",
["SOLDIER_TOWER_DARK_ELF_8_NAME"] = "Zyn",
["SOLDIER_TOWER_DARK_ELF_9_NAME"] = "Elerra",
["SOLDIER_TOWER_DWARF_10_NAME"] = "Babbi",
["SOLDIER_TOWER_DWARF_1_NAME"] = "Pippi",
["SOLDIER_TOWER_DWARF_2_NAME"] = "Ginni",
["SOLDIER_TOWER_DWARF_3_NAME"] = "Merri",
["SOLDIER_TOWER_DWARF_4_NAME"] = "Lorri",
["SOLDIER_TOWER_DWARF_5_NAME"] = "Talli",
["SOLDIER_TOWER_DWARF_6_NAME"] = "Danni",
["SOLDIER_TOWER_DWARF_7_NAME"] = "Getti",
["SOLDIER_TOWER_DWARF_8_NAME"] = "Daffi",
["SOLDIER_TOWER_DWARF_9_NAME"] = "Bibbi",
["SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "Elandil",
["SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "Puck",
["SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "Thas",
["SOLDIER_TOWER_ELVEN_BARRACK_4_NAME"] = "Kastore",
["SOLDIER_TOWER_ELVEN_BARRACK_5_NAME"] = "Elric",
["SOLDIER_TOWER_ELVEN_BARRACK_6_NAME"] = "Elaith",
["SOLDIER_TOWER_NECROMANCER_SKELETON_GOLEM_NAME"] = "Golem de huesos",
["SOLDIER_TOWER_NECROMANCER_SKELETON_NAME"] = "Esqueleto",
["SOLDIER_TOWER_PANDAS_FEMALE_1_NAME"] = "Yan",
["SOLDIER_TOWER_PANDAS_FEMALE_2_NAME"] = "Qingzhao",
["SOLDIER_TOWER_PANDAS_FEMALE_3_NAME"] = "Hui",
["SOLDIER_TOWER_PANDAS_FEMALE_4_NAME"] = "Ailing",
["SOLDIER_TOWER_PANDAS_MALE_1_NAME"] = "Tzu",
["SOLDIER_TOWER_PANDAS_MALE_2_NAME"] = "Qian",
["SOLDIER_TOWER_PANDAS_MALE_3_NAME"] = "Xueqin",
["SOLDIER_TOWER_PANDAS_MALE_4_NAME"] = "Nai'an",
["SOLDIER_TOWER_PANDAS_MALE_5_NAME"] = "Xun",
["SOLDIER_TOWER_PANDAS_MALE_6_NAME"] = "Xingjian",
["SOLDIER_TOWER_PANDAS_MALE_7_NAME"] = "Wei",
["SOLDIER_TOWER_PANDAS_MALE_8_NAME"] = "Chen",
["SOLDIER_TOWER_ROCKET_GUNNERS_10_NAME"] = "Fortus",
["SOLDIER_TOWER_ROCKET_GUNNERS_1_NAME"] = "Axl",
["SOLDIER_TOWER_ROCKET_GUNNERS_2_NAME"] = "Rose",
["SOLDIER_TOWER_ROCKET_GUNNERS_3_NAME"] = "Slash",
["SOLDIER_TOWER_ROCKET_GUNNERS_4_NAME"] = "Hudson",
["SOLDIER_TOWER_ROCKET_GUNNERS_5_NAME"] = "Izzy",
["SOLDIER_TOWER_ROCKET_GUNNERS_6_NAME"] = "Duff",
["SOLDIER_TOWER_ROCKET_GUNNERS_7_NAME"] = "Adler",
["SOLDIER_TOWER_ROCKET_GUNNERS_8_NAME"] = "Dizzy",
["SOLDIER_TOWER_ROCKET_GUNNERS_9_NAME"] = "Ferrer",
["SOLDIER_ZHU_APPRENTICE_NAME"] = "Zhu Bajie",
["SPECIAL_ARBOREAN_BARRACK_DESCRIPTION"] = "Invoca a 3 soldados arbóreos que luchan contra enemigos en su camino.",
["SPECIAL_ARBOREAN_BARRACK_NAME"] = "Ciudadanos Arbóreos",
["SPECIAL_ARBOREAN_HONEY_DESCRIPTION"] = "El apicultor toma su puesto, mandando a sus abejas a ralentizar y dañar enemigos con miel pegajosa!",
["SPECIAL_ARBOREAN_HONEY_NAME"] = "Apicultor Arbóreo",
["SPECIAL_ARBOREAN_OLDTREE_DESCRIPTION"] = "El tipo gruñón desata un enorme tronco rodante que aplasta a los enemigos en su camino.",
["SPECIAL_ARBOREAN_OLDTREE_NAME"] = "Árbol Viejo",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_DESCRIPTION"] = "Ágiles protectoras del bosque.",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_NAME"] = "Lanzaespinosa Arborean",
["SPECIAL_PRIESTS_SOLDIERS_DESCRIPTION"] = "Cultistas redimidos que se convierten en abominaciones al morir.",
["SPECIAL_PRIESTS_SOLDIERS_NAME"] = "Cultistas Cegados",
["SPECIAL_REPAIR_HOLDER_DRAGON_DESCRIPTION"] = "Apaga las llamas para liberar la torre al instante.",
["SPECIAL_REPAIR_HOLDER_DRAGON_NAME"] = "Envuelto en llamas",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_DESCRIPTION"] = "Aumenta la salud de las unidades de la torre.\nGenera periódicamente 2 Guerreros de Piedra.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_NAME"] = "Elemental Holder: Earth",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_DESCRIPTION"] = "Aumenta el daño de la torre construida.\nOcasionalmente elimina a un enemigo al instante.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_NAME"] = "Elemental Holder: Fire",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_DESCRIPTION"] = "Reduce el costo de construcción.\nGenera oro de los enemigos.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_NAME"] = "Elemental Holder: Metal",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_DESCRIPTION"] = "Cura constantemente a las unidades aliadas cercanas.\nTeletransporta a los enemigos hacia atrás a lo largo del camino.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_NAME"] = "Elemental Holder: Water",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_DESCRIPTION"] = "Aumenta el alcance de la torre construida.\nOcasionalmente genera raíces que persisten brevemente y ralentizan a los enemigos.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_NAME"] = "Elemental Holder: Wood",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_DESCRIPTION"] = "Limpia los escombros para desbloquear este punto estratégico.",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_NAME"] = "Escombros",
["SPECIAL_REPAIR_HOLDER_SPIDERS_DESCRIPTION"] = "Libera al portador de las telarañas para habilitar este punto estratégico.",
["SPECIAL_REPAIR_HOLDER_SPIDERS_NAME"] = "Portador Enredado",
["SPECIAL_REPAIR_OVERSEER_DESCRIPTION"] = "Repele a los tentáculos para desbloquear esta posición estratégica.",
["SPECIAL_REPAIR_OVERSEER_NAME"] = "Tentáculos",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_DESCRIPTION"] = "Contrata un Elfo Mercenario para ayudar en la batalla. Revive cada 10 segundos.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "Elfos Mercenarios I",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_DESCRIPTION"] = "Contrata hasta 2 Elfos Mercenarios para ayudar en la batalla. Reviven cada 10 segundos.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "Elfos Mercenarios II",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_DESCRIPTION"] = "Contrata hasta 3 Elfos Mercenarios para ayudar en la batalla. Reviven cada 10 segundos.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "Elfos Mercenarios III",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_1"] = "Lanza proyectiles mágicos que destruyen a las ilusiones de Mydrias y previenen que pueda crear más por unos segundos.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_2"] = "Invoca 2 Guardias Demonios que van por el camino luchando contra enemigos.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_3"] = "Atrapa a Denas, previniendo que pueda moverse o atacar.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_1"] = "Impacto de Almas",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_2"] = "Engendros Infernales",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_3"] = "Grilletes Mágicos",
["START"] = "COMENZAR",
["START BATTLE!"] = "¡COMENZAR BATALLA!",
["START HERE!"] = "¡COMIENZA AQUÍ!",
["STRATEGY BASICS!"] = "¡CONSEJOS BÁSICOS!",
["Select"] = "Seleccionar",
["Select and train abilities"] = "Selecciona y entrena habilidades",
["Select by clicking on the portrait or hero unit. Hotkey: space bar"] = "Haz clic en el retrato o en la unidad del héroe para seleccionarlo. También puedes oprimir la barra espaciadora.",
["Select hero"] = "Seleccionar héroe",
["Selected"] = "Seleccionado",
["Sell Tower"] = "Vender torre",
["Sell this tower and get a %s GP refund."] = "Vende esta torre y obtén %s piezas de oro como reembolso.",
["Short"] = "Corto",
["Shows level, health and experience."] = "Muestra el nivel, la vitalidad y la experiencia.",
["Skills"] = "Habilidades",
["Skip this!"] = "¡Saltear esto!",
["Slow"] = "Baja",
["Special abilities"] = "Habilidades especiales",
["Support your soldiers with ranged towers!"] = "¡Brinda apoyo a tus soldados con torres de ataque a distancia!",
["Survival mode!"] = "¡Modo Supervivencia!",
["TAP_TO_START"] = "Toca para iniciar",
["TAUNT_BOSS_PIG_FROM_POOL_0001"] = "¡Voy a hacerlos chillar!",
["TAUNT_BOSS_PIG_FROM_POOL_0002"] = "Di 'tocino' de vuelta. ¡Te reto!",
["TAUNT_BOSS_PIG_FROM_POOL_0003"] = "¡Los humanos están de vuelta en el menú, muchachos!",
["TAUNT_BOSS_PIG_FROM_POOL_0004"] = "¡Apresúrate! Tengo hambre.",
["TAUNT_BOSS_PIG_FROM_POOL_0005"] = "Voy a disfrutar verte morir.",
["TAUNT_BOSS_PIG_FROM_POOL_0006"] = "Lo sé, soy malo.",
["TAUNT_LVL30_BOSS_ABILITY_01"] = "¡Dense un festín, mis hijos!",
["TAUNT_LVL30_BOSS_ABILITY_02"] = "¡No se cuelguen! ¡MWAHAHAHA!",
["TAUNT_LVL30_BOSS_ABILITY_03"] = "¡Por el Culto!",
["TAUNT_LVL30_BOSS_ABILITY_04"] = "¡Comidas deliciosas para todos!",
["TAUNT_LVL30_BOSS_ABILITY_05"] = "¡Mi sentido arácnido está enloqueciendo!",
["TAUNT_LVL30_BOSS_ABILITY_06"] = "¡Arrodíllate ante mí, Alianza!",
["TAUNT_LVL30_BOSS_ABILITY_07"] = "¡Mi casa, mis reglas!",
["TAUNT_LVL30_BOSS_ABILITY_08"] = "¡Nadie escapa de mi red!",
["TAUNT_LVL30_BOSS_ABILITY_09"] = "¡Muere, plaga humanoide!",
["TAUNT_LVL30_BOSS_ABILITY_10"] = "¡Manejo sus hilos!",
["TAUNT_LVL30_BOSS_ABILITY_11"] = "¡Mátenlos a todos!",
["TAUNT_LVL30_BOSS_INTRO_01"] = "Finalmente! Los asesinos de mis hermanas han venido...",
["TAUNT_LVL30_BOSS_INTRO_02"] = "Vengaré a mis hermanas Sarelgaz y Mactans...",
["TAUNT_LVL30_BOSS_INTRO_03"] = "¡Y me convertiré en una diosa, todo en el mismo día!",
["TAUNT_LVL30_BOSS_PREFIGHT_01"] = "Ya basta...",
["TAUNT_LVL30_BOSS_PREFIGHT_02"] = "No son más que insectos insignificantes...",
["TAUNT_LVL30_BOSS_PREFIGHT_03"] = "¡Atrapados en la telaraña de la Reina!",
["TAUNT_LVL32_BOSS_ABILITY_01"] = "¡Necios! ¡Yo controlo la llama divina, el Fuego Samadhi!",
["TAUNT_LVL32_BOSS_ABILITY_02"] = "¡Llamas abrasadoras surgen de los cielos!",
["TAUNT_LVL32_BOSS_ABILITY_03"] = "¡Temed al fuego verdadero en su forma más pura!",
["TAUNT_LVL32_BOSS_ABILITY_04"] = "¡La carne y las almas arden por igual!",
["TAUNT_LVL32_BOSS_FIGHT_01"] = "¡El fuego en mí nunca morirá!",
["TAUNT_LVL32_BOSS_FINAL_01"] = "Mi llama se está apagando...\npero todavía tengo mi dragón...",
["TAUNT_LVL32_BOSS_INTRO_01"] = "¿Así que tienes un ejército?",
["TAUNT_LVL32_BOSS_INTRO_02"] = "¡Yo tengo un dragón! ¡Ja ja ja ja!",
["TAUNT_LVL32_BOSS_PREFIGHT_01"] = "¡Basta! ¡Aquí es donde yo gano!",
["TAUNT_LVL32_BOSS_PREFIGHT_02"] = "¡Admiren mi verdadera forma!",
["TAUNT_LVL34_BOSS_BOSSFIGHT_01"] = "Está bien entonces, sé exactamente lo que necesitamos. Más de mí. Yo, yo, yo...",
["TAUNT_LVL34_BOSS_DEATH_01"] = "Esto no puede ser... No importa, mi esposo les hará pagar...",
["TAUNT_LVL34_BOSS_INTRO_01"] = "¡Malditos monos! ¿Se atreven a venir aquí después de lo que le hicieron a mi hijo?",
["TAUNT_LVL34_BOSS_WAVES_01"] = "¡Prueben mi poder, necios insolentes!",
["TAUNT_LVL34_BOSS_WAVES_02"] = "¡El fin está cerca!",
["TAUNT_STAGE02_RAELYN_0001"] = "Hagámoslo.",
["TAUNT_STAGE02_VEZNAN_0001"] = "Aquí vienen. Ayudaré a tus débiles huestes...",
["TAUNT_STAGE02_VEZNAN_0002"] = "...más bien, uno de mis mejores soldados lo hará. ¡JA!",
["TAUNT_STAGE02_VEZNAN_0003"] = "¡JA JA JA!",
["TAUNT_STAGE06_BOSS_PIG_PREBATTLE_0001"] = "Muy bien... lo haré yo mismo.",
["TAUNT_STAGE06_BOSS_PIG_RESPONSE_0001"] = "Relájate, todo está bajo control.",
["TAUNT_STAGE06_CULTIST_GREETING_0001"] = "Veo que estás muy cómodo...",
["TAUNT_STAGE06_CULTIST_GREETING_0002"] = "...más te vale cumplir con tu parte del trato.",
["TAUNT_STAGE11_CULTIST_LEADER_0001"] = "Te felicito por llegar tan lejos...",
["TAUNT_STAGE11_CULTIST_LEADER_0002"] = "...¡pero no puedes parar lo inevitable!",
["TAUNT_STAGE11_CULTIST_LEADER_0003"] = "¡¡¡SUFICIENTE!!!",
["TAUNT_STAGE11_CULTIST_LEADER_0004"] = "¡Es hora de que ustedes se INCLINEN ante nosotros!",
["TAUNT_STAGE11_CULTIST_LEADER_0005"] = "Grrr... ¡este no es el final!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0001"] = "Un nuevo mundo nos espera.",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0002"] = "Subestimas mi poder.",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0003"] = "¡Oculus Poculus!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0004"] = "¡Escucha el sonido de lo inevitable!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0005"] = "¿Soy malvada? ¡Si, lo soy!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0006"] = "¡El Omnividente nos bendice!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0001"] = "¡Tu final está cerca!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0002"] = "¡Mis ojos han sido abiertos!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0003"] = "¡Di hola a mis amigos del vacío!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0004"] = "¡Oculus Poculus!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0005"] = "¡Escoria débil y patética!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0006"] = "¡El Omnividente nos bendice!",
["TAUNT_STAGE11_VEZNAN_0001"] = "Denas, amigo mío. ¡Tanto tiempo sin verte!",
["TAUNT_STAGE15_CULTIST_0001"] = "Falta poco... ya lo siento despertar!",
["TAUNT_STAGE15_CULTIST_0002"] = "Una nueva era se acerca. ¡Sus esfuerzos serán en vano!.",
["TAUNT_STAGE15_CULTIST_0003"] = "Grrr... su alianza es poderosa.",
["TAUNT_STAGE15_CULTIST_0004"] = "¡Pero les mostraré lo que es el verdadero poder!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0001"] = "Tontos! Han venido a morir.",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0002"] = "Ríndanse ante su mirada!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0003"] = "Se volverán verdaderos creyentes.",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0004"] = "Alianza o no, están perdidos!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0005"] = "No hay vida en el vacío. Solo muerte!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0006"] = "Dejen de desperdiciar mi tiempo!",
["TAUNT_STAGE15_DENAS_0001"] = "Tengo una cuenta que saldar. ¡No me perderé esta pelea!",
["TAUNT_STAGE16_DENAS_AFTER_BOSSFIGHT_0001"] = "No lo viste venir, ¿eh?",
["TAUNT_STAGE18_ERIDAN_FIGHT_0001"] = "Sangre ha sido derramada esta noche.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0002"] = "Confiamos en Elynie.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0003"] = "¡Gnilur speek Edihnori!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0004"] = "Soy incapaz de fallar.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0005"] = "¡Aredhel prevalecerá!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0006"] = "¡Estos no son elfos normales!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0007"] = "¿Estás llevando la cuenta?",
["TAUNT_STAGE18_ERIDAN_FIGHT_0008"] = "¡Déjalos venir!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0001"] = "¡Tienes mi arco!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0002"] = "¡Actúa con premura!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0003"] = "¡A sus posiciones!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0004"] = "¡Mantén los ojos abiertos!",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0001"] = "¡Suficiente calentamiento!",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0002"] = "Demostraron ser una molestia...",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0003"] = "¡Que comience la verdadera batalla!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0001"] = "¡Entreguen sus almas!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0002"] = "¡Los elfos se alzarán de nuevo!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0003"] = "¡Si hay algo que levanto... son los muertos!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0004"] = "¡Teman a mis hijos de ultratumba!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0005"] = "¡Mi ejército es interminable!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0006"] = "¡Le devolveré la gloria a mi pueblo!",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0001"] = "Ah, ¡la poderosa Alianza vino de visita!",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0002"] = "Justo a tiempo para ver el velo levantarse.",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0003"] = "¡Veamos si pueden enfrentar el poder de la muerte!",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0001"] = "Libre al fin de devorar...",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0002"] = "¡¡¡¡¡TODO!!!!!",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0001"] = "¡Basta de intromisiones!",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0002"] = "Grymbeard les enseñará modales.",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0003"] = "¡Todos a bordo, JAJAJA!",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0001"] = "¡Bufones insolentes!",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0002"] = "¡Nunca me atraparán, JAJAJA!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "No! Todavía queda...",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "¡POR MIS BARBAS!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0001"] = "¡No eres rival para este ejército!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0002"] = "Grymbeard no está en peligro.",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0003"] = "¡Grymbeard ES el peligro!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0004"] = "¿Podría un loco hacer esto?",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0005"] = "¡El mundo se arrodillará ante Grymbeard!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0001"] = "La paciencia de Grymbeard se agotó.",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0002"] = "Enfréntate a las consecuencias!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0003"] = "Grymbeard no necesita a nadie.",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0004"] = "¡¿Podrías apurarte?!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "¡Maldita Alianza entrometida!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "Les enseñaré a no meterse...",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0003"] = "...¡con ÉL enano!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0001"] = "Aplasten todos los clones que quieran, simplemente haré más.",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0002"] = "Si quieres que algo esté bien hecho, hazlo tú mismo.",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0003"] = "¡Ah Grymbeard, qué genio eres!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0004"] = "¡No te saldrás con la tuya!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0005"] = "¿Se están esforzando?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0006"] = "¿Crees que puedes superar a mis creaciones?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0001"] = "Supongo que no se aburrieron de mis otros yo...",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0002"] = "...¿y ahora quieren medirse con el VERDADERO enano?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0003"] = "Inténtalo si quieres.",
["TAUNT_TUTORIAL_ARBOREAN_ALL_0001"] = "¡Sigue así! ¡Creemos en tí!",
["TAUNT_TUTORIAL_ARBOREAN_BARRACK_0001"] = "¡Construye una barraca aquí!",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "Limblliam",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "Henry Tentáculo",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "Geoffrey Tentáculo",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "Tentaclas",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "Tedtacle",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "Holimb",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "Tentodo",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "Limbdric",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "Halimb",
["TERMS_OF_SERVICE_LINK"] = "Términos de servicio",
["TIP!"] = "¡CONSEJO!",
["TIP_1"] = "Recuerda tocar sobre los enemigos y leer los consejos para saber cómo lidiar con ellos. ",
["TIP_10"] = "No se puede bloquear a los enemigos voladores y la mayoría de torres de artillería los ignoran.",
["TIP_11"] = "Puedes usar los poderosos objetos en la tienda. ¡Pueden ayudarte a cambiar el combate a tu favor!",
["TIP_2"] = "El daño mágico es una forma muy efectiva de lidiar con enemigos con armadura pesada.",
["TIP_3"] = "Si mejoras una barraca, aparece un grupo de soldados nuevos que reemplaza a los soldados caídos.",
["TIP_4"] = "Adelantar a una oleada te otorga oro adicional y enfría tus conjuros durante un rato.",
["TIP_5"] = "Ajusta el punto de encuentro de las barracas para moverlas a sitios con vistas privilegiadas.",
["TIP_6"] = "Presta atención a la bandera de la siguiente oleada. Si la tocas, verás lo que se avecina. ¡Prepárate!",
["TIP_7"] = "Los enemigos voladores reciben daño de la mayoría de los ataques en área aunque no sean el objetivo.",
["TIP_8"] = "Vender una torre que no es útil te puede dar suficiente dinero para mejorar tu estrategia.",
["TIP_9"] = "Mejorar una torre suele ser más efectivo que construir más torres del mismo tipo.",
["TIP_ALERT_ICON"] = "CONSEJO",
["TIP_TITLE"] = "Consejo:",
["TOWER_ARBOREAN_EMISSARY_1_DESCRIPTION"] = "Los Arborean dejan vulnerable a sus enemigos utilizando la poderosa magia de la naturaleza.",
["TOWER_ARBOREAN_EMISSARY_1_NAME"] = "Emisario Arborean I",
["TOWER_ARBOREAN_EMISSARY_2_DESCRIPTION"] = "Los Arborean dejan vulnerable a sus enemigos utilizando la poderosa magia de la naturaleza.",
["TOWER_ARBOREAN_EMISSARY_2_NAME"] = "Emisario Arborean II",
["TOWER_ARBOREAN_EMISSARY_3_DESCRIPTION"] = "Los Arborean dejan vulnerable a sus enemigos utilizando la poderosa magia de la naturaleza.",
["TOWER_ARBOREAN_EMISSARY_3_NAME"] = "Emisario Arborean III",
["TOWER_ARBOREAN_EMISSARY_4_DESCRIPTION"] = "Los Arborean dejan vulnerable a sus enemigos utilizando la poderosa magia de la naturaleza.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_DESCRIPTION"] = "Invoca fuegos fatuos que curan %$towers.arborean_emissary.gift_of_nature.s_heal[1]%$ de salud por segundo en un área por %$towers.arborean_emissary.gift_of_nature.duration[1]%$ segundos.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_NAME"] = "REGALO DE LA NATURALEZA",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_DESCRIPTION"] = "Invoca fuegos fatuos que curan %$towers.arborean_emissary.gift_of_nature.s_heal[2]%$ de vida a los aliados en un área por %$towers.arborean_emissary.gift_of_nature.duration[2]%$ segundos.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_NAME"] = "GIFT OF NATURE",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_DESCRIPTION"] = "Invoca fuegos fatuos que curan %$towers.arborean_emissary.gift_of_nature.s_heal[3]%$ de vida a los aliados en un área por %$towers.arborean_emissary.gift_of_nature.duration[3]%$ segundos.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_NAME"] = "REGALO DE LA NATURALEZA",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NAME"] = "REGALO DE LA NATURALEZA",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NOTE"] = "Nunca te metas con lo Verde.",
["TOWER_ARBOREAN_EMISSARY_4_NAME"] = "Emisario Arborean IV",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_DESCRIPTION"] = "Invoca %$towers.arborean_emissary.wave_of_roots.max_targets[1]%$ raíces a lo largo del camino, causando %$towers.arborean_emissary.wave_of_roots.s_damage[1]%$ de daño verdadero y aturdiendo a los enemigos por %$towers.arborean_emissary.wave_of_roots.mod_duration[1]%$ segundos.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_NAME"] = "AGARRE DE ZARZAS",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_DESCRIPTION"] = "Invoca %$towers.arborean_emissary.wave_of_roots.max_targets[2]%$ raíces a lo largo del camino, haciendo %$towers.arborean_emissary.wave_of_roots.s_damage[2]%$ de daño verdadero a los enemigos y aturdiendolos por %$towers.arborean_emissary.wave_of_roots.mod_duration[2]%$ segundos.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_NAME"] = "AGARRE DE ZARZAS",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_DESCRIPTION"] = "Invoca %$towers.arborean_emissary.wave_of_roots.max_targets[3]%$ raíces a lo largo del camino, haciendo %$towers.arborean_emissary.wave_of_roots.s_damage[3]%$ de daño verdadero a los enemigos y aturdiendolos por %$towers.arborean_emissary.wave_of_roots.mod_duration[3]%$ segundos.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_NAME"] = "AGARRE DE ZARZAS",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NAME"] = "AGARRE DE ZARZAS",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NOTE"] = "Mira por donde caminas.",
["TOWER_ARBOREAN_EMISSARY_DESC"] = "Si se los provoca, los pacíficos Arborean son conocidos por utilizar su magia para marcar y debilitar a sus enemigos.",
["TOWER_ARBOREAN_EMISSARY_NAME"] = "Emisario Arborean",
["TOWER_ARBOREAN_SENTINELS_DESCRIPTION"] = "Ágiles protectoras del bosque.",
["TOWER_ARBOREAN_SENTINELS_NAME"] = "Lanzaespinosas Arborean",
["TOWER_ARCANE_WIZARD_1_DESCRIPTION"] = "Conocedores del arte de la magia, los Hechiceros están siempre listos para pelear.",
["TOWER_ARCANE_WIZARD_1_NAME"] = "Hechicero Arcano I",
["TOWER_ARCANE_WIZARD_2_DESCRIPTION"] = "Conocedores del arte de la magia, los Hechiceros están siempre listos para pelear.",
["TOWER_ARCANE_WIZARD_2_NAME"] = "Hechicero Arcano II",
["TOWER_ARCANE_WIZARD_3_DESCRIPTION"] = "Conocedores del arte de la magia, los Hecicheros están siempre listos para pelear.",
["TOWER_ARCANE_WIZARD_3_NAME"] = "Hechicero Arcano III",
["TOWER_ARCANE_WIZARD_4_DESCRIPTION"] = "Conocedores del arte de la magia, los Hechiceros están siempre listos para pelear.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_DESCRIPTION"] = "Lanza un rayo que mata al enemigo instantáneamente. Jefes y mini jefes reciben %$towers.arcane_wizard.disintegrate.boss_damage[1]%$ de daño mágico en su lugar.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_NAME"] = "DESINTEGRAR",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_DESCRIPTION"] = "Reduce el renfríamiento de Desintegrar a %$towers.arcane_wizard.disintegrate.cooldown[2]%$ segundos. Daño a jefe y mini jefes aumenta a %$towers.arcane_wizard.disintegrate.boss_damage[2]%$.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_NAME"] = "DESINTEGRAR",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_DESCRIPTION"] = "Reduce el renfríamiento de Desintegrar a %$towers.arcane_wizard.disintegrate.cooldown[3]%$ segundos.  Daño a jefe y mini jefes aumenta a %$towers.arcane_wizard.disintegrate.boss_damage[3]%$.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_NAME"] = "DESINTEGRAR",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NAME"] = "DESINTEGRAR",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NOTE"] = "Polvo al polvo.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_DESCRIPTION"] = "Otorga %$towers.arcane_wizard.empowerment.s_damage_factor[1]%$% de daño extra a las torres cercanas.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_NAME"] = "FORTALECIMIENTO",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_DESCRIPTION"] = "Incrementa el daño de las torres cercanas en un %$towers.arcane_wizard.empowerment.s_damage_factor[2]%$%.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_NAME"] = "FORTALECIMIENTO",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_DESCRIPTION"] = "Incrementa el daño de las torres cercanas en un %$towers.arcane_wizard.empowerment.s_damage_factor[3]%$%.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_NAME"] = "FORTALECIMIENTO",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NAME"] = "FORTALECIMIENTO",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NOTE"] = "Poder ilimitado.",
["TOWER_ARCANE_WIZARD_4_NAME"] = "Hechicero Arcano IV",
["TOWER_ARCANE_WIZARD_DESC"] = "Utilizando magia pura, los Hechiceros Arcanos esgrimen suficiente poder como para destruir completamente a sus enemigos.",
["TOWER_ARCANE_WIZARD_NAME"] = "Hechicero Arcano",
["TOWER_BALLISTA_1_DESCRIPTION"] = "Es un gran añadido a las armas de los pielesverdes y es un milagro que todavía no se haya roto en pedazos.",
["TOWER_BALLISTA_1_NAME"] = "Torre Balista I",
["TOWER_BALLISTA_2_DESCRIPTION"] = "Es un gran añadido a las armas de los pielesverdes y es un milagro que todavía no se haya roto en pedazos.",
["TOWER_BALLISTA_2_NAME"] = "Torre Balista II",
["TOWER_BALLISTA_3_DESCRIPTION"] = "Es un gran añadido a las armas de los pielesverdes y es un milagro que todavía no se haya roto en pedazos.",
["TOWER_BALLISTA_3_NAME"] = "Torre Balista III",
["TOWER_BALLISTA_4_DESCRIPTION"] = "Es un gran añadido a las armas de los pielesverdes y es un milagro que todavía no se haya roto en pedazos.",
["TOWER_BALLISTA_4_NAME"] = "Torre Balista IV",
["TOWER_BALLISTA_4_SKILL_BOMB_1_DESCRIPTION"] = "Dispara una bomba de chatarra a gran distancia, haciendo %$towers.ballista.skill_bomb.damage_min[1]%$-%$towers.ballista.skill_bomb.damage_max[1]%$ de daño físico en área y dejando restos que los enlentecen por %$towers.ballista.skill_bomb.duration[1]%$ segundos.",
["TOWER_BALLISTA_4_SKILL_BOMB_1_NAME"] = "BOMBA DE CHATARRA",
["TOWER_BALLISTA_4_SKILL_BOMB_2_DESCRIPTION"] = "La bomba de chatarra hace %$towers.ballista.skill_bomb.damage_min[2]%$-%$towers.ballista.skill_bomb.damage_max[2]%$ de daño físico. Enlentece a los enemigos por %$towers.ballista.skill_bomb.duration[1]%$ segundos.",
["TOWER_BALLISTA_4_SKILL_BOMB_2_NAME"] = "BOMBA DE CHATARRA",
["TOWER_BALLISTA_4_SKILL_BOMB_3_DESCRIPTION"] = "La bomba de chatarra hace %$towers.ballista.skill_bomb.damage_min[3]%$-%$towers.ballista.skill_bomb.damage_max[3]%$ de daño físico. Enlentece a los enemigos por %$towers.ballista.skill_bomb.duration[1]%$ segundos.",
["TOWER_BALLISTA_4_SKILL_BOMB_3_NAME"] = "BOMBA DE CHATARRA",
["TOWER_BALLISTA_4_SKILL_BOMB_NOTE"] = "¡Fore!",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_DESCRIPTION"] = "El último tiro de la torre hace %$towers.ballista.skill_final_shot.s_damage_factor[1]%$% más de daño y aturde al objetivo por %$towers.ballista.skill_final_shot.s_stun%$ segundos.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_NAME"] = "CLAVO REMATADOR",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_DESCRIPTION"] = "El último tiro de la torre hace %$towers.ballista.skill_final_shot.s_damage_factor[2]%$% más de daño y aturde al objetivo por %$towers.ballista.skill_final_shot.s_stun%$ segundo.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_NAME"] = "CLAVO REMATADOR",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_DESCRIPTION"] = "El último tiro de la torre hace %$towers.ballista.skill_final_shot.s_damage_factor[3]%$% más de daño y aturde al objetivo por %$towers.ballista.skill_final_shot.s_stun%$ segundo.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_NAME"] = "CLAVO REMATADOR",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_NOTE"] = "¡Fue uno en un millón, muchacho!",
["TOWER_BALLISTA_DESC"] = "Entusiasmados por la guerra, los goblins hicieron un esfuerzo extra para no tener que levantar un arco nunca más.",
["TOWER_BALLISTA_NAME"] = "Torre Balista",
["TOWER_BARREL_1_DESCRIPTION"] = "Los barriles de poción de los norteños son un arma poderosa contra las hordas de enemigos.",
["TOWER_BARREL_1_NAME"] = "Maestros Fermentadores I",
["TOWER_BARREL_2_DESCRIPTION"] = "Los barriles de poción de los norteños son un arma poderosa contra las hordas de enemigos.",
["TOWER_BARREL_2_NAME"] = "Maestros Fermentadores II",
["TOWER_BARREL_3_DESCRIPTION"] = "Los barriles de poción de los norteños son un arma poderosa contra las hordas de enemigos.",
["TOWER_BARREL_3_NAME"] = "Maestros Fermentadores III",
["TOWER_BARREL_4_DESCRIPTION"] = "Los barriles de poción de los norteños son un arma poderosa contra las hordas de enemigos.",
["TOWER_BARREL_4_NAME"] = "Maestros Fermentadores IV",
["TOWER_BARREL_4_SKILL_BARREL_1_DESCRIPTION"] = "Lanza un barril tóxico que causa %$towers.barrel.skill_barrel.explosion.damage_min[1]%$-%$towers.barrel.skill_barrel.explosion.damage_max[1]%$ de daño al explotar. El barril deja un veneno que hace %$towers.barrel.skill_barrel.poison.s_damage%$ de daño por segundo durante %$towers.barrel.skill_barrel.poison.duration%$ segundos.",
["TOWER_BARREL_4_SKILL_BARREL_1_NAME"] = "LOTE CADUCO",
["TOWER_BARREL_4_SKILL_BARREL_2_DESCRIPTION"] = "La explosión del barril hace %$towers.barrel.skill_barrel.explosion.damage_min[2]%$-%$towers.barrel.skill_barrel.explosion.damage_max[2]%$ daño físico. El veneno del barril hace %$towers.barrel.skill_barrel.poison.s_damage%$ daño verdadero por segundo por %$towers.barrel.skill_barrel.poison.duration%$ segundos.",
["TOWER_BARREL_4_SKILL_BARREL_2_NAME"] = "LOTE CADUCO",
["TOWER_BARREL_4_SKILL_BARREL_3_DESCRIPTION"] = "La explosión del barril hace %$towers.barrel.skill_barrel.explosion.damage_min[3]%$-%$towers.barrel.skill_barrel.explosion.damage_max[3]%$ daño físico. El veneno del barril hace %$towers.barrel.skill_barrel.poison.s_damage%$ daño verdadero por segundo por %$towers.barrel.skill_barrel.poison.duration%$ segundos.",
["TOWER_BARREL_4_SKILL_BARREL_3_NAME"] = "LOTE CADUCO",
["TOWER_BARREL_4_SKILL_BARREL_NOTE"] = "¡Solo para valientes!",
["TOWER_BARREL_4_SKILL_WARRIOR_1_DESCRIPTION"] = "Invoca a un guerrero potenciado que lucha con los enemigos en el camino. Tiene %$towers.barrel.skill_warrior.entity.hp_max[1]%$ de vida y hace %$towers.barrel.skill_warrior.entity.damage_min[1]%$-%$towers.barrel.skill_warrior.entity.damage_max[1]%$ de daño físico a enemigos.",
["TOWER_BARREL_4_SKILL_WARRIOR_1_NAME"] = "ELIXIR DE FUERZA",
["TOWER_BARREL_4_SKILL_WARRIOR_2_DESCRIPTION"] = "El guerrero tiene %$towers.barrel.skill_warrior.entity.hp_max[2]%$ de vida y hace %$towers.barrel.skill_warrior.entity.damage_min[2]%$-%$towers.barrel.skill_warrior.entity.damage_max[2]%$ de daño físico.",
["TOWER_BARREL_4_SKILL_WARRIOR_2_NAME"] = "ELIXIR DE FUERZA",
["TOWER_BARREL_4_SKILL_WARRIOR_3_DESCRIPTION"] = "El guerrero tiene %$towers.barrel.skill_warrior.entity.hp_max[3]%$ de vida y hace %$towers.barrel.skill_warrior.entity.damage_min[3]%$-%$towers.barrel.skill_warrior.entity.damage_max[3]%$ de daño físico.",
["TOWER_BARREL_4_SKILL_WARRIOR_3_NAME"] = "ELIXIR DE FUERZA",
["TOWER_BARREL_4_SKILL_WARRIOR_NOTE"] = "¡Sabe a victoria!",
["TOWER_BARREL_DESC"] = "Los norteños son expertos en el arte de hacer pociones y utilizan sus brebajes contra los enemigos en el campo de batalla.",
["TOWER_BARREL_NAME"] = "Maestros Fermentadores",
["TOWER_BARREL_WARRIOR_NAME"] = "Halfdan el Duro",
["TOWER_BROKEN_DESCRIPTION"] = "Esta torre está dañada, paga oro para repararla.",
["TOWER_BROKEN_NAME"] = "Torre Dañada",
["TOWER_CROCS_EATEN_DESCRIPTION"] = "Reconstruir mágicamente la torre a su forma original.",
["TOWER_CROCS_EATEN_NAME"] = "Restos de torre",
["TOWER_DARK_ELF_1_DESCRIPTION"] = "No importa la distancia o la fuerza del enemigo, sus flechas siempre son certeras.",
["TOWER_DARK_ELF_1_NAME"] = "Arquero Crepuscular I",
["TOWER_DARK_ELF_2_DESCRIPTION"] = "No importa la distancia o la fuerza del enemigo, sus flechas siempre son certeras.",
["TOWER_DARK_ELF_2_NAME"] = "Arquero Crepuscular II",
["TOWER_DARK_ELF_3_DESCRIPTION"] = "No importa la distancia o la fuerza del enemigo, sus flechas siempre son certeras.",
["TOWER_DARK_ELF_3_NAME"] = "Arquero Crepuscular III",
["TOWER_DARK_ELF_4_DESCRIPTION"] = "No importa la distancia o la fuerza del enemigo, sus flechas siempre son certeras.",
["TOWER_DARK_ELF_4_NAME"] = "Arquero Crepuscular IV",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_DESCRIPTION"] = "Cada vez que la torre mata a un enemigo, su daño de ataque incrementa en %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_NAME"] = "EMOCIÓN DE LA CAZA",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_DESCRIPTION"] = "Cada vez que la torre mata a un enemigo, su daño de ataque incrementa en %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_NAME"] = "EMOCIÓN DE LA CAZA",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_DESCRIPTION"] = "Cada vez que la torre mata a un enemigo, su daño de ataque incrementa en %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_NAME"] = "EMOCIÓN DE LA CAZA",
["TOWER_DARK_ELF_4_SKILL_BUFF_NOTE"] = "¡Tally-ho!",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_DESCRIPTION"] = "Invoca dos Hostigadores Crepusculares. Tienen %$towers.dark_elf.soldier.hp[1]%$ de vida y hacen %$towers.dark_elf.soldier.basic_attack.damage_min[1]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[1]%$ de daño físico.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_NAME"] = "ESPADAS DE REFUERZO",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_DESCRIPTION"] = "Los Hostigadores Crepusculares ahora tienen %$towers.dark_elf.soldier.hp[2]%$ de vida y hacen %$towers.dark_elf.soldier.basic_attack.damage_min[2]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[2]%$ de daño físico.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_NAME"] = "ESPADAS DE REFUERZO",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_DESCRIPTION"] = "Los Hostigadores Crepusculares ahora tienen %$towers.dark_elf.soldier.hp[3]%$ de vida y hacen %$towers.dark_elf.soldier.basic_attack.damage_min[3]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[3]%$ de daño físico.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_NAME"] = "ESPADAS DE REFUERZO",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_NOTE"] = "Vienen amigos a jugar.",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_DESCRIPTION"] = "Cambia el enfoque de la torre al enemigo que está más cerca de la salida.",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NAME"] = "Foco Enemigo: Más Cercano",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NOTE"] = "¡No dejes que se escape!",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_DESCRIPTION"] = "Cambia el enfoque de la torre al enemigo con más vida total.",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NAME"] = "Foco Enemigo: Vida Alta",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NOTE"] = "¡Ve por el más grande!",
["TOWER_DARK_ELF_DESC"] = "Arqueros especializados en la caza de enemigos poderosos a grandes distancias, mejorando sus disparos con energía oscura.",
["TOWER_DARK_ELF_NAME"] = "Arquero Crepuscular",
["TOWER_DEMON_PIT_1_DESCRIPTION"] = "Traviesos y peligrosos, estos demonios siempre están buscando problemas.",
["TOWER_DEMON_PIT_1_NAME"] = "Fosa Demoníaca I",
["TOWER_DEMON_PIT_2_DESCRIPTION"] = "Traviesos y peligrosos, estos demonios siempre están buscando problemas.",
["TOWER_DEMON_PIT_2_NAME"] = "Fosa Demoníaca II",
["TOWER_DEMON_PIT_3_DESCRIPTION"] = "Traviesos y peligrosos, estos demonios siempre están buscando problemas.",
["TOWER_DEMON_PIT_3_NAME"] = "Fosa Demoníaca III",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_DESCRIPTION"] = "Invoca un enorme diablillo de %$towers.demon_pit.big_guy.hp_max[1]%$ salud que avanza peleando con los enemigos, haciendo %$towers.demon_pit.big_guy.melee_attack.damage_min[1]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[1]%$ de daño físico. Al morir, hace %$towers.demon_pit.big_guy.explosion_damage[1]%$ daño a su alrededor.",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_NAME"] = "JEFAZO",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_DESCRIPTION"] = "El Gran Diablillo tiene %$towers.demon_pit.big_guy.hp_max[2]%$ de vida y hace %$towers.demon_pit.big_guy.melee_attack.damage_min[2]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[2]%$ de daño físico. La explosión hace %$towers.demon_pit.big_guy.explosion_damage[2]%$ de daño.",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_NAME"] = "JEFAZO",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_DESCRIPTION"] = "El Gran Diablillo tiene %$towers.demon_pit.big_guy.hp_max[3]%$ de vida y hace %$towers.demon_pit.big_guy.melee_attack.damage_min[3]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[3]%$ de daño físico. La explosión hace %$towers.demon_pit.big_guy.explosion_damage[3]%$ de daño.",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_NAME"] = "JEFAZO",
["TOWER_DEMON_PIT_4_BIG_DEMON_NAME"] = "JEFAZO",
["TOWER_DEMON_PIT_4_BIG_DEMON_NOTE"] = "Oye, tranquilo viejo.",
["TOWER_DEMON_PIT_4_DESCRIPTION"] = "Traviesos y peligrosos, estos demonios siempre están buscando problemas.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_DESCRIPTION"] = "La explosión de los diablillos hace %$towers.demon_pit.master_exploders.s_damage_increase[1]%$% más daño y quema a los enemigos por %$towers.demon_pit.master_exploders.s_burning_duration[1]%$ segundos, haciendo %$towers.demon_pit.master_exploders.s_total_burning_damage_min[1]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[1]%$ de daño verdadero por segundo.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_NAME"] = "MAESTROS EXPLOSIVOS",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_DESCRIPTION"] = "La explosión de los diablillos hace %$towers.demon_pit.master_exploders.s_damage_increase[2]%$% más de daño. El quemado hace %$towers.demon_pit.master_exploders.s_total_burning_damage_min[2]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[2]%$ de daño verdadero por segundo por %$towers.demon_pit.master_exploders.s_burning_duration[2]%$ segundos.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_NAME"] = "MAESTROS EXPLOSIVOS",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_DESCRIPTION"] = "La explosión de los diablillos hace %$towers.demon_pit.master_exploders.s_damage_increase[3]%$% más de daño. El quemado hace %$towers.demon_pit.master_exploders.s_total_burning_damage_min[3]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[3]%$ de daño verdadero por segundo por %$towers.demon_pit.master_exploders.s_burning_duration[3]%$ segundos.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_NAME"] = "MAESTROS EXPLOSIVOS",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NAME"] = "MAESTROS EXPLOSIVOS",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NOTE"] = "Solo un idiota haría este trabajo.",
["TOWER_DEMON_PIT_4_NAME"] = "Fosa Demoníaca IV",
["TOWER_DEMON_PIT_DESC"] = "Surgidos de las profundidades de la lava, los diablillos no dudan en lanzarse hacia el camino de los enemigos.",
["TOWER_DEMON_PIT_NAME"] = "Fosa Demoníaca",
["TOWER_DEMON_PIT_SOLDIER_BIG_GUY_NAME"] = "Grandote",
["TOWER_DEMON_PIT_SOLDIER_NAME"] = "Diablillo",
["TOWER_DWARF_1_DESCRIPTION"] = "A pesar de su reducida altura, nada pasa de sus líneas con vida.",
["TOWER_DWARF_1_NAME"] = "Escuadra de Cañoneras I",
["TOWER_DWARF_2_DESCRIPTION"] = "A pesar de su reducida altura, nada pasa de sus líneas con vida.",
["TOWER_DWARF_2_NAME"] = "Escuadra de Cañoneras II",
["TOWER_DWARF_3_DESCRIPTION"] = "A pesar de su reducida altura, nada pasa de sus líneas con vida.",
["TOWER_DWARF_3_NAME"] = "Escuadra de Cañoneras III",
["TOWER_DWARF_4_DESCRIPTION"] = "A pesar de su reducida altura, nada pasa de sus líneas con vida.",
["TOWER_DWARF_4_FORMATION_1_DESCRIPTION"] = "Agrega una tercera Cañonera a la escuadra.",
["TOWER_DWARF_4_FORMATION_1_NAME"] = "FILAS CRECIENTES",
["TOWER_DWARF_4_FORMATION_2_DESCRIPTION"] = "Agrega una cuarta Cañonera a la escuadra.",
["TOWER_DWARF_4_FORMATION_2_NAME"] = "FILAS CRECIENTES",
["TOWER_DWARF_4_FORMATION_3_DESCRIPTION"] = "Agrega una quinta Cañonera a la escuadra.",
["TOWER_DWARF_4_FORMATION_3_NAME"] = "FILAS CRECIENTES",
["TOWER_DWARF_4_FORMATION_NOTE"] = "Nadie pasa de esta esquina.",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_DESCRIPTION"] = "Lanzan un explosivo que hace %$towers.dwarf.incendiary_ammo.damages_min[1]%$ - %$towers.dwarf.incendiary_ammo.damages_max[1]%$ daño y quema enemigos en el área por %$towers.dwarf.incendiary_ammo.burn.s_damage[1]%$ daño durante %$towers.dwarf.incendiary_ammo.burn.duration%$ segundos.",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_NAME"] = "MUNICIÓN INCENDIARIA",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_DESCRIPTION"] = "Lanzan un explosivo que hace %$towers.dwarf.incendiary_ammo.damages_min[2]%$ - %$towers.dwarf.incendiary_ammo.damages_max[2]%$ daño y quema enemigos en el área por %$towers.dwarf.incendiary_ammo.burn.s_damage[2]%$ daño durante %$towers.dwarf.incendiary_ammo.burn.duration%$ segundos.",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_NAME"] = "MUNICIÓN INCENDIARIA",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_DESCRIPTION"] = "Lanzan un explosivo que hace %$towers.dwarf.incendiary_ammo.damages_min[3]%$ - %$towers.dwarf.incendiary_ammo.damages_max[3]%$ daño y quema enemigos en el área por %$towers.dwarf.incendiary_ammo.burn.s_damage[3]%$ daño durante %$towers.dwarf.incendiary_ammo.burn.duration%$ segundos.",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_NAME"] = "MUNICIÓN INCENDIARIA",
["TOWER_DWARF_4_INCENDIARY_AMMO_NOTE"] = "No jueguen con fuego!",
["TOWER_DWARF_4_NAME"] = "Escuadra de Cañoneras IV",
["TOWER_DWARF_DESC"] = "Expertas tiradoras con un sentido de camadería inigualable, enviadas desde el norte para controlar el uso inadecuado de tecnología ",
["TOWER_DWARF_NAME"] = "Escuadra de Cañoneras",
["TOWER_ELVEN_STARGAZERS_DESC"] = "Canalizando las energías del cosmos, los Astrónomos Elfo pueden pelear con varios enemigos al mismo tiempo.",
["TOWER_ELVEN_STARGAZERS_NAME"] = "Miraestrellas Elfo",
["TOWER_FLAMESPITTER_1_DESCRIPTION"] = "Su fuego puede compararse con el de un dragón, aterrorizando a los malvados.",
["TOWER_FLAMESPITTER_1_NAME"] = "Escupellamas Enano I",
["TOWER_FLAMESPITTER_2_DESCRIPTION"] = "Su fuego puede compararse con el de un dragón, aterrorizando a los malvados.",
["TOWER_FLAMESPITTER_2_NAME"] = "Escupellamas Enano II",
["TOWER_FLAMESPITTER_3_DESCRIPTION"] = "Su fuego puede compararse con el de un dragón, aterrorizando a los malvados.",
["TOWER_FLAMESPITTER_3_NAME"] = "Escupellamas Enano III",
["TOWER_FLAMESPITTER_4_DESCRIPTION"] = "Su fuego puede compararse con el de un dragón, aterrorizando a los malvados.",
["TOWER_FLAMESPITTER_4_NAME"] = "Escupellamas IV",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_DESCRIPTION"] = "Dispara una bomba incendiaria que hace %$towers.flamespitter.skill_bomb.s_damage[1]%$ de daño físico en área y quema enemigos por %$towers.flamespitter.skill_bomb.burning.s_damage%$ de daño verdadero por segundo durante %$towers.flamespitter.skill_bomb.burning.duration%$ segundos.",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_NAME"] = "SENDERO ARDIENTE",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_DESCRIPTION"] = "La bomba incendiaria hace %$towers.flamespitter.skill_bomb.s_damage[2]%$ de daño físico. El quemado hace %$towers.flamespitter.skill_bomb.burning.s_damage%$ de daño verdadero por segundo %$towers.flamespitter.skill_bomb.burning.duration%$ por segundos.",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_NAME"] = "SENDERO ARDIENTE",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_DESCRIPTION"] = "La bomba incendiaria hace %$towers.flamespitter.skill_bomb.s_damage[3]%$ de daño físico. El quemado hace %$towers.flamespitter.skill_bomb.burning.s_damage%$ de daño verdadero por segundo %$towers.flamespitter.skill_bomb.burning.duration%$ por segundos.",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_NAME"] = "SENDERO ARDIENTE",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_NOTE"] = "Ardiente y salvaje.",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_DESCRIPTION"] = "Crea columnas de fuego en el camino que hacen %$towers.flamespitter.skill_columns.s_damage_out[1]%$-%$towers.flamespitter.skill_columns.s_damage_in[1]%$ de daño físico y aturden enemigos por %$towers.flamespitter.skill_columns.s_stun%$ segundo.",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_NAME"] = "FOGONAZO ABRASADOR",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_DESCRIPTION"] = "Las columnas de fuego hacen %$towers.flamespitter.skill_columns.s_damage_out[2]%$-%$towers.flamespitter.skill_columns.s_damage_in[2]%$ de daño físico y aturden enemigos por %$towers.flamespitter.skill_columns.s_stun%$ segundo.",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_NAME"] = "FOGONAZO ABRASADOR",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_DESCRIPTION"] = "Las columnas de fuego hacen %$towers.flamespitter.skill_columns.s_damage_out[3]%$-%$towers.flamespitter.skill_columns.s_damage_in[3]%$ de daño físico y aturden enemigos por %$towers.flamespitter.skill_columns.s_stun%$ segundo.",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_NAME"] = "FOGONAZO ABRASADOR",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_NOTE"] = "¡Fíjate por donde andas!",
["TOWER_FLAMESPITTER_DESC"] = "Llevando el calor de la forja al campo de batalla, los enanos luchan de forma decidida junto a la alianza.",
["TOWER_FLAMESPITTER_NAME"] = "Escupellamas Enano",
["TOWER_GHOST_1_DESCRIPTION"] = "Ahora los ves, ahora no los ves. Ahora estás muerto.",
["TOWER_GHOST_1_NAME"] = "Espectros Sombríos I",
["TOWER_GHOST_2_DESCRIPTION"] = "Ahora los ves, ahora no los ves. Ahora estás muerto.",
["TOWER_GHOST_2_NAME"] = "Espectros Sombríos II",
["TOWER_GHOST_3_DESCRIPTION"] = "Ahora los ves, ahora no los ves. Ahora estás muerto.",
["TOWER_GHOST_3_NAME"] = "Espectros Sombríos III",
["TOWER_GHOST_4_DESCRIPTION"] = "Ahora los ves, ahora no los ves. Ahora estás muerto.",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_DESCRIPTION"] = "Los espectros hacen %$towers.ghost.extra_damage.s_damage[1]%$% de daño extra después de pasar %$towers.ghost.extra_damage.cooldown_start%$ segundos en combate.",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_NAME"] = "DRENADO DE ALMAS",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_DESCRIPTION"] = "Los espectros hacen %$towers.ghost.extra_damage.s_damage[2]%$% de daño extra después de pasar %$towers.ghost.extra_damage.cooldown_start%$ segundos en combate.",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_NAME"] = "DRENADO DE ALMAS",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_DESCRIPTION"] = "Los espectros hacen %$towers.ghost.extra_damage.s_damage[3]%$% de daño extra después de pasar %$towers.ghost.extra_damage.cooldown_start%$ segundos en combate.",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_NAME"] = "DRENADO DE ALMAS",
["TOWER_GHOST_4_EXTRA_DAMAGE_NOTE"] = "Se recomienda no exponerse.",
["TOWER_GHOST_4_NAME"] = "Espectros Sombríos IV",
["TOWER_GHOST_4_SOUL_ATTACK_1_DESCRIPTION"] = "Los espectros derrotados se lanzan hacia un enemigo cercano, haciéndole %$towers.ghost.soul_attack.s_damage[1]%$ de daño verdadero, enlenteciéndolo y reduciendo su daño de ataque.",
["TOWER_GHOST_4_SOUL_ATTACK_1_NAME"] = "PAVOR IMPERECEDERO",
["TOWER_GHOST_4_SOUL_ATTACK_2_DESCRIPTION"] = "Los espectros derrotados se lanzan hacia un enemigo cercano, haciéndole %$towers.ghost.soul_attack.s_damage[2]%$ de daño verdadero, enlenteciéndolo y reduciendo su daño de ataque.",
["TOWER_GHOST_4_SOUL_ATTACK_2_NAME"] = "PAVOR IMPERECEDERO",
["TOWER_GHOST_4_SOUL_ATTACK_3_DESCRIPTION"] = "Los espectros derrotados se lanzan hacia un enemigo cercano, haciéndole %$towers.ghost.soul_attack.s_damage[3]%$ de daño verdadero, enlenteciéndolo y reduciendo su daño de ataque.",
["TOWER_GHOST_4_SOUL_ATTACK_3_NAME"] = "PAVOR IMPERECEDERO",
["TOWER_GHOST_4_SOUL_ATTACK_NOTE"] = "¡Vendrás con nosotros!",
["TOWER_GHOST_DESC"] = "Espectros que luchan incluso después de la muerte. Su poder les permite moverse entre las sombras y sorprender a los enemigos.",
["TOWER_GHOST_NAME"] = "Espectros Sombríos",
["TOWER_HERMIT_TOAD_1_DESCRIPTION"] = "Un poco de magia, un poco de fuerza bruta, lo que sea necesario para deshacerse de los intrusos molestos.",
["TOWER_HERMIT_TOAD_1_NAME"] = "Sapote Ermitaño I",
["TOWER_HERMIT_TOAD_2_DESCRIPTION"] = "Un poco de magia, un poco de fuerza bruta, lo que sea necesario para deshacerse de los intrusos molestos.",
["TOWER_HERMIT_TOAD_2_NAME"] = "Sapote Ermitaño II",
["TOWER_HERMIT_TOAD_3_DESCRIPTION"] = "Un poco de magia, un poco de fuerza bruta, lo que sea necesario para deshacerse de los intrusos molestos.",
["TOWER_HERMIT_TOAD_3_NAME"] = "Sapote Ermitaño III",
["TOWER_HERMIT_TOAD_4_DESCRIPTION"] = "Un poco de magia, un poco de fuerza bruta, lo que sea necesario para deshacerse de los intrusos molestos.",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_DESCRIPTION"] = "Cada %$towers.hermit_toad.power_instakill.cooldown[1]%$ segundos utiliza su lengua para devorar a un enemigo.",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_NAME"] = "Lengua Pegajosa",
["TOWER_HERMIT_TOAD_4_JUMP_1_DESCRIPTION"] = "Cada %$towers.hermit_toad.power_jump.cooldown[1]%$ segundos, el ermitaño salta alto en el cielo, estrellándose contra los enemigos, causando %$towers.hermit_toad.power_jump.damage_min[1]%$ de daño y aturdiéndolos por %$towers.hermit_toad.power_jump.stun_duration[1]%$ segundos al aterrizar.",
["TOWER_HERMIT_TOAD_4_JUMP_1_NAME"] = "Golpeador Terrestre",
["TOWER_HERMIT_TOAD_4_NAME"] = "Sapote Ermitaño IV",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_DESCRIPTION"] = "Cada %$towers.hermit_toad.power_instakill.cooldown[1]%$ segundos, usa su lengua para devorar a un enemigo.",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_NAME"] = "Lengua Pegajosa I",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_NOTE"] = "Asunto pegajoso.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_DESCRIPTION"] = "Cada %$towers.hermit_toad.power_jump.cooldown[1]%$ segundos, el ermitaño salta alto en el cielo, estrellándose contra los enemigos, causando %$towers.hermit_toad.power_jump.damage_min[1]%$ de daño y aturdiéndolos por %$towers.hermit_toad.power_jump.stun_duration[1]%$ segundos al aterrizar.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_NAME"] = "Golpeador Terrestre I",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_DESCRIPTION"] = "Cada %$towers.hermit_toad.power_jump.cooldown[2]%$ segundos, el ermitaño salta alto en el cielo, estrellándose contra los enemigos, causando %$towers.hermit_toad.power_jump.damage_min[2]%$ de daño y aturdiéndolos por %$towers.hermit_toad.power_jump.stun_duration[2]%$ segundos al aterrizar.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_NAME"] = "Golpeador Terrestre II",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_DESCRIPTION"] = "Cada %$towers.hermit_toad.power_jump.cooldown[3]%$ segundos, el ermitaño salta alto en el cielo, estrellándose contra los enemigos, causando %$towers.hermit_toad.power_jump.damage_min[3]%$ de daño y aturdiéndolos por %$towers.hermit_toad.power_jump.stun_duration[3]%$ segundos al aterrizar.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_NAME"] = "Golpeador Terrestre III",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_NOTE"] = "Listo para el equipo de voleibol del pantano.",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_DESCRIPTION"] = "El ermitaño adopta una postura física.",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NAME"] = "Pantano lodoso",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NOTE"] = "¡Ensuciándose!",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_DESCRIPTION"] = "El ermitaño adopta una postura mágica.",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NAME"] = "Estanque mágico",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NOTE"] = "¡¡Poder Ilimitado!!",
["TOWER_HERMIT_TOAD_DESC"] = "Un mago sapo gigante con un don para escupir bolas de moco. Todo lo que quiere es un poco de paz y tranquilidad para sus baños en el estanque. NO LO MOLESTEN",
["TOWER_HERMIT_TOAD_NAME"] = "Sapote Ermitaño",
["TOWER_NECROMANCER_1_DESCRIPTION"] = "Como maestros de la muerte, los Nigromantes cosechan el caos que siembran en el campo de batalla.",
["TOWER_NECROMANCER_1_NAME"] = "Nigromante I",
["TOWER_NECROMANCER_2_DESCRIPTION"] = "Como maestros de la muerte, los Nigromantes cosechan el caos que siembran en el campo de batalla.",
["TOWER_NECROMANCER_2_NAME"] = "Nigromante II",
["TOWER_NECROMANCER_3_DESCRIPTION"] = "Como maestros de la muerte, los Nigromantes cosechan el caos que siembran en el campo de batalla.",
["TOWER_NECROMANCER_3_NAME"] = "Nigromante III",
["TOWER_NECROMANCER_4_DESCRIPTION"] = "Como maestros de la muerte, los Nigromantes cosechan el caos que siembran en el campo de batalla.",
["TOWER_NECROMANCER_4_NAME"] = "Nigromante IV",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_DESCRIPTION"] = "Coloca un tótem por %$towers.necromancer.skill_debuff.aura_duration[1]%$ segundos, maldiciendo enemigos y otorgando a esqueletos %$towers.necromancer.skill_debuff.s_damage_factor[1]%$% de daño de ataque extra.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_NAME"] = "EFIGE ÓSEA",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_DESCRIPTION"] = "El tótem otorga %$towers.necromancer.skill_debuff.s_damage_factor[2]%$% de daño de ataque extra a esqueletos. Enfriamiento reducido a %$towers.necromancer.skill_debuff.cooldown[2]%$ segundos.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_NAME"] = "EFIGE ÓSEA",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_DESCRIPTION"] = "El tótem otorga %$towers.necromancer.skill_debuff.s_damage_factor[3]%$% de daño de ataque extra a esqueletos. Enfriamiento reducido a %$towers.necromancer.skill_debuff.cooldown[3]%$ segundos.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_NAME"] = "EFIGE ÓSEA",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_NOTE"] = "Ejército de huesos fuertes!",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_DESCRIPTION"] = "Invoca a un Jinete Oscuro en el camino que hace %$towers.necromancer.skill_rider.s_damage[1]%$ de daño verdadero a los enemigos que atraviesa.",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_NAME"] = "JINETE OSCURO",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_DESCRIPTION"] = "El Jinete Oscuro hace %$towers.necromancer.skill_rider.s_damage[2]%$ de daño verdadero.",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_NAME"] = "JINETE OSCURO",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_DESCRIPTION"] = "El Jinete Oscuro hace %$towers.necromancer.skill_rider.s_damage[3]%$ de daño verdadero.",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_NAME"] = "JINETE OSCURO",
["TOWER_NECROMANCER_4_SKILL_RIDER_NOTE"] = "Un boleto de ida...",
["TOWER_NECROMANCER_DESC"] = "Con magia oscura, los Nigromantes reviven a sus enemigos como parte de su ejército.",
["TOWER_NECROMANCER_NAME"] = "Nigromante",
["TOWER_PALADIN_COVENANT_1_DESCRIPTION"] = "Feroces y dedicados, los paladines trabajan duro para proteger al reino del peligro.",
["TOWER_PALADIN_COVENANT_1_NAME"] = "Pacto de Paladines I",
["TOWER_PALADIN_COVENANT_2_DESCRIPTION"] = "Feroces y dedicados, los paladines trabajan duro para proteger al reino del peligro.",
["TOWER_PALADIN_COVENANT_2_NAME"] = "Pacto de Paladines II",
["TOWER_PALADIN_COVENANT_3_DESCRIPTION"] = "Feroces y dedicados, los paladines trabajan duro para proteger al reino del peligro.",
["TOWER_PALADIN_COVENANT_3_NAME"] = "Pacto de Paladines III",
["TOWER_PALADIN_COVENANT_4_DESCRIPTION"] = "Feroces y dedicados, los paladines trabajan duro para proteger al reino del peligro.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_DESCRIPTION"] = "Cuando los soldados llegan al %$towers.paladin_covenant.healing_prayer.health_trigger_factor[1]%$% de su vida total, se vuelven invencibles y recuperan %$towers.paladin_covenant.healing_prayer.s_healing[1]%$ vida por segundo por %$towers.paladin_covenant.healing_prayer.duration%$ segundos.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_NAME"] = "PLEGARIA CURATIVA",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_DESCRIPTION"] = "La curación se incrementa a %$towers.paladin_covenant.healing_prayer.s_healing[2]%$ de vida por segundo por %$towers.paladin_covenant.healing_prayer.duration%$ segundos.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_NAME"] = "PLEGARIA CURATIVA",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_DESCRIPTION"] = "La curación se incrementa a %$towers.paladin_covenant.healing_prayer.s_healing[3]%$ de vida por segundo por %$towers.paladin_covenant.healing_prayer.duration%$ segundos.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_NAME"] = "PLEGARIA CURATIVA",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NAME"] = "PLEGARIA CURATIVA",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NOTE"] = "Deber hasta la muerte.",
["TOWER_PALADIN_COVENANT_4_LEAD_1_DESCRIPTION"] = "Reemplaza uno de los paladines con un Veterano de la Guardia, que otorga %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$% de daño extra a unidades cercanas.",
["TOWER_PALADIN_COVENANT_4_LEAD_1_NAME"] = "LIDERAR CON EL EJEMPLO",
["TOWER_PALADIN_COVENANT_4_LEAD_2_DESCRIPTION"] = "Reemplaza uno de los paladines con un Veterano de la Guardia, que otorga %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$% de daño extra a unidades cercanas.",
["TOWER_PALADIN_COVENANT_4_LEAD_2_NAME"] = "LIDERAR CON EL EJEMPLO",
["TOWER_PALADIN_COVENANT_4_LEAD_3_DESCRIPTION"] = "Reemplaza uno de los paladines con un Veterano de la Guardia, que otorga %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$% de daño extra a unidades cercanas.",
["TOWER_PALADIN_COVENANT_4_LEAD_3_NAME"] = "LIDERAR CON EL EJEMPLO",
["TOWER_PALADIN_COVENANT_4_LEAD_NAME"] = "LIDERAR CON EL EJEMPLO",
["TOWER_PALADIN_COVENANT_4_LEAD_NOTE"] = "Por el rey, por la tierra, por las montañas.",
["TOWER_PALADIN_COVENANT_4_NAME"] = "Pacto de Paladines IV",
["TOWER_PALADIN_COVENANT_DESC"] = "Los Paladines son la base de las fuerzas de elite de Linirea y usan su poder divino para protegerse y curarse durante la batalla.",
["TOWER_PALADIN_COVENANT_NAME"] = "Pacto de Paladines",
["TOWER_PANDAS_1_DESCRIPTION"] = "Armados con dominio elemental y una determinación inquebrantable, los Maestros lucharán sin descanso para preservar el equilibrio natural del mundo.",
["TOWER_PANDAS_1_NAME"] = "Maestros del Bambú I",
["TOWER_PANDAS_2_DESCRIPTION"] = "Armados con dominio elemental y una determinación inquebrantable, los Maestros lucharán sin descanso para preservar el equilibrio natural del mundo.",
["TOWER_PANDAS_2_NAME"] = "Maestros del Bambú II",
["TOWER_PANDAS_3_DESCRIPTION"] = "Armados con dominio elemental y una determinación inquebrantable, los Maestros lucharán sin descanso para preservar el equilibrio natural del mundo.",
["TOWER_PANDAS_3_NAME"] = "Maestros del Bambú III",
["TOWER_PANDAS_4_DESCRIPTION"] = "Armados con dominio elemental y una determinación inquebrantable, los Maestros lucharán sin descanso para preservar el equilibrio natural del mundo.",
["TOWER_PANDAS_4_FIERY"] = "Kawoosh",
["TOWER_PANDAS_4_FIERY_1_DESCRIPTION"] = "Lanza un proyectil de fuego que inflige %$towers.pandas.soldier.teleport.damage_min[1]%$-%$towers.pandas.soldier.teleport.damage_max[1]%$ de daño verdadero y teletransporta a los enemigos alcanzados hacia atrás en el camino.",
["TOWER_PANDAS_4_FIERY_1_NAME"] = "Flama Abisal",
["TOWER_PANDAS_4_FIERY_2_DESCRIPTION"] = "Lanza una bola de fuego que inflige %$towers.pandas.soldier.teleport.damage_min[2]%$-%$towers.pandas.soldier.teleport.damage_max[2]%$ de daño verdadero y teletransporta a los enemigos afectados hacia atrás en el camino.",
["TOWER_PANDAS_4_FIERY_2_NAME"] = "Flama Abisal",
["TOWER_PANDAS_4_HAT"] = "Un sombrero para golpearlos a todos",
["TOWER_PANDAS_4_HAT_1_DESCRIPTION"] = "Lanza su sombrero afilado a un enemigo, rebotando entre enemigos y causando %$towers.pandas.soldier.hat.damage_levels[1].min%$-%$towers.pandas.soldier.hat.damage_levels[1].max%$ de daño con cada impacto.",
["TOWER_PANDAS_4_HAT_1_NAME"] = "Truco de Sombrero",
["TOWER_PANDAS_4_HAT_2_DESCRIPTION"] = "Lanza su sombrero afilado a un enemigo, rebotando entre enemigos y causando %$towers.pandas.soldier.hat.damage_levels[2].min%$-%$towers.pandas.soldier.hat.damage_levels[2].max%$ de daño con cada impacto.",
["TOWER_PANDAS_4_HAT_2_NAME"] = "Truco de Sombrero",
["TOWER_PANDAS_4_NAME"] = "Maestros del Bambú IV",
["TOWER_PANDAS_4_THUNDER"] = "Combate Panda",
["TOWER_PANDAS_4_THUNDER_1_DESCRIPTION"] = "Lanza rayos sobre un área pequeña, cada uno inflige %$towers.pandas.soldier.thunder.damage_min[1]%$-%$towers.pandas.soldier.thunder.damage_max[1]%$ de daño de área y aturde brevemente a los enemigos alcanzados.",
["TOWER_PANDAS_4_THUNDER_1_NAME"] = "Sobrecarga Eléctrica",
["TOWER_PANDAS_4_THUNDER_2_DESCRIPTION"] = "Lanza rayos sobre un área pequeña, cada uno inflige %$towers.pandas.soldier.thunder.damage_min[2]%$-%$towers.pandas.soldier.thunder.damage_max[2]%$ de daño de área y aturde brevemente a los enemigos alcanzados.",
["TOWER_PANDAS_4_THUNDER_2_NAME"] = "Sobrecarga de rayos",
["TOWER_PANDAS_DESC"] = "Fusionando destreza marcial con afinidad elemental, este trío de pandas arrasa con los enemigos y sigue siendo una amenaza incluso cuando parece derrotado.",
["TOWER_PANDAS_NAME"] = "Maestros del Bambú",
["TOWER_PANDAS_RETREAT_DESCRIPTION"] = "Retira a los pandas de pie al refugio durante 8 segundos.",
["TOWER_PANDAS_RETREAT_NAME"] = "Retirada Táctica",
["TOWER_PANDAS_RETREAT_NOTE"] = "La discreción es la mejor parte del valor.",
["TOWER_RAY_1_DESCRIPTION"] = "Las peligrosas y mancilladas formas de energía mágica nunca desalentaron a los magos malvados de perseguir oscuros propósitos.",
["TOWER_RAY_1_NAME"] = "Canalizador Espeluznante I",
["TOWER_RAY_2_DESCRIPTION"] = "Las peligrosas y mancilladas formas de energía mágica nunca desalentaron a los magos malvados de perseguir oscuros propósitos.",
["TOWER_RAY_2_NAME"] = "Canalizador Espeluznante II",
["TOWER_RAY_3_DESCRIPTION"] = "Las peligrosas y mancilladas formas de energía mágica nunca desalentaron a los magos malvados de perseguir oscuros propósitos.",
["TOWER_RAY_3_NAME"] = "Canalizador Espeluznante III",
["TOWER_RAY_4_CHAIN_1_DESCRIPTION"] = "El rayo ahora se extiende sobre %$towers.ray.skill_chain.s_max_enemies%$ enemigos adicionales, haciendo %$towers.ray.skill_chain.damage_mult[1]%$% del daño mágico total inicial a cada objetivo.",
["TOWER_RAY_4_CHAIN_1_NAME"] = "PODER DESBORDADO",
["TOWER_RAY_4_CHAIN_2_DESCRIPTION"] = "El rayo ahora se extiende sobre %$towers.ray.skill_chain.s_max_enemies%$ enemigos adicionales, haciendo %$towers.ray.skill_chain.damage_mult[2]%$% del daño mágico total inicial a cada objetivo.",
["TOWER_RAY_4_CHAIN_2_NAME"] = "PODER DESBORDADO",
["TOWER_RAY_4_CHAIN_3_DESCRIPTION"] = "El rayo ahora se extiende sobre %$towers.ray.skill_chain.s_max_enemies%$ enemigos adicionales, haciendo %$towers.ray.skill_chain.damage_mult[3]%$% del daño mágico total inicial a cada objetivo.",
["TOWER_RAY_4_CHAIN_3_NAME"] = "PODER DESBORDADO",
["TOWER_RAY_4_CHAIN_NOTE"] = "Hay suficiente dolor para todos.",
["TOWER_RAY_4_DESCRIPTION"] = "Las peligrosas y mancilladas formas de energía mágica nunca desalentaron a los magos malvados de perseguir oscuros propósitos.",
["TOWER_RAY_4_NAME"] = "Canalizador Espeluznante IV",
["TOWER_RAY_4_SHEEP_1_DESCRIPTION"] = "Convierte a un enemigo cercano en una oveja indefensa. La oveja tiene %$towers.ray.skill_sheep.sheep.hp_mult%$% de la vida del objetivo.",
["TOWER_RAY_4_SHEEP_1_NAME"] = "MALEFICIO DE MUTACIÓN",
["TOWER_RAY_4_SHEEP_2_DESCRIPTION"] = "Convierte a un enemigo cercano en una oveja indefensa. La oveja tiene %$towers.ray.skill_sheep.sheep.hp_mult%$% de la vida del objetivo.",
["TOWER_RAY_4_SHEEP_2_NAME"] = "MALEFICIO DE MUTACIÓN",
["TOWER_RAY_4_SHEEP_3_DESCRIPTION"] = "Convierte a un enemigo cercano en una oveja indefensa. La oveja tiene %$towers.ray.skill_sheep.sheep.hp_mult%$% de la vida del objetivo.",
["TOWER_RAY_4_SHEEP_3_NAME"] = "MALEFICIO DE MUTACIÓN",
["TOWER_RAY_4_SHEEP_NOTE"] = "Honestamente, te ves mejor ahora.",
["TOWER_RAY_DESC"] = "Los aprendices de Vez'nan utilizan su poder corrupto para lanzar un oscuro rayo de aflicción sobre sus enemigos.",
["TOWER_RAY_NAME"] = "Canalizador Espeluznante",
["TOWER_ROCKET_GUNNERS_1_DESCRIPTION"] = "Equipados con la tecnología más reciente del Ejército Oscuro, los Tiradores patrullan los cielos.",
["TOWER_ROCKET_GUNNERS_1_NAME"] = "Tiradores Cohete I",
["TOWER_ROCKET_GUNNERS_2_DESCRIPTION"] = "Equipados con la tecnología más reciente del Ejército Oscuro, los Tiradores patrullan los cielos.",
["TOWER_ROCKET_GUNNERS_2_NAME"] = "Tiradores Cohete II",
["TOWER_ROCKET_GUNNERS_3_DESCRIPTION"] = "Equipados con la tecnología más reciente del Ejército Oscuro, los Tiradores patrullan los cielos.",
["TOWER_ROCKET_GUNNERS_3_NAME"] = "Tiradores Cohete III",
["TOWER_ROCKET_GUNNERS_4_DESCRIPTION"] = "Equipados con la tecnología más reciente del Ejército Oscuro, los Tiradores patrullan los cielos.",
["TOWER_ROCKET_GUNNERS_4_NAME"] = "Tiradores Cohete IV",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_DESCRIPTION"] = "Los ataques destruyen un %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[1]%$% de la armadura de los enemigos y hacen daño en área.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_NAME"] = "RECUBRIMIENTO FOSFÓRICO",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_DESCRIPTION"] = "Los ataques destruyen un %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[2]%$% de la armadura de los enemigos y hacen %$towers.rocket_gunners.soldier.phosphoric.damage_area_min[2]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[2]%$ daño en área.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_NAME"] = "RECUBRIMIENTO FOSFÓRICO",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_DESCRIPTION"] = "Los ataques destruyen un %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[3]%$% de la armadura de los enemigos y hacen %$towers.rocket_gunners.soldier.phosphoric.damage_area_min[3]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[3]%$ daño en área.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_NAME"] = "RECUBRIMIENTO FOSFÓRICO",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_NOTE"] = "Balas condimentadas con malicia.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_DESCRIPTION"] = "Dispara un misil que mata instantáneamente a un enemigo de hasta %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[1]%$ de vida.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_NAME"] = "MISIL STINGER",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_DESCRIPTION"] = "Reduce el enfríamiento a %$towers.rocket_gunners.sting_missiles.cooldown[2]%$ segundos. Ahora escoge enemigos de hasta %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[2]%$ de vida.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_NAME"] = "MISIL STINGER",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_DESCRIPTION"] = "Reduce el enfríamiento a %$towers.rocket_gunners.sting_missiles.cooldown[3]%$ segundos. Ahora escoge enemigos de hasta %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[3]%$ de vida.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_NAME"] = "MISIL STINGER",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_NOTE"] = "¡Esquiva esto!",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_DESCRIPTION"] = "Los Pistoleros despegan y no pueden bloquear enemigos.",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NAME"] = "Despegue",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NOTE"] = "¡Al infinito y más allá!",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_DESCRIPTION"] = "Los Pistoleros aterrizan y pueden bloquear enemigos.",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NAME"] = "Aterrizaje",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NOTE"] = "¡El águila ha aterrizado!",
["TOWER_ROCKET_GUNNERS_DESC"] = "Estas tropas especiales son efectivas tanto en aire como en tierra, desatando el caos con sus armas avanzadas.",
["TOWER_ROCKET_GUNNERS_NAME"] = "Tiradores Cohete",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "Esta torre está incluida en la campaña de Amenaza Colosal.",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "Esta torre está incluida en la campaña El Viaje de Wukong.",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "Campaña de Amenaza Colosal",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "Campaña El Viaje de Wukong",
["TOWER_ROOM_EQUIPPED_TOWERS_TITLE"] = "Torres equipadas",
["TOWER_ROOM_GET_DLC"] = "CONSÍGUELO",
["TOWER_ROOM_LABEL_ROSTER_THUMB_NEW"] = "¡Nuevo!",
["TOWER_ROOM_SKILLS_TITLE"] = "Habilidades",
["TOWER_ROYAL_ARCHERS_1_DESCRIPTION"] = "Leales hasta el final, los Arqueros Reales protegen a las fuerzas de Linirea a la distancia.",
["TOWER_ROYAL_ARCHERS_1_NAME"] = "Arqueros Reales I",
["TOWER_ROYAL_ARCHERS_2_DESCRIPTION"] = "Leales hasta el final, los Arqueros Reales protegen a las fuerzas de Linirea a la distancia.",
["TOWER_ROYAL_ARCHERS_2_NAME"] = "Arqueros Reales II",
["TOWER_ROYAL_ARCHERS_3_DESCRIPTION"] = "Leales hasta el final, los Arqueros Reales protegen a las fuerzas de Linirea a la distancia.",
["TOWER_ROYAL_ARCHERS_3_NAME"] = "Arqueros Reales III",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_DESCRIPTION"] = "Dispara tres poderosas flechas que hacen %$towers.royal_archers.armor_piercer.damage_min[1]%$-%$towers.royal_archers.armor_piercer.damage_max[1]%$ de daño físico e ignoran %$towers.royal_archers.armor_piercer.armor_penetration[1]%$% de armadura.",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_NAME"] = "PERFORADOR",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_DESCRIPTION"] = "Dispara tres poderosas flechas que hacen %$towers.royal_archers.armor_piercer.damage_min[2]%$-%$towers.royal_archers.armor_piercer.damage_max[2]%$ de daño físico, ignorando %$towers.royal_archers.armor_piercer.armor_penetration[2]%$% de la armadura enemiga.",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_NAME"] = "PERFORADOR",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_DESCRIPTION"] = "Dispara tres poderosas flechas que hacen %$towers.royal_archers.armor_piercer.damage_min[3]%$-%$towers.royal_archers.armor_piercer.damage_max[3]%$ de daño físico, ignorando %$towers.royal_archers.armor_piercer.armor_penetration[3]%$% de la armadura enemiga.",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_NAME"] = "PERFORADOR",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NAME"] = "PERFORADOR",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NOTE"] = "Te tenemos en la mira.",
["TOWER_ROYAL_ARCHERS_4_DESCRIPTION"] = "Leales hasta el final, los Arqueros Reales protegen a las fuerzas de Linirea a la distancia.",
["TOWER_ROYAL_ARCHERS_4_NAME"] = "Arqueros Reales IV",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_DESCRIPTION"] = "Invoca a un águila que ataca a los enemigos en el camino, haciendo %$towers.royal_archers.rapacious_hunter.damage_min[1]%$-%$towers.royal_archers.rapacious_hunter.damage_max[1]%$ de daño físico.",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_NAME"] = "CAZADOR RAPAZ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_DESCRIPTION"] = "El águila hace %$towers.royal_archers.rapacious_hunter.damage_min[2]%$-%$towers.royal_archers.rapacious_hunter.damage_max[2]%$ de daño físico a los enemigos.",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_NAME"] = "CAZADOR RAPAZ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_DESCRIPTION"] = "El águila hace %$towers.royal_archers.rapacious_hunter.damage_min[3]%$-%$towers.royal_archers.rapacious_hunter.damage_max[3]%$ de daño físico a los enemigos.",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_NAME"] = "CAZADOR RAPAZ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NAME"] = "CAZADOR RAPAZ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NOTE"] = "El ojo del águila esconde algo trágico.",
["TOWER_ROYAL_ARCHERS_DESC"] = "Los arqueros más fuertes y renombrados del reino, asistidos por águilas de guerra.",
["TOWER_ROYAL_ARCHERS_NAME"] = "Arqueros Reales",
["TOWER_SAND_1_DESCRIPTION"] = "Su habilidad con los cuchillos arrojadizos es suficiente como para amedrentar a cualquier mercenario creído.",
["TOWER_SAND_1_NAME"] = "Centinelas de las Dunas I",
["TOWER_SAND_2_DESCRIPTION"] = "Su habilidad con los cuchillos arrojadizos es suficiente como para amedrentar a cualquier mercenario creído.",
["TOWER_SAND_2_NAME"] = "Centinelas de las Dunas II",
["TOWER_SAND_3_DESCRIPTION"] = "Su habilidad con los cuchillos arrojadizos es suficiente como para amedrentar a cualquier mercenario creído.",
["TOWER_SAND_3_NAME"] = "Centinelas de las Dunas III",
["TOWER_SAND_4_DESCRIPTION"] = "Su habilidad con los cuchillos arrojadizos es suficiente como para amedrentar a cualquier mercenario creído.",
["TOWER_SAND_4_NAME"] = "Centinelas de las Dunas IV",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_DESCRIPTION"] = "Dispara cuchillas giratorias en el camino, haciendo %$towers.sand.skill_big_blade.s_damage_min[1]%$-%$towers.sand.skill_big_blade.s_damage_max[1]%$ de daño físico por segundo durante %$towers.sand.skill_big_blade.duration[1]%$ segundos.",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_NAME"] = "TORBELLINO FATAL",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_DESCRIPTION"] = "Las cuchillas giratorias hacen %$towers.sand.skill_big_blade.s_damage_min[2]%$-%$towers.sand.skill_big_blade.s_damage_max[2]%$ de daño físico por segundo durante %$towers.sand.skill_big_blade.duration[2]%$ segundos.",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_NAME"] = "TORBELLINO FATAL",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_DESCRIPTION"] = "Las cuchillas giratorias hacen %$towers.sand.skill_big_blade.s_damage_min[3]%$-%$towers.sand.skill_big_blade.s_damage_max[3]%$ de daño físico por segundo durante %$towers.sand.skill_big_blade.duration[3]%$ segundos.",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_NAME"] = "TORBELLINO FATAL",
["TOWER_SAND_4_SKILL_BIG_BLADE_NOTE"] = "Me haces girar y girar, baby.",
["TOWER_SAND_4_SKILL_GOLD_1_DESCRIPTION"] = "Lanza un cuchillo rebotante que hace %$towers.sand.skill_gold.s_damage[1]%$ de daño físico. Los enemigos eliminados por el cuchillo dan %$towers.sand.skill_gold.gold_extra[1]%$ oro extra.",
["TOWER_SAND_4_SKILL_GOLD_1_NAME"] = "CAZARRECOMPENSAS",
["TOWER_SAND_4_SKILL_GOLD_2_DESCRIPTION"] = "El cuchillo hace %$towers.sand.skill_gold.s_damage[2]%$ de daño físico. Una muerte enemiga da %$towers.sand.skill_gold.gold_extra[2]%$ de oro extra.",
["TOWER_SAND_4_SKILL_GOLD_2_NAME"] = "CAZARRECOMPENSAS",
["TOWER_SAND_4_SKILL_GOLD_3_DESCRIPTION"] = "El cuchillo hace %$towers.sand.skill_gold.s_damage[3]%$ de daño físico. Una muerte enemiga da %$towers.sand.skill_gold.gold_extra[3]%$ de oro extra.",
["TOWER_SAND_4_SKILL_GOLD_3_NAME"] = "CAZARRECOMPENSAS",
["TOWER_SAND_4_SKILL_GOLD_NOTE"] = "El folleto dice vivo O muerto.",
["TOWER_SAND_DESC"] = "Oriundas de Hammerhold, las Centinelas de las Dunas deben ser las más mortíferas habitantes del desierto.",
["TOWER_SAND_NAME"] = "Centinelas de las Dunas",
["TOWER_SELL"] = "Vender torre",
["TOWER_SPARKING_GEODE_1_DESCRIPTION"] = "Invocador de tormentas y portador de caos certificado. Ten cuidado con su consumo de energía.",
["TOWER_SPARKING_GEODE_1_NAME"] = "Coloso Tormenta I",
["TOWER_SPARKING_GEODE_2_DESCRIPTION"] = "Invocador de tormentas y portador de caos certificado. Ten cuidado con su consumo de energía.",
["TOWER_SPARKING_GEODE_2_NAME"] = "Coloso Tormenta II",
["TOWER_SPARKING_GEODE_3_DESCRIPTION"] = "Invocador de tormentas y portador de caos certificado. Ten cuidado con su consumo de energía.",
["TOWER_SPARKING_GEODE_3_NAME"] = "Coloso Tormenta III",
["TOWER_SPARKING_GEODE_4_CRISTALIZE"] = "¡Rayo!",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_DESCRIPTION"] = "Cada %$towers.sparking_geode.crystalize.cooldown[1]%$ segundos, cristaliza a %$towers.sparking_geode.crystalize.max_targets[1]%$ enemigos dentro de su alcance, aturdiéndolos y haciendo que reciban %$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$% más de daño.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_NAME"] = "Cristalización",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_DESCRIPTION"] = "Cada %$towers.sparking_geode.crystalize.cooldown[2]%$ segundos, cristaliza a %$towers.sparking_geode.crystalize.max_targets[2]%$ enemigos dentro de su alcance, aturdiéndolos y haciendo que reciban un %$towers.sparking_geode.crystalize.s_received_damage_factor[2]%$% más de daño.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_NAME"] = "Cristalización",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_DESCRIPTION"] = "Cada %$towers.sparking_geode.crystalize.cooldown[3]%$ segundos, cristaliza a %$towers.sparking_geode.crystalize.max_targets[3]%$ enemigos dentro de su alcance, aturdiéndolos y haciendo que reciban un %$towers.sparking_geode.crystalize.s_received_damage_factor[3]%$% más de daño.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_NAME"] = "Cristalización",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_DESCRIPTION"] = "Cada %$towers.sparking_geode.crystalize.cooldown[1]%$ segundos, cristaliza a %$towers.sparking_geode.crystalize.max_targets[1]%$ enemigos dentro de su alcance, aturdiéndolos y haciendo que reciban un %$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$% más de daño.",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_NAME"] = "Cristalización",
["TOWER_SPARKING_GEODE_4_DESCRIPTION"] = "Invocador de tormentas y portador del caos certificado. Ten cuidado con su consumo de energía.",
["TOWER_SPARKING_GEODE_4_NAME"] = "Coloso Tormenta IV",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST"] = "Más duro, mejor, más rápido, más fuerte.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_DESCRIPTION"] = "Cada %$towers.sparking_geode.spike_burst.cooldown[1]%$ segundos, el Coloso invoca un campo eléctrico que daña y ralentiza a los enemigos cercanos durante %$towers.sparking_geode.spike_burst.duration[1]%$ segundos.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_NAME"] = "Sobrecarga Eléctrica",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_DESCRIPTION"] = "Cada %$towers.sparking_geode.spike_burst.cooldown[2]%$ segundos, el Coloso invoca un campo eléctrico que daña y ralentiza a los enemigos cercanos durante %$towers.sparking_geode.spike_burst.duration[2]%$ segundos.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_NAME"] = "Sobrecarga Eléctrica",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_DESCRIPTION"] = "Cada %$towers.sparking_geode.spike_burst.cooldown[3]%$ segundos, el Coloso invoca un campo eléctrico que daña y ralentiza a los enemigos cercanos durante %$towers.sparking_geode.spike_burst.duration[3]%$ segundos.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_NAME"] = "Sobrecarga Eléctrica",
["TOWER_SPARKING_GEODE_DESC"] = "Originario de una antigua raza pacífica, este poderoso ser sigue su instinto protector y usa sus poderes eléctricos para luchar por la Alianza, atacando con la furia de una tormenta.",
["TOWER_SPARKING_GEODE_NAME"] = "Coloso Tormenta",
["TOWER_STAGE_13_SUNRAY_NAME"] = "Torre Rayoscuro",
["TOWER_STAGE_13_SUNRAY_REPAIR_DESCRIPTION"] = "Repara la torre para utilizar sus poderes destructivos.",
["TOWER_STAGE_13_SUNRAY_REPAIR_NAME"] = "Reparar",
["TOWER_STAGE_17_WEIRDWOOD_NAME"] = "Bosquextraño",
["TOWER_STAGE_18_ELVEN_BARRACK_DESCRIPTION"] = "Elfos contratados para luchar.",
["TOWER_STAGE_18_ELVEN_BARRACK_NAME"] = "Mercenarios Elfos",
["TOWER_STAGE_20_ARBOREAN_BARRACK_DESCRIPTION"] = "Llama al pueblo arbóreo a luchar.",
["TOWER_STAGE_20_ARBOREAN_BARRACK_NAME"] = "Ciudadanos Arbóreos",
["TOWER_STAGE_20_ARBOREAN_HONEY_DESCRIPTION"] = "Invoca al gran comandante de las abejas.",
["TOWER_STAGE_20_ARBOREAN_HONEY_NAME"] = "Apicultor Arbóreo",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_DESCRIPTION"] = "Pide ayuda al árbol antiguo.",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_NAME"] = "Árbol Viejo",
["TOWER_STAGE_22_ARBOREAN_MAGES_NAME"] = "Mago Arbóreo",
["TOWER_STAGE_28_PRIESTS_BARRACK_DESCRIPTION"] = "Cultistas redimidos que traen su magia al campo de batalla y se transforman en abominaciones al morir.",
["TOWER_STAGE_28_PRIESTS_BARRACK_NAME"] = "Creyentes del sin ojos",
["TOWER_STARGAZER_1_DESCRIPTION"] = "Las Miraestrellas buscan magia poderosa más allá del reino terrenal.",
["TOWER_STARGAZER_1_NAME"] = "Miraestrellas Elfo I",
["TOWER_STARGAZER_2_DESCRIPTION"] = "Las Miraestrellas buscan magia poderosa más allá del reino terrenal.",
["TOWER_STARGAZER_2_NAME"] = "Miraestrellas Elfo II",
["TOWER_STARGAZER_3_DESCRIPTION"] = "Las Miraestrellas buscan magia poderosa más allá del reino terrenal.",
["TOWER_STARGAZER_3_NAME"] = "Miraestrellas Elfo III",
["TOWER_STARGAZER_4_DESCRIPTION"] = "Las Miraestrellas buscan magia poderosa más allá del reino terrenal.",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_DESCRIPTION"] = "Teletransporta hasta %$towers.elven_stargazers.teleport.max_targets[1]%$ enemigos hacia atrás en el camino.",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_NAME"] = "HORIZONTE DE EVENTOS",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_DESCRIPTION"] = "Teletransporta hasta %$towers.elven_stargazers.teleport.max_targets[2]%$ enemigos hacia más atrás en el camino.",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_NAME"] = "HORIZONTE DE EVENTOS",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_DESCRIPTION"] = "Teletransporta hasta %$towers.elven_stargazers.teleport.max_targets[3]%$ enemigos incluso más atrás en el camino.",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_NAME"] = "HORIZONTE DE EVENTOS",
["TOWER_STARGAZER_4_EVENT_HORIZON_NAME"] = "HORIZONTE DE EVENTOS",
["TOWER_STARGAZER_4_EVENT_HORIZON_NOTE"] = "Desaparecer, aparecer.",
["TOWER_STARGAZER_4_NAME"] = "Miraestrellas Elfo IV",
["TOWER_STARGAZER_4_RISING_STAR_1_DESCRIPTION"] = "Enemigos eliminados por esta torre explotan en una ráfaga de %$towers.elven_stargazers.stars_death.stars[1]%$ estrellas que se lanzan hacia otros enemigos, haciendo %$towers.elven_stargazers.stars_death.damage_min[1]%$-%$towers.elven_stargazers.stars_death.damage_max[1]%$ de daño mágico.",
["TOWER_STARGAZER_4_RISING_STAR_1_NAME"] = "ESTRELLA ASCENDENTE",
["TOWER_STARGAZER_4_RISING_STAR_2_DESCRIPTION"] = "Incrementa la cantidad de estrellas a %$towers.elven_stargazers.stars_death.stars[2]%$. Las estrellas hacen %$towers.elven_stargazers.stars_death.damage_min[2]%$-%$towers.elven_stargazers.stars_death.damage_max[2]%$ de daño mágico.",
["TOWER_STARGAZER_4_RISING_STAR_2_NAME"] = "ESTRELLA ASCENDENTE",
["TOWER_STARGAZER_4_RISING_STAR_3_DESCRIPTION"] = "Incrementa la cantidad de estrellas a %$towers.elven_stargazers.stars_death.stars[3]%$. Las estrellas hacen %$towers.elven_stargazers.stars_death.damage_min[3]%$-%$towers.elven_stargazers.stars_death.damage_max[3]%$ de daño mágico.",
["TOWER_STARGAZER_4_RISING_STAR_3_NAME"] = "ESTRELLA ASCENDENTE",
["TOWER_STARGAZER_4_RISING_STAR_NAME"] = "ESTRELLA ASCENDENTE",
["TOWER_STARGAZER_4_RISING_STAR_NOTE"] = "¡Es una revolución estelar!",
["TOWER_TRICANNON_1_DESCRIPTION"] = "Una oda devastadora a la guerra y una presencia que infunde miedo tanto en enemigos como aliados.",
["TOWER_TRICANNON_1_NAME"] = "Tricañón I",
["TOWER_TRICANNON_2_DESCRIPTION"] = "Una oda devastadora a la guerra y una presencia que infunde miedo tanto en enemigos como aliados.",
["TOWER_TRICANNON_2_NAME"] = "Tricañón II",
["TOWER_TRICANNON_3_DESCRIPTION"] = "Una oda devastadora a la guerra y una presencia que infunde miedo tanto en enemigos como aliados.",
["TOWER_TRICANNON_3_NAME"] = "Tricañón III",
["TOWER_TRICANNON_4_BOMBARDMENT_1_DESCRIPTION"] = "Dispara bombas en rápida sucesión y cubriendo un área más extensa, haciendo %$towers.tricannon.bombardment.damage_min[1]%$-%$towers.tricannon.bombardment.damage_max[1]%$ de daño cada una.",
["TOWER_TRICANNON_4_BOMBARDMENT_1_NAME"] = "BOMBARDEO",
["TOWER_TRICANNON_4_BOMBARDMENT_2_DESCRIPTION"] = "Dispara más bombas en un área más extensa. Cada una hace %$towers.tricannon.bombardment.damage_min[2]%$-%$towers.tricannon.bombardment.damage_max[2]%$ de daño físico.",
["TOWER_TRICANNON_4_BOMBARDMENT_2_NAME"] = "BOMBARDEO",
["TOWER_TRICANNON_4_BOMBARDMENT_3_DESCRIPTION"] = "Dispara aún más bombas en un área más extensa. Cada una hace %$towers.tricannon.bombardment.damage_min[3]%$-%$towers.tricannon.bombardment.damage_max[3]%$ de daño físico.",
["TOWER_TRICANNON_4_BOMBARDMENT_3_NAME"] = "BOMBARDEO",
["TOWER_TRICANNON_4_BOMBARDMENT_NAME"] = "BOMBARDEO",
["TOWER_TRICANNON_4_BOMBARDMENT_NOTE"] = "Hablemos de escalabilidad.",
["TOWER_TRICANNON_4_DESCRIPTION"] = "Una oda devastadora a la guerra y una presencia que infunde miedo tanto en enemigos como aliados.",
["TOWER_TRICANNON_4_NAME"] = "Tricañon IV",
["TOWER_TRICANNON_4_OVERHEAT_1_DESCRIPTION"] = "Sobrecalienta los cañones al rojo vivo por %$towers.tricannon.overheat.duration[1]%$ segundos, haciendo que las bombas incendien el área para hacer %$towers.tricannon.overheat.decal.effect.s_damage[1]%$ de daño verdadero por segundo a los enemigos.",
["TOWER_TRICANNON_4_OVERHEAT_1_NAME"] = "RECALENTAMIENTO",
["TOWER_TRICANNON_4_OVERHEAT_2_DESCRIPTION"] = "Cada área de fuego hace %$towers.tricannon.overheat.decal.effect.s_damage[2]%$ de daño verdadero por segundo. La duración se incrementa a %$towers.tricannon.overheat.duration[2]%$ segundos.",
["TOWER_TRICANNON_4_OVERHEAT_2_NAME"] = "RECALENTAMIENTO",
["TOWER_TRICANNON_4_OVERHEAT_3_DESCRIPTION"] = "Cada área de fuego hace %$towers.tricannon.overheat.decal.effect.s_damage[3]%$ de daño verdadero por segundo. La duración se incrementa a %$towers.tricannon.overheat.duration[3]%$ segundos.",
["TOWER_TRICANNON_4_OVERHEAT_3_NAME"] = "RECALENTAMIENTO",
["TOWER_TRICANNON_4_OVERHEAT_NAME"] = "RECALENTAMIENTO",
["TOWER_TRICANNON_4_OVERHEAT_NOTE"] = "Estamos al rojo vivo.",
["TOWER_TRICANNON_DESC"] = "El último aporte del Ejército Oscuro a la guerra moderna hace llover fuego y destrucción gracias a sus múltiples cañones.",
["TOWER_TRICANNON_NAME"] = "Tricañón",
["TUTORIAL_hero_room_hero_points_desc"] = "Obtén Puntos de Héroe subiendo de nivel a cada héroe en combate.",
["TUTORIAL_hero_room_hero_points_title"] = "Puntos de Héroe",
["TUTORIAL_hero_room_power_desc"] = "Usa los Puntos de Héroe para comprar y mejorar los poderes de tu héroe.",
["TUTORIAL_hero_room_power_title"] = "Poderes de Héroe",
["TUTORIAL_hero_room_tutorial_navigate_desc"] = "Navega entre los diferentes héroes.",
["TUTORIAL_hero_room_tutorial_select_desc"] = "Selecciona los héroes que quieras usar en el campo de batalla.",
["TUTORIAL_item_room_buy_desc"] = "Utiliza las Gemas para comprar ítems que te ayuden durante la batalla.",
["TUTORIAL_item_room_buy_title"] = "Comprar Ítems",
["TUTORIAL_item_room_tutorial_equip_desc"] = "Utiliza las ranuras para equipar los ítems. Arrastra para cambiarlos.",
["TUTORIAL_item_room_tutorial_navigate_desc"] = "Navega entre los ítems disponibles.",
["TUTORIAL_tower_room_power_desc"] = "Estos poderes se desbloquearán cuando la torre llegue a nivel 4.",
["TUTORIAL_tower_room_power_title"] = "Poderes de nivel IV",
["TUTORIAL_tower_room_tutorial_equip_desc"] = "Puedes equipar nuevas torres para probar diferentes combinaciones.",
["TUTORIAL_tower_room_tutorial_navigate_desc"] = "Navega entre las diferentes torres.",
["TUTORIAL_tower_room_tutorial_slots_desc"] = "Usa las ranuras para equipar las torres. Arrastra para cambiarlas.",
["TUTORIAL_upgrade_room_tooltip_buy_desc"] = "Utiliza Puntos para comprar mejoras para tus poderes, torres y héroes.",
["TUTORIAL_upgrade_room_tooltip_souls_desc"] = "Obtén Puntos de Mejora completando niveles de campaña.",
["TUTORIAL_upgrade_room_tooltip_souls_title"] = "Puntos de Mejora",
["Tap the road!"] = "¡Toca en el camino!",
["Tip"] = "Consejo",
["Tower construction"] = "Construir torres",
["Towers"] = "Torres",
["Try again"] = "Intenta de nuevo",
["Typography"] = "Tipografía",
["UPDATE_POPUP"] = "ACTUALIZAR",
["UPDATING_CLOUDSAVE_MESSAGE"] = "Actualizando partidas guardadas en la nube...",
["UPGRADES"] = "MEJORAS",
["UPGRADES AND HEROES RESTRICTIONS!"] = "¡RESTRICCIONES EN HÉROES Y MEJORAS!",
["UPGRADE_LEVEL"] = "nivel de mejora",
["Undo"] = "Deshacer",
["Unlocks at Level"] = "Se desbloquea en el nivel",
["Upgrades"] = "Mejoras",
["Use the earned hero points to train your hero!"] = "¡Usa los puntos de héroe para entrenar a tu héroe!",
["Use the earned stars to improve your towers and powers!"] = "¡Usa las estrellas para mejorar tus torres y hechizos!",
["VICTORY"] = "VICTORIA",
["Very fast"] = "Muy rápida",
["Very slow"] = "Muy lenta",
["Veteran"] = "Veterano",
["Victory!"] = "¡Victoria!",
["Voice Talent"] = "Voces",
["WARNING"] = "ADVERTENCIA",
["WAVE_TOOLTIP_TAP_AGAIN"] = "CLIC PARA LLAMAR ENEMIGOS ANTES",
["WAVE_TOOLTIP_TITLE"] = "SE APROXIMA UNA OLEADA",
["We would like to thank"] = "Agradecimientos Especiales",
["Yes"] = "Sí",
["You can always change the difficulty in the options menu."] = "Siempre puedes cambiar la dificultad en el menú de opciones.",
["_manually_included_characters"] = "$ ¥ ￥ ƒ ₩ € ™ × $ zł ¢ £ ¤ ¥ ƒ ден дин лв. ؋ ৳ ฿ ლ ₡ ₣ ₤ ₥ ₦ ₨ ₩ ₪ ₫ € ₭ ₮ ₱ ₲ ₴ ₵ ₹ ₺ ₽ ﷼",
["alliance_close_to_home_DESCRIPTION"] = "Comienza cada nivel con oro extra.",
["alliance_close_to_home_NAME"] = "RESERVAS COMPARTIDAS",
["alliance_corageous_stand_DESCRIPTION"] = "Cada torre de LINIREA construida incrementa los puntos de vida de los héroes.",
["alliance_corageous_stand_NAME"] = "DEFENSA VALIENTE",
["alliance_display_of_true_might_dark_DESCRIPTION"] = "Los Poderes de Héroe del Ejército Oscuro ahora enlentecen a todos los enemigos en la pantalla.",
["alliance_display_of_true_might_dark_NAME"] = "MALDICIÓN OMINOSA",
["alliance_display_of_true_might_linirea_DESCRIPTION"] = "Los Poderes de Héroe de Linirea ahora también curan y reviven unidades de torre.",
["alliance_display_of_true_might_linirea_NAME"] = "BENDICIÓN DE VITALIDAD",
["alliance_flux_altering_coils_DESCRIPTION"] = "Reemplaza todas las banderas de salida con pilares arcanos que teletransportan enemigos hacia atrás en el camino.",
["alliance_flux_altering_coils_NAME"] = "PILARES ARCANOS",
["alliance_friends_of_the_crown_DESCRIPTION"] = "Cada héroe de LINIREA equipado reduce el costo de construir y mejorar torres.",
["alliance_friends_of_the_crown_NAME"] = "AMIGOS DE LA CORONA",
["alliance_merciless_DESCRIPTION"] = "Cada torre del EJÉRCITO OSCURO construida incrementa el daño de los héroes.",
["alliance_merciless_NAME"] = "DEFENSA DESPIADADA",
["alliance_seal_of_punishment_DESCRIPTION"] = "Crea un sello mágico al final de cada camino, dañando a los enemigos que pasen por encima.",
["alliance_seal_of_punishment_NAME"] = "SELLO DE CASTIGO",
["alliance_shady_company_DESCRIPTION"] = "Cada héroe del EJÉRCITO OSCURO equipado incrementa el daño de ataque de las torres.",
["alliance_shady_company_NAME"] = "COMPAÑÍA SOSPECHOSA",
["alliance_shared_reserves_DESCRIPTION"] = "Comienza cada nivel con oro extra.",
["alliance_shared_reserves_NAME"] = "RESERVAS COMPARTIDAS",
["build defensive towers along the road to stop them."] = "Construye torres defensivas a lo largo del camino para detenerlos.",
["build towers to defend the road."] = "Construye torres para defender el camino.",
["check the stage description to see:"] = "revisa la descripción del nivel para ver:",
["click these!"] = "¡Haz clic aquí!",
["click to continue..."] = "Haz clic para continuar...",
["deals area damage"] = "Inflige daño de área",
["don't let enemies past this point."] = "Que los enemigos no pasen de aquí.",
["earn gold by killing enemies."] = "Mata enemigos para conseguir oro.",
["good rate of fire"] = "Buena cadencia de tiro.",
["heroes_desperate_effort_DESCRIPTION"] = "Los ataques de los héroes ignoran 10% de las resistencias de los enemigos.",
["heroes_desperate_effort_NAME"] = "CONOCE AL ENEMIGO",
["heroes_lethal_focus_DESCRIPTION"] = "Los héroes hacen daño crítico en el 20% de sus ataques.",
["heroes_lethal_focus_NAME"] = "ENFOQUE LETAL",
["heroes_limit_pushing_DESCRIPTION"] = "Después de utilizar un Poder de Héroe cinco veces, su tiempo de enfríamiento se resetea instantáneamente.",
["heroes_limit_pushing_NAME"] = "EMPUJAR EL LÍMITE",
["heroes_lone_wolves_DESCRIPTION"] = "Los héroes ganan más experiencia cuando están lejos el uno del otro.",
["heroes_lone_wolves_NAME"] = "LOBOS SOLITARIOS",
["heroes_nimble_physique_DESCRIPTION"] = "Los héroes esquivan el 20% de los ataques enemigos.",
["heroes_nimble_physique_NAME"] = "FÍSICO ÁGIL",
["heroes_unlimited_vigor_DESCRIPTION"] = "Reduce el enfriamiento de todos los Poderes de Héroe.",
["heroes_unlimited_vigor_NAME"] = "VIGOR ILIMITADO",
["heroes_visual_learning_DESCRIPTION"] = "Los héroes ganan 10% de armadura cuando están cerca el uno del otro.",
["heroes_visual_learning_NAME"] = "ALMA CARITATIVA",
["high damage, armor piercing"] = "daño alto, perfora armadura",
["iron and heroic challenges may have restrictions on upgrades!"] = "¡Los desafíos de hierro y heroico pueden tener restricciones en las mejoras!",
["max lvl allowed"] = "nivel max. permitido",
["multi-shot, armor piercing"] = "disparo múltiple, perfora armaduras",
["no heroes"] = "Sin héroes",
["protect your lands from the enemy attacks."] = "Protege tus tierras de los ataques enemigos.",
["rally range"] = "punto de encuentro",
["ready for action!"] = "¡Listos para la acción!",
["reinforcements_intense_workout_DESCRIPTION"] = "Aumenta la cantidad de vida y duración de los refuerzos.",
["reinforcements_intense_workout_NAME"] = "ENTRENAMIENTO INTENSO",
["reinforcements_master_blacksmiths_DESCRIPTION"] = "Mejora el daño de ataque y la armadura de los refuerzos.",
["reinforcements_master_blacksmiths_NAME"] = "MAESTROS HERREROS",
["reinforcements_night_veil_DESCRIPTION"] = "Los Arqueros Sombríos tienen más rango y velocidad de ataque.",
["reinforcements_night_veil_NAME"] = "ARCOS CENICIENTOS",
["reinforcements_power_trio_DESCRIPTION"] = "Llamar a los refuerzos también invoca a un Caballero Ejemplar.",
["reinforcements_power_trio_NAME"] = "CABALLERO EJEMPLAR",
["reinforcements_power_trio_dark_DESCRIPTION"] = "Llamar a los refuerzos también invoca a un Llamacuervos Sombrío.",
["reinforcements_power_trio_dark_NAME"] = "LLAMACUERVOS SOMBRÍO",
["reinforcements_rebel_militia_DESCRIPTION"] = "Los refuerzos se convierten en Rebeldes Linireanos, tienen más vida y armadura.",
["reinforcements_rebel_militia_NAME"] = "MILICIA LINIREANA",
["reinforcements_shadow_archer_DESCRIPTION"] = "Los refuerzos se convierten en Arqueros Sombríos, pudiendo atacar a distancia y atacar a unidades aéreas.",
["reinforcements_shadow_archer_NAME"] = "ORDEN DE LAS SOMBRAS",
["reinforcements_thorny_armor_DESCRIPTION"] = "Los Rebeldes Linireanos reflejan parte del daño de cada ataque que reciben cuerpo a cuerpo.",
["reinforcements_thorny_armor_NAME"] = "ARMADURA CON PÚAS",
["resist damage from"] = "Resiste daño de",
["resists damage from"] = "Resiste \ndaño de",
["select the rally point control"] = "Selecciona el controlador del punto de encuentro.",
["select the tower you want to build!"] = "¡Selecciona la torre que deseas construir!",
["select where you want to move your soldiers"] = "¡Selecciona el lugar donde quieres mover tus tropas!",
["soldiers block enemies"] = "Los soldados bloquean enemigos.",
["some enemies enjoy different levels of magic resistance that protects them against magical attacks."] = "Hay enemigos que poseen distintos niveles de resistencia a la magia que los protegen de los ataques mágicos.",
["some enemies wear armor of different strengths that protects them against non-magical attacks."] = "Hay enemigos que usan armaduras de resistencia variada que los protegen de los ataques no mágicos.",
["tap these!"] = "¡Toca aquí!",
["this is a strategic point."] = "Este es un punto estratégico.",
["towers_favorite_customer_DESCRIPTION"] = "Al comprar el nivel final de una habilidad, reduce su costo en un 50 %.",
["towers_favorite_customer_NAME"] = "CLIENTE FAVORITO",
["towers_golden_time_DESCRIPTION"] = "Incrementa el oro extra ganado por llamar a una oleada de forma temprana.",
["towers_golden_time_NAME"] = "HORA DORADA",
["towers_improved_formulas_DESCRIPTION"] = "Maximiza el daño de TODAS las explosiones e incrementa su área de efecto.",
["towers_improved_formulas_NAME"] = "FÓRMULAS MEJORADAS",
["towers_keen_accuracy_DESCRIPTION"] = "Reduce el enfriamiento de TODAS las habilidades de torre en un 20%.",
["towers_keen_accuracy_NAME"] = "FERVOR DE BATALLA",
["towers_royal_training_DESCRIPTION"] = "Reduce el tiempo de regreso de las unidades de torres y el enfríamiento del poder de Refuerzos.",
["towers_royal_training_NAME"] = "LLAMADO A LA ACCIÓN",
["towers_scoping_mechanism_DESCRIPTION"] = "Incrementa el rango de ataque de TODAS las torres en 10%.",
["towers_scoping_mechanism_NAME"] = "MECANISMO DE APUNTADO",
["towers_war_rations_DESCRIPTION"] = "Incrementa la vida de TODAS las unidades de torres en 10%.",
["towers_war_rations_NAME"] = "RACIONES DE GUERRA",
["towers_wise_investment_DESCRIPTION"] = "Las torres reembolsan 90% de su costo al ser vendidas.",
["towers_wise_investment_NAME"] = "INVERSIÓN SABIA",
["wOOt!"] = "Opa la la!",
["you can adjust your soldiers rally point to make them defend a different area."] = "Puedes cambiar el punto de encuentro de tus soldados para que defiendan una zona distinta.",
}
