-- ------------------------------------------------
-- -- WARNING: DO NOT EDIT BY HAND                 
-- -- Generated by kr-i18n/tools/strings-export.lua
-- ------------------------------------------------
return {
["%d Life"] = "%d Life",
["%d Lives"] = "%d Lives",
["%i sec."] = "%i sec.",
["- if heroes are allowed"] = "- if heroes are allowed",
["- max lvl allowed"] = "- max lvl allowed",
["- max upgrade level allowed"] = "- max upgrade level allowed",
["- no heroes"] = "- no heroes",
["A good challenge!"] = "A good challenge!",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "Abominated <PERSON>",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "Abominated <PERSON>",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "Abominated Geoffrey",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "Constipated Nicholas",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "Edomination",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "Hobomination",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "Odomination",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "Abominated Cedric",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "Halbomination",
["ACHIEVEMENT"] = "ACHIEVEMENT",
["ACHIEVEMENTS"] = "ACHIEVEMENTS",
["ACHIEVEMENTS_TITLE"] = "ACHIEVEMENTS",
["ACHIEVEMENT_AGE_OF_HEROES_DESCRIPTION"] = "Win all the Heroic Mode campaign challenges.",
["ACHIEVEMENT_AGE_OF_HEROES_NAME"] = "Age of Heroes",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_DESCRIPTION"] = "Eliminate 182 Blinkers.",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_NAME"] = "All the Small Things",
["ACHIEVEMENT_ARACHNED_DESCRIPTION"] = "Defeat Mygale, the Spider Queen.",
["ACHIEVEMENT_ARACHNED_NAME"] = "A Farewell to Arms.",
["ACHIEVEMENT_A_COON_OF_SURPRISES_DESCRIPTION"] = "Help Fredo escape again. ",
["ACHIEVEMENT_A_COON_OF_SURPRISES_NAME"] = "A Cocoon of Surprises.",
["ACHIEVEMENT_A_TEST_OF_PROWESS_DESCRIPTION"] = "Win a stage with 3 stars.",
["ACHIEVEMENT_A_TEST_OF_PROWESS_NAME"] = "A Test of Prowess",
["ACHIEVEMENT_BREAKER_OF_CHAINS_DESCRIPTION"] = "Save the four elves in the Carmine Mines.",
["ACHIEVEMENT_BREAKER_OF_CHAINS_NAME"] = "Breaker of Chains",
["ACHIEVEMENT_BUTTERTENTACLES_DESCRIPTION"] = "Complete The Eyesore Tower while preventing Mydrias from ensnaring your units.",
["ACHIEVEMENT_BUTTERTENTACLES_NAME"] = "Slippery Soldiers",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_DESCRIPTION"] = "Defeat Seeress Mydrias.",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_NAME"] = "Bye, Bye, Beautiful",
["ACHIEVEMENT_CIRCLE_OF_LIFE_DESCRIPTION"] = "Attend the presentation of the newborn Arborean.",
["ACHIEVEMENT_CIRCLE_OF_LIFE_NAME"] = "Circle of Life",
["ACHIEVEMENT_CLEANSE_THE_KING_DESCRIPTION"] = "Rescue the Linirean King.",
["ACHIEVEMENT_CLEANSE_THE_KING_NAME"] = "Glory to the King",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_DESCRIPTION"] = "Complete the Ravaged Outskirts without clearing rubble from the strategic points.",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_NAME"] = "Cleanup is Optional",
["ACHIEVEMENT_CONJUNTIVICTORY_DESCRIPTION"] = "Defeat the Overseer.",
["ACHIEVEMENT_CONJUNTIVICTORY_NAME"] = "Conjunctivictory",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_DESCRIPTION"] = "Get 3 stars on every stage of the Void Beyond.",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_NAME"] = "Conqueror of the Void",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_DESCRIPTION"] = "Collect all three pork chops in the Wildbeast Den.",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_NAME"] = "Crafting in the Mines",
["ACHIEVEMENT_CROWD_CONTROL_DESCRIPTION"] = "Complete the Corruption Valley without any Flesh Behemoth spawning from the pit.",
["ACHIEVEMENT_CROWD_CONTROL_NAME"] = "Crowd Control",
["ACHIEVEMENT_CROW_SCARER_DESCRIPTION"] = "Scare all the crows in Bleak Valley.",
["ACHIEVEMENT_CROW_SCARER_NAME"] = "Crow Scarer",
["ACHIEVEMENT_CRYSTAL_CLEAR_DESCRIPTION"] = "Get 3 stars on every stage of the Forsaken Canyon.",
["ACHIEVEMENT_CRYSTAL_CLEAR_NAME"] = "Crystal Clear",
["ACHIEVEMENT_DARK_LIEUTENANT_DESCRIPTION"] = "Reach level 10 with Raelyn.",
["ACHIEVEMENT_DARK_LIEUTENANT_NAME"] = "Dark Lieutenant",
["ACHIEVEMENT_DARK_RUTHLESSNESS_DESCRIPTION"] = "Win a stage using only Dark Army towers and heroes.",
["ACHIEVEMENT_DARK_RUTHLESSNESS_NAME"] = "Dark Ruthlessness",
["ACHIEVEMENT_DISTURBING_THE_PEACE_DESCRIPTION"] = "Interrupt the workers' lunch break in the Dominion Dome.",
["ACHIEVEMENT_DISTURBING_THE_PEACE_NAME"] = "Disturbing the Peace",
["ACHIEVEMENT_DLC1_WIN_BOSS_DESCRIPTION"] = "Defeat Grymbeard and stop the construction of the war machine.",
["ACHIEVEMENT_DLC1_WIN_BOSS_NAME"] = "Self-Unemployment",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_DESCRIPTION"] = "Collect 8 Hongbaos on Tempest Island.",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_NAME"] = "Wishing You Wealth and Prosperity",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_DESCRIPTION"] = "Defeat The Bull Demon King at his fortress.",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_NAME"] = "The Return of the Monkey King",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_DESCRIPTION"] = "Defeat Princess Iron Fan and her water army.",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_NAME"] = "An Evil Wind is Rising",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_DESCRIPTION"] = "Defeat Red Boy and his fire army.",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_NAME"] = "Everything Changed...",
["ACHIEVEMENT_DOMO_ARIGATO_DESCRIPTION"] = "Get 20 enemies crushed by the giant fist in the Colossal Core.",
["ACHIEVEMENT_DOMO_ARIGATO_NAME"] = "Domo Arigato",
["ACHIEVEMENT_FACTORY_STRIKE_DESCRIPTION"] = "Reach the bossfight in Frantic Assembly without letting Grymbeard activate the conveyor belt.",
["ACHIEVEMENT_FACTORY_STRIKE_NAME"] = "Factory Strike",
["ACHIEVEMENT_FIELD_TRIP_RUINER_DESCRIPTION"] = "Put out the camper's fire.",
["ACHIEVEMENT_FIELD_TRIP_RUINER_NAME"] = "Field Trip Ruiner",
["ACHIEVEMENT_FOREST_PROTECTOR_DESCRIPTION"] = "Reach level 10 with Nyru.",
["ACHIEVEMENT_FOREST_PROTECTOR_NAME"] = "Forest Protector",
["ACHIEVEMENT_GARBAGE_DISPOSAL_DESCRIPTION"] = "Eliminate 10 Mad Tinkerers before they can spawn Scrap Drones.",
["ACHIEVEMENT_GARBAGE_DISPOSAL_NAME"] = "Garbage Disposal",
["ACHIEVEMENT_GEM_SPILLER_DESCRIPTION"] = "Break all of the gem baskets.",
["ACHIEVEMENT_GEM_SPILLER_NAME"] = "Gem Spiller",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_DESCRIPTION"] = "Solve the puzzle and summon the band.",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_NAME"] = "Get the Party Started",
["ACHIEVEMENT_GIFT_OF_LIFE_DESCRIPTION"] = "Free the cloning experiment in the Replication Chamber.",
["ACHIEVEMENT_GIFT_OF_LIFE_NAME"] = "The Gift of Life",
["ACHIEVEMENT_GREENLIT_ALLIES_DESCRIPTION"] = "Summon 10 Arborean Thornspears.",
["ACHIEVEMENT_GREENLIT_ALLIES_NAME"] = "Greenlit Allies",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_DESCRIPTION"] = "Find the crocodile king.",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_NAME"] = "Hail to the K, baby!",
["ACHIEVEMENT_HEARTLESS_VICTORY_DESCRIPTION"] = "Complete the Heart of the Forest without using the Heart of the Arboreans' power.",
["ACHIEVEMENT_HEARTLESS_VICTORY_NAME"] = "Heartless Victory",
["ACHIEVEMENT_INTO_THE_OGREVERSE_DESCRIPTION"] = "Find the secrets of the mysterious spider person. ",
["ACHIEVEMENT_INTO_THE_OGREVERSE_NAME"] = "Unfriendly Neighbor.",
["ACHIEVEMENT_IRONCLAD_DESCRIPTION"] = "Win all the Iron Mode campaign challenges.",
["ACHIEVEMENT_IRONCLAD_NAME"] = "Ironclad",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_DESCRIPTION"] = "Help Lank fish 5 rupees.",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_NAME"] = "It's a Secret to Everyone",
["ACHIEVEMENT_KEPT_YOU_WAITING_DESCRIPTION"] = "Find the sneaky soldier in the Colossal Core.",
["ACHIEVEMENT_KEPT_YOU_WAITING_NAME"] = "Kept You Waiting, Huh?",
["ACHIEVEMENT_LEARNING_THE_ROPES_DESCRIPTION"] = "Finish the Tutorial with 3 stars.",
["ACHIEVEMENT_LEARNING_THE_ROPES_NAME"] = "Learning the Ropes",
["ACHIEVEMENT_LINIREAN_RESISTANCE_DESCRIPTION"] = "Win a stage using only Linirean towers and heroes.",
["ACHIEVEMENT_LINIREAN_RESISTANCE_NAME"] = "Linirean Resistance",
["ACHIEVEMENT_LUCAS_SPIDER_DESCRIPTION"] = "Play with Lucus until he’s happy.",
["ACHIEVEMENT_LUCAS_SPIDER_NAME"] = "Lucus the Spider.",
["ACHIEVEMENT_MASTER_TACTICIAN_DESCRIPTION"] = "Complete the campaign on Impossible difficulty.",
["ACHIEVEMENT_MASTER_TACTICIAN_NAME"] = "Master Tactician",
["ACHIEVEMENT_MECHANICAL_BURNOUT_DESCRIPTION"] = "Overfeed the machine in the Darksteel Gates.",
["ACHIEVEMENT_MECHANICAL_BURNOUT_NAME"] = "Mechanical Burnout",
["ACHIEVEMENT_MIGHTY_III_DESCRIPTION"] = "Slay 10000 enemies.",
["ACHIEVEMENT_MIGHTY_III_NAME"] = "Mighty III",
["ACHIEVEMENT_MIGHTY_II_DESCRIPTION"] = "Slay 3000 enemies.",
["ACHIEVEMENT_MIGHTY_II_NAME"] = "Mighty II",
["ACHIEVEMENT_MIGHTY_I_DESCRIPTION"] = "Slay 500 enemies.",
["ACHIEVEMENT_MIGHTY_I_NAME"] = "Mighty I",
["ACHIEVEMENT_MOST_DELICIOUS_DESCRIPTION"] = "Feed some honey to Biggie the Arborean.",
["ACHIEVEMENT_MOST_DELICIOUS_NAME"] = "Most Delicious",
["ACHIEVEMENT_NATURES_WRATH_DESCRIPTION"] = "Kill 30 enemies using the Heart of the Arboreans.",
["ACHIEVEMENT_NATURES_WRATH_NAME"] = "Nature's Wrath",
["ACHIEVEMENT_NONE_SHALL_PASS_DESCRIPTION"] = "Complete the Wildbeast Den without letting extra enemies go through the door.",
["ACHIEVEMENT_NONE_SHALL_PASS_NAME"] = "None Shall Pass!",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_DESCRIPTION"] = "Call 15 waves early.",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_NAME"] = "Not a Moment to Waste",
["ACHIEVEMENT_NO_FLY_ZONE_DESCRIPTION"] = "Kill 50 Ballooning Spiders.",
["ACHIEVEMENT_NO_FLY_ZONE_NAME"] = "No Flying Zone.",
["ACHIEVEMENT_OBLITERATE_DESCRIPTION"] = "Find the pieces of the forbidden robot in each Colossal Dwarfare stage.",
["ACHIEVEMENT_OBLITERATE_NAME"] = "Obliterate!",
["ACHIEVEMENT_ONE_SHOT_TOWER_DESCRIPTION"] = "Eliminate 10 enemies with a single Darkray Tower beam.",
["ACHIEVEMENT_ONE_SHOT_TOWER_NAME"] = "One Shot at Glory",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_DESCRIPTION"] = "Defeat Goregrind before jumping in Impossible difficulty.",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_NAME"] = "Tangled Up",
["ACHIEVEMENT_OVER_THE_EDGE_DESCRIPTION"] = "Push the Arboreans from the treetops.",
["ACHIEVEMENT_OVER_THE_EDGE_NAME"] = "Game Over",
["ACHIEVEMENT_OVINE_JOURNALISM_DESCRIPTION"] = "Find Sheepy in each campaign terrain.",
["ACHIEVEMENT_OVINE_JOURNALISM_NAME"] = "Ovine Journalism",
["ACHIEVEMENT_PEST_CONTROL_DESCRIPTION"] = "Kill 300 Glarelings.",
["ACHIEVEMENT_PEST_CONTROL_NAME"] = "Pest Control",
["ACHIEVEMENT_PLAYFUL_FRIENDS_DESCRIPTION"] = "Play \"burrow\" with all of the Arboreans in the Heart of the Forest.",
["ACHIEVEMENT_PLAYFUL_FRIENDS_NAME"] = "Playful Friends",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_DESCRIPTION"] = "Defeat Goregrind.",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_NAME"] = "Pork's Off the Menu",
["ACHIEVEMENT_PROMOTION_DENIED_DESCRIPTION"] = "Kill 30 Cult Priests before they transform into Abominations.",
["ACHIEVEMENT_PROMOTION_DENIED_NAME"] = "Promotion Denied",
["ACHIEVEMENT_ROCK_BEATS_ROCK_DESCRIPTION"] = "Make the statue win against itself.",
["ACHIEVEMENT_ROCK_BEATS_ROCK_NAME"] = "Rock Beats... Rock?",
["ACHIEVEMENT_ROOM_achievement_claim"] = "Claim Reward!",
["ACHIEVEMENT_ROYAL_CAPTAIN_DESCRIPTION"] = "Reach level 10 with Vesper.",
["ACHIEVEMENT_ROYAL_CAPTAIN_NAME"] = "Royal Captain",
["ACHIEVEMENT_RUNEQUEST_DESCRIPTION"] = "Activate all six runes throughout the Everadiant Forest.",
["ACHIEVEMENT_RUNEQUEST_NAME"] = "Runequest",
["ACHIEVEMENT_RUST_IN_PEACE_DESCRIPTION"] = "Complete a stage without allowing any Animated Armor to respawn.",
["ACHIEVEMENT_RUST_IN_PEACE_NAME"] = "Rust In Peace",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_DESCRIPTION"] = "Win the stage without losing any arborean flowers.",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_NAME"] = "Saviour of the forest",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_DESCRIPTION"] = "Get 3 stars on every stage of the Everadiant Forest.",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_NAME"] = "Saviour of the Green",
["ACHIEVEMENT_SCRAMBLED_EGGS_DESCRIPTION"] = "Kill 50 crokinder before they hatch.",
["ACHIEVEMENT_SCRAMBLED_EGGS_NAME"] = "Scrambled Eggs",
["ACHIEVEMENT_SEASONED_GENERAL_DESCRIPTION"] = "Complete the campaign on Veteran difficulty.",
["ACHIEVEMENT_SEASONED_GENERAL_NAME"] = "Seasoned General",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_DESCRIPTION"] = "Defeat Abominor, the devourer.",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_NAME"] = "See ya later, alligator",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_DESCRIPTION"] = "Complete Dominion Dome while keeping Grymbeard from setting your towers on fire.",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_NAME"] = "Shut Your Mouth!",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_DESCRIPTION"] = "Use Hero Powers 500 times.",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_NAME"] = "Signature Techniques",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_DESCRIPTION"] = "Help Gerhart slay the tree monster.",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_NAME"] = "Silver for Monsters",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_DESCRIPTION"] = "Help the friendly gator to start his boat.",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_NAME"] = "Smooth Oper-gator",
["ACHIEVEMENT_SPECTRAL_FURY_DESCRIPTION"] = "Defeat Navira and stop the Revenant invasion.",
["ACHIEVEMENT_SPECTRAL_FURY_NAME"] = "Spectral Fury",
["ACHIEVEMENT_STARLIGHT_DESCRIPTION"] = "Help Fredo and Sammy escape from the Giant Spider.",
["ACHIEVEMENT_STARLIGHT_NAME"] = "Starlight",
["ACHIEVEMENT_TAKE_ME_HOME_DESCRIPTION"] = "Return Riff the Goblin to his home dimension.",
["ACHIEVEMENT_TAKE_ME_HOME_NAME"] = "Take On Me",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_DESCRIPTION"] = "Summon 1000 reinforcements.",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_NAME"] = "The cavalry is here!",
["ACHIEVEMENT_TIPPING_THE_SCALES_DESCRIPTION"] = "Throw Robin Wood to the river.",
["ACHIEVEMENT_TIPPING_THE_SCALES_NAME"] = "Tipping the Scales",
["ACHIEVEMENT_TREE_HUGGER_DESCRIPTION"] = "Complete Misty Ruins with at least one Weirdwood standing.",
["ACHIEVEMENT_TREE_HUGGER_NAME"] = "Tree Hugger",
["ACHIEVEMENT_TURN_A_BLIND_EYE_DESCRIPTION"] = "Kill 100 Corruption Spawns while they are under the effects of the Glare.",
["ACHIEVEMENT_TURN_A_BLIND_EYE_NAME"] = "Turn a Blind Eye",
["ACHIEVEMENT_UNBOUND_VICTORY_DESCRIPTION"] = "Complete the Wicked Crossing without any Nightmares turning into Bound Nightmares.",
["ACHIEVEMENT_UNBOUND_VICTORY_NAME"] = "Unbound Victory",
["ACHIEVEMENT_UNENDING_RICHES_DESCRIPTION"] = "Collect a total of 150000 gold.",
["ACHIEVEMENT_UNENDING_RICHES_NAME"] = "Unending Riches",
["ACHIEVEMENT_UNTAMED_BEAST_DESCRIPTION"] = "Reach level 10 with Grimson.",
["ACHIEVEMENT_UNTAMED_BEAST_NAME"] = "Untamed Beast",
["ACHIEVEMENT_WAR_MASONRY_DESCRIPTION"] = "Build 100 towers.",
["ACHIEVEMENT_WAR_MASONRY_NAME"] = "War Masonry",
["ACHIEVEMENT_WEIRDER_THINGS_DESCRIPTION"] = "Help Ernie and Daston repel the Blinkers in the Blighted Farmlands.",
["ACHIEVEMENT_WEIRDER_THINGS_NAME"] = "Weirder Things",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_DESCRIPTION"] = "Find the elusive cat in each Undying Fury campaign stage.",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_NAME"] = "We Are All Mad Here",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_DESCRIPTION"] = "Kill 15 Twisted Sisters before they can spawn a Nightmare.",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_NAME"] = "We're Not Gonna Take It",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_DESCRIPTION"] = "Repair Nick and Marty's portal gun.",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_NAME"] = "Wobba-Lubba-Dub-Dub!",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_DESCRIPTION"] = "Save King Denas without letting Seeress Mydrias cast projections in Impossible Difficulty.",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_NAME"] = "You Shall Not Cast!",
["ADS_MESSAGE_OK"] = "Ok",
["ADS_MESSAGE_TITLE"] = "MORE GEMS",
["ALERT_VERSION"] = "A newer version of the game is available. Please download it from the store.",
["APPLY_SETTINGS_AND_RESTART"] = "Restart to apply changes?",
["ARCHER TOWER"] = "ARCHER TOWER",
["ARE YOU SURE YOU WANT TO QUIT?"] = "ARE YOU SURE YOU WANT TO QUIT?",
["ARMORED ENEMIES!"] = "ARMORED ENEMIES!",
["ARTILLERY"] = "ARTILLERY",
["Achievements"] = "Achievements",
["Advanced"] = "Advanced",
["Average"] = "Average",
["BARRACKS"] = "BARRACKS",
["BOSS_BULL_KING_NAME"] = "Bull Demon King",
["BOSS_CORRUPTED_DENAS_DESCRIPTION"] = "The defeated King of Linirea, now turned into a towering abomination by the dark powers of the Cult of the Overseer.",
["BOSS_CORRUPTED_DENAS_EXTRA"] = "- Spawns Glarelings",
["BOSS_CORRUPTED_DENAS_NAME"] = "Corrupted Denas",
["BOSS_CROCS_DESCRIPTION"] = "Hunger embodied, an ancient being capable of devouring the world itself if left unchecked.",
["BOSS_CROCS_EXTRA"] = "- Eat towers\n- Evolves after satisfying its hunger\n- Summon crokinders",
["BOSS_CROCS_LVL1_DESCRIPTION"] = "Hunger impersonated, an ancient being capable of devouring the world itself if left unchecked.",
["BOSS_CROCS_LVL1_EXTRA"] = "- Eat towers\n- Evolves after satisfying its hunger\n- Summon crokinders",
["BOSS_CROCS_LVL1_NAME"] = "Abominor",
["BOSS_CROCS_LVL2_DESCRIPTION"] = "Hunger impersonated, an ancient being capable of devouring the world itself if left unchecked.",
["BOSS_CROCS_LVL2_EXTRA"] = "- Eat towers\n- Evolves after satisfying its hunger\n- Summon crokinders",
["BOSS_CROCS_LVL2_NAME"] = "Abominor",
["BOSS_CROCS_LVL3_DESCRIPTION"] = "Hunger impersonated, an ancient being capable of devouring the world itself if left unchecked.",
["BOSS_CROCS_LVL3_EXTRA"] = "- Eat towers\n- Evolves after satisfying its hunger\n- Summon crokinders",
["BOSS_CROCS_LVL3_NAME"] = "Abominor",
["BOSS_CROCS_LVL4_DESCRIPTION"] = "Hunger impersonated, an ancient being capable of devouring the world itself if left unchecked.",
["BOSS_CROCS_LVL4_EXTRA"] = "- Eat towers\n- Evolves after satisfying its hunger\n- Summon crokinders",
["BOSS_CROCS_LVL4_NAME"] = "Abominor",
["BOSS_CROCS_LVL5_DESCRIPTION"] = "Hunger impersonated, an ancient being capable of devouring the world itself if left unchecked.",
["BOSS_CROCS_LVL5_EXTRA"] = "- Eat towers\n- Evolves after satisfying its hunger\n- Summon crokinders",
["BOSS_CROCS_LVL5_NAME"] = "Abominor",
["BOSS_CROCS_NAME"] = "Abominor",
["BOSS_CULT_LEADER_DESCRIPTION"] = "The current leader of the Cult, Mydrias acts as the hand of the Overseer, orchestrating the invasion of worlds. ",
["BOSS_CULT_LEADER_EXTRA"] = "- High armor and magic resistance while she's not blocked\n - High area damage",
["BOSS_CULT_LEADER_NAME"] = "Seeress Mydrias",
["BOSS_GRYMBEARD_DESCRIPTION"] = "An egomaniacal dwarf with delusions of grandeur who is as dangerous as deranged.",
["BOSS_GRYMBEARD_EXTRA"] = "- Launches a rocket fist against player units",
["BOSS_GRYMBEARD_NAME"] = "Grymbeard",
["BOSS_MACHINIST_DESCRIPTION"] = "On top of this latest invention, Grymbeard chases his enemies making fire and metal rain.",
["BOSS_MACHINIST_EXTRA"] = "- Flying\n- Fires scrap at units",
["BOSS_MACHINIST_NAME"] = "Grymbeard",
["BOSS_NAVIRA_DESCRIPTION"] = "Fallen from grace and tapping the forbidden forces of death magic, Navira seeks to restore glory to the elves.",
["BOSS_NAVIRA_EXTRA"] = "- Blocks towers with fireballs\n- Turns into an unblockable tornado",
["BOSS_NAVIRA_NAME"] = "Navira",
["BOSS_PIG_DESCRIPTION"] = "The one and only self-appointed King of the Wildbeasts uses a gigantic flail to crush his enemies.",
["BOSS_PIG_EXTRA"] = "- Jumps great distances across paths",
["BOSS_PIG_NAME"] = "Goregrind",
["BOSS_PRINCESS_IRON_FAN_DESCRIPTION"] = "Elegant yet deadly, Princess Iron Fan is a formidable opponent. Wielder of the legendary Iron Fan, capable of extinguishing flames and causing storms.",
["BOSS_PRINCESS_IRON_FAN_EXTRA"] = "- Clones herself\n- Locks heroes in a flask\n- Turns towers into enemy spawners",
["BOSS_PRINCESS_IRON_FAN_NAME"] = "Princess Iron Fan",
["BOSS_REDBOY_TEEN_DESCRIPTION"] = "The fierce and prideful young demon prince, known for his fiery temper, cocky attitude, and relentless ambition. Commander of the Samadhi Fire and expert martial artist with the spear.",
["BOSS_REDBOY_TEEN_EXTRA"] = "- Big AoE damage attack\n- Commands his dragon to stun towers",
["BOSS_REDBOY_TEEN_NAME"] = "Red Boy",
["BOSS_SPIDER_QUEEN_DESCRIPTION"] = "One of the Spider Queens, an ancient force awakened from her slumber to reclaim what is rightfully hers.",
["BOSS_SPIDER_QUEEN_EXTRA"] = "- Stuns towers\n- Drains life from nearby enemies\n- Summons Lifesteal Spiders\n- Throws webs into your eyes",
["BOSS_SPIDER_QUEEN_NAME"] = "Mygale",
["BRIEFING_LEVEL_WARNING"] = "This campaign has a high difficulty level.",
["BUILD HERE!"] = "BUILD HERE!",
["BUTTON_BUG_CRASH"] = "CRASH",
["BUTTON_BUG_OTHER"] = "OTHER",
["BUTTON_BUG_REPORT"] = "BUG",
["BUTTON_BUY"] = "BUY",
["BUTTON_BUY_UPGRADE"] = "BUY UPGRADE",
["BUTTON_CLOSE"] = "CLOSE",
["BUTTON_CONFIRM"] = "CONFIRM",
["BUTTON_CONTINUE"] = "CONTINUE",
["BUTTON_DISABLE"] = "Disable",
["BUTTON_DONE"] = "DONE",
["BUTTON_ENDLESS_QUIT"] = "QUIT",
["BUTTON_ENDLESS_TRYAGAIN"] = "TRY AGAIN",
["BUTTON_GET_GEMS"] = "GET ITEMS",
["BUTTON_LEVEL_SELECT_FIGHT"] = "FIGHT!",
["BUTTON_LOST_CONTENT"] = "LOST CONTENT",
["BUTTON_MAIN_MENU"] = "MAIN MENU",
["BUTTON_NO"] = "NO",
["BUTTON_OK"] = "OK!",
["BUTTON_OPEN"] = "OPEN",
["BUTTON_QUIT"] = "QUIT",
["BUTTON_RESET"] = "RESET",
["BUTTON_RESTART"] = "RESTART",
["BUTTON_RESUME"] = "RESUME",
["BUTTON_TO_BATTLE_1"] = "TO",
["BUTTON_TO_BATTLE_2"] = "BATTLE",
["BUTTON_UNDO"] = "UNDO",
["BUTTON_YES"] = "YES",
["BUY UPGRADES!"] = "BUY UPGRADES!",
["Basic"] = "Basic",
["Basic Tower Types"] = "Basic Tower Types",
["CARD_REWARDS_CAMPAIGN"] = "New campaign!",
["CARD_REWARDS_DLC_1"] = "Colossal Dwarfare",
["CARD_REWARDS_DLC_2"] = "Wukong's Journey",
["CARD_REWARDS_HERO"] = "NEW HERO!",
["CARD_REWARDS_TOWER"] = "NEW TOWER!",
["CARD_REWARDS_TOWER_LEVEL"] = "NEW TOWER LEVEL!",
["CARD_REWARDS_TOWER_LEVEL_PREFIX"] = "LVL",
["CARD_REWARDS_UPDATE_01"] = "Undying Fury",
["CARD_REWARDS_UPDATE_02"] = "Ancient Hunger",
["CARD_REWARDS_UPDATE_03"] = "Arachnophobia",
["CARD_REWARDS_UPGRADES"] = "UPGRADE POINTS!",
["CArmor0"] = "None",
["CArmor1"] = "Low",
["CArmor2"] = "Medium",
["CArmor3"] = "High",
["CArmor4"] = "Great",
["CArmor9"] = "Immune",
["CArmorSmall0"] = "Non",
["CArmorSmall1"] = "Low",
["CArmorSmall2"] = "Med",
["CArmorSmall3"] = "Hig",
["CArmorSmall4"] = "Grt",
["CArmorSmall9"] = "Imm",
["CHANGE_LANGUAGE_QUESTION"] = "ARE YOU SURE YOU WANT TO CHANGE THE LANGUAGE SETTINGS?",
["CINEMATICS_TAP_TO_CONTINUE_KR1"] = "click to continue...",
["CINEMATICS_TAP_TO_CONTINUE_KR2"] = "click to continue...",
["CINEMATICS_TAP_TO_CONTINUE_KR3"] = "click to continue...",
["CINEMATICS_TAP_TO_CONTINUE_KR5"] = "click to continue...",
["CLAIM_GIFT"] = "Claim Gift",
["CLICK HERE TO SKIP.\nPLEASE DON'T"] = "CLICK HERE TO SKIP.\nPLEASE DON'T",
["CLICK HERE!"] = "CLICK HERE!",
["CLICK ON THE ROAD"] = "CLICK ON THE ROAD",
["CLICK TO CALL IT EARLY"] = "CLICK TO CALL IT EARLY",
["CLOUDSYNC_PLEASE_WAIT"] = "Updating cloud save...",
["CLOUD_DIALOG_NO"] = "No",
["CLOUD_DIALOG_OK"] = "Ok",
["CLOUD_DIALOG_YES"] = "Yes",
["CLOUD_DOWNLOAD_QUESTION"] = "Download saved game from iCloud?",
["CLOUD_DOWNLOAD_TITLE"] = "Download from iCould",
["CLOUD_SAVE"] = "Cloud Save",
["CLOUD_SAVE_DISABLE_EXTRA"] = "Note: You might lose your game progress if the game is uninstalled.",
["CLOUD_SAVE_DISABLE_GENERIC_DESCRIPTION"] = "Are you sure you want to disable saving your game progress in the cloud?",
["CLOUD_SAVE_OFF"] = "Cloud save off",
["CLOUD_SAVE_ON"] = "Cloud save on",
["CLOUD_UPLOAD_QUESTION"] = "Upload saved game to iCloud?",
["CLOUD_UPLOAD_TITLE"] = "Upload to iCloud",
["COMIC_10_1_KR5_KR5"] = "Release me! I'm doing what's best for the kingdom!",
["COMIC_10_2_KR5_KR5"] = "Stop this blasphemy, brother. This is not the elven way.",
["COMIC_10_3_KR5_KR5"] = "Thanks, my old apprentice. We'll take it from here.",
["COMIC_10_4_KR5_KR5"] = "Later, at the camp...",
["COMIC_10_5_KR5_KR5"] = "So... you are sure Vez'nan can be trusted, then?",
["COMIC_10_6_KR5_KR5"] = "We have an eye on him...",
["COMIC_10_7_KR5_KR5"] = "...but he seems to be playing nice for now.",
["COMIC_10_8_KR5_KR5"] = "Heh. For now...",
["COMIC_11_1_KR5_KR5"] = "The swamp seems to have awakened...",
["COMIC_11_2_KR5_KR5"] = "...like it is watching us...",
["COMIC_11_3_KR5_KR5"] = "...advancing and lurking...",
["COMIC_11_4_KR5_KR5"] = "...ready to devour us.",
["COMIC_11_5_KR5_KR5"] = "Be careful!",
["COMIC_11_6_KR5_KR5"] = "We are under attack!",
["COMIC_11_7_KR5_KR5"] = "Go, little wisp! Our safety lies in your haste!",
["COMIC_12_1_KR5_KR5"] = "Simply locking you up was a mistake. One I will not repeat.",
["COMIC_12_2_KR5_KR5"] = "NOOOOOO!!!",
["COMIC_12_3_KR5_KR5"] = "I banish you forever!!",
["COMIC_12_4_KR5_KR5"] = "Cough!",
["COMIC_12_5_KR5_KR5"] = "Cough, Cough!",
["COMIC_12_6_KR5_KR5"] = "Uh... I guess I must be out of practice.",
["COMIC_13_1_KR5_KR5"] = "They said it was madness.",
["COMIC_13_2_KR5_KR5"] = "That such a weapon was impossible.",
["COMIC_13_3_KR5_KR5"] = "But soon they'll know how wrong they were...",
["COMIC_13_4_KR5_KR5"] = "...and surrender before Grymbeard's genius!",
["COMIC_14_1_KR5_KR5"] = "What are we gonna do with them?",
["COMIC_14_2_KR5_KR5"] = "Leave them to me!",
["COMIC_14_3_KR5_KR5"] = "I know just the place.",
["COMIC_14_4_KR5_KR5"] = "So this is it?",
["COMIC_14_5_KR5_KR5"] = "Make Grymbeard rot in a cell?!",
["COMIC_14_6_KR5_KR5"] = "Quite the opposite, my short friend...",
["COMIC_14_7_KR5_KR5"] = "...I have big plans for that big brain of yours!",
["COMIC_15_10_KR5_KR5"] = "...but not in a great condition.",
["COMIC_15_1_KR5_KR5"] = "Somewhere in the mountain.",
["COMIC_15_2_KR5_KR5"] = "Hey, Goblin!",
["COMIC_15_3_KR5_KR5"] = "Get to work!",
["COMIC_15_4_KR5_KR5"] = "You need to deliver a message.",
["COMIC_15_5_KR5_KR5"] = "We should send more scouts. We can't rest easy with all those cultists roaming around.",
["COMIC_15_6_KR5_KR5"] = "We could send some wisps to help; they...",
["COMIC_15_7_KR5_KR5"] = "Dark Lord! Urgent news!",
["COMIC_15_8_KR5_KR5"] = "Well...",
["COMIC_15_9_KR5_KR5"] = "We found our scouts...",
["COMIC_16_1_KR5_KR5"] = "I shall be avenged!",
["COMIC_16_2_KR5_KR5"] = "My sister...whaaat?",
["COMIC_17_10_KR5_KR5"] = "If we don't stop them, they'll wipe out all the kingdoms!",
["COMIC_17_11_KR5_KR5"] = "We have to help him!",
["COMIC_17_12_KR5_KR5"] = "Oh, sure we do.",
["COMIC_17_13_KR5_KR5"] = "Sure we do...",
["COMIC_17_1_KR5_KR5"] = "Beautiful afternoon, isn't it?",
["COMIC_17_2_KR5_KR5"] = "I could get used to this peace.",
["COMIC_17_3_KR5_KR5"] = "Better not.",
["COMIC_17_4_KR5_KR5"] = "Sun, is that you?! You could have just waved, you know...",
["COMIC_17_5_KR5_KR5"] = "Friends, something terrible has happened...",
["COMIC_17_6_KR5_KR5"] = "I was peacefully meditating on my turtle when...",
["COMIC_17_7_KR5_KR5"] = "The Three Demon Kings appeared out of nowhere!",
["COMIC_17_8_KR5_KR5"] = "Needless to say, I fought valiantly, but...",
["COMIC_17_9_KR5_KR5"] = "They dishonorably took my celestial spheres!",
["COMIC_18_1_KR5_KR5"] = "Near the shores of The Bull Demon King's lair...",
["COMIC_18_2_KR5_KR5"] = "Target spotted!",
["COMIC_18_3_KR5_KR5"] = "Let's blow up that fortress!",
["COMIC_18_4_KR5_KR5"] = "My walls laugh at your pebbles!",
["COMIC_18_5_KR5_KR5"] = "For Linirea!",
["COMIC_18_6_KR5_KR5"] = "Back off, boys! We need an opening here!",
["COMIC_19_1_KR5_KR5"] = "The celestial spheres can't remain in your custody, it's absurd!",
["COMIC_19_2_KR5_KR5"] = "Yeah, be careful with that, buddy.",
["COMIC_19_3_KR5_KR5"] = "You have been very wise, noble monkey!",
["COMIC_19_4_KR5_KR5"] = "What am I gonna do with you three?",
["COMIC_1_1_KR5"] = "It's been a month since we arrived in this land, looking for our lost King...",
["COMIC_1_2B_KR5"] = "...After he was banished by Vez'nan, the dark wizard.",
["COMIC_1_4_KR5"] = "We found a place and set up camp to recover our strength...",
["COMIC_1_5_KR5"] = "...in peace...",
["COMIC_1_8_KR5"] = "...But it seems that is over now.",
["COMIC_2_1_KR5"] = "HURRAH!!",
["COMIC_2_3_KR5"] = "Vez'nan?!",
["COMIC_2_4a_KR5"] = "Easy now... I come to propose...",
["COMIC_2_4b_KR5"] = "...a deal.",
["COMIC_2_5_KR5"] = "After what you've done to our kingdom?!",
["COMIC_2_6_KR5"] = "King Denas' eyes needed to be opened.",
["COMIC_2_7_KR5"] = "He refused to see the danger infesting the kingdom.",
["COMIC_2_8_1_KR5"] = "But let us find your king...",
["COMIC_2_8_2_KR5"] = "...and put an end to this threat.",
["COMIC_2_8b_KR5"] = "...together.",
["COMIC_3_1_KR5"] = "Oh my! What do we have here...?",
["COMIC_3_2_KR5"] = "The mighty sword of Elynie!",
["COMIC_3_3_KR5"] = "Ouch!",
["COMIC_3_4a_KR5"] = "Of course...",
["COMIC_3_4b_KR5"] = "Quit wasting time!",
["COMIC_3_5a_KR5"] = "Ah... but he's closer than you think.",
["COMIC_3_5b_KR5"] = "Our king is still missing.",
["COMIC_3_6_KR5"] = "It could be an uphill battle though.",
["COMIC_4_10a_KR5"] = "Ha! I've always been.",
["COMIC_4_10b_KR5"] = "So... what happens now?",
["COMIC_4_11_KR5"] = "We may have our differences...",
["COMIC_4_12_KR5"] = "...but we have a bigger threat in common.",
["COMIC_4_1_KR5"] = "Elynie...",
["COMIC_4_2_KR5"] = "...give him strength!",
["COMIC_4_4_KR5"] = "Aaurrrgh!",
["COMIC_4_7a_KR5"] = "I see your 'vacation' has done you wonders!",
["COMIC_4_7b_KR5"] = "YOU!!!",
["COMIC_4_8_KR5"] = "You should pay for your mischief!",
["COMIC_4_9_KR5"] = "But you were right.",
["COMIC_5_1_KR2"] = "Victory!",
["COMIC_5_1_KR5_KR5"] = "You worms can't stop...",
["COMIC_5_2_KR2"] = "Victory!",
["COMIC_5_2_KR5_KR5"] = "...THE NEW WORLD!",
["COMIC_5_6_KR5_KR5"] = "It has awakened! ",
["COMIC_5_7a_KR5_KR5"] = "So this is it...",
["COMIC_5_7b_KR5_KR5"] = "...the final showdown.",
["COMIC_6_1a_KR5_KR5"] = "You're brave challenging me.",
["COMIC_6_1b_KR5_KR5"] = "But... that thing has no place here!",
["COMIC_6_4_KR5_KR5"] = "Hey!",
["COMIC_6_5_KR5_KR5"] = "You, cosmic slug...",
["COMIC_6_6_KR5_KR5"] = "...don't understimate MY power!!!",
["COMIC_6_8_KR5_KR5"] = "Get ready. I can't hold it for too long!",
["COMIC_7_1_KR5_KR5"] = "NOOO!!! This cannot... be!",
["COMIC_7_3_KR5_KR5"] = "So... what now?",
["COMIC_7_4a_KR5_KR5"] = "Well, my mission is done...",
["COMIC_7_4b_KR5_KR5"] = "...and I think they need their king.",
["COMIC_7_5_2_KR2"] = "Nope",
["COMIC_7_6_KR5_KR5"] = "Till next time, dear foe.",
["COMIC_7_7_KR5_KR5"] = "Later, at the Everadiant Forest...",
["COMIC_8_1_KR5_KR5"] = "Ah, at last!",
["COMIC_8_2_KR5_KR5"] = "This power is, once again... ",
["COMIC_8_4_KR5_KR5"] = "... MINE!",
["COMIC_8_5_KR5_KR5"] = "MUA HA HA HA HA!",
["COMIC_9_1_KR5_KR5"] = "Not so long ago, we elves were revered for our magic and grace...",
["COMIC_9_2_KR5_KR5"] = "...until our sacred relic was corrupted and we became a shadow of what we used to be.",
["COMIC_9_3_KR5_KR5"] = "But with this army, I'll restore our glory...",
["COMIC_9_4_KR5_KR5"] = "...and I'll lead a new world ruled by the elves!!!",
["COMIC_BALLOON_0002_KR1"] = "Victory!",
["COMIC_BALLOON_02_KR1"] = "Victory!",
["COMIC_balloon_0002_KR1"] = "Victory!",
["COMMAND YOUR TROOPS!"] = "COMMAND YOUR TROOPS!",
["CONFIRM_EXIT"] = "Exit?",
["CONFIRM_RESTART"] = "Restart?",
["CONTROLLER_STAGE_16_OVERSEER_DESCRIPTION"] = "An extradimensional monstrosity that invades and conquers other worlds to absorb their energy. Must be stopped at all costs.",
["CONTROLLER_STAGE_16_OVERSEER_EXTRA"] = "- Swaps the player's towers\n- Spawns Glarelings\n- Destroys holders",
["CONTROLLER_STAGE_16_OVERSEER_NAME"] = "The Overseer",
["CREDITS"] = "CREDITS",
["CREDITS_COPYRIGHT"] = "© 2014 Ironhide Game Studio. All rights reserved.",
["CREDITS_POWERED_BY"] = "Powered by",
["CREDITS_SUBTITLE_01"] = "(in alphabetical order)",
["CREDITS_SUBTITLE_07"] = "(in alphabetical order)",
["CREDITS_SUBTITLE_09"] = "(in alphabetical order)",
["CREDITS_SUBTITLE_16"] = "(in alphabetical order)",
["CREDITS_TEXT_18"] = "To our families, friends and the community",
["CREDITS_TEXT_18_2"] = "for supporting us all along these years.",
["CREDITS_TITLE_01"] = "Creative Directors & Executive Producers",
["CREDITS_TITLE_01_CREATIVE_DIRECTORS"] = "Creative Directors",
["CREDITS_TITLE_01_EXECUTIVE_PRODUCERS"] = "Executive Producers",
["CREDITS_TITLE_02"] = "Lead Game Designer",
["CREDITS_TITLE_02_LEAD_GAME_DESIGNERS"] = "Lead Game Designers",
["CREDITS_TITLE_03"] = "Game Designers",
["CREDITS_TITLE_03_GAME_DESIGNER"] = "Game Designer",
["CREDITS_TITLE_04"] = "Story Writer",
["CREDITS_TITLE_04_STORY_WRITERS"] = "Story Writers",
["CREDITS_TITLE_05"] = "Text Writers",
["CREDITS_TITLE_06"] = "Lead Programmer",
["CREDITS_TITLE_06_LEAD_PROGRAMMERS"] = "Lead Programmers",
["CREDITS_TITLE_07"] = "Programmers",
["CREDITS_TITLE_08"] = "Lead Artist",
["CREDITS_TITLE_09"] = "Artists",
["CREDITS_TITLE_10"] = "Comic Artist",
["CREDITS_TITLE_11"] = "Comic Writer",
["CREDITS_TITLE_12"] = "Technical Artist",
["CREDITS_TITLE_13"] = "Sound FX",
["CREDITS_TITLE_14"] = "Original music by",
["CREDITS_TITLE_15"] = "Voice Talent",
["CREDITS_TITLE_16"] = "Q&A & Testing",
["CREDITS_TITLE_17"] = "Beta Testing",
["CREDITS_TITLE_18"] = "Special Thanks",
["CREDITS_TITLE_19_PMO"] = "PMO",
["CREDITS_TITLE_20_PRODUCER"] = "Producer",
["CREDITS_TITLE_21_MARKETING"] = "Marketing",
["CREDITS_TITLE_22_SPECIAL_COLLAB"] = "Special Collaborators",
["CREDITS_TITLE_ANCIENT_HUNGER_UPDATE"] = "Ancient Hunger / Arachnophobia / Wukong's Journey",
["CREDITS_TITLE_GAME_ENGINE_PROGRAMMER"] = "Game Engine Programmer",
["CREDITS_TITLE_LOCALIZATION"] = "Localization",
["CREDITS_TITLE_LOGO"] = "A GAME BY",
["CRange0"] = "Short",
["CRange1"] = "Average",
["CRange2"] = "Long",
["CRange3"] = "Great",
["CRange4"] = "Extreme",
["CReload0"] = "Very Slow",
["CReload1"] = "Slow",
["CReload2"] = "Average",
["CReload3"] = "Fast",
["CReload4"] = "Very fast",
["CSpeed0"] = "Slow",
["CSpeed1"] = "Medium",
["CSpeed2"] = "Fast",
["C_DIFFICULTY_EASY"] = "Completed in Casual",
["C_DIFFICULTY_HARD"] = "Completed in Veteran",
["C_DIFFICULTY_IMPOSSIBLE"] = "Completed in Impossible",
["C_DIFFICULTY_NORMAL"] = "Completed in Normal",
["C_REWARD"] = "Reward :",
["Campaign"] = "Campaign",
["Campaing"] = "Campaign",
["Casual"] = "Casual",
["Challenge Rules"] = "Challenge Rules",
["Clear_progress"] = "Clear progress",
["Click on the path to move the hero."] = "Click on the path to move the hero.",
["Click to select"] = "Click to select",
["Coming soon"] = "Coming soon",
["Community Manager"] = "Community Manager",
["Continue"] = "Continue",
["Credits"] = "Credits",
["DAYS_ABBREVIATION"] = "d",
["DEFEAT"] = "DEFEAT",
["DELETE SLOT?"] = "Delete Slot?",
["DIFFICULTY LEVEL"] = "DIFFICULTY LEVEL",
["DIFFICULTY_SELECTION_EASY_DESCRIPTION"] = "For Beginners To Strategy Games!",
["DIFFICULTY_SELECTION_HARD_DESCRIPTION"] = "Hardcore! Play at your own risk!",
["DIFFICULTY_SELECTION_IMPOSSIBLE_DESCRIPTION"] = "Only the strongest have a chance!",
["DIFFICULTY_SELECTION_IMPOSSIBLE_LOCKED_DESCRIPTION"] = "Complete the campaign to unlock this mode",
["DIFFICULTY_SELECTION_NORMAL_DESCRIPTION"] = "A good challenge!",
["DIFFICULTY_SELECTION_NOTE"] = "You can always change the difficulty level when selecting a stage.",
["DIFFICULTY_SELECTION_TITLE"] = "Choose the difficulty level!",
["DISCOUNT"] = "DISCOUNT",
["DLC_OWNED"] = "OWNED",
["Defeat"] = "Defeat",
["Difficulty Level"] = "Difficulty Level",
["Done"] = "Done",
["ELITE STAGE!"] = "ELITE STAGE!",
["ENEMY_ACOLYTE_DESCRIPTION"] = "Short and meek, the acolytes make their numbers count in battle.",
["ENEMY_ACOLYTE_EXTRA"] = "- Spawns a tentacle on death",
["ENEMY_ACOLYTE_NAME"] = "Cult Acolyte",
["ENEMY_ACOLYTE_SPECIAL"] = "Spawns a tentacle on death",
["ENEMY_ACOLYTE_TENTACLE_DESCRIPTION"] = "As a last resort, Acolytes sacrifice their life to the Overseer, spawning deadly tentacles.",
["ENEMY_ACOLYTE_TENTACLE_EXTRA"] = "- Spawns from dead Acolytes",
["ENEMY_ACOLYTE_TENTACLE_NAME"] = "Acolyte Tentacle",
["ENEMY_AMALGAM_DESCRIPTION"] = "Monstrosities made from both flesh and soil of the Void Beyond. The Behemoths spread fear despite their slowness to traverse the battlefield.",
["ENEMY_AMALGAM_EXTRA"] = "- Mini-boss\n- Explodes when it dies",
["ENEMY_AMALGAM_NAME"] = "Flesh Behemoth",
["ENEMY_ANIMATED_ARMOR_DESCRIPTION"] = "Battered relics from battles of the past, now possessed by specters that drive them into the fray.",
["ENEMY_ANIMATED_ARMOR_EXTRA"] = "- When defeated, it can be reanimated by a specter",
["ENEMY_ANIMATED_ARMOR_NAME"] = "Animated Armor",
["ENEMY_ARMORED_NIGHTMARE_DESCRIPTION"] = "Clad in armor thanks to cult magic, these Nightmares thrust themselves into battle headfirst.",
["ENEMY_ARMORED_NIGHTMARE_EXTRA"] = "- High armor\n- Turns into a Nightmare when defeated",
["ENEMY_ARMORED_NIGHTMARE_NAME"] = "Bound Nightmare",
["ENEMY_ARMORED_NIGHTMARE_SPECIAL"] = "Turns into a Nightmare when defeated.",
["ENEMY_ASH_SPIRIT_DESCRIPTION"] = "Powerful spirits turned into frightening monsters, born from lava, ash and sorrow.",
["ENEMY_ASH_SPIRIT_EXTRA"] = "- High health\n- High armor\n- Regenerates health while on flaming ground",
["ENEMY_ASH_SPIRIT_NAME"] = "Ash Spirit",
["ENEMY_BALLOONING_SPIDER_DESCRIPTION"] = "Fast and sneaky spiders with a knack for avoiding trouble.",
["ENEMY_BALLOONING_SPIDER_EXTRA"] = "- Starts flying when cornered\n- Medium armor",
["ENEMY_BALLOONING_SPIDER_FLYER_DESCRIPTION"] = "Fast and sneaky spiders with a knack for avoiding trouble.",
["ENEMY_BALLOONING_SPIDER_FLYER_EXTRA"] = "- Starts flying when cornered\n- Medium armor",
["ENEMY_BALLOONING_SPIDER_FLYER_NAME"] = "Ballooning Spider",
["ENEMY_BALLOONING_SPIDER_NAME"] = "Ballooning Spider",
["ENEMY_BANE_WOLF_DESCRIPTION"] = "Twisted wolves that prey upon those who are too slow to see them coming.",
["ENEMY_BANE_WOLF_EXTRA"] = "- Moves faster each time it receives damage",
["ENEMY_BANE_WOLF_NAME"] = "Bane Wolf",
["ENEMY_BEAR_VANGUARD_DESCRIPTION"] = "Big, broad and bad, they tear their enemies by the dozen.",
["ENEMY_BEAR_VANGUARD_EXTRA"] = "- High armor\n- Gets enraged when a Bear dies nearby",
["ENEMY_BEAR_VANGUARD_NAME"] = "Bear Vanguard",
["ENEMY_BEAR_VANGUARD_SPECIAL"] = "Enters a frenzied state when another nearby bear dies.",
["ENEMY_BEAR_WOODCUTTER_DESCRIPTION"] = "Tends to sleep while on duty, but when it wakes up, things get serious.",
["ENEMY_BEAR_WOODCUTTER_EXTRA"] = "- High armor\n- Gets enraged when a Bear dies nearby",
["ENEMY_BEAR_WOODCUTTER_NAME"] = "Bear Woodcutter",
["ENEMY_BIG_TERRACOTA_DESCRIPTION"] = "An antropomorphic mud clump born from the fusion of several souls driven by murderous intent",
["ENEMY_BIG_TERRACOTA_EXTRA"] = "- Melee",
["ENEMY_BIG_TERRACOTA_NAME"] = "Monster Illusory Decoy",
["ENEMY_BLAZE_RAIDER_DESCRIPTION"] = "Proud and burly captains, initiates of the Path of Fire, who wield snake spears to outmaneuver foes.",
["ENEMY_BLAZE_RAIDER_EXTRA"] = "- Low armor\n- Special attack on flaming ground",
["ENEMY_BLAZE_RAIDER_NAME"] = "Blaze Raider",
["ENEMY_BLINKER_DESCRIPTION"] = "With their menacing stare and bat-like wings, Blinkers prey on the unsuspecting enemies.",
["ENEMY_BLINKER_EXTRA"] = "- Stuns player units",
["ENEMY_BLINKER_NAME"] = "Void Blinker",
["ENEMY_BLINKER_SPECIAL"] = "Stuns player units.",
["ENEMY_BOSS_BULL_KING_DESCRIPTION"] = "A ruthless and authoritative leader, war veteran and pragmatic strategist. Famous for his immense strenght, resentful personality and martial prowess.",
["ENEMY_BOSS_BULL_KING_EXTRA"] = "-High armor\n- High magic resistance\n- Big area stun on units and towers",
["ENEMY_BOSS_BULL_KING_NAME"] = "Bull Demon King",
["ENEMY_BOSS_CORRUPTED_DENAS_NAME"] = "Corrupted Denas",
["ENEMY_BOSS_CROCS_2_NAME"] = "Abominor Venomus",
["ENEMY_BOSS_CROCS_3_NAME"] = "Abominor Ignis",
["ENEMY_BOSS_CROCS_NAME"] = "Abominor",
["ENEMY_BOSS_CULT_LEADER_NAME"] = "Seeress Mydrias",
["ENEMY_BOSS_DEFORMED_GRYMBEARD_NAME"] = "Deformed Grymbeard",
["ENEMY_BOSS_GRYMBEARD_NAME"] = "Grymbeard",
["ENEMY_BOSS_MACHINIST_NAME"] = "Grymbeard",
["ENEMY_BOSS_NAVIRA_NAME"] = "Navira",
["ENEMY_BOSS_OVERSEER_NAME"] = "The Overseer",
["ENEMY_BOSS_PIG_NAME"] = "Goregrind",
["ENEMY_BOSS_PRINCESS_IRON_FAN_CLONE_NAME"] = "Princess Iron Fan Clone",
["ENEMY_BOSS_PRINCESS_IRON_FAN_NAME"] = "Princess Iron Fan",
["ENEMY_BOSS_REDBOY_TEEN_NAME"] = "Red Boy",
["ENEMY_BOSS_SPIDER_QUEEN_NAME"] = "Mygale",
["ENEMY_BRUTE_WELDER_DESCRIPTION"] = "These workers will use their torches on enemies unprovoked.",
["ENEMY_BRUTE_WELDER_EXTRA"] = "- Blocks a tower when killed",
["ENEMY_BRUTE_WELDER_NAME"] = "Brute Welder",
["ENEMY_BURNING_TREANT_DESCRIPTION"] = "Wooden creatures with malicious intent, born in the midst of a burning forest.",
["ENEMY_BURNING_TREANT_EXTRA"] = "- Area damage\n- Leaves a flaming ground on attack",
["ENEMY_BURNING_TREANT_NAME"] = "Burning Treant",
["ENEMY_CITIZEN_1_DESCRIPTION"] = "Sinister fishermen that serve the Princess, smuggling their way through the black market.",
["ENEMY_CITIZEN_1_EXTRA"] = "- Weak",
["ENEMY_CITIZEN_1_NAME"] = "Old Fishmonger",
["ENEMY_CITIZEN_2_DESCRIPTION"] = "Sinister fishermen that serve the Princess, smuggling their way through the black market.",
["ENEMY_CITIZEN_2_EXTRA"] = "- Weak",
["ENEMY_CITIZEN_2_NAME"] = "Blackwater Fisher",
["ENEMY_CITIZEN_3_DESCRIPTION"] = "Sinister fishermen that serve the Princess, smuggling their way through the black market.",
["ENEMY_CITIZEN_3_EXTRA"] = "- Weak",
["ENEMY_CITIZEN_3_NAME"] = "Ink Smuggler",
["ENEMY_CITIZEN_4_DESCRIPTION"] = "Sinister fishermen that serve the Princess, smuggling their way through the black market.",
["ENEMY_CITIZEN_4_EXTRA"] = "- Weak",
["ENEMY_CITIZEN_4_NAME"] = "Tide Poacher",
["ENEMY_CITIZEN_DESCRIPTION"] = "Standard proletariat people forced to fight for The Queen.",
["ENEMY_CITIZEN_EXTRA"] = "- Weak",
["ENEMY_CITIZEN_NAME"] = "Citizen",
["ENEMY_COMMON_CLONE_DESCRIPTION"] = "Not remarkable, not special, much like the original.",
["ENEMY_COMMON_CLONE_EXTRA"] = "- Pushes forward mindlessly",
["ENEMY_COMMON_CLONE_NAME"] = "Clone ",
["ENEMY_CORRUPTED_ELF_DESCRIPTION"] = "Reanimated elves that hunt enemies from afar. Even in death they remain utterly effective.",
["ENEMY_CORRUPTED_ELF_EXTRA"] = "- Spawns a specter when it dies",
["ENEMY_CORRUPTED_ELF_NAME"] = "Revenant Ranger",
["ENEMY_CORRUPTED_STALKER_DESCRIPTION"] = "Cloud Stalkers tamed by the Acolytes, they now serve as mounts for the Cult.",
["ENEMY_CORRUPTED_STALKER_EXTRA"] = "- Flying",
["ENEMY_CORRUPTED_STALKER_NAME"] = "Tamed Stalker",
["ENEMY_CORRUPTED_STALKER_SPECIAL"] = "Flying",
["ENEMY_CROCS_BASIC_DESCRIPTION"] = "A proud Crok warrior, still early in life and only a few calories away from transforming into the killing machine it knows it can be.",
["ENEMY_CROCS_BASIC_EGG_DESCRIPTION"] = "Newly born and unstoppable on their feet, \"they grow so fast\" was a phrase invented thanks to these little ones full of surprises.",
["ENEMY_CROCS_BASIC_EGG_EXTRA"] = "- Unblockable\n- Low Armor\n- Hatch into a Gator after a few seconds",
["ENEMY_CROCS_BASIC_EGG_NAME"] = "Crokinder",
["ENEMY_CROCS_BASIC_EXTRA"] = "- Melee",
["ENEMY_CROCS_BASIC_NAME"] = "Gator",
["ENEMY_CROCS_EGG_SPAWNER_DESCRIPTION"] = "This crok packs a nest of trouble! Every few steps, she drops eggs that hatch into frenzied crokinders— a mobile nursery with a serious bite!",
["ENEMY_CROCS_EGG_SPAWNER_EXTRA"] = "- Spawns Crokinders on the Path",
["ENEMY_CROCS_EGG_SPAWNER_NAME"] = "Nesting Gator",
["ENEMY_CROCS_FLIER_DESCRIPTION"] = "Cunning Croks that, in their contempt for natural evolution, forged their own wings to gain an aerial advantage.",
["ENEMY_CROCS_FLIER_EXTRA"] = "- Flying",
["ENEMY_CROCS_FLIER_NAME"] = "Winged Crok",
["ENEMY_CROCS_HYDRA_DESCRIPTION"] = "Two heads are better than one, and hydras prove it. There is an old myth about three-headed beasts like this one, but it's probably a lie.",
["ENEMY_CROCS_HYDRA_EXTRA"] = "- Spawns a third head when it dies\n- Spits poison on the ground",
["ENEMY_CROCS_HYDRA_NAME"] = "Hydra",
["ENEMY_CROCS_QUICKFEET_GATOR_NAME"] = "Quickfeet",
["ENEMY_CROCS_RANGED_DESCRIPTION"] = "Speedy lizard hunters that deal with their enemies using long-range slingshots.",
["ENEMY_CROCS_RANGED_EXTRA"] = "- Fast\n- Ranged",
["ENEMY_CROCS_RANGED_NAME"] = "Lizardshot",
["ENEMY_CROCS_SHAMAN_DESCRIPTION"] = "Magic beings of great importance to the Croks. After all, for a cold-blooded race, the ability to foresee the whims of the skies is a matter of life and death.",
["ENEMY_CROCS_SHAMAN_EXTRA"] = "- Ranged Magic Damage\n- Medium Magic Resistance\n- Heals other crocs\n- Stuns towers",
["ENEMY_CROCS_SHAMAN_NAME"] = "Wise Crok",
["ENEMY_CROCS_TANK_DESCRIPTION"] = "Cornerstones of the Croks' forces, they embrace \"a good defense is the best offense\", using stolen shells in the way they see fit.",
["ENEMY_CROCS_TANK_EXTRA"] = "- High health\n- Medium armor\n- Spins when blocked",
["ENEMY_CROCS_TANK_NAME"] = "Tankzard",
["ENEMY_CRYSTAL_GOLEM_DESCRIPTION"] = "Imbued with outworldly magic from its crystals, these stony effigies are almost unstoppable.",
["ENEMY_CRYSTAL_GOLEM_EXTRA"] = "- Mini-boss\n- Very high armor",
["ENEMY_CRYSTAL_GOLEM_NAME"] = "Crystal Golem",
["ENEMY_CULTBROOD_DESCRIPTION"] = "Half spider, half cultist abominations that charge into battle without fear or mercy.",
["ENEMY_CULTBROOD_EXTRA"] = "- Fast \n- Poison attack \n- If a unit dies while poisoned, it breeds another Cultbrood",
["ENEMY_CULTBROOD_NAME"] = "Cultbrood",
["ENEMY_CUTTHROAT_RAT_DESCRIPTION"] = "Cunning and devious by nature, the rats are keen assassins and infiltrators.",
["ENEMY_CUTTHROAT_RAT_EXTRA"] = "- Fast speed\n- Turns invisible after hitting an enemy",
["ENEMY_CUTTHROAT_RAT_NAME"] = "Cutthroat Rat",
["ENEMY_CUTTHROAT_RAT_SPECIAL"] = "Turns invisible after hitting an enemy.",
["ENEMY_DARKSTEEL_ANVIL_DESCRIPTION"] = "The dwarven answer to war drums. The heavier they look the louder they sing.",
["ENEMY_DARKSTEEL_ANVIL_EXTRA"] = "- Grants armor and speed buffs to enemy units",
["ENEMY_DARKSTEEL_ANVIL_NAME"] = "Darksteel Anvil",
["ENEMY_DARKSTEEL_FIST_DESCRIPTION"] = "Mechanically enhanced to bend metal, will punch other people instead.",
["ENEMY_DARKSTEEL_FIST_EXTRA"] = "- Special attack stuns player units",
["ENEMY_DARKSTEEL_FIST_NAME"] = "Darksteel Fist",
["ENEMY_DARKSTEEL_GUARDIAN_DESCRIPTION"] = "Sturdy battle suits operated by dwarven warriors and powered by fiery engines. Talk about being dressed to kill.",
["ENEMY_DARKSTEEL_GUARDIAN_EXTRA"] = "- Mini-boss\n- Gets frenzied at low health",
["ENEMY_DARKSTEEL_GUARDIAN_NAME"] = "Darksteel Guardian",
["ENEMY_DARKSTEEL_HAMMERER_DESCRIPTION"] = "Warriors as blunt as their weapon of preference.",
["ENEMY_DARKSTEEL_HAMMERER_EXTRA"] = " ",
["ENEMY_DARKSTEEL_HAMMERER_NAME"] = "Darksteel Hammerer",
["ENEMY_DARKSTEEL_HULK_DESCRIPTION"] = "Cranky and with molten steel running through their veins, this is the heaviest dwarves will get.",
["ENEMY_DARKSTEEL_HULK_EXTRA"] = "- Mini-boss\n- At low health, charges through the path dealing damage",
["ENEMY_DARKSTEEL_HULK_NAME"] = "Darksteel Hulk",
["ENEMY_DARKSTEEL_SHIELDER_DESCRIPTION"] = "Protected by huge shields, they push enemies aside as they advance.",
["ENEMY_DARKSTEEL_SHIELDER_EXTRA"] = "- Turns into a Hammerer when defeated",
["ENEMY_DARKSTEEL_SHIELDER_NAME"] = "Darksteel Shielder",
["ENEMY_DEATHWOOD_DESCRIPTION"] = "Weirdwoods corrupted by the dark specters that now wander through the forest wreaking havoc.",
["ENEMY_DEATHWOOD_EXTRA"] = "- Mini-boss\n- Throws a cursed acorn that deals damage in an area",
["ENEMY_DEATHWOOD_NAME"] = "Deathwood",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_DESCRIPTION"] = "The result of Grymbeard's unbridled arrogance. His mental power is only matched by his hideousness.",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_EXTRA"] = "- Flying\n- High magic resistance shield",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_NAME"] = "Deformed Clone",
["ENEMY_DEMON_MINOTAUR_DESCRIPTION"] = "Half-human, half-bull hybrid demons with a devastating charge attack. They know no mercy. ",
["ENEMY_DEMON_MINOTAUR_EXTRA"] = "- High damage\n- Charge attack\n- Can't be instakilled",
["ENEMY_DEMON_MINOTAUR_NAME"] = "Demon Minotaur",
["ENEMY_DOOM_BRINGER_DESCRIPTION"] = "Having lost all their humanity, these fearsome assassins find purpose in serving Death itself.",
["ENEMY_DOOM_BRINGER_EXTRA"] = "- Stuns towers",
["ENEMY_DOOM_BRINGER_NAME"] = "Doombringer",
["ENEMY_DRAINBROOD_DESCRIPTION"] = "An ancient spider with a deadly bite. Some speculate that it is the primary culprit behind the crystallization of the other spiders.",
["ENEMY_DRAINBROOD_EXTRA"] = "- Crystallizes enemies while draining their life",
["ENEMY_DRAINBROOD_NAME"] = "Leechfang",
["ENEMY_DREADEYE_VIPER_DESCRIPTION"] = "Coating their arrows with their own venom, they are deadly foes from afar.",
["ENEMY_DREADEYE_VIPER_EXTRA"] = "- Low magic resistance\n- Poisonous attacks",
["ENEMY_DREADEYE_VIPER_NAME"] = "Dreadeye Viper",
["ENEMY_DREADEYE_VIPER_SPECIAL"] = "Arrows apply poison to the target.",
["ENEMY_DUST_CRYPTID_DESCRIPTION"] = "Before, a marvelous sight, now a haunting visage for those who wander too far.",
["ENEMY_DUST_CRYPTID_EXTRA"] = "- Flying\n- Leaves a cloud of pollen that makes enemies invulnerable to damage",
["ENEMY_DUST_CRYPTID_NAME"] = "Dust Cryptid",
["ENEMY_EVOLVING_SCOURGE_DESCRIPTION"] = "They might look almost cuddly at first sight, but if the Scourge feeds on fallen prey things will get awry fast.",
["ENEMY_EVOLVING_SCOURGE_EXTRA"] = "- Eats fallen units to evolve into a stronger form\n- Instantly evolves into its final form when affected by Glare",
["ENEMY_EVOLVING_SCOURGE_NAME"] = "Evolving Scourge",
["ENEMY_FAN_GUARD_DESCRIPTION"] = "Strong and highly-versatile female warriors, adept at both causing pain and protecting themselves with their magic fans.",
["ENEMY_FAN_GUARD_EXTRA"] = "- Has medium armor and magic resistance while not blocked",
["ENEMY_FAN_GUARD_NAME"] = "Fan Guard",
["ENEMY_FIRE_FOX_DESCRIPTION"] = "Elusive and cute foxes born from fire. Too fast and volatile to be tamed.",
["ENEMY_FIRE_FOX_EXTRA"] = "- Low magic resistance\n- Faster while on flaming ground\n- Leaves a flaming ground on death",
["ENEMY_FIRE_FOX_NAME"] = "Fire Fox",
["ENEMY_FIRE_PHOENIX_DESCRIPTION"] = "Mythical flying creatures that feed from fire itself. They live and die in a blazing flame.",
["ENEMY_FIRE_PHOENIX_EXTRA"] = "- Flying\n- Leaves a flaming ground on death",
["ENEMY_FIRE_PHOENIX_NAME"] = "Zhuque",
["ENEMY_FLAME_GUARD_DESCRIPTION"] = "Striving for their masters' approval, these low-ranking disciples excel with small blades.",
["ENEMY_FLAME_GUARD_EXTRA"] = "- Special attack on flaming ground",
["ENEMY_FLAME_GUARD_NAME"] = "Flame Guard",
["ENEMY_GALE_WARRIOR_DESCRIPTION"] = "Graceful and stylish, these warriors were chosen by their princess and are willing to die for her.",
["ENEMY_GALE_WARRIOR_EXTRA"] = "- Medium armor\n- Causes bleeding every 3 attacks",
["ENEMY_GALE_WARRIOR_NAME"] = "Gale Warrior",
["ENEMY_GLAREBROOD_CRYSTAL_NAME"] = "Glarebrood Crystal",
["ENEMY_GLARELING_DESCRIPTION"] = "Left unchecked, these meek creatures can overrun even the stoutest army.",
["ENEMY_GLARELING_EXTRA"] = "- High speed",
["ENEMY_GLARELING_NAME"] = "Glareling",
["ENEMY_GLARENWARDEN_DESCRIPTION"] = "These abominable spiders are the product of Glarebrood fusioning, making them stronger and sturdier than ever before.",
["ENEMY_GLARENWARDEN_EXTRA"] = "- High Armor\n- Lifesteal on attack",
["ENEMY_GLARENWARDEN_NAME"] = "Glarewarden",
["ENEMY_GOLDEN_EYED_DESCRIPTION"] = "Colossal lion-beasts whose roar's deepness and intensity is unmatched across all kingdoms.",
["ENEMY_GOLDEN_EYED_EXTRA"] = "- Miniboss\n- Stuns units in an area when blocked\n- Stuns towers ",
["ENEMY_GOLDEN_EYED_NAME"] = "Golden-Eyed Beast",
["ENEMY_HARDENED_HORROR_DESCRIPTION"] = "These breed of Horrors have sharpened blades for hands and will carve out a path through their enemies.",
["ENEMY_HARDENED_HORROR_EXTRA"] = "- Rolls at high speed and cannot be blocked when affected by Glare",
["ENEMY_HARDENED_HORROR_NAME"] = "Bladeclaw Horror",
["ENEMY_HELLFIRE_WARLOCK_DESCRIPTION"] = "Extremely dangerous warlocks, experts at summoning both creatures and flames from the depths of hell.",
["ENEMY_HELLFIRE_WARLOCK_EXTRA"] = "- Casts fireballs\n- Summons a Nine-Tailed Fox",
["ENEMY_HELLFIRE_WARLOCK_NAME"] = "Hellfire Warlock",
["ENEMY_HOG_INVADER_DESCRIPTION"] = "Filthy and disorganized troublemakers. The bulk of the wildbeast army.",
["ENEMY_HOG_INVADER_EXTRA"] = "- Low HP",
["ENEMY_HOG_INVADER_NAME"] = "Hog Invader",
["ENEMY_HYENA5_DESCRIPTION"] = "Gruesome fighters with a penchant for feasting on their fallen foes.",
["ENEMY_HYENA5_EXTRA"] = "- Medium armor\n- Heals by eating fallen player units",
["ENEMY_HYENA5_NAME"] = "Rottenfang Hyena",
["ENEMY_HYENA5_SPECIAL"] = "Heals by eating killed blocking enemies.",
["ENEMY_KILLERTILE_DESCRIPTION"] = "Mighty destroyers, years of combat experience (or a chicken), have left them with a strong and lethal bite.",
["ENEMY_KILLERTILE_EXTRA"] = "- High Health\n- High Damage",
["ENEMY_KILLERTILE_NAME"] = "Killertile",
["ENEMY_LESSER_EYE_DESCRIPTION"] = "Evil eyes that float over the battlefield, acting as scouts of the Vile Spawners.",
["ENEMY_LESSER_EYE_EXTRA"] = "- Flying",
["ENEMY_LESSER_EYE_NAME"] = "Lesser Eye",
["ENEMY_LESSER_SISTER_DESCRIPTION"] = "With their malicious magic, Twisted Sisters ease Nightmares into the physical world.",
["ENEMY_LESSER_SISTER_EXTRA"] = "- High magic resistance\n- Summons Nightmares",
["ENEMY_LESSER_SISTER_NAME"] = "Twisted Sister",
["ENEMY_LESSER_SISTER_NIGHTMARE_DESCRIPTION"] = "Ethereal shadows weaved from the book of chants of the Sisters of the Cult.",
["ENEMY_LESSER_SISTER_NIGHTMARE_EXTRA"] = "- Cannot be targeted unless it is blocked by melee units",
["ENEMY_LESSER_SISTER_NIGHTMARE_NAME"] = "Nightmare",
["ENEMY_LESSER_SISTER_SPECIAL"] = "Summons Nightmares",
["ENEMY_MACHINIST_DESCRIPTION"] = "Obsessed with cogs and engines, this dwarf lives for industrial automation and warfare.",
["ENEMY_MACHINIST_EXTRA"] = "- Operates an assembly line that spawns Sentries",
["ENEMY_MACHINIST_NAME"] = "Grymbeard",
["ENEMY_MAD_TINKERER_DESCRIPTION"] = "Tinkerers don't care much about anything other than building things out of junk.",
["ENEMY_MAD_TINKERER_EXTRA"] = "- Creates Drones using scrap left by other units",
["ENEMY_MAD_TINKERER_NAME"] = "Mad Tinkerer",
["ENEMY_MINDLESS_HUSK_DESCRIPTION"] = "Due to their appearance, Husks seem to be weak enemies, yet each one of them carries a surprise into the battlefield.",
["ENEMY_MINDLESS_HUSK_EXTRA"] = "- Spawns a Glareling on death",
["ENEMY_MINDLESS_HUSK_NAME"] = "Mindless Husk",
["ENEMY_NINE_TAILED_FOX_DESCRIPTION"] = "Mysterious creatures both beautiful and powerful. Will blaze through enemies like a raging bonfire.",
["ENEMY_NINE_TAILED_FOX_EXTRA"] = "- Medium magic resistance\n- Teleports forward, stunning enemies on arrival\n- Area damage",
["ENEMY_NINE_TAILED_FOX_NAME"] = "Nine-Tailed Fox",
["ENEMY_NOXIOUS_HORROR_DESCRIPTION"] = "Amphibian looking creatures that spit poisonous bile over their prey. Also dangerous up close.",
["ENEMY_NOXIOUS_HORROR_EXTRA"] = "- Gains Magic Resistance and emits a poisonous aura when affected by Glare",
["ENEMY_NOXIOUS_HORROR_NAME"] = "Noxious Spitter",
["ENEMY_PALACE_GUARD_DESCRIPTION"] = "Low-talented trainees whose only motivation is to fulfill their Princess’ wishes.",
["ENEMY_PALACE_GUARD_EXTRA"] = "- Melee\n- Low armor",
["ENEMY_PALACE_GUARD_NAME"] = "Palace Guard",
["ENEMY_PUMPKIN_WITCH_DESCRIPTION"] = "Enemy turned into Pumpkling. Easily stompable.",
["ENEMY_PUMPKIN_WITCH_EXTRA"] = "- Unblockable",
["ENEMY_PUMPKIN_WITCH_FLYING_DESCRIPTION"] = "Enemy turned into Pumpkling. Easily stompable.",
["ENEMY_PUMPKIN_WITCH_FLYING_EXTRA"] = "- Unblockable",
["ENEMY_PUMPKIN_WITCH_FLYING_NAME"] = "Pumpkling",
["ENEMY_PUMPKIN_WITCH_NAME"] = "Pumpkling",
["ENEMY_QIONGQI_DESCRIPTION"] = "Fierceful flying lions that attack with the power of lightning. Kings of the tempest.",
["ENEMY_QIONGQI_EXTRA"] = "- Flying\n- Very high damage\n- Medium magic resistance",
["ENEMY_QIONGQI_NAME"] = "Qiongqi",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_DESCRIPTION"] = "After years of delivering chickens to their brothers, they have become so fast that sometimes they forget to bring the chicken.",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_EXTRA"] = "- Fast\n- Ranged\n- It can deliver a chicken leg to a Gator, evolving it",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_NAME"] = "Quickfeet",
["ENEMY_QUICKFEET_GATOR_DESCRIPTION"] = "After years of delivering chickens to their brothers, they have become so fast that sometimes they forget to bring the chicken.",
["ENEMY_QUICKFEET_GATOR_EXTRA"] = "- Fast\n- Ranged\n- It can deliver a chicken leg to a Gator, evolving it",
["ENEMY_QUICKFEET_GATOR_NAME"] = "Quickfeet",
["ENEMY_REVENANT_HARVESTER_DESCRIPTION"] = "Priestesses of old now wander through the forest, spreading their influence through specters.",
["ENEMY_REVENANT_HARVESTER_EXTRA"] = "- Turns nearby specters into Revenant Harvesters",
["ENEMY_REVENANT_HARVESTER_NAME"] = "Revenant Harvester",
["ENEMY_REVENANT_SOULCALLER_DESCRIPTION"] = "Elven mages that suffered the pull of death magic and rose from the earth to summon the specters of the fallen.",
["ENEMY_REVENANT_SOULCALLER_EXTRA"] = "- Disables towers\n- Summons specters",
["ENEMY_REVENANT_SOULCALLER_NAME"] = "Revenant Soulcaller",
["ENEMY_RHINO_DESCRIPTION"] = "A living battering ram that tramples without regards through the battlefield.",
["ENEMY_RHINO_EXTRA"] = "- Mini-boss\n- Charges towards enemies",
["ENEMY_RHINO_NAME"] = "Razing Rhino",
["ENEMY_RHINO_SPECIAL"] = "Charges towards enemies.",
["ENEMY_ROLLING_SENTRY_DESCRIPTION"] = "Once they are shot down, they still prey on the ground.",
["ENEMY_ROLLING_SENTRY_EXTRA"] = "- Turns into scrap when killed\n- Ranged",
["ENEMY_ROLLING_SENTRY_NAME"] = "Rolling Sentry",
["ENEMY_SCRAP_DRONE_DESCRIPTION"] = "Roughly put together with the only aim of pestering the troops.",
["ENEMY_SCRAP_DRONE_EXTRA"] = "- Flying",
["ENEMY_SCRAP_DRONE_NAME"] = "Scrap Drone",
["ENEMY_SCRAP_SPEEDSTER_DESCRIPTION"] = "Loud and annoying, with a need for speed.",
["ENEMY_SCRAP_SPEEDSTER_EXTRA"] = "- Turns into scrap when killed",
["ENEMY_SCRAP_SPEEDSTER_NAME"] = "Scrap Speedster",
["ENEMY_SKUNK_BOMBARDIER_DESCRIPTION"] = "Taking their natural toxins to another level, skunks spread disorder on enemy lines.",
["ENEMY_SKUNK_BOMBARDIER_EXTRA"] = "- Low speed\n- Medium magic resistance\n- Attacks weaken player units\n- Explodes when it dies",
["ENEMY_SKUNK_BOMBARDIER_NAME"] = "Skunk Bombardier",
["ENEMY_SKUNK_BOMBARDIER_SPECIAL"] = "Attacks weaken player units. Explodes when it dies, dealing damage.",
["ENEMY_SMALL_STALKER_DESCRIPTION"] = "Corrupted by the magic of the Cult, these Cloud Stalkers teleport over the battlefield sowing chaos.",
["ENEMY_SMALL_STALKER_EXTRA"] = "- Teleports forward when attacked",
["ENEMY_SMALL_STALKER_NAME"] = "Corrupted Stalker",
["ENEMY_SMALL_STALKER_SPECIAL"] = "Teleports a short distance, evading attacks.",
["ENEMY_SPECTER_DESCRIPTION"] = "Enslaved beyond earthly decay, bound to haunt the living.",
["ENEMY_SPECTER_EXTRA"] = "- Can interact with other enemies and elements",
["ENEMY_SPECTER_NAME"] = "Specter",
["ENEMY_SPIDEAD_DESCRIPTION"] = "Direct descendants of the Spider Queen Mygale, these spiders always find a way to be annoying, even from beyond the grave.",
["ENEMY_SPIDEAD_EXTRA"] = "- Magic resistance\n- Spawns a Spider web on death",
["ENEMY_SPIDEAD_NAME"] = "Silk Daughter",
["ENEMY_SPIDERLING_DESCRIPTION"] = "Spiders enhanced by the Cult's magic. Fast and furious. Will bite.",
["ENEMY_SPIDERLING_EXTRA"] = "- Fast speed\n- Low magic resistance",
["ENEMY_SPIDERLING_NAME"] = "Glarebrood",
["ENEMY_SPIDER_PRIEST_DESCRIPTION"] = "Entangled by their new god, the priests walk onto the battlefield wielding dark magic.",
["ENEMY_SPIDER_PRIEST_EXTRA"] = "- High magic resistance\n- Turns into a Glarewarden when near death",
["ENEMY_SPIDER_PRIEST_NAME"] = "Priest of the web",
["ENEMY_SPIDER_SISTER_DESCRIPTION"] = "As firm believers of the Spider Queen, they wield their magic to summon their kin.",
["ENEMY_SPIDER_SISTER_EXTRA"] = "- Magic resistance\n- Summons Glarebroods",
["ENEMY_SPIDER_SISTER_NAME"] = "Spider Sister",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_DESCRIPTION"] = "Shadow doppelgangers that Mydrias uses to intervene in battle.",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_EXTRA"] = "- Shields enemies from damage\n- Traps towers with dark tentacles",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_NAME"] = "Mydrias' Illusion",
["ENEMY_STORM_ELEMENTAL_DESCRIPTION"] = "Powerful elementals spawned by typhoons, lightning, and anger. A distant cousin of the Ash Spirit.",
["ENEMY_STORM_ELEMENTAL_EXTRA"] = "- High armor\n- Ranged\n- Stuns a nearby tower on death",
["ENEMY_STORM_ELEMENTAL_NAME"] = "Tempest Spirit",
["ENEMY_STORM_SPIRIT_DESCRIPTION"] = "Tiny dragons leaping through storm clouds, deftly evading dangers and foes.",
["ENEMY_STORM_SPIRIT_EXTRA"] = "- Flying\n- Low magic resistance\n- Dashes forward when damaged",
["ENEMY_STORM_SPIRIT_NAME"] = "Storm Drakeling",
["ENEMY_SURVEILLANCE_SENTRY_DESCRIPTION"] = "Dwarven engineered to keep an eye on enemies from the skies.",
["ENEMY_SURVEILLANCE_SENTRY_EXTRA"] = "- Flying\n- Turns into a Rolling Sentry when defeated",
["ENEMY_SURVEILLANCE_SENTRY_NAME"] = "Flying Sentry",
["ENEMY_SURVEYOR_HARPY_DESCRIPTION"] = "Looking for carrion, vultures follow the wildbeasts everywhere.",
["ENEMY_SURVEYOR_HARPY_EXTRA"] = "- Flying",
["ENEMY_SURVEYOR_HARPY_NAME"] = "Patrolling Vulture",
["ENEMY_SURVEYOR_HARPY_SPECIAL"] = "Flying.",
["ENEMY_TERRACOTA_DESCRIPTION"] = "Shadow copies of fallen soldiers who still serve their Princess, even from the underworld.",
["ENEMY_TERRACOTA_EXTRA"] = "- Melee",
["ENEMY_TERRACOTA_NAME"] = "Illusory Decoy",
["ENEMY_TOWER_RAY_SHEEP_DESCRIPTION"] = "Baaaaaa.",
["ENEMY_TOWER_RAY_SHEEP_EXTRA"] = "- Unblockable",
["ENEMY_TOWER_RAY_SHEEP_FLYING_DESCRIPTION"] = "Baaaaaa.",
["ENEMY_TOWER_RAY_SHEEP_FLYING_EXTRA"] = "- Flying",
["ENEMY_TOWER_RAY_SHEEP_FLYING_NAME"] = "Flying Sheep",
["ENEMY_TOWER_RAY_SHEEP_NAME"] = "Sheep",
["ENEMY_TURTLE_SHAMAN_DESCRIPTION"] = "Peaceful looking but mean spirited, shamans keep the wildbeasts patched up and ready to fight.",
["ENEMY_TURTLE_SHAMAN_EXTRA"] = "- Slow speed\n- High HP\n- High magic resistance\n- Heals enemy units",
["ENEMY_TURTLE_SHAMAN_NAME"] = "Turtle Shaman",
["ENEMY_TURTLE_SHAMAN_SPECIAL"] = "Heals enemy units.",
["ENEMY_TUSKED_BRAWLER_DESCRIPTION"] = "More tenacious than the invaders and outfitted with janky armor. Always ready to rumble.",
["ENEMY_TUSKED_BRAWLER_EXTRA"] = "- Low armor",
["ENEMY_TUSKED_BRAWLER_NAME"] = "Tusked Brawler",
["ENEMY_UNBLINDED_ABOMINATION_DESCRIPTION"] = "Fully corrupted priests of the Cult, known for their savagery in combat.",
["ENEMY_UNBLINDED_ABOMINATION_EXTRA"] = "- Devours units with low health",
["ENEMY_UNBLINDED_ABOMINATION_NAME"] = "Cult Abomination",
["ENEMY_UNBLINDED_ABOMINATION_SPECIAL"] = "Occasionally devours a unit with low health.",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_DESCRIPTION"] = "After enslaving the elves, some Abominations were appointed to make sure work at the mines goes smoothly.",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_EXTRA"] = "- Must be killed to free the elves",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_NAME"] = "Taskmaster Abomination",
["ENEMY_UNBLINDED_PRIEST_DESCRIPTION"] = "Between prayers and the occult, the priests walk into battle wielding dark magic.",
["ENEMY_UNBLINDED_PRIEST_EXTRA"] = "- High magic resistance\n- Turns into an Abomination when near death",
["ENEMY_UNBLINDED_PRIEST_NAME"] = "Cult Priest",
["ENEMY_UNBLINDED_PRIEST_SPECIAL"] = "At low health, turns into an Abomination.",
["ENEMY_UNBLINDED_SHACKLER_DESCRIPTION"] = "Channeling corrupt magic through the crystals embedded on their arms, the Shacklers are fearsome foes in close quarters.",
["ENEMY_UNBLINDED_SHACKLER_EXTRA"] = "- Medium magic resistance\n- Disables towers at low health",
["ENEMY_UNBLINDED_SHACKLER_NAME"] = "Shackler",
["ENEMY_UNBLINDED_SHACKLER_SPECIAL"] = "Binds towers, preventing them from attacking",
["ENEMY_VILE_SPAWNER_DESCRIPTION"] = "Flinging their many flying eyes over enemies, the Vile Spawners are always watching in every direction.",
["ENEMY_VILE_SPAWNER_EXTRA"] = "- Spawns Lesser Eyes",
["ENEMY_VILE_SPAWNER_NAME"] = "Vile Spawner",
["ENEMY_WATER_SORCERESS_DESCRIPTION"] = "Timeworn elemental casters commanding water's power to heal allies and vanquish foes from afar.",
["ENEMY_WATER_SORCERESS_EXTRA"] = "- Ranged\n- Medium magic resistance\n- Heals allies",
["ENEMY_WATER_SORCERESS_NAME"] = "Water Master",
["ENEMY_WATER_SPIRIT_DESCRIPTION"] = "Soulless water entities surging in relentless waves, ravaging the shores with fury.",
["ENEMY_WATER_SPIRIT_EXTRA"] = "- Low magic resistance\n- Can spawn from water",
["ENEMY_WATER_SPIRIT_NAME"] = "Water Spirit",
["ENEMY_WATER_SPIRIT_SPAWNLESS_DESCRIPTION"] = "Soulless water entities surging in relentless waves, ravaging the shores with fury.",
["ENEMY_WATER_SPIRIT_SPAWNLESS_EXTRA"] = "- Low magic resistance\n- Can spawn from water",
["ENEMY_WATER_SPIRIT_SPAWNLESS_NAME"] = "Water Spirit",
["ENEMY_WUXIAN_DESCRIPTION"] = "Masters of fire and flame, these resilient powerful wizards can reduce an army to ashes.",
["ENEMY_WUXIAN_EXTRA"] = "- Ranged\n- Medium armor\n- Special attack on flaming ground",
["ENEMY_WUXIAN_NAME"] = "Wuxian",
["ERROR_MESSAGE_GENERIC"] = "Ups! Something went wrong.",
["Earn huge bonus points and gold by calling waves earlier!"] = "Earn huge bonus points and gold by calling waves earlier!",
["Encyclopedia"] = "Encyclopedia",
["Enemies"] = "Enemies",
["Extreme"] = "Extreme",
["FIRST_WEEK_PACK"] = "Gift",
["Face an endless unrelenting enemy force and try to defeat as many as possible to comete for the best score!"] = "Face an endless unrelenting enemy force and try to defeat as many as possible to compete for the best score!",
["Face an endless unrelenting enemy force and try to defeat as many as possible to compete for the best score!"] = "Face an endless unrelenting enemy force and try to defeat as many as possible to compete for the best score!",
["Fast"] = "Fast",
["For beginners to strategy games!"] = "For beginners to strategy games!",
["GAME_TITLE_KR5"] = "Kingdom Rush 5: Alliance",
["GEMS_BARREL_NAME"] = "BARREL OF GEMS",
["GEMS_CHEST_NAME"] = "CHEST OF GEMS",
["GEMS_HANDFUL_NAME"] = "HANDFUL OF GEMS",
["GEMS_MOUNTAIN_NAME"] = "MOUNTAIN OF GEMS",
["GEMS_POUCH_NAME"] = "POUCH OF GEMS",
["GEMS_WAGON_NAME"] = "WAGON OF GEMS",
["GET_ALL_AWESOME_HEROES"] = "GET ALL OF THESE AWESOME HEROES",
["GET_THIS_AWESOME"] = "GET THIS\nAWESOME HERO",
["GET_THIS_AWESOME_2"] = "GET THESE\n AWESOME HEROES",
["GET_THIS_AWESOME_3"] = "GET THESE\n AWESOME HEROES",
["GIFT_CLAIMED"] = "Gift claimed",
["GOOGLE_PLAY"] = "GOOGLE PLAY",
["Got it!"] = "Got it!",
["Great"] = "Great",
["HERO LEVEL UP!"] = "HERO LEVEL UP!",
["HERO ROOM"] = "HEROES",
["HERO UNLOCKED!"] = "HERO UNLOCKED!",
["HEROES"] = "HEROES",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_1"] = "Summons gryphons that fly above the area during %$heroes.hero_bird.ultimate.bird.duration[2]%$ seconds striking enemies, dealing %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[2]%$ damage each time.",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_2"] = "Summons gryphons that fly above the area during %$heroes.hero_bird.ultimate.bird.duration[3]%$ seconds striking enemies, dealing %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[3]%$ damage each time.",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_3"] = "Summons gryphons that fly above the area during %$heroes.hero_bird.ultimate.bird.duration[4]%$ seconds striking enemies, dealing %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[4]%$ damage each time.",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_DESCRIPTION"] = "Summons gryphons that fly above the area attacking enemies.",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_NAME"] = "Birds of Fray",
["HERO_BIRD_BIRDS_OF_PREY_TITLE"] = "BIRDS OF FRAY",
["HERO_BIRD_CLASS"] = "the Ace Rider",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_1"] = "Throws an explosive that splits over enemies, dealing %$heroes.hero_bird.cluster_bomb.explosion_damage_min[1]%$ damage each and setting the floor on fire %$heroes.hero_bird.cluster_bomb.fire_duration[1]%$ seconds, burning enemies for %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$ damage over 3 seconds.",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_2"] = "Throws an explosive that splits over enemies, dealing %$heroes.hero_bird.cluster_bomb.explosion_damage_min[2]%$ damage each and setting the floor on fire %$heroes.hero_bird.cluster_bomb.fire_duration[2]%$ seconds, burning enemies for %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$ damage over 3 seconds.",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_3"] = "Throws an explosive that splits over enemies, dealing %$heroes.hero_bird.cluster_bomb.explosion_damage_min[3]%$ damage each and setting the floor on fire %$heroes.hero_bird.cluster_bomb.fire_duration[3]%$ seconds, burning enemies for %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$ damage over 3 seconds.",
["HERO_BIRD_CLUSTER_BOMB_TITLE"] = "CARPET BOMBING",
["HERO_BIRD_DESC"] = "The courageous gryphon rider flies into battle brandishing an arsenal of steel and fire. While he joined the Alliance reluctantly since the Dark Army invaded his home, Broden agreed to rain destruction over the Cult as a means to restore the status quo in Linirea.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_1"] = "The gryphon dives to the ground to devour an enemy of up to %$heroes.hero_bird.eat_instakill.hp_max[1]%$ health.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_2"] = "The gryphon dives to the ground to devour an enemy of up to %$heroes.hero_bird.eat_instakill.hp_max[2]%$ health.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_3"] = "The gryphon dives to the ground to devour an enemy of up to %$heroes.hero_bird.eat_instakill.hp_max[3]%$ health.",
["HERO_BIRD_EAT_INSTAKILL_TITLE"] = "HUNTING DIVE",
["HERO_BIRD_GATTLING_DESCRIPTION_1"] = "Rains bullets against an enemy, dealing %$heroes.hero_bird.gattling.s_damage_min[1]%$-%$heroes.hero_bird.gattling.s_damage_max[1]%$ physical damage.",
["HERO_BIRD_GATTLING_DESCRIPTION_2"] = "Rains bullets against an enemy, dealing %$heroes.hero_bird.gattling.s_damage_min[2]%$-%$heroes.hero_bird.gattling.s_damage_max[2]%$ physical damage.",
["HERO_BIRD_GATTLING_DESCRIPTION_3"] = "Rains bullets against an enemy, dealing %$heroes.hero_bird.gattling.s_damage_min[3]%$-%$heroes.hero_bird.gattling.s_damage_max[3]%$ physical damage.",
["HERO_BIRD_GATTLING_TITLE"] = "EXEMPLARY LEAD",
["HERO_BIRD_NAME"] = "Broden",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_1"] = "The gryphon produces a deafening screech, stunning enemies for %$heroes.hero_bird.shout_stun.stun_duration[1]%$ second and slowing them for %$heroes.hero_bird.shout_stun.slow_duration[1]%$ seconds afterwards.",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_2"] = "The gryphon produces a deafening screech, stunning enemies for %$heroes.hero_bird.shout_stun.stun_duration[2]%$ seconds and slowing them for %$heroes.hero_bird.shout_stun.slow_duration[2]%$ seconds afterwards.",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_3"] = "The gryphon produces a deafening screech, stunning enemies for %$heroes.hero_bird.shout_stun.stun_duration[3]%$ seconds and slowing them for %$heroes.hero_bird.shout_stun.slow_duration[3]%$ seconds afterwards.",
["HERO_BIRD_SHOUT_STUN_TITLE"] = "TERROR SHRIEK",
["HERO_BUILDER_CLASS"] = "Master Foreman",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_1"] = "Builds a makeshift tower that attacks passing enemies for %$heroes.hero_builder.defensive_turret.duration[1]%$ seconds, dealing %$heroes.hero_builder.defensive_turret.attack.damage_min[1]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[1]%$ physical damage per attack.",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_2"] = "Builds a makeshift tower that attacks passing enemies for %$heroes.hero_builder.defensive_turret.duration[2]%$ seconds, dealing %$heroes.hero_builder.defensive_turret.attack.damage_min[2]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[2]%$ physical damage per attack.",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_3"] = "Builds a makeshift tower that attacks passing enemies for %$heroes.hero_builder.defensive_turret.duration[3]%$ seconds, dealing %$heroes.hero_builder.defensive_turret.attack.damage_min[3]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[3]%$ physical damage per attack.",
["HERO_BUILDER_DEFENSIVE_TURRET_TITLE"] = "DEFENSIVE TURRET",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_1"] = "Quickly spins his wooden beam, dealing %$heroes.hero_builder.demolition_man.s_damage_min[1]%$-%$heroes.hero_builder.demolition_man.s_damage_max[1]%$ physical damage to enemies around him.",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_2"] = "Quickly spins his wooden beam, dealing %$heroes.hero_builder.demolition_man.s_damage_min[2]%$-%$heroes.hero_builder.demolition_man.s_damage_max[2]%$ physical damage to enemies around him.",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_3"] = "Quickly spins his wooden beam, dealing %$heroes.hero_builder.demolition_man.s_damage_min[3]%$-%$heroes.hero_builder.demolition_man.s_damage_max[3]%$ physical damage to enemies around him.",
["HERO_BUILDER_DEMOLITION_MAN_TITLE"] = "DEMOLITION MAN",
["HERO_BUILDER_DESC"] = "Years in charge of constructing the Linirean defenses taught Torres a thing or two about battle. Now that the whole kingdom is in danger (and bored of watching from the sidelines) he uses all his tools and knowledge on the fray.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_1"] = "Torres stops fighting to eat a snack, healing himself for %$heroes.hero_builder.lunch_break.heal_hp[1]%$ health.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_2"] = "Torres stops fighting to eat a snack, healing himself for %$heroes.hero_builder.lunch_break.heal_hp[2]%$ health.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_3"] = "Torres stops fighting to eat a snack, healing himself for %$heroes.hero_builder.lunch_break.heal_hp[3]%$ health.",
["HERO_BUILDER_LUNCH_BREAK_TITLE"] = "LUNCH BREAK",
["HERO_BUILDER_NAME"] = "Torres",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_1"] = "Calls two builders that fight by his side for %$heroes.hero_builder.overtime_work.soldier.duration%$ seconds.",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_2"] = "The builders have %$heroes.hero_builder.overtime_work.soldier.hp_max[2]%$ health and deal %$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[2]%$ physical damage. They fight for %$heroes.hero_builder.overtime_work.soldier.duration%$ seconds.",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_3"] = "The builders have %$heroes.hero_builder.overtime_work.soldier.hp_max[3]%$ health and deal %$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[3]%$ physical damage. They fight for %$heroes.hero_builder.overtime_work.soldier.duration%$ seconds.",
["HERO_BUILDER_OVERTIME_WORK_TITLE"] = "MEN AT WORK",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_1"] = "Drops a giant steel ball on the path, dealing %$heroes.hero_builder.ultimate.damage[2]%$ physical damage and stunning enemies for %$heroes.hero_builder.ultimate.stun_duration[2]%$ seconds.",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_2"] = "Drops a giant steel ball on the path, dealing %$heroes.hero_builder.ultimate.damage[3]%$ physical damage and stunning enemies for %$heroes.hero_builder.ultimate.stun_duration[3]%$ seconds.",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_3"] = "Drops a giant steel ball on the path, dealing %$heroes.hero_builder.ultimate.damage[4]%$ physical damage and stunning enemies for %$heroes.hero_builder.ultimate.stun_duration[4]%$ seconds.",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_DESCRIPTION"] = "Drops a wrecking ball on the path, damaging enemies.",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_NAME"] = "Wrecking Ball",
["HERO_BUILDER_WRECKING_BALL_TITLE"] = "WRECKING BALL",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_1"] = "Sylvara unlocks her true form for %$heroes.hero_dragon_arb.ultimate.duration[2]%$ seconds, in which she gains %$heroes.hero_dragon_arb.ultimate.s_bonuses[2]%$% damage, speed, resistances, and evolves some of her powers.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_2"] = "Sylvara unlocks her true form for %$heroes.hero_dragon_arb.ultimate.duration[3]%$ seconds, in which she gains %$heroes.hero_dragon_arb.ultimate.s_bonuses[3]%$% damage, speed, resistances, and evolves some of her powers.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_3"] = "Sylvara unlocks her true form for %$heroes.hero_dragon_arb.ultimate.duration[4]%$ seconds, in which she gains %$heroes.hero_dragon_arb.ultimate.s_bonuses[4]%$% damage, speed, resistances, and evolves some of her powers.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_DESCRIPTION"] = "Unleash Sylvara's true form.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_NAME"] = "Inner Nature",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_TITLE"] = "Inner Nature",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_1"] = "Transforms Green patches into Arboreans that fight for %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[1]%$ seconds. During Inner Nature, it summons stronger Arboreans.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_2"] = "Transforms Green patches into Arboreans that fight for %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[2]%$ seconds. During Inner Nature, it summons stronger Arboreans.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_3"] = "Transforms Green patches into Arboreans that fight for %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[3]%$ seconds. During Inner Nature, it summons stronger Arboreans.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_TITLE"] = "Call of the forest",
["HERO_DRAGON_ARB_CLASS"] = "Nature Force",
["HERO_DRAGON_ARB_DESC"] = "The nature dragon and protector of the Arboreans, she weaves forests with her breath and makes the wind dance with her wings. Like nature itself, she can be both caring and punishing. Make sure not to litter!",
["HERO_DRAGON_ARB_NAME"] = "Sylvara",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_1"] = "Every %$heroes.hero_dragon_arb.thorn_bleed.cooldown[1]%$ seconds, Sylvara empowers her next breath to damage enemies depending on their speed. During Inner Nature, it has a %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[1]%$% chance to instakill.",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_2"] = "Every %$heroes.hero_dragon_arb.thorn_bleed.cooldown[2]%$ seconds, Sylvara empowers her next breath to damage enemies depending on their speed. During Inner Nature, it has a %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[2]%$% chance to instakill.",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_3"] = "Every %$heroes.hero_dragon_arb.thorn_bleed.cooldown[3]%$ seconds, Sylvara empowers her next breath to damage enemies depending on their speed. During Inner Nature, it has a %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[3]%$% chance to instakill.",
["HERO_DRAGON_ARB_THORN BLEED_TITLE"] = "Thorny Breath",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_1"] = "Increase nearby towers' damage by %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[1]%$% for %$heroes.hero_dragon_arb.tower_runes.duration[1]%$ seconds.",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_2"] = "Increase nearby towers' damage by %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[2]%$% for %$heroes.hero_dragon_arb.tower_runes.duration[2]%$ seconds.",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_3"] = "Increase nearby towers' damage by %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[3]%$% for %$heroes.hero_dragon_arb.tower_runes.duration[3]%$ seconds.",
["HERO_DRAGON_ARB_TOWER RUNES_TITLE"] = "Deep Roots",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_1"] = "Summon plants near towers that last %$heroes.hero_dragon_arb.tower_plants.duration[1]%$ seconds. Depending on their allegiance, they become poisonous plants that deal damage and slow, or healer plants that heal allies.",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_2"] = "Summon plants near towers that last %$heroes.hero_dragon_arb.tower_plants.duration[2]%$ seconds. Depending on their allegiance, they become poisonous plants that deal damage and slow, or healer plants that heal allies.",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_3"] = "Summon plants near towers that last %$heroes.hero_dragon_arb.tower_plants.duration[3]%$ seconds. Depending on their allegiance, they become poisonous plants that deal damage and slow, or healer plants that heal allies.",
["HERO_DRAGON_ARB_TOWER_PLANTS_TITLE"] = "Lifebringer",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_1"] = "Launches %$heroes.hero_dragon_bone.burst.proj_count[1]%$ magic projectiles, each dealing %$heroes.hero_dragon_bone.burst.damage_min[1]%$-%$heroes.hero_dragon_bone.burst.damage_max[1]%$ true damage and applying plague.",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_2"] = "Launches %$heroes.hero_dragon_bone.burst.proj_count[2]%$ magic projectiles, each dealing %$heroes.hero_dragon_bone.burst.damage_min[2]%$-%$heroes.hero_dragon_bone.burst.damage_max[2]%$ true damage and applying plague.",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_3"] = "Launches %$heroes.hero_dragon_bone.burst.proj_count[3]%$ magic projectiles, each dealing %$heroes.hero_dragon_bone.burst.damage_min[3]%$-%$heroes.hero_dragon_bone.burst.damage_max[3]%$ true damage and applying plague.",
["HERO_DRAGON_BONE_BURST_TITLE"] = "SPREADING BURST",
["HERO_DRAGON_BONE_CLASS"] = "Dracolich",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_1"] = "Covers an area with a pestilent cloud that applies plague to enemies and slows them for %$heroes.hero_dragon_bone.cloud.duration[1]%$ seconds.",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_2"] = "Covers an area with a pestilent cloud that applies plague to enemies and slows them for %$heroes.hero_dragon_bone.cloud.duration[2]%$ seconds.",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_3"] = "Covers an area with a pestilent cloud that applies plague to enemies and slows them for %$heroes.hero_dragon_bone.cloud.duration[3]%$ seconds.",
["HERO_DRAGON_BONE_CLOUD_TITLE"] = "PLAGUE CLOUD",
["HERO_DRAGON_BONE_DESC"] = "After being freed by Vez'nan during his campaign of conquest, Bonehart offered to repay his debt by using his powers to scour the land in search of magic users that might pose a threat to the Dark Wizard's plans.",
["HERO_DRAGON_BONE_NAME"] = "Bonehart",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_1"] = "Lunges towards the path, dealing %$heroes.hero_dragon_bone.nova.damage_min[1]%$-%$heroes.hero_dragon_bone.nova.damage_max[1]%$ explosive damage to enemies and applying plague.",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_2"] = "Lunges towards the path, dealing %$heroes.hero_dragon_bone.nova.damage_min[2]%$-%$heroes.hero_dragon_bone.nova.damage_max[2]%$ explosive damage to enemies and applying plague.",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_3"] = "Lunges towards the path, dealing %$heroes.hero_dragon_bone.nova.damage_min[3]%$-%$heroes.hero_dragon_bone.nova.damage_max[3]%$ explosive damage to enemies and applying plague.",
["HERO_DRAGON_BONE_NOVA_TITLE"] = "DISEASE NOVA",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_1"] = "Throws %$heroes.hero_dragon_bone.rain.bones_count[1]%$ bone spines towards enemies, dealing %$heroes.hero_dragon_bone.rain.damage_min[1]%$-%$heroes.hero_dragon_bone.rain.damage_max[1]%$ true damage and stunning them briefly.",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_2"] = "Throws %$heroes.hero_dragon_bone.rain.bones_count[2]%$ bone spines towards enemies, dealing %$heroes.hero_dragon_bone.rain.damage_min[2]%$-%$heroes.hero_dragon_bone.rain.damage_max[2]%$ true damage and stunning them briefly.",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_3"] = "Throws %$heroes.hero_dragon_bone.rain.bones_count[3]%$ bone spines towards enemies, dealing %$heroes.hero_dragon_bone.rain.damage_min[3]%$-%$heroes.hero_dragon_bone.rain.damage_max[3]%$ true damage and stunning them briefly.",
["HERO_DRAGON_BONE_RAIN_TITLE"] = "SPINE RAIN",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_1"] = "Summons two bone drakes. Each drake has %$heroes.hero_dragon_bone.ultimate.dog.hp[2]%$ health and deals %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[2]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[2]%$ physical damage.",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_2"] = "Summons two bone drakes. Each drake has %$heroes.hero_dragon_bone.ultimate.dog.hp[3]%$ health and deals %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[3]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[3]%$ physical damage.",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_3"] = "Summons two bone drakes. Each drake has %$heroes.hero_dragon_bone.ultimate.dog.hp[4]%$ health and deals %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[4]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[4]%$ physical damage.",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_DESCRIPTION"] = "Summons two bone drakes.",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_NAME"] = "Raise Drakes",
["HERO_DRAGON_BONE_RAISE_DRAKES_TITLE"] = "RAISE DRAKES",
["HERO_DRAGON_GEM_CLASS"] = "Unbroken",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_1"] = "Encases an enemy of up to %$heroes.hero_dragon_gem.crystal_instakill.hp_max[1]%$ health in a crystal for a few seconds. The crystal then explodes, killing the target instantly and dealing %$heroes.hero_dragon_gem.crystal_instakill.s_damage[1]%$ true damage around it.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_2"] = "Encases an enemy of up to %$heroes.hero_dragon_gem.crystal_instakill.hp_max[2]%$ health in a crystal for a few seconds. The crystal then explodes, killing the target instantly and dealing %$heroes.hero_dragon_gem.crystal_instakill.s_damage[2]%$ true damage around it.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_3"] = "Encases an enemy of up to %$heroes.hero_dragon_gem.crystal_instakill.hp_max[3]%$ health in a crystal for a few seconds. The crystal then explodes, killing the target instantly and dealing %$heroes.hero_dragon_gem.crystal_instakill.s_damage[3]%$ true damage around it.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_TITLE"] = "GARNET TOMB",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_1"] = "Throws a crystal on the path that reduces enemy speed by %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% and deals %$heroes.hero_dragon_gem.crystal_totem.s_damage[1]%$ magic damage every 1 seconds around it. Lasts for %$heroes.hero_dragon_gem.crystal_totem.duration[1]%$ seconds.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_2"] = "Throws a crystal on the path that reduces enemy speed by %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% and deals %$heroes.hero_dragon_gem.crystal_totem.s_damage[2]%$ magic damage every 1 seconds around it. Lasts for %$heroes.hero_dragon_gem.crystal_totem.duration[2]%$ seconds.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_3"] = "Throws a crystal on the path that reduces enemy speed by %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% and deals %$heroes.hero_dragon_gem.crystal_totem.s_damage[3]%$ magic damage every 1 seconds around it. Lasts for %$heroes.hero_dragon_gem.crystal_totem.duration[3]%$ seconds.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_TITLE"] = "POWER CONDUIT",
["HERO_DRAGON_GEM_DESC"] = "Kosmyr's isolated life was interrupted when the Cult began their operations in the Forsaken Canyon. Wanting to get rid of the intruders, the dragon made a deal with Vez'nan to join the Alliance against a mutual enemy.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_1"] = "Summons %$heroes.hero_dragon_gem.ultimate.max_shards[2]%$ crystal barrages that deal %$heroes.hero_dragon_gem.ultimate.damage_min[2]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[2]%$ true damage to enemies caught in the area.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_2"] = "Summons %$heroes.hero_dragon_gem.ultimate.max_shards[3]%$ crystal barrages that deal %$heroes.hero_dragon_gem.ultimate.damage_min[3]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[3]%$ true damage to enemies caught in the area.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_3"] = "Summons %$heroes.hero_dragon_gem.ultimate.max_shards[4]%$ crystal barrages that deal %$heroes.hero_dragon_gem.ultimate.damage_min[4]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[4]%$ true damage to enemies caught in the area.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_DESCRIPTION"] = "Throws various crystal barrages against enemies.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_NAME"] = "Crystal Avalanche",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_TITLE"] = "CRYSTAL AVALANCHE",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_1"] = "Grows crystal spikes across the paths around him, dealing %$heroes.hero_dragon_gem.floor_impact.damage_min[1]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[1]%$ physical damage to each enemy hit.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_2"] = "Grows crystal spikes across the paths around him, dealing %$heroes.hero_dragon_gem.floor_impact.damage_min[2]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[2]%$ physical damage to each enemy hit.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_3"] = "Grows crystal spikes across the paths around him, dealing %$heroes.hero_dragon_gem.floor_impact.damage_min[3]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[3]%$ physical damage to each enemy hit.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_TITLE"] = "PRISMATIC SHARDS",
["HERO_DRAGON_GEM_NAME"] = "Kosmyr",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_1"] = "Crystallizes a group of enemies, stunning them for %$heroes.hero_dragon_gem.stun.duration[1]%$ seconds.",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_2"] = "Crystallizes a group of enemies, stunning them for %$heroes.hero_dragon_gem.stun.duration[2]%$ seconds.",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_3"] = "Crystallizes a group of enemies, stunning them for %$heroes.hero_dragon_gem.stun.duration[3]%$ seconds.",
["HERO_DRAGON_GEM_STUN_TITLE"] = "PARALYZING BREATH",
["HERO_HUNTER_BEASTS_DESCRIPTION_1"] = "Summons 2 bats that attack nearby enemies for %$heroes.hero_hunter.beasts.duration[1]%$ seconds, dealing %$heroes.hero_hunter.beasts.damage_min[1]%$-%$heroes.hero_hunter.beasts.damage_max[1]%$ physical damage. Each bat has a chance of stealing %$heroes.hero_hunter.beasts.gold_to_steal[1]%$ gold from their target.",
["HERO_HUNTER_BEASTS_DESCRIPTION_2"] = "Summons 2 bats that attack nearby enemies for %$heroes.hero_hunter.beasts.duration[2]%$ seconds, dealing %$heroes.hero_hunter.beasts.damage_min[2]%$-%$heroes.hero_hunter.beasts.damage_max[2]%$ physical damage. Each bat has a chance of stealing %$heroes.hero_hunter.beasts.gold_to_steal[2]%$ gold from their target.",
["HERO_HUNTER_BEASTS_DESCRIPTION_3"] = "Summons 2 bats that attack nearby enemies for %$heroes.hero_hunter.beasts.duration[3]%$ seconds, dealing %$heroes.hero_hunter.beasts.damage_min[3]%$-%$heroes.hero_hunter.beasts.damage_max[3]%$ physical damage. Each bat has a chance of stealing %$heroes.hero_hunter.beasts.gold_to_steal[3]%$ gold from their target.",
["HERO_HUNTER_BEASTS_TITLE"] = "DUSK BEASTS",
["HERO_HUNTER_CLASS"] = "Silver Huntress",
["HERO_HUNTER_DESC"] = "Born from the union of a vampire and a renowned hunter, Anya follows her father's footsteps fighting against the denizens of the dark. The relentless hunt for cultists quickly took her to the southern lands and joining the Alliance.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_1"] = "Every 7th melee attack deals %$heroes.hero_hunter.heal_strike.damage_min[1]%$-%$heroes.hero_hunter.heal_strike.damage_max[1]%$ true damage and heals Anya for %$heroes.hero_hunter.heal_strike.heal_factor[1]%$% of her target's maximum health.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_2"] = "Every 7th melee attack deals %$heroes.hero_hunter.heal_strike.damage_min[2]%$-%$heroes.hero_hunter.heal_strike.damage_max[2]%$ true damage and heals Anya for %$heroes.hero_hunter.heal_strike.heal_factor[2]%$% of her target's maximum health.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_3"] = "Every 7th melee attack deals %$heroes.hero_hunter.heal_strike.damage_min[3]%$-%$heroes.hero_hunter.heal_strike.damage_max[3]%$ true damage and heals Anya for %$heroes.hero_hunter.heal_strike.heal_factor[3]%$% of her target's maximum health.",
["HERO_HUNTER_HEAL_STRIKE_TITLE"] = "VAMPIRIC CLAW",
["HERO_HUNTER_NAME"] = "Anya",
["HERO_HUNTER_RICOCHET_DESCRIPTION_1"] = "Anya turns into mist and bounces between %$heroes.hero_hunter.ricochet.s_bounces[1]%$ enemies, dealing %$heroes.hero_hunter.ricochet.damage_min[1]%$-%$heroes.hero_hunter.ricochet.damage_max[1]%$ physical damage to each one.",
["HERO_HUNTER_RICOCHET_DESCRIPTION_2"] = "Anya turns into mist and bounces between %$heroes.hero_hunter.ricochet.s_bounces[2]%$ enemies, dealing %$heroes.hero_hunter.ricochet.damage_min[2]%$-%$heroes.hero_hunter.ricochet.damage_max[2]%$ physical damage to each one.",
["HERO_HUNTER_RICOCHET_DESCRIPTION_3"] = "Anya turns into mist and bounces between %$heroes.hero_hunter.ricochet.s_bounces[3]%$ enemies, dealing %$heroes.hero_hunter.ricochet.damage_min[3]%$-%$heroes.hero_hunter.ricochet.damage_max[3]%$ physical damage to each one.",
["HERO_HUNTER_RICOCHET_TITLE"] = "MISTY STEP",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_1"] = "Shoots all enemies around her, dealing %$heroes.hero_hunter.shoot_around.s_damage_min[1]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[1]%$ true damage to each one.",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_2"] = "Shoots all enemies around her, dealing %$heroes.hero_hunter.shoot_around.s_damage_min[2]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[2]%$ true damage to each one.",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_3"] = "Shoots all enemies around her, dealing %$heroes.hero_hunter.shoot_around.s_damage_min[3]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[3]%$ true damage to each one.",
["HERO_HUNTER_SHOOT_AROUND_TITLE"] = "ARGENT STORM",
["HERO_HUNTER_SPIRIT_DESCRIPTION_1"] = "Summons a projection of Dante that deals %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[2]%$ true damage per second for %$heroes.hero_hunter.ultimate.duration%$ seconds. Revives Anya if her body is nearby.",
["HERO_HUNTER_SPIRIT_DESCRIPTION_2"] = "Summons a projection of Dante that deals %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[3]%$ true damage per second for %$heroes.hero_hunter.ultimate.duration%$ seconds. Revives Anya if her body is nearby.",
["HERO_HUNTER_SPIRIT_DESCRIPTION_3"] = "Summons a projection of Dante that deals %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[4]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[4]%$ true damage per second for %$heroes.hero_hunter.ultimate.duration%$ seconds. Revives Anya if her body is nearby.",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_DESCRIPTION"] = "Summons a projection of Dante that slows and attacks enemies.",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_NAME"] = "Hunter's Aid",
["HERO_HUNTER_SPIRIT_TITLE"] = "HUNTER'S AID",
["HERO_HUNTER_ULTIMATE_ENTITY_NAME"] = "Dante's Projection",
["HERO_LAVA_CLASS"] = "Molten Fury",
["HERO_LAVA_DESC"] = "A fiery and destructive being with bad temper awakened from a deep sleep by Grymbeard's activities. As dialogue is not his forte, Kratoa will bash its way through his enemies' lines until calming down to be able to sleep once again.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_1"] = "Throws a lava ball that deals %$heroes.hero_lava.double_trouble.s_damage[1]%$ explosive damage to enemies and spawns a %$heroes.hero_lava.double_trouble.soldier.hp_max[1]%$ health magmite that fights for %$heroes.hero_lava.double_trouble.soldier.duration%$ seconds.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_2"] = "Throws a lava ball that deals %$heroes.hero_lava.double_trouble.s_damage[2]%$ explosive damage to enemies and spawns a %$heroes.hero_lava.double_trouble.soldier.hp_max[2]%$ health magmite that fights for %$heroes.hero_lava.double_trouble.soldier.duration%$ seconds.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_3"] = "Throws a lava ball that deals %$heroes.hero_lava.double_trouble.s_damage[3]%$ explosive damage to enemies and spawns a %$heroes.hero_lava.double_trouble.soldier.hp_max[3]%$ health magmite that fights for %$heroes.hero_lava.double_trouble.soldier.duration%$ seconds.",
["HERO_LAVA_DOUBLE_TROUBLE_SOLDIER_NAME"] = "Magmite",
["HERO_LAVA_DOUBLE_TROUBLE_TITLE"] = "DOUBLE TROUBLE",
["HERO_LAVA_HOTHEADED_DESCRIPTION_1"] = "When Kratoa revives, it grants a %$heroes.hero_lava.hotheaded.s_damage_factors[1]%$% damage buff to nearby towers for %$heroes.hero_lava.hotheaded.durations[1]%$ seconds.",
["HERO_LAVA_HOTHEADED_DESCRIPTION_2"] = "When Kratoa revives, it grants a %$heroes.hero_lava.hotheaded.s_damage_factors[2]%$% damage buff to nearby towers for %$heroes.hero_lava.hotheaded.durations[2]%$ seconds.",
["HERO_LAVA_HOTHEADED_DESCRIPTION_3"] = "When Kratoa revives, it grants a %$heroes.hero_lava.hotheaded.s_damage_factors[3]%$% damage buff to nearby towers for %$heroes.hero_lava.hotheaded.durations[3]%$ seconds.",
["HERO_LAVA_HOTHEADED_TITLE"] = "HOTHEADED",
["HERO_LAVA_NAME"] = "Kratoa",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_1"] = "Repeatedly bashes an enemy, dealing %$heroes.hero_lava.temper_tantrum.s_damage_min[1]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[1]%$ physical damage and stunning the target for %$heroes.hero_lava.temper_tantrum.duration[1]%$ seconds.",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_2"] = "Repeatedly bashes an enemy, dealing %$heroes.hero_lava.temper_tantrum.s_damage_min[2]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[2]%$ physical damage and stunning the target for %$heroes.hero_lava.temper_tantrum.duration[2]%$ seconds.",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_3"] = "Repeatedly bashes an enemy, dealing %$heroes.hero_lava.temper_tantrum.s_damage_min[3]%$-%$heroes.hero_lava.temper_tantrum.s_damage_max[3]%$ physical damage and stunning the target for %$heroes.hero_lava.temper_tantrum.duration[3]%$ seconds.",
["HERO_LAVA_TEMPER_TANTRUM_TITLE"] = "TEMPER TANTRUM",
["HERO_LAVA_ULTIMATE_DESCRIPTION_1"] = "Throws %$heroes.hero_lava.ultimate.fireball_count[2]%$ bursts of lava onto the path, each dealing %$heroes.hero_lava.ultimate.bullet.s_damage[2]%$ true damage to each enemy hit and burning them for %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ seconds.",
["HERO_LAVA_ULTIMATE_DESCRIPTION_2"] = "Throws %$heroes.hero_lava.ultimate.fireball_count[3]%$ bursts of lava onto the path, each dealing %$heroes.hero_lava.ultimate.bullet.s_damage[3]%$ true damage to each enemy hit and burning them for %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ seconds.",
["HERO_LAVA_ULTIMATE_DESCRIPTION_3"] = "Throws %$heroes.hero_lava.ultimate.fireball_count[4]%$ bursts of lava onto the path, each dealing %$heroes.hero_lava.ultimate.bullet.s_damage[4]%$ true damage to each enemy hit and burning them for %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ seconds.",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "Throws bursts of lava into the path, burning the ground.",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_NAME"] = "Rage Outburst",
["HERO_LAVA_ULTIMATE_TITLE"] = "RAGE OUTBURST",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_1"] = "Sprays lava upon enemies, dealing %$heroes.hero_lava.wild_eruption.s_damage[1]%$ true damage per second and burning enemies for %$heroes.hero_lava.wild_eruption.duration[1]%$ seconds.",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_2"] = "Sprays lava upon enemies, dealing %$heroes.hero_lava.wild_eruption.s_damage[2]%$ true damage per second and burning enemies for %$heroes.hero_lava.wild_eruption.duration[2]%$ seconds.",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_3"] = "Sprays lava upon enemies, dealing %$heroes.hero_lava.wild_eruption.s_damage[3]%$ true damage per second and burning enemies for %$heroes.hero_lava.wild_eruption.duration[3]%$ seconds.",
["HERO_LAVA_WILD_ERUPTION_TITLE"] = "WILD ERUPTION",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_1"] = "Summons %$heroes.hero_lumenir.ultimate.soldier_count[1]%$ warriors of light which briefly stun nearby enemies and deal %$heroes.hero_lumenir.ultimate.damage_min[1]%$-%$heroes.hero_lumenir.ultimate.damage_max[1]%$ true damage.",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_2"] = "Summons %$heroes.hero_lumenir.ultimate.soldier_count[2]%$ warriors of light which briefly stun nearby enemies and deal %$heroes.hero_lumenir.ultimate.damage_min[2]%$-%$heroes.hero_lumenir.ultimate.damage_max[2]%$ true damage.",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_3"] = "Summons %$heroes.hero_lumenir.ultimate.soldier_count[3]%$ warriors of light which briefly stun nearby enemies and deal %$heroes.hero_lumenir.ultimate.damage_min[3]%$-%$heroes.hero_lumenir.ultimate.damage_max[3]%$ true damage.",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "Summons divine warriors that fight enemies.",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_NAME"] = "Call of Triumph",
["HERO_LUMENIR_ARROW_STORM_TITLE"] = "CALL OF TRIUMPH",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_1"] = "Casts a divine sword of light over the strongest enemy nearby, dealing %$heroes.hero_lumenir.celestial_judgement.damage[1]%$ true damage and stunning it for %$heroes.hero_lumenir.celestial_judgement.stun_duration[1]%$ seconds.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_2"] = "Casts a divine sword of light over the strongest enemy nearby, dealing %$heroes.hero_lumenir.celestial_judgement.damage[2]%$ true damage and stunning it for %$heroes.hero_lumenir.celestial_judgement.stun_duration[2]%$ seconds.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_3"] = "Casts a divine sword of light over the strongest enemy nearby, dealing %$heroes.hero_lumenir.celestial_judgement.damage[3]%$ true damage and stunning it for %$heroes.hero_lumenir.celestial_judgement.stun_duration[3]%$ seconds.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_TITLE"] = "CELESTIAL JUDGEMENT",
["HERO_LUMENIR_CLASS"] = "Lightbringer",
["HERO_LUMENIR_DESC"] = "Soaring between the realms, Lumenir stands as the avatar of justice and resolve. She is the fabled Lightbringer, revered by the Linirean paladins, to whom she bestows her blessings, granting them great powers to aid in their fight against evil.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_1"] = "Breathes %$heroes.hero_lumenir.fire_balls.flames_count[1]%$ orbs of divine light that travel through the path damaging enemies. Each orb deals %$heroes.hero_lumenir.fire_balls.flame_damage_min[1]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[1]%$ true damage to each enemy it passes through.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_2"] = "Breathes %$heroes.hero_lumenir.fire_balls.flames_count[2]%$ orbs of divine light that travel through the path damaging enemies. Each orb deals %$heroes.hero_lumenir.fire_balls.flame_damage_min[2]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[2]%$ true damage to each enemy it passes through.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_3"] = "Breathes %$heroes.hero_lumenir.fire_balls.flames_count[3]%$ orbs of divine light that travel through the path damaging enemies. Each orb deals %$heroes.hero_lumenir.fire_balls.flame_damage_min[3]%$-%$heroes.hero_lumenir.fire_balls.flame_damage_max[3]%$ true damage to each enemy it passes through.",
["HERO_LUMENIR_FIRE_BALLS_TITLE"] = "RADIANT WAVE",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_1"] = "Summons a small light dragon that follows the other equipped hero for %$heroes.hero_lumenir.mini_dragon.dragon.duration[1]%$ seconds. The dragon deals %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[1]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[1]%$ physical damage per attack.",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_2"] = "Summons a small light dragon that follows the other equipped hero for %$heroes.hero_lumenir.mini_dragon.dragon.duration[2]%$ seconds. The dragon deals %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[2]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[2]%$ physical damage per attack.",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_3"] = "Summons a small light dragon that follows the other equipped hero for %$heroes.hero_lumenir.mini_dragon.dragon.duration[3]%$ seconds. The dragon deals %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[3]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[3]%$ physical damage per attack.",
["HERO_LUMENIR_MINI_DRAGON_TITLE"] = "LIGHT COMPANION",
["HERO_LUMENIR_NAME"] = "Lumenir",
["HERO_LUMENIR_SHIELD_DESCRIPTION_1"] = "Grants allied units a %$heroes.hero_lumenir.shield.armor[1]%$% armor shield that reflects %$heroes.hero_lumenir.shield.spiked_armor[1]%$% damage back at enemies.",
["HERO_LUMENIR_SHIELD_DESCRIPTION_2"] = "Grants allied units a %$heroes.hero_lumenir.shield.armor[2]%$% armor shield that reflects %$heroes.hero_lumenir.shield.spiked_armor[2]%$% damage back at enemies.",
["HERO_LUMENIR_SHIELD_DESCRIPTION_3"] = "Grants allied units a %$heroes.hero_lumenir.shield.armor[3]%$% armor shield that reflects %$heroes.hero_lumenir.shield.spiked_armor[3]%$% damage back at enemies.",
["HERO_LUMENIR_SHIELD_TITLE"] = "BLESSING OF RETRIBUTION",
["HERO_MECHA_CLASS"] = "Mobile Menace",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_1"] = "Calls a goblin zeppelin that bombards enemies near the target area, dealing %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[2]%$ true area damage per attack.",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_2"] = "Calls a goblin zeppelin that bombards enemies near the target area, dealing %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[3]%$ true area damage per attack.",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_3"] = "Calls a goblin zeppelin that bombards enemies near the target area, dealing %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[4]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[4]%$ true area damage per attack.",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_DESCRIPTION"] = "Summons a zeppelin that bombards enemies in the area.",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_NAME"] = "Death from Above",
["HERO_MECHA_DEATH_FROM_ABOVE_TITLE"] = "DEATH FROM ABOVE",
["HERO_MECHA_DESC"] = "Born from the mind of two mad goblin tinkerers and built on the foundations of stolen dwarven technology, Onagro is the definitive greenskin war machine and a frightening sight for the enemies of the Dark Army.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_1"] = "Summons %$heroes.hero_mecha.goblidrones.units%$ drones that attack enemies for %$heroes.hero_mecha.goblidrones.drone.duration[1]%$ seconds, dealing %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[1]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[1]%$ physical damage per attack.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_2"] = "Summons %$heroes.hero_mecha.goblidrones.units%$ drones that attack enemies for %$heroes.hero_mecha.goblidrones.drone.duration[2]%$ seconds, dealing %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[2]%$ physical damage per attack.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_3"] = "Summons %$heroes.hero_mecha.goblidrones.units%$ drones that attack enemies for %$heroes.hero_mecha.goblidrones.drone.duration[3]%$ seconds, dealing %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[3]%$ physical damage per attack.",
["HERO_MECHA_GOBLIDRONES_TITLE"] = "GOBLIDRONES",
["HERO_MECHA_MINE_DROP_DESCRIPTION_1"] = "While standing still, the mech periodically leaves up to %$heroes.hero_mecha.mine_drop.max_mines[1]%$ explosive mines on the path. Mines explode dealing %$heroes.hero_mecha.mine_drop.damage_min[1]%$-%$heroes.hero_mecha.mine_drop.damage_max[1]%$ explosive damage each.",
["HERO_MECHA_MINE_DROP_DESCRIPTION_2"] = "While standing still, the mech periodically leaves up to %$heroes.hero_mecha.mine_drop.max_mines[2]%$ explosive mines on the path. Mines explode dealing %$heroes.hero_mecha.mine_drop.damage_min[2]%$-%$heroes.hero_mecha.mine_drop.damage_max[2]%$ explosive damage each.",
["HERO_MECHA_MINE_DROP_DESCRIPTION_3"] = "While standing still, the mech periodically leaves up to %$heroes.hero_mecha.mine_drop.max_mines[3]%$ explosive mines on the path. Mines explode dealing %$heroes.hero_mecha.mine_drop.damage_min[3]%$-%$heroes.hero_mecha.mine_drop.damage_max[3]%$ explosive damage each.",
["HERO_MECHA_MINE_DROP_TITLE"] = "MINE DROP",
["HERO_MECHA_NAME"] = "Onagro",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_1"] = "The mech pounds the ground, briefly stunning and dealing %$heroes.hero_mecha.power_slam.s_damage[1]%$ physical damage to all nearby enemies.",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_2"] = "The mech pounds the ground, briefly stunning and dealing %$heroes.hero_mecha.power_slam.s_damage[2]%$ physical damage to all nearby enemies.",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_3"] = "The mech pounds the ground, briefly stunning and dealing %$heroes.hero_mecha.power_slam.s_damage[3]%$ physical damage to all nearby enemies.",
["HERO_MECHA_POWER_SLAM_TITLE"] = "POWER SLAM",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_1"] = "Throws a bomb that spills tar on the path, slowing enemies by %$heroes.hero_mecha.tar_bomb.slow_factor%$% for %$heroes.hero_mecha.tar_bomb.duration[1]%$ seconds.",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_2"] = "Throws a bomb that spills tar on the path, slowing enemies by %$heroes.hero_mecha.tar_bomb.slow_factor%$% for %$heroes.hero_mecha.tar_bomb.duration[2]%$ seconds.",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_3"] = "Throws a bomb that spills tar on the path, slowing enemies by %$heroes.hero_mecha.tar_bomb.slow_factor%$% for %$heroes.hero_mecha.tar_bomb.duration[3]%$ seconds.",
["HERO_MECHA_TAR_BOMB_TITLE"] = "TAR BOMB",
["HERO_MUYRN_CLASS"] = "Forest Guardian",
["HERO_MUYRN_DESC"] = "Despite his childish looks, the trickster Nyru has been protecting the forest for hundreds of years using his connection with the forces of nature. He joined the Alliance in order to put an end to the increasing waves of invaders threatening his home.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_1"] = "Charms all enemies on an area, reducing their attack damage by %$heroes.hero_muyrn.faery_dust.s_damage_factor[1]%$% for %$heroes.hero_muyrn.faery_dust.duration[1]%$ seconds.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_2"] = "Charms all enemies on an area, reducing their attack damage by %$heroes.hero_muyrn.faery_dust.s_damage_factor[2]%$% for %$heroes.hero_muyrn.faery_dust.duration[2]%$ seconds.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_3"] = "Charms all enemies on an area, reducing their attack damage by %$heroes.hero_muyrn.faery_dust.s_damage_factor[3]%$% for %$heroes.hero_muyrn.faery_dust.duration[3]%$ seconds.",
["HERO_MUYRN_FAERY_DUST_TITLE"] = "ENFEEBLEMENT CHARM",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_1"] = "When in combat, Nyru creates a leaf shield around himself. The shield deals %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[1]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[1]%$ magical damage per second and heals Nyru for %$heroes.hero_muyrn.leaf_whirlwind.duration[1]%$ seconds.",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_2"] = "When in combat, Nyru creates a leaf shield around himself. The shield deals %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[2]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[2]%$ magical damage per second and heals Nyru for %$heroes.hero_muyrn.leaf_whirlwind.duration[2]%$ seconds.",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_3"] = "When in combat, Nyru creates a leaf shield around himself. The shield deals %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[3]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[3]%$ magical damage per second and heals Nyru for %$heroes.hero_muyrn.leaf_whirlwind.duration[3]%$ seconds.",
["HERO_MUYRN_LEAF_WHIRLWIND_TITLE"] = "LEAF WHIRLWIND",
["HERO_MUYRN_NAME"] = "Nyru",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_1"] = "Spawns roots over an area for %$heroes.hero_muyrn.ultimate.duration[2]%$ seconds, slowing enemies and dealing %$heroes.hero_muyrn.ultimate.s_damage_min[2]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[2]%$ true damage per second.",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_2"] = "Spawns roots over an area for %$heroes.hero_muyrn.ultimate.duration[3]%$ seconds, slowing enemies and dealing %$heroes.hero_muyrn.ultimate.s_damage_min[3]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[3]%$ true damage per second.",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_3"] = "Spawns roots over an area for %$heroes.hero_muyrn.ultimate.duration[4]%$ seconds, slowing enemies and dealing %$heroes.hero_muyrn.ultimate.s_damage_min[4]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[4]%$ true damage per second.",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_DESCRIPTION"] = "Spawns roots that damage and slow enemies.",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_NAME"] = "Root Defender",
["HERO_MUYRN_ROOT_DEFENDER_TITLE"] = "ROOT DEFENDER",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_1"] = "Summons %$heroes.hero_muyrn.sentinel_wisps.max_summons[1]%$ friendly wisp that follows Nyru for %$heroes.hero_muyrn.sentinel_wisps.wisp.duration[1]%$ seconds. The wisp deals %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[1]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[1]%$ magic damage.",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_2"] = "Summons %$heroes.hero_muyrn.sentinel_wisps.max_summons[2]%$ friendly wisps that follow Nyru for %$heroes.hero_muyrn.sentinel_wisps.wisp.duration[2]%$ seconds. The wisps deal %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[2]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[2]%$ magic damage.",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_3"] = "Summons %$heroes.hero_muyrn.sentinel_wisps.max_summons[3]%$ friendly wisps that follow Nyru for %$heroes.hero_muyrn.sentinel_wisps.wisp.duration[3]%$ seconds. The wisps deal %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[3]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[3]%$ magic damage.",
["HERO_MUYRN_SENTINEL_WISPS_TITLE"] = "SENTINEL WISPS",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_1"] = "Fires a green blast of energy towards an enemy, dealing %$heroes.hero_muyrn.verdant_blast.s_damage[1]%$ magic damage.",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_2"] = "Fires a green blast of energy towards an enemy, dealing %$heroes.hero_muyrn.verdant_blast.s_damage[2]%$ magic damage.",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_3"] = "Fires a green blast of energy towards an enemy, dealing %$heroes.hero_muyrn.verdant_blast.s_damage[3]%$ magic damage.",
["HERO_MUYRN_VERDANT_BLAST_TITLE"] = "VERDANT BLAST",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_1"] = "Brutally strikes an enemy with her sword, dealing %$heroes.hero_raelyn.brutal_slash.s_damage[1]%$ true damage.",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_2"] = "Brutally strikes an enemy with her sword, dealing %$heroes.hero_raelyn.brutal_slash.s_damage[2]%$ true damage.",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_3"] = "Brutally strikes an enemy with her sword, dealing %$heroes.hero_raelyn.brutal_slash.s_damage[3]%$ true damage.",
["HERO_RAELYN_BRUTAL_SLASH_TITLE"] = "BRUTAL SLASH",
["HERO_RAELYN_CLASS"] = "Dark Lieutenant",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_1"] = "Summons a Dark Knight that has %$heroes.hero_raelyn.ultimate.entity.hp_max[2]%$ health and deals %$heroes.hero_raelyn.ultimate.entity.damage_min[2]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[2]%$ true damage.",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_2"] = "The Dark Knight has %$heroes.hero_raelyn.ultimate.entity.hp_max[3]%$ health and deals %$heroes.hero_raelyn.ultimate.entity.damage_min[3]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[3]%$ true damage.",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_3"] = "The Dark Knight has %$heroes.hero_raelyn.ultimate.entity.hp_max[4]%$ health and deals %$heroes.hero_raelyn.ultimate.entity.damage_min[4]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[4]%$ true damage.",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_DESCRIPTION"] = "Summons a Dark Knight to the battlefield.",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_NAME"] = "Command Orders",
["HERO_RAELYN_COMMAND_ORDERS_TITLE"] = "COMMAND ORDERS",
["HERO_RAELYN_DESC"] = "The imposing Raelyn lives to lead the Dark Knights at the vanguard. Her brutality and relentlessness earned her Vez’nan’s recognition and the Linirean’s fear. Always ready for a good fight, she was the first volunteer to join the Dark Wizard’s ranks.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_1"] = "Stuns nearby enemies for %$heroes.hero_raelyn.inspire_fear.stun_duration[1]%$ seconds and reduces their attack damage by %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[1]%$% for %$heroes.hero_raelyn.inspire_fear.damage_duration[1]%$ seconds.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_2"] = "Stuns nearby enemies for %$heroes.hero_raelyn.inspire_fear.stun_duration[2]%$ seconds and reduces their attack damage by %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[2]%$% for %$heroes.hero_raelyn.inspire_fear.damage_duration[2]%$ seconds.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_3"] = "Stuns nearby enemies for %$heroes.hero_raelyn.inspire_fear.stun_duration[3]%$ seconds and reduces their attack damage by %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[3]%$% for %$heroes.hero_raelyn.inspire_fear.damage_duration[3]%$ seconds.",
["HERO_RAELYN_INSPIRE_FEAR_TITLE"] = "INSPIRE FEAR",
["HERO_RAELYN_NAME"] = "Raelyn",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_1"] = "For %$heroes.hero_raelyn.onslaught.duration[1]%$ seconds, Raelyn attacks faster and deals %$heroes.hero_raelyn.onslaught.damage_factor[1]%$% of her attack damage in a small area around the primary target.",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_2"] = "For %$heroes.hero_raelyn.onslaught.duration[2]%$ seconds, Raelyn attacks faster and deals %$heroes.hero_raelyn.onslaught.damage_factor[2]%$% of her attack damage in a small area around the primary target.",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_3"] = "For %$heroes.hero_raelyn.onslaught.duration[3]%$ seconds, Raelyn attacks faster and deals %$heroes.hero_raelyn.onslaught.damage_factor[3]%$% of her attack damage in a small area around the primary target.",
["HERO_RAELYN_ONSLAUGHT_TITLE"] = "ONSLAUGHT",
["HERO_RAELYN_ULTIMATE_ENTITY_NAME"] = "Dark Knight",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_1"] = "When in combat, Raelyn generates a health shield based on how many enemies are near her (%$heroes.hero_raelyn.unbreakable.shield_per_enemy[1]%$% of her life total per each of up to %$heroes.hero_raelyn.unbreakable.max_targets%$ enemies)",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_2"] = "When in combat, Raelyn generates a health shield based on how many enemies are near her (%$heroes.hero_raelyn.unbreakable.shield_per_enemy[2]%$% of her life total per each of up to %$heroes.hero_raelyn.unbreakable.max_targets%$ enemies)",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_3"] = "When in combat, Raelyn generates a health shield based on how many enemies are near her (%$heroes.hero_raelyn.unbreakable.shield_per_enemy[3]%$% of her life total per each of up to %$heroes.hero_raelyn.unbreakable.max_targets%$ enemies)",
["HERO_RAELYN_UNBREAKABLE_TITLE"] = "UNBREAKABLE",
["HERO_ROBOT_CLASS"] = "Siege Golem",
["HERO_ROBOT_DESC"] = "The Dark Army forgemasters outdid themselves by creating a warring automaton which they aptly named Warhead. Bolstered by fiery engines and unbothered by emotions, Warhead lunges into battle disregarding both friend and foe.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_1"] = "Generates a fiery blast that deals %$heroes.hero_robot.explode.damage_min[1]%$-%$heroes.hero_robot.explode.damage_max[1]%$ explosive damage to enemies and burns them for %$heroes.hero_robot.explode.burning_duration%$ seconds. Burning deals %$heroes.hero_robot.explode.s_burning_damage[1]%$ damage per second.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_2"] = "Generates a fiery blast that deals %$heroes.hero_robot.explode.damage_min[2]%$-%$heroes.hero_robot.explode.damage_max[2]%$ explosive damage to enemies and burns them for %$heroes.hero_robot.explode.burning_duration%$ seconds. Burning deals %$heroes.hero_robot.explode.s_burning_damage[2]%$ damage per second.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_3"] = "Generates a fiery blast that deals %$heroes.hero_robot.explode.damage_min[3]%$-%$heroes.hero_robot.explode.damage_max[3]%$ explosive damage to enemies and burns them for %$heroes.hero_robot.explode.burning_duration%$ seconds. Burning deals %$heroes.hero_robot.explode.s_burning_damage[3]%$ damage per second.",
["HERO_ROBOT_EXPLODE_TITLE"] = "IMMOLATION",
["HERO_ROBOT_FIRE_DESCRIPTION_1"] = "Fires a cannon full of fiery embers, dealing %$heroes.hero_robot.fire.damage_min[1]%$-%$heroes.hero_robot.fire.damage_max[1]%$ physical damage and slowing enemies for %$heroes.hero_robot.fire.s_slow_duration[1]%$ seconds.",
["HERO_ROBOT_FIRE_DESCRIPTION_2"] = "Fires a cannon full of fiery embers, dealing %$heroes.hero_robot.fire.damage_min[2]%$-%$heroes.hero_robot.fire.damage_max[2]%$ physical damage and slowing enemies for %$heroes.hero_robot.fire.s_slow_duration[1]%$ seconds.",
["HERO_ROBOT_FIRE_DESCRIPTION_3"] = "Fires a cannon full of fiery embers, dealing %$heroes.hero_robot.fire.damage_min[3]%$-%$heroes.hero_robot.fire.damage_max[3]%$ physical damage and slowing enemies for %$heroes.hero_robot.fire.s_slow_duration[1]%$ seconds.",
["HERO_ROBOT_FIRE_TITLE"] = "SMOKESCREEN",
["HERO_ROBOT_JUMP_DESCRIPTION_1"] = "Jumps over an enemy, stunning it for %$heroes.hero_robot.jump.stun_duration[1]%$ seconds and dealing %$heroes.hero_robot.jump.s_damage[1]%$ physical damage in an area.",
["HERO_ROBOT_JUMP_DESCRIPTION_2"] = "Jumps over an enemy, stunning it for %$heroes.hero_robot.jump.stun_duration[2]%$ seconds and dealing %$heroes.hero_robot.jump.s_damage[2]%$ physical damage in an area.",
["HERO_ROBOT_JUMP_DESCRIPTION_3"] = "Jumps over an enemy, stunning it for %$heroes.hero_robot.jump.stun_duration[3]%$ seconds and dealing %$heroes.hero_robot.jump.s_damage[3]%$ physical damage in an area.",
["HERO_ROBOT_JUMP_TITLE"] = "DEEP IMPACT",
["HERO_ROBOT_NAME"] = "Warhead",
["HERO_ROBOT_TRAIN_DESCRIPTION_1"] = "Summons a war wagon that travels through the path dealing %$heroes.hero_robot.ultimate.s_damage[2]%$ damage to enemies and burning them for %$heroes.hero_robot.ultimate.burning_duration%$ seconds. Burning deals %$heroes.hero_robot.ultimate.s_burning_damage%$ damage per second.",
["HERO_ROBOT_TRAIN_DESCRIPTION_2"] = "Summons a war wagon that travels through the path dealing %$heroes.hero_robot.ultimate.s_damage[3]%$ damage to enemies and burning them for %$heroes.hero_robot.ultimate.burning_duration%$ seconds. Burning deals %$heroes.hero_robot.ultimate.s_burning_damage%$ damage per second.",
["HERO_ROBOT_TRAIN_DESCRIPTION_3"] = "Summons a war wagon that travels through the path dealing %$heroes.hero_robot.ultimate.s_damage[4]%$ damage to enemies and burning them for %$heroes.hero_robot.ultimate.burning_duration%$ seconds. Burning deals %$heroes.hero_robot.ultimate.s_burning_damage%$ damage per second.",
["HERO_ROBOT_TRAIN_MENUBOTTOM_DESCRIPTION"] = "Summons a war wagon that tramples enemies.",
["HERO_ROBOT_TRAIN_MENUBOTTOM_NAME"] = "Motor Head",
["HERO_ROBOT_TRAIN_TITLE"] = "MOTOR HEAD",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_1"] = "Strikes an enemy with less than %$heroes.hero_robot.uppercut.s_life_threshold[1]%$% health, finishing it instantly.",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_2"] = "Strikes an enemy with less than %$heroes.hero_robot.uppercut.s_life_threshold[2]%$% health, finishing it instantly.",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_3"] = "Strikes an enemy with less than %$heroes.hero_robot.uppercut.s_life_threshold[3]%$% health, finishing it instantly.",
["HERO_ROBOT_UPPERCUT_TITLE"] = "IRON UPPERCUT",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "This hero is included in the Colossal Dwarfare Campaign.",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "This hero is included in the Wukong's Journey Campaign.",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "Colossal Dwarfare Campaign",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "Wukong's Journey Campaign",
["HERO_ROOM_EQUIPPED_HEROES"] = "Equipped Heroes",
["HERO_ROOM_GET_DLC"] = "GET IT",
["HERO_ROOM_LABEL_ROSTER_THUMB_NEW"] = "New!",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_1"] = "Summons a magic reflection of Therien that attacks enemies, dealing %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[1]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[1]%$ magic damage.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_2"] = "Summons a magic reflection of Therien that attacks enemies, dealing %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[2]%$ magic damage.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_3"] = "Summons a magic reflection of Therien that attacks enemies, dealing %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[3]%$ magic damage.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_ENTITY_NAME"] = "Astral Reflection",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_TITLE"] = "ASTRAL REFLECTION",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_1"] = "Shields an allied unit preventing up to %$heroes.hero_space_elf.black_aegis.shield_base[1]%$ damage. The shield explodes when depleted or on expiration, dealing %$heroes.hero_space_elf.black_aegis.explosion_damage[1]%$ magic damage in an area.",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_2"] = "Shields an allied unit preventing up to %$heroes.hero_space_elf.black_aegis.shield_base[2]%$ damage. The explosive shield now deals %$heroes.hero_space_elf.black_aegis.explosion_damage[2]%$ magic damage in an area.",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_3"] = "Shields an allied unit preventing up to %$heroes.hero_space_elf.black_aegis.shield_base[3]%$ damage. The explosive shield now deals %$heroes.hero_space_elf.black_aegis.explosion_damage[3]%$ magic damage in an area.",
["HERO_SPACE_ELF_BLACK_AEGIS_TITLE"] = "BLACK AEGIS",
["HERO_SPACE_ELF_CLASS"] = "Voidmancer",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_1"] = "Traps a group of enemies in the void for %$heroes.hero_space_elf.ultimate.duration[2]%$ seconds, dealing %$heroes.hero_space_elf.ultimate.damage[2]%$ damage.",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_2"] = "Traps a group of enemies in the void for %$heroes.hero_space_elf.ultimate.duration[3]%$ seconds, dealing %$heroes.hero_space_elf.ultimate.damage[3]%$ damage.",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_3"] = "Traps a group of enemies in the void for %$heroes.hero_space_elf.ultimate.duration[4]%$ seconds, dealing %$heroes.hero_space_elf.ultimate.damage[4]%$ damage.",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_DESCRIPTION"] = "Traps enemies in an area, damaging them.",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_NAME"] = "Cosmic Prison",
["HERO_SPACE_ELF_COSMIC_PRISON_TITLE"] = "COSMIC PRISON",
["HERO_SPACE_ELF_DESC"] = "Shunned for years by her peers for meddling with unknown and otherworldly forces, the voidmancer Therien now finds herself as one of the greatest assets of the Alliance to understand the Overseer and any forces from beyond this plane.",
["HERO_SPACE_ELF_NAME"] = "Therien",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_1"] = "Distorts the space around all towers for %$heroes.hero_space_elf.spatial_distortion.duration[1]%$ seconds, increasing their range by %$heroes.hero_space_elf.spatial_distortion.s_range_factor[1]%$%.",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_2"] = "Distorts the space around all towers for %$heroes.hero_space_elf.spatial_distortion.duration[2]%$ seconds, increasing their range by %$heroes.hero_space_elf.spatial_distortion.s_range_factor[2]%$%.",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_3"] = "Distorts the space around all towers for %$heroes.hero_space_elf.spatial_distortion.duration[3]%$ seconds, increasing their range by %$heroes.hero_space_elf.spatial_distortion.s_range_factor[3]%$%.",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_TITLE"] = "SPATIAL DISTORTION",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_1"] = "Opens %$heroes.hero_space_elf.void_rift.cracks_amount[1]%$ rift on the path for %$heroes.hero_space_elf.void_rift.duration[1]%$ seconds, dealing %$heroes.hero_space_elf.void_rift.s_damage_min[1]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[1]%$ damage per second to each enemy that stands over it.",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_2"] = "Opens %$heroes.hero_space_elf.void_rift.cracks_amount[2]%$ rifts on the path for %$heroes.hero_space_elf.void_rift.duration[2]%$ seconds, dealing %$heroes.hero_space_elf.void_rift.s_damage_min[2]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[2]%$ damage per second to each enemy that stands over it.",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_3"] = "Opens %$heroes.hero_space_elf.void_rift.cracks_amount[3]%$ rifts on the path for %$heroes.hero_space_elf.void_rift.duration[3]%$ seconds, dealing %$heroes.hero_space_elf.void_rift.s_damage_min[3]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[3]%$ damage per second to each enemy that stands over it.",
["HERO_SPACE_ELF_VOID_RIFT_TITLE"] = "VOID RIFT",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_1"] = "Summons %$heroes.hero_spider.ultimate.spawn_amount[2]%$ spiders that fight for %$heroes.hero_spider.ultimate.spider.duration[2]%$ seconds, stunning enemies on hit.",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_2"] = "Summons %$heroes.hero_spider.ultimate.spawn_amount[3]%$ spiders that fight for %$heroes.hero_spider.ultimate.spider.duration[3]%$ seconds, stunning enemies on hit.",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_3"] = "Summons %$heroes.hero_spider.ultimate.spawn_amount[4]%$ spiders that fight for %$heroes.hero_spider.ultimate.spider.duration[4]%$ seconds, stunning enemies on hit.",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_DESCRIPTION"] = "Summons a pack of stunning spiders.",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_NAME"] = "Hunter´s Call",
["HERO_SPIDER_ARACNID_SPAWNER_TITLE"] = "Hunter´s Call",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_1"] = "Every %$heroes.hero_spider.area_attack.cooldown[1]%$ seconds, Spydyr asserts her presence, stunning nearby enemies for %$heroes.hero_spider.area_attack.s_stun_time[1]%$ seconds.",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_2"] = "Every %$heroes.hero_spider.area_attack.cooldown[2]%$ seconds, Spydyr asserts her presence, stunning nearby enemies for %$heroes.hero_spider.area_attack.s_stun_time[2]%$ seconds.",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_3"] = "Every %$heroes.hero_spider.area_attack.cooldown[3]%$ seconds, Spydyr asserts her presence, stunning nearby enemies for %$heroes.hero_spider.area_attack.s_stun_time[3]%$ seconds.",
["HERO_SPIDER_AREA_ATTACK_TITLE"] = "Overwhelming Presence",
["HERO_SPIDER_DESC"] = "Spydyr is the last living member of a group of Twilight Elves tasked with annihilating the Spider Queen's Cult. Adding shadow magic to her unmatched hunting prowess, she is feared as one of the deadliest assassins across all kingdoms.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_1"] = "Every %$heroes.hero_spider.instakill_melee.cooldown[1]%$ seconds Spydyr can execute a stunned enemy whose health is below %$heroes.hero_spider.instakill_melee.life_threshold[1]%$.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_2"] = "Every %$heroes.hero_spider.instakill_melee.cooldown[2]%$ seconds Spydyr can execute a stunned enemy whose health is below %$heroes.hero_spider.instakill_melee.life_threshold[2]%$.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_3"] = "Every %$heroes.hero_spider.instakill_melee.cooldown[3]%$ seconds Spydyr can execute a stunned enemy whose health is below %$heroes.hero_spider.instakill_melee.life_threshold[3]%$.",
["HERO_SPIDER_INSTAKILL_MELEE_TITLE"] = "Death's Grasp",
["HERO_SPIDER_NAME"] = "Spydyr",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_1"] = "In the blink of an eye, Spydyr teleports to the enemy with the highest health, dealing %$heroes.hero_spider.supreme_hunter.damage_min[1]%$-%$heroes.hero_spider.supreme_hunter.damage_max[1]%$ damage to it.",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_2"] = "In the blink of an eye, Spydyr teleports to the enemy with the highest health, dealing %$heroes.hero_spider.supreme_hunter.damage_min[2]%$-%$heroes.hero_spider.supreme_hunter.damage_max[2]%$ damage to it.",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_3"] = "In the blink of an eye, Spydyr teleports to the enemy with the highest health, dealing %$heroes.hero_spider.supreme_hunter.damage_min[3]%$-%$heroes.hero_spider.supreme_hunter.damage_max[3]%$ damage to it.",
["HERO_SPIDER_SUPREME_HUNTER_TITLE"] = "Shadow Step",
["HERO_SPIDER_TUNNELING_DESCRIPTION_1"] = "Spydyr's tunneling now deals %$heroes.hero_spider.tunneling.damage_min[1]%$-%$heroes.hero_spider.tunneling.damage_max[1]%$ damage upon resurfacing.",
["HERO_SPIDER_TUNNELING_DESCRIPTION_2"] = "Spydyr's tunneling now deals %$heroes.hero_spider.tunneling.damage_min[2]%$-%$heroes.hero_spider.tunneling.damage_max[2]%$ damage upon resurfacing.",
["HERO_SPIDER_TUNNELING_DESCRIPTION_3"] = "Spydyr's tunneling now deals %$heroes.hero_spider.tunneling.damage_min[3]%$-%$heroes.hero_spider.tunneling.damage_max[3]%$ damage upon resurfacing.",
["HERO_SPIDER_TUNNELING_TITLE"] = "Tunneling",
["HERO_VENOM_CLASS"] = "Tainted Slayer",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_1"] = "Fills an area with a gooey substance that slows enemies and, after a moment, turns into piercing spikes that deal %$heroes.hero_venom.ultimate.s_damage[2]%$ true damage.",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_2"] = "Fills an area with a gooey substance that slows enemies and, after a moment, turns into piercing spikes that deal %$heroes.hero_venom.ultimate.s_damage[3]%$ true damage.",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_3"] = "Fills an area with a gooey substance that slows enemies and, after a moment, turns into piercing spikes that deal %$heroes.hero_venom.ultimate.s_damage[4]%$ true damage.",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_DESCRIPTION"] = "Summons a gooey substance on the path that slows and damages enemies.",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_NAME"] = "Creeping Death",
["HERO_VENOM_CREEPING_DEATH_TITLE"] = "CREEPING DEATH",
["HERO_VENOM_DESC"] = "After resisting being turned into an abomination by the Cult, the mercenary Grimson was imprisoned and left to rot. The torturous process granted Grimson shapeshifting powers, which he used to escape from the Cult, vowing to come back for revenge.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_1"] = "Grimson devours an enemy with less than %$heroes.hero_venom.eat_enemy.hp_trigger%$% health, regaining %$heroes.hero_venom.eat_enemy.regen[1]%$% of his own total health in the process.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_2"] = "Grimson devours an enemy with less than %$heroes.hero_venom.eat_enemy.hp_trigger%$% health, regaining %$heroes.hero_venom.eat_enemy.regen[2]%$% of his own total health in the process.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_3"] = "Grimson devours an enemy with less than %$heroes.hero_venom.eat_enemy.hp_trigger%$% health, regaining %$heroes.hero_venom.eat_enemy.regen[3]%$% of his own total health in the process.",
["HERO_VENOM_EAT_ENEMY_TITLE"] = "RENEW FLESH",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_1"] = "Spreads spiky tendrils on the path, dealing %$heroes.hero_venom.floor_spikes.s_damage[1]%$ true damage per spike to nearby enemies.",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_2"] = "Spreads spiky tendrils on the path, dealing %$heroes.hero_venom.floor_spikes.s_damage[2]%$ true damage per spike to nearby enemies.",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_3"] = "Spreads spiky tendrils on the path, dealing %$heroes.hero_venom.floor_spikes.s_damage[3]%$ true damage per spike to nearby enemies.",
["HERO_VENOM_FLOOR_SPIKES_TITLE"] = "DEADLY SPIKES",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_1"] = "When below %$heroes.hero_venom.inner_beast.trigger_hp%$% health, Grimson fully transforms, gaining %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[1]%$% extra damage and healing himself for %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$% of his total life per hit for %$heroes.hero_venom.inner_beast.duration%$ seconds.",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_2"] = "When below %$heroes.hero_venom.inner_beast.trigger_hp%$% health, Grimson fully transforms, gaining %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[2]%$% extra damage and healing himself for %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$% of his total life per hit for %$heroes.hero_venom.inner_beast.duration%$ seconds.",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_3"] = "When below %$heroes.hero_venom.inner_beast.trigger_hp%$% health, Grimson fully transforms, gaining %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[3]%$% extra damage and healing himself for %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$% of his total life per hit for %$heroes.hero_venom.inner_beast.duration%$ seconds.",
["HERO_VENOM_INNER_BEAST_TITLE"] = "INNER BEAST",
["HERO_VENOM_NAME"] = "Grimson",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_1"] = "Strikes a distant enemy, dealing %$heroes.hero_venom.ranged_tentacle.s_damage[1]%$ physical damage with a %$heroes.hero_venom.ranged_tentacle.bleed_chance[1]%$% chance of causing bleeding. Bleeding deals %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$ damage per second for %$heroes.hero_venom.ranged_tentacle.bleed_duration[1]%$ seconds.",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_2"] = "Strikes a distant enemy, dealing %$heroes.hero_venom.ranged_tentacle.s_damage[2]%$ physical damage with a %$heroes.hero_venom.ranged_tentacle.bleed_chance[2]%$% chance of causing bleeding. Bleeding deals %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$ damage per second for %$heroes.hero_venom.ranged_tentacle.bleed_duration[2]%$ seconds.",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_3"] = "Strikes a distant enemy, dealing %$heroes.hero_venom.ranged_tentacle.s_damage[3]%$ physical damage with a %$heroes.hero_venom.ranged_tentacle.bleed_chance[3]%$% chance of causing bleeding. Bleeding deals %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$ damage per second for %$heroes.hero_venom.ranged_tentacle.bleed_duration[3]%$ seconds.",
["HERO_VENOM_RANGED_TENTACLE_TITLE"] = "HEARTSEEKER",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_1"] = "Showers an area with %$heroes.hero_vesper.ultimate.s_spread[2]%$ arrows, each dealing %$heroes.hero_vesper.ultimate.damage[2]%$ physical damage to enemies.",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_2"] = "Showers an area with %$heroes.hero_vesper.ultimate.s_spread[3]%$ arrows, each dealing %$heroes.hero_vesper.ultimate.damage[3]%$ physical damage to enemies.",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_3"] = "Showers an area with %$heroes.hero_vesper.ultimate.s_spread[4]%$ arrows, each dealing %$heroes.hero_vesper.ultimate.damage[4]%$ physical damage to enemies.",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "Showers an area with arrows, dealing damage to enemies.",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_NAME"] = "Arrow Storm",
["HERO_VESPER_ARROW_STORM_TITLE"] = "ARROW STORM",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_1"] = "Shoots an arrow that stuns the enemy for %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[1]%$ seconds, dealing %$heroes.hero_vesper.arrow_to_the_knee.s_damage[1]%$ physical damage.",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_2"] = "Shoots an arrow that stuns the enemy for %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[2]%$ seconds, dealing %$heroes.hero_vesper.arrow_to_the_knee.s_damage[2]%$ physical damage.",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_3"] = "Shoots an arrow that stuns the enemy for %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[3]%$ second, dealing %$heroes.hero_vesper.arrow_to_the_knee.s_damage[3]%$ physical damage.",
["HERO_VESPER_ARROW_TO_THE_KNEE_TITLE"] = "ARROW TO THE KNEE",
["HERO_VESPER_CLASS"] = "Royal Captain",
["HERO_VESPER_DESC"] = "Proficient with both sword and bow, Vesper won his place as commander of the Linirean forces. After Linirea fell and King Denas disappeared, he gathered all the troops he could and started a crusade to bring back the former ruler.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_1"] = "When below %$heroes.hero_vesper.disengage.hp_to_trigger%$% health, Vesper evades the next melee attack by jumping backwards. He then shoots three arrows that deal %$heroes.hero_vesper.disengage.s_damage[1]%$ physical damage each to nearby enemies.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_2"] = "When below %$heroes.hero_vesper.disengage.hp_to_trigger%$% health, Vesper evades the next melee attack by jumping backwards. He then shoots three arrows that deal %$heroes.hero_vesper.disengage.s_damage[2]%$ physical damage each to nearby enemies.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_3"] = "When below %$heroes.hero_vesper.disengage.hp_to_trigger%$% health, Vesper evades the next melee attack by jumping backwards. He then shoots three arrows that deal %$heroes.hero_vesper.disengage.s_damage[3]%$ physical damage each to nearby enemies.",
["HERO_VESPER_DISENGAGE_TITLE"] = "DISENGAGE",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_1"] = "Strikes an enemy three times, dealing %$heroes.hero_vesper.martial_flourish.s_damage[1]%$ physical damage.",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_2"] = "Strikes an enemy three times, dealing %$heroes.hero_vesper.martial_flourish.s_damage[2]%$ physical damage.",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_3"] = "Strikes an enemy three times, dealing %$heroes.hero_vesper.martial_flourish.s_damage[3]%$ physical damage.",
["HERO_VESPER_MARTIAL_FLOURISH_TITLE"] = "MARTIAL FLOURISH",
["HERO_VESPER_NAME"] = "Vesper",
["HERO_VESPER_RICOCHET_DESCRIPTION_1"] = "Shoots an arrow that bounces between %$heroes.hero_vesper.ricochet.s_bounces[1]%$ enemies dealing %$heroes.hero_vesper.ricochet.s_damage[1]%$ physical damage each time.",
["HERO_VESPER_RICOCHET_DESCRIPTION_2"] = "Shoots an arrow that bounces between %$heroes.hero_vesper.ricochet.s_bounces[2]%$ enemies dealing %$heroes.hero_vesper.ricochet.s_damage[2]%$ physical damage each time.",
["HERO_VESPER_RICOCHET_DESCRIPTION_3"] = "Shoots an arrow that bounces between %$heroes.hero_vesper.ricochet.s_bounces[3]%$ enemies dealing %$heroes.hero_vesper.ricochet.s_damage[3]%$ physical damage each time.",
["HERO_VESPER_RICOCHET_TITLE"] = "RICOCHET",
["HERO_WITCH_CLASS"] = "Trickster Witch",
["HERO_WITCH_DESC"] = "While she loves to surprise strangers passing through the Faery Forest with fun and harmless tricks, those who pose a threat to the woods or her fellow gnomes soon find that her playful smile hides a relentless witch to be reckoned with. ",
["HERO_WITCH_DISENGAGE_DESCRIPTION_1"] = "When below %$heroes.hero_witch.disengage.hp_to_trigger%$% health, Stregi teleports backwards leaving a decoy to fight in her place. The decoy has %$heroes.hero_witch.disengage.decoy.hp_max[1]%$ health and explodes when destroyed, stunning enemies for %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[1]%$ second.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_2"] = "When below %$heroes.hero_witch.disengage.hp_to_trigger%$% health, Stregi teleports backwards leaving a decoy to fight in her place. The decoy has %$heroes.hero_witch.disengage.decoy.hp_max[2]%$ health and explodes when destroyed, stunning enemies for %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[2]%$ seconds.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_3"] = "When below %$heroes.hero_witch.disengage.hp_to_trigger%$% health, Stregi teleports backwards leaving a decoy to fight in her place. The decoy has %$heroes.hero_witch.disengage.decoy.hp_max[3]%$ health and explodes when destroyed, stunning enemies for %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[3]%$ seconds.",
["HERO_WITCH_DISENGAGE_TITLE"] = "DAZZLING DECOY",
["HERO_WITCH_NAME"] = "Stregi",
["HERO_WITCH_PATH_AOE_DESCRIPTION_1"] = "Throws a giant potion onto the path, dealing %$heroes.hero_witch.skill_path_aoe.s_damage[1]%$ magical damage in an area and slowing enemies for %$heroes.hero_witch.skill_path_aoe.duration[1]%$ seconds.",
["HERO_WITCH_PATH_AOE_DESCRIPTION_2"] = "Throws a giant potion onto the path, dealing %$heroes.hero_witch.skill_path_aoe.s_damage[2]%$ magical damage in an area and slowing enemies for %$heroes.hero_witch.skill_path_aoe.duration[2]%$ seconds.",
["HERO_WITCH_PATH_AOE_DESCRIPTION_3"] = "Throws a giant potion onto the path, dealing %$heroes.hero_witch.skill_path_aoe.s_damage[3]%$ magical damage in an area and slowing enemies for %$heroes.hero_witch.skill_path_aoe.duration[3]%$ seconds.",
["HERO_WITCH_PATH_AOE_TITLE"] = "SWISH 'N' SQUASH",
["HERO_WITCH_POLYMORPH_DESCRIPTION_1"] = "Turns an enemy into a pumpkling for %$heroes.hero_witch.skill_polymorph.duration[1]%$ seconds. The pumpkling has %$heroes.hero_witch.skill_polymorph.pumpkin.hp[1]%$% of the target's health.",
["HERO_WITCH_POLYMORPH_DESCRIPTION_2"] = "Turns an enemy into a pumpkling for %$heroes.hero_witch.skill_polymorph.duration[2]%$ seconds. The pumpkling has %$heroes.hero_witch.skill_polymorph.pumpkin.hp[2]%$% of the target's health.",
["HERO_WITCH_POLYMORPH_DESCRIPTION_3"] = "Turns an enemy into a pumpkling for %$heroes.hero_witch.skill_polymorph.duration[3]%$ seconds. The pumpkling has %$heroes.hero_witch.skill_polymorph.pumpkin.hp[3]%$% of the target's health.",
["HERO_WITCH_POLYMORPH_TITLE"] = "VEGGIEFY!",
["HERO_WITCH_SOLDIERS_DESCRIPTION_1"] = "Summons %$heroes.hero_witch.skill_soldiers.soldiers_amount[1]%$ cat that fights enemies. The cat has %$heroes.hero_witch.skill_soldiers.soldier.hp_max[1]%$ health and deals %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[1]%$ physical damage.",
["HERO_WITCH_SOLDIERS_DESCRIPTION_2"] = "Summons %$heroes.hero_witch.skill_soldiers.soldiers_amount[2]%$ cats that fight enemies. The cats have %$heroes.hero_witch.skill_soldiers.soldier.hp_max[2]%$ health and deal %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[2]%$ physical damage.",
["HERO_WITCH_SOLDIERS_DESCRIPTION_3"] = "Summons %$heroes.hero_witch.skill_soldiers.soldiers_amount[3]%$ cats that fight enemies. The cats have %$heroes.hero_witch.skill_soldiers.soldier.hp_max[3]%$ health and deal %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[3]%$ physical damage.",
["HERO_WITCH_SOLDIERS_TITLE"] = "NIGHT FURIES",
["HERO_WITCH_ULTIMATE_DESCRIPTION_1"] = "Teleports %$heroes.hero_witch.ultimate.max_targets[2]%$ enemies backwards, leaving them asleep for %$heroes.hero_witch.ultimate.duration[2]%$ seconds.",
["HERO_WITCH_ULTIMATE_DESCRIPTION_2"] = "Teleports %$heroes.hero_witch.ultimate.max_targets[3]%$ enemies backwards, leaving them asleep for %$heroes.hero_witch.ultimate.duration[3]%$ seconds.",
["HERO_WITCH_ULTIMATE_DESCRIPTION_3"] = "Teleports %$heroes.hero_witch.ultimate.max_targets[4]%$ enemies backwards, leaving them asleep for %$heroes.hero_witch.ultimate.duration[4]%$ seconds.",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "Teleports enemies backwards the path, leaving them asleep for a while.",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_NAME"] = "Drowsy Return",
["HERO_WITCH_ULTIMATE_TITLE"] = "DROWSY RETURN",
["HERO_WUKONG_CLASS"] = "The Monkey King",
["HERO_WUKONG_DESC"] = "Born of a celestial stone of Yin and Yang, Sun Wukong was gifted with strength, agility, and immortality. But the demon kings stole the spheres of power from him. Now, the legendary trickster rises to reclaim them before it is too late.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_1"] = "Tumbles and enlarges the Jingu Bang to stomp an enemy, instanly killing it and dealing %$heroes.hero_wukong.giant_staff.area_damage.damage_min[1]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[1]%$ damage in an area around the target.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_2"] = "Tumbles and enlarges the Jingu Bang to stomp an enemy, instanly killing it and dealing %$heroes.hero_wukong.giant_staff.area_damage.damage_min[2]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[2]%$ damage in an area around the target.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_3"] = "Tumbles and enlarges the Jingu Bang to stomp an enemy, instanly killing it and dealing %$heroes.hero_wukong.giant_staff.area_damage.damage_min[3]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[3]%$ damage in an area around the target.",
["HERO_WUKONG_GIANT_STAFF_TITLE"] = "Jingu Bang Technique",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_1"] = "Summons 2 hair clones of Sun Wukong to fight at his side. They deal %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[1]%$ damage and last for %$heroes.hero_wukong.hair_clones.soldier.duration[1]%$ seconds.",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_2"] = "Summons 2 hair clones of Sun Wukong to fight at his side. They deal %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[2]%$ damage and last for %$heroes.hero_wukong.hair_clones.soldier.duration[2]%$ seconds.",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_3"] = "Summons 2 hair clones of Sun Wukong to fight at his side. They deal %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[3]%$ damage and last for %$heroes.hero_wukong.hair_clones.soldier.duration[3]%$ seconds.",
["HERO_WUKONG_HAIR_CLONES_TITLE"] = "Hair Clones",
["HERO_WUKONG_NAME"] = "Sun Wukong",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_1"] = "Launches the Jingu Bang into the air, multiplying it into %$heroes.hero_wukong.pole_ranged.pole_amounts[1]%$ poles that fall onto enemies, each dealing %$heroes.hero_wukong.pole_ranged.damage_min[1]%$ damage and stunning enemies in a small area.",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_2"] = "Launches the Jingu Bang into the air, multiplying it into %$heroes.hero_wukong.pole_ranged.pole_amounts[2]%$ poles that fall onto enemies, each dealing %$heroes.hero_wukong.pole_ranged.damage_min[2]%$ damage and stunning enemies in a small area.",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_3"] = "Launches the Jingu Bang into the air, multiplying it into %$heroes.hero_wukong.pole_ranged.pole_amounts[3]%$ poles that fall onto enemies, each dealing %$heroes.hero_wukong.pole_ranged.damage_min[3]%$ damage and stunning enemies in a small area.",
["HERO_WUKONG_POLE_RANGED_TITLE"] = "Pole Barrage",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_1"] = "The White Dragon bursts into the ground with tremendous force, dealing %$heroes.hero_wukong.ultimate.damage_total[2]%$ true damage and leaving a slowing area.",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_2"] = "The White Dragon bursts into the ground with tremendous force, dealing %$heroes.hero_wukong.ultimate.damage_total[3]%$ true damage and leaving a slowing area.",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_3"] = "The White Dragon bursts into the ground with tremendous force, dealing %$heroes.hero_wukong.ultimate.damage_total[4]%$ true damage and leaving a slowing area.",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "Summons the White Dragon.",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_NAME"] = "The White Dragon",
["HERO_WUKONG_ULTIMATE_TITLE"] = "The White Dragon",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_1"] = "Zhu Bajie, Sun Wukong's loyal companion follows him everywhere. Deals %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[1]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[1]%$ damage and has a small chance to deal a big area damage attack.",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_2"] = "Zhu Bajie, Sun Wukong's loyal companion follows him everywhere. Deals %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[2]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[2]%$ damage and has a small chance to deal a big area damage attack.",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_3"] = "Zhu Bajie, Sun Wukong's loyal companion follows him everywhere. Deals %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[3]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[3]%$ damage and has a small chance to deal a big area damage attack.",
["HERO_WUKONG_ZHU_APPRENTICE_TITLE"] = "Zhu's Apprentice",
["HINT"] = "HINT",
["HOURS_ABBREVIATION"] = "h",
["Hardcore! play at your own risk!"] = "Hardcore! play at your own risk!",
["Help"] = "Help",
["Hero at your command!"] = "Hero at your command!",
["Heroes"] = "Heroes",
["Heroes are elite units that can face strong enemies and support your forces."] = "Heroes are elite units that can face strong enemies and support your forces.",
["Heroes gain experience every time they damage an enemy or use an ability."] = "Heroes gain experience every time they damage an enemy or use an ability.",
["Heroic"] = "Heroic",
["Heroic challenge"] = "Heroic challenge",
["High"] = "High",
["I'm ready. Now bring it on!"] = "I'm ready. Now bring it on!",
["INCOMING NEXT WAVE!"] = "INCOMING NEXT WAVE!",
["INCOMING WAVE"] = "INCOMING WAVE",
["INGAME_BALLOON_BUILD_HERE"] = "Build here!",
["INGAME_BALLOON_GOAL"] = "Don’t let the enemies pass beyond this point",
["INGAME_BALLOON_GOLD"] = "Earn gold by killing enemies",
["INGAME_BALLOON_INCOMING"] = "INCOMING NEXT WAVE!",
["INGAME_BALLOON_NEW_HERO"] = "New hero!",
["INGAME_BALLOON_NEW_POWER"] = "New power!",
["INGAME_BALLOON_NOTIFICATION_TAP_HERE"] = "Tap here!",
["INGAME_BALLOON_SELECT_HERO"] = "Tap to select!",
["INGAME_BALLOON_START_BATTLE"] = "START BATTLE!",
["INGAME_BALLOON_TAP_HERE"] = "Tap on the road",
["INGAME_BALLOON_TAP_TO_CALL"] = "TAP TO CALL IT EARLY",
["INGAME_BALLOON_TAP_TWICE_BUILD"] = "Click to build a tower",
["INGAME_BALLOON_TAP_TWICE_START"] = "TAP TWICE TO START BATTLE",
["INGAME_BALLOON_TAP_TWICE_WAVE"] = "Click to call the wave",
["INGAME_TUTORIAL1_HELP1"] = "Don't let enemies past this point.",
["INGAME_TUTORIAL1_HELP2"] = "Build towers to defend the road.",
["INGAME_TUTORIAL1_HELP3"] = "Earn gold by killing enemies.",
["INGAME_TUTORIAL1_SUBTITLE1"] = "Protect your lands from the enemy attacks.",
["INGAME_TUTORIAL1_SUBTITLE2"] = "Build defensive towers along the road to stop them.",
["INGAME_TUTORIAL1_TITLE"] = "Objective",
["INGAME_TUTORIAL_GOTCHA_1"] = "Got it!",
["INGAME_TUTORIAL_GOTCHA_2"] = "I'm ready, now bring it on!",
["INGAME_TUTORIAL_HINT"] = "HINT",
["INGAME_TUTORIAL_INSTRUCTIONS"] = "INSTRUCTIONS",
["INGAME_TUTORIAL_NEW_TIP"] = "NEW TIP",
["INGAME_TUTORIAL_NEXT"] = "Next!",
["INGAME_TUTORIAL_OK"] = "Ok!",
["INGAME_TUTORIAL_SKIP"] = "Skip this!",
["INGAME_TUTORIAL_TIP_CHALLENGE"] = "WARNING",
["INSTRUCTIONS"] = "INSTRUCTIONS",
["ITEM_CLUSTER_BOMB_BOTTOM_DESC"] = "Like popcorn, but a lot more fun and less tasty.",
["ITEM_CLUSTER_BOMB_BOTTOM_INFO"] = "A bomb that creates new smaller bombs.",
["ITEM_CLUSTER_BOMB_DESC"] = "Throw a bomb that damages enemies in the area and launches other smaller bombs around it.",
["ITEM_CLUSTER_BOMB_NAME"] = "Cluster Bomb",
["ITEM_DEATHS_TOUCH_BOTTOM_DESC"] = "Great for when you want to feel like a god... OF DEATH!",
["ITEM_DEATHS_TOUCH_BOTTOM_INFO"] = "Select. Tap on your target. Kill.",
["ITEM_DEATHS_TOUCH_DESC"] = "Get imbued with the power of Death and tap over any enemy to instantly eliminate them. Does not work on bosses or mini bosses.",
["ITEM_DEATHS_TOUCH_NAME"] = "Death's Touch",
["ITEM_LOOT_BOX_BOTTOM_DESC"] = "A couple of these and you are set for life.",
["ITEM_LOOT_BOX_BOTTOM_INFO"] = "Drop a crate onto the path, damaging enemies and instantly obtaining gold.",
["ITEM_LOOT_BOX_DESC"] = "Drop a crate onto the path, damaging enemies and instantly obtaining 300 gold.",
["ITEM_LOOT_BOX_NAME"] = "Motherlode Box",
["ITEM_MEDICAL_KIT_BOTTOM_DESC"] = "All you need to get patched up, general.",
["ITEM_MEDICAL_KIT_BOTTOM_INFO"] = "Restores up to 3 hearts to the player.",
["ITEM_MEDICAL_KIT_DESC"] = "A special kit that restores up to 3 hearts to the player.",
["ITEM_MEDICAL_KIT_NAME"] = "Medical Kit",
["ITEM_PORTABLE_COIL_BOTTOM_DESC"] = "Zip! Zap! Fried like a rat!",
["ITEM_PORTABLE_COIL_BOTTOM_INFO"] = "Set up a trap that damages and stuns enemies in an area.",
["ITEM_PORTABLE_COIL_DESC"] = "Set up an area trap that damages and stuns enemies that trigger it. Its effects can chain towards nearby enemies.",
["ITEM_PORTABLE_COIL_NAME"] = "Portable Coil",
["ITEM_ROOM_EQUIP"] = "Equip",
["ITEM_ROOM_EQUIPPED"] = "Equipped",
["ITEM_ROOM_EQUIPPED_ITEMS"] = "Equipped items",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_DESC"] = "Ever ran out of time to fight your enemies? Worry no more!",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_INFO"] = "Teleport a group of enemies back down the path.",
["ITEM_SCROLL_OF_SPACESHIFT_DESC"] = "Teleport up to 10 enemies back down the path.",
["ITEM_SCROLL_OF_SPACESHIFT_NAME"] = "Scroll of Spaceshift",
["ITEM_SECOND_BREATH_BOTTOM_DESC"] = "Rise from the grave, without the undead disadvantage.",
["ITEM_SECOND_BREATH_BOTTOM_INFO"] = "Revives fallen heroes, heals those who are injured and resets the cooldown of hero powers.",
["ITEM_SECOND_BREATH_DESC"] = "A divine blessing that revives fallen heroes, heals those who are injured and resets the cooldown of hero powers.",
["ITEM_SECOND_BREATH_NAME"] = "Second Breath",
["ITEM_SUMMON_BLACKBURN_BOTTOM_DESC"] = "The one. The only. The inimitable.",
["ITEM_SUMMON_BLACKBURN_BOTTOM_INFO"] = "Summon the powerful Blackburn to fight by your side.",
["ITEM_SUMMON_BLACKBURN_DESC"] = "Summon the powerful warrior revenant to vanquish your enemies.",
["ITEM_SUMMON_BLACKBURN_NAME"] = "Helm of Blackburn",
["ITEM_VEZNAN_WRATH_BOTTOM_DESC"] = "Let them have a taste of the Dark Wizard's unlimited power!",
["ITEM_VEZNAN_WRATH_BOTTOM_INFO"] = "Decimates every enemy on the battlefield.",
["ITEM_VEZNAN_WRATH_DESC"] = "Vez'nan casts a powerful spell that decimates every enemy on the battlefield.",
["ITEM_VEZNAN_WRATH_NAME"] = "Vez'nan's Wrath",
["ITEM_WINTER_AGE_BOTTOM_DESC"] = "Also useful if you just REALLY dislike summer.",
["ITEM_WINTER_AGE_BOTTOM_INFO"] = "Freeze all enemies on the screen.",
["ITEM_WINTER_AGE_DESC"] = "A powerful spell that creates bone-chilling winds to freeze all enemies for various seconds.",
["ITEM_WINTER_AGE_NAME"] = "Winter Age",
["Impossible"] = "Impossible",
["Iron"] = "Iron",
["Iron Challenge"] = "Iron Challenge",
["Iron challenge"] = "Iron challenge",
["JOYSTICK_CONFIG_AXIS_DEAD_ZONE"] = "Stick dead zone",
["JOYSTICK_CONFIG_AXIS_DEAD_ZONE_XBOX"] = "Stick dead zone",
["JOYSTICK_CONFIG_FIRST_REPEAT_DELAY"] = "First repeat delay",
["JOYSTICK_CONFIG_POINTER_ACCEL"] = "Pointer accel.",
["JOYSTICK_CONFIG_POINTER_MAX_ACCEL"] = "Pointer max. accel.",
["JOYSTICK_CONFIG_POINTER_SENS"] = "Pointer sens.",
["JOYSTICK_CONFIG_POINTER_SPEED"] = "Pointer speed",
["JOYSTICK_CONFIG_REPEAT_DELAY"] = "Repeat delay",
["JOYSTICK_CONFIG_SWAP_ABXY"] = "Swap A/B and X/Y",
["JOYSTICK_HELP_INGAME_A"] = "Select",
["JOYSTICK_HELP_INGAME_AXIS_LEFT"] = "Move",
["JOYSTICK_HELP_INGAME_AXIS_LEFT_BUTTON"] = "Toggle pointer",
["JOYSTICK_HELP_INGAME_B"] = "Cancel/Back",
["JOYSTICK_HELP_INGAME_BACK"] = "Show info card",
["JOYSTICK_HELP_INGAME_DPAD_DOWN"] = "Move reinforcements",
["JOYSTICK_HELP_INGAME_DPAD_LEFT"] = "Call Reinforcements",
["JOYSTICK_HELP_INGAME_DPAD_RIGHT"] = "Hero power 2",
["JOYSTICK_HELP_INGAME_DPAD_UP"] = "Hero power 1",
["JOYSTICK_HELP_INGAME_ESCAPE"] = "Cancel/Back",
["JOYSTICK_HELP_INGAME_LB"] = "Primary hero",
["JOYSTICK_HELP_INGAME_MOVE_HEROES"] = "Move heroes",
["JOYSTICK_HELP_INGAME_MOVE_REINFORCEMENTS"] = "Move reinforcements",
["JOYSTICK_HELP_INGAME_NX_A"] = "Select",
["JOYSTICK_HELP_INGAME_NX_AXIS_LEFT"] = "Move",
["JOYSTICK_HELP_INGAME_NX_AXIS_LEFT_BUTTON"] = "Toggle pointer",
["JOYSTICK_HELP_INGAME_NX_B"] = "Cancel/Back",
["JOYSTICK_HELP_INGAME_NX_L"] = "Primary hero",
["JOYSTICK_HELP_INGAME_NX_MINUS"] = "Show info card",
["JOYSTICK_HELP_INGAME_NX_PLUS"] = "Pause/Resume",
["JOYSTICK_HELP_INGAME_NX_R"] = "Secondary hero",
["JOYSTICK_HELP_INGAME_NX_X"] = "Send wave",
["JOYSTICK_HELP_INGAME_NX_Y"] = "Wave info",
["JOYSTICK_HELP_INGAME_POWERS"] = "Powers",
["JOYSTICK_HELP_INGAME_RB"] = "Secondary hero",
["JOYSTICK_HELP_INGAME_START"] = "Pause/Resume",
["JOYSTICK_HELP_INGAME_X"] = "Send wave",
["JOYSTICK_HELP_INGAME_Y"] = "Wave info",
["JOYSTICK_HELP_MAP_A"] = "Select",
["JOYSTICK_HELP_MAP_AXIS_LEFT"] = "Move",
["JOYSTICK_HELP_MAP_B"] = "Cancel/Back",
["JOYSTICK_HELP_MAP_BACK"] = "Show/Hide options",
["JOYSTICK_HELP_MAP_LB"] = "Prev. level/page",
["JOYSTICK_HELP_MAP_NX_A"] = "Select",
["JOYSTICK_HELP_MAP_NX_AXIS_LEFT"] = "Move",
["JOYSTICK_HELP_MAP_NX_B"] = "Cancel/Back",
["JOYSTICK_HELP_MAP_NX_L"] = "Prev. level/page",
["JOYSTICK_HELP_MAP_NX_MINUS"] = "Show/Hide options",
["JOYSTICK_HELP_MAP_NX_PLUS"] = "Show/Hide options",
["JOYSTICK_HELP_MAP_NX_R"] = "Next level/page",
["JOYSTICK_HELP_MAP_RB"] = "Next level/page",
["JOYSTICK_HELP_MAP_START"] = "Show/Hide options",
["JOYSTICK_HELP_SLOTS_A"] = "Select",
["JOYSTICK_HELP_SLOTS_AXIS_LEFT"] = "Move",
["JOYSTICK_HELP_SLOTS_B"] = "Cancel/Back",
["JOYSTICK_HELP_SLOTS_BACK"] = "Show/Hide options",
["JOYSTICK_HELP_SLOTS_NX_A"] = "Select",
["JOYSTICK_HELP_SLOTS_NX_AXIS_LEFT"] = "Move",
["JOYSTICK_HELP_SLOTS_NX_B"] = "Cancel/Back",
["JOYSTICK_HELP_SLOTS_NX_MINUS"] = "Show/Hide options",
["JOYSTICK_HELP_SLOTS_NX_PLUS"] = "Show/Hide options",
["JOYSTICK_HELP_SLOTS_START"] = "Show/Hide options",
["KEYBOARD_KEY_ESCAPE"] = "ESCAPE",
["KEYBOARD_KEY_PAGE_DOWN"] = "PAGE DOWN",
["KEYBOARD_KEY_PAGE_UP"] = "PAGE UP",
["KEYBOARD_KEY_RETURN"] = "RETURN",
["KEYBOARD_KEY_SPACE"] = "SPACE",
["LEVEL_10_HEROIC"] = "Heroic Description 10",
["LEVEL_10_HISTORY"] = "It turns out that the Cult is using the mined crystals to build an ominous looking artifact right out of the canyon. It buzzes with strange energy and the air surrounding the place feels heavy. We have to make sure it is destroyed before advancing.",
["LEVEL_10_IRON"] = "Iron Description 10",
["LEVEL_10_IRON_UNLOCK"] = "To be defined",
["LEVEL_10_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_10_TITLE"] = "10. Temple Courtyard",
["LEVEL_11_HEROIC"] = "Heroic Description 11",
["LEVEL_11_HISTORY"] = "We finally made it out of the canyons but there is still a long way to go. We now stand in front of a giant portal embedded with crystals as Seeress Mydrias finishes her rituals. What will come from beyond we do not know, but we still stand ready. Brace yourselves!",
["LEVEL_11_IRON"] = "Iron Description 11",
["LEVEL_11_IRON_UNLOCK"] = "To be defined",
["LEVEL_11_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_11_TITLE"] = "11. Canyon Plateau",
["LEVEL_12_HEROIC"] = "Heroic Description 12",
["LEVEL_12_HISTORY"] = "With Denas back on our side, we crossed the portal into the unknown. This strange world looks like a twisted reflection of Linirea, but one swallowed by blight. Mind your step, something worse than the Cult lurks in the dark.",
["LEVEL_12_IRON"] = "Iron Description 12",
["LEVEL_12_IRON_UNLOCK"] = "To be defined",
["LEVEL_12_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_12_TITLE"] = "12. Blighted Farmlands",
["LEVEL_13_HEROIC"] = "Heroic Description 13",
["LEVEL_13_HISTORY"] = "The familiar sight of the Stormcloud Temple looms on the horizon. The path is clear enough, follow the stench and corruption as they grow and we will find the source of it all. We just have to survive the twisted horrors that seem to emerge from the earth itself.",
["LEVEL_13_IRON"] = "Iron Description 13",
["LEVEL_13_IRON_UNLOCK"] = "To be defined",
["LEVEL_13_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_13_TITLE"] = "13. Desecrated Temple",
["LEVEL_14_HEROIC"] = "Heroic Description 14",
["LEVEL_14_HISTORY"] = "These damned creatures seem to spawn out of nowhere! The troops are restless, everything we touch looks alive and ready to attack us, as if the land itself was fighting against us with all its might. Seeress Mydrias and her minions must be near.",
["LEVEL_14_IRON"] = "Iron Description 14",
["LEVEL_14_IRON_UNLOCK"] = "To be defined",
["LEVEL_14_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_14_TITLE"] = "14. Corruption Valley",
["LEVEL_15_HEROIC"] = "Heroic Description 15",
["LEVEL_15_HISTORY"] = "We emerged from the valley victorious and now the only thing standing between us and the Overseer is Mydrias herself. We saw what she was capable of back in the canyons, but here, under the sight and might of her master, she has the upper hand. Not that the odds ever stopped us before. Look alive!",
["LEVEL_15_IRON"] = "Iron Description 15",
["LEVEL_15_IRON_UNLOCK"] = "To be defined",
["LEVEL_15_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_15_TITLE"] = "15. The Eyesore Tower",
["LEVEL_16_HEROIC"] = "Heroic Description 16",
["LEVEL_16_HISTORY"] = "Mydrias is no more and the Overseer is the greater enemy that remains. This is our very last chance to put an end to the Cult and the invasion. What happens next does not matter if we do not stand together one last time. Onwards!",
["LEVEL_16_IRON"] = "Iron Description 16",
["LEVEL_16_IRON_UNLOCK"] = "To be defined",
["LEVEL_16_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_16_TITLE"] = "16. Hunger's Peak",
["LEVEL_17_HISTORY"] = "The surroundings of the once whimsical Faery Forest are now looking unfriendly and ghastly. Word is that hordes of fallen elven warriors and spectral beings now roam these lands, attacking travelers and corrupting the forest itself with their presence. General, we must inspect further.",
["LEVEL_17_TITLE"] = "17. Misty Ruins",
["LEVEL_18_HISTORY"] = "A message came to us from Deepleaf Outpost, where some elves are barely resisting the advance of the revenant horde. We must hurry to aid them and their captain, Eridan, before it's too late. Once the Outpost is properly secured, we can move forward to get to the root of this invasion.",
["LEVEL_18_TITLE"] = "18. Deepleaf Outpost",
["LEVEL_19_HISTORY"] = "A weary Eridan pointed us towards the Temple of the Fallen from where the horde is being raised to overrun the continent, commanded by a mage that goes by the name of Navira, the Soulbender. He must be stopped at all costs!",
["LEVEL_19_TITLE"] = "19. Temple of the Fallen",
["LEVEL_1_HEROIC"] = "Heroic Description 1",
["LEVEL_1_HISTORY"] = "We have been scouring the southern forests for months unsuccessfully as King Denas is nowhere to be found. In the meantime we befriended the Arboreans, spirits of nature and met their warring neighbors, the Wildbeasts, who keep attacking us on sight.\nLet's get this fight done so we can keep looking for the King.",
["LEVEL_1_IRON"] = "Iron Description 1",
["LEVEL_1_IRON_UNLOCK"] = "Royal Archers\nPaladin Covenant",
["LEVEL_1_MODES_UPGRADES"] = "lvl 1 max",
["LEVEL_1_TITLE"] = "1. Sea of Trees",
["LEVEL_20_HISTORY"] = "We've received an urgent wisp from the Arboreans at the edge of the forest, desperately calling for aid. They're under attack by the relentless Croks. They won't be able to hold much longer. Remember to be cautious, General. The Croks have many tricks up their scales.",
["LEVEL_20_TITLE"] = "20. Arborean Hamlet",
["LEVEL_21_HISTORY"] = "After ensuring the town's safety, the Arboreans revealed that, just before the attack, they felt their ancient seal faltering. Armed with a lead on the Croks' sudden invasion, we plunged into the heart of the swamp. We stumbled upon an old Arborean stone circle; it looks like a lair... a lair of something huge.",
["LEVEL_21_TITLE"] = "21. The Sunken Ruins",
["LEVEL_22_HISTORY"] = "Arriving at the ancient temple, our worst fears were confirmed. The seal that had long protected our world from Abominor—the Devourer of Realms—was nearly unwoven, held together only by some desperate Arboreans shamans' binding magic. General, stop Abominor or the kingdoms will be consumed by its insatiable maw.",
["LEVEL_22_TITLE"] = "22. Starving Hollow",
["LEVEL_23_HISTORY"] = "Scouts have reported unnatural landslides on the neighbouring mountains. Further investigation revealed these are caused by some dwarves we cannot recognize. They are putting together a giant automaton on the southern face of the mountain. You should take a look into it, General.",
["LEVEL_23_TITLE"] = "23. Darksteel Gates",
["LEVEL_24_HISTORY"] = "Dwarves were always known as inventors but this self-labeled Darksteel clan takes its devotion to metal too far, putting even Bolgur's folk to shame as they use their forge to \"better\" themselves in rapid fashion. Who is behind such insanity? We need to find out!",
["LEVEL_24_TITLE"] = "24. Frantic Assembly",
["LEVEL_25_HISTORY"] = "As we feared, the entire inside of the mountain has been converted into a forge capable of creating this automaton. How many dwarves are here? They resist our advance and yet they keep smithing and welding. And stranger still, they all look identical? Something is amiss.",
["LEVEL_25_TITLE"] = "25. Colossal Core",
["LEVEL_26_HISTORY"] = "Our road in and out of the mountain took us to a chamber filled with vats... and they weren't empty. No wonder they have strength in numbers, but share looks and skilled craftsmanship - they are all the same dwarf, Grymbeard! He has been creating clones of himself through wicked science. General, we must stop this!",
["LEVEL_26_TITLE"] = "26. Replication Chamber",
["LEVEL_27_HISTORY"] = "We managed to disrupt most of the Darksteel operation in the mountain but it will all be in vain if Grymbeard is still at large. He is surely working on the finishing touches of the automaton's head. General, take the troops to the peaks and let's hope this time we deal with the right dwarf.",
["LEVEL_27_TITLE"] = "27. Dominion Dome",
["LEVEL_28_HISTORY"] = "Following the clues left by our scouts, we’ve uncovered a trail leading to the remnants of those accursed cultists. It seems they’ve found a new goddess to worship, a vile, web-spinning abomination… Cultists AND spiders? Nothing good can come out of this combination.",
["LEVEL_28_TITLE"] = "28. Defiled Temple",
["LEVEL_29_HISTORY"] = "The deeper we go, the clearer it becomes that this terror has been growing underneath for a long time waiting for the right moment to attack. Judging by the thickening web around us and the eerie darkness breathing down our necks, I'd wager we're nearing the very heart of the lair.",
["LEVEL_29_TITLE"] = "29. Breeding Chamber",
["LEVEL_2_HEROIC"] = "Heroic Description 2",
["LEVEL_2_HISTORY"] = "Be alert, word reached us by wisp! The Heart of the Forest is under attack! We must go back and help the Arboreans. Some Dark Army forces will join us on the battlefield, so keep an eye open. We might be in the same boat for now, but that could change at any moment.",
["LEVEL_2_IRON"] = "Iron Description 2",
["LEVEL_2_IRON_UNLOCK"] = "Arcane Wizard\nTricannon",
["LEVEL_2_MODES_UPGRADES"] = "lvl 2 max",
["LEVEL_2_TITLE"] = "2. The Guardian Gate",
["LEVEL_30_HISTORY"] = "At last, we arrived at the lair of their so-called goddess, a decrepit, long-abandoned temple, crumbling under the weight of its own forgotten past. A fitting throne for a forsaken deity. This time we'll make sure to leave none alive and exterminate these pests once and for all.",
["LEVEL_30_TITLE"] = "30. The Forgotten Throne",
["LEVEL_31_HISTORY"] = "After all the fighting and struggle, peace has finally returned to the kingdoms. Now, playing board games while waiting for an old friend is the only task at hand. \n\nAnd yet, even with everything seeming so calm, I wonder how long this peace will last...",
["LEVEL_31_TITLE"] = "31. Celestial Monkey Forest",
["LEVEL_32_HISTORY"] = "Our pursuit has led us deep into the heart of the volcano, where a long-forgotten temple once paid tribute to the flames.\n\nBut the Great Fire Dragon, once a neutral guardian of these fiery depths, now stirs with unnatural rage. All signs point to Red Boy’s influence corrupting his will.",
["LEVEL_32_TITLE"] = "32. Fire Dragon Cave",
["LEVEL_33_HISTORY"] = "After an exhausting clash with Red Boy, we press onward to Tempest Island. The moment we set foot here, lightning-filled clouds and fierce gusts began howling in strange, twisting patterns. Still we have no choice but to push forward, as the island holds the only entrance into the Princess’ palace.\nBrace yourselves... a storm is coming.",
["LEVEL_33_TITLE"] = "33. Tempest Island",
["LEVEL_34_HISTORY"] = "We can thank the Princess and her Iron Fan for the hardships we’ve endured. After crossing the bridge and weathering the fiercest of storms, we now stand at the eye of it all.\n\nThis place stands still, deceptively calm and beautiful. We can't let our guard down. Not even demonic royalty will stand in our way.",
["LEVEL_34_TITLE"] = "34. The Eye of the Storm",
["LEVEL_35_HISTORY"] = "This is it. The Bull Demon King stands tall at his opulent, impenetrable fortress. We've gathered the remainder of our troops to charge head-on against his walls, a task for which we'll need more wit than brute force. We must strike before he fully unleashes the power of the orbs.\nBy all that you hold dear on this good land... I bid you stand, Alliance!",
["LEVEL_35_TITLE"] = "35. The Demon King Stronghold",
["LEVEL_3_HEROIC"] = "Heroic Description 2",
["LEVEL_3_HISTORY"] = "We made it back to the Heart in the nick of time, but the Wildbeasts are already passing through. Look alive and fortify your positions! Protect the Heart at all costs or the forest and the Arboreans will surely perish.",
["LEVEL_3_IRON"] = "Iron Description 2",
["LEVEL_3_IRON_UNLOCK"] = "Royal Archers\nPaladin Covenant",
["LEVEL_3_MODES_UPGRADES"] = "lvl 3 max",
["LEVEL_3_TITLE"] = "3. The Heart of the Forest",
["LEVEL_4_HEROIC"] = "Heroic Description 3",
["LEVEL_4_HISTORY"] = "Now that the Heart of the Forest is safe we must regroup and press the advantage. It is time to take the fight to Wildbeast territory. Take the troops to the treetops of the forest and look for their encampment from above.",
["LEVEL_4_IRON"] = "Iron Description 3",
["LEVEL_4_IRON_UNLOCK"] = "Tricannon\nArborean Emissary",
["LEVEL_4_MODES_UPGRADES"] = "lvl 4 max",
["LEVEL_4_TITLE"] = "4. Emerald Treetops",
["LEVEL_5_HEROIC"] = "Heroic Description 4",
["LEVEL_5_HISTORY"] = "Thanks to your efforts taking the high ground, we located the Wildbeast encampment in some ancient ruins beyond the limits of the forest. Lead the forces towards their territory and be wary of their tactics. We might have won another battle, but this is far from done.",
["LEVEL_5_IRON"] = "Iron Description 4",
["LEVEL_5_IRON_UNLOCK"] = "Arcane Wizard\nPaladin Covenant",
["LEVEL_5_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_5_TITLE"] = "5. Ravaged Outskirts",
["LEVEL_6_HEROIC"] = "Heroic Description 6",
["LEVEL_6_HISTORY"] = "We might have the upper hand against the Wildbeasts, but we still have to face their leader, Goregrind. The self proclaimed Wildbeast King is a mighty foe, so don't let yourself be fooled by his antics or you will meet your end under his tusks.",
["LEVEL_6_IRON"] = "Iron Description 6",
["LEVEL_6_IRON_UNLOCK"] = "Royal Archers\nDemon Pit",
["LEVEL_6_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_6_TITLE"] = "6. The Wildbeast Den",
["LEVEL_7_HEROIC"] = "Heroic Description 7",
["LEVEL_7_HISTORY"] = "Following the trail of the cultists who assisted the Wildbeasts in razing part of the forest, we arrive at a desolate place where we suspect the Cult is carrying out their strange plans. We need to be cautious, as we don't know exactly what we're up against... but they seem to have a few tricks under their hoods.",
["LEVEL_7_IRON"] = "Iron Description 7",
["LEVEL_7_IRON_UNLOCK"] = "No royal archers",
["LEVEL_7_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_7_TITLE"] = "7. Bleak Valley",
["LEVEL_8_HEROIC"] = "Heroic Description 7",
["LEVEL_8_HISTORY"] = "As we made our way into cultist territory, we arrived in a set of huge caverns full of crystals that resonate with strange magic. The Cult is mining these crystals, surely to use them as a power source. To what ends, we do not know, but disrupting their activities is a good way to cause chaos in their ranks.",
["LEVEL_8_IRON"] = "Iron Description 7",
["LEVEL_8_IRON_UNLOCK"] = "Tricannon\nPaladin Covenant",
["LEVEL_8_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_8_TITLE"] = "8. Carmine Mines",
["LEVEL_9_HEROIC"] = "Heroic Description 9",
["LEVEL_9_HISTORY"] = "These tunnels' twists and turns are maddening but we know we are on the right path as cultist activity keeps increasing. As we progress further we face new kinds of horrors, which begs the question of how deep the corruption runs within the Cult's ranks.",
["LEVEL_9_IRON"] = "Iron Description 9",
["LEVEL_9_IRON_UNLOCK"] = "Demon Pit\nArcane Wizard",
["LEVEL_9_MODES_UPGRADES"] = "lvl 5 max",
["LEVEL_9_TITLE"] = "9. Wicked Crossing",
["LEVEL_DEFEAT_TITLE"] = "DEFEAT!",
["LEVEL_MODE_CAMPAIGN"] = "Campaign",
["LEVEL_MODE_HEROIC"] = "Heroic Challenge",
["LEVEL_MODE_HEROIC_DESCRIPTION"] = "Test your tactical skills against an elite enemy force in this challenge meant for only the most heroic defenders!",
["LEVEL_MODE_IRON"] = "Iron Challenge",
["LEVEL_MODE_IRON_DESCRIPTION"] = "A test for the ultimate defender, the iron challenge will take your tactical skills to the limit. ",
["LEVEL_MODE_LOCKED_DESCRIPTION"] = "Complete this stage with 3 stars to unlock this mode.",
["LEVEL_SELECT_AVAILABLE_TOWERS"] = "Available Towers",
["LEVEL_SELECT_CHALLENGE_ONE_ELITE_WAVE"] = "1 elite wave",
["LEVEL_SELECT_CHALLENGE_ONE_LIFE"] = "1 life total",
["LEVEL_SELECT_CHALLENGE_RULES"] = "Challenge Rules",
["LEVEL_SELECT_CHALLENGE_SIX_ELITE_WAVE"] = "6 elite waves",
["LEVEL_SELECT_DIFFICULTY_CASUAL"] = "Casual",
["LEVEL_SELECT_DIFFICULTY_IMPOSSIBLE"] = "Impossible",
["LEVEL_SELECT_DIFFICULTY_NORMAL"] = "Normal",
["LEVEL_SELECT_DIFFICULTY_VETERAN"] = "Veteran",
["LEVEL_SELECT_GET_DLC"] = "GET IT",
["LEVEL_SELECT_MODE_LOCKED1"] = "Mode Locked",
["LEVEL_SELECT_MODE_LOCKED2"] = "Unlock this mode by completing this stage.",
["LEVEL_SELECT_TO_BATTLE"] = "TO\nBATTLE",
["LV22_BOSS_BEFORE_FIGHT_EAT_01"] = "Tasty! Ha Ha Ha",
["LV22_BOSS_BEFORE_FIGHT_EAT_02"] = "I hate plants",
["LV22_BOSS_BEFORE_FIGHT_EAT_03"] = "You are what you eat",
["LV22_BOSS_BEFORE_FIGHT_EAT_04"] = "This bite was refreshing",
["LV22_BOSS_BEFORE_FIGHT_EAT_05"] = "Tired already?",
["LV22_BOSS_BEFORE_FIGHT_EAT_06"] = "I´ll never be hungry again",
["LV22_BOSS_BEFORE_FIGHT_EAT_07"] = "A great tower it was, hahaha",
["LV22_BOSS_BEFORE_FIGHT_EAT_08"] = "It tastes like freedom",
["LV22_BOSS_INTRO_01"] = "Bringing snacks for my first meal.",
["LV22_BOSS_INTRO_02"] = "They look... crunchy",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_01"] = "You will taste only creepers",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_02"] = "Greens are friends, not food",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_03"] = "And you will eat nothing more!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_04"] = "Return to your prison, monster!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_05"] = "You shall not eat!!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_06"] = "I will protect the Green!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_07"] = "You will not laugh at the end",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_08"] = "He is getting stronger! Help!!",
["LV22_MAGE_INTRO_01"] = "Shut yer mouth!",
["LV22_MAGE_INTRO_02"] = "Hurry! I can't stop him much longer!",
["Level"] = "Level",
["Localization Manager"] = "Localization Manager",
["Long"] = "Long",
["Low"] = "Low",
["MAGES’ GUILD"] = "MAGES' GUILD",
["MAGIC RESISTANT ENEMIES!"] = "MAGIC RESISTANT ENEMIES!",
["MAP_BALLON_BUY_UPGRADES_DESCRIPTION"] = "Use the earned stars to improve your towers and powers!",
["MAP_BALLON_BUY_UPGRADES_TITLE"] = "BUY UPGRADES!",
["MAP_BALLON_HERO_LEVELUP_DESCRIPTION"] = "Use the earned hero points to train your hero!",
["MAP_BALLON_HERO_LEVELUP_TITLE"] = "HERO LEVEL UP!",
["MAP_BALLON_HERO_UNLOCKED"] = "HERO UNLOCKED!",
["MAP_BALLON_START_HERE"] = "START HERE!",
["MAP_BUTTON_ACHIEVEMENTS"] = "ACHIEVEMENTS",
["MAP_BUTTON_HERO_ROOM"] = "HEROES",
["MAP_BUTTON_ITEMS"] = "ITEMS",
["MAP_BUTTON_SHOP"] = "SHOP",
["MAP_BUTTON_TOWER_ROOM"] = "TOWERS",
["MAP_BUTTON_UPGRADES"] = "UPGRADES",
["MAP_HEROROOM_HELP1"] = "Select and train abilities!",
["MAP_HEROROOM_HELP2"] = "Tap to select",
["MAP_HEROROOM_HELP3"] = "Improve hero power!",
["MAP_HERO_ROOM_GET_IT_NOW"] = "GET IT NOW!",
["MAP_HERO_ROOM_SELECT"] = "EQUIP",
["MAP_HERO_ROOM_SELECTED"] = "EQUIPPED",
["MAP_HERO_ROOM_TRAIN"] = "TRAIN",
["MAP_HERO_ROOM_UNLOCK"] = "UNLOCKS AT STAGE %s",
["MAP_HERO_ROOM_UNLOCK_10"] = "UNLOCKS AT STAGE 10",
["MAP_HERO_ROOM_UNLOCK_14"] = "UNLOCKS AT STAGE 14",
["MAP_HERO_ROOM_UNLOCK_15"] = "UNLOCKS AT STAGE 15",
["MAP_HERO_ROOM_UNLOCK_4"] = "UNLOCKS AT STAGE 4",
["MAP_HERO_ROOM_UNLOCK_7"] = "UNLOCKS AT STAGE 7",
["MAP_HERO_ROOM_UNLOCK_9"] = "UNLOCKS AT STAGE 9",
["MAP_HERO_ROOM_UNLOCK_AFTER_CAMPAIGN"] = "Unlocks upon finishing the game",
["MAP_INAPPS_BUBBLE_INFO_1"] = "Gather gems by playing the game.",
["MAP_INAPPS_BUBBLE_INFO_2"] = "Use gems to buy special items!",
["MAP_INAPPS_BUBBLE_MORE_GEMS"] = "You need more gems!",
["MAP_INAPPS_BUBBLE_SUCCESSFUL"] = "Purchase\nSuccessful!",
["MAP_INAPP_GEMS_GEM_SHOP_TITLE"] = "GEM SHOP",
["MAP_INAPP_GEM_PACK_1"] = "HANDFUL OF GEMS",
["MAP_INAPP_GEM_PACK_2"] = "POUCH OF GEMS",
["MAP_INAPP_GEM_PACK_3"] = "BARREL OF GEMS",
["MAP_INAPP_GEM_PACK_4"] = "CHEST OF GEMS",
["MAP_INAPP_GEM_PACK_5"] = "WAGON OF GEMS",
["MAP_INAPP_GEM_PACK_6"] = "MOUNTAIN OF GEMS",
["MAP_INAPP_GEM_PACK_BAG"] = "Bag Of Gems",
["MAP_INAPP_GEM_PACK_BARREL"] = "Barrel Of Gems",
["MAP_INAPP_GEM_PACK_CHEST"] = "Chest Of Gems",
["MAP_INAPP_GEM_PACK_FREE"] = "Free Gems",
["MAP_INAPP_GEM_PACK_HANDFUL"] = "Handful Of Gems",
["MAP_INAPP_GEM_PACK_VAULT"] = "Vault Of Gems",
["MAP_INAPP_GEM_PACK_WAGON"] = "Wagon Of Gems",
["MAP_INAPP_MORE_GEMS"] = "MORE GEMS",
["MAP_INAPP_TEXT_1"] = "Handful Of Gems",
["MAP_INAPP_TEXT_2"] = "Bag Of Gems",
["MAP_INAPP_TEXT_3"] = "Chest Of Gems",
["MAP_INAPP_TEXT_4"] = "Free Gems",
["MAP_INAPP_TEXT_GEMS"] = "Gems",
["MAP_NEW_GAMEMODE_UNLOCKED_DESCRIPTION"] = "Face endless enemies and compete for the best score!",
["MAP_NEW_GAMEMODE_UNLOCKED_TITLE"] = "NEW CHALLENGE!",
["MAP_NEW_HERO_ALERT"] = "NEW\nHERO!",
["MAP_NEW_TOWER_ALERT"] = "NEW\nTOWER!",
["MAP_TOWER_ROOM_SELECT"] = "EQUIP",
["MAP_TOWER_ROOM_SELECTED"] = "EQUIPPED",
["MENU_HUD_WAVES"] = "%i/%i",
["MINUTES_ABBREVIATION"] = "m",
["MORE_GAMES"] = "More games",
["MUSIC"] = "MUSIC",
["Magic resistant enemies take less damage from mages."] = "Magic resistant enemies take less damage from mages.",
["Medium"] = "Medium",
["Music"] = "Music",
["NEW POWER!"] = "NEW POWER!",
["NEW SPECIAL POWER!"] = "NEW SPECIAL POWER!",
["NEW TOWER UNLOCKED"] = "NEW TOWER UNLOCKED",
["NEW TOWER UPGRADES"] = "NEW TOWER UPGRADES",
["NEW TOWERS UNLOCKED"] = "NEW TOWERS UNLOCKED",
["NEWS"] = "NEWS",
["NEW_ENEMY_ALERT_ICON"] = "NEW ENEMY",
["NO HEROES"] = "NO HEROES",
["NOTIFICATION_NEW_ENEMY_TITLE"] = "NEW ENEMY",
["NOTIFICATION_NEW_SPECIAL_TITLE"] = "NEW SPECIAL POWER!",
["NOTIFICATION_NEW_TOWERS_SUB_DESCRIPTION"] = "You can now upgrade your towers up to level %d.",
["NOTIFICATION_NEW_TOWERS_SUB_TITLE"] = "LEVEL %d TOWERS AVAILABLE",
["NOTIFICATION_NEW_TOWERS_TITLE"] = "NEW TOWER UPGRADES",
["NOTIFICATION_NEW_TOWER_TITLE"] = "NEW TOWER UNLOCKED",
["NOTIFICATION_armored_enemies_desc_body_1"] = "Some enemies wear armor of different strengths, which protects them against non-magical attacks.",
["NOTIFICATION_armored_enemies_desc_body_2"] = "Resists damage from",
["NOTIFICATION_armored_enemies_desc_body_3"] = "Armored enemies take less damage from Marksman, Barrack and Artillery towers.",
["NOTIFICATION_armored_enemies_desc_title"] = "Armored Enemies!",
["NOTIFICATION_armored_enemies_enemy_name"] = "Tusked Brawler",
["NOTIFICATION_bottom_info_desc_body"] = "You can review enemy information anytime by clicking on a unit and its portrait.",
["NOTIFICATION_bottom_info_desc_title"] = "ENEMY INFORMATION",
["NOTIFICATION_bottom_info_tap_portrait_desc"] = "Click here to reopen.",
["NOTIFICATION_button_ok"] = "Ok",
["NOTIFICATION_glare_desc_body"] = "The Overseer stares into the battlefield, empowering nearby enemies with its corrupted Glare.",
["NOTIFICATION_glare_desc_bullets"] = "- Heals enemies standing in the area\n- Triggers enemies' unique abilities",
["NOTIFICATION_glare_desc_title"] = "Glare of the Overseer",
["NOTIFICATION_hero_desc"] = "Shows level, health and experience.",
["NOTIFICATION_hero_desc_baloon_1"] = "Select by clicking on the portrait or hero unit. Hotkey: space bar",
["NOTIFICATION_hero_desc_baloon_2"] = "Click on the path to move the hero.",
["NOTIFICATION_hero_desc_body_1"] = "Heroes are elite units that can face strong enemies and support your forces.",
["NOTIFICATION_hero_desc_body_2"] = "Heroes gain experience every time they attack an enemy or use an ability.",
["NOTIFICATION_hero_desc_title"] = "Hero at your command!",
["NOTIFICATION_magic_resistant_enemies_desc_body_1"] = "Some enemies have different levels of magic resistance, which protects them against magical attacks.",
["NOTIFICATION_magic_resistant_enemies_desc_body_2"] = "Resists damage from",
["NOTIFICATION_magic_resistant_enemies_desc_body_3"] = "Magic-resistant enemies take less damage from Mage towers.",
["NOTIFICATION_magic_resistant_enemies_desc_title"] = "Magic Resistant Enemies!",
["NOTIFICATION_magic_resistant_enemies_enemy_name"] = "Turtle Shaman",
["NOTIFICATION_rally_point_desc_body_1"] = "You can adjust your barracks' rally point to make units defend a different area.",
["NOTIFICATION_rally_point_desc_body_2"] = "select the rally point control",
["NOTIFICATION_rally_point_desc_body_3"] = "select where you want to move your soldiers",
["NOTIFICATION_rally_point_desc_subtitle"] = "Rally Range",
["NOTIFICATION_rally_point_desc_title"] = "Command your troops!",
["NOTIFICATION_special_desc_body"] = "You can summon additional troops to aid you on the battlefield.",
["NOTIFICATION_special_desc_bullets"] = "Reinforcements are great to stall enemies.",
["NOTIFICATION_special_desc_title"] = "Call Reinforcements",
["NOTIFICATION_title_enemy"] = "Enemy Info",
["NOTIFICATION_title_glare"] = "NEW TIP!",
["NOTIFICATION_title_hint"] = "HERO UNLOCKED",
["NOTIFICATION_title_new_tip"] = "NEW TIP",
["NOTIFICATION_title_special"] = "SPECIAL UNLOCKED",
["Next!"] = "Next!",
["No"] = "No",
["None"] = "None",
["Nope"] = "Nope",
["Normal"] = "Normal",
["OFFER_GET_IT_NOW"] = "GET IT NOW",
["OFFER_GET_THEM_NOW"] = "GET THEM NOW",
["OFFER_OFF"] = "OFF",
["OFFER_REGULAR"] = "REGULAR PRICE",
["OK!"] = "OK!",
["ONE_TIME_OFFER"] = "ONE TIME OFFER!",
["OPTIONS"] = "OPTIONS",
["OPTIONS_PAGE_CONTROLS"] = "CONTROLS",
["OPTIONS_PAGE_HELP"] = "HELP",
["OPTIONS_PAGE_SHORTCUTS"] = "KEYBOARD HELP",
["OPTIONS_PAGE_VIDEO"] = "VIDEO",
["Objective"] = "Objective",
["Options"] = "Options",
["Over 50 stars are recommended to face this stage."] = "Over 50 stars are recommended to face this stage.",
["POPUP_CLEAR_PROGRESS_CONFIRM"] = "ARE YOU SURE YOU WANT TO CLEAR YOUR PROGRESS?",
["POPUP_LABEL_MAIN_MENU"] = "Main Menu",
["POPUP_SETTINGS_LANGUAGE"] = "Language",
["POPUP_SETTINGS_MUSIC"] = "MUSIC",
["POPUP_SETTINGS_SFX"] = "SFX",
["POPUP_label_error_msg"] = "Ups! Something went wrong.",
["POPUP_label_error_msg2"] = "Ups! Something went wrong.",
["POPUP_label_purchasing"] = "PROCESSING YOUR REQUEST",
["POPUP_label_title_options"] = "Options",
["POPUP_label_version"] = "Version 0.0.9",
["POWER_SUMMON_DESCRIPTION"] = "You can summon troops to help you in the battlefield.",
["POWER_SUMMON_LARGE_DESCRIPTION"] = "You can summon troops to help you on the battlefield.\n\nReinforcements are free and you can call them every 15 seconds.",
["POWER_SUMMON_NAME"] = "Call Reinforcements",
["PRICE_FREE"] = "Free",
["PRIVACY_POLICY_ASK_AGE"] = "When were you born?",
["PRIVACY_POLICY_BUTTON_LINK"] = "Privacy Policy",
["PRIVACY_POLICY_CONSENT_SHORT"] = "Before playing our game, please confirm you (and your parent, if you are a child or adolescent) have read and acknowledged our privacy policy.",
["PRIVACY_POLICY_LINK"] = "Privacy Policy",
["PRIVACY_POLICY_WELCOME"] = "Welcome!",
["PROCESSING YOUR REQUEST"] = "PROCESSING YOUR REQUEST",
["Produced by %s"] = "Produced by %s",
["QUIT"] = "Quit",
["Quit"] = "Quit",
["RESTORE_PURCHASES"] = "Restore purchases",
["Reset"] = "Reset",
["Restart"] = "Restart",
["Resume"] = "Resume",
["SECONDS_ABBREVIATION"] = "s",
["SETTINGS_DISPLAY"] = "Display",
["SETTINGS_FRAMES_PER_SECOND"] = "FPS",
["SETTINGS_FULLSCREEN"] = "Fullscreen",
["SETTINGS_FULLSCREEN_BORDERLESS"] = "Borderless",
["SETTINGS_IMAGE_QUALITY"] = "Image quality",
["SETTINGS_LANGUAGE"] = "Language",
["SETTINGS_LARGE_MOUSE_POINTER"] = "Large mouse pointer",
["SETTINGS_LOW_IMAGE_QUALITY_LINK"] = "Low image quality? Click here for help.",
["SETTINGS_RETINA_DISPLAY"] = "Retina display (Mac)",
["SETTINGS_SCREEN_RESOLUTION"] = "Screen resolution",
["SETTINGS_SUPPORT"] = "Support",
["SETTINGS_VSYNC"] = "Vsync",
["SFX"] = "SFX",
["SHOP_DESKTOP_GET_DLC_BUTTON"] = "GET IT",
["SHOP_DESKTOP_TITLE"] = "SHOP",
["SHOP_ROOM_BEST_VALUE_TITLE"] = "BEST\nVALUE",
["SHOP_ROOM_DLC_1_DESCRIPTION"] = "EMBARK ON THIS NEW EPIC ADVENTURE",
["SHOP_ROOM_DLC_1_TITLE"] = "COLOSSAL DWARFARE CAMPAIGN",
["SHOP_ROOM_DLC_1_TOOLTIP_DESCRIPTION"] = "5 New Stages\nNew Tower\nNew Hero\nOver 10 New Enemies\n2 Mini Boss Fights\nAn Epic Boss Fight\nAnd more...",
["SHOP_ROOM_DLC_1_TOOLTIP_TITLE"] = "COLOSSAL DWARFARE CAMPAIGN",
["SHOP_ROOM_DLC_2_DESCRIPTION"] = "EMBARK ON THIS NEW EPIC ADVENTURE",
["SHOP_ROOM_DLC_2_TITLE"] = "WUKONG'S JOURNEY CAMPAIGN",
["SHOP_ROOM_MOST_POPULAR_TITLE"] = "MOST\nPOPULAR",
["SLOT_CLOUD_DOWNLOADING"] = "Downloading...",
["SLOT_CLOUD_DOWNLOAD_FAILED"] = "Failed to download saved game from iCould, try again later.",
["SLOT_CLOUD_DOWNLOAD_SUCCESSFUL"] = "Download succsessful.",
["SLOT_CLOUD_UPLOADING"] = "Uploading...",
["SLOT_CLOUD_UPLOAD_FAILED"] = "Failed to upload saved game to iCloud, try again later.",
["SLOT_CLOUD_UPLOAD_ICLOUD_NOT_CONFIGURED"] = "iCloud not configured in your device.",
["SLOT_CLOUD_UPLOAD_SUCCESSFUL"] = "Upload successful.",
["SLOT_DELETE_SLOT"] = "Delete Slot?",
["SLOT_NAME"] = "Slot",
["SLOT_NEW_GAME"] = "NEW GAME",
["SOLDIER_ARBOREAN_BARRACK_NAME"] = "Arborean soldier",
["SOLDIER_ARBOREAN_SENTINELS_1_NAME"] = "Baluu",
["SOLDIER_ARBOREAN_SENTINELS_2_NAME"] = "Vylla",
["SOLDIER_ARBOREAN_SENTINELS_3_NAME"] = "Ykkon",
["SOLDIER_ARBOREAN_SENTINELS_4_NAME"] = "Haavi",
["SOLDIER_ARBOREAN_SENTINELS_5_NAME"] = "Plook",
["SOLDIER_ARBOREAN_SENTINELS_6_NAME"] = "Guldd",
["SOLDIER_ARBOREAN_SENTINELS_7_NAME"] = "Teena",
["SOLDIER_ARBOREAN_SENTINELS_8_NAME"] = "Uuzky",
["SOLDIER_ARBOREAN_SENTINELS_9_NAME"] = "Deluu",
["SOLDIER_DRAGON_BONE_ULTIMATE_DOG_NAME"] = "Bone Drake",
["SOLDIER_EARTH_HOLDER_NAME"] = "Stone Warrior",
["SOLDIER_GHOST_TOWER_NAME"] = "Wraith",
["SOLDIER_HERO_BUILDER_WORKER_1_NAME"] = "Hemmar",
["SOLDIER_HERO_BUILDER_WORKER_2_NAME"] = "O'Tool",
["SOLDIER_HERO_BUILDER_WORKER_3_NAME"] = "Crews",
["SOLDIER_HERO_BUILDER_WORKER_4_NAME"] = "Birck",
["SOLDIER_HERO_BUILDER_WORKER_5_NAME"] = "Lauck",
["SOLDIER_HERO_BUILDER_WORKER_6_NAME"] = "O'Nail",
["SOLDIER_HERO_BUILDER_WORKER_7_NAME"] = "Hovels",
["SOLDIER_HERO_BUILDER_WORKER_8_NAME"] = "Woody",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL1_NAME"] = "Arborean Guardian",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL2_NAME"] = "Arborean Guardian",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL3_NAME"] = "Arborean Guardian",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL1_NAME"] = "Arborean Paragon",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL2_NAME"] = "Arborean Paragon",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL3_NAME"] = "Arborean Paragon",
["SOLDIER_HERO_SPIDER_ULTIMATE_NAME"] = "Spiderling",
["SOLDIER_HERO_WITCH_CAT_1_NAME"] = "Conan",
["SOLDIER_HERO_WITCH_CAT_2_NAME"] = "Alfajor",
["SOLDIER_HERO_WITCH_CAT_3_NAME"] = "Babieca",
["SOLDIER_HERO_WITCH_CAT_4_NAME"] = "Peluche",
["SOLDIER_HERO_WITCH_CAT_5_NAME"] = "Pipa",
["SOLDIER_HERO_WITCH_CAT_6_NAME"] = "Watson",
["SOLDIER_HERO_WITCH_CAT_7_NAME"] = "Chimi",
["SOLDIER_HERO_WITCH_CAT_8_NAME"] = "Pantufla",
["SOLDIER_HERO_WITCH_DECOY_NAME"] = "Ragdoll",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_1_NAME"] = "San Wikung",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_2_NAME"] = "Son Wokeng",
["SOLDIER_ITEM_SUMMON_BLACKBURN_NAME"] = "Lord Blackburn",
["SOLDIER_PALADINS_10_NAME"] = "Sir Joacim",
["SOLDIER_PALADINS_11_NAME"] = "Sir Andre",
["SOLDIER_PALADINS_12_NAME"] = "Sir Sammet",
["SOLDIER_PALADINS_13_NAME"] = "Sir Udo",
["SOLDIER_PALADINS_14_NAME"] = "Sir Eric",
["SOLDIER_PALADINS_15_NAME"] = "Sir Bruce",
["SOLDIER_PALADINS_16_NAME"] = "Sir Rob",
["SOLDIER_PALADINS_17_NAME"] = "Sir Biff",
["SOLDIER_PALADINS_18_NAME"] = "Sir Bowes",
["SOLDIER_PALADINS_1_NAME"] = "Sir Kai",
["SOLDIER_PALADINS_2_NAME"] = "Sir Hansi",
["SOLDIER_PALADINS_3_NAME"] = "Sir Luca",
["SOLDIER_PALADINS_4_NAME"] = "Sir Timo",
["SOLDIER_PALADINS_5_NAME"] = "Sir Ralf",
["SOLDIER_PALADINS_6_NAME"] = "Sir Tobias",
["SOLDIER_PALADINS_7_NAME"] = "Sir Deris",
["SOLDIER_PALADINS_8_NAME"] = "Sir Kiske",
["SOLDIER_PALADINS_9_NAME"] = "Sir Pesch",
["SOLDIER_PRIESTS_BARRACK_1_NAME"] = "Willy",
["SOLDIER_PRIESTS_BARRACK_2_NAME"] = "Henry",
["SOLDIER_PRIESTS_BARRACK_3_NAME"] = "Geoffrey",
["SOLDIER_PRIESTS_BARRACK_4_NAME"] = "Nicholas",
["SOLDIER_PRIESTS_BARRACK_5_NAME"] = "Ed",
["SOLDIER_PRIESTS_BARRACK_6_NAME"] = "Hob",
["SOLDIER_PRIESTS_BARRACK_7_NAME"] = "Odo",
["SOLDIER_PRIESTS_BARRACK_8_NAME"] = "Cedric",
["SOLDIER_PRIESTS_BARRACK_9_NAME"] = "Hal",
["SOLDIER_RANDOM_10_NAME"] = "Alvus",
["SOLDIER_RANDOM_11_NAME"] = "Borin",
["SOLDIER_RANDOM_12_NAME"] = "Hadrian",
["SOLDIER_RANDOM_13_NAME"] = "Thomas",
["SOLDIER_RANDOM_14_NAME"] = "Henry",
["SOLDIER_RANDOM_15_NAME"] = "Bryce",
["SOLDIER_RANDOM_16_NAME"] = "Rulf",
["SOLDIER_RANDOM_17_NAME"] = "Allister",
["SOLDIER_RANDOM_18_NAME"] = "Altair",
["SOLDIER_RANDOM_19_NAME"] = "Simon",
["SOLDIER_RANDOM_1_NAME"] = "Douglas",
["SOLDIER_RANDOM_20_NAME"] = "Egbert",
["SOLDIER_RANDOM_21_NAME"] = "Eldon",
["SOLDIER_RANDOM_22_NAME"] = "Garrett",
["SOLDIER_RANDOM_23_NAME"] = "Godwin",
["SOLDIER_RANDOM_24_NAME"] = "Gordon",
["SOLDIER_RANDOM_25_NAME"] = "Jerald",
["SOLDIER_RANDOM_26_NAME"] = "Kelvin",
["SOLDIER_RANDOM_27_NAME"] = "Lando",
["SOLDIER_RANDOM_28_NAME"] = "Maddox",
["SOLDIER_RANDOM_29_NAME"] = "Peyton",
["SOLDIER_RANDOM_2_NAME"] = "Dan McKill",
["SOLDIER_RANDOM_30_NAME"] = "Ramsey",
["SOLDIER_RANDOM_31_NAME"] = "Raymond",
["SOLDIER_RANDOM_32_NAME"] = "Robert",
["SOLDIER_RANDOM_33_NAME"] = "Sawyer",
["SOLDIER_RANDOM_34_NAME"] = "Silas",
["SOLDIER_RANDOM_35_NAME"] = "Stuart",
["SOLDIER_RANDOM_36_NAME"] = "Tanner",
["SOLDIER_RANDOM_37_NAME"] = "Usher",
["SOLDIER_RANDOM_38_NAME"] = "Wallace",
["SOLDIER_RANDOM_39_NAME"] = "Wesley",
["SOLDIER_RANDOM_3_NAME"] = "James Lee",
["SOLDIER_RANDOM_40_NAME"] = "Willard",
["SOLDIER_RANDOM_4_NAME"] = "Jar Johson",
["SOLDIER_RANDOM_5_NAME"] = "Phil",
["SOLDIER_RANDOM_6_NAME"] = "Robin",
["SOLDIER_RANDOM_7_NAME"] = "William",
["SOLDIER_RANDOM_8_NAME"] = "Martin",
["SOLDIER_RANDOM_9_NAME"] = "Arthur",
["SOLDIER_REINFORCEMENTS_F_1_NAME"] = "Ataina",
["SOLDIER_REINFORCEMENTS_F_2_NAME"] = "Maucil",
["SOLDIER_REINFORCEMENTS_F_3_NAME"] = "Gulica",
["SOLDIER_REINFORCEMENTS_F_4_NAME"] = "Rogas",
["SOLDIER_REINFORCEMENTS_M_10_NAME"] = "Podgie",
["SOLDIER_REINFORCEMENTS_M_1_NAME"] = "Gabini",
["SOLDIER_REINFORCEMENTS_M_2_NAME"] = "O'Bell",
["SOLDIER_REINFORCEMENTS_M_3_NAME"] = "Kent",
["SOLDIER_REINFORCEMENTS_M_4_NAME"] = "Jendars",
["SOLDIER_REINFORCEMENTS_M_5_NAME"] = "Jarlosc",
["SOLDIER_REINFORCEMENTS_M_6_NAME"] = "Astong",
["SOLDIER_REINFORCEMENTS_M_7_NAME"] = "Buigell",
["SOLDIER_REINFORCEMENTS_M_8_NAME"] = "Clane",
["SOLDIER_REINFORCEMENTS_M_9_NAME"] = "Magus",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_1_NAME"] = "Dench",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_2_NAME"] = "Smith",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_3_NAME"] = "Andrews",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_4_NAME"] = "Thompson",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_5_NAME"] = "Taylor",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_1_NAME"] = "McCartney",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_2_NAME"] = "McKellen",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_3_NAME"] = "Hopkins",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_4_NAME"] = "Caine",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_5_NAME"] = "Kingsley",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_10_NAME"] = "Viper",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_1_NAME"] = "Fang",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_2_NAME"] = "Blade",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_3_NAME"] = "Claw",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_4_NAME"] = "Talon",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_5_NAME"] = "Edgee",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_6_NAME"] = "Shiv",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_7_NAME"] = "Scythe",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_8_NAME"] = "Dagger",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_9_NAME"] = "Sting",
["SOLDIER_REINFORCEMENTS_SPECIAL_DARK_ARMY_1_NAME"] = "Shadow Crowcaller",
["SOLDIER_REINFORCEMENTS_SPECIAL_LINIREA_1_NAME"] = "Paragon Knight",
["SOLDIER_STAGE_10_YMCA_BIKER_NAME"] = "Glenn",
["SOLDIER_STAGE_10_YMCA_CONSTRUCTOR_NAME"] = "David",
["SOLDIER_STAGE_10_YMCA_INDIO_NAME"] = "Felipe",
["SOLDIER_STAGE_10_YMCA_POLICIA_NAME"] = "Victor",
["SOLDIER_STAGE_15_DENAS_NAME"] = "King Denas",
["SOLDIER_TOWER_DARK_ELF_1_NAME"] = "Filraen",
["SOLDIER_TOWER_DARK_ELF_2_NAME"] = "Faeryl",
["SOLDIER_TOWER_DARK_ELF_3_NAME"] = "Gurina",
["SOLDIER_TOWER_DARK_ELF_4_NAME"] = "Jhalass",
["SOLDIER_TOWER_DARK_ELF_5_NAME"] = "Solenzar",
["SOLDIER_TOWER_DARK_ELF_6_NAME"] = "Tebryn",
["SOLDIER_TOWER_DARK_ELF_7_NAME"] = "Vierna",
["SOLDIER_TOWER_DARK_ELF_8_NAME"] = "Zyn",
["SOLDIER_TOWER_DARK_ELF_9_NAME"] = "Elerra",
["SOLDIER_TOWER_DWARF_10_NAME"] = "Babbi",
["SOLDIER_TOWER_DWARF_1_NAME"] = "Pippi",
["SOLDIER_TOWER_DWARF_2_NAME"] = "Ginni",
["SOLDIER_TOWER_DWARF_3_NAME"] = "Merri",
["SOLDIER_TOWER_DWARF_4_NAME"] = "Lorri",
["SOLDIER_TOWER_DWARF_5_NAME"] = "Talli",
["SOLDIER_TOWER_DWARF_6_NAME"] = "Danni",
["SOLDIER_TOWER_DWARF_7_NAME"] = "Getti",
["SOLDIER_TOWER_DWARF_8_NAME"] = "Daffi",
["SOLDIER_TOWER_DWARF_9_NAME"] = "Bibbi",
["SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "Elandil",
["SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "Puck",
["SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "Thas",
["SOLDIER_TOWER_ELVEN_BARRACK_4_NAME"] = "Kastore",
["SOLDIER_TOWER_ELVEN_BARRACK_5_NAME"] = "Elric",
["SOLDIER_TOWER_ELVEN_BARRACK_6_NAME"] = "Elaith",
["SOLDIER_TOWER_NECROMANCER_SKELETON_GOLEM_NAME"] = "Bone Golem",
["SOLDIER_TOWER_NECROMANCER_SKELETON_NAME"] = "Skeleton",
["SOLDIER_TOWER_PANDAS_FEMALE_1_NAME"] = "Yan",
["SOLDIER_TOWER_PANDAS_FEMALE_2_NAME"] = "Qingzhao",
["SOLDIER_TOWER_PANDAS_FEMALE_3_NAME"] = "Hui",
["SOLDIER_TOWER_PANDAS_FEMALE_4_NAME"] = "Ailing",
["SOLDIER_TOWER_PANDAS_MALE_1_NAME"] = "Tzu",
["SOLDIER_TOWER_PANDAS_MALE_2_NAME"] = "Qian",
["SOLDIER_TOWER_PANDAS_MALE_3_NAME"] = "Xueqin",
["SOLDIER_TOWER_PANDAS_MALE_4_NAME"] = "Nai'an",
["SOLDIER_TOWER_PANDAS_MALE_5_NAME"] = "Xun",
["SOLDIER_TOWER_PANDAS_MALE_6_NAME"] = "Xingjian",
["SOLDIER_TOWER_PANDAS_MALE_7_NAME"] = "Wei",
["SOLDIER_TOWER_PANDAS_MALE_8_NAME"] = "Chen",
["SOLDIER_TOWER_ROCKET_GUNNERS_10_NAME"] = "Fortus",
["SOLDIER_TOWER_ROCKET_GUNNERS_1_NAME"] = "Axl",
["SOLDIER_TOWER_ROCKET_GUNNERS_2_NAME"] = "Rose",
["SOLDIER_TOWER_ROCKET_GUNNERS_3_NAME"] = "Slash",
["SOLDIER_TOWER_ROCKET_GUNNERS_4_NAME"] = "Hudson",
["SOLDIER_TOWER_ROCKET_GUNNERS_5_NAME"] = "Izzy",
["SOLDIER_TOWER_ROCKET_GUNNERS_6_NAME"] = "Duff",
["SOLDIER_TOWER_ROCKET_GUNNERS_7_NAME"] = "Adler",
["SOLDIER_TOWER_ROCKET_GUNNERS_8_NAME"] = "Dizzy",
["SOLDIER_TOWER_ROCKET_GUNNERS_9_NAME"] = "Ferrer",
["SOLDIER_ZHU_APPRENTICE_NAME"] = "Zhu Bajie",
["SPECIAL_ARBOREAN_BARRACK_DESCRIPTION"] = "Summon 3 arborean soldiers that fight enemies in their path.",
["SPECIAL_ARBOREAN_BARRACK_NAME"] = "Arborean citizens",
["SPECIAL_ARBOREAN_HONEY_DESCRIPTION"] = "The Beekeeper takes his post, commanding his bees to slow and damage foes with sticky honey!",
["SPECIAL_ARBOREAN_HONEY_NAME"] = "Arborean Beekeeper",
["SPECIAL_ARBOREAN_OLDTREE_DESCRIPTION"] = "The grumpy fella unleashes a massive rolling log that crushes enemies in its path.",
["SPECIAL_ARBOREAN_OLDTREE_NAME"] = "Old Tree",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_DESCRIPTION"] = "Nimble protectors of the forest.",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_NAME"] = "Arborean Thornspear",
["SPECIAL_PRIESTS_SOLDIERS_DESCRIPTION"] = "Redeemed cultists that turn into abominations when they die.",
["SPECIAL_PRIESTS_SOLDIERS_NAME"] = "Blinded Cultist",
["SPECIAL_REPAIR_HOLDER_DRAGON_DESCRIPTION"] = "Extinguish the flames to free the tower instantly.",
["SPECIAL_REPAIR_HOLDER_DRAGON_NAME"] = "Engulfed in Flames",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_DESCRIPTION"] = "Increases health of tower's units.\nSpawns up to 3 Stone Warriors.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_NAME"] = "Elemental Holder: Earth",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_DESCRIPTION"] = "Adds extra damage to the constructed tower.\nOccasionally kills an enemy instantly.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_NAME"] = "Elemental Holder: Fire",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_DESCRIPTION"] = "Reduces construction cost.\nGenerates gold from enemies.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_NAME"] = "Elemental Holder: Metal",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_DESCRIPTION"] = "Constantly heals nearby allied units.\nTeleports enemies back along the path.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_NAME"] = "Elemental Holder: Water",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_DESCRIPTION"] = "Adds extra range to the constructed tower.\nOccasionally spawns briefly lingering roots that slow enemies.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_NAME"] = "Elemental Holder: Wood",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_DESCRIPTION"] = "Clean up the rubble to enable this strategic point.",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_NAME"] = "Rubble",
["SPECIAL_REPAIR_HOLDER_SPIDERS_DESCRIPTION"] = "Free the holder from the webs to enable this strategic point.",
["SPECIAL_REPAIR_HOLDER_SPIDERS_NAME"] = "Webbed Holder",
["SPECIAL_REPAIR_OVERSEER_DESCRIPTION"] = "Repel the tentacles to unlock this strategic position.",
["SPECIAL_REPAIR_OVERSEER_NAME"] = "Tentacles",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_DESCRIPTION"] = "Hire an Elven Mercenary to aid in battle. Respawns every 10 seconds.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "Elven Mercenaries I",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_DESCRIPTION"] = "Hire up to 2 Elven Mercenaries to aid in battle. Respawns every 10 seconds.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "Elven Mercenaries II",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_DESCRIPTION"] = "Hire up to 3 Elven Mercenaries to aid in battle. Respawns every 10 seconds.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "Elven Mercenaries III",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_1"] = "Casts bolts of magic that destroy Mydrias' illusions and prevent her from creating more for some seconds.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_2"] = "Summons 2 Demon Guards that walk through the path fighting enemies.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_3"] = "Traps Denas, preventing him from moving and attacking.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_1"] = "Soul Impact",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_2"] = "Infernal Spawns",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_3"] = "Magic Shackles",
["START"] = "START",
["START BATTLE!"] = "START BATTLE!",
["START HERE!"] = "START HERE!",
["STRATEGY BASICS!"] = "STRATEGY BASICS!",
["Select"] = "Select",
["Select and train abilities"] = "Select and train abilities",
["Select by clicking on the portrait or hero unit. Hotkey: space bar"] = "Select by clicking on the portrait or hero unit. Hotkey: space bar",
["Select hero"] = "Select hero",
["Selected"] = "Selected",
["Sell Tower"] = "Sell Tower",
["Sell this tower and get a %s GP refund."] = "Sell this tower and get a %s GP refund.",
["Short"] = "Short",
["Shows level, health and experience."] = "Shows level, health and experience.",
["Skills"] = "Skills",
["Skip this!"] = "Skip this!",
["Slow"] = "Slow",
["Special abilities"] = "Special abilities",
["Support your soldiers with ranged towers!"] = "Support your soldiers with ranged towers!",
["Survival mode!"] = "Survival mode!",
["TAP_TO_START"] = "Tap to start",
["TAUNT_BOSS_PIG_FROM_POOL_0001"] = "I’m gonna make you squeal!",
["TAUNT_BOSS_PIG_FROM_POOL_0002"] = "Say ‘bacon’ again. I double dare you!",
["TAUNT_BOSS_PIG_FROM_POOL_0003"] = "Humans are back on the menu, boys!",
["TAUNT_BOSS_PIG_FROM_POOL_0004"] = "Hurry up! I’m hungry.",
["TAUNT_BOSS_PIG_FROM_POOL_0005"] = "I shall enjoy watching you die.",
["TAUNT_BOSS_PIG_FROM_POOL_0006"] = "I know, I'm the wurst.",
["TAUNT_LVL30_BOSS_ABILITY_01"] = "Feast, my children!",
["TAUNT_LVL30_BOSS_ABILITY_02"] = "HANG in there! MWAHAHAHA!",
["TAUNT_LVL30_BOSS_ABILITY_03"] = "For the Cult!",
["TAUNT_LVL30_BOSS_ABILITY_04"] = "Tasty meals for everyone!",
["TAUNT_LVL30_BOSS_ABILITY_05"] = "My spider sense is tingling!",
["TAUNT_LVL30_BOSS_ABILITY_06"] = "Kneel before me, Alliance!",
["TAUNT_LVL30_BOSS_ABILITY_07"] = "My house, my rules!",
["TAUNT_LVL30_BOSS_ABILITY_08"] = "No one escapes my grasp!",
["TAUNT_LVL30_BOSS_ABILITY_09"] = "Die, humanoid pestilence!",
["TAUNT_LVL30_BOSS_ABILITY_10"] = "Pulling your strings!",
["TAUNT_LVL30_BOSS_ABILITY_11"] = "Kill 'em all!",
["TAUNT_LVL30_BOSS_INTRO_01"] = "Finally! My sisters’ slayers have come…",
["TAUNT_LVL30_BOSS_INTRO_02"] = "I will avenge my siblings Sarelgaz and Mactans…",
["TAUNT_LVL30_BOSS_INTRO_03"] = "And become a goddess, all on the same day!",
["TAUNT_LVL30_BOSS_PREFIGHT_01"] = "Enough of this...",
["TAUNT_LVL30_BOSS_PREFIGHT_02"] = "You are nothing but insignificant insects...",
["TAUNT_LVL30_BOSS_PREFIGHT_03"] = "Caught in the Queen's web!",
["TAUNT_LVL32_BOSS_ABILITY_01"] = "Fools! I wield the divine flame, Samadhi Fire!",
["TAUNT_LVL32_BOSS_ABILITY_02"] = "Blazing flames surge from the heavens!",
["TAUNT_LVL32_BOSS_ABILITY_03"] = "Fear True Fire in its purest form!",
["TAUNT_LVL32_BOSS_ABILITY_04"] = "Flesh and souls burn alike !",
["TAUNT_LVL32_BOSS_FIGHT_01"] = "The fire in me will never die!",
["TAUNT_LVL32_BOSS_FINAL_01"] = "My flame is dousing...\nbut I still have my dragon...",
["TAUNT_LVL32_BOSS_INTRO_01"] = "So you have an army?",
["TAUNT_LVL32_BOSS_INTRO_02"] = "I have a dragon! Ha ha ha ha!",
["TAUNT_LVL32_BOSS_PREFIGHT_01"] = "Enough! This is the part where I win!",
["TAUNT_LVL32_BOSS_PREFIGHT_02"] = "Admire my true form!",
["TAUNT_LVL34_BOSS_BOSSFIGHT_01"] = "Ok then, I know just what we need. More me. Me, me, me...",
["TAUNT_LVL34_BOSS_DEATH_01"] = "This can't be... No matter, my husband will make you pay...",
["TAUNT_LVL34_BOSS_INTRO_01"] = "You monkeys! You dare come here after what you did to my son?",
["TAUNT_LVL34_BOSS_WAVES_01"] = "Taste my power, insolent fools!",
["TAUNT_LVL34_BOSS_WAVES_02"] = "The end is nigh!",
["TAUNT_LVL35_BOSS_DESTROY_HOUSE_01"] = "Mmm that was expensive. Time for some firepower!",
["TAUNT_LVL35_BOSS_DESTROY_HOUSE_02"] = "Ah, the sound of perseverance. Water time, my love!",
["TAUNT_LVL35_BOSS_DESTROY_HOUSE_03"] = "Grrr! Submit to my vulgar display of power!",
["TAUNT_LVL35_BOSS_INTRO_01"] = "Petty humnas, rejoice while you remain among the living.",
["TAUNT_LVL35_BOSS_INTRO_02"] = "Let me explain why killing is my business.",
["TAUNT_LVL35_BOSS_INTRO_03"] = "It's time for the new order.",
["TAUNT_LVL35_BOSS_INTRO_04"] = "Arrgh I'm screaming for vengeance!",
["TAUNT_LVL35_BOSS_PREFIGHT_01"] = "This ends now! I'll demonstrate why I reign in blood!",
["TAUNT_STAGE02_RAELYN_0001"] = "Let's do this.",
["TAUNT_STAGE02_VEZNAN_0001"] = "Here they come. I'll help your puny forces...",
["TAUNT_STAGE02_VEZNAN_0002"] = "...I mean, one of my best soldiers will do it. HA!",
["TAUNT_STAGE02_VEZNAN_0003"] = "HA HA HA!",
["TAUNT_STAGE06_BOSS_PIG_PREBATTLE_0001"] = "Fine... I’ll do it myself.",
["TAUNT_STAGE06_BOSS_PIG_RESPONSE_0001"] = "Relax, everything is under control.",
["TAUNT_STAGE06_CULTIST_GREETING_0001"] = "I see you are very comfortable there...",
["TAUNT_STAGE06_CULTIST_GREETING_0002"] = "...you better keep your end of the deal.",
["TAUNT_STAGE11_CULTIST_LEADER_0001"] = "It is good you made it this far...",
["TAUNT_STAGE11_CULTIST_LEADER_0002"] = "...but you can’t stop the inevitable!",
["TAUNT_STAGE11_CULTIST_LEADER_0003"] = "ENOUGH!!!",
["TAUNT_STAGE11_CULTIST_LEADER_0004"] = "It’s time for you to BOW before us!",
["TAUNT_STAGE11_CULTIST_LEADER_0005"] = "Grrr... this is not the end!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0001"] = "A new world awaits us.",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0002"] = "You underestimate my power.",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0003"] = "Oculus Poculus!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0004"] = "Hear the sound of inevitability!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0005"] = "Am I evil? Yes I am!",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0006"] = "The All-Seeing blesses us!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0001"] = "Your end is Nigh!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0002"] = "My eyes have been opened!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0003"] = "Say “hello” to my void friends!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0004"] = "Oculus Poculus!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0005"] = "Pathetic weak scum!",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0006"] = "The All-Seeing blesses us!",
["TAUNT_STAGE11_VEZNAN_0001"] = "Denas, my friend. Long time no see!",
["TAUNT_STAGE15_CULTIST_0001"] = "It is close... I can feel it awakening!",
["TAUNT_STAGE15_CULTIST_0002"] = "A new era is near. Your efforts will be in vain!",
["TAUNT_STAGE15_CULTIST_0003"] = "Grrr… your alliance is powerful.",
["TAUNT_STAGE15_CULTIST_0004"] = "But I will show you what true power is!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0001"] = "Fools! You've come to die.",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0002"] = "Surrender before its gaze!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0003"] = "You will become true believers.",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0004"] = "Alliance or not, you are doomed!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0005"] = "There is no life in the Void. Only death!",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0006"] = "Stop wasting my time!",
["TAUNT_STAGE15_DENAS_0001"] = "I have a score to settle. This fight I won’t miss!",
["TAUNT_STAGE16_DENAS_AFTER_BOSSFIGHT_0001"] = "Didn't see it coming, huh?",
["TAUNT_STAGE18_ERIDAN_FIGHT_0001"] = "Blood has been spilled tonight.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0002"] = "In Elynie we trust.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0003"] = "Gnilur speek Edihnori!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0004"] = "I just can't seem to miss.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0005"] = "Aredhel will prevail!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0006"] = "These are no mere rangers!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0007"] = "Are you keeping the score?",
["TAUNT_STAGE18_ERIDAN_FIGHT_0008"] = "Let them come!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0001"] = "You have my bow!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0002"] = "Act swiftly!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0003"] = "To your positions!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0004"] = "Keep your eyes open!",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0001"] = "That's enough for the warm up!",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0002"] = "You have proved yourself a nuisance...",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0003"] = "Let the real fight begin!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0001"] = "I bend all souls!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0002"] = "The elves shall rise again.",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0003"] = "I do even lift... the dead!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0004"] = "By the ancient unholy powers!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0005"] = "Fear my children of the grave!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0006"] = "I'll restore glory to my people.",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0001"] = "Ah, the mighty Alliance came to visit.",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0002"] = "It's time for the veil to be lifted!",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0003"] = "Let me show you the power of death!",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0001"] = "Free at last to devour....",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0002"] = "EEEEVERYTHING!!!!!",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0001"] = "Enough meddling!",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0002"] = "Grymbeard will teach you some manners.",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0003"] = "All aboard, AHAHAHA!",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0001"] = "You insolent buffoons!",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0002"] = "You will never catch me, HAHAHA!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "No! There's still more-...",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "GREAT SCOTT!!!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0001"] = "You're no match for this army!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0002"] = "Grymbeard is not in danger.",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0003"] = "Grymbeard IS the danger!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0004"] = "Would a madman be able to do this?",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0005"] = "The world will bow before Grymbeard!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0001"] = "Grymbeard's patience has run thin.",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0002"] = "You're gonna see some serious stuff!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0003"] = "Grymbeard needs no one but himself!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0004"] = "Will you hurry up?!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "You and your damned meddling Alliance!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "I'll teach you not to mess...",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0003"] = "...with the MAIN dwarf!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0001"] = "Stomp all the clones you want, I'll just make more.",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0002"] = "If you want something done right, do it yourself.",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0003"] = "Oh Grymbeard, you genius!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0004"] = "You're not getting away with this!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0005"] = "Are you even trying?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0006"] = "Think you can surpass my creations?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0001"] = "I guess you couldn't get enough of me...",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0002"] = "...and now you want to take a shot at the PRIME dwarf?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0003"] = "You are welcome to try.",
["TAUNT_TUTORIAL_ARBOREAN_ALL_0001"] = "Keep going! We be-leaf in you.",
["TAUNT_TUTORIAL_ARBOREAN_BARRACK_0001"] = "Here, build a barrack!",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "Limblliam",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "Tentacle Henry",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "Tentacle Geoffrey",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "Tentaclas",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "Tedtacle",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "Holimb",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "Tentodo",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "Limbdric",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "Halimb",
["TERMS_OF_SERVICE_LINK"] = "Terms of Service",
["TIP!"] = "TIP!",
["TIP_1"] = "Remember to tap over enemies and read the tips to learn more about how to deal with them. ",
["TIP_10"] = "Flying enemies can't be blocked, and most artillery towers ignore them.",
["TIP_11"] = "You can use the powerful items of the shop in battle. They can help you turn the odds in your favor!",
["TIP_2"] = "Magical damage is a very effective way to deal with heavy armored enemies.",
["TIP_3"] = "If you upgrade a Barracks, a batch of fresh soldiers will come out instantly to replace their withered comrades.",
["TIP_4"] = "Calling an early wave gives you extra gold and decreases spell cooldowns.",
["TIP_5"] = "You can adjust the rally point of Barracks to move them to advantageous positions for your strategies.",
["TIP_6"] = "Pay attention to the next wave flag. If you tap it once, you'll see what's coming next. Be prepared!",
["TIP_7"] = "Flying enemies receive damage from most area attacks even if they're not directly aimed at them.",
["TIP_8"] = "Sometimes selling a tower that's being ineffective can give you enough money to overcome a tight spot.",
["TIP_9"] = "Upgrading a tower is usually more effective than building more of the same.",
["TIP_ALERT_ICON"] = "TIP",
["TIP_TITLE"] = "Tip:",
["TOWER_ARBOREAN_EMISSARY_1_DESCRIPTION"] = "The Arboreans make their enemies more vulnerable using powerful nature magic.",
["TOWER_ARBOREAN_EMISSARY_1_NAME"] = "Arborean Emissary I",
["TOWER_ARBOREAN_EMISSARY_2_DESCRIPTION"] = "The Arboreans make their enemies more vulnerable using powerful nature magic.",
["TOWER_ARBOREAN_EMISSARY_2_NAME"] = "Arborean Emissary II",
["TOWER_ARBOREAN_EMISSARY_3_DESCRIPTION"] = "The Arboreans make their enemies more vulnerable using powerful nature magic.",
["TOWER_ARBOREAN_EMISSARY_3_NAME"] = "Arborean Emissary III",
["TOWER_ARBOREAN_EMISSARY_4_DESCRIPTION"] = "The Arboreans make their enemies more vulnerable using powerful nature magic.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_DESCRIPTION"] = "Summons wisps that heal %$towers.arborean_emissary.gift_of_nature.s_heal[1]%$ health per second for %$towers.arborean_emissary.gift_of_nature.duration[1]%$ seconds to allies in an area.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_NAME"] = "GIFT OF NATURE",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_DESCRIPTION"] = "Summons wisps that heal %$towers.arborean_emissary.gift_of_nature.s_heal[2]%$ health per second for %$towers.arborean_emissary.gift_of_nature.duration[2]%$ seconds to allies in an area.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_NAME"] = "GIFT OF NATURE",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_DESCRIPTION"] = "Summons wisps that heal %$towers.arborean_emissary.gift_of_nature.s_heal[3]%$ health per second for %$towers.arborean_emissary.gift_of_nature.duration[3]%$ seconds to allies in an area.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_NAME"] = "GIFT OF NATURE",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NAME"] = "GIFT OF NATURE",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NOTE"] = "Never meddle with the Green.",
["TOWER_ARBOREAN_EMISSARY_4_NAME"] = "Arborean Emissary IV",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_DESCRIPTION"] = "Grows %$towers.arborean_emissary.wave_of_roots.max_targets[1]%$ roots along the path, dealing %$towers.arborean_emissary.wave_of_roots.s_damage[1]%$ true damage and stunning enemies for %$towers.arborean_emissary.wave_of_roots.mod_duration[1]%$ seconds.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_NAME"] = "BRAMBLE GRASP",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_DESCRIPTION"] = "Grows %$towers.arborean_emissary.wave_of_roots.max_targets[2]%$ roots along the path, dealing %$towers.arborean_emissary.wave_of_roots.s_damage[2]%$ true damage and stunning enemies for %$towers.arborean_emissary.wave_of_roots.mod_duration[2]%$ seconds.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_NAME"] = "BRAMBLE GRASP",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_DESCRIPTION"] = "Grows %$towers.arborean_emissary.wave_of_roots.max_targets[3]%$ roots along the path, dealing %$towers.arborean_emissary.wave_of_roots.s_damage[3]%$ true damage and stunning enemies for %$towers.arborean_emissary.wave_of_roots.mod_duration[3]%$ seconds.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_NAME"] = "BRAMBLE GRASP",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NAME"] = "BRAMBLE GRASP",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NOTE"] = "Watch your step.",
["TOWER_ARBOREAN_EMISSARY_DESC"] = "When provoked, the peaceful Arboreans have been known to use their magic to mark and weaken their enemies.",
["TOWER_ARBOREAN_EMISSARY_NAME"] = "Arborean Emissary",
["TOWER_ARBOREAN_SENTINELS_DESCRIPTION"] = "Nimble protectors of the forest.",
["TOWER_ARBOREAN_SENTINELS_NAME"] = "Arborean Thornspears",
["TOWER_ARCANE_WIZARD_1_DESCRIPTION"] = "Well versed in the arts of magic, these wizards are always ready for a fight.",
["TOWER_ARCANE_WIZARD_1_NAME"] = "Arcane Wizard I",
["TOWER_ARCANE_WIZARD_2_DESCRIPTION"] = "Well versed in the arts of magic, these wizards are always ready for a fight.",
["TOWER_ARCANE_WIZARD_2_NAME"] = "Arcane Wizard II",
["TOWER_ARCANE_WIZARD_3_DESCRIPTION"] = "Well versed in the arts of magic, these wizards are always ready for a fight.",
["TOWER_ARCANE_WIZARD_3_NAME"] = "Arcane Wizard III",
["TOWER_ARCANE_WIZARD_4_DESCRIPTION"] = "Well versed in the arts of magic, these wizards are always ready for a fight.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_DESCRIPTION"] = "Casts a ray that instantly kills the target. Bosses and mini-bosses receive %$towers.arcane_wizard.disintegrate.boss_damage[1]%$ magic damage instead.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_NAME"] = "DISINTEGRATE",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_DESCRIPTION"] = "Reduces its cooldown to %$towers.arcane_wizard.disintegrate.cooldown[2]%$ seconds. Damage to bosses and mini-bosses is now %$towers.arcane_wizard.disintegrate.boss_damage[2]%$.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_NAME"] = "DISINTEGRATE",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_DESCRIPTION"] = "Reduces its cooldown to %$towers.arcane_wizard.disintegrate.cooldown[3]%$ seconds. Damage to bosses and mini-bosses is now %$towers.arcane_wizard.disintegrate.boss_damage[3]%$.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_NAME"] = "DISINTEGRATE",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NAME"] = "DISINTEGRATE",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NOTE"] = "Dust to dust.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_DESCRIPTION"] = "Increases the damage of nearby towers by %$towers.arcane_wizard.empowerment.s_damage_factor[1]%$%.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_NAME"] = "EMPOWERMENT",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_DESCRIPTION"] = "Increases the damage of nearby towers by %$towers.arcane_wizard.empowerment.s_damage_factor[2]%$%.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_NAME"] = "EMPOWERMENT",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_DESCRIPTION"] = "Increases the damage of nearby towers by %$towers.arcane_wizard.empowerment.s_damage_factor[3]%$%",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_NAME"] = "EMPOWERMENT",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NAME"] = "EMPOWERMENT",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NOTE"] = "Unlimited Power.",
["TOWER_ARCANE_WIZARD_4_NAME"] = "Arcane Wizard IV",
["TOWER_ARCANE_WIZARD_DESC"] = "Tapping into pure magic, Linirean wizards wield enough power to utterly destroy their enemies.",
["TOWER_ARCANE_WIZARD_NAME"] = "Arcane Wizard",
["TOWER_BALLISTA_1_DESCRIPTION"] = "A great addition to greenskin warfare, it's a miracle it hasn't fallen apart yet.",
["TOWER_BALLISTA_1_NAME"] = "Ballista Outpost I",
["TOWER_BALLISTA_2_DESCRIPTION"] = "A great addition to greenskin warfare, it's a miracle it hasn't fallen apart yet.",
["TOWER_BALLISTA_2_NAME"] = "Ballista Outpost II",
["TOWER_BALLISTA_3_DESCRIPTION"] = "A great addition to greenskin warfare, it's a miracle it hasn't fallen apart yet.",
["TOWER_BALLISTA_3_NAME"] = "Ballista Outpost III",
["TOWER_BALLISTA_4_DESCRIPTION"] = "A great addition to greenskin warfare, it's a miracle it hasn't fallen apart yet.",
["TOWER_BALLISTA_4_NAME"] = "Ballista Outpost IV",
["TOWER_BALLISTA_4_SKILL_BOMB_1_DESCRIPTION"] = "Fires a bomb made of scrap at great range that deals %$towers.ballista.skill_bomb.damage_min[1]%$-%$towers.ballista.skill_bomb.damage_max[1]%$ physical damage. It slows enemies for %$towers.ballista.skill_bomb.duration[1]%$ seconds.",
["TOWER_BALLISTA_4_SKILL_BOMB_1_NAME"] = "SCRAP BOMB",
["TOWER_BALLISTA_4_SKILL_BOMB_2_DESCRIPTION"] = "The scrap bomb deals %$towers.ballista.skill_bomb.damage_min[2]%$-%$towers.ballista.skill_bomb.damage_max[2]%$ physical damage. It slows enemies for %$towers.ballista.skill_bomb.duration[1]%$ seconds.",
["TOWER_BALLISTA_4_SKILL_BOMB_2_NAME"] = "SCRAP BOMB",
["TOWER_BALLISTA_4_SKILL_BOMB_3_DESCRIPTION"] = "The scrap bomb deals %$towers.ballista.skill_bomb.damage_min[3]%$-%$towers.ballista.skill_bomb.damage_max[3]%$ physical damage. It slows enemies for %$towers.ballista.skill_bomb.duration[1]%$ seconds.",
["TOWER_BALLISTA_4_SKILL_BOMB_3_NAME"] = "SCRAP BOMB",
["TOWER_BALLISTA_4_SKILL_BOMB_NOTE"] = "Fore!",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_DESCRIPTION"] = "The last shot of the tower deals %$towers.ballista.skill_final_shot.s_damage_factor[1]%$% more damage and stuns the target for %$towers.ballista.skill_final_shot.s_stun%$ seconds.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_NAME"] = "FINAL NAIL",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_DESCRIPTION"] = "The last shot deals %$towers.ballista.skill_final_shot.s_damage_factor[2]%$% more damage and stuns the target for %$towers.ballista.skill_final_shot.s_stun%$ seconds.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_NAME"] = "FINAL NAIL",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_DESCRIPTION"] = "The last shot deals %$towers.ballista.skill_final_shot.s_damage_factor[3]%$% more damage and stuns the target for %$towers.ballista.skill_final_shot.s_stun%$ seconds.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_NAME"] = "FINAL NAIL",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_NOTE"] = "That was one in a million, kid!",
["TOWER_BALLISTA_DESC"] = "Overly enthusiastic about war, the goblins made an extra effort to make sure they wouldn't need to wield a bow again.",
["TOWER_BALLISTA_NAME"] = "Ballista Outpost",
["TOWER_BARREL_1_DESCRIPTION"] = "The northerners' potion caskets are a powerful weapon against hordes of enemies.",
["TOWER_BARREL_1_NAME"] = "Battle Brewmasters I",
["TOWER_BARREL_2_DESCRIPTION"] = "The northerners' potion caskets are a powerful weapon against hordes of enemies.",
["TOWER_BARREL_2_NAME"] = "Battle Brewmasters II",
["TOWER_BARREL_3_DESCRIPTION"] = "The northerners' potion caskets are a powerful weapon against hordes of enemies.",
["TOWER_BARREL_3_NAME"] = "Battle Brewmasters III",
["TOWER_BARREL_4_DESCRIPTION"] = "The northerners' potion caskets are a powerful weapon against hordes of enemies.",
["TOWER_BARREL_4_NAME"] = "Battle Brewmasters IV",
["TOWER_BARREL_4_SKILL_BARREL_1_DESCRIPTION"] = "Throws a noxious barrel that deals %$towers.barrel.skill_barrel.explosion.damage_min[1]%$-%$towers.barrel.skill_barrel.explosion.damage_max[1]%$ physical damage. The barrel leaves a poison that deals %$towers.barrel.skill_barrel.poison.s_damage%$ true damage per second for %$towers.barrel.skill_barrel.poison.duration%$ seconds.",
["TOWER_BARREL_4_SKILL_BARREL_1_NAME"] = "BAD BATCH",
["TOWER_BARREL_4_SKILL_BARREL_2_DESCRIPTION"] = "The poison barrel's explosion deals %$towers.barrel.skill_barrel.explosion.damage_min[2]%$-%$towers.barrel.skill_barrel.explosion.damage_max[2]%$ physical damage. The barrel's poison deals %$towers.barrel.skill_barrel.poison.s_damage%$ true damage per second for %$towers.barrel.skill_barrel.poison.duration%$ seconds.",
["TOWER_BARREL_4_SKILL_BARREL_2_NAME"] = "BAD BATCH",
["TOWER_BARREL_4_SKILL_BARREL_3_DESCRIPTION"] = "The poison barrel's explosion deals %$towers.barrel.skill_barrel.explosion.damage_min[3]%$-%$towers.barrel.skill_barrel.explosion.damage_max[3]%$ physical damage. The barrel's poison deals %$towers.barrel.skill_barrel.poison.s_damage%$ true damage per second for %$towers.barrel.skill_barrel.poison.duration%$ seconds.",
["TOWER_BARREL_4_SKILL_BARREL_3_NAME"] = "BAD BATCH",
["TOWER_BARREL_4_SKILL_BARREL_NOTE"] = "Only for the daring!",
["TOWER_BARREL_4_SKILL_WARRIOR_1_DESCRIPTION"] = "Summons an empowered warrior to fight in the path. It has %$towers.barrel.skill_warrior.entity.hp_max[1]%$ health and deals %$towers.barrel.skill_warrior.entity.damage_min[1]%$-%$towers.barrel.skill_warrior.entity.damage_max[1]%$ physical damage.",
["TOWER_BARREL_4_SKILL_WARRIOR_1_NAME"] = "ELIXIR OF MIGHT",
["TOWER_BARREL_4_SKILL_WARRIOR_2_DESCRIPTION"] = "The warrior has %$towers.barrel.skill_warrior.entity.hp_max[2]%$ health and deals %$towers.barrel.skill_warrior.entity.damage_min[2]%$-%$towers.barrel.skill_warrior.entity.damage_max[2]%$ physical damage.",
["TOWER_BARREL_4_SKILL_WARRIOR_2_NAME"] = "ELIXIR OF MIGHT",
["TOWER_BARREL_4_SKILL_WARRIOR_3_DESCRIPTION"] = "The warrior has %$towers.barrel.skill_warrior.entity.hp_max[3]%$ health and deals %$towers.barrel.skill_warrior.entity.damage_min[3]%$-%$towers.barrel.skill_warrior.entity.damage_max[3]%$ physical damage.",
["TOWER_BARREL_4_SKILL_WARRIOR_3_NAME"] = "ELIXIR OF MIGHT",
["TOWER_BARREL_4_SKILL_WARRIOR_NOTE"] = "Tastes like victory!",
["TOWER_BARREL_DESC"] = "The northerners are experts in the art of potion making and use their beverages in battle to fight enemies.",
["TOWER_BARREL_NAME"] = "Battle Brewmasters",
["TOWER_BARREL_WARRIOR_NAME"] = "Halfdan the Blunt",
["TOWER_BROKEN_DESCRIPTION"] = "This tower is damaged, spend gold to repair it.",
["TOWER_BROKEN_NAME"] = "Damaged Tower",
["TOWER_CROCS_EATEN_DESCRIPTION"] = "Magically reconstruct the tower to its original shape.",
["TOWER_CROCS_EATEN_NAME"] = "Tower leftovers",
["TOWER_DARK_ELF_1_DESCRIPTION"] = "No matter the distance or the strength of the enemy, their aim is always true.",
["TOWER_DARK_ELF_1_NAME"] = "Twilight Longbows I",
["TOWER_DARK_ELF_2_DESCRIPTION"] = "No matter the distance or the strength of the enemy, their aim is always true.",
["TOWER_DARK_ELF_2_NAME"] = "Twilight Longbows II",
["TOWER_DARK_ELF_3_DESCRIPTION"] = "No matter the distance or the strength of the enemy, their aim is always true.",
["TOWER_DARK_ELF_3_NAME"] = "Twilight Longbows III",
["TOWER_DARK_ELF_4_DESCRIPTION"] = "No matter the distance or the strength of the enemy, their aim is always true.",
["TOWER_DARK_ELF_4_NAME"] = "Twilight Longbows IV",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_DESCRIPTION"] = "Each time it kills an enemy, the tower increases its attack damage by %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_NAME"] = "THRILL OF THE HUNT",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_DESCRIPTION"] = "Each time it kills an enemy, the tower increases its attack damage by %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_NAME"] = "THRILL OF THE HUNT",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_DESCRIPTION"] = "Each time it kills an enemy, the tower increases its attack damage by %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_NAME"] = "THRILL OF THE HUNT",
["TOWER_DARK_ELF_4_SKILL_BUFF_NOTE"] = "Tally-ho!",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_DESCRIPTION"] = "Summons two Twilight Harassers. They have %$towers.dark_elf.soldier.hp[1]%$ health and deal %$towers.dark_elf.soldier.basic_attack.damage_min[1]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[1]%$ physical damage.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_NAME"] = "SUPPORT BLADES",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_DESCRIPTION"] = "The Twilight Harassers now have %$towers.dark_elf.soldier.hp[2]%$ health and deal %$towers.dark_elf.soldier.basic_attack.damage_min[2]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[2]%$ physical damage.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_NAME"] = "SUPPORT BLADES",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_DESCRIPTION"] = "The Twilight Harassers now have %$towers.dark_elf.soldier.hp[3]%$ health and deal %$towers.dark_elf.soldier.basic_attack.damage_min[3]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[3]%$ physical damage.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_NAME"] = "SUPPORT BLADES",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_NOTE"] = "They're coming out to play.",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_DESCRIPTION"] = "Changes the tower's focus to the enemy that's nearest to the exit.",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NAME"] = "Enemy Focus: Foremost",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NOTE"] = "Don't let them through!",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_DESCRIPTION"] = "Changes the tower's focus to the enemy with the highest max health.",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NAME"] = "Enemy Focus: Max HP",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NOTE"] = "Go for the big one!",
["TOWER_DARK_ELF_DESC"] = "Archers specialized in hunting powerful enemies from afar, empowering their shots with dark energy.",
["TOWER_DARK_ELF_NAME"] = "Twilight Longbows",
["TOWER_DEMON_PIT_1_DESCRIPTION"] = "Mischievous and dangerous, these demons are always looking for trouble.",
["TOWER_DEMON_PIT_1_NAME"] = "Demon Pit I",
["TOWER_DEMON_PIT_2_DESCRIPTION"] = "Mischievous and dangerous, these demons are always looking for trouble.",
["TOWER_DEMON_PIT_2_NAME"] = "Demon Pit II",
["TOWER_DEMON_PIT_3_DESCRIPTION"] = "Mischievous and dangerous, these demons are always looking for trouble.",
["TOWER_DEMON_PIT_3_NAME"] = "Demon Pit III",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_DESCRIPTION"] = "Spawns a huge imp with %$towers.demon_pit.big_guy.hp_max[1]%$ health that deals %$towers.demon_pit.big_guy.melee_attack.damage_min[1]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[1]%$ physical damage. When exploding, deals %$towers.demon_pit.big_guy.explosion_damage[1]%$ damage.",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_NAME"] = "BIG BOSS",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_DESCRIPTION"] = "The Big Imp has %$towers.demon_pit.big_guy.hp_max[2]%$ health and deals %$towers.demon_pit.big_guy.melee_attack.damage_min[2]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[2]%$ physical damage. The explosion deals %$towers.demon_pit.big_guy.explosion_damage[2]%$ damage.",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_NAME"] = "BIG BOSS",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_DESCRIPTION"] = "The Big Imp has %$towers.demon_pit.big_guy.hp_max[3]%$ health and deals %$towers.demon_pit.big_guy.melee_attack.damage_min[3]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[3]%$ physical damage. The explosion deals %$towers.demon_pit.big_guy.explosion_damage[3]%$ damage.",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_NAME"] = "BIG BOSS",
["TOWER_DEMON_PIT_4_BIG_DEMON_NAME"] = "BIG BOSS",
["TOWER_DEMON_PIT_4_BIG_DEMON_NOTE"] = "Just trying to chill.",
["TOWER_DEMON_PIT_4_DESCRIPTION"] = "Mischievous and dangerous, these demons are always looking for trouble.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_DESCRIPTION"] = "The imps' explosion now deals %$towers.demon_pit.master_exploders.s_damage_increase[1]%$% more damage and burns enemies, dealing %$towers.demon_pit.master_exploders.s_total_burning_damage_min[1]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[1]%$ true damage per second for %$towers.demon_pit.master_exploders.s_burning_duration[1]%$ seconds.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_NAME"] = "MASTER EXPLODERS",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_DESCRIPTION"] = "The imps' explosion deals %$towers.demon_pit.master_exploders.s_damage_increase[2]%$% more damage. Burning deals %$towers.demon_pit.master_exploders.s_total_burning_damage_min[2]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[2]%$ true damage per second for %$towers.demon_pit.master_exploders.s_burning_duration[2]%$ seconds.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_NAME"] = "MASTER EXPLODERS",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_DESCRIPTION"] = "The imps' explosion deals %$towers.demon_pit.master_exploders.s_damage_increase[3]%$% more damage. Burning deals %$towers.demon_pit.master_exploders.s_total_burning_damage_min[3]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[3]%$ true damage per second for %$towers.demon_pit.master_exploders.s_burning_duration[3]%$ seconds.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_NAME"] = "MASTER EXPLODERS",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NAME"] = "MASTER EXPLODERS",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NOTE"] = "Only an idiot would do this job.",
["TOWER_DEMON_PIT_4_NAME"] = "Demon Pit IV",
["TOWER_DEMON_PIT_DESC"] = "Spawning from the lava's depths, these Imps don't hesitate to fling themselves into enemies' paths.",
["TOWER_DEMON_PIT_NAME"] = "Demon Pit",
["TOWER_DEMON_PIT_SOLDIER_BIG_GUY_NAME"] = "Big Guy",
["TOWER_DEMON_PIT_SOLDIER_NAME"] = "Imp",
["TOWER_DWARF_1_DESCRIPTION"] = "Despite being as short as their fuses, nothing gets past their lines alive.",
["TOWER_DWARF_1_NAME"] = "Cannoneer Squad I",
["TOWER_DWARF_2_DESCRIPTION"] = "Despite being as short as their fuses, nothing gets past their lines alive.",
["TOWER_DWARF_2_NAME"] = "Cannoneer Squad II",
["TOWER_DWARF_3_DESCRIPTION"] = "Despite being as short as their fuses, nothing gets past their lines alive.",
["TOWER_DWARF_3_NAME"] = "Cannoneer Squad III",
["TOWER_DWARF_4_DESCRIPTION"] = "Despite being as short as their fuses, nothing gets past their lines alive.",
["TOWER_DWARF_4_FORMATION_1_DESCRIPTION"] = "Add a third Cannoneer to the squad.",
["TOWER_DWARF_4_FORMATION_1_NAME"] = "GROWING RANKS",
["TOWER_DWARF_4_FORMATION_2_DESCRIPTION"] = "Add a fourth Cannoneer to the squad.",
["TOWER_DWARF_4_FORMATION_2_NAME"] = "GROWING RANKS",
["TOWER_DWARF_4_FORMATION_3_DESCRIPTION"] = "Add a fifth Cannoneer to the squad.",
["TOWER_DWARF_4_FORMATION_3_NAME"] = "GROWING RANKS",
["TOWER_DWARF_4_FORMATION_NOTE"] = "Girls just wanna have guns.",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_DESCRIPTION"] = "Fire an explosive that deals %$towers.dwarf.incendiary_ammo.damages_min[1]%$ - %$towers.dwarf.incendiary_ammo.damages_max[1]%$ damage and burns enemies in the area for %$towers.dwarf.incendiary_ammo.burn.s_damage[1]%$ damage across %$towers.dwarf.incendiary_ammo.burn.duration%$ seconds.",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_NAME"] = "INCENDIARY AMMO",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_DESCRIPTION"] = "Fire an explosive that deals %$towers.dwarf.incendiary_ammo.damages_min[2]%$ - %$towers.dwarf.incendiary_ammo.damages_max[2]%$ damage and burns enemies in the area for %$towers.dwarf.incendiary_ammo.burn.s_damage[2]%$ damage across %$towers.dwarf.incendiary_ammo.burn.duration%$ seconds.",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_NAME"] = "INCENDIARY AMMO",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_DESCRIPTION"] = "Fire an explosive that deals %$towers.dwarf.incendiary_ammo.damages_min[3]%$ - %$towers.dwarf.incendiary_ammo.damages_max[3]%$ damage and burns enemies in the area for %$towers.dwarf.incendiary_ammo.burn.s_damage[3]%$ damage across %$towers.dwarf.incendiary_ammo.burn.duration%$ seconds.",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_NAME"] = "INCENDIARY AMMO",
["TOWER_DWARF_4_INCENDIARY_AMMO_NOTE"] = "Hot to go!",
["TOWER_DWARF_4_NAME"] = "Cannoneer Squad IV",
["TOWER_DWARF_DESC"] = "Expert shooters with an unparalleled esprit de corps, sent from the north to control the unethical use of technology.",
["TOWER_DWARF_NAME"] = "Cannoneer Squad",
["TOWER_ELVEN_STARGAZERS_DESC"] = "Calling upon the energies of the cosmos, the Elven Stargazers can fight many enemies at the same time.",
["TOWER_ELVEN_STARGAZERS_NAME"] = "Elven Stargazer",
["TOWER_FLAMESPITTER_1_DESCRIPTION"] = "Its fire can be easily compared with that of a dragon, spreading terror amongst the wicked.",
["TOWER_FLAMESPITTER_1_NAME"] = "Dwarven Flamespitter I",
["TOWER_FLAMESPITTER_2_DESCRIPTION"] = "Its fire can be easily compared with that of a dragon, spreading terror amongst the wicked.",
["TOWER_FLAMESPITTER_2_NAME"] = "Dwarven Flamespitter II",
["TOWER_FLAMESPITTER_3_DESCRIPTION"] = "Its fire can be easily compared with that of a dragon, spreading terror amongst the wicked.",
["TOWER_FLAMESPITTER_3_NAME"] = "Dwarven Flamespitter III",
["TOWER_FLAMESPITTER_4_DESCRIPTION"] = "Its fire can be easily compared with that of a dragon, spreading terror amongst the wicked.",
["TOWER_FLAMESPITTER_4_NAME"] = "Dwarven Flamespitter IV",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_DESCRIPTION"] = "Fires a flaming bomb that deals %$towers.flamespitter.skill_bomb.s_damage[1]%$ physical damage and burns enemies for %$towers.flamespitter.skill_bomb.burning.s_damage%$ true damage per second for %$towers.flamespitter.skill_bomb.burning.duration%$ seconds.",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_NAME"] = "BLAZING TRAIL",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_DESCRIPTION"] = "The flaming bomb deals %$towers.flamespitter.skill_bomb.s_damage[2]%$ physical damage. Burning deals %$towers.flamespitter.skill_bomb.burning.s_damage%$ true damage per second for %$towers.flamespitter.skill_bomb.burning.duration%$ seconds.",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_NAME"] = "BLAZING TRAIL",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_DESCRIPTION"] = "The flaming bomb deals %$towers.flamespitter.skill_bomb.s_damage[3]%$ physical damage. Burning deals %$towers.flamespitter.skill_bomb.burning.s_damage%$ true damage per second for %$towers.flamespitter.skill_bomb.burning.duration%$ seconds.",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_NAME"] = "BLAZING TRAIL",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_NOTE"] = "Burn to be wild.",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_DESCRIPTION"] = "Columns of fire erupt from the path that deal %$towers.flamespitter.skill_columns.s_damage_out[1]%$-%$towers.flamespitter.skill_columns.s_damage_in[1]%$ physical damage and stun enemies for %$towers.flamespitter.skill_columns.s_stun%$ second.",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_NAME"] = "SCORCHING TORCHES",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_DESCRIPTION"] = "The fire columns deal %$towers.flamespitter.skill_columns.s_damage_out[2]%$-%$towers.flamespitter.skill_columns.s_damage_in[2]%$ physical damage and stun enemies for %$towers.flamespitter.skill_columns.s_stun%$ second.",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_NAME"] = "SCORCHING TORCHES",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_DESCRIPTION"] = "The fire columns deal %$towers.flamespitter.skill_columns.s_damage_out[3]%$-%$towers.flamespitter.skill_columns.s_damage_in[3]%$ physical damage and stun enemies for %$towers.flamespitter.skill_columns.s_stun%$ second.",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_NAME"] = "SCORCHING TORCHES",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_NOTE"] = "Watch your step!",
["TOWER_FLAMESPITTER_DESC"] = "Bringing the heat of the forge into battle, dwarves lend their fiery resolve to the alliance.",
["TOWER_FLAMESPITTER_NAME"] = "Dwarven Flamespitter",
["TOWER_GHOST_1_DESCRIPTION"] = "Now you see them, now you don't. Now you're dead.",
["TOWER_GHOST_1_NAME"] = "Grim Wraiths I",
["TOWER_GHOST_2_DESCRIPTION"] = "Now you see them, now you don't. Now you're dead.",
["TOWER_GHOST_2_NAME"] = "Grim Wraiths II",
["TOWER_GHOST_3_DESCRIPTION"] = "Now you see them, now you don't. Now you're dead.",
["TOWER_GHOST_3_NAME"] = "Grim Wraiths III",
["TOWER_GHOST_4_DESCRIPTION"] = "Now you see them, now you don't. Now you're dead.",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_DESCRIPTION"] = "The wraiths deal %$towers.ghost.extra_damage.s_damage[1]%$% extra damage after spending %$towers.ghost.extra_damage.cooldown_start%$ seconds in combat.",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_NAME"] = "SOUL SIPHONING",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_DESCRIPTION"] = "The wraiths deal %$towers.ghost.extra_damage.s_damage[2]%$% extra damage after spending %$towers.ghost.extra_damage.cooldown_start%$ seconds in combat.",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_NAME"] = "SOUL SIPHONING",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_DESCRIPTION"] = "The wraiths deal %$towers.ghost.extra_damage.s_damage[3]%$% extra damage after spending %$towers.ghost.extra_damage.cooldown_start%$ seconds in combat.",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_NAME"] = "SOUL SIPHONING",
["TOWER_GHOST_4_EXTRA_DAMAGE_NOTE"] = "Exposure not recommended.",
["TOWER_GHOST_4_NAME"] = "Grim Wraiths IV",
["TOWER_GHOST_4_SOUL_ATTACK_1_DESCRIPTION"] = "Defeated wraiths fling themselves against a nearby enemy, dealing %$towers.ghost.soul_attack.s_damage[1]%$ true damage, reducing its speed and halving its attack damage.",
["TOWER_GHOST_4_SOUL_ATTACK_1_NAME"] = "UNDYING DREAD",
["TOWER_GHOST_4_SOUL_ATTACK_2_DESCRIPTION"] = "Defeated wraiths fling themselves against a nearby enemy, dealing %$towers.ghost.soul_attack.s_damage[2]%$ true damage, reducing its speed and halving its attack damage.",
["TOWER_GHOST_4_SOUL_ATTACK_2_NAME"] = "UNDYING DREAD",
["TOWER_GHOST_4_SOUL_ATTACK_3_DESCRIPTION"] = "Defeated wraiths fling themselves against a nearby enemy, dealing %$towers.ghost.soul_attack.s_damage[3]%$ true damage, reducing its speed and halving its attack damage.",
["TOWER_GHOST_4_SOUL_ATTACK_3_NAME"] = "UNDYING DREAD",
["TOWER_GHOST_4_SOUL_ATTACK_NOTE"] = "You're coming with us!",
["TOWER_GHOST_DESC"] = "Specters that fight even after death. Their power allows them to move through shadows and surprise enemies.",
["TOWER_GHOST_NAME"] = "Grim Wraiths",
["TOWER_HERMIT_TOAD_1_DESCRIPTION"] = "A little magic, a little brute force, whatever it takes to get rid of pesky intruders.",
["TOWER_HERMIT_TOAD_1_NAME"] = "Bog Hermit I",
["TOWER_HERMIT_TOAD_2_DESCRIPTION"] = "A little magic, a little brute force, whatever it takes to get rid of pesky intruders.",
["TOWER_HERMIT_TOAD_2_NAME"] = "Bog Hermit II",
["TOWER_HERMIT_TOAD_3_DESCRIPTION"] = "A little magic, a little brute force, whatever it takes to get rid of pesky intruders.",
["TOWER_HERMIT_TOAD_3_NAME"] = "Bog Hermit III",
["TOWER_HERMIT_TOAD_4_DESCRIPTION"] = "A little magic, a little brute force, whatever it takes to get rid of pesky intruders.",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_DESCRIPTION"] = "Every %$towers.hermit_toad.power_instakill.cooldown[1]%$ seconds, it uses its tongue to devour an enemy.",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_NAME"] = "Sticky Tongue",
["TOWER_HERMIT_TOAD_4_JUMP_1_DESCRIPTION"] = "Every %$towers.hermit_toad.power_jump.cooldown[1]%$ seconds, the hermit jumps high into the sky, crashing into enemies, dealing %$towers.hermit_toad.power_jump.damage_min[1]%$ damage and stunning them for %$towers.hermit_toad.power_jump.stun_duration[1]%$ second upon landing.",
["TOWER_HERMIT_TOAD_4_JUMP_1_NAME"] = "Ground Pounder",
["TOWER_HERMIT_TOAD_4_NAME"] = "Bog Hermit IV",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_DESCRIPTION"] = "Every %$towers.hermit_toad.power_instakill.cooldown[1]%$ seconds, it uses its tongue to devour an enemy.",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_NAME"] = "Sticky Tongue I",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_NOTE"] = "Sticky business.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_DESCRIPTION"] = "Every %$towers.hermit_toad.power_jump.cooldown[1]%$ seconds, the hermit jumps high into the sky, crashing into enemies, dealing %$towers.hermit_toad.power_jump.damage_min[1]%$ damage and stunning them for %$towers.hermit_toad.power_jump.stun_duration[1]%$ second upon landing.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_NAME"] = "Ground Pounder I",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_DESCRIPTION"] = "Every %$towers.hermit_toad.power_jump.cooldown[2]%$ seconds, the hermit jumps high into the sky, crashing into enemies, dealing %$towers.hermit_toad.power_jump.damage_min[2]%$ damage and stunning them for %$towers.hermit_toad.power_jump.stun_duration[2]%$ second upon landing.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_NAME"] = "Ground Pounder II",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_DESCRIPTION"] = "Every %$towers.hermit_toad.power_jump.cooldown[3]%$ seconds, the hermit jumps high into the sky, crashing into enemies, dealing %$towers.hermit_toad.power_jump.damage_min[3]%$ damage and stunning them for %$towers.hermit_toad.power_jump.stun_duration[3]%$ second upon landing.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_NAME"] = "Ground Pounder III",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_NOTE"] = "Ready for the swamp volleyball team.",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_DESCRIPTION"] = "The hermit changes into a physical stance.",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NAME"] = "Muddy Swamp",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NOTE"] = "Getting dirty!",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_DESCRIPTION"] = "The hermit changes into a magical stance.",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NAME"] = "Magic pond",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NOTE"] = "Unlimited Power!!",
["TOWER_HERMIT_TOAD_DESC"] = "A giant toad mage with a knack for spitting mucus balls. All he wants is some peace and quiet for his pond baths. DO NOT disturb him.",
["TOWER_HERMIT_TOAD_NAME"] = "Bog Hermit",
["TOWER_NECROMANCER_1_DESCRIPTION"] = "With their mastery over death, Necromancers reap the chaos they sow on the battlefield.",
["TOWER_NECROMANCER_1_NAME"] = "Necromancer I",
["TOWER_NECROMANCER_2_DESCRIPTION"] = "With their mastery over death, Necromancers reap the chaos they sow on the battlefield.",
["TOWER_NECROMANCER_2_NAME"] = "Necromancer II",
["TOWER_NECROMANCER_3_DESCRIPTION"] = "With their mastery over death, Necromancers reap the chaos they sow on the battlefield.",
["TOWER_NECROMANCER_3_NAME"] = "Necromancer III",
["TOWER_NECROMANCER_4_DESCRIPTION"] = "With their mastery over death, Necromancers reap the chaos they sow on the battlefield.",
["TOWER_NECROMANCER_4_NAME"] = "Necromancer IV",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_DESCRIPTION"] = "Places a totem that lasts %$towers.necromancer.skill_debuff.aura_duration[1]%$ seconds, cursing enemies and giving skeletons %$towers.necromancer.skill_debuff.s_damage_factor[1]%$% extra attack damage.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_NAME"] = "RATTLING BEACON",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_DESCRIPTION"] = "The totem gives skeletons %$towers.necromancer.skill_debuff.s_damage_factor[2]%$% extra attack damage. Cooldown is reduced to %$towers.necromancer.skill_debuff.cooldown[2]%$ seconds.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_NAME"] = "RATTLING BEACON",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_DESCRIPTION"] = "The totem gives skeletons %$towers.necromancer.skill_debuff.s_damage_factor[3]%$% extra attack damage. Cooldown is reduced to %$towers.necromancer.skill_debuff.cooldown[3]%$ seconds.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_NAME"] = "RATTLING BEACON",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_NOTE"] = "Strong-boned army!",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_DESCRIPTION"] = "Summons a Death Rider on the path that deals %$towers.necromancer.skill_rider.s_damage[1]%$ true damage to any enemy it passes through.",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_NAME"] = "DEATH RIDER",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_DESCRIPTION"] = "The Death Rider deals %$towers.necromancer.skill_rider.s_damage[2]%$ true damage.",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_NAME"] = "DEATH RIDER",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_DESCRIPTION"] = "The Death Rider deals %$towers.necromancer.skill_rider.s_damage[3]%$ true damage.",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_NAME"] = "DEATH RIDER",
["TOWER_NECROMANCER_4_SKILL_RIDER_NOTE"] = "A one way ticket...",
["TOWER_NECROMANCER_DESC"] = "Wielding the darkest form of magic, Necromancers use their enemies as part of the ranks of an endless army.",
["TOWER_NECROMANCER_NAME"] = "Necromancer",
["TOWER_PALADIN_COVENANT_1_DESCRIPTION"] = "Fierce and dedicated, paladins work hard to protect the kingdom from danger.",
["TOWER_PALADIN_COVENANT_1_NAME"] = "Paladin Covenant I",
["TOWER_PALADIN_COVENANT_2_DESCRIPTION"] = "Fierce and dedicated, paladins work hard to protect the kingdom from danger.",
["TOWER_PALADIN_COVENANT_2_NAME"] = "Paladin Covenant II",
["TOWER_PALADIN_COVENANT_3_DESCRIPTION"] = "Fierce and dedicated, paladins work hard to protect the kingdom from danger.",
["TOWER_PALADIN_COVENANT_3_NAME"] = "Paladin Covenant III",
["TOWER_PALADIN_COVENANT_4_DESCRIPTION"] = "Fierce and dedicated, paladins work hard to protect the kingdom from danger.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_DESCRIPTION"] = "When soldiers reach %$towers.paladin_covenant.healing_prayer.health_trigger_factor[1]%$% of their health, they become invincible and restore %$towers.paladin_covenant.healing_prayer.s_healing[1]%$ health per second for %$towers.paladin_covenant.healing_prayer.duration%$ seconds.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_NAME"] = "HEALING PRAYER",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_DESCRIPTION"] = "Healing increases to %$towers.paladin_covenant.healing_prayer.s_healing[2]%$ health per second for %$towers.paladin_covenant.healing_prayer.duration%$ seconds.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_NAME"] = "HEALING PRAYER",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_DESCRIPTION"] = "Healing increases to %$towers.paladin_covenant.healing_prayer.s_healing[3]%$ health per second for %$towers.paladin_covenant.healing_prayer.duration%$ seconds.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_NAME"] = "HEALING PRAYER",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NAME"] = "HEALING PRAYER",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NOTE"] = "Duty unto death.",
["TOWER_PALADIN_COVENANT_4_LEAD_1_DESCRIPTION"] = "Replaces one of the paladins with a Veteran of the Guard, which gives nearby allies a %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$% attack damage boost.",
["TOWER_PALADIN_COVENANT_4_LEAD_1_NAME"] = "LEAD BY EXAMPLE",
["TOWER_PALADIN_COVENANT_4_LEAD_2_DESCRIPTION"] = "Replaces one of the paladins with a Veteran of the Guard, which gives nearby allies a %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$% attack damage boost.",
["TOWER_PALADIN_COVENANT_4_LEAD_2_NAME"] = "LEAD BY EXAMPLE",
["TOWER_PALADIN_COVENANT_4_LEAD_3_DESCRIPTION"] = "Replaces one of the paladins with a Veteran of the Guard, which gives nearby allies a %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$% attack damage boost.",
["TOWER_PALADIN_COVENANT_4_LEAD_3_NAME"] = "LEAD BY EXAMPLE",
["TOWER_PALADIN_COVENANT_4_LEAD_NAME"] = "LEAD BY EXAMPLE",
["TOWER_PALADIN_COVENANT_4_LEAD_NOTE"] = "For the king, for the land, for the mountains.",
["TOWER_PALADIN_COVENANT_4_NAME"] = "Paladin Covenant IV",
["TOWER_PALADIN_COVENANT_DESC"] = "Paladins are the backbone of Linirea's elite forces, using their divine power to protect and heal themselves in battle.",
["TOWER_PALADIN_COVENANT_NAME"] = "Paladin Covenant",
["TOWER_PANDAS_1_DESCRIPTION"] = "Armed with elemental mastery and unwavering determination, The Masters will fight tirelessly to preserve the natural balance of the world.",
["TOWER_PANDAS_1_NAME"] = "Bamboo Masters I",
["TOWER_PANDAS_2_DESCRIPTION"] = "Armed with elemental mastery and unwavering determination, The Masters will fight tirelessly to preserve the natural balance of the world.",
["TOWER_PANDAS_2_NAME"] = "Bamboo Masters II",
["TOWER_PANDAS_3_DESCRIPTION"] = "Armed with elemental mastery and unwavering determination, The Masters will fight tirelessly to preserve the natural balance of the world.",
["TOWER_PANDAS_3_NAME"] = "Bamboo Masters III",
["TOWER_PANDAS_4_DESCRIPTION"] = "Armed with elemental mastery and unwavering determination, The Masters will fight tirelessly to preserve the natural balance of the world.",
["TOWER_PANDAS_4_FIERY"] = "Kawoosh",
["TOWER_PANDAS_4_FIERY_1_DESCRIPTION"] = "Casts a firebolt dealing %$towers.pandas.soldier.teleport.damage_min[1]%$-%$towers.pandas.soldier.teleport.damage_max[1]%$ true damage and teleporting affected enemies back along the path.",
["TOWER_PANDAS_4_FIERY_1_NAME"] = "Nether Flame",
["TOWER_PANDAS_4_FIERY_2_DESCRIPTION"] = "Casts a firebolt dealing %$towers.pandas.soldier.teleport.damage_min[2]%$-%$towers.pandas.soldier.teleport.damage_max[2]%$ true damage and teleporting affected enemies back along the path.",
["TOWER_PANDAS_4_FIERY_2_NAME"] = "Nether Flame",
["TOWER_PANDAS_4_HAT"] = "One hat to hit them all",
["TOWER_PANDAS_4_HAT_1_DESCRIPTION"] = "Launches her sharpened hat at an enemy, ricocheting between foes for %$towers.pandas.soldier.hat.damage_levels[1].min%$-%$towers.pandas.soldier.hat.damage_levels[1].max%$ damage with each hit.",
["TOWER_PANDAS_4_HAT_1_NAME"] = "Hat Trick",
["TOWER_PANDAS_4_HAT_2_DESCRIPTION"] = "Launches her sharpened hat at an enemy, ricocheting between foes for %$towers.pandas.soldier.hat.damage_levels[2].min%$-%$towers.pandas.soldier.hat.damage_levels[2].max%$ damage with each hit.",
["TOWER_PANDAS_4_HAT_2_NAME"] = "Hat Trick",
["TOWER_PANDAS_4_NAME"] = "Bamboo Masters IV",
["TOWER_PANDAS_4_THUNDER"] = "Panda Kombat",
["TOWER_PANDAS_4_THUNDER_1_DESCRIPTION"] = "Calls down lightning strikes on a small area, each dealing %$towers.pandas.soldier.thunder.damage_min[1]%$-%$towers.pandas.soldier.thunder.damage_max[1]%$ area damage and briefly stunning enemies hit.",
["TOWER_PANDAS_4_THUNDER_1_NAME"] = "Lightning Overload",
["TOWER_PANDAS_4_THUNDER_2_DESCRIPTION"] = "Calls down lightning strikes on a small area, each dealing %$towers.pandas.soldier.thunder.damage_min[2]%$-%$towers.pandas.soldier.thunder.damage_max[2]%$ area damage and briefly stunning enemies hit.",
["TOWER_PANDAS_4_THUNDER_2_NAME"] = "Lightning Overload",
["TOWER_PANDAS_DESC"] = "Blending martial prowess with elemental affinity, this panda trio tears through enemies and remains a threat even when appearing to be defeated.",
["TOWER_PANDAS_NAME"] = "Bamboo Masters",
["TOWER_PANDAS_RETREAT_DESCRIPTION"] = "Retreat standing pandas to refuge for 8 seconds.",
["TOWER_PANDAS_RETREAT_NAME"] = "Tactical Retreat",
["TOWER_PANDAS_RETREAT_NOTE"] = "Discretion is the better part of valor",
["TOWER_RAY_1_DESCRIPTION"] = "Dangerous, tainted forms of magic never stopped any evil mages from pursuing nefarious purposes.",
["TOWER_RAY_1_NAME"] = "Eldritch Channeler I",
["TOWER_RAY_2_DESCRIPTION"] = "Dangerous, tainted forms of magic never stopped any evil mages from pursuing nefarious purposes.",
["TOWER_RAY_2_NAME"] = "Eldritch Channeler II",
["TOWER_RAY_3_DESCRIPTION"] = "Dangerous, tainted forms of magic never stopped any evil mages from pursuing nefarious purposes.",
["TOWER_RAY_3_NAME"] = "Eldritch Channeler III",
["TOWER_RAY_4_CHAIN_1_DESCRIPTION"] = "The magic ray now extends over %$towers.ray.skill_chain.s_max_enemies%$ additional enemies slowing them and dealing %$towers.ray.skill_chain.damage_mult[1]%$% of the total magic damage to each target.",
["TOWER_RAY_4_CHAIN_1_NAME"] = "POWER OVERFLOW",
["TOWER_RAY_4_CHAIN_2_DESCRIPTION"] = "The magic ray now extends over %$towers.ray.skill_chain.s_max_enemies%$ additional enemies slowing them and dealing %$towers.ray.skill_chain.damage_mult[2]%$% of the total magic damage to each target.",
["TOWER_RAY_4_CHAIN_2_NAME"] = "POWER OVERFLOW",
["TOWER_RAY_4_CHAIN_3_DESCRIPTION"] = "The magic ray now extends over %$towers.ray.skill_chain.s_max_enemies%$ additional enemies slowing them and dealing %$towers.ray.skill_chain.damage_mult[3]%$% of the total magic damage to each target.",
["TOWER_RAY_4_CHAIN_3_NAME"] = "POWER OVERFLOW",
["TOWER_RAY_4_CHAIN_NOTE"] = "There is enough pain for everyone.",
["TOWER_RAY_4_DESCRIPTION"] = "Dangerous, tainted forms of magic never stopped any evil mages from pursuing nefarious purposes.",
["TOWER_RAY_4_NAME"] = "Eldritch Channeler IV",
["TOWER_RAY_4_SHEEP_1_DESCRIPTION"] = "Turns a nearby enemy into a powerless sheep. The sheep has %$towers.ray.skill_sheep.sheep.hp_mult%$% of the target's health.",
["TOWER_RAY_4_SHEEP_1_NAME"] = "MUTATION HEX",
["TOWER_RAY_4_SHEEP_2_DESCRIPTION"] = "Turns a nearby enemy into a powerless sheep. The sheep has %$towers.ray.skill_sheep.sheep.hp_mult%$% of the target's health.",
["TOWER_RAY_4_SHEEP_2_NAME"] = "MUTATION HEX",
["TOWER_RAY_4_SHEEP_3_DESCRIPTION"] = "Turns a nearby enemy into a powerless sheep. The sheep has %$towers.ray.skill_sheep.sheep.hp_mult%$% of the target's health.",
["TOWER_RAY_4_SHEEP_3_NAME"] = "MUTATION HEX",
["TOWER_RAY_4_SHEEP_NOTE"] = "Honestly, you look better now.",
["TOWER_RAY_DESC"] = "Vez'nan's apprentices use their corrupt power to cast a dark ray of affliction over their enemies.",
["TOWER_RAY_NAME"] = "Eldritch Channeler",
["TOWER_ROCKET_GUNNERS_1_DESCRIPTION"] = "Strapped in with the latest Dark Army technology, the gunners patrol the skies.",
["TOWER_ROCKET_GUNNERS_1_NAME"] = "Rocket Gunners I",
["TOWER_ROCKET_GUNNERS_2_DESCRIPTION"] = "Strapped in with the latest Dark Army technology, the gunners patrol the skies.",
["TOWER_ROCKET_GUNNERS_2_NAME"] = "Rocket Gunners II",
["TOWER_ROCKET_GUNNERS_3_DESCRIPTION"] = "Strapped in with the latest Dark Army technology, the gunners patrol the skies.",
["TOWER_ROCKET_GUNNERS_3_NAME"] = "Rocket Gunners III",
["TOWER_ROCKET_GUNNERS_4_DESCRIPTION"] = "Strapped in with the latest Dark Army technology, the gunners patrol the skies.",
["TOWER_ROCKET_GUNNERS_4_NAME"] = "Rocket Gunners IV",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_DESCRIPTION"] = "Each attack destroys %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[1]%$% of enemy armor and deals area damage.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_NAME"] = "PHOSPHORIC COATING",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_DESCRIPTION"] = "Each attack destroys %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[2]%$% of enemy armor and deals %$towers.rocket_gunners.soldier.phosphoric.damage_area_min[2]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[2]%$ area damage.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_NAME"] = "PHOSPHORIC COATING",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_DESCRIPTION"] = "Each attack destroys %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[3]%$% of enemy armor and deals %$towers.rocket_gunners.soldier.phosphoric.damage_area_min[3]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[3]%$ area damage.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_NAME"] = "PHOSPHORIC COATING",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_NOTE"] = "Evily seasoned bullets.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_DESCRIPTION"] = "Fires a missile that instantly kills a target of up to %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[1]%$ health.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_NAME"] = "STING MISSILES",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_DESCRIPTION"] = "Decreases the cooldown to %$towers.rocket_gunners.sting_missiles.cooldown[2]%$ seconds. Now it can target enemies of up to %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[2]%$ health. ",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_NAME"] = "STING MISSILES",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_DESCRIPTION"] = "Decreases the cooldown to %$towers.rocket_gunners.sting_missiles.cooldown[3]%$ seconds. Now it can target enemies of up to %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[3]%$ health. ",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_NAME"] = "STING MISSILES",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_NOTE"] = "Dodge this!",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_DESCRIPTION"] = "Rocket Gunners take off and cannot block enemies.",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NAME"] = "Takeoff",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NOTE"] = "To infinity and beyond!",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_DESCRIPTION"] = "Rocket Gunners land on the ground and can block enemies.",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NAME"] = "Touch Down",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NOTE"] = "The eagle has landed!",
["TOWER_ROCKET_GUNNERS_DESC"] = "These special troops can hold their own both on ground and air, unleashing their advanced weaponry upon unsuspecting enemies.",
["TOWER_ROCKET_GUNNERS_NAME"] = "Rocket Gunners",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "This tower is included in the Colossal Dwarfare Campaign.",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "This tower is included in the Wukong's Journey Campaign.",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "Colossal Dwarfare Campaign",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "Wukong's Journey Campaign",
["TOWER_ROOM_EQUIPPED_TOWERS_TITLE"] = "Equipped towers",
["TOWER_ROOM_GET_DLC"] = "GET IT",
["TOWER_ROOM_LABEL_ROSTER_THUMB_NEW"] = "New!",
["TOWER_ROOM_SKILLS_TITLE"] = "Skills",
["TOWER_ROYAL_ARCHERS_1_DESCRIPTION"] = "Loyal until the very end, the Royal Archers protect Linirean forces from afar.",
["TOWER_ROYAL_ARCHERS_1_NAME"] = "Royal Archers I",
["TOWER_ROYAL_ARCHERS_2_DESCRIPTION"] = "Loyal until the very end, the Royal Archers protect Linirean forces from afar.",
["TOWER_ROYAL_ARCHERS_2_NAME"] = "Royal Archers II",
["TOWER_ROYAL_ARCHERS_3_DESCRIPTION"] = "Loyal until the very end, the Royal Archers protect Linirean forces from afar.",
["TOWER_ROYAL_ARCHERS_3_NAME"] = "Royal Archers III",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_DESCRIPTION"] = "Shoots three empowered arrows that deal %$towers.royal_archers.armor_piercer.damage_min[1]%$-%$towers.royal_archers.armor_piercer.damage_max[1]%$ physical damage, ignoring %$towers.royal_archers.armor_piercer.armor_penetration[1]%$% of enemy armor.",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_NAME"] = "ARMOR PIERCER",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_DESCRIPTION"] = "Shoots three empowered arrows that deal %$towers.royal_archers.armor_piercer.damage_min[2]%$-%$towers.royal_archers.armor_piercer.damage_max[2]%$ physical damage, ignoring %$towers.royal_archers.armor_piercer.armor_penetration[2]%$% of enemy armor.",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_NAME"] = "ARMOR PIERCER",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_DESCRIPTION"] = "Shoots three empowered arrows that deal %$towers.royal_archers.armor_piercer.damage_min[3]%$-%$towers.royal_archers.armor_piercer.damage_max[3]%$ physical damage, ignoring %$towers.royal_archers.armor_piercer.armor_penetration[3]%$% of enemy armor.",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_NAME"] = "ARMOR PIERCER",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NAME"] = "ARMOR PIERCER",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NOTE"] = "We've got you in our sights.",
["TOWER_ROYAL_ARCHERS_4_DESCRIPTION"] = "Loyal until the very end, the Royal Archers protect Linirean forces from afar.",
["TOWER_ROYAL_ARCHERS_4_NAME"] = "Royal Archers IV",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_DESCRIPTION"] = "Summons an eagle that attacks enemies on the path, dealing %$towers.royal_archers.rapacious_hunter.damage_min[1]%$-%$towers.royal_archers.rapacious_hunter.damage_max[1]%$ physical damage.",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_NAME"] = "RAPACIOUS HUNTER",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_DESCRIPTION"] = "The eagle deals %$towers.royal_archers.rapacious_hunter.damage_min[2]%$-%$towers.royal_archers.rapacious_hunter.damage_max[2]%$ physical damage.",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_NAME"] = "RAPACIOUS HUNTER",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_DESCRIPTION"] = "The eagle deals %$towers.royal_archers.rapacious_hunter.damage_min[3]%$-%$towers.royal_archers.rapacious_hunter.damage_max[3]%$ physical damage.",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_NAME"] = "RAPACIOUS HUNTER",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NAME"] = "RAPACIOUS HUNTER",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NOTE"] = "The eagle's eye is hiding something tragic.",
["TOWER_ROYAL_ARCHERS_DESC"] = "The mightiest marksmen of the kingdom, they are also renowned for being assisted by war eagles.",
["TOWER_ROYAL_ARCHERS_NAME"] = "Royal Archers",
["TOWER_SAND_1_DESCRIPTION"] = "Their skill with the thrown blade is enough to frighten any mercenary too full of themselves.",
["TOWER_SAND_1_NAME"] = "Dune Sentinels I",
["TOWER_SAND_2_DESCRIPTION"] = "Their skill with the thrown blade is enough to frighten any mercenary too full of themselves.",
["TOWER_SAND_2_NAME"] = "Dune Sentinels II",
["TOWER_SAND_3_DESCRIPTION"] = "Their skill with the thrown blade is enough to frighten any mercenary too full of themselves.",
["TOWER_SAND_3_NAME"] = "Dune Sentinels III",
["TOWER_SAND_4_DESCRIPTION"] = "Their skill with the thrown blade is enough to frighten any mercenary too full of themselves.",
["TOWER_SAND_4_NAME"] = "Dune Sentinels IV",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_DESCRIPTION"] = "Launches whirling blades to the path that deal %$towers.sand.skill_big_blade.s_damage_min[1]%$-%$towers.sand.skill_big_blade.s_damage_max[1]%$ physical damage per second for %$towers.sand.skill_big_blade.duration[1]%$ seconds.",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_NAME"] = "WHIRLING DOOM",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_DESCRIPTION"] = "The whirling blades deal %$towers.sand.skill_big_blade.s_damage_min[2]%$-%$towers.sand.skill_big_blade.s_damage_max[2]%$ physical damage per second for %$towers.sand.skill_big_blade.duration[2]%$ seconds.",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_NAME"] = "WHIRLING DOOM",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_DESCRIPTION"] = "The whirling blades deal %$towers.sand.skill_big_blade.s_damage_min[3]%$-%$towers.sand.skill_big_blade.s_damage_max[3]%$ physical damage per second for %$towers.sand.skill_big_blade.duration[3]%$ seconds.",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_NAME"] = "WHIRLING DOOM",
["TOWER_SAND_4_SKILL_BIG_BLADE_NOTE"] = "You spin me round, round, baby.",
["TOWER_SAND_4_SKILL_GOLD_1_DESCRIPTION"] = "Throws a bouncing blade that deals %$towers.sand.skill_gold.s_damage[1]%$ physical damage to target enemies. Any target killed by the blade yields %$towers.sand.skill_gold.gold_extra[1]%$ bonus gold.",
["TOWER_SAND_4_SKILL_GOLD_1_NAME"] = "BOUNTY HUNT",
["TOWER_SAND_4_SKILL_GOLD_2_DESCRIPTION"] = "The blade deals %$towers.sand.skill_gold.s_damage[2]%$ physical damage. A kill grants %$towers.sand.skill_gold.gold_extra[2]%$ bonus gold.",
["TOWER_SAND_4_SKILL_GOLD_2_NAME"] = "BOUNTY HUNT",
["TOWER_SAND_4_SKILL_GOLD_3_DESCRIPTION"] = "The blade deals %$towers.sand.skill_gold.s_damage[3]%$ physical damage. A kill grants %$towers.sand.skill_gold.gold_extra[3]%$ bonus gold.",
["TOWER_SAND_4_SKILL_GOLD_3_NAME"] = "BOUNTY HUNT",
["TOWER_SAND_4_SKILL_GOLD_NOTE"] = "The leaflet says dead OR alive.",
["TOWER_SAND_DESC"] = "Hailing from Hammerhold, the Dune Sentinels might be the deadliest of the desert's denizens.",
["TOWER_SAND_NAME"] = "Dune Sentinels",
["TOWER_SELL"] = "Sell Tower",
["TOWER_SPARKING_GEODE_1_DESCRIPTION"] = "Summoner of storms and certified chaos bringer. Be careful with its power consumption.",
["TOWER_SPARKING_GEODE_1_NAME"] = "Surge Colossus I",
["TOWER_SPARKING_GEODE_2_DESCRIPTION"] = "Summoner of storms and certified chaos bringer. Be careful with its power consumption.",
["TOWER_SPARKING_GEODE_2_NAME"] = "Surge Colossus II",
["TOWER_SPARKING_GEODE_3_DESCRIPTION"] = "Summoner of storms and certified chaos bringer. Be careful with its power consumption.",
["TOWER_SPARKING_GEODE_3_NAME"] = "Surge Colossus III",
["TOWER_SPARKING_GEODE_4_CRISTALIZE"] = "Thunderbolt!",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_DESCRIPTION"] = "Every %$towers.sparking_geode.crystalize.cooldown[1]%$ seconds, it crystallizes %$towers.sparking_geode.crystalize.max_targets[1]%$ enemies within range, stunning them and causing them to take %$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$% more damage.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_NAME"] = "Crystallization",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_DESCRIPTION"] = "Every %$towers.sparking_geode.crystalize.cooldown[2]%$ seconds, it crystallizes %$towers.sparking_geode.crystalize.max_targets[2]%$ enemies within range, stunning them and causing them to take %$towers.sparking_geode.crystalize.s_received_damage_factor[2]%$% more damage.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_NAME"] = "Crystallization",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_DESCRIPTION"] = "Every %$towers.sparking_geode.crystalize.cooldown[3]%$ seconds, it crystallizes %$towers.sparking_geode.crystalize.max_targets[3]%$ enemies within range, stunning them and causing them to take %$towers.sparking_geode.crystalize.s_received_damage_factor[3]%$% more damage.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_NAME"] = "Crystallization",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_DESCRIPTION"] = "Every %$towers.sparking_geode.crystalize.cooldown[1]%$ seconds, it crystallizes %$towers.sparking_geode.crystalize.max_targets[1]%$ enemies within range, stunning them and causing them to take %$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$% more damage.",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_NAME"] = "Crystallization",
["TOWER_SPARKING_GEODE_4_DESCRIPTION"] = "Summoner of storms and certified chaos bringer. Be careful with its power consumption.",
["TOWER_SPARKING_GEODE_4_NAME"] = "Surge Colossus IV",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST"] = "Harder, Better, Faster, Stronger.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_DESCRIPTION"] = "Every %$towers.sparking_geode.spike_burst.cooldown[1]%$ seconds, the Colossus summons an electric field that damages and slows nearby enemies for %$towers.sparking_geode.spike_burst.duration[1]%$ seconds.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_NAME"] = "Electrical Surge",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_DESCRIPTION"] = "Every %$towers.sparking_geode.spike_burst.cooldown[2]%$ seconds, the Colossus summons an electric field that damages and slows nearby enemies for %$towers.sparking_geode.spike_burst.duration[2]%$ seconds.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_NAME"] = "Electrical Surge",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_DESCRIPTION"] = "Every %$towers.sparking_geode.spike_burst.cooldown[3]%$ seconds, the Colossus summons an electric field that damages and slows nearby enemies for %$towers.sparking_geode.spike_burst.duration[3]%$ seconds.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_NAME"] = "Electrical Surge",
["TOWER_SPARKING_GEODE_DESC"] = "Originary from a peaceful ancient race, this mighty being follows his protective instinct and uses his lightning powers to fight for the Alliance, striking with the fury of a tempest.",
["TOWER_SPARKING_GEODE_NAME"] = "Surge Colossus",
["TOWER_STAGE_13_SUNRAY_NAME"] = "Darkray Tower",
["TOWER_STAGE_13_SUNRAY_REPAIR_DESCRIPTION"] = "Repair the tower to use its destructive powers.",
["TOWER_STAGE_13_SUNRAY_REPAIR_NAME"] = "Repair",
["TOWER_STAGE_17_WEIRDWOOD_NAME"] = "Weirdwood",
["TOWER_STAGE_18_ELVEN_BARRACK_DESCRIPTION"] = "Hired elves to fight until the end.",
["TOWER_STAGE_18_ELVEN_BARRACK_NAME"] = "Elven Mercenaries",
["TOWER_STAGE_20_ARBOREAN_BARRACK_DESCRIPTION"] = "Call the Arborean people to fight.",
["TOWER_STAGE_20_ARBOREAN_BARRACK_NAME"] = "Arborean citizens",
["TOWER_STAGE_20_ARBOREAN_HONEY_DESCRIPTION"] = "Summon the great commander of the bees.",
["TOWER_STAGE_20_ARBOREAN_HONEY_NAME"] = "Arborean Beekeeper",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_DESCRIPTION"] = "Ask the ancient tree for help.",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_NAME"] = "Old Tree",
["TOWER_STAGE_22_ARBOREAN_MAGES_NAME"] = "Arborean Mage",
["TOWER_STAGE_28_PRIESTS_BARRACK_DESCRIPTION"] = "Redeemed cultists that bring their sorcery to the battlefield and turn into abominations when they die.",
["TOWER_STAGE_28_PRIESTS_BARRACK_NAME"] = "Believers of the Eyeless",
["TOWER_STARGAZER_1_DESCRIPTION"] = "Stargazers look to harness powerful magic from beyond the earthly realm.",
["TOWER_STARGAZER_1_NAME"] = "Elven Stargazer I",
["TOWER_STARGAZER_2_DESCRIPTION"] = "Stargazers look to harness powerful magic from beyond the earthly realm.",
["TOWER_STARGAZER_2_NAME"] = "Elven Stargazer II",
["TOWER_STARGAZER_3_DESCRIPTION"] = "Stargazers look to harness powerful magic from beyond the earthly realm.",
["TOWER_STARGAZER_3_NAME"] = "Elven Stargazer III",
["TOWER_STARGAZER_4_DESCRIPTION"] = "Stargazers look to harness powerful magic from beyond the earthly realm.",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_DESCRIPTION"] = "Teleports up to %$towers.elven_stargazers.teleport.max_targets[1]%$ enemies back down the path.",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_NAME"] = "EVENT HORIZON",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_DESCRIPTION"] = "Teleports up to %$towers.elven_stargazers.teleport.max_targets[2]%$ enemies further back down the path.",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_NAME"] = "EVENT HORIZON",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_DESCRIPTION"] = "Teleports up to %$towers.elven_stargazers.teleport.max_targets[3]%$ enemies even further back down the path.",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_NAME"] = "EVENT HORIZON",
["TOWER_STARGAZER_4_EVENT_HORIZON_NAME"] = "EVENT HORIZON",
["TOWER_STARGAZER_4_EVENT_HORIZON_NOTE"] = "Phase out, phase in.",
["TOWER_STARGAZER_4_NAME"] = "Elven Stargazer IV",
["TOWER_STARGAZER_4_RISING_STAR_1_DESCRIPTION"] = "Enemies killed by the tower explode in a burst of %$towers.elven_stargazers.stars_death.stars[1]%$ stars that deal %$towers.elven_stargazers.stars_death.damage_min[1]%$-%$towers.elven_stargazers.stars_death.damage_max[1]%$ magic damage to enemies.",
["TOWER_STARGAZER_4_RISING_STAR_1_NAME"] = "RISING STAR",
["TOWER_STARGAZER_4_RISING_STAR_2_DESCRIPTION"] = "Star quantity increases to %$towers.elven_stargazers.stars_death.stars[2]%$. Stars deal %$towers.elven_stargazers.stars_death.damage_min[2]%$-%$towers.elven_stargazers.stars_death.damage_max[2]%$ magic damage.",
["TOWER_STARGAZER_4_RISING_STAR_2_NAME"] = "RISING STAR",
["TOWER_STARGAZER_4_RISING_STAR_3_DESCRIPTION"] = "Star quantity increases to %$towers.elven_stargazers.stars_death.stars[3]%$. Stars deal %$towers.elven_stargazers.stars_death.damage_min[3]%$-%$towers.elven_stargazers.stars_death.damage_max[3]%$ magic damage.",
["TOWER_STARGAZER_4_RISING_STAR_3_NAME"] = "RISING STAR",
["TOWER_STARGAZER_4_RISING_STAR_NAME"] = "RISING STAR",
["TOWER_STARGAZER_4_RISING_STAR_NOTE"] = "It's a stardust revolution!",
["TOWER_TRICANNON_1_DESCRIPTION"] = "A devastating love song to warfare and a fearful sight for enemies and allies alike.",
["TOWER_TRICANNON_1_NAME"] = "Tricannon I",
["TOWER_TRICANNON_2_DESCRIPTION"] = "A devastating love song to warfare and a fearful sight for enemies and allies alike.",
["TOWER_TRICANNON_2_NAME"] = "Tricannon II",
["TOWER_TRICANNON_3_DESCRIPTION"] = "A devastating love song to warfare and a fearful sight for enemies and allies alike.",
["TOWER_TRICANNON_3_NAME"] = "Tricannon III",
["TOWER_TRICANNON_4_BOMBARDMENT_1_DESCRIPTION"] = "Shoots bombs rapidly in a wide area, each dealing %$towers.tricannon.bombardment.damage_min[1]%$-%$towers.tricannon.bombardment.damage_max[1]%$ physical damage.",
["TOWER_TRICANNON_4_BOMBARDMENT_1_NAME"] = "BOMBARDMENT",
["TOWER_TRICANNON_4_BOMBARDMENT_2_DESCRIPTION"] = "Shoots more bombs in a wider area, each dealing %$towers.tricannon.bombardment.damage_min[2]%$-%$towers.tricannon.bombardment.damage_max[2]%$ physical damage.",
["TOWER_TRICANNON_4_BOMBARDMENT_2_NAME"] = "BOMBARDMENT",
["TOWER_TRICANNON_4_BOMBARDMENT_3_DESCRIPTION"] = "Shoots even more bombs in a wider area, each dealing %$towers.tricannon.bombardment.damage_min[3]%$-%$towers.tricannon.bombardment.damage_max[3]%$ physical damage.",
["TOWER_TRICANNON_4_BOMBARDMENT_3_NAME"] = "BOMBARDMENT",
["TOWER_TRICANNON_4_BOMBARDMENT_NAME"] = "BOMBARDMENT",
["TOWER_TRICANNON_4_BOMBARDMENT_NOTE"] = "Talk about scalability.",
["TOWER_TRICANNON_4_DESCRIPTION"] = "A devastating love song to warfare and a fearful sight for enemies and allies alike.",
["TOWER_TRICANNON_4_NAME"] = "Tricannon IV",
["TOWER_TRICANNON_4_OVERHEAT_1_DESCRIPTION"] = "The Tricannon's barrels become red hot for %$towers.tricannon.overheat.duration[1]%$ seconds, making bombs scorch the ground to deal %$towers.tricannon.overheat.decal.effect.s_damage[1]%$ true damage per second to enemies.",
["TOWER_TRICANNON_4_OVERHEAT_1_NAME"] = "OVERHEAT",
["TOWER_TRICANNON_4_OVERHEAT_2_DESCRIPTION"] = "Each scorching area deals %$towers.tricannon.overheat.decal.effect.s_damage[2]%$ true damage per second. Duration is increased to %$towers.tricannon.overheat.duration[2]%$ seconds.",
["TOWER_TRICANNON_4_OVERHEAT_2_NAME"] = "OVERHEAT",
["TOWER_TRICANNON_4_OVERHEAT_3_DESCRIPTION"] = "Each scorching area deals %$towers.tricannon.overheat.decal.effect.s_damage[3]%$ true damage per second. Duration is increased to %$towers.tricannon.overheat.duration[3]%$ seconds.",
["TOWER_TRICANNON_4_OVERHEAT_3_NAME"] = "OVERHEAT",
["TOWER_TRICANNON_4_OVERHEAT_NAME"] = "OVERHEAT",
["TOWER_TRICANNON_4_OVERHEAT_NOTE"] = "We are red hot.",
["TOWER_TRICANNON_DESC"] = "The Dark Army's newest definition for modern warfare rains fire and destruction thanks to its multiple cannons.",
["TOWER_TRICANNON_NAME"] = "Tricannon",
["TUTORIAL_hero_room_hero_points_desc"] = "Earn Hero Points by leveling up each hero in combat.",
["TUTORIAL_hero_room_hero_points_title"] = "Hero Points",
["TUTORIAL_hero_room_power_desc"] = "Use Hero Points to buy and improve powers for your hero.",
["TUTORIAL_hero_room_power_title"] = "Hero Powers",
["TUTORIAL_hero_room_tutorial_navigate_desc"] = "Navigate through different heroes.",
["TUTORIAL_hero_room_tutorial_select_desc"] = "Equip the heroes you want to use in the battlefield.",
["TUTORIAL_item_room_buy_desc"] = "Use your Gems to buy items to aid you in the battlefield.",
["TUTORIAL_item_room_buy_title"] = "Buying Items",
["TUTORIAL_item_room_tutorial_equip_desc"] = "Use each slot to equip your items. Drag to switch their order!",
["TUTORIAL_item_room_tutorial_navigate_desc"] = "Navigate through the various available items.",
["TUTORIAL_tower_room_power_desc"] = "These skills are available once the tower reaches level IV.",
["TUTORIAL_tower_room_power_title"] = "Level IV skills",
["TUTORIAL_tower_room_tutorial_equip_desc"] = "Equip new towers to try out different combinations.",
["TUTORIAL_tower_room_tutorial_navigate_desc"] = "Navigate through the different towers.",
["TUTORIAL_tower_room_tutorial_slots_desc"] = "Use each slot to equip your towers. Drag to switch their order!",
["TUTORIAL_upgrade_room_tooltip_buy_desc"] = "Use Points to purchase upgrades for your powers, towers and heroes.",
["TUTORIAL_upgrade_room_tooltip_souls_desc"] = "Earn Upgrade Points by completing campaign stages.",
["TUTORIAL_upgrade_room_tooltip_souls_title"] = "Upgrade Points",
["Tap the road!"] = "Tap the road!",
["Tip"] = "Tip",
["Tower construction"] = "Tower construction",
["Towers"] = "Towers",
["Try again"] = "Try again",
["Typography"] = "Typography",
["UPDATE_POPUP"] = "UPDATE",
["UPDATING_CLOUDSAVE_MESSAGE"] = "Updating cloud save...",
["UPGRADES"] = "UPGRADES",
["UPGRADES AND HEROES RESTRICTIONS!"] = "UPGRADES AND HEROES RESTRICTIONS!",
["UPGRADE_LEVEL"] = "upgrade level",
["Undo"] = "Undo",
["Unlocks at Level"] = "Unlocks at Level",
["Upgrades"] = "Upgrades",
["Use the earned hero points to train your hero!"] = "Use the earned hero points to train your hero!",
["Use the earned stars to improve your towers and powers!"] = "Use the earned stars to improve your towers and powers!",
["VICTORY"] = "VICTORY",
["Very fast"] = "Very fast",
["Very slow"] = "Very slow",
["Veteran"] = "Veteran",
["Victory!"] = "Victory!",
["Voice Talent"] = "Voice Talent",
["WARNING"] = "WARNING",
["WAVE_TOOLTIP_TAP_AGAIN"] = "CLICK TO CALL IT EARLY",
["WAVE_TOOLTIP_TITLE"] = "INCOMING WAVE",
["We would like to thank"] = "Special Thanks",
["Yes"] = "Yes",
["You can always change the difficulty in the options menu."] = "You can always change the difficulty in the options menu.",
["_manually_included_characters"] = "$ ¥ ￥ ƒ ₩ € ™ × $ zł ¢ £ ¤ ¥ ƒ ден дин лв. ؋ ৳ ฿ ლ ₡ ₣ ₤ ₥ ₦ ₨ ₩ ₪ ₫ € ₭ ₮ ₱ ₲ ₴ ₵ ₹ ₺ ₽ ﷼ ",
["alliance_close_to_home_DESCRIPTION"] = "Grants extra gold at the start of the stage.",
["alliance_close_to_home_NAME"] = "SHARED RESERVES",
["alliance_corageous_stand_DESCRIPTION"] = "Each LINIREAN tower built increases the heroes' health points.",
["alliance_corageous_stand_NAME"] = "COURAGEOUS STAND",
["alliance_display_of_true_might_dark_DESCRIPTION"] = "Dark Army Hero Spells now also slow all enemies on the screen. ",
["alliance_display_of_true_might_dark_NAME"] = "OMINOUS CURSE",
["alliance_display_of_true_might_linirea_DESCRIPTION"] = "Linirean Hero Spells now also heal and respawn all tower units.",
["alliance_display_of_true_might_linirea_NAME"] = "BLESSING OF VITALITY",
["alliance_flux_altering_coils_DESCRIPTION"] = "Replaces all exit flags with arcane pillars that teleport back nearby enemies.",
["alliance_flux_altering_coils_NAME"] = "ARCANE PILLARS",
["alliance_friends_of_the_crown_DESCRIPTION"] = "Each equipped LINIREAN hero reduces the cost of building and upgrading towers.",
["alliance_friends_of_the_crown_NAME"] = "FRIENDS OF THE CROWN",
["alliance_merciless_DESCRIPTION"] = "Each DARK ARMY tower built increases the heroes' attack damage.",
["alliance_merciless_NAME"] = "MERCILESS DEFENSE",
["alliance_seal_of_punishment_DESCRIPTION"] = "Replaces the defense point with a magic seal that damages enemies that walk over it.",
["alliance_seal_of_punishment_NAME"] = "SEAL OF PUNISHMENT",
["alliance_shady_company_DESCRIPTION"] = "Each equipped DARK ARMY hero increases the towers' attack damage.",
["alliance_shady_company_NAME"] = "SHADY COMPANY",
["alliance_shared_reserves_DESCRIPTION"] = "Grants extra gold at the start of the stage.",
["alliance_shared_reserves_NAME"] = "SHARED RESERVES",
["build defensive towers along the road to stop them."] = "build defensive towers along the road to stop them.",
["build towers to defend the road."] = "build towers to defend the road.",
["check the stage description to see:"] = "check the stage description to see:",
["click these!"] = "click these!",
["click to continue..."] = "click to continue...",
["deals area damage"] = "deals area damage",
["don't let enemies past this point."] = "don't let enemies past this point.",
["earn gold by killing enemies."] = "earn gold by killing enemies.",
["good rate of fire"] = "good rate of fire",
["heroes_desperate_effort_DESCRIPTION"] = "Heroes' attacks ignore 10% of the enemies' resistance.",
["heroes_desperate_effort_NAME"] = "KNOW THY ENEMY",
["heroes_lethal_focus_DESCRIPTION"] = "Heroes deal critical damage in 20% of their attacks.",
["heroes_lethal_focus_NAME"] = "LETHAL FOCUS",
["heroes_limit_pushing_DESCRIPTION"] = "After using each Hero Spell five times its cooldown will be instantly reset.",
["heroes_limit_pushing_NAME"] = "LIMIT PUSHING",
["heroes_lone_wolves_DESCRIPTION"] = "Heroes earn more experience when they are away from each other.",
["heroes_lone_wolves_NAME"] = "LONE WOLVES",
["heroes_nimble_physique_DESCRIPTION"] = "Heroes dodge 20% of enemy attacks.",
["heroes_nimble_physique_NAME"] = "NIMBLE PHYSIQUE",
["heroes_unlimited_vigor_DESCRIPTION"] = "Reduces all Hero Spell cooldowns by 10%.",
["heroes_unlimited_vigor_NAME"] = "UNLIMITED VIGOR",
["heroes_visual_learning_DESCRIPTION"] = "Heroes have 10% extra armor when standing near each other.",
["heroes_visual_learning_NAME"] = "HELPING HAND",
["high damage, armor piercing"] = "high damage, armor piercing",
["iron and heroic challenges may have restrictions on upgrades!"] = "iron and heroic challenges may have restrictions on upgrades!",
["max lvl allowed"] = "max lvl allowed",
["multi-shot, armor piercing"] = "multi-shot, armor piercing",
["no heroes"] = "no heroes",
["protect your lands from the enemy attacks."] = "protect your lands from the enemy attacks.",
["rally range"] = "rally range",
["ready for action!"] = "ready for action!",
["reinforcements_intense_workout_DESCRIPTION"] = "Improves the Reinforcements' health and duration.",
["reinforcements_intense_workout_NAME"] = "INTENSE WORKOUT",
["reinforcements_master_blacksmiths_DESCRIPTION"] = "Improves the Reinforcements' attack damage and armor.",
["reinforcements_master_blacksmiths_NAME"] = "MASTER BLACKSMITHS",
["reinforcements_night_veil_DESCRIPTION"] = "Shadow Archers have increased range and attack speed.",
["reinforcements_night_veil_NAME"] = "ASHEN BOWS",
["reinforcements_power_trio_DESCRIPTION"] = "Calling the Reinforcements now also summons a Paragon Knight.",
["reinforcements_power_trio_NAME"] = "LINIREAN PARAGON",
["reinforcements_power_trio_dark_DESCRIPTION"] = "Calling the Reinforcements also summons a Shadow Crowcaller.",
["reinforcements_power_trio_dark_NAME"] = "SHADOW CROWCALLER",
["reinforcements_rebel_militia_DESCRIPTION"] = "Reinforcements are replaced by Linirean Rebels, durable fighters wearing great sets of armor.",
["reinforcements_rebel_militia_NAME"] = "LINIREAN MILITIA",
["reinforcements_shadow_archer_DESCRIPTION"] = "Reinforcements are replaced by Shadow Archers, attacking from afar and targeting flying units.",
["reinforcements_shadow_archer_NAME"] = "ORDER OF SHADOWS",
["reinforcements_thorny_armor_DESCRIPTION"] = "Linirean Rebels reflect some damage from enemy melee attacks.",
["reinforcements_thorny_armor_NAME"] = "SPIKED ARMOR",
["resist damage from"] = "resists damage from",
["resists damage from"] = "resists \ndamage from",
["select the rally point control"] = "select the rally point control",
["select the tower you want to build!"] = "select the tower you want to build!",
["select where you want to move your soldiers"] = "select where you want to move your soldiers",
["soldiers block enemies"] = "soldiers block enemies",
["some enemies enjoy different levels of magic resistance that protects them against magical attacks."] = "some enemies enjoy different levels of magic resistance that protects them against magical attacks.",
["some enemies wear armor of different strengths that protects them against non-magical attacks."] = "some enemies wear armor of different strengths that protects them against non-magical attacks.",
["tap these!"] = "tap these!",
["this is a strategic point."] = "this is a strategic point.",
["towers_favorite_customer_DESCRIPTION"] = "When buying the final level of a skill, reduce its cost by 50%.",
["towers_favorite_customer_NAME"] = "FAVORITE CUSTOMER",
["towers_golden_time_DESCRIPTION"] = "Increases the bonus gold for calling a wave early.",
["towers_golden_time_NAME"] = "GOLDEN TIME",
["towers_improved_formulas_DESCRIPTION"] = "Maximizes the damage of ALL explosions from towers and increases their area of effect.",
["towers_improved_formulas_NAME"] = "IMPROVED FORMULAS",
["towers_keen_accuracy_DESCRIPTION"] = "Reduces the cooldown of ALL tower skills by 20%.",
["towers_keen_accuracy_NAME"] = "BATTLE FERVOR",
["towers_royal_training_DESCRIPTION"] = "Reduces tower units' spawn time and Reinforcements' cooldown.",
["towers_royal_training_NAME"] = "CALL TO ACTION",
["towers_scoping_mechanism_DESCRIPTION"] = "Increases the attack range of ALL towers by 10%.",
["towers_scoping_mechanism_NAME"] = "SCOPING MECHANISM",
["towers_war_rations_DESCRIPTION"] = "Increases the health of ALL tower units by 10%.",
["towers_war_rations_NAME"] = "WAR RATIONS",
["towers_wise_investment_DESCRIPTION"] = "Towers now reimburse 90% of their cost when sold.",
["towers_wise_investment_NAME"] = "WISE INVESTMENT",
["wOOt!"] = "wOOt!",
["you can adjust your soldiers rally point to make them defend a different area."] = "you can adjust your soldiers' rally point to make them defend a different area.",
}
