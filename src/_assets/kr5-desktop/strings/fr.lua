-- ------------------------------------------------
-- -- WARNING: DO NOT EDIT BY HAND                 
-- -- Generated by kr-i18n/tools/strings-export.lua
-- ------------------------------------------------
return {
["!!!COMMENT_LOCALIZATION_SOURCE"] = "Keywords",
["%d Life"] = "%d Vie",
["%d Lives"] = "%d Vies",
["%i sec."] = "%i sec.",
["- if heroes are allowed"] = "- si les héros sont autorisés",
["- max lvl allowed"] = "- niveau max autorisé",
["- max upgrade level allowed"] = "- niveau d'amélioration max autorisé",
["- no heroes"] = "- aucun héros",
["A good challenge!"] = "Un bon défi!",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "Willy Abominé",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "Henry Abominé",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "Geoffrey Abominé",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "Nicholas Constipé",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "Édomination",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "Hobomination",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "Odomination",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "Cedric Abominé",
["ABOMINATED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "Halbomination",
["ACHIEVEMENT"] = "RÉALISATION",
["ACHIEVEMENTS"] = "RÉALISATION",
["ACHIEVEMENTS_TITLE"] = "SUCCÈS",
["ACHIEVEMENT_AGE_OF_HEROES_DESCRIPTION"] = "Remportez tous les défis de la campagne en Mode Héroïque.",
["ACHIEVEMENT_AGE_OF_HEROES_NAME"] = "Âge des Héros",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_DESCRIPTION"] = "Éliminez 182 Clignotants.",
["ACHIEVEMENT_ALL_THE_SMALL_THINGS_NAME"] = "Toutes les Petites Choses",
["ACHIEVEMENT_ARACHNED_DESCRIPTION"] = "Vaincs Mygale, la Reine Araignée.",
["ACHIEVEMENT_ARACHNED_NAME"] = "Adieu aux armes",
["ACHIEVEMENT_A_COON_OF_SURPRISES_DESCRIPTION"] = "Aide Fredo à s’échapper.",
["ACHIEVEMENT_A_COON_OF_SURPRISES_NAME"] = "Un cocon de surprises",
["ACHIEVEMENT_A_TEST_OF_PROWESS_DESCRIPTION"] = "Gagnez une étape avec 3 étoiles.",
["ACHIEVEMENT_A_TEST_OF_PROWESS_NAME"] = "Un test de prouesse",
["ACHIEVEMENT_BREAKER_OF_CHAINS_DESCRIPTION"] = "Sauvez les quatre elfes dans les Mines Carmine.",
["ACHIEVEMENT_BREAKER_OF_CHAINS_NAME"] = "Briseur de Chaînes",
["ACHIEVEMENT_BUTTERTENTACLES_DESCRIPTION"] = "Terminez la Tour Abominable tout en empêchant Mydrias d'attraper vos unités.",
["ACHIEVEMENT_BUTTERTENTACLES_NAME"] = "Soldats Glissants",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_DESCRIPTION"] = "Vaincre Seeres Mydrias.",
["ACHIEVEMENT_BYE_BYE_BEAUTIFUL_NAME"] = "Au Revoir, Au Revoir, Belle",
["ACHIEVEMENT_CIRCLE_OF_LIFE_DESCRIPTION"] = "Assistez à la présentation du nouveau-né Arborean.",
["ACHIEVEMENT_CIRCLE_OF_LIFE_NAME"] = "Cercle de la Vie",
["ACHIEVEMENT_CLEANSE_THE_KING_DESCRIPTION"] = "Sauvez le Roi Liniréen.",
["ACHIEVEMENT_CLEANSE_THE_KING_NAME"] = "Gloire au Roi",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_DESCRIPTION"] = "Terminez les Outskirts Ravagés sans déblayer les décombres des points stratégiques.",
["ACHIEVEMENT_CLEANUP_IS_OPTIONAL_NAME"] = "Le nettoyage est facultatif",
["ACHIEVEMENT_CONJUNTIVICTORY_DESCRIPTION"] = "Vaincre le Surveillant.",
["ACHIEVEMENT_CONJUNTIVICTORY_NAME"] = "Conjunctivictoire ",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_DESCRIPTION"] = "Obtenez 3 étoiles à chaque étape de l'Au-delà du Vide.",
["ACHIEVEMENT_CONQUEROR_OF_THE_VOID_NAME"] = "Conquérant du Vide",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_DESCRIPTION"] = "Collectez les trois côtelettes de porc dans le Repaire des Bêtes Sauvages.",
["ACHIEVEMENT_CRAFTING_IN_THE_MINES_NAME"] = "Artisanat dans les Mines",
["ACHIEVEMENT_CROWD_CONTROL_DESCRIPTION"] = "Terminez la Vallée de la Corruption sans qu'aucun Béhémoth de Chair n'émerge du puits.",
["ACHIEVEMENT_CROWD_CONTROL_NAME"] = "Contrôle de Foule",
["ACHIEVEMENT_CROW_SCARER_DESCRIPTION"] = "Effrayez tous les corbeaux dans la Vallée Morne.",
["ACHIEVEMENT_CROW_SCARER_NAME"] = "Épouvantail",
["ACHIEVEMENT_CRYSTAL_CLEAR_DESCRIPTION"] = "Obtenez 3 étoiles à chaque étape du Canyon Abandonné.",
["ACHIEVEMENT_CRYSTAL_CLEAR_NAME"] = "Clair comme de l'eau de roche",
["ACHIEVEMENT_DARK_LIEUTENANT_DESCRIPTION"] = "Atteignez le niveau 10 avec Raelyn.",
["ACHIEVEMENT_DARK_LIEUTENANT_NAME"] = "Lieutenant Sombre",
["ACHIEVEMENT_DARK_RUTHLESSNESS_DESCRIPTION"] = "Gagnez une étape en utilisant uniquement des tours et des héros de l'Armée des Ténèbres.",
["ACHIEVEMENT_DARK_RUTHLESSNESS_NAME"] = "Obscurité Impitoyable",
["ACHIEVEMENT_DISTURBING_THE_PEACE_DESCRIPTION"] = "Interromps la pause déjeuner des travailleurs dans le Dôme de Domination.",
["ACHIEVEMENT_DISTURBING_THE_PEACE_NAME"] = "Perturbation de la Paix",
["ACHIEVEMENT_DLC1_WIN_BOSS_DESCRIPTION"] = "Vaincs Grymbeard et arrête la construction de la machine de guerre.",
["ACHIEVEMENT_DLC1_WIN_BOSS_NAME"] = "Auto-Sans-Emploi",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_DESCRIPTION"] = "Collectez 8 hongbaos sur l’île Tempête.",
["ACHIEVEMENT_DLC2_GATHER_ENVELOPS_NAME"] = "Nous vous souhaitons richesse et prospérité.",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_DESCRIPTION"] = "Vainquez le Roi Démon Taureau dans sa forteresse.",
["ACHIEVEMENT_DLC2_WIN_BOSS_KING_NAME"] = "Le Retour du Roi Singe",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_DESCRIPTION"] = "Vaincre la Princesse Éventail de Fer et son armée aquatique.",
["ACHIEVEMENT_DLC2_WIN_BOSS_PRINCESS_NAME"] = "Un Vent Maléfique se Lève",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_DESCRIPTION"] = "Vainquez Garçon Rouge et son armée de feu.",
["ACHIEVEMENT_DLC2_WIN_BOSS_REDBOY_NAME"] = "Tout a changé...",
["ACHIEVEMENT_DOMO_ARIGATO_DESCRIPTION"] = "Écrasez 20 ennemis avec le poing géant dans le Cœur Colossal.",
["ACHIEVEMENT_DOMO_ARIGATO_NAME"] = "Domo Arigato",
["ACHIEVEMENT_FACTORY_STRIKE_DESCRIPTION"] = "Terminez Frantic Assembly sans laisser Grymbeard utiliser la machinerie.",
["ACHIEVEMENT_FACTORY_STRIKE_NAME"] = "Grève de l'Usine",
["ACHIEVEMENT_FIELD_TRIP_RUINER_DESCRIPTION"] = "Éteignez le feu du campeur.",
["ACHIEVEMENT_FIELD_TRIP_RUINER_NAME"] = "Gâcheur de Sortie Scolaire",
["ACHIEVEMENT_FOREST_PROTECTOR_DESCRIPTION"] = "Atteignez le niveau 10 avec Nyru.",
["ACHIEVEMENT_FOREST_PROTECTOR_NAME"] = "Protecteur de la Forêt",
["ACHIEVEMENT_GARBAGE_DISPOSAL_DESCRIPTION"] = "Élimine 10 Fous Inventeurs avant qu'ils ne puissent générer des Drones de Débris.",
["ACHIEVEMENT_GARBAGE_DISPOSAL_NAME"] = "Élimination des Déchets",
["ACHIEVEMENT_GEM_SPILLER_DESCRIPTION"] = "Brisez tous les paniers de gemmes.",
["ACHIEVEMENT_GEM_SPILLER_NAME"] = "Distributeur de Gemmes",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_DESCRIPTION"] = "Résolvez l'énigme et invoquez le groupe.",
["ACHIEVEMENT_GET_THE_PARTY_STARTED_NAME"] = "Commencez la fête",
["ACHIEVEMENT_GIFT_OF_LIFE_DESCRIPTION"] = "Libère l'expérience de clonage dans la Chambre de Réplication.",
["ACHIEVEMENT_GIFT_OF_LIFE_NAME"] = "Le Don de la Vie",
["ACHIEVEMENT_GREENLIT_ALLIES_DESCRIPTION"] = "Invoquez 10 Lances d'Épines Arboreuses.",
["ACHIEVEMENT_GREENLIT_ALLIES_NAME"] = "Alliés Approuvés",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_DESCRIPTION"] = "Trouve le roi des crocodiles.",
["ACHIEVEMENT_HAIL_TO_THE_K_BABY_NAME"] = "Salut au K, bébé !",
["ACHIEVEMENT_HEARTLESS_VICTORY_DESCRIPTION"] = "Terminez le Cœur de la Forêt sans utiliser le pouvoir du Cœur des Arboréens.",
["ACHIEVEMENT_HEARTLESS_VICTORY_NAME"] = "Victoire Sans Cœur",
["ACHIEVEMENT_INTO_THE_OGREVERSE_DESCRIPTION"] = "Découvre les secrets du mystérieux homme-araignée.",
["ACHIEVEMENT_INTO_THE_OGREVERSE_NAME"] = "Voisin peu amical",
["ACHIEVEMENT_IRONCLAD_DESCRIPTION"] = "Remportez tous les défis de la campagne en Mode Fer.",
["ACHIEVEMENT_IRONCLAD_NAME"] = "Cuirassé",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_DESCRIPTION"] = "Aidez Lank à pêcher 5 roupies.",
["ACHIEVEMENT_ITS_A_SECRET_TO_EVERYONE_NAME"] = "C'est un secret pour tout le monde",
["ACHIEVEMENT_KEPT_YOU_WAITING_DESCRIPTION"] = "Trouve le soldat furtif dans le Cœur Colossal.",
["ACHIEVEMENT_KEPT_YOU_WAITING_NAME"] = "Tu M'as Fait Attendre, Hein ?",
["ACHIEVEMENT_LEARNING_THE_ROPES_DESCRIPTION"] = "Terminez le tutoriel avec 3 étoiles.",
["ACHIEVEMENT_LEARNING_THE_ROPES_NAME"] = "Apprendre les ficelles",
["ACHIEVEMENT_LINIREAN_RESISTANCE_DESCRIPTION"] = "Gagnez une étape en utilisant uniquement des tours et des héros liniréens.",
["ACHIEVEMENT_LINIREAN_RESISTANCE_NAME"] = "Résistance Liniréenne",
["ACHIEVEMENT_LUCAS_SPIDER_DESCRIPTION"] = "Joue avec Lucus jusqu’à ce qu’il soit heureux.",
["ACHIEVEMENT_LUCAS_SPIDER_NAME"] = "Lucus l’Araignée",
["ACHIEVEMENT_MASTER_TACTICIAN_DESCRIPTION"] = "Terminez la campagne en difficulté Impossible.",
["ACHIEVEMENT_MASTER_TACTICIAN_NAME"] = "Maître Tacticien",
["ACHIEVEMENT_MECHANICAL_BURNOUT_DESCRIPTION"] = "Surcharger la machine dans les Portes d'Acier Noir.",
["ACHIEVEMENT_MECHANICAL_BURNOUT_NAME"] = "Épuisement Mécanique",
["ACHIEVEMENT_MIGHTY_III_DESCRIPTION"] = "Tuez 10000 ennemis.",
["ACHIEVEMENT_MIGHTY_III_NAME"] = "Puissant III",
["ACHIEVEMENT_MIGHTY_II_DESCRIPTION"] = "Tuez 3000 ennemis.",
["ACHIEVEMENT_MIGHTY_II_NAME"] = "Puissant II",
["ACHIEVEMENT_MIGHTY_I_DESCRIPTION"] = "Tuez 500 ennemis.",
["ACHIEVEMENT_MIGHTY_I_NAME"] = "Puissant Moi",
["ACHIEVEMENT_MOST_DELICIOUS_DESCRIPTION"] = "Donne du miel à Biggie l'Arboréen.",
["ACHIEVEMENT_MOST_DELICIOUS_NAME"] = "Plus Délicieux",
["ACHIEVEMENT_NATURES_WRATH_DESCRIPTION"] = "Tuez 30 ennemis en utilisant le Cœur des Arboréens.",
["ACHIEVEMENT_NATURES_WRATH_NAME"] = "Colère de la Nature",
["ACHIEVEMENT_NONE_SHALL_PASS_DESCRIPTION"] = "Terminez le Repaire des Bêtes Sauvages sans laisser passer d'ennemis supplémentaires par la porte.",
["ACHIEVEMENT_NONE_SHALL_PASS_NAME"] = "Personne ne passera !",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_DESCRIPTION"] = "Lancez 15 vagues plus tôt.",
["ACHIEVEMENT_NOT_A_MOMENT_TO_WASTE_NAME"] = "Pas un instant à perdre",
["ACHIEVEMENT_NO_FLY_ZONE_DESCRIPTION"] = "Tue 50 Araignées Ballon.",
["ACHIEVEMENT_NO_FLY_ZONE_NAME"] = "Zone interdite au vol",
["ACHIEVEMENT_OBLITERATE_DESCRIPTION"] = "Trouve les pièces du robot interdit dans chaque étape de la Menace Colossale.",
["ACHIEVEMENT_OBLITERATE_NAME"] = "Oblitérer !",
["ACHIEVEMENT_ONE_SHOT_TOWER_DESCRIPTION"] = "Éliminez 10 ennemis avec un seul faisceau de la Tour Darkray.",
["ACHIEVEMENT_ONE_SHOT_TOWER_NAME"] = "Un Coup pour la Gloire",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_DESCRIPTION"] = "Vaincre Goregrind avant de sauter en difficulté Impossible.",
["ACHIEVEMENT_OUTBACK_BARBEQUICK_NAME"] = "Emmêlé",
["ACHIEVEMENT_OVER_THE_EDGE_DESCRIPTION"] = "Poussez les Arboréens des cimes des arbres.",
["ACHIEVEMENT_OVER_THE_EDGE_NAME"] = "Fin de partie",
["ACHIEVEMENT_OVINE_JOURNALISM_DESCRIPTION"] = "Trouvez Sheepy dans chaque terrain de campagne.",
["ACHIEVEMENT_OVINE_JOURNALISM_NAME"] = "Journalisme Ovin",
["ACHIEVEMENT_PEST_CONTROL_DESCRIPTION"] = "Tuez 300 Éblouis.",
["ACHIEVEMENT_PEST_CONTROL_NAME"] = "Contrôle des Parasites",
["ACHIEVEMENT_PLAYFUL_FRIENDS_DESCRIPTION"] = "Joue \"terrier\" avec tous les Arboréens au Cœur de la Forêt.",
["ACHIEVEMENT_PLAYFUL_FRIENDS_NAME"] = "Amis Joueurs",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_DESCRIPTION"] = "Vaincre Goregrind.",
["ACHIEVEMENT_PORKS_OFF_THE_MENU_NAME"] = "Le porc est hors menu",
["ACHIEVEMENT_PROMOTION_DENIED_DESCRIPTION"] = "Tuez 30 Prêtres du Culte avant qu'ils ne se transforment en Abominations.",
["ACHIEVEMENT_PROMOTION_DENIED_NAME"] = "Promotion Refusée",
["ACHIEVEMENT_ROCK_BEATS_ROCK_DESCRIPTION"] = "Faire en sorte que la statue gagne contre elle-même.",
["ACHIEVEMENT_ROCK_BEATS_ROCK_NAME"] = "Pierre bat... Pierre?",
["ACHIEVEMENT_ROOM_achievement_claim"] = "Réclamez votre récompense !",
["ACHIEVEMENT_ROYAL_CAPTAIN_DESCRIPTION"] = "Atteignez le niveau 10 avec Vesper.",
["ACHIEVEMENT_ROYAL_CAPTAIN_NAME"] = "Capitaine Royal",
["ACHIEVEMENT_RUNEQUEST_DESCRIPTION"] = "Activez toutes les six runes à travers la Forêt Everadiant.",
["ACHIEVEMENT_RUNEQUEST_NAME"] = "Runequest",
["ACHIEVEMENT_RUST_IN_PEACE_DESCRIPTION"] = "Terminer une étape sans permettre à aucune Armure animée de se réanimer.",
["ACHIEVEMENT_RUST_IN_PEACE_NAME"] = "Repos rouillé",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_DESCRIPTION"] = "Gagnez l'étape sans perdre aucune fleur arboreenne.",
["ACHIEVEMENT_SAVIOUR_OF_THE_FOREST_NAME"] = "Sauveur de la forêt",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_DESCRIPTION"] = "Obtenez 3 étoiles à chaque étape de la Forêt Everadiant.",
["ACHIEVEMENT_SAVIOUR_OF_THE_GREEN_NAME"] = "Sauveur du Vert",
["ACHIEVEMENT_SCRAMBLED_EGGS_DESCRIPTION"] = "Tuez 50 crokinder avant qu'ils n'éclosent.",
["ACHIEVEMENT_SCRAMBLED_EGGS_NAME"] = "Œufs brouillés",
["ACHIEVEMENT_SEASONED_GENERAL_DESCRIPTION"] = "Terminez la campagne en difficulté Vétéran.",
["ACHIEVEMENT_SEASONED_GENERAL_NAME"] = "Général Aguerri",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_DESCRIPTION"] = "Vaincre Abominor, le dévoreur.",
["ACHIEVEMENT_SEE_YA_LATER_ALLIGATOR_NAME"] = "À plus tard, alligator",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_DESCRIPTION"] = "Complète le Dôme de Domination en empêchant Grymbeard de mettre le feu à tes tours.",
["ACHIEVEMENT_SHUT_YOUR_MOUTH_NAME"] = "Ferme Ta Bouche !",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_DESCRIPTION"] = "Utilisez les Pouvoirs de Héros 500 fois.",
["ACHIEVEMENT_SIGNATURE_TECHNIQUES_NAME"] = "Techniques Signatures",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_DESCRIPTION"] = "Aidez Gerhart à tuer le monstre arbre.",
["ACHIEVEMENT_SILVER_FOR_MONSTERS_NAME"] = "Argent pour les Monstres",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_DESCRIPTION"] = "Aidez l'alligator sympathique à démarrer son bateau.",
["ACHIEVEMENT_SMOOTH_OPER_GATOR_NAME"] = "Opér-gator lisse",
["ACHIEVEMENT_SPECTRAL_FURY_DESCRIPTION"] = "Vaincre Navira et arrêter l'invasion des Revenants.",
["ACHIEVEMENT_SPECTRAL_FURY_NAME"] = "Fureur spectrale",
["ACHIEVEMENT_STARLIGHT_DESCRIPTION"] = "Aidez Fredo et Sammy à s'échapper de l'Araignée Géante.",
["ACHIEVEMENT_STARLIGHT_NAME"] = "Clair de lune",
["ACHIEVEMENT_TAKE_ME_HOME_DESCRIPTION"] = "Renvoyez Riff le Gobelin à sa dimension d'origine.",
["ACHIEVEMENT_TAKE_ME_HOME_NAME"] = "Prends-moi",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_DESCRIPTION"] = "Invoquez 1000 renforts.",
["ACHIEVEMENT_THE_CAVALRY_IS_HERE_NAME"] = "La cavalerie est arrivée !",
["ACHIEVEMENT_TIPPING_THE_SCALES_DESCRIPTION"] = "Jetez Robin Wood dans la rivière.",
["ACHIEVEMENT_TIPPING_THE_SCALES_NAME"] = "Renverser la balance",
["ACHIEVEMENT_TREE_HUGGER_DESCRIPTION"] = "Terminer les Ruines brumeuses avec au moins un Weirdwood encore debout.",
["ACHIEVEMENT_TREE_HUGGER_NAME"] = "Amoureux des arbres",
["ACHIEVEMENT_TURN_A_BLIND_EYE_DESCRIPTION"] = "Tuez 100 Progénitures de la Corruption pendant qu'elles sont sous les effets de l'Éblouissement.",
["ACHIEVEMENT_TURN_A_BLIND_EYE_NAME"] = "Fermer les Yeux",
["ACHIEVEMENT_UNBOUND_VICTORY_DESCRIPTION"] = "Terminez la Traversée Maudite sans qu'aucun Cauchemar ne se transforme en Cauchemar Blindé.",
["ACHIEVEMENT_UNBOUND_VICTORY_NAME"] = "Victoire Déliée",
["ACHIEVEMENT_UNENDING_RICHES_DESCRIPTION"] = "Collectez un total de 150000 or.",
["ACHIEVEMENT_UNENDING_RICHES_NAME"] = "Richesses Infinies",
["ACHIEVEMENT_UNTAMED_BEAST_DESCRIPTION"] = "Atteignez le niveau 10 avec Grimson.",
["ACHIEVEMENT_UNTAMED_BEAST_NAME"] = "Bête Sauvage",
["ACHIEVEMENT_WAR_MASONRY_DESCRIPTION"] = "Construisez 100 tours.",
["ACHIEVEMENT_WAR_MASONRY_NAME"] = "Maçonnerie de Guerre",
["ACHIEVEMENT_WEIRDER_THINGS_DESCRIPTION"] = "Aidez Ernie et Daston à repousser les Clignotants dans les Plaines Flétries.",
["ACHIEVEMENT_WEIRDER_THINGS_NAME"] = "Choses Plus Étranges",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_DESCRIPTION"] = "Trouver le chat insaisissable dans chaque étape de la campagne Fureur Éternelle.",
["ACHIEVEMENT_WE_ARE_ALL_MAD_HERE_NAME"] = "Nous sommes tous fous ici",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_DESCRIPTION"] = "Tuez 15 Sœurs Tordues avant qu'elles puissent engendrer un Cauchemar.",
["ACHIEVEMENT_WE_RE_NOT_GONNA_TAKE_IT_NAME"] = "On ne va pas le prendre",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_DESCRIPTION"] = "Réparer le pistolet à portail de Nick et Marty.",
["ACHIEVEMENT_WOBBA_LUBBA_DUB_DUB_NAME"] = "Wobba-Lubba-Dub-Dub!",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_DESCRIPTION"] = "Vaincre Denas Corrompu sans laisser la Voyante Mydrias lancer des projections en Difficulté Impossible.",
["ACHIEVEMENT_YOU_SHALL_NOT_CAST_NAME"] = "Vous Ne Lancerez Pas!",
["ADS_MESSAGE_OK"] = "OK",
["ADS_MESSAGE_TITLE"] = "PLUS DE GEMMES",
["ALERT_VERSION"] = "Une nouvelle version du jeu est disponible. Veuillez la télécharger depuis le store.",
["APPLY_SETTINGS_AND_RESTART"] = "Redémarrer pour appliquer les modifications?",
["ARCHER TOWER"] = "TOUR D'ARCHERS",
["ARE YOU SURE YOU WANT TO QUIT?"] = "VEUX-TU VRAIMENT QUITTER?",
["ARMORED ENEMIES!"] = "Ennemis en armure",
["ARTILLERY"] = "ARTILLERIE",
["Achievements"] = "Succès",
["Advanced"] = "Avancé",
["Average"] = "Moyenne",
["BARRACKS"] = "CASERNE",
["BOSS_BULL_KING_NAME"] = "Roi Démon Taureau",
["BOSS_CORRUPTED_DENAS_DESCRIPTION"] = "Le roi vaincu de Linirea, désormais transformé en une abomination imposante par les pouvoirs obscurs du Culte de l'Observateur.",
["BOSS_CORRUPTED_DENAS_EXTRA"] = "- Génère des Glarelings ",
["BOSS_CORRUPTED_DENAS_NAME"] = "Denas Corrompu",
["BOSS_CROCS_DESCRIPTION"] = "La faim incarnée, Un être ancien capable de dévorer le monde lui-même si on ne le contrôle pas.",
["BOSS_CROCS_EXTRA"] = "- Mange des tours\n- Évolue après avoir satisfait sa faim\n- Invoque des crokinders",
["BOSS_CROCS_LVL1_DESCRIPTION"] = "La faim incarnée, Un être ancien capable de dévorer le monde lui-même si on ne le contrôle pas.",
["BOSS_CROCS_LVL1_EXTRA"] = "- Mange des tours\n- Évolue après avoir satisfait sa faim\n- Invoque des crokinders",
["BOSS_CROCS_LVL1_NAME"] = "Abominor",
["BOSS_CROCS_LVL2_DESCRIPTION"] = "La faim incarnée, Un être ancien capable de dévorer le monde lui-même si on ne le contrôle pas.",
["BOSS_CROCS_LVL2_EXTRA"] = "- Mange des tours\n- Évolue après avoir satisfait sa faim\n- Invoque des crokinders",
["BOSS_CROCS_LVL2_NAME"] = "Abominor",
["BOSS_CROCS_LVL3_DESCRIPTION"] = "La faim incarnée, Un être ancien capable de dévorer le monde lui-même si on ne le contrôle pas.",
["BOSS_CROCS_LVL3_EXTRA"] = "- Mange des tours\n- Évolue après avoir satisfait sa faim\n- Invoque des crokinders",
["BOSS_CROCS_LVL3_NAME"] = "Abominor",
["BOSS_CROCS_LVL4_DESCRIPTION"] = "La faim incarnée, Un être ancien capable de dévorer le monde lui-même si on ne le contrôle pas.",
["BOSS_CROCS_LVL4_EXTRA"] = "- Mange des tours\n- Évolue après avoir satisfait sa faim\n- Invoque des crokinders",
["BOSS_CROCS_LVL4_NAME"] = "Abominor",
["BOSS_CROCS_LVL5_DESCRIPTION"] = "La faim incarnée, Un être ancien capable de dévorer le monde lui-même si on ne le contrôle pas.",
["BOSS_CROCS_LVL5_EXTRA"] = "- Mange des tours\n- Évolue après avoir satisfait sa faim\n- Invoque des crokinders",
["BOSS_CROCS_LVL5_NAME"] = "Abominor",
["BOSS_CROCS_NAME"] = "Abominor",
["BOSS_CULT_LEADER_DESCRIPTION"] = "Le leader actuel du Culte, Mydrias agit comme la main de l'Observateur, orchestrant l'invasion des mondes.",
["BOSS_CULT_LEADER_EXTRA"] = "- Haute armure et résistance magique tant qu'elle n'est pas bloquée\n - Dégâts de zone élevés",
["BOSS_CULT_LEADER_NAME"] = "Prophétesse Mydrias",
["BOSS_GRYMBEARD_DESCRIPTION"] = "Un nain égomaniaque avec des illusions de grandeur, aussi dangereux que dérangé.",
["BOSS_GRYMBEARD_EXTRA"] = "- Lance un poing-fusée contre les unités du joueur",
["BOSS_GRYMBEARD_NAME"] = "Grymbeard",
["BOSS_MACHINIST_DESCRIPTION"] = "Sur son dernier engin, Grymbeard poursuit ses ennemis, faisant pleuvoir feu et métal.",
["BOSS_MACHINIST_EXTRA"] = "- Volant\n- Tire de la ferraille sur les unités",
["BOSS_MACHINIST_NAME"] = "Grymbeard",
["BOSS_NAVIRA_DESCRIPTION"] = "Tombée en disgrâce et puisant dans les forces interdites de la magie de la mort, Navira cherche à restaurer la gloire des elfes.",
["BOSS_NAVIRA_EXTRA"] = "- Bloque les tours avec des boules de feu\n- Se transforme en tornade imblocable",
["BOSS_NAVIRA_NAME"] = "Navira",
["BOSS_PIG_DESCRIPTION"] = "Le seul et unique roi auto-proclamé des Bêtes Sauvages utilise un fléau géant pour écraser ses ennemis.",
["BOSS_PIG_EXTRA"] = "- Saute de grandes distances à travers les chemins",
["BOSS_PIG_NAME"] = "Goregrind",
["BOSS_PRINCESS_IRON_FAN_DESCRIPTION"] = "Alliant élégance et puissance mortelle, la Princesse Éventail de Fer n’est pas seulement l’épouse du Roi Démon Taureau, c’est aussi une redoutable adversaire. Une démone posée et calculatrice. Porteuse du légendaire éventail de fer, capable d’éteindre les flammes et de provoquer des tempêtes.",
["BOSS_PRINCESS_IRON_FAN_EXTRA"] = "- Se clone elle-même\n- Enferme les héros dans une fiole\n- Transforme les tours en générateurs d’ennemis",
["BOSS_REDBOY_TEEN_DESCRIPTION"] = "Le jeune prince démon fougueux et orgueilleux, connu pour son tempérament de feu, son arrogance et son ambition sans limite. Commandant du Feu Samadhi et expert en arts martiaux à la lance. Fils de la Princesse et du Roi Démon Taureau.",
["BOSS_REDBOY_TEEN_EXTRA"] = "- Grosse attaque de dégâts de zone\n- Ordonne à son dragon d'étourdir les tours",
["BOSS_REDBOY_TEEN_NAME"] = "Red Boy",
["BOSS_SPIDER_QUEEN_DESCRIPTION"] = "Une ancienne Reine Araignée, une force primordiale réveillée de son sommeil pour reprendre ce qui lui revient de droit.",
["BOSS_SPIDER_QUEEN_EXTRA"] = "- Étourdit les tours\n- Draine la vie des ennemis proches\n- Invoque des Araignées Vol de Vie\n- Lance des toiles dans vos yeux",
["BOSS_SPIDER_QUEEN_NAME"] = "Mygale",
["BRIEFING_LEVEL_WARNING"] = "Cette campagne a un niveau de difficulté élevé.",
["BUILD HERE!"] = "CONSTRUIS ICI!",
["BUTTON_BUG_CRASH"] = "DÉFAUT DE JEU",
["BUTTON_BUG_OTHER"] = "AUTRE",
["BUTTON_BUG_REPORT"] = "ERREUR",
["BUTTON_BUY"] = "ACHETER",
["BUTTON_BUY_UPGRADE"] = "ACHETER AMÉLIORATION",
["BUTTON_CLOSE"] = "FERMER",
["BUTTON_CONFIRM"] = "CONFIRMER",
["BUTTON_CONTINUE"] = "CONTINUER",
["BUTTON_DISABLE"] = "Désactiver",
["BUTTON_DONE"] = "TERMINÉ",
["BUTTON_ENDLESS_QUIT"] = "QUITTER",
["BUTTON_ENDLESS_TRYAGAIN"] = "RÉESSAYER",
["BUTTON_GET_GEMS"] = "OBTENIR OBJETS",
["BUTTON_LEVEL_SELECT_FIGHT"] = "COMBATTEZ !",
["BUTTON_LOST_CONTENT"] = "CONTENU PERDU",
["BUTTON_MAIN_MENU"] = "MENU PRINCIPAL",
["BUTTON_NO"] = "NON",
["BUTTON_OK"] = "OK!",
["BUTTON_OPEN"] = "OUVRIR",
["BUTTON_QUIT"] = "QUITTER",
["BUTTON_RESET"] = "RÉINIT.",
["BUTTON_RESTART"] = "RECOMMENCER",
["BUTTON_RESUME"] = "REPRENDRE",
["BUTTON_TO_BATTLE_1"] = "AU",
["BUTTON_TO_BATTLE_2"] = "COMBAT",
["BUTTON_UNDO"] = "ANNULER",
["BUTTON_YES"] = "OUI",
["BUY UPGRADES!"] = "ACHETER AMÉLIORATIONS!",
["Basic"] = "Basique",
["Basic Tower Types"] = "Types de tours basiques.",
["CARD_REWARDS_CAMPAIGN"] = "Nouvelle campagne!",
["CARD_REWARDS_DLC_1"] = "Menace Colossale",
["CARD_REWARDS_DLC_2"] = "Le Voyage de Wukong",
["CARD_REWARDS_HERO"] = "NOUVEAU HÉROS!",
["CARD_REWARDS_TOWER"] = "NOUVELLE TOUR!",
["CARD_REWARDS_TOWER_LEVEL"] = "NOUVEAU NIVEAU DE TOUR!",
["CARD_REWARDS_TOWER_LEVEL_PREFIX"] = "NIV.",
["CARD_REWARDS_UPDATE_01"] = "Fureur Éternelle",
["CARD_REWARDS_UPDATE_02"] = "Faim Ancienne",
["CARD_REWARDS_UPDATE_03"] = "Arachnophobie",
["CARD_REWARDS_UPGRADES"] = "POINTS D'AMÉLIORATION!",
["CArmor0"] = "Aucune",
["CArmor1"] = "Légère",
["CArmor2"] = "Moyenne",
["CArmor3"] = "Lourde",
["CArmor4"] = "Super",
["CArmor9"] = "Immunisé",
["CArmorSmall0"] = "Non",
["CArmorSmall1"] = "Lgr",
["CArmorSmall2"] = "Moy",
["CArmorSmall3"] = "Lrd",
["CArmorSmall4"] = "Sup",
["CArmorSmall9"] = "Imm.",
["CHANGE_LANGUAGE_QUESTION"] = "VEUX-TU VRAIMENT CHANGER LES PARAMÈTRES DE LANGUE ?",
["CINEMATICS_TAP_TO_CONTINUE_KR1"] = "clique pour continuer...",
["CINEMATICS_TAP_TO_CONTINUE_KR2"] = "clique pour continuer...",
["CINEMATICS_TAP_TO_CONTINUE_KR3"] = "clique pour continuer...",
["CINEMATICS_TAP_TO_CONTINUE_KR5"] = "clique pour continuer...",
["CLAIM_GIFT"] = "Réclamer le cadeau",
["CLICK HERE TO SKIP.\nPLEASE DON'T"] = "CLIQUE ICI POUR PASSER.\nNE CLIQUE PAS",
["CLICK HERE!"] = "CLIQUE ICI!",
["CLICK ON THE ROAD"] = "CLIQUE SUR LA ROUTE",
["CLICK TO CALL IT EARLY"] = "CLIQUE POUR L'APPELER PLUS TÔT",
["CLOUDSYNC_PLEASE_WAIT"] = "Mise à jour des jeux enregistrés dans le cloud ...",
["CLOUD_DIALOG_NO"] = "Non",
["CLOUD_DIALOG_OK"] = "OK",
["CLOUD_DIALOG_YES"] = "Oui",
["CLOUD_DOWNLOAD_QUESTION"] = "Télécharger partie sauvegardée depuis l'iCloud?",
["CLOUD_DOWNLOAD_TITLE"] = "Télécharger depuis l'iCloud",
["CLOUD_SAVE"] = "Sauvegarde cloud",
["CLOUD_SAVE_DISABLE_EXTRA"] = "Attention : Si vous désinstallez le jeu, vous risquez de perdre votre progression.",
["CLOUD_SAVE_DISABLE_GENERIC_DESCRIPTION"] = "Voulez-vous vraiment désactiver la sauvegarde sur le cloud ?",
["CLOUD_SAVE_OFF"] = "Cloud désactivée",
["CLOUD_SAVE_ON"] = "Cloud activée",
["CLOUD_UPLOAD_QUESTION"] = "Envoyer partie sauvegardée sur l'iCloud?",
["CLOUD_UPLOAD_TITLE"] = "Envoyer sur l'iCloud",
["COMIC_10_1_KR5_KR5"] = "Libérez-moi! Je fais ce qui est le mieux pour le royaume!",
["COMIC_10_2_KR5_KR5"] = "Arrête cette blasphème, frère. Ce n'est pas la voie des elfes.",
["COMIC_10_3_KR5_KR5"] = "Merci, mon ancien apprenti. Nous prendrons le relais à partir d'ici.",
["COMIC_10_4_KR5_KR5"] = "Plus tard, au camp...",
["COMIC_10_5_KR5_KR5"] = "Alors... tu es sûr qu'on peut faire confiance à Vez'nan ?",
["COMIC_10_6_KR5_KR5"] = "Nous gardons un œil sur lui, mais il semble jouer le jeu pour l'instant.",
["COMIC_10_7_KR5_KR5"] = "...mais il semble bien se tenir pour l’instant.",
["COMIC_10_8_KR5_KR5"] = "Heh. Pour l'instant...",
["COMIC_11_1_KR5_KR5"] = "Le marais semble s'être réveillé...",
["COMIC_11_2_KR5_KR5"] = "...comme s'il nous observait...",
["COMIC_11_3_KR5_KR5"] = "...avançant et se cachant...",
["COMIC_11_4_KR5_KR5"] = "...prêt à nous dévorer.",
["COMIC_11_5_KR5_KR5"] = "Soyez prudent !",
["COMIC_11_6_KR5_KR5"] = "Nous sommes sous attaque !",
["COMIC_11_7_KR5_KR5"] = "Va, petite luciole ! Notre sécurité réside dans ta hâte !",
["COMIC_12_1_KR5_KR5"] = "Vous enfermer simplement était une erreur. Une que je ne répéterai pas.",
["COMIC_12_2_KR5_KR5"] = "NONNNNNN !!!",
["COMIC_12_3_KR5_KR5"] = "Je te bannis pour toujours !!",
["COMIC_12_4_KR5_KR5"] = "Cof !",
["COMIC_12_5_KR5_KR5"] = "Cof, Cof !",
["COMIC_12_6_KR5_KR5"] = "Euh-Je suppose que je dois manquer d'entraînement.",
["COMIC_13_1_KR5_KR5"] = "Ils disaient que c'était de la folie.",
["COMIC_13_2_KR5_KR5"] = "Qu'une telle arme était impossible.",
["COMIC_13_3_KR5_KR5"] = "Mais bientôt ils sauront à quel point ils avaient tort...",
["COMIC_13_4_KR5_KR5"] = "...et se rendront devant le génie de Grymbeard !",
["COMIC_14_1_KR5_KR5"] = "Que va-t-on faire avec eux ?",
["COMIC_14_2_KR5_KR5"] = "Laisse-les moi !",
["COMIC_14_3_KR5_KR5"] = "Je connais exactement l'endroit.",
["COMIC_14_4_KR5_KR5"] = "Donc c'est tout ?",
["COMIC_14_5_KR5_KR5"] = "Faire pourrir Grymbeard dans une cellule ?",
["COMIC_14_6_KR5_KR5"] = "Bien au contraire, mon petit ami...",
["COMIC_14_7_KR5_KR5"] = "...J'ai de grands projets pour ton grand cerveau !",
["COMIC_15_10_KR5_KR5"] = "…mais pas en bon état.",
["COMIC_15_1_KR5_KR5"] = "Quelque part dans la montagne.",
["COMIC_15_2_KR5_KR5"] = "Hé, Gobelin !",
["COMIC_15_3_KR5_KR5"] = "Au travail !",
["COMIC_15_4_KR5_KR5"] = "Tu dois livrer un message.",
["COMIC_15_5_KR5_KR5"] = "Nous devrions envoyer plus d’éclaireurs. Nous ne pouvons pas être tranquilles avec tous ces cultistes qui rôdent.",
["COMIC_15_6_KR5_KR5"] = "Nous pourrions envoyer quelques esprits pour aider ; ils…",
["COMIC_15_7_KR5_KR5"] = "Seigneur des Ténèbres ! Nouvelles urgentes !",
["COMIC_15_8_KR5_KR5"] = "Eh bien…",
["COMIC_15_9_KR5_KR5"] = "Nous avons retrouvé nos éclaireurs…",
["COMIC_16_1_KR5_KR5"] = "Je serai vengé !",
["COMIC_16_2_KR5_KR5"] = "Ma sœur...quoooi ?",
["COMIC_17_10_KR5_KR5"] = "Si on ne les arrête pas, ils anéantiront tous les royaumes !",
["COMIC_17_11_KR5_KR5"] = "Nous devons l’aider !",
["COMIC_17_12_KR5_KR5"] = "Oh, bien sûr que oui.",
["COMIC_17_13_KR5_KR5"] = "Bien sûr que oui…",
["COMIC_17_1_KR5_KR5"] = "Preste après-midi, n'est-ce pas ?",
["COMIC_17_2_KR5_KR5"] = "Je pourrais m’habituer à cette paix.",
["COMIC_17_3_KR5_KR5"] = "Mieux vaut pas.",
["COMIC_17_4_KR5_KR5"] = "Soleil, c’est toi ?! Tu aurais pu juste faire un signe, tu sais…",
["COMIC_17_5_KR5_KR5"] = "Amis, quelque chose de terrible s'est produit...",
["COMIC_17_6_KR5_KR5"] = "Je méditais paisiblement dans ma tortue quand…",
["COMIC_17_7_KR5_KR5"] = "Les Trois Rois Démons sont apparus de nulle part !",
["COMIC_17_8_KR5_KR5"] = "Inutile de dire que j’ai combattu vaillamment, mais…",
["COMIC_17_9_KR5_KR5"] = "Ils ont dérobé mes sphères célestes de manière déshonorante !",
["COMIC_1_1_KR5"] = "Cela fait un mois que nous sommes arrivés dans cette terre, à la recherche de notre Roi perdu...",
["COMIC_1_2B_KR5"] = "...Après avoir été banni par Vez'nan, le sorcier noir.",
["COMIC_1_4_KR5"] = "Nous avons trouvé un endroit et avons établi un camp pour récupérer nos forces...",
["COMIC_1_5_KR5"] = "...en paix...",
["COMIC_1_8_KR5"] = "...Mais il semble que c'est fini maintenant.",
["COMIC_2_1_KR5"] = "Hourra!",
["COMIC_2_3_KR5"] = "Vez'nan?!",
["COMIC_2_4a_KR5"] = "Du calme maintenant... Je viens proposer...",
["COMIC_2_4b_KR5"] = "...un marché.",
["COMIC_2_5_KR5"] = "Après ce que vous avez fait à notre royaume ?",
["COMIC_2_6_KR5"] = "Il fallait ouvrir les yeux du roi Denas.",
["COMIC_2_7_KR5"] = "Il a refusé de voir le danger qui infestait le royaume.",
["COMIC_2_8_1_KR5"] = "Mais laissons-nous trouver votre roi...",
["COMIC_2_8_2_KR5"] = "...et mettons fin à cette menace.",
["COMIC_2_8b_KR5"] = "...ensemble.",
["COMIC_3_1_KR5"] = "Oh là là ! Qu'avons-nous ici... ?",
["COMIC_3_2_KR5"] = "L'épée puissante d'Elynie !",
["COMIC_3_3_KR5"] = "Aïe !",
["COMIC_3_4a_KR5"] = "Bien sûr...",
["COMIC_3_4b_KR5"] = "Arrête de perdre du temps !",
["COMIC_3_5a_KR5"] = "Ah... mais il est plus proche que vous ne le pensez.",
["COMIC_3_5b_KR5"] = "Notre roi est toujours porté disparu.",
["COMIC_3_6_KR5"] = "Cela pourrait être une bataille difficile, cependant.",
["COMIC_4_10a_KR5"] = "Ha! J'ai toujours raison.",
["COMIC_4_10b_KR5"] = "Alors... que se passe-t-il maintenant ?",
["COMIC_4_11_KR5"] = "Nous pouvons avoir nos différences...",
["COMIC_4_12_KR5"] = "...mais nous avons en commun une menace plus grande.",
["COMIC_4_1_KR5"] = "Elynie...",
["COMIC_4_2_KR5"] = "...donne-lui de la force !",
["COMIC_4_4_KR5"] = "Aaurrrgh !",
["COMIC_4_7a_KR5"] = "Je vois que tes 'vacances' t'ont fait le plus grand bien !",
["COMIC_4_7b_KR5"] = "TOI!!!",
["COMIC_4_8_KR5"] = "Tu devrais payer pour tes méfaits !",
["COMIC_4_9_KR5"] = "Mais tu avais raison.",
["COMIC_5_1_KR2"] = "Victoire!",
["COMIC_5_1_KR5_KR5"] = "Vous, vers, ne pouvez pas arrêter...",
["COMIC_5_2_KR2"] = "Victoire!",
["COMIC_5_2_KR5_KR5"] = "LE NOUVEAU MONDE!",
["COMIC_5_6_KR5_KR5"] = "Il s'est réveillé !",
["COMIC_5_7a_KR5_KR5"] = "Alors c'est ça...",
["COMIC_5_7b_KR5_KR5"] = "le duel final.",
["COMIC_6_1a_KR5_KR5"] = "Tu es courageux de me défier...",
["COMIC_6_1b_KR5_KR5"] = "mais ça n'a pas sa place ici !",
["COMIC_6_4_KR5_KR5"] = "Hé !",
["COMIC_6_5_KR5_KR5"] = "Toi, limace cosmique...",
["COMIC_6_6_KR5_KR5"] = "...sous-estimes MON pouvoir!!!",
["COMIC_6_8_KR5_KR5"] = "Préparez-vous. Je ne peux pas le tenir très longtemps !",
["COMIC_7_1_KR5_KR5"] = "NON ! Ceci... ne peut pas être !!!",
["COMIC_7_3_KR5_KR5"] = "Alors... que faire maintenant ?",
["COMIC_7_4a_KR5_KR5"] = "Eh bien, ma mission est accomplie...",
["COMIC_7_4b_KR5_KR5"] = "...et je pense qu'ils ont besoin de leur roi.",
["COMIC_7_5_2_KR2"] = "Non.",
["COMIC_7_6_KR5_KR5"] = "Jusqu'à la prochaine fois, cher ennemi.",
["COMIC_7_7_KR5_KR5"] = "Plus tard, dans la Forêt Everadiante",
["COMIC_8_1_KR5_KR5"] = "Ah, enfin!",
["COMIC_8_2_KR5_KR5"] = "Ce pouvoir est, encore une fois...",
["COMIC_8_4_KR5_KR5"] = "... LE MIEN!",
["COMIC_8_5_KR5_KR5"] = "MUA HA HA HA HA!",
["COMIC_9_1_KR5_KR5"] = "Il n'y a pas si longtemps, nous, les elfes, étions vénérés pour notre magie et notre grâce...",
["COMIC_9_2_KR5_KR5"] = "...jusqu'à ce que notre relique sacrée soit corrompue et que nous devenions une ombre de ce que nous étions.",
["COMIC_9_3_KR5_KR5"] = "Mais avec cette armée, je vais restaurer notre gloire...",
["COMIC_9_4_KR5_KR5"] = "...et je vais diriger un nouveau monde gouverné par les elfes!!!",
["COMIC_BALLOON_0002_KR1"] = "Victoire!",
["COMIC_BALLOON_02_KR1"] = "Victoire!",
["COMIC_balloon_0002_KR1"] = "Victoire!",
["COMMAND YOUR TROOPS!"] = "COMMANDE TES TROUPES!",
["CONFIRM_EXIT"] = "Sortir?",
["CONFIRM_RESTART"] = "Redémarrer?",
["CONTROLLER_STAGE_16_OVERSEER_DESCRIPTION"] = "Une monstruosité extradimensionnelle qui envahit et conquiert d'autres mondes pour absorber leur énergie. Doit être arrêté à tout prix.",
["CONTROLLER_STAGE_16_OVERSEER_EXTRA"] = "- Échange les tours du joueur\n- Fait apparaître des Éblouis\n- Détruit les supports",
["CONTROLLER_STAGE_16_OVERSEER_NAME"] = "L'Intendant",
["CREDITS"] = "CRÉDITS",
["CREDITS_COPYRIGHT"] = "© 2014 Ironhide Game Studio. Tous droits réservés.",
["CREDITS_POWERED_BY"] = "Motorisé par",
["CREDITS_SUBTITLE_01"] = "(par ordre alphabétique)",
["CREDITS_SUBTITLE_07"] = "(par ordre alphabétique)",
["CREDITS_SUBTITLE_09"] = "(par ordre alphabétique)",
["CREDITS_SUBTITLE_16"] = "(par ordre alphabétique)",
["CREDITS_TEXT_18"] = "À nos familles, à nos amis et à notre communauté",
["CREDITS_TEXT_18_2"] = "pour leur soutien tout au long de ces années.",
["CREDITS_TITLE_01"] = "Directeurs créatifs & Producteurs exécutifs",
["CREDITS_TITLE_01_CREATIVE_DIRECTORS"] = "Directeurs créatifs",
["CREDITS_TITLE_01_EXECUTIVE_PRODUCERS"] = "Producteurs exécutifs",
["CREDITS_TITLE_02"] = "Responsable conception du jeu",
["CREDITS_TITLE_02_LEAD_GAME_DESIGNERS"] = "Chefs de conception de jeux",
["CREDITS_TITLE_03"] = "Concepteurs du jeu",
["CREDITS_TITLE_03_GAME_DESIGNER"] = "Concepteur de jeux",
["CREDITS_TITLE_04"] = "Scénariste de l'histoire",
["CREDITS_TITLE_04_STORY_WRITERS"] = "Scénaristes",
["CREDITS_TITLE_05"] = "Auteurs des textes",
["CREDITS_TITLE_06"] = "Responsable programmation",
["CREDITS_TITLE_06_LEAD_PROGRAMMERS"] = "Programmeurs principaux",
["CREDITS_TITLE_07"] = "Programmeurs",
["CREDITS_TITLE_08"] = "Responsable graphismes",
["CREDITS_TITLE_09"] = "Graphistes",
["CREDITS_TITLE_10"] = "Graphiste BD",
["CREDITS_TITLE_11"] = "Scénariste BD",
["CREDITS_TITLE_12"] = "Graphiste technique",
["CREDITS_TITLE_13"] = "Effets sonores",
["CREDITS_TITLE_14"] = "Musique originale de",
["CREDITS_TITLE_15"] = "Doublage",
["CREDITS_TITLE_16"] = "AQ & tests",
["CREDITS_TITLE_17"] = "Tests bêta",
["CREDITS_TITLE_18"] = "Remerciements particuliers",
["CREDITS_TITLE_19_PMO"] = "Bureau de gestion de projets",
["CREDITS_TITLE_20_PRODUCER"] = "Producteur",
["CREDITS_TITLE_21_MARKETING"] = "Marketing",
["CREDITS_TITLE_22_SPECIAL_COLLAB"] = "Collaborateurs spéciaux",
["CREDITS_TITLE_ANCIENT_HUNGER_UPDATE"] = "Faim Ancienne / Arachnophobie / Le Voyage de Wukong",
["CREDITS_TITLE_GAME_ENGINE_PROGRAMMER"] = "Programmeur de moteur de jeu",
["CREDITS_TITLE_LOCALIZATION"] = "Localisation",
["CREDITS_TITLE_LOGO"] = "UN JEU DE",
["CRange0"] = "Courte",
["CRange1"] = "Moyenne",
["CRange2"] = "Longue",
["CRange3"] = "Super",
["CRange4"] = "Extrême",
["CReload0"] = "Très lente",
["CReload1"] = "Lente",
["CReload2"] = "Moyenne",
["CReload3"] = "Rapide",
["CReload4"] = "Très rapide",
["CSpeed0"] = "Lente",
["CSpeed1"] = "Moyenne",
["CSpeed2"] = "Rapide",
["C_DIFFICULTY_EASY"] = "Mode Décontracté terminé",
["C_DIFFICULTY_HARD"] = "Mode Vétéran terminé",
["C_DIFFICULTY_IMPOSSIBLE"] = "Mode Impossible terminé",
["C_DIFFICULTY_NORMAL"] = "Mode Normal terminé",
["C_REWARD"] = "Récompense :",
["Campaign"] = "Campagne",
["Campaing"] = "Campagne",
["Casual"] = "Décontracté",
["Challenge Rules"] = "Règles du défi",
["Clear_progress"] = "Effacer la progression",
["Click on the path to move the hero."] = "CLIQUE sur le chemin pour déplacer ton héros.",
["Click to select"] = "Clique pour sélectionner",
["Coming soon"] = "Prochainement",
["Community Manager"] = "Gestionnaire de communauté",
["Continue"] = "Continuer",
["Credits"] = "Crédits",
["DAYS_ABBREVIATION"] = "j",
["DEFEAT"] = "DÉFAITE",
["DELETE SLOT?"] = "Supprimer l'emplacement?",
["DIFFICULTY LEVEL"] = "NIVEAU DE DIFFICULTÉ",
["DIFFICULTY_SELECTION_EASY_DESCRIPTION"] = "Pour les débutants dans les jeux de stratégie!",
["DIFFICULTY_SELECTION_HARD_DESCRIPTION"] = "Insensé! Joue à tes risques et périls!",
["DIFFICULTY_SELECTION_IMPOSSIBLE_DESCRIPTION"] = "Seuls les plus forts auront une chance!",
["DIFFICULTY_SELECTION_IMPOSSIBLE_LOCKED_DESCRIPTION"] = "Termine la campagne pour débloquer ce mode",
["DIFFICULTY_SELECTION_NORMAL_DESCRIPTION"] = "Un bon défi!",
["DIFFICULTY_SELECTION_NOTE"] = "Tu peux toujours changer le niveau de difficulté en sélectionnant une Étape.",
["DIFFICULTY_SELECTION_TITLE"] = "Choisis le niveau de difficulté!",
["DISCOUNT"] = "REMISE",
["DLC_OWNED"] = "ACHÉTÉ",
["Defeat"] = "Défaite",
["Difficulty Level"] = "Niveau de difficulté",
["Done"] = "Terminé",
["ELITE STAGE!"] = "ÉTAPE ÉLITE!",
["ENEMY_ACOLYTE_DESCRIPTION"] = "Petits et dociles, les acolytes font valoir leur nombre dans la bataille.",
["ENEMY_ACOLYTE_EXTRA"] = "- Fait apparaître un tentacule à la mort",
["ENEMY_ACOLYTE_NAME"] = "Acolyte du Culte",
["ENEMY_ACOLYTE_SPECIAL"] = "Fait apparaître un tentacule à la mort",
["ENEMY_ACOLYTE_TENTACLE_DESCRIPTION"] = "En dernier recours, les Acolytes sacrifient leur vie à l'Inspecteur, engendrant des tentacules mortels.",
["ENEMY_ACOLYTE_TENTACLE_EXTRA"] = "- Apparaît à partir des Acolytes morts",
["ENEMY_ACOLYTE_TENTACLE_NAME"] = "Tentacule d'Acolyte",
["ENEMY_AMALGAM_DESCRIPTION"] = "Monstruosités faites à la fois de chair et du sol du Vide d'Au-delà. Les Béhémoths répandent la peur malgré leur lenteur à traverser le champ de bataille.",
["ENEMY_AMALGAM_EXTRA"] = "- Mini-boss\n- Explose quand il meurt",
["ENEMY_AMALGAM_NAME"] = "Béhémoth de Chair",
["ENEMY_ANIMATED_ARMOR_DESCRIPTION"] = "Relique usée de batailles passées, désormais possédée par des spectres qui les entraînent dans la mêlée.",
["ENEMY_ANIMATED_ARMOR_EXTRA"] = "- Lorsqu'elle est vaincue, elle peut être réanimée par un spectre",
["ENEMY_ANIMATED_ARMOR_NAME"] = "Armure animée",
["ENEMY_ARMORED_NIGHTMARE_DESCRIPTION"] = "Revêtus d'armure grâce à la magie du culte, ces Nightmares se lancent tête la première dans la bataille",
["ENEMY_ARMORED_NIGHTMARE_EXTRA"] = "- Haute armure\n- Se transforme en Nightmare lorsqu'il est vaincu",
["ENEMY_ARMORED_NIGHTMARE_NAME"] = "Cauchemar Lié",
["ENEMY_ARMORED_NIGHTMARE_SPECIAL"] = "Se transforme en Nightmare lorsqu'il est vaincu.",
["ENEMY_ASH_SPIRIT_DESCRIPTION"] = "Des esprits puissants transformés en monstres terrifiants, nés de la lave, des cendres et du chagrin.",
["ENEMY_ASH_SPIRIT_EXTRA"] = "- Santé élevée\n- Armure élevée\n- Régénère sa santé sur un sol enflammé",
["ENEMY_ASH_SPIRIT_NAME"] = "Ash Spirit",
["ENEMY_BALLOONING_SPIDER_DESCRIPTION"] = "Araignées rapides et furtives, douées pour éviter les ennuis.",
["ENEMY_BALLOONING_SPIDER_EXTRA"] = "- Commence à voler lorsqu’elle est acculée\n- Armure moyenne",
["ENEMY_BALLOONING_SPIDER_FLYER_DESCRIPTION"] = "Araignées rapides et furtives, douées pour éviter les ennuis.",
["ENEMY_BALLOONING_SPIDER_FLYER_EXTRA"] = "- Commence à voler lorsqu’elle est acculée\n- Armure moyenne",
["ENEMY_BALLOONING_SPIDER_FLYER_NAME"] = "Araignée Ballon",
["ENEMY_BALLOONING_SPIDER_NAME"] = "Araignée Ballon",
["ENEMY_BANE_WOLF_DESCRIPTION"] = "Loups pervertis qui traquent ceux qui sont trop lents pour les voir arriver.",
["ENEMY_BANE_WOLF_EXTRA"] = "- Se déplace plus vite chaque fois qu'il reçoit des dégâts",
["ENEMY_BANE_WOLF_NAME"] = "Loup maudit",
["ENEMY_BEAR_VANGUARD_DESCRIPTION"] = "Grands, larges et mauvais, ils déchirent leurs ennemis par douzaines.",
["ENEMY_BEAR_VANGUARD_EXTRA"] = "- Haute armure\n- Entre dans une rage quand un Ours meurt à proximité.",
["ENEMY_BEAR_VANGUARD_NAME"] = "Avant-garde Ours",
["ENEMY_BEAR_VANGUARD_SPECIAL"] = "Entre dans un état de frénésie lorsqu'un autre ours à proximité meurt.",
["ENEMY_BEAR_WOODCUTTER_DESCRIPTION"] = "A tendance à dormir pendant son service, mais une fois réveillé, les choses deviennent sérieuses.",
["ENEMY_BEAR_WOODCUTTER_EXTRA"] = "- Haute armure\n- Devient enragé lorsqu'un ours meurt à proximité",
["ENEMY_BEAR_WOODCUTTER_NAME"] = "Bûcheron Ours",
["ENEMY_BIG_TERRACOTA_DESCRIPTION"] = "Une masse de boue anthropomorphe née de la fusion de plusieurs âmes animées par une intention meurtrière.",
["ENEMY_BIG_TERRACOTA_EXTRA"] = "- Mêlée",
["ENEMY_BIG_TERRACOTA_NAME"] = "Leurre Illusoire de Monstre",
["ENEMY_BLAZE_RAIDER_DESCRIPTION"] = "Capitaines fiers et costauds, initiés de la Voie du Feu, maniant des lances-serpents pour déjouer leurs ennemis.",
["ENEMY_BLAZE_RAIDER_EXTRA"] = "- Faible armure\n- Attaque spéciale sur sol en feu",
["ENEMY_BLAZE_RAIDER_NAME"] = "Blaze Raider",
["ENEMY_BLINKER_DESCRIPTION"] = "Avec leur regard menaçant et leurs ailes semblables à celles d'une chauve-souris, les Clignoteurs chassent les ennemis imprudents.",
["ENEMY_BLINKER_EXTRA"] = "- Étourdit les unités du joueur",
["ENEMY_BLINKER_NAME"] = "Clignoteur du Vide",
["ENEMY_BLINKER_SPECIAL"] = "Étourdit les unités de joueur",
["ENEMY_BOSS_BULL_KING_DESCRIPTION"] = "Un chef impitoyable et autoritaire, vétéran de guerre et stratège pragmatique. Autrefois frère d’armes de Sun Wukong, le Roi Démon prévoit maintenant de voler les sphères célestes de son rival. Réputé pour sa force immense, sa rancune tenace et ses talents martiaux.",
["ENEMY_BOSS_BULL_KING_EXTRA"] = "-Armure élevée\n- Résistance magique élevée\n- Étourdissement de grande zone sur les unités et les tours",
["ENEMY_BOSS_BULL_KING_NAME"] = "Bull Demon King",
["ENEMY_BOSS_CORRUPTED_DENAS_NAME"] = "Corrupted Denas",
["ENEMY_BOSS_CROCS_2_NAME"] = "Venin Abominor",
["ENEMY_BOSS_CROCS_3_NAME"] = "Feu Abominor",
["ENEMY_BOSS_CROCS_NAME"] = "Abominor",
["ENEMY_BOSS_CULT_LEADER_NAME"] = "Prophétesse Mydrias",
["ENEMY_BOSS_DEFORMED_GRYMBEARD_NAME"] = "Grymbeard Déformé",
["ENEMY_BOSS_GRYMBEARD_NAME"] = "Grymbeard",
["ENEMY_BOSS_MACHINIST_NAME"] = "Grymbeard",
["ENEMY_BOSS_NAVIRA_NAME"] = "Navira",
["ENEMY_BOSS_OVERSEER_NAME"] = "L'Intendant",
["ENEMY_BOSS_PIG_NAME"] = "Goregrind",
["ENEMY_BOSS_PRINCESS_IRON_FAN_CLONE_NAME"] = "Clone de la Princesse Éventail de Fer",
["ENEMY_BOSS_PRINCESS_IRON_FAN_NAME"] = "Princess Iron Fan",
["ENEMY_BOSS_REDBOY_TEEN_NAME"] = "Garçon Rouge",
["ENEMY_BOSS_SPIDER_QUEEN_NAME"] = "Mygale",
["ENEMY_BRUTE_WELDER_DESCRIPTION"] = "Ces ouvriers utilisent leurs torches sur les ennemis sans provocation.",
["ENEMY_BRUTE_WELDER_EXTRA"] = "- Bloque une tour lorsqu'il est tué",
["ENEMY_BRUTE_WELDER_NAME"] = "Soudeur Brutal",
["ENEMY_BURNING_TREANT_DESCRIPTION"] = "Créatures de bois aux intentions malveillantes, nées au cœur d'une forêt en feu.",
["ENEMY_BURNING_TREANT_EXTRA"] = "- Dégâts de zone\n- Laisse un sol enflammé à l'attaque",
["ENEMY_BURNING_TREANT_NAME"] = "Burning Treant",
["ENEMY_CITIZEN_1_DESCRIPTION"] = "Sinistres pêcheurs au service de la Princesse, s’infiltrant dans le marché noir.",
["ENEMY_CITIZEN_1_EXTRA"] = "- Faible",
["ENEMY_CITIZEN_1_NAME"] = "Vieux poissonnier",
["ENEMY_CITIZEN_2_DESCRIPTION"] = "Funestes pêcheurs au service de la Princesse, ils se faufilent par le marché noir.",
["ENEMY_CITIZEN_2_EXTRA"] = "- Faible",
["ENEMY_CITIZEN_2_NAME"] = "Pêcheur de Blackwater",
["ENEMY_CITIZEN_3_DESCRIPTION"] = "Sinistres pêcheurs au service de la Princesse, s’infiltrant dans le marché noir.",
["ENEMY_CITIZEN_3_EXTRA"] = "- Faible",
["ENEMY_CITIZEN_3_NAME"] = "Contrebandier d’Encre",
["ENEMY_CITIZEN_4_DESCRIPTION"] = "Sinistres pêcheurs au service de la Princesse, s’infiltrant dans le marché noir.",
["ENEMY_CITIZEN_4_EXTRA"] = "- Faible",
["ENEMY_CITIZEN_4_NAME"] = "Pillardeur des marées",
["ENEMY_COMMON_CLONE_DESCRIPTION"] = "Pas remarquable, pas spécial, tout comme l'original.",
["ENEMY_COMMON_CLONE_EXTRA"] = "- Avance sans réfléchir",
["ENEMY_COMMON_CLONE_NAME"] = "Clone",
["ENEMY_CORRUPTED_ELF_DESCRIPTION"] = "Elfes réanimés qui chassent les ennemis à distance. Même dans la mort, ils restent d'une efficacité redoutable.",
["ENEMY_CORRUPTED_ELF_EXTRA"] = "- Invoque un spectre à sa mort",
["ENEMY_CORRUPTED_ELF_NAME"] = "Rôdeur revenant",
["ENEMY_CORRUPTED_STALKER_DESCRIPTION"] = "Cloud Stalkers apprivoisés par les Acolytes, ils servent maintenant de montures pour le Culte.",
["ENEMY_CORRUPTED_STALKER_EXTRA"] = "- Volant",
["ENEMY_CORRUPTED_STALKER_NAME"] = "Chasseur Apprivoisé",
["ENEMY_CORRUPTED_STALKER_SPECIAL"] = "Volant",
["ENEMY_CROCS_BASIC_DESCRIPTION"] = "Fier guerrier Crok, encore au début de sa vie et à quelques calories de se transformer en la machine à tuer qu'il sait pouvoir être. ",
["ENEMY_CROCS_BASIC_EGG_DESCRIPTION"] = "Nouvellement nés et imparables sur leurs pieds, \"ils grandissent si vite\" était une phrase inventée grâce à ces petits pleins de surprises. ",
["ENEMY_CROCS_BASIC_EGG_EXTRA"] = "- Inblocable\n- Faible Armure\n- Se transforme en un Gator après quelques secondes ",
["ENEMY_CROCS_BASIC_EGG_NAME"] = "Crokinder ",
["ENEMY_CROCS_BASIC_EXTRA"] = "- Mêlée ",
["ENEMY_CROCS_BASIC_NAME"] = "Gator",
["ENEMY_CROCS_EGG_SPAWNER_DESCRIPTION"] = "Ce crok transporte un nid à problèmes ! Tous les quelques pas, elle pond des œufs qui éclosent en une frénésie de crokinders. Une nurserie mobile, mais avec du mordant !",
["ENEMY_CROCS_EGG_SPAWNER_EXTRA"] = "- Fait apparaître des Crokinders sur le Chemin",
["ENEMY_CROCS_EGG_SPAWNER_NAME"] = "Gator nicheur",
["ENEMY_CROCS_FLIER_DESCRIPTION"] = "Des Croks rusés qui, dans leur mépris de l'évolution naturelle, se sont forgés leurs propres ailes pour obtenir un avantage aérien.",
["ENEMY_CROCS_FLIER_EXTRA"] = "- Volant",
["ENEMY_CROCS_FLIER_NAME"] = "Crok ailé",
["ENEMY_CROCS_HYDRA_DESCRIPTION"] = "Deux têtes valent mieux qu'une et les hydres le prouvent. Il existe un vieux mythe sur une bête à trois têtes comme celle-ci, mais c'est probablement un mensonge.",
["ENEMY_CROCS_HYDRA_EXTRA"] = "- Haute santé\n- Dégâts élevés\n- Haute résistance magique\n- Fait apparaître une troisième tête à sa mort\n- Crache du poison au sol",
["ENEMY_CROCS_HYDRA_NAME"] = "Hydre",
["ENEMY_CROCS_QUICKFEET_GATOR_NAME"] = "Pieds Rapides",
["ENEMY_CROCS_RANGED_DESCRIPTION"] = "Rapides et furtifs, des lézards chasseurs qui s'occupent de leurs ennemis à l'aide de frondes à longue portée. ",
["ENEMY_CROCS_RANGED_EXTRA"] = "- Rapide\n- À distance ",
["ENEMY_CROCS_RANGED_NAME"] = "Lizardshot ",
["ENEMY_CROCS_SHAMAN_DESCRIPTION"] = "Êtres magiques essentiels pour les Croks. Pour une race à sang froid, prévoir les caprices du ciel est vital.",
["ENEMY_CROCS_SHAMAN_EXTRA"] = "- Dégâts Magiques à Distance\n- Haute Résistance Magique\n- Soigne les autres crocos\n- Assomme les tours",
["ENEMY_CROCS_SHAMAN_NAME"] = "Crok Sage",
["ENEMY_CROCS_TANK_DESCRIPTION"] = "Piliers des forces de Croks, avec la mentalité \"une bonne défense est la meilleure attaque\", ils ont volé des coquilles et ont commencé à les utiliser comme ils le pensaient être la meilleure manière.",
["ENEMY_CROCS_TANK_EXTRA"] = "- Santé élevée\n- Armure élevée\n- Tourne sur lui-même lorsqu'il est bloqué",
["ENEMY_CROCS_TANK_NAME"] = "Tankzard",
["ENEMY_CRYSTAL_GOLEM_DESCRIPTION"] = "Imprégnées de magie d'un autre monde provenant de leurs cristaux, ces effigies de pierre sont presque inarrêtables.",
["ENEMY_CRYSTAL_GOLEM_EXTRA"] = "- Mini-boss\n- Armure très élevée",
["ENEMY_CRYSTAL_GOLEM_NAME"] = "Golem de cristal",
["ENEMY_CULTBROOD_DESCRIPTION"] = "Moitié araignée, moitié abomination sectaire, ces créatures se jettent dans la bataille sans peur ni pitié.",
["ENEMY_CULTBROOD_EXTRA"] = "- Rapide \n- Attaque empoisonnée \n- Si un ennemi meurt empoisonné, il engendre un autre Cultbrood",
["ENEMY_CULTBROOD_NAME"] = "Cultbrood",
["ENEMY_CUTTHROAT_RAT_DESCRIPTION"] = "Fourbes et sournois par nature, les rats sont des assassins et des infiltrateurs aiguisés.",
["ENEMY_CUTTHROAT_RAT_EXTRA"] = "- Vitesse rapide\n- Devient invisible après avoir frappé un ennemi.",
["ENEMY_CUTTHROAT_RAT_NAME"] = "Rat Coupe-Gorge",
["ENEMY_CUTTHROAT_RAT_SPECIAL"] = "Devient invisible après avoir frappé un ennemi.",
["ENEMY_DARKSTEEL_ANVIL_DESCRIPTION"] = "La réponse naine aux tambours de guerre. Plus ils sont lourds, plus ils chantent fort.",
["ENEMY_DARKSTEEL_ANVIL_EXTRA"] = "- Accorde des bonus d'armure et de vitesse aux ennemis",
["ENEMY_DARKSTEEL_ANVIL_NAME"] = "Enclume d'Acier Sombre",
["ENEMY_DARKSTEEL_FIST_DESCRIPTION"] = "Amélioré mécaniquement pour plier le métal, mais préfère frapper les autres.",
["ENEMY_DARKSTEEL_FIST_EXTRA"] = "- L'attaque spéciale étourdit les unités du joueur",
["ENEMY_DARKSTEEL_FIST_NAME"] = "Poing d'Acier Sombre",
["ENEMY_DARKSTEEL_GUARDIAN_DESCRIPTION"] = "Des combinaisons de combat robustes opérées par des guerriers nains et alimentées par des moteurs ardents. Parés pour tuer.",
["ENEMY_DARKSTEEL_GUARDIAN_EXTRA"] = "- Mini-boss\n- Devient frénétique à faible santé",
["ENEMY_DARKSTEEL_GUARDIAN_NAME"] = "Gardien d'Acier Sombre",
["ENEMY_DARKSTEEL_HAMMERER_DESCRIPTION"] = "Des guerriers aussi bruts que leur arme de prédilection.",
["ENEMY_DARKSTEEL_HAMMERER_EXTRA"] = " ",
["ENEMY_DARKSTEEL_HAMMERER_NAME"] = "Marteleur d'Acier Sombre",
["ENEMY_DARKSTEEL_HULK_DESCRIPTION"] = "Grincheux et avec de l'acier en fusion dans les veines, c'est le maximum de poids que peuvent atteindre les nains.",
["ENEMY_DARKSTEEL_HULK_EXTRA"] = "- Mini-boss\n- À faible santé, charge sur le chemin en infligeant des dégâts",
["ENEMY_DARKSTEEL_HULK_NAME"] = "Colosse d'Acier Sombre",
["ENEMY_DARKSTEEL_SHIELDER_DESCRIPTION"] = "Protégés par d'immenses boucliers, ils repoussent les ennemis en avançant.",
["ENEMY_DARKSTEEL_SHIELDER_EXTRA"] = "- Se transforme en Marteleur une fois vaincu",
["ENEMY_DARKSTEEL_SHIELDER_NAME"] = "Bouclier d’acier sombre",
["ENEMY_DEATHWOOD_DESCRIPTION"] = "Weirdwoods corrompus par les sombres spectres qui errent maintenant dans la forêt, semant le chaos.",
["ENEMY_DEATHWOOD_EXTRA"] = "- Mini-boss\n- Lance un gland maudit qui inflige des dégâts dans une zone",
["ENEMY_DEATHWOOD_NAME"] = "Bois de la mort",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_DESCRIPTION"] = "Le résultat de l'arrogance débridée de Grymbeard. Sa puissance mentale n'a d'égal que sa hideur.",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_EXTRA"] = "- Volant\n- Bouclier à haute résistance magique",
["ENEMY_DEFORMED_GRYMBEARD_CLONE_NAME"] = "Clone Déformé",
["ENEMY_DEMON_MINOTAUR_DESCRIPTION"] = "Créatures impitoyables, mi-humaines mi-taureaux, venues des enfers.",
["ENEMY_DEMON_MINOTAUR_EXTRA"] = "- Dégâts élevés\n- Attaque de charge\n- Ne peut pas être tué instantanément",
["ENEMY_DEMON_MINOTAUR_NAME"] = "Demon Minotaur",
["ENEMY_DOOM_BRINGER_DESCRIPTION"] = "Guerriers redoutables apportant la ruine à tout prix.",
["ENEMY_DOOM_BRINGER_EXTRA"] = "- Étourdit les tours",
["ENEMY_DOOM_BRINGER_NAME"] = "Doombringer",
["ENEMY_DRAINBROOD_DESCRIPTION"] = "Une araignée ancienne avec une morsure mortelle. Certains spéculent qu’elle est la principale responsable de la cristallisation des autres araignées.",
["ENEMY_DRAINBROOD_EXTRA"] = "- Cristallise les ennemis tout en drainant leur vie",
["ENEMY_DRAINBROOD_NAME"] = "Araignée Voleuse de Vie",
["ENEMY_DREADEYE_VIPER_DESCRIPTION"] = "Enrobant leurs flèches de leur propre venin, ils sont des ennemis mortels à distance.",
["ENEMY_DREADEYE_VIPER_EXTRA"] = "- Faible résistance magique\n- Attaques empoisonnées",
["ENEMY_DREADEYE_VIPER_NAME"] = "Vipère Œil de Terreur",
["ENEMY_DREADEYE_VIPER_SPECIAL"] = "Les flèches appliquent du poison à la cible.",
["ENEMY_DUST_CRYPTID_DESCRIPTION"] = "Autrefois une merveille à voir, maintenant une vision hantée pour ceux qui s'aventurent trop loin.",
["ENEMY_DUST_CRYPTID_EXTRA"] = "- Volant\n- Laisse un nuage de pollen qui rend les ennemis invulnérables aux dégâts",
["ENEMY_DUST_CRYPTID_NAME"] = "Cryptide de poussière",
["ENEMY_EVOLVING_SCOURGE_DESCRIPTION"] = "Ils peuvent sembler presque câlins à première vue, mais si le Fléau se nourrit de proies tombées, les choses se gâteront rapidement.",
["ENEMY_EVOLVING_SCOURGE_EXTRA"] = "- Mange des unités tombées pour évoluer vers une forme plus forte\n - Évolue instantanément sous sa forme finale lorsqu'affecté par Glare",
["ENEMY_EVOLVING_SCOURGE_NAME"] = "Fléau Évolutif",
["ENEMY_FAN_GUARD_DESCRIPTION"] = "Guerrières fortes et très polyvalentes, capables d’infliger la douleur tout en se protégeant avec leurs éventails magiques.",
["ENEMY_FAN_GUARD_EXTRA"] = "- Possède une armure moyenne et une résistance magique tant qu’il n’est pas bloqué.",
["ENEMY_FAN_GUARD_NAME"] = "Fan Guard",
["ENEMY_FIRE_FOX_DESCRIPTION"] = "Foxes insaisissables et adorables nées du feu. Trop rapides et instables pour être apprivoisées.",
["ENEMY_FIRE_FOX_EXTRA"] = "- Faible résistance magique\n- Plus rapide sur sol enflammé\n- Laisse un sol enflammé à la mort",
["ENEMY_FIRE_FOX_NAME"] = "Fire Fox",
["ENEMY_FIRE_PHOENIX_DESCRIPTION"] = "Creatures volants mythiques qui se nourrissent du feu lui-même. Ils vivent et meurent dans une flamme ardente.",
["ENEMY_FIRE_PHOENIX_EXTRA"] = "- Volant\n- Laisse un sol enflammé à la mort",
["ENEMY_FIRE_PHOENIX_NAME"] = "Zhuque",
["ENEMY_FLAME_GUARD_DESCRIPTION"] = "En quête de l’approbation de leurs maîtres, ces disciples de bas rang excellent avec de petites lames.",
["ENEMY_FLAME_GUARD_EXTRA"] = "- Attaque spéciale sur sol enflammé",
["ENEMY_FLAME_GUARD_NAME"] = "Garde de Flammes",
["ENEMY_GALE_WARRIOR_DESCRIPTION"] = "Gracieuses et élégantes, ces guerrières ont été choisies par leur princesse et sont prêtes à mourir pour elle.",
["ENEMY_GALE_WARRIOR_EXTRA"] = "- Armure moyenne\n- Provoque un saignement tous les 3 attaques",
["ENEMY_GALE_WARRIOR_NAME"] = "Gale Warrior",
["ENEMY_GLAREBROOD_CRYSTAL_NAME"] = "Crystal de Glarebrood",
["ENEMY_GLARELING_DESCRIPTION"] = "Si on ne les surveille pas, ces créatures dociles peuvent submerger même l'armée la plus solide.",
["ENEMY_GLARELING_EXTRA"] = "- Haute vitesse",
["ENEMY_GLARELING_NAME"] = "Éblouissement",
["ENEMY_GLARENWARDEN_DESCRIPTION"] = "Ces araignées abominables sont le fruit de la fusion des Glarebroods, les rendant plus fortes et plus résistantes que jamais.",
["ENEMY_GLARENWARDEN_EXTRA"] = "- Armure élevée\n- Vol de vie en attaque",
["ENEMY_GLARENWARDEN_NAME"] = "Gardien de l’Éclat",
["ENEMY_GOLDEN_EYED_DESCRIPTION"] = "Une bête colossale dont le rugissement inspire la peur à ses ennemis.",
["ENEMY_GOLDEN_EYED_EXTRA"] = "- Miniboss\n- Étourdit les unités dans une zone lorsqu'il est bloqué\n- Étourdit les tours",
["ENEMY_GOLDEN_EYED_NAME"] = "Golden-Eyed Beast",
["ENEMY_HARDENED_HORROR_DESCRIPTION"] = "Cette race d'Horreurs a des lames aiguisées en guise de mains et taillera un chemin à travers leurs ennemis.",
["ENEMY_HARDENED_HORROR_EXTRA"] = "- Roule à grande vitesse et ne peut pas être bloqué en étant affecté par Glare.",
["ENEMY_HARDENED_HORROR_NAME"] = "Horreur Grifferreur",
["ENEMY_HELLFIRE_WARLOCK_DESCRIPTION"] = "Sorciers extrêmement dangereux, experts en invocation de créatures et de flammes venues des profondeurs de l’enfer.",
["ENEMY_HELLFIRE_WARLOCK_EXTRA"] = "- Lance des boules de feu\n- Invoque un Renard à Neuf Queues",
["ENEMY_HELLFIRE_WARLOCK_NAME"] = "Hellfire Warlock",
["ENEMY_HOG_INVADER_DESCRIPTION"] = "Sales et désorganisés fauteurs de troubles. La majeure partie de l'armée des bêtes sauvages.",
["ENEMY_HOG_INVADER_EXTRA"] = "- Faible HP",
["ENEMY_HOG_INVADER_NAME"] = "Envahisseur Sanglier",
["ENEMY_HYENA5_DESCRIPTION"] = "Combattants macabres avec une prédilection pour se repaître de leurs ennemis tombés.",
["ENEMY_HYENA5_EXTRA"] = "- Armure moyenne\n- Se soigne en mangeant des unités de joueur tombées",
["ENEMY_HYENA5_NAME"] = "Hyène de Dent Pourri",
["ENEMY_HYENA5_SPECIAL"] = "Se soigne en mangeant des ennemis bloquants tués.",
["ENEMY_KILLERTILE_DESCRIPTION"] = "Puissants destructeurs, des années d'expérience de combat (ou un poulet) leur ont laissé une morsure forte et mortelle. ",
["ENEMY_KILLERTILE_EXTRA"] = "- Haute Santé\nHauts Dégâts",
["ENEMY_KILLERTILE_NAME"] = "Killertile ",
["ENEMY_LESSER_EYE_DESCRIPTION"] = "Yeux maléfiques qui flottent au-dessus du champ de bataille, agissant en tant qu'éclaireurs des Répandeurs Vils.",
["ENEMY_LESSER_EYE_EXTRA"] = "- Volant",
["ENEMY_LESSER_EYE_NAME"] = "Œil Inférieur",
["ENEMY_LESSER_SISTER_DESCRIPTION"] = "Avec leur magie malveillante, les Sœurs Tordues facilitent l'entrée des Cauchemars dans le monde physique. ",
["ENEMY_LESSER_SISTER_EXTRA"] = "- Haute résistance magique\n- Invoque des Cauchemars ",
["ENEMY_LESSER_SISTER_NAME"] = "Soeur Tordue",
["ENEMY_LESSER_SISTER_NIGHTMARE_DESCRIPTION"] = "Ombres éthérées tissées du livre de chants des Sœurs du Culte",
["ENEMY_LESSER_SISTER_NIGHTMARE_EXTRA"] = "- Ne peut être ciblé à moins qu'il ne soit bloqué par des unités de mêlée",
["ENEMY_LESSER_SISTER_NIGHTMARE_NAME"] = "Cauchemar",
["ENEMY_LESSER_SISTER_SPECIAL"] = "Invoque des Cauchemars",
["ENEMY_MACHINIST_DESCRIPTION"] = "Obsédé par les rouages et les moteurs, ce nain vit pour l'automatisation industrielle et la guerre.",
["ENEMY_MACHINIST_EXTRA"] = "- Gère une ligne d'assemblage qui génère des Sentinelles",
["ENEMY_MACHINIST_NAME"] = "Grymbeard",
["ENEMY_MAD_TINKERER_DESCRIPTION"] = "Les bricoleurs ne se soucient de rien d'autre que de construire des choses à partir de déchets.",
["ENEMY_MAD_TINKERER_EXTRA"] = "- Crée des Drones en utilisant la ferraille laissée par d'autres unités",
["ENEMY_MAD_TINKERER_NAME"] = "Bricoleur Fou",
["ENEMY_MINDLESS_HUSK_DESCRIPTION"] = "En raison de leur apparence, les Husks semblent être des ennemis faibles, pourtant chacun d'entre eux porte une surprise sur le champ de bataille.",
["ENEMY_MINDLESS_HUSK_EXTRA"] = "- Produit un Glareling à la mort",
["ENEMY_MINDLESS_HUSK_NAME"] = "Coquille Sans Esprit",
["ENEMY_NINE_TAILED_FOX_DESCRIPTION"] = "Créatures mystérieuses, aussi belles que puissantes. Elles balayeront les ennemis tel un brasier déchaîné.",
["ENEMY_NINE_TAILED_FOX_EXTRA"] = "- Résistance magique moyenne\n- Se téléporte vers l’avant, étourdissant les ennemis à l’arrivée\n- Dégâts de zone",
["ENEMY_NINE_TAILED_FOX_NAME"] = "Renard à Neuf Queues",
["ENEMY_NOXIOUS_HORROR_DESCRIPTION"] = "Créatures amphibies qui crachent de la bile venimeuse sur leur proie. Aussi dangereuses de près.",
["ENEMY_NOXIOUS_HORROR_EXTRA"] = "- Gagne une résistance magique et émet une aura toxique lorsqu'affecté par Glare.",
["ENEMY_NOXIOUS_HORROR_NAME"] = "Cracheur Nocif",
["ENEMY_PALACE_GUARD_DESCRIPTION"] = "Apprentis peu talentueux dont la seule motivation est de satisfaire les désirs de leur Princesse.",
["ENEMY_PALACE_GUARD_EXTRA"] = "- Mêlée\n- Faible armure",
["ENEMY_PALACE_GUARD_NAME"] = "Garde du Palais",
["ENEMY_PUMPKIN_WITCH_DESCRIPTION"] = "Ennemi transformé en citrouillette. Facile à écraser.",
["ENEMY_PUMPKIN_WITCH_EXTRA"] = "- Imblocable",
["ENEMY_PUMPKIN_WITCH_FLYING_DESCRIPTION"] = "Ennemi transformé en citrouillette. Facile à écraser.",
["ENEMY_PUMPKIN_WITCH_FLYING_EXTRA"] = "- Imblocable",
["ENEMY_PUMPKIN_WITCH_FLYING_NAME"] = "Citrouillette",
["ENEMY_PUMPKIN_WITCH_NAME"] = "Citrouillette",
["ENEMY_QIONGQI_DESCRIPTION"] = "Lions volants féroces qui attaquent avec la puissance de la foudre. Les rois de la tempête.",
["ENEMY_QIONGQI_EXTRA"] = "- Volant\n- Dégâts très élevés\n- Résistance magique moyenne",
["ENEMY_QIONGQI_NAME"] = "Qiongqi",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_DESCRIPTION"] = "Après des années à livrer des poulets à leurs frères, ils sont devenus tellement rapides qu'ils oublient parfois même d'apporter le poulet.",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_EXTRA"] = "- Rapide\n- À distance\n- Il peut livrer une cuisse de poulet à un Gator, le faisant évoluer",
["ENEMY_QUICKFEET_GATOR_CHICKEN_LEG_NAME"] = "Pieds Rapides",
["ENEMY_QUICKFEET_GATOR_DESCRIPTION"] = "Après des années à livrer des poulets à leurs frères, ils sont devenus tellement rapides qu'ils oublient parfois même d'apporter le poulet.",
["ENEMY_QUICKFEET_GATOR_EXTRA"] = "- Rapide\n- À distance\n- Il peut livrer une cuisse de poulet à un Gator, le faisant évoluer",
["ENEMY_QUICKFEET_GATOR_NAME"] = "Pieds Rapides",
["ENEMY_REVENANT_HARVESTER_DESCRIPTION"] = "Prêtresses d'autrefois, maintenant errant dans la forêt, répandant leur influence à travers les spectres.",
["ENEMY_REVENANT_HARVESTER_EXTRA"] = "- Transforme les spectres à proximité en Moissonneurs revenants",
["ENEMY_REVENANT_HARVESTER_NAME"] = "Moissonneur revenant",
["ENEMY_REVENANT_SOULCALLER_DESCRIPTION"] = "Mages elfes ayant souffert de la magie de la mort, se levant de la terre pour invoquer les spectres des défunts.",
["ENEMY_REVENANT_SOULCALLER_EXTRA"] = "- Désactive les tours\n- Invoque des spectres",
["ENEMY_REVENANT_SOULCALLER_NAME"] = "Appelâme revenant",
["ENEMY_RHINO_DESCRIPTION"] = "Un bélier vivant qui piétine sans égard à travers le champ de bataille.",
["ENEMY_RHINO_EXTRA"] = "- Mini-boss\n- Charge vers les ennemis",
["ENEMY_RHINO_NAME"] = "Rhinocéros Ravageur",
["ENEMY_RHINO_SPECIAL"] = "Charge vers les ennemis.",
["ENEMY_ROLLING_SENTRY_DESCRIPTION"] = "Une fois abattus, ils continuent de chasser au sol.",
["ENEMY_ROLLING_SENTRY_EXTRA"] = "- Se transforme en ferraille lorsqu'il est tué\n- À distance",
["ENEMY_ROLLING_SENTRY_NAME"] = "Sentinelle Roulante",
["ENEMY_SCRAP_DRONE_DESCRIPTION"] = "Assemblé grossièrement avec pour seul but d'embêter les troupes.",
["ENEMY_SCRAP_DRONE_EXTRA"] = "- Volant",
["ENEMY_SCRAP_DRONE_NAME"] = "Drone de Ferraille",
["ENEMY_SCRAP_SPEEDSTER_DESCRIPTION"] = "Bruyant et agaçant, avec un besoin de vitesse.",
["ENEMY_SCRAP_SPEEDSTER_EXTRA"] = "- Se transforme en ferraille lorsqu'il est tué",
["ENEMY_SCRAP_SPEEDSTER_NAME"] = "Vitesse de Ferraille",
["ENEMY_SKUNK_BOMBARDIER_DESCRIPTION"] = "Portant leurs toxines naturelles à un autre niveau, les mouffettes sèment le désordre dans les lignes ennemies.",
["ENEMY_SKUNK_BOMBARDIER_EXTRA"] = "- Vitesse faible\n- Résistance magique moyenne\n- Les attaques affaiblissent les unités du joueur\n- Explose à sa mort",
["ENEMY_SKUNK_BOMBARDIER_NAME"] = "Bombardier Putois",
["ENEMY_SKUNK_BOMBARDIER_SPECIAL"] = "Les attaques affaiblissent les unités du joueur. Explose à sa mort, infligeant des dégâts.",
["ENEMY_SMALL_STALKER_DESCRIPTION"] = "Corrompus par la magie du Culte, ces Cloud Stalkers se téléportent sur le champ de bataille en semant le chaos.",
["ENEMY_SMALL_STALKER_EXTRA"] = "- Se téléporte vers l'avant lorsqu'il est attaqué",
["ENEMY_SMALL_STALKER_NAME"] = "Stalker Corrompu",
["ENEMY_SMALL_STALKER_SPECIAL"] = "Se téléporte sur une courte distance, esquivant les attaques.",
["ENEMY_SPECTER_DESCRIPTION"] = "Esclaves au-delà de la décomposition terrestre, condamnés à hanter les vivants.",
["ENEMY_SPECTER_EXTRA"] = "- Peut interagir avec d'autres ennemis et éléments",
["ENEMY_SPECTER_NAME"] = "Spectre",
["ENEMY_SPIDEAD_DESCRIPTION"] = "Descendants directs de la Reine Araignée Mygale, ces araignées trouvent toujours un moyen d’être agaçantes, même après la mort.",
["ENEMY_SPIDEAD_EXTRA"] = "- Résistance magique\n- Génère une toile d’araignée à la mort",
["ENEMY_SPIDEAD_NAME"] = "Fille de Soie",
["ENEMY_SPIDERLING_DESCRIPTION"] = "Araignées améliorées par la magie du Culte. Rapides et furieuses. Elles mordront.",
["ENEMY_SPIDERLING_EXTRA"] = "- Vitesse rapide\n- Faible résistance magique",
["ENEMY_SPIDERLING_NAME"] = "Lueur-Couveuse",
["ENEMY_SPIDER_PRIEST_DESCRIPTION"] = "Enlacés par leur nouveau dieu, les prêtres marchent sur le champ de bataille en brandissant une magie noire.",
["ENEMY_SPIDER_PRIEST_EXTRA"] = "- Résistance magique élevée\n- Se transforme en Gardien de l’Éclat à l’agonie",
["ENEMY_SPIDER_PRIEST_NAME"] = "Prêtre de la Toile",
["ENEMY_SPIDER_SISTER_DESCRIPTION"] = "Fermes croyantes en la Reine Araignée, elles utilisent leur magie pour invoquer ses semblables.",
["ENEMY_SPIDER_SISTER_EXTRA"] = "- Résistance magique\n- Invoque des Glarebroods",
["ENEMY_SPIDER_SISTER_NAME"] = "Sœur Araignée",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_DESCRIPTION"] = "Des doppelgängers d'ombre que Mydrias utilise pour intervenir dans la bataille.",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_EXTRA"] = "- Protège les ennemis des dégâts\n- Piège les tours avec des tentacules sombres",
["ENEMY_STAGE_11_CULT_LEADER_ILLUSION_NAME"] = "Illusion de Mydrias",
["ENEMY_STORM_ELEMENTAL_DESCRIPTION"] = "Élémentaires puissants nés des typhons, de la foudre et de la colère. Un cousin éloigné de l’Esprit de Cendre.",
["ENEMY_STORM_ELEMENTAL_EXTRA"] = "- Armure élevée\n- À distance\n- Étourdit une tour proche à la mort",
["ENEMY_STORM_ELEMENTAL_NAME"] = "Esprit de Tempête",
["ENEMY_STORM_SPIRIT_DESCRIPTION"] = "Petits dragons bondissant à travers les nuages d'orage, esquivant habilement dangers et ennemis.",
["ENEMY_STORM_SPIRIT_EXTRA"] = "- Volant\n- Faible résistance magique\n- Se précipite en avant lorsqu'il est blessé",
["ENEMY_STORM_SPIRIT_NAME"] = "Drakéon de Tempête",
["ENEMY_SURVEILLANCE_SENTRY_DESCRIPTION"] = "Ingénierie naine conçue pour surveiller les ennemis depuis les cieux.",
["ENEMY_SURVEILLANCE_SENTRY_EXTRA"] = "- Volant",
["ENEMY_SURVEILLANCE_SENTRY_NAME"] = "Sentinelle Volante",
["ENEMY_SURVEYOR_HARPY_DESCRIPTION"] = "À la recherche de charogne, les vautours suivent les bêtes sauvages partout.",
["ENEMY_SURVEYOR_HARPY_EXTRA"] = "- En vol",
["ENEMY_SURVEYOR_HARPY_NAME"] = "Vautour Patrouilleur",
["ENEMY_SURVEYOR_HARPY_SPECIAL"] = "Volant.",
["ENEMY_TERRACOTA_DESCRIPTION"] = "Ombres manifestées servant de distraction.",
["ENEMY_TERRACOTA_EXTRA"] = "- Corps à corps",
["ENEMY_TERRACOTA_NAME"] = "Leurre Illusoire",
["ENEMY_TOWER_RAY_SHEEP_DESCRIPTION"] = "Bêêêêêê.",
["ENEMY_TOWER_RAY_SHEEP_EXTRA"] = "- Inblocable",
["ENEMY_TOWER_RAY_SHEEP_FLYING_DESCRIPTION"] = "Bêêêêêê.",
["ENEMY_TOWER_RAY_SHEEP_FLYING_EXTRA"] = "- Volant",
["ENEMY_TOWER_RAY_SHEEP_FLYING_NAME"] = "Mouton volant",
["ENEMY_TOWER_RAY_SHEEP_NAME"] = "Mouton",
["ENEMY_TURTLE_SHAMAN_DESCRIPTION"] = "D'apparence paisible mais d'esprit malveillant, les chamans gardent les bêtes sauvages soignées et prêtes à se battre.",
["ENEMY_TURTLE_SHAMAN_EXTRA"] = "- Vitesse lente\n- HP élevé\n- Haute résistance magique\n- Soigne les unités ennemies",
["ENEMY_TURTLE_SHAMAN_NAME"] = "Chaman Tortue",
["ENEMY_TURTLE_SHAMAN_SPECIAL"] = "Soigne les unités ennemies.",
["ENEMY_TUSKED_BRAWLER_DESCRIPTION"] = "Plus tenaces que les envahisseurs et équipés d'armures bancales. Toujours prêts à se battre.",
["ENEMY_TUSKED_BRAWLER_EXTRA"] = "- Faible armure",
["ENEMY_TUSKED_BRAWLER_NAME"] = "Bagarreur à Défenses",
["ENEMY_UNBLINDED_ABOMINATION_DESCRIPTION"] = "Prêtres totalement corrompus du Culte, connus pour leur sauvagerie au combat.",
["ENEMY_UNBLINDED_ABOMINATION_EXTRA"] = "- Dévore les unités à faible santé",
["ENEMY_UNBLINDED_ABOMINATION_NAME"] = "Abomination du Culte",
["ENEMY_UNBLINDED_ABOMINATION_SPECIAL"] = "Dévore occasionnellement une unité à faible santé",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_DESCRIPTION"] = "Après avoir asservi les elfes, certaines Abominations ont été nommées pour s'assurer que le travail dans les mines se déroule sans encombre.",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_EXTRA"] = "- Doit être tué pour libérer les elfes",
["ENEMY_UNBLINDED_ABOMINATION_STAGE_8_NAME"] = "Superviseur Abomination",
["ENEMY_UNBLINDED_PRIEST_DESCRIPTION"] = "Entre prières et occultisme, les prêtres vont au combat en maniant la magie noire.",
["ENEMY_UNBLINDED_PRIEST_EXTRA"] = "- Haute résistance à la magie\n- Se transforme en Abomination lorsqu'il est près de la mort",
["ENEMY_UNBLINDED_PRIEST_NAME"] = "Prêtre du Culte",
["ENEMY_UNBLINDED_PRIEST_SPECIAL"] = "À faible santé, se transforme en une Abomination.",
["ENEMY_UNBLINDED_SHACKLER_DESCRIPTION"] = "Canalisant de la magie corrompue à travers les cristaux incrustés sur leurs bras, les Enchaîneurs sont des ennemis redoutables au corps à corps",
["ENEMY_UNBLINDED_SHACKLER_EXTRA"] = "- Résistance magique moyenne\n- Désactive les tours à faible santé",
["ENEMY_UNBLINDED_SHACKLER_NAME"] = "Shackler",
["ENEMY_UNBLINDED_SHACKLER_SPECIAL"] = "Lie les tours, les empêchant d'attaquer",
["ENEMY_VILE_SPAWNER_DESCRIPTION"] = "Lançant leurs nombreux yeux volants sur les ennemis, les Répandeurs Vils surveillent toujours dans toutes les directions.",
["ENEMY_VILE_SPAWNER_EXTRA"] = "- Produit des Yeux Inférieurs.",
["ENEMY_VILE_SPAWNER_NAME"] = "Répandeur Vile",
["ENEMY_WATER_SORCERESS_DESCRIPTION"] = "Vieux invocateurs élémentaires maîtrisant le pouvoir de l’eau pour soigner les alliés et vaincre les ennemis à distance.",
["ENEMY_WATER_SORCERESS_EXTRA"] = "- À distance\n- Résistance magique moyenne\n- Soigne les alliés",
["ENEMY_WATER_SORCERESS_NAME"] = "Water Master",
["ENEMY_WATER_SPIRIT_DESCRIPTION"] = "Entités aquatiques sans âme déferlant en vagues incessantes, ravageant les rivages avec fureur.",
["ENEMY_WATER_SPIRIT_EXTRA"] = "- Faible résistance magique\n- Peut apparaître à partir de l'eau",
["ENEMY_WATER_SPIRIT_NAME"] = "Water Spirit",
["ENEMY_WATER_SPIRIT_SPAWNLESS_DESCRIPTION"] = "Entités aquatiques sans âme déferlant en vagues incessantes, ravageant les rivages avec fureur.",
["ENEMY_WATER_SPIRIT_SPAWNLESS_EXTRA"] = "- Faible résistance magique\n- Peut apparaître à partir de l'eau",
["ENEMY_WATER_SPIRIT_SPAWNLESS_NAME"] = "Water Spirit",
["ENEMY_WUXIAN_DESCRIPTION"] = "Puissants et durables, ces sorciers anéantissent leurs ennemis avec la magie.",
["ENEMY_WUXIAN_EXTRA"] = "- À distance\n- Armure moyenne\n- Attaque spéciale sur sol enflammé",
["ENEMY_WUXIAN_NAME"] = "Wuxian",
["ERROR_MESSAGE_GENERIC"] = "Oups ! Quelque chose cloche.",
["Earn huge bonus points and gold by calling waves earlier!"] = "Gagne une grande quantité de points et d'or bonus en appelant les vagues plus tôt !",
["Encyclopedia"] = "Encyclopédie",
["Enemies"] = "Ennemis",
["Extreme"] = "Extrême",
["FIRST_WEEK_PACK"] = "Cadeau",
["Face an endless unrelenting enemy force and try to defeat as many as possible to comete for the best score!"] = "Affronte des vagues incessantes d'ennemis et essaie d'en vaincre le plus possible pour décrocher le meilleur score !",
["Face an endless unrelenting enemy force and try to defeat as many as possible to compete for the best score!"] = "Affronte des vagues incessantes d'ennemis et essaie d'en vaincre le plus possible pour décrocher le meilleur score !",
["Fast"] = "Rapide",
["For beginners to strategy games!"] = "Pour les débutants dans les jeux de stratégie!",
["GAME_TITLE_KR5"] = "Kingdom Rush 5: Alliance",
["GEMS_BARREL_NAME"] = "TONNEAU DE GEMMES",
["GEMS_CHEST_NAME"] = "COFFRE DE GEMMES",
["GEMS_HANDFUL_NAME"] = "POIGNÉE DE GEMMES",
["GEMS_MOUNTAIN_NAME"] = "MONTAGNE DE GEMMES",
["GEMS_POUCH_NAME"] = "BOURSE DE GEMMES",
["GEMS_WAGON_NAME"] = "WAGONNET DE GEMMES",
["GET_ALL_AWESOME_HEROES"] = "OBTIENS TOUS CES HÉROS GÉNIAUX",
["GET_THIS_AWESOME"] = "OBTIENS CE\nHÉROS GÉNIAL",
["GET_THIS_AWESOME_2"] = "OBTIENS CES\n HÉROS GÉNIAUX",
["GET_THIS_AWESOME_3"] = "OBTIENS CES\n HÉROS GÉNIAUX",
["GIFT_CLAIMED"] = "Cadeau réclamé!",
["GOOGLE_PLAY"] = "GOOGLE PLAY",
["Got it!"] = "Pigé!",
["Great"] = "Super",
["HERO LEVEL UP!"] = "GAIN DE NIVEAU DE HÉROS!",
["HERO ROOM"] = "HÉROS",
["HERO UNLOCKED!"] = "HÉROS DÉBLOQUÉ!",
["HEROES"] = "HÉROS",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_1"] = "Invoque des griffons qui volent au-dessus de la zone pendant %$heroes.hero_bird.ultimate.bird.duration[2]%$ secondes en frappant les ennemis, infligeant %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[2]%$ de dégâts à chaque fois.",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_2"] = "Invoque des griffons qui volent au-dessus de la zone pendant %$heroes.hero_bird.ultimate.bird.duration[3]%$ secondes en frappant les ennemis, infligeant %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[3]%$ de dégâts à chaque fois.",
["HERO_BIRD_BIRDS_OF_PREY_DESCRIPTION_3"] = "Invoque des griffons qui volent au-dessus de la zone pendant %$heroes.hero_bird.ultimate.bird.duration[4]%$ secondes en frappant les ennemis, infligeant %$heroes.hero_bird.ultimate.bird.melee_attack.damage_max[4]%$ de dégâts à chaque fois.",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_DESCRIPTION"] = "Invoque des griffons qui volent au-dessus de la zone en attaquant les ennemis.",
["HERO_BIRD_BIRDS_OF_PREY_MENUBOTTOM_NAME"] = "Oiseaux de Mêlée",
["HERO_BIRD_BIRDS_OF_PREY_TITLE"] = "OISEAUX DE FRAY",
["HERO_BIRD_CLASS"] = "Le Cavalier As",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_1"] = "Lance un explosif qui se répartit sur les ennemis, infligeant %$heroes.hero_bird.cluster_bomb.explosion_damage_min[1]%$ de dégâts chacun et mettant le sol en feu pendant %$heroes.hero_bird.cluster_bomb.fire_duration[1]%$ secondes, brûlant les ennemis pour %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$ de dégâts sur 3 secondes.",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_2"] = "Lance un explosif qui se répartit sur les ennemis, infligeant %$heroes.hero_bird.cluster_bomb.explosion_damage_min[2]%$ de dégâts chacun et mettant le sol en feu pendant %$heroes.hero_bird.cluster_bomb.fire_duration[2]%$ secondes, brûlant les ennemis pour %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$ de dégâts sur 3 secondes.",
["HERO_BIRD_CLUSTER_BOMB_DESCRIPTION_3"] = "Lance un explosif qui se répartit sur les ennemis, infligeant %$heroes.hero_bird.cluster_bomb.explosion_damage_min[3]%$ de dégâts chacun et mettant le sol en feu pendant %$heroes.hero_bird.cluster_bomb.fire_duration[3]%$ secondes, brûlant les ennemis pour %$heroes.hero_bird.cluster_bomb.burning.s_total_damage%$ de dégâts sur 3 secondes.",
["HERO_BIRD_CLUSTER_BOMB_TITLE"] = "BOMBARDEMENT DE TAPIS",
["HERO_BIRD_DESC"] = "Le courageux cavalier de griffon vole au combat en brandissant un arsenal d'acier et de feu. Bien qu'il ait rejoint l'Alliance à contrecœur depuis que l'Armée des Ténèbres a envahi sa maison, Broden a accepté de pleuvoir la destruction sur le Culte comme moyen de restaurer le statu quo à Linirea.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_1"] = "Le griffon plonge vers le sol pour dévorer un ennemi ayant jusqu'à %$heroes.hero_bird.eat_instakill.hp_max[1]%$ de santé.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_2"] = "Le griffon plonge vers le sol pour dévorer un ennemi ayant jusqu'à %$heroes.hero_bird.eat_instakill.hp_max[2]%$ de santé.",
["HERO_BIRD_EAT_INSTAKILL_DESCRIPTION_3"] = "Le griffon plonge vers le sol pour dévorer un ennemi ayant jusqu'à %$heroes.hero_bird.eat_instakill.hp_max[3]%$ de santé.",
["HERO_BIRD_EAT_INSTAKILL_TITLE"] = "PLONGÉE DE CHASSE",
["HERO_BIRD_GATTLING_DESCRIPTION_1"] = "Pluie de balles sur un ennemi, infligeant %$heroes.hero_bird.gattling.s_damage_min[1]%$-%$heroes.hero_bird.gattling.s_damage_max[1]%$ de dégâts physiques.",
["HERO_BIRD_GATTLING_DESCRIPTION_2"] = "Pluie de balles sur un ennemi, infligeant %$heroes.hero_bird.gattling.s_damage_min[2]%$-%$heroes.hero_bird.gattling.s_damage_max[2]%$ de dégâts physiques.",
["HERO_BIRD_GATTLING_DESCRIPTION_3"] = "Pluie de balles sur un ennemi, infligeant %$heroes.hero_bird.gattling.s_damage_min[3]%$-%$heroes.hero_bird.gattling.s_damage_max[3]%$ de dégâts physiques.",
["HERO_BIRD_GATTLING_TITLE"] = "MODÈLE À SUIVRE",
["HERO_BIRD_NAME"] = "Broden",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_1"] = "Le griffon produit un cri assourdissant, étourdissant les ennemis pendant %$heroes.hero_bird.shout_stun.stun_duration[1]%$ seconde et les ralentissant pendant %$heroes.hero_bird.shout_stun.slow_duration[1]%$ secondes ensuite.",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_2"] = "Le griffon produit un cri assourdissant, étourdissant les ennemis pendant %$heroes.hero_bird.shout_stun.stun_duration[2]%$ seconde et les ralentissant pendant %$heroes.hero_bird.shout_stun.slow_duration[2]%$ secondes ensuite.",
["HERO_BIRD_SHOUT_STUN_DESCRIPTION_3"] = "Le griffon produit un cri assourdissant, étourdissant les ennemis pendant %$heroes.hero_bird.shout_stun.stun_duration[3]%$ seconde et les ralentissant pendant %$heroes.hero_bird.shout_stun.slow_duration[3]%$ secondes ensuite.",
["HERO_BIRD_SHOUT_STUN_TITLE"] = "CRI DE TERREUR",
["HERO_BUILDER_CLASS"] = "Maître d'œuvre",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_1"] = "Construit une tour de fortune qui attaque les ennemis passants pendant %$heroes.hero_builder.defensive_turret.duration[1]%$ secondes, infligeant %$heroes.hero_builder.defensive_turret.attack.damage_min[1]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[1]%$ de dégâts physiques par attaque.",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_2"] = "Construit une tour de fortune qui attaque les ennemis passants pendant %$heroes.hero_builder.defensive_turret.duration[2]%$ secondes, infligeant %$heroes.hero_builder.defensive_turret.attack.damage_min[2]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[2]%$ de dégâts physiques par attaque.",
["HERO_BUILDER_DEFENSIVE_TURRET_DESCRIPTION_3"] = "Construit une tour de fortune qui attaque les ennemis passants pendant %$heroes.hero_builder.defensive_turret.duration[3]%$ secondes, infligeant %$heroes.hero_builder.defensive_turret.attack.damage_min[3]%$-%$heroes.hero_builder.defensive_turret.attack.damage_max[3]%$ de dégâts physiques par attaque.",
["HERO_BUILDER_DEFENSIVE_TURRET_TITLE"] = "TOURELLE DÉFENSIVE",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_1"] = "Tourne rapidement sa poutre en bois, infligeant %$heroes.hero_builder.demolition_man.s_damage_min[1]%$-%$heroes.hero_builder.demolition_man.s_damage_max[1]%$ de dégâts physiques aux ennemis autour de lui.",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_2"] = "Tourne rapidement sa poutre en bois, infligeant %$heroes.hero_builder.demolition_man.s_damage_min[2]%$-%$heroes.hero_builder.demolition_man.s_damage_max[2]%$ de dégâts physiques aux ennemis autour de lui.",
["HERO_BUILDER_DEMOLITION_MAN_DESCRIPTION_3"] = "Tourne rapidement sa poutre en bois, infligeant %$heroes.hero_builder.demolition_man.s_damage_min[3]%$-%$heroes.hero_builder.demolition_man.s_damage_max[3]%$ de dégâts physiques aux ennemis autour de lui.",
["HERO_BUILDER_DEMOLITION_MAN_TITLE"] = "HOMME DE DÉMOLITION",
["HERO_BUILDER_DESC"] = "Les années passées à construire les défenses de Linirea ont appris à Torres une chose ou deux sur la bataille. Maintenant que tout le royaume est en danger (et ennuyé de regarder depuis les coulisses), il utilise tous ses outils et son savoir dans la mêlée.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_1"] = "Torres arrête de combattre pour manger une collation, se soignant de %$heroes.hero_builder.lunch_break.heal_hp[1]%$ points de vie.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_2"] = "Torres arrête de combattre pour manger une collation, se soignant de %$heroes.hero_builder.lunch_break.heal_hp[2]%$ points de vie.",
["HERO_BUILDER_LUNCH_BREAK_DESCRIPTION_3"] = "Torres arrête de combattre pour manger une collation, se soignant de %$heroes.hero_builder.lunch_break.heal_hp[3]%$ points de vie.",
["HERO_BUILDER_LUNCH_BREAK_TITLE"] = "PAUSE DÉJEUNER",
["HERO_BUILDER_NAME"] = "Torres",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_1"] = "Appelle deux constructeurs qui combattent à ses côtés pendant %$heroes.hero_builder.overtime_work.soldier.duration%$ secondes.",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_2"] = "Les constructeurs ont %$heroes.hero_builder.overtime_work.soldier.hp_max[2]%$ de santé et infligent %$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[2]%$ de dégâts physiques. Ils combattent pendant %$heroes.hero_builder.overtime_work.soldier.duration%$ secondes.",
["HERO_BUILDER_OVERTIME_WORK_DESCRIPTION_3"] = "Les constructeurs ont %$heroes.hero_builder.overtime_work.soldier.hp_max[3]%$ de santé et infligent %$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_builder.overtime_work.soldier.melee_attack.damage_max[3]%$ de dégâts physiques. Ils combattent pendant %$heroes.hero_builder.overtime_work.soldier.duration%$ secondes. ",
["HERO_BUILDER_OVERTIME_WORK_TITLE"] = "HOMMES AU TRAVAIL",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_1"] = "Lâche une boule d'acier géante sur le chemin, infligeant %$heroes.hero_builder.ultimate.damage[2]%$ de dégâts physiques et étourdissant les ennemis pendant %$heroes.hero_builder.ultimate.stun_duration[2]%$ secondes.",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_2"] = "Lâche une boule d'acier géante sur le chemin, infligeant %$heroes.hero_builder.ultimate.damage[3]%$ de dégâts physiques et étourdissant les ennemis pendant %$heroes.hero_builder.ultimate.stun_duration[3]%$ secondes.",
["HERO_BUILDER_WRECKING_BALL_DESCRIPTION_3"] = "Lâche une boule d'acier géante sur le chemin, infligeant %$heroes.hero_builder.ultimate.damage[4]%$ de dégâts physiques et étourdissant les ennemis pendant %$heroes.hero_builder.ultimate.stun_duration[4]%$ secondes.",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_DESCRIPTION"] = "Lâche une boule de démolition sur le chemin, endommageant les ennemis.",
["HERO_BUILDER_WRECKING_BALL_MENUBOTTOM_NAME"] = "Boule de démolition",
["HERO_BUILDER_WRECKING_BALL_TITLE"] = "BOULE DE DÉMOLITION",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_1"] = "Sylvara débloque sa véritable forme pendant %$heroes.hero_dragon_arb.ultimate.duration[2]%$ secondes, période pendant laquelle elle gagne %$heroes.hero_dragon_arb.ultimate.s_bonuses[2]%$% de dégâts, de vitesse, de résistances et fait évoluer certains de ses pouvoirs.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_2"] = "Sylvara débloque sa véritable forme pendant %$heroes.hero_dragon_arb.ultimate.duration[3]%$ secondes, période pendant laquelle elle gagne %$heroes.hero_dragon_arb.ultimate.s_bonuses[3]%$% de dégâts, de vitesse, de résistances et fait évoluer certains de ses pouvoirs.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_DESCRIPTION_3"] = "Sylvara débloque sa véritable forme pendant %$heroes.hero_dragon_arb.ultimate.duration[4]%$ secondes, période pendant laquelle elle gagne %$heroes.hero_dragon_arb.ultimate.s_bonuses[4]%$% de dégâts, de vitesse, de résistances et fait évoluer certains de ses pouvoirs.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_DESCRIPTION"] = "Libérez la véritable forme de Sylvara.",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_MENUBOTTOM_NAME"] = "Nature Intérieure",
["HERO_DRAGON_ARB_ARBOREAN EVOLVE_TITLE"] = "Nature Intérieure",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_1"] = "Transforme les parcelles vertes en arboreans qui combattent pendant %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[1]%$ secondes, pendant la Nature Intérieure, il invoque des arboreans plus forts.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_2"] = "Transforme les parcelles vertes en arboreans qui combattent pendant %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[2]%$ secondes, pendant la Nature Intérieure, il invoque des arboreans plus forts.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_DESCRIPTION_3"] = "Transforme les parcelles vertes en arboreans qui combattent pendant %$heroes.hero_dragon_arb.arborean_spawn.arborean.duration[3]%$ secondes, pendant la Nature Intérieure, il invoque des arboreans plus forts.",
["HERO_DRAGON_ARB_ARBOREAN SPAWN_TITLE"] = "Appel de la forêt",
["HERO_DRAGON_ARB_CLASS"] = "Force de la Nature",
["HERO_DRAGON_ARB_DESC"] = "Le dragon de la nature et protecteur des arboreans, elle tisse les forêts avec son souffle et fait danser le vent avec ses ailes. Comme la nature elle-même, elle peut être à la fois bienveillante et punitive. Assurez-vous de ne pas jeter de déchets !",
["HERO_DRAGON_ARB_NAME"] = "Sylvara",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_1"] = "Tous les %$heroes.hero_dragon_arb.thorn_bleed.cooldown[1]%$ secondes, Sylvara renforce son prochain souffle afin de blesser les ennemis en fonction de leur vitesse, pendant la Nature Intérieure, elle a %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[1]%$% de chances de tuer instantanément.",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_2"] = "Tous les %$heroes.hero_dragon_arb.thorn_bleed.cooldown[2]%$ secondes, Sylvara renforce son prochain souffle afin de blesser les ennemis en fonction de leur vitesse, pendant la Nature Intérieure, elle a %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[2]%$% de chances de tuer instantanément.",
["HERO_DRAGON_ARB_THORN BLEED_DESCRIPTION_3"] = "Tous les %$heroes.hero_dragon_arb.thorn_bleed.cooldown[3]%$ secondes, Sylvara renforce son prochain souffle afin de blesser les ennemis en fonction de leur vitesse, pendant la Nature Intérieure, elle a %$heroes.hero_dragon_arb.thorn_bleed.instakill_chance[3]%$% de chances de tuer instantanément.",
["HERO_DRAGON_ARB_THORN BLEED_TITLE"] = "Souffle Épineux",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_1"] = "Augmente les dégâts des tours à proximité de %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[1]%$% pendant %$heroes.hero_dragon_arb.tower_runes.duration[1]%$ secondes.",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_2"] = "Augmente les dégâts des tours à proximité de %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[2]%$% pendant %$heroes.hero_dragon_arb.tower_runes.duration[2]%$ secondes.",
["HERO_DRAGON_ARB_TOWER RUNES_DESCRIPTION_3"] = "Augmente les dégâts des tours à proximité de %$heroes.hero_dragon_arb.tower_runes.s_damage_factor[3]%$% pendant %$heroes.hero_dragon_arb.tower_runes.duration[3]%$ secondes.",
["HERO_DRAGON_ARB_TOWER RUNES_TITLE"] = "Racines Profondes",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_1"] = "Invoque des plantes près des tours qui durent %$heroes.hero_dragon_arb.tower_plants.duration[1]%$ secondes. Selon leur allégeance, elles deviennent des plantes venimeuses qui infligent des dégâts et ralentissent, ou des plantes guérisseuses qui soignent les alliés.",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_2"] = "Invoque des plantes près des tours qui durent %$heroes.hero_dragon_arb.tower_plants.duration[2]%$ secondes. Selon leur allégeance, elles deviennent des plantes venimeuses qui infligent des dégâts et ralentissent, ou des plantes guérisseuses qui soignent les alliés.",
["HERO_DRAGON_ARB_TOWER_PLANTS_DESCRIPTION_3"] = "Invoque des plantes près des tours qui durent %$heroes.hero_dragon_arb.tower_plants.duration[3]%$ secondes. Selon leur allégeance, elles deviennent des plantes venimeuses qui infligent des dégâts et ralentissent, ou des plantes guérisseuses qui soignent les alliés.",
["HERO_DRAGON_ARB_TOWER_PLANTS_TITLE"] = "Porteur de Vie",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_1"] = "Lance %$heroes.hero_dragon_bone.burst.proj_count[1]%$ projectiles magiques, chacun infligeant %$heroes.hero_dragon_bone.burst.damage_min[1]%$-%$heroes.hero_dragon_bone.burst.damage_max[1]%$ de dégâts réels et appliquant la peste.",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_2"] = "Lance %$heroes.hero_dragon_bone.burst.proj_count[2]%$ projectiles magiques, chacun infligeant %$heroes.hero_dragon_bone.burst.damage_min[2]%$-%$heroes.hero_dragon_bone.burst.damage_max[2]%$ de dégâts réels et appliquant la peste.",
["HERO_DRAGON_BONE_BURST_DESCRIPTION_3"] = "Lance %$heroes.hero_dragon_bone.burst.proj_count[3]%$ projectiles magiques, chacun infligeant %$heroes.hero_dragon_bone.burst.damage_min[3]%$-%$heroes.hero_dragon_bone.burst.damage_max[3]%$ de dégâts réels et appliquant la peste.",
["HERO_DRAGON_BONE_BURST_TITLE"] = "EXPLOSION DE PESTE",
["HERO_DRAGON_BONE_CLASS"] = "Dracoliche",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_1"] = "Couvre une zone d'un nuage pestilentiel qui applique la peste aux ennemis et les ralentit pendant %$heroes.hero_dragon_bone.cloud.duration[1]%$ secondes.",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_2"] = "Couvre une zone d'un nuage pestilentiel qui applique la peste aux ennemis et les ralentit pendant %$heroes.hero_dragon_bone.cloud.duration[2]%$ secondes.",
["HERO_DRAGON_BONE_CLOUD_DESCRIPTION_3"] = "Couvre une zone d'un nuage pestilentiel qui applique la peste aux ennemis et les ralentit pendant %$heroes.hero_dragon_bone.cloud.duration[3]%$ secondes.",
["HERO_DRAGON_BONE_CLOUD_TITLE"] = "NUAGE DE PESTE",
["HERO_DRAGON_BONE_DESC"] = "Après avoir été libéré par Vez'nan lors de sa campagne de conquête, Bonehart a proposé de rembourser sa dette en utilisant ses pouvoirs pour sillonner les terres à la recherche de mages susceptibles de menacer les plans du Sorcier Noir.",
["HERO_DRAGON_BONE_NAME"] = "Bonehart",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_1"] = "Se jette sur le chemin, infligeant %$heroes.hero_dragon_bone.nova.damage_min[1]%$-%$heroes.hero_dragon_bone.nova.damage_max[1]%$ de dégâts explosifs aux ennemis et appliquant la peste.",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_2"] = "Se jette sur le chemin, infligeant %$heroes.hero_dragon_bone.nova.damage_min[2]%$-%$heroes.hero_dragon_bone.nova.damage_max[2]%$ de dégâts explosifs aux ennemis et appliquant la peste.",
["HERO_DRAGON_BONE_NOVA_DESCRIPTION_3"] = "Se jette sur le chemin, infligeant %$heroes.hero_dragon_bone.nova.damage_min[3]%$-%$heroes.hero_dragon_bone.nova.damage_max[3]%$ de dégâts explosifs aux ennemis et appliquant la peste.",
["HERO_DRAGON_BONE_NOVA_TITLE"] = "NOVA DE PESTE",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_1"] = "Lance %$heroes.hero_dragon_bone.rain.bones_count[1]%$ épines d'os vers les ennemis, infligeant %$heroes.hero_dragon_bone.rain.damage_min[1]%$-%$heroes.hero_dragon_bone.rain.damage_max[1]%$ de dégâts réels et les étourdissant brièvement.",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_2"] = "Lance %$heroes.hero_dragon_bone.rain.bones_count[2]%$ épines d'os vers les ennemis, infligeant %$heroes.hero_dragon_bone.rain.damage_min[2]%$-%$heroes.hero_dragon_bone.rain.damage_max[2]%$ de dégâts réels et les étourdissant brièvement.",
["HERO_DRAGON_BONE_RAIN_DESCRIPTION_3"] = "Lance %$heroes.hero_dragon_bone.rain.bones_count[3]%$ épines d'os vers les ennemis, infligeant %$heroes.hero_dragon_bone.rain.damage_min[3]%$-%$heroes.hero_dragon_bone.rain.damage_max[3]%$ de dégâts réels et les étourdissant brièvement.",
["HERO_DRAGON_BONE_RAIN_TITLE"] = "PLUIE D'ÉPINES",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_1"] = "Invoque deux dragons d'os. Chaque dragon a %$heroes.hero_dragon_bone.ultimate.dog.hp[2]%$ points de vie et inflige %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[2]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[2]%$ de dégâts physiques.",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_2"] = "Invoque deux dragons d'os. Chaque dragon a %$heroes.hero_dragon_bone.ultimate.dog.hp[3]%$ points de vie et inflige %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[3]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[3]%$ de dégâts physiques.",
["HERO_DRAGON_BONE_RAISE_DRAKES_DESCRIPTION_3"] = "Invoque deux dragons d'os. Chaque dragon a %$heroes.hero_dragon_bone.ultimate.dog.hp[4]%$ points de vie et inflige %$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_min[4]%$-%$heroes.hero_dragon_bone.ultimate.dog.melee_attack.damage_max[4]%$ de dégâts physiques.",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_DESCRIPTION"] = "Invoque deux dragons d'os.",
["HERO_DRAGON_BONE_RAISE_DRAKES_MENUBOTTOM_NAME"] = "Éveil des Dragons",
["HERO_DRAGON_BONE_RAISE_DRAKES_TITLE"] = "ÉVEIL DES DRAGONS",
["HERO_DRAGON_GEM_CLASS"] = "Inébranlable",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_1"] = "Enferme un ennemi dans un cristal pendant quelques secondes. Le cristal explose ensuite, tuant instantanément la cible et infligeant %$heroes.hero_dragon_gem.crystal_instakill.s_damage[1]%$ de dégâts réels autour.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_2"] = "Enferme un ennemi dans un cristal pendant quelques secondes. Le cristal explose ensuite, tuant instantanément la cible et infligeant %$heroes.hero_dragon_gem.crystal_instakill.s_damage[2]%$ de dégâts réels autour.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_DESCRIPTION_3"] = "Enferme un ennemi dans un cristal pendant quelques secondes. Le cristal explose ensuite, tuant instantanément la cible et infligeant %$heroes.hero_dragon_gem.crystal_instakill.s_damage[3]%$ de dégâts réels autour.",
["HERO_DRAGON_GEM_CRYSTAL_INSTAKILL_TITLE"] = "TOMBE DE GRENAT",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_1"] = "Lance un cristal sur le chemin qui réduit la vitesse de l'ennemi de %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% et inflige %$heroes.hero_dragon_gem.crystal_totem.s_damage[1]%$ de dégâts magiques toutes les 1 secondes autour. Dure %$heroes.hero_dragon_gem.crystal_totem.duration[1]%$ secondes.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_2"] = "Lance un cristal sur le chemin qui réduit la vitesse de l'ennemi de %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% et inflige %$heroes.hero_dragon_gem.crystal_totem.s_damage[2]%$ de dégâts magiques toutes les 1 secondes autour. Dure %$heroes.hero_dragon_gem.crystal_totem.duration[2]%$ secondes.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_DESCRIPTION_3"] = "Lance un cristal sur le chemin qui réduit la vitesse de l'ennemi de %$heroes.hero_dragon_gem.crystal_totem.s_slow_factor%$% et inflige %$heroes.hero_dragon_gem.crystal_totem.s_damage[3]%$ de dégâts magiques toutes les 1 secondes autour. Dure %$heroes.hero_dragon_gem.crystal_totem.duration[3]%$ secondes.",
["HERO_DRAGON_GEM_CRYSTAL_TOTEM_TITLE"] = "CONDUITE DE PUISSANCE",
["HERO_DRAGON_GEM_DESC"] = "La vie isolée de Kosmyr a été interrompue lorsque le Culte a commencé ses opérations dans le Canyon Abandonné. Voulant se débarrasser des intrus, le dragon a passé un accord avec Vez'nan pour rejoindre l'Alliance contre un ennemi commun.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_1"] = "Invoque %$heroes.hero_dragon_gem.ultimate.max_shards[2]%$ barrages de cristal infligeant %$heroes.hero_dragon_gem.ultimate.damage_min[2]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[2]%$ de dégâts réels aux ennemis pris dans la zone.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_2"] = "Invoque %$heroes.hero_dragon_gem.ultimate.max_shards[3]%$ barrages de cristal infligeant %$heroes.hero_dragon_gem.ultimate.damage_min[3]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[3]%$ de dégâts réels aux ennemis pris dans la zone.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_DESCRIPTION_3"] = "Invoque %$heroes.hero_dragon_gem.ultimate.max_shards[4]%$ barrages de cristal infligeant %$heroes.hero_dragon_gem.ultimate.damage_min[4]%$-%$heroes.hero_dragon_gem.ultimate.damage_max[4]%$ de dégâts réels aux ennemis pris dans la zone.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_DESCRIPTION"] = "Lance diverses salves de cristaux contre les ennemis.",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_MENUBOTTOM_NAME"] = "Avalanche de Cristal",
["HERO_DRAGON_GEM_FALLING_CRYSTALS_TITLE"] = "AVALANCHE DE CRISTAL",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_1"] = "Fait pousser des pics de cristal sur les chemins autour de lui, infligeant %$heroes.hero_dragon_gem.floor_impact.damage_min[1]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[1]%$ de dégâts physiques à chaque ennemi touché.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_2"] = "Fait pousser des pics de cristal sur les chemins autour de lui, infligeant %$heroes.hero_dragon_gem.floor_impact.damage_min[2]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[2]%$ de dégâts physiques à chaque ennemi touché.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_DESCRIPTION_3"] = "Fait pousser des pics de cristal sur les chemins autour de lui, infligeant %$heroes.hero_dragon_gem.floor_impact.damage_min[3]%$-%$heroes.hero_dragon_gem.floor_impact.damage_max[3]%$ de dégâts physiques à chaque ennemi touché.",
["HERO_DRAGON_GEM_FLOOR_IMPACT_TITLE"] = "ÉCLATS PRISMATIQUES",
["HERO_DRAGON_GEM_NAME"] = "Kosmyr",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_1"] = "Cristallise un groupe d'ennemis, les étourdissant pendant %$heroes.hero_dragon_gem.stun.duration[1]%$ secondes.",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_2"] = "Cristallise un groupe d'ennemis, les étourdissant pendant %$heroes.hero_dragon_gem.stun.duration[2]%$ secondes.",
["HERO_DRAGON_GEM_STUN_DESCRIPTION_3"] = "Cristallise un groupe d'ennemis, les étourdissant pendant %$heroes.hero_dragon_gem.stun.duration[3]%$ secondes.",
["HERO_DRAGON_GEM_STUN_TITLE"] = "SOUFFLE PARALYSANT",
["HERO_HUNTER_BEASTS_DESCRIPTION_1"] = "Invoque 2 chauves-souris qui attaquent les ennemis proches pendant %$heroes.hero_hunter.beasts.duration[1]%$ secondes, infligeant %$heroes.hero_hunter.beasts.damage_min[1]%$-%$heroes.hero_hunter.beasts.damage_max[1]%$ de dégâts physiques. Chaque chauve-souris a une chance de voler %$heroes.hero_hunter.beasts.gold_to_steal[1]%$ or de leur cible.",
["HERO_HUNTER_BEASTS_DESCRIPTION_2"] = "Invoque 2 chauves-souris qui attaquent les ennemis proches pendant %$heroes.hero_hunter.beasts.duration[2]%$ secondes, infligeant %$heroes.hero_hunter.beasts.damage_min[2]%$-%$heroes.hero_hunter.beasts.damage_max[2]%$ de dégâts physiques. Chaque chauve-souris a une chance de voler %$heroes.hero_hunter.beasts.gold_to_steal[2]%$ or de leur cible.",
["HERO_HUNTER_BEASTS_DESCRIPTION_3"] = "Invoque 2 chauves-souris qui attaquent les ennemis proches pendant %$heroes.hero_hunter.beasts.duration[3]%$ secondes, infligeant %$heroes.hero_hunter.beasts.damage_min[3]%$-%$heroes.hero_hunter.beasts.damage_max[3]%$ de dégâts physiques. Chaque chauve-souris a une chance de voler %$heroes.hero_hunter.beasts.gold_to_steal[3]%$ or de leur cible.",
["HERO_HUNTER_BEASTS_TITLE"] = "BÊTES DU CRÉPUSCULE",
["HERO_HUNTER_CLASS"] = "Chasseresse Argentée",
["HERO_HUNTER_DESC"] = "Née de l'union d'un vampire et d'un chasseur renommé, Anya suit les pas de son père en luttant contre les habitants des ténèbres. La chasse acharnée aux cultistes l'a rapidement emmenée vers les terres du sud et à rejoindre l'Alliance.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_1"] = "Tous les 7ème attaques au corps à corps infligent entre %$heroes.hero_hunter.heal_strike.damage_min[1]%$ et %$heroes.hero_hunter.heal_strike.damage_max[1]%$ de dégâts réels et soignent Anya de %$heroes.hero_hunter.heal_strike.heal_factor[1]%$% de la santé maximale de sa cible.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_2"] = "Tous les 7ème attaques au corps à corps infligent entre %$heroes.hero_hunter.heal_strike.damage_min[2]%$ et %$heroes.hero_hunter.heal_strike.damage_max[2]%$ de dégâts réels et soignent Anya de %$heroes.hero_hunter.heal_strike.heal_factor[2]%$% de la santé maximale de sa cible.",
["HERO_HUNTER_HEAL_STRIKE_DESCRIPTION_3"] = "Tous les 7ème attaques au corps à corps infligent entre %$heroes.hero_hunter.heal_strike.damage_min[3]%$ et %$heroes.hero_hunter.heal_strike.damage_max[3]%$ de dégâts réels et soignent Anya de %$heroes.hero_hunter.heal_strike.heal_factor[3]%$% de la santé maximale de sa cible.",
["HERO_HUNTER_HEAL_STRIKE_TITLE"] = "GRIFFE VAMPIRIQUE",
["HERO_HUNTER_NAME"] = "Anya",
["HERO_HUNTER_RICOCHET_DESCRIPTION_1"] = "Anya se transforme en brume et rebondit entre %$heroes.hero_hunter.ricochet.s_bounces[1]%$ ennemis, infligeant %$heroes.hero_hunter.ricochet.damage_min[1]%$-%$heroes.hero_hunter.ricochet.damage_max[1]%$ de dégâts physiques à chacun.",
["HERO_HUNTER_RICOCHET_DESCRIPTION_2"] = "Anya se transforme en brume et rebondit entre %$heroes.hero_hunter.ricochet.s_bounces[2]%$ ennemis, infligeant %$heroes.hero_hunter.ricochet.damage_min[2]%$-%$heroes.hero_hunter.ricochet.damage_max[2]%$ de dégâts physiques à chacun.",
["HERO_HUNTER_RICOCHET_DESCRIPTION_3"] = "Anya se transforme en brume et rebondit entre %$heroes.hero_hunter.ricochet.s_bounces[3]%$ ennemis, infligeant %$heroes.hero_hunter.ricochet.damage_min[3]%$-%$heroes.hero_hunter.ricochet.damage_max[3]%$ de dégâts physiques à chacun.",
["HERO_HUNTER_RICOCHET_TITLE"] = "PAS BRUMEUX",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_1"] = "Tire sur tous les ennemis autour d'elle, infligeant %$heroes.hero_hunter.shoot_around.s_damage_min[1]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[1]%$ de dégâts réels à chacun.",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_2"] = "Tire sur tous les ennemis autour d'elle, infligeant %$heroes.hero_hunter.shoot_around.s_damage_min[2]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[2]%$ de dégâts réels par seconde à chacun.",
["HERO_HUNTER_SHOOT_AROUND_DESCRIPTION_3"] = "Tire sur tous les ennemis autour d'elle, infligeant %$heroes.hero_hunter.shoot_around.s_damage_min[3]%$-%$heroes.hero_hunter.shoot_around.s_damage_max[3]%$ de dégâts réels par seconde à chacun.",
["HERO_HUNTER_SHOOT_AROUND_TITLE"] = "TEMPÊTE ARGENTÉE",
["HERO_HUNTER_SPIRIT_DESCRIPTION_1"] = "Invoque une projection de Dante qui inflige %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[2]%$ de dégâts réels par seconde pendant %$heroes.hero_hunter.ultimate.duration%$ secondes. Ressuscite Anya si son corps est à proximité.",
["HERO_HUNTER_SPIRIT_DESCRIPTION_2"] = "Invoque une projection de Dante qui inflige %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[3]%$ de dégâts réels par seconde pendant %$heroes.hero_hunter.ultimate.duration%$ secondes. Ressuscite Anya si son corps est à proximité.",
["HERO_HUNTER_SPIRIT_DESCRIPTION_3"] = "Invoque une projection de Dante qui inflige %$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_min[4]%$-%$heroes.hero_hunter.ultimate.entity.basic_ranged.damage_max[4]%$ de dégâts réels par seconde pendant %$heroes.hero_hunter.ultimate.duration%$ secondes. Ressuscite Anya si son corps est à proximité.",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_DESCRIPTION"] = "Invoque une projection de Dante qui ralentit et attaque les ennemis.",
["HERO_HUNTER_SPIRIT_MENUBOTTOM_NAME"] = "Aide du Chasseur",
["HERO_HUNTER_SPIRIT_TITLE"] = "AIDE DU CHASSEUR",
["HERO_HUNTER_ULTIMATE_ENTITY_NAME"] = "Projection de Dante",
["HERO_LAVA_CLASS"] = "Fureur Fondue",
["HERO_LAVA_DESC"] = "Un être de feu et de destruction avec un mauvais tempérament, réveillé d'un profond sommeil par les activités de Grymbeard. Comme le dialogue n'est pas son fort, Kratoa percera les lignes de ses ennemis jusqu'à se calmer pour pouvoir dormir à nouveau.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_1"] = "Lance une boule de lave qui inflige %$heroes.hero_lava.double_trouble.s_damage[1]%$ de dégâts explosifs aux ennemis et fait apparaître un magmite de %$heroes.hero_lava.double_trouble.soldier.hp_max[1]%$ de santé qui combat pendant %$heroes.hero_lava.double_trouble.soldier.duration%$ secondes.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_2"] = "Lance une boule de lave qui inflige %$heroes.hero_lava.double_trouble.s_damage[2]%$ de dégâts explosifs aux ennemis et fait apparaître un magmite de %$heroes.hero_lava.double_trouble.soldier.hp_max[2]%$ de santé qui combat pendant %$heroes.hero_lava.double_trouble.soldier.duration%$ secondes.",
["HERO_LAVA_DOUBLE_TROUBLE_DESCRIPTION_3"] = "Lance une boule de lave qui inflige %$heroes.hero_lava.double_trouble.s_damage[3]%$ de dégâts explosifs aux ennemis et fait apparaître un magmite de %$heroes.hero_lava.double_trouble.soldier.hp_max[3]%$ de santé qui combat pendant %$heroes.hero_lava.double_trouble.soldier.duration%$ secondes.",
["HERO_LAVA_DOUBLE_TROUBLE_SOLDIER_NAME"] = "Magmite",
["HERO_LAVA_DOUBLE_TROUBLE_TITLE"] = "DOUBLE PROBLÈME",
["HERO_LAVA_HOTHEADED_DESCRIPTION_1"] = "Lorsque Kratoa revient à la vie, il accorde un bonus de dégâts de %$heroes.hero_lava.hotheaded.s_damage_factors[1]%$% aux tours proches pendant %$heroes.hero_lava.hotheaded.durations[1]%$ secondes.",
["HERO_LAVA_HOTHEADED_DESCRIPTION_2"] = "Lorsque Kratoa revient à la vie, il accorde un bonus de dégâts de %$heroes.hero_lava.hotheaded.s_damage_factors[2]%$% aux tours proches pendant %$heroes.hero_lava.hotheaded.durations[2]%$ secondes.",
["HERO_LAVA_HOTHEADED_DESCRIPTION_3"] = "Lorsque Kratoa revient à la vie, il accorde un bonus de dégâts de %$heroes.hero_lava.hotheaded.s_damage_factors[3]%$% aux tours proches pendant %$heroes.hero_lava.hotheaded.durations[3]%$ secondes.",
["HERO_LAVA_HOTHEADED_TITLE"] = "TÊTE BRÛLANTE",
["HERO_LAVA_NAME"] = "Kratoa",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_1"] = "Batte répétitivement un ennemi, infligeant de %$heroes.hero_lava.temper_tantrum.s_damage_min[1]%$ à %$heroes.hero_lava.temper_tantrum.s_damage_max[1]%$ de dégâts physiques et étourdissant la cible pendant %$heroes.hero_lava.temper_tantrum.duration[1]%$ secondes.",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_2"] = "Batte répétitivement un ennemi, infligeant de %$heroes.hero_lava.temper_tantrum.s_damage_min[2]%$ à %$heroes.hero_lava.temper_tantrum.s_damage_max[2]%$ de dégâts physiques et étourdissant la cible pendant %$heroes.hero_lava.temper_tantrum.duration[2]%$ secondes.",
["HERO_LAVA_TEMPER_TANTRUM_DESCRIPTION_3"] = "Batte répétitivement un ennemi, infligeant de %$heroes.hero_lava.temper_tantrum.s_damage_min[3]%$ à %$heroes.hero_lava.temper_tantrum.s_damage_max[3]%$ de dégâts physiques et étourdissant la cible pendant %$heroes.hero_lava.temper_tantrum.duration[3]%$ secondes.",
["HERO_LAVA_TEMPER_TANTRUM_TITLE"] = "CRISE DE COLÈRE",
["HERO_LAVA_ULTIMATE_DESCRIPTION_1"] = "Lance %$heroes.hero_lava.ultimate.fireball_count[2]%$ explosions de lave sur le chemin, chacune infligeant %$heroes.hero_lava.ultimate.bullet.s_damage[2]%$ de dégâts réels à chaque ennemi touché et les brûlant pendant %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ secondes.",
["HERO_LAVA_ULTIMATE_DESCRIPTION_2"] = "Lance %$heroes.hero_lava.ultimate.fireball_count[3]%$ explosions de lave sur le chemin, chacune infligeant %$heroes.hero_lava.ultimate.bullet.s_damage[3]%$ de dégâts réels à chaque ennemi touché et les brûlant pendant %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ secondes.",
["HERO_LAVA_ULTIMATE_DESCRIPTION_3"] = "Lance %$heroes.hero_lava.ultimate.fireball_count[4]%$ explosions de lave sur le chemin, chacune infligeant %$heroes.hero_lava.ultimate.bullet.s_damage[4]%$ de dégâts réels à chaque ennemi touché et les brûlant pendant %$heroes.hero_lava.ultimate.bullet.scorch.duration%$ secondes.",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "Lance des bouffées de lave sur le chemin, brûlant le sol.",
["HERO_LAVA_ULTIMATE_MENUBOTTOM_NAME"] = "Explosion de Rage",
["HERO_LAVA_ULTIMATE_TITLE"] = "EXPLOSION DE RAGE",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_1"] = "Projette de la lave sur les ennemis, infligeant %$heroes.hero_lava.wild_eruption.s_damage[1]%$ de dégâts véritables par seconde et les brûlant pendant %$heroes.hero_lava.wild_eruption.duration[1]%$ secondes.",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_2"] = "Projette de la lave sur les ennemis, infligeant %$heroes.hero_lava.wild_eruption.s_damage[2]%$ de dégâts véritables par seconde et les brûlant pendant %$heroes.hero_lava.wild_eruption.duration[2]%$ secondes.",
["HERO_LAVA_WILD_ERUPTION_DESCRIPTION_3"] = "Projette de la lave sur les ennemis, infligeant %$heroes.hero_lava.wild_eruption.s_damage[3]%$ de dégâts véritables par seconde et les brûlant pendant %$heroes.hero_lava.wild_eruption.duration[3]%$ secondes.",
["HERO_LAVA_WILD_ERUPTION_TITLE"] = "ÉRUPTION SAUVAGE",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_1"] = "Invoque %$heroes.hero_lumenir.ultimate.soldier_count[1]%$ guerriers de lumière qui étourdissent brièvement les ennemis proches et infligent %$heroes.hero_lumenir.ultimate.damage_min[1]%$-%$heroes.hero_lumenir.ultimate.damage_max[1]%$ de dégâts réels.",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_2"] = "Invoque %$heroes.hero_lumenir.ultimate.soldier_count[2]%$ guerriers de lumière qui étourdissent brièvement les ennemis proches et infligent %$heroes.hero_lumenir.ultimate.damage_min[2]%$-%$heroes.hero_lumenir.ultimate.damage_max[2]%$ de dégâts réels.",
["HERO_LUMENIR_ARROW_STORM_DESCRIPTION_3"] = "Invoque %$heroes.hero_lumenir.ultimate.soldier_count[3]%$ guerriers de lumière qui étourdissent brièvement les ennemis proches et infligent %$heroes.hero_lumenir.ultimate.damage_min[3]%$-%$heroes.hero_lumenir.ultimate.damage_max[3]%$ de dégâts réels.",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "Invoque des guerriers divins qui combattent les ennemis.",
["HERO_LUMENIR_ARROW_STORM_MENUBOTTOM_NAME"] = "Appel du Triomphe",
["HERO_LUMENIR_ARROW_STORM_TITLE"] = "APPEL DU TRIOMPHE",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_1"] = "Lance une épée divine de lumière sur l'ennemi le plus fort à proximité, infligeant %$heroes.hero_lumenir.celestial_judgement.damage[1]%$ de dégâts réels et le étourdissant pendant %$heroes.hero_lumenir.celestial_judgement.stun_duration[1]%$ secondes.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_2"] = "Lance une épée divine de lumière sur l'ennemi le plus fort à proximité, infligeant %$heroes.hero_lumenir.celestial_judgement.damage[2]%$ de dégâts réels et le étourdissant pendant %$heroes.hero_lumenir.celestial_judgement.stun_duration[2]%$ secondes.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_DESCRIPTION_3"] = "Lance une épée divine de lumière sur l'ennemi le plus fort à proximité, infligeant %$heroes.hero_lumenir.celestial_judgement.damage[3]%$ de dégâts réels et le étourdissant pendant %$heroes.hero_lumenir.celestial_judgement.stun_duration[3]%$ secondes.",
["HERO_LUMENIR_CELESTIAL_JUDGEMENT_TITLE"] = "JUGEMENT CÉLESTE",
["HERO_LUMENIR_CLASS"] = "Porte-Lumière",
["HERO_LUMENIR_DESC"] = "Voguant entre les royaumes, Lumenir se tient comme l'avatar de la justice et de la résolution. Elle est la légendaire Porte-Lumière, révérée par les paladins de Linirea, à qui elle accorde ses bénédictions, leur octroyant de grands pouvoirs pour aider dans leur lutte contre le mal.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_1"] = "Émet %$heroes.hero_lumenir.fire_balls.flames_count[1]%$ orbes de lumière divine qui parcourent le chemin en infligeant des dégâts aux ennemis. Chaque orbe inflige de %$heroes.hero_lumenir.fire_balls.flame_damage_min[1]%$ à %$heroes.hero_lumenir.fire_balls.flame_damage_max[1]%$ de dégâts réels à chaque ennemi qu'il traverse.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_2"] = "Émet %$heroes.hero_lumenir.fire_balls.flames_count[2]%$ orbes de lumière divine qui parcourent le chemin en infligeant des dégâts aux ennemis. Chaque orbe inflige de %$heroes.hero_lumenir.fire_balls.flame_damage_min[2]%$ à %$heroes.hero_lumenir.fire_balls.flame_damage_max[2]%$ de dégâts réels à chaque ennemi qu'il traverse.",
["HERO_LUMENIR_FIRE_BALLS_DESCRIPTION_3"] = "Émet %$heroes.hero_lumenir.fire_balls.flames_count[3]%$ orbes de lumière divine qui parcourent le chemin en infligeant des dégâts aux ennemis. Chaque orbe inflige de %$heroes.hero_lumenir.fire_balls.flame_damage_min[3]%$ à %$heroes.hero_lumenir.fire_balls.flame_damage_max[3]%$ de dégâts réels à chaque ennemi qu'il traverse.",
["HERO_LUMENIR_FIRE_BALLS_TITLE"] = "VAGUE RADIEUSE",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_1"] = "Invoque un petit dragon de lumière qui suit l'autre héros équipé pendant %$heroes.hero_lumenir.mini_dragon.dragon.duration[1]%$ secondes. Chaque dragon inflige %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[1]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[1]%$ de dégâts physiques par attaque.",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_2"] = "Invoque un petit dragon de lumière qui suit l'autre héros équipé pendant %$heroes.hero_lumenir.mini_dragon.dragon.duration[2]%$ secondes. Chaque dragon inflige %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[2]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[2]%$ de dégâts physiques par attaque.",
["HERO_LUMENIR_MINI_DRAGON_DESCRIPTION_3"] = "Invoque un petit dragon de lumière qui suit l'autre héros équipé pendant %$heroes.hero_lumenir.mini_dragon.dragon.duration[3]%$ secondes. Chaque dragon inflige %$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_min[3]%$-%$heroes.hero_lumenir.mini_dragon.dragon.ranged_attack.damage_max[3]%$ de dégâts physiques par attaque.",
["HERO_LUMENIR_MINI_DRAGON_TITLE"] = "COMPAGNON LUMINEUX",
["HERO_LUMENIR_NAME"] = "Lumenir",
["HERO_LUMENIR_SHIELD_DESCRIPTION_1"] = "Accorde aux unités alliées un bouclier d'armure de %$heroes.hero_lumenir.shield.armor[1]%$% qui réfléchit %$heroes.hero_lumenir.shield.spiked_armor[1]%$% des dégâts aux ennemis.",
["HERO_LUMENIR_SHIELD_DESCRIPTION_2"] = "Accorde aux unités alliées un bouclier d'armure de %$heroes.hero_lumenir.shield.armor[2]%$% qui réfléchit %$heroes.hero_lumenir.shield.spiked_armor[2]%$% des dégâts aux ennemis.",
["HERO_LUMENIR_SHIELD_DESCRIPTION_3"] = "Accorde aux unités alliées un bouclier d'armure de %$heroes.hero_lumenir.shield.armor[3]%$% qui réfléchit %$heroes.hero_lumenir.shield.spiked_armor[3]%$% des dégâts aux ennemis.",
["HERO_LUMENIR_SHIELD_TITLE"] = "BÉNÉDICTION DE RÉTRIBUTION",
["HERO_MECHA_CLASS"] = "Menace Mobile",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_1"] = "Invoque un zeppelin goblin qui bombarde les ennemis à proximité de la zone ciblée, infligeant %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[2]%$ de dégâts réels de zone par attaque.",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_2"] = "Invoque un zeppelin goblin qui bombarde les ennemis à proximité de la zone ciblée, infligeant %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[3]%$ de dégâts réels de zone par attaque.",
["HERO_MECHA_DEATH_FROM_ABOVE_DESCRIPTION_3"] = "Invoque un zeppelin goblin qui bombarde les ennemis à proximité de la zone ciblée, infligeant %$heroes.hero_mecha.ultimate.ranged_attack.damage_min[4]%$-%$heroes.hero_mecha.ultimate.ranged_attack.damage_max[4]%$ de dégâts réels de zone par attaque.",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_DESCRIPTION"] = "Invoque un zeppelin qui bombarde les ennemis dans la zone.",
["HERO_MECHA_DEATH_FROM_ABOVE_MENUBOTTOM_NAME"] = "Mort d'en Haut",
["HERO_MECHA_DEATH_FROM_ABOVE_TITLE"] = "MORT VENUE D'EN HAUT",
["HERO_MECHA_DESC"] = "Né de l'esprit de deux gobelins inventeurs fous et construit sur les fondations de la technologie naine volée, Onagro est la machine de guerre orque définitive et un spectacle effrayant pour les ennemis de l'Armée Sombre.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_1"] = "Invoque %$heroes.hero_mecha.goblidrones.units%$ drones qui attaquent les ennemis pendant %$heroes.hero_mecha.goblidrones.drone.duration[1]%$ secondes, infligeant %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[1]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[1]%$ de dégâts physiques par attaque.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_2"] = "Invoque %$heroes.hero_mecha.goblidrones.units%$ drones qui attaquent les ennemis pendant %$heroes.hero_mecha.goblidrones.drone.duration[2]%$ secondes, infligeant %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[2]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[2]%$ de dégâts physiques par attaque.",
["HERO_MECHA_GOBLIDRONES_DESCRIPTION_3"] = "Invoque %$heroes.hero_mecha.goblidrones.units%$ drones qui attaquent les ennemis pendant %$heroes.hero_mecha.goblidrones.drone.duration[3]%$ secondes, infligeant %$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_min[3]%$-%$heroes.hero_mecha.goblidrones.drone.ranged_attack.damage_max[3]%$ de dégâts physiques par attaque.",
["HERO_MECHA_GOBLIDRONES_TITLE"] = "GOBLIDRONES",
["HERO_MECHA_MINE_DROP_DESCRIPTION_1"] = "En restant immobile, le mech laisse périodiquement jusqu'à %$heroes.hero_mecha.mine_drop.max_mines[1]%$ mines explosives sur le chemin. Les mines explosent en infligeant %$heroes.hero_mecha.mine_drop.damage_min[1]%$-%$heroes.hero_mecha.mine_drop.damage_max[1]%$ dégâts explosifs chacune.",
["HERO_MECHA_MINE_DROP_DESCRIPTION_2"] = "En restant immobile, le mech laisse périodiquement jusqu'à %$heroes.hero_mecha.mine_drop.max_mines[2]%$ mines explosives sur le chemin. Les mines explosent en infligeant %$heroes.hero_mecha.mine_drop.damage_min[2]%$-%$heroes.hero_mecha.mine_drop.damage_max[2]%$ dégâts explosifs chacune.",
["HERO_MECHA_MINE_DROP_DESCRIPTION_3"] = "En restant immobile, le mech laisse périodiquement jusqu'à %$heroes.hero_mecha.mine_drop.max_mines[3]%$ mines explosives sur le chemin. Les mines explosent en infligeant %$heroes.hero_mecha.mine_drop.damage_min[3]%$-%$heroes.hero_mecha.mine_drop.damage_max[3]%$ dégâts explosifs chacune.",
["HERO_MECHA_MINE_DROP_TITLE"] = "LÂCHER DE MINES",
["HERO_MECHA_NAME"] = "Onagre",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_1"] = "Le mech frappe le sol, étourdissant brièvement et infligeant %$heroes.hero_mecha.power_slam.s_damage[1]%$ de dégâts physiques à tous les ennemis proches.",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_2"] = "Le mech frappe le sol, étourdissant brièvement et infligeant %$heroes.hero_mecha.power_slam.s_damage[2]%$ de dégâts physiques à tous les ennemis proches.",
["HERO_MECHA_POWER_SLAM_DESCRIPTION_3"] = "Le mech frappe le sol, étourdissant brièvement et infligeant %$heroes.hero_mecha.power_slam.s_damage[3]%$ de dégâts physiques à tous les ennemis proches.",
["HERO_MECHA_POWER_SLAM_TITLE"] = "COUP DE PUISSANCE",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_1"] = "Lance une bombe qui répand du goudron sur le chemin, ralentissant les ennemis de %$heroes.hero_mecha.tar_bomb.slow_factor%$% pendant %$heroes.hero_mecha.tar_bomb.duration[1]%$ secondes.",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_2"] = "Lance une bombe qui répand du goudron sur le chemin, ralentissant les ennemis de %$heroes.hero_mecha.tar_bomb.slow_factor%$% pendant %$heroes.hero_mecha.tar_bomb.duration[2]%$ secondes.",
["HERO_MECHA_TAR_BOMB_DESCRIPTION_3"] = "Lance une bombe qui répand du goudron sur le chemin, ralentissant les ennemis de %$heroes.hero_mecha.tar_bomb.slow_factor%$% pendant %$heroes.hero_mecha.tar_bomb.duration[3]%$ secondes.",
["HERO_MECHA_TAR_BOMB_TITLE"] = "BOMBE DE GOUDRON",
["HERO_MUYRN_CLASS"] = "Gardien de la Forêt",
["HERO_MUYRN_DESC"] = "Malgré son apparence enfantine, le rusé Nyru protège la forêt depuis des centaines d'années grâce à sa connexion avec les forces de la nature. Il a rejoint l'Alliance afin de mettre un terme aux vagues croissantes d'envahisseurs menaçant sa maison.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_1"] = "Charme tous les ennemis dans une zone, réduisant leurs dégâts d'attaque de %$heroes.hero_muyrn.faery_dust.s_damage_factor[1]%$% pendant %$heroes.hero_muyrn.faery_dust.duration[1]%$ secondes.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_2"] = "Charme tous les ennemis dans une zone, réduisant leurs dégâts d'attaque de %$heroes.hero_muyrn.faery_dust.s_damage_factor[2]%$% pendant %$heroes.hero_muyrn.faery_dust.duration[2]%$ secondes.",
["HERO_MUYRN_FAERY_DUST_DESCRIPTION_3"] = "Charme tous les ennemis dans une zone, réduisant leurs dégâts d'attaque de %$heroes.hero_muyrn.faery_dust.s_damage_factor[3]%$% pendant %$heroes.hero_muyrn.faery_dust.duration[3]%$ secondes.",
["HERO_MUYRN_FAERY_DUST_TITLE"] = "Charme d'Affaiblissement",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_1"] = "En combat, Nyru crée un bouclier de feuilles autour de lui. Le bouclier inflige %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[1]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[1]%$ de dégâts magiques par seconde et soigne Nyru pendant %$heroes.hero_muyrn.leaf_whirlwind.duration[1]%$ secondes.",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_2"] = "En combat, Nyru crée un bouclier de feuilles autour de lui. Le bouclier inflige %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[2]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[2]%$ de dégâts magiques par seconde et soigne Nyru pendant %$heroes.hero_muyrn.leaf_whirlwind.duration[2]%$ secondes.",
["HERO_MUYRN_LEAF_WHIRLWIND_DESCRIPTION_3"] = "En combat, Nyru crée un bouclier de feuilles autour de lui. Le bouclier inflige %$heroes.hero_muyrn.leaf_whirlwind.s_damage_min[3]%$-%$heroes.hero_muyrn.leaf_whirlwind.s_damage_max[3]%$ de dégâts magiques par seconde et soigne Nyru pendant %$heroes.hero_muyrn.leaf_whirlwind.duration[3]%$ secondes.",
["HERO_MUYRN_LEAF_WHIRLWIND_TITLE"] = "Tourbillon de Feuilles",
["HERO_MUYRN_NAME"] = "Nyru",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_1"] = "Fait apparaître des racines sur une zone pendant %$heroes.hero_muyrn.ultimate.duration[2]%$ secondes, ralentissant les ennemis et infligeant %$heroes.hero_muyrn.ultimate.s_damage_min[2]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[2]%$ de dégâts réels par seconde.",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_2"] = "Fait apparaître des racines sur une zone pendant %$heroes.hero_muyrn.ultimate.duration[3]%$ secondes, ralentissant les ennemis et infligeant %$heroes.hero_muyrn.ultimate.s_damage_min[3]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[3]%$ de dégâts réels par seconde.",
["HERO_MUYRN_ROOT_DEFENDER_DESCRIPTION_3"] = "Fait apparaître des racines sur une zone pendant %$heroes.hero_muyrn.ultimate.duration[4]%$ secondes, ralentissant les ennemis et infligeant %$heroes.hero_muyrn.ultimate.s_damage_min[4]%$-%$heroes.hero_muyrn.ultimate.s_damage_max[4]%$ de dégâts réels par seconde.",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_DESCRIPTION"] = "Génère des racines qui blessent et ralentissent les ennemis.",
["HERO_MUYRN_ROOT_DEFENDER_MENUBOTTOM_NAME"] = "Défenseur Racine",
["HERO_MUYRN_ROOT_DEFENDER_TITLE"] = "Défenseur des Racines",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_1"] = "Invoque %$heroes.hero_muyrn.sentinel_wisps.max_summons[1]%$ amical qui suit Nyru pendant %$heroes.hero_muyrn.sentinel_wisps.wisp.duration[1]%$ secondes. Le feu follet inflige %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[1]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[1]%$ de dégâts magiques.",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_2"] = "Invoque %$heroes.hero_muyrn.sentinel_wisps.max_summons[2]%$ feux follets amicaux qui suivent Nyru pendant %$heroes.hero_muyrn.sentinel_wisps.wisp.duration[2]%$ secondes. Les feux follets infligent %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[2]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[2]%$ de dégâts magiques.",
["HERO_MUYRN_SENTINEL_WISPS_DESCRIPTION_3"] = "Invoque %$heroes.hero_muyrn.sentinel_wisps.max_summons[3]%$ feux follets amicaux qui suivent Nyru pendant %$heroes.hero_muyrn.sentinel_wisps.wisp.duration[3]%$ secondes. Les feux follets infligent %$heroes.hero_muyrn.sentinel_wisps.wisp.damage_min[3]%$-%$heroes.hero_muyrn.sentinel_wisps.wisp.damage_max[3]%$ de dégâts magiques.",
["HERO_MUYRN_SENTINEL_WISPS_TITLE"] = "SENTINELLES FEUX FOLLETS",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_1"] = "Lance une explosion verte d'énergie vers un ennemi, infligeant %$heroes.hero_muyrn.verdant_blast.s_damage[1]%$ de dégâts magiques.",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_2"] = "Lance une explosion verte d'énergie vers un ennemi, infligeant %$heroes.hero_muyrn.verdant_blast.s_damage[2]%$ de dégâts magiques.",
["HERO_MUYRN_VERDANT_BLAST_DESCRIPTION_3"] = "Lance une explosion verte d'énergie vers un ennemi, infligeant %$heroes.hero_muyrn.verdant_blast.s_damage[3]%$ de dégâts magiques.",
["HERO_MUYRN_VERDANT_BLAST_TITLE"] = "Explosion Verdoyante",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_1"] = "Frappe brutalement un ennemi avec son épée, infligeant %$heroes.hero_raelyn.brutal_slash.s_damage[1]%$ de dégâts réels.",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_2"] = "Frappe brutalement un ennemi avec son épée, infligeant %$heroes.hero_raelyn.brutal_slash.s_damage[2]%$ de dégâts réels.",
["HERO_RAELYN_BRUTAL_SLASH_DESCRIPTION_3"] = "Frappe brutalement un ennemi avec son épée, infligeant %$heroes.hero_raelyn.brutal_slash.s_damage[3]%$ de dégâts réels.",
["HERO_RAELYN_BRUTAL_SLASH_TITLE"] = "COUP BRUTAL",
["HERO_RAELYN_CLASS"] = "Lieutenant Sombre",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_1"] = "Invoque un Chevalier Noir ayant %$heroes.hero_raelyn.ultimate.entity.hp_max[2]%$ de santé et infligeant %$heroes.hero_raelyn.ultimate.entity.damage_min[2]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[2]%$ de dégâts réels.",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_2"] = "Le Chevalier Noir a %$heroes.hero_raelyn.ultimate.entity.hp_max[3]%$ de santé et inflige %$heroes.hero_raelyn.ultimate.entity.damage_min[3]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[3]%$ de dégâts réels.",
["HERO_RAELYN_COMMAND_ORDERS_DESCRIPTION_3"] = "Le Chevalier Noir a %$heroes.hero_raelyn.ultimate.entity.hp_max[4]%$ de santé et inflige %$heroes.hero_raelyn.ultimate.entity.damage_min[4]%$-%$heroes.hero_raelyn.ultimate.entity.damage_max[4]%$ de dégâts réels.",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_DESCRIPTION"] = "Invoque un Chevalier Noir sur le champ de bataille.",
["HERO_RAELYN_COMMAND_ORDERS_MENUBOTTOM_NAME"] = "Ordres de Commandement",
["HERO_RAELYN_COMMAND_ORDERS_TITLE"] = "ORDRES DE COMMANDEMENT",
["HERO_RAELYN_DESC"] = "La imposante Raelyn vit pour diriger les Chevaliers Noirs à l'avant-garde. Sa brutalité et son acharnement lui ont valu la reconnaissance de Vez’nan et la crainte des Liniréens. Toujours prête à un bon combat, elle fut la première volontaire à rejoindre les rangs du Sorcier Noir.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_1"] = "Étourdit les ennemis proches pendant %$heroes.hero_raelyn.inspire_fear.stun_duration[1]%$ secondes et réduit leur dégâts d'attaque de %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[1]%$% pendant %$heroes.hero_raelyn.inspire_fear.damage_duration[1]%$ secondes.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_2"] = "Étourdit les ennemis proches pendant %$heroes.hero_raelyn.inspire_fear.stun_duration[2]%$ secondes et réduit leur dégâts d'attaque de %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[2]%$% pendant %$heroes.hero_raelyn.inspire_fear.damage_duration[2]%$ secondes.",
["HERO_RAELYN_INSPIRE_FEAR_DESCRIPTION_3"] = "Étourdit les ennemis proches pendant %$heroes.hero_raelyn.inspire_fear.stun_duration[3]%$ secondes et réduit leur dégâts d'attaque de %$heroes.hero_raelyn.inspire_fear.s_inflicted_damage_factor[3]%$% pendant %$heroes.hero_raelyn.inspire_fear.damage_duration[3]%$ secondes.",
["HERO_RAELYN_INSPIRE_FEAR_TITLE"] = "INSPIRER LA PEUR",
["HERO_RAELYN_NAME"] = "Raelyn",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_1"] = "Pendant %$heroes.hero_raelyn.onslaught.duration[1]%$ secondes, Raelyn attaque plus rapidement et inflige %$heroes.hero_raelyn.onslaught.damage_factor[1]%$% de ses dégâts d'attaque dans une petite zone autour de la cible principale.",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_2"] = "Pendant %$heroes.hero_raelyn.onslaught.duration[2]%$ secondes, Raelyn attaque plus rapidement et inflige %$heroes.hero_raelyn.onslaught.damage_factor[2]%$% de ses dégâts d'attaque dans une petite zone autour de la cible principale.",
["HERO_RAELYN_ONSLAUGHT_DESCRIPTION_3"] = "Pendant %$heroes.hero_raelyn.onslaught.duration[3]%$ secondes, Raelyn attaque plus rapidement et inflige %$heroes.hero_raelyn.onslaught.damage_factor[3]%$% de ses dégâts d'attaque dans une petite zone autour de la cible principale.",
["HERO_RAELYN_ONSLAUGHT_TITLE"] = "ASSAUT",
["HERO_RAELYN_ULTIMATE_ENTITY_NAME"] = "Chevalier Noir",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_1"] = "Lorsqu'elle est au combat, Raelyn génère un bouclier de santé basé sur le nombre d'ennemis à proximité (%$heroes.hero_raelyn.unbreakable.shield_per_enemy[1]%$% de sa vie totale par chaque ennemi, jusqu'à %$heroes.hero_raelyn.unbreakable.max_targets%$ ennemis)",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_2"] = "Lorsqu'elle est en combat, Raelyn génère un bouclier de santé basé sur le nombre d'ennemis à proximité (%$heroes.hero_raelyn.unbreakable.shield_per_enemy[2]%$% de son total de vie par chacun des jusqu'à %$heroes.hero_raelyn.unbreakable.max_targets%$ ennemis)",
["HERO_RAELYN_UNBREAKABLE_DESCRIPTION_3"] = "Lorsqu'elle est en combat, Raelyn génère un bouclier de santé basé sur le nombre d'ennemis à proximité (%$heroes.hero_raelyn.unbreakable.shield_per_enemy[3]%$% de son total de vie par chacun des jusqu'à %$heroes.hero_raelyn.unbreakable.max_targets%$ ennemis)",
["HERO_RAELYN_UNBREAKABLE_TITLE"] = "INÉBRANLABLE",
["HERO_ROBOT_CLASS"] = "Golem de siège",
["HERO_ROBOT_DESC"] = "Les maîtres forgerons de l'Armée Noire se sont surpassés en créant un automate de guerre qu'ils ont judicieusement nommé Warhead. Renforcé par des moteurs enflammés et indifférent aux émotions, Warhead se lance dans la bataille sans faire de distinction entre ami et ennemi.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_1"] = "Génère une explosion ardente qui inflige %$heroes.hero_robot.explode.damage_min[1]%$-%$heroes.hero_robot.explode.damage_max[1]%$ de dégâts explosifs aux ennemis et les brûle pendant %$heroes.hero_robot.explode.burning_duration%$ secondes. La brûlure inflige %$heroes.hero_robot.explode.s_burning_damage[1]%$ de dégâts par seconde.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_2"] = "Génère une explosion ardente qui inflige %$heroes.hero_robot.explode.damage_min[2]%$-%$heroes.hero_robot.explode.damage_max[2]%$ de dégâts explosifs aux ennemis et les brûle pendant %$heroes.hero_robot.explode.burning_duration%$ secondes. La brûlure inflige %$heroes.hero_robot.explode.s_burning_damage[2]%$ de dégâts par seconde.",
["HERO_ROBOT_EXPLODE_DESCRIPTION_3"] = "Génère une explosion ardente qui inflige %$heroes.hero_robot.explode.damage_min[3]%$-%$heroes.hero_robot.explode.damage_max[3]%$ de dégâts explosifs aux ennemis et les brûle pendant %$heroes.hero_robot.explode.burning_duration%$ secondes. La brûlure inflige %$heroes.hero_robot.explode.s_burning_damage[3]%$ de dégâts par seconde.",
["HERO_ROBOT_EXPLODE_TITLE"] = "IMMOLATION",
["HERO_ROBOT_FIRE_DESCRIPTION_1"] = "Tire un canon rempli de braises ardentes, infligeant %$heroes.hero_robot.fire.damage_min[1]%$-%$heroes.hero_robot.fire.damage_max[1]%$ de dégâts physiques et ralentissant les ennemis pendant %$heroes.hero_robot.fire.s_slow_duration[1]%$ secondes.",
["HERO_ROBOT_FIRE_DESCRIPTION_2"] = "Tire un canon rempli de braises ardentes, infligeant %$heroes.hero_robot.fire.damage_min[2]%$-%$heroes.hero_robot.fire.damage_max[2]%$ de dégâts physiques et ralentissant les ennemis pendant %$heroes.hero_robot.fire.s_slow_duration[1]%$ secondes.",
["HERO_ROBOT_FIRE_DESCRIPTION_3"] = "Tire un canon rempli de braises ardentes, infligeant %$heroes.hero_robot.fire.damage_min[3]%$-%$heroes.hero_robot.fire.damage_max[3]%$ de dégâts physiques et ralentissant les ennemis pendant %$heroes.hero_robot.fire.s_slow_duration[1]%$ secondes.",
["HERO_ROBOT_FIRE_TITLE"] = "ÉCRAN DE FUMÉE",
["HERO_ROBOT_JUMP_DESCRIPTION_1"] = "Saute sur un ennemi, le stupéfiant pendant %$heroes.hero_robot.jump.stun_duration[1]%$ secondes et infligeant %$heroes.hero_robot.jump.s_damage[1]%$ de dégâts physiques dans une zone.",
["HERO_ROBOT_JUMP_DESCRIPTION_2"] = "Saute sur un ennemi, le stupéfiant pendant %$heroes.hero_robot.jump.stun_duration[2]%$ secondes et infligeant %$heroes.hero_robot.jump.s_damage[2]%$ de dégâts physiques dans une zone.",
["HERO_ROBOT_JUMP_DESCRIPTION_3"] = "Saute sur un ennemi, le stupéfiant pendant %$heroes.hero_robot.jump.stun_duration[3]%$ secondes et infligeant %$heroes.hero_robot.jump.s_damage[3]%$ de dégâts physiques dans une zone.",
["HERO_ROBOT_JUMP_TITLE"] = "IMPACT PROFOND",
["HERO_ROBOT_NAME"] = "Ogive",
["HERO_ROBOT_TRAIN_DESCRIPTION_1"] = "Invoque un chariot de guerre qui parcourt le chemin en infligeant %$heroes.hero_robot.ultimate.s_damage[2]%$ de dégâts aux ennemis et les brûlant pendant %$heroes.hero_robot.ultimate.burning_duration%$ secondes. La brûlure inflige %$heroes.hero_robot.ultimate.s_burning_damage%$ de dégâts par seconde.",
["HERO_ROBOT_TRAIN_DESCRIPTION_2"] = "Invoque un chariot de guerre qui parcourt le chemin en infligeant %$heroes.hero_robot.ultimate.s_damage[3]%$ de dégâts aux ennemis et les brûlant pendant %$heroes.hero_robot.ultimate.burning_duration%$ secondes. La brûlure inflige %$heroes.hero_robot.ultimate.s_burning_damage%$ de dégâts par seconde.",
["HERO_ROBOT_TRAIN_DESCRIPTION_3"] = "Invoque un chariot de guerre qui parcourt le chemin en infligeant %$heroes.hero_robot.ultimate.s_damage[4]%$ de dégâts aux ennemis et les brûlant pendant %$heroes.hero_robot.ultimate.burning_duration%$ secondes. La brûlure inflige %$heroes.hero_robot.ultimate.s_burning_damage%$ de dégâts par seconde.",
["HERO_ROBOT_TRAIN_MENUBOTTOM_DESCRIPTION"] = "Invoque un char de guerre qui piétine les ennemis.",
["HERO_ROBOT_TRAIN_MENUBOTTOM_NAME"] = "Tête de Moteur",
["HERO_ROBOT_TRAIN_TITLE"] = "TÊTE DE MOTEUR",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_1"] = "Frappe un ennemi ayant moins de %$heroes.hero_robot.uppercut.s_life_threshold[1]%$% de santé, le finissant instantanément.",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_2"] = "Frappe un ennemi ayant moins de %$heroes.hero_robot.uppercut.s_life_threshold[2]%$% de santé, le finissant instantanément.",
["HERO_ROBOT_UPPERCUT_DESCRIPTION_3"] = "Frappe un ennemi ayant moins de %$heroes.hero_robot.uppercut.s_life_threshold[3]%$% de santé, le finissant instantanément.",
["HERO_ROBOT_UPPERCUT_TITLE"] = "UPPERCUT DE FER",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "Ce héros est inclus dans la Campagne de la Menace Colossale",
["HERO_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "Ce héros est inclus dans la campagne « Voyage de Wukong ».",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "Campagne de la Menace Colossale",
["HERO_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "Campagne Le Voyage de Wukong",
["HERO_ROOM_EQUIPPED_HEROES"] = "Héros Équipés",
["HERO_ROOM_GET_DLC"] = "PRENEZ-LE",
["HERO_ROOM_LABEL_ROSTER_THUMB_NEW"] = "Nouveau!",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_1"] = "Invoque un reflet magique de Therien qui attaque les ennemis, infligeant %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[1]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[1]%$ de dégâts magiques.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_2"] = "Invoque un reflet magique de Therien qui attaque les ennemis, infligeant %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[2]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[2]%$ de dégâts magiques.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_DESCRIPTION_3"] = "Invoque un reflet magique de Therien qui attaque les ennemis, infligeant %$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_min[3]%$-%$heroes.hero_space_elf.astral_reflection.entity.basic_ranged.damage_max[3]%$ de dégâts magiques.",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_ENTITY_NAME"] = "Réflexion Astrale",
["HERO_SPACE_ELF_ASTRAL_REFLECTION_TITLE"] = "Réflexion Astrale",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_1"] = "Protège une unité alliée en prévenant jusqu'à %$heroes.hero_space_elf.black_aegis.shield_base[1]%$ de dégâts. Le bouclier explose après un moment, infligeant %$heroes.hero_space_elf.black_aegis.explosion_damage[1]%$ de dégâts magiques dans une zone.",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_2"] = "Protège une unité alliée en prévenant jusqu'à %$heroes.hero_space_elf.black_aegis.shield_base[2]%$ de dégâts. Le bouclier explosif inflige maintenant %$heroes.hero_space_elf.black_aegis.explosion_damage[2]%$ de dégâts magiques dans une zone.",
["HERO_SPACE_ELF_BLACK_AEGIS_DESCRIPTION_3"] = "Protège une unité alliée en prévenant jusqu'à %$heroes.hero_space_elf.black_aegis.shield_base[3]%$ de dégâts. Le bouclier explosif inflige maintenant %$heroes.hero_space_elf.black_aegis.explosion_damage[3]%$ de dégâts magiques dans une zone.",
["HERO_SPACE_ELF_BLACK_AEGIS_TITLE"] = "AEGIS NOIR",
["HERO_SPACE_ELF_CLASS"] = "Voidmancer",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_1"] = "Piège un groupe d'ennemis dans le vide pendant %$heroes.hero_space_elf.ultimate.duration[2]%$ secondes, infligeant %$heroes.hero_space_elf.ultimate.damage[2]%$ de dégâts.",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_2"] = "Piège un groupe d'ennemis dans le vide pendant %$heroes.hero_space_elf.ultimate.duration[3]%$ secondes, infligeant %$heroes.hero_space_elf.ultimate.damage[3]%$ de dégâts.",
["HERO_SPACE_ELF_COSMIC_PRISON_DESCRIPTION_3"] = "Piège un groupe d'ennemis dans le vide pendant %$heroes.hero_space_elf.ultimate.duration[4]%$ secondes, infligeant %$heroes.hero_space_elf.ultimate.damage[4]%$ de dégâts.",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_DESCRIPTION"] = "Piège des ennemis dans une zone, les blessant.",
["HERO_SPACE_ELF_COSMIC_PRISON_MENUBOTTOM_NAME"] = "Prison Cosmique",
["HERO_SPACE_ELF_COSMIC_PRISON_TITLE"] = "PRISON COSMIQUE",
["HERO_SPACE_ELF_DESC"] = "Rejetée pendant des années par ses pairs pour s'être mêlée à des forces inconnues et d'autres mondes, la voidmancer Therien se trouve maintenant être l'un des plus grands atouts de l'Alliance pour comprendre l'Observateur et toutes les forces venant d'au-delà de ce plan.",
["HERO_SPACE_ELF_NAME"] = "Therien",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_1"] = "Déforme l'espace autour de toutes les tours pendant %$heroes.hero_space_elf.spatial_distortion.duration[1]%$ secondes, augmentant leur portée de %$heroes.hero_space_elf.spatial_distortion.s_range_factor[1]%$%.",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_2"] = "Déforme l'espace autour de toutes les tours pendant %$heroes.hero_space_elf.spatial_distortion.duration[2]%$ secondes, augmentant leur portée de %$heroes.hero_space_elf.spatial_distortion.s_range_factor[2]%$%.",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_DESCRIPTION_3"] = "Déforme l'espace autour de toutes les tours pendant %$heroes.hero_space_elf.spatial_distortion.duration[3]%$ secondes, augmentant leur portée de %$heroes.hero_space_elf.spatial_distortion.s_range_factor[3]%$%.",
["HERO_SPACE_ELF_SPATIAL_DISTORTION_TITLE"] = "DISTORSION SPATIALE",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_1"] = "Ouvre %$heroes.hero_space_elf.void_rift.cracks_amount[1]%$ fissure sur le chemin pendant %$heroes.hero_space_elf.void_rift.duration[1]%$ secondes, infligeant %$heroes.hero_space_elf.void_rift.s_damage_min[1]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[1]%$ de dégâts par seconde à chaque ennemi se trouvant dessus.",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_2"] = "Ouvre %$heroes.hero_space_elf.void_rift.cracks_amount[2]%$ fissures sur le chemin pendant %$heroes.hero_space_elf.void_rift.duration[2]%$ secondes, infligeant %$heroes.hero_space_elf.void_rift.s_damage_min[2]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[2]%$ de dégâts par seconde à chaque ennemi se trouvant dessus.",
["HERO_SPACE_ELF_VOID_RIFT_DESCRIPTION_3"] = "Ouvre %$heroes.hero_space_elf.void_rift.cracks_amount[3]%$ fissures sur le chemin pendant %$heroes.hero_space_elf.void_rift.duration[3]%$ secondes, infligeant %$heroes.hero_space_elf.void_rift.s_damage_min[3]%$-%$heroes.hero_space_elf.void_rift.s_damage_max[3]%$ de dégâts par seconde à chaque ennemi se trouvant dessus.",
["HERO_SPACE_ELF_VOID_RIFT_TITLE"] = "FAILLE DU VIDE",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_1"] = "Invoque %$heroes.hero_spider.ultimate.spawn_amount[2]%$ araignées qui combattent pendant %$heroes.hero_spider.ultimate.spider.duration[2]%$ secondes, étourdissant les ennemis à chaque coup.",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_2"] = "Invoque %$heroes.hero_spider.ultimate.spawn_amount[3]%$ araignées qui combattent pendant %$heroes.hero_spider.ultimate.spider.duration[3]%$ secondes, étourdissant les ennemis à chaque coup.",
["HERO_SPIDER_ARACNID_SPAWNER_DESCRIPTION_3"] = "Invoque %$heroes.hero_spider.ultimate.spawn_amount[4]%$ araignées qui combattent pendant %$heroes.hero_spider.ultimate.spider.duration[4]%$ secondes, étourdissant les ennemis à chaque coup.",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_DESCRIPTION"] = "Invoque une meute d’araignées étourdissantes.",
["HERO_SPIDER_ARACNID_SPAWNER_MENUBOTTOM_NAME"] = "Appel du Chasseur",
["HERO_SPIDER_ARACNID_SPAWNER_TITLE"] = "Appel du Chasseur",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_1"] = "Toutes les %$heroes.hero_spider.area_attack.cooldown[1]%$ secondes, Spydyr affirme sa présence, étourdissant les ennemis proches pendant %$heroes.hero_spider.area_attack.s_stun_time[1]%$ secondes.",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_2"] = "Toutes les %$heroes.hero_spider.area_attack.cooldown[2]%$ secondes, Spydyr affirme sa présence, étourdissant les ennemis proches pendant %$heroes.hero_spider.area_attack.s_stun_time[2]%$ secondes.",
["HERO_SPIDER_AREA_ATTACK_DESCRIPTION_3"] = "Toutes les %$heroes.hero_spider.area_attack.cooldown[3]%$ secondes, Spydyr affirme sa présence, étourdissant les ennemis proches pendant %$heroes.hero_spider.area_attack.s_stun_time[3]%$ secondes.",
["HERO_SPIDER_AREA_ATTACK_TITLE"] = "Présence Écrasante",
["HERO_SPIDER_DESC"] = "Spydyr est la dernière survivante d’un groupe d’Elfes du Crépuscule chargé d’anéantir le Culte de la Reine Araignée. Alliant la magie des ombres à ses talents de chasse inégalés, elle est redoutée comme l’une des assassines les plus mortelles de tous les royaumes.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_1"] = "Toutes les %$heroes.hero_spider.instakill_melee.cooldown[1]%$ secondes, Spydyr peut exécuter un ennemi étourdi dont la santé est inférieure à %$heroes.hero_spider.instakill_melee.life_threshold[1]%$.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_2"] = "Toutes les %$heroes.hero_spider.instakill_melee.cooldown[2]%$ secondes, Spydyr peut exécuter un ennemi étourdi dont la santé est inférieure à %$heroes.hero_spider.instakill_melee.life_threshold[2]%$.",
["HERO_SPIDER_INSTAKILL_MELEE_DESCRIPTION_3"] = "Toutes les %$heroes.hero_spider.instakill_melee.cooldown[3]%$ secondes, Spydyr peut exécuter un ennemi étourdi dont la santé est inférieure à %$heroes.hero_spider.instakill_melee.life_threshold[3]%$.",
["HERO_SPIDER_INSTAKILL_MELEE_TITLE"] = "Étreinte de la Mort",
["HERO_SPIDER_NAME"] = "Spydyr",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_1"] = "En un clin d'œil, Spydyr se téléporte vers l’ennemi ayant le plus de santé, lui infligeant %$heroes.hero_spider.supreme_hunter.damage_min[1]%$-%$heroes.hero_spider.supreme_hunter.damage_max[1]%$ dégâts.",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_2"] = "En un clin d'œil, Spydyr se téléporte vers l’ennemi ayant le plus de santé, lui infligeant %$heroes.hero_spider.supreme_hunter.damage_min[2]%$-%$heroes.hero_spider.supreme_hunter.damage_max[2]%$ dégâts.",
["HERO_SPIDER_SUPREME_HUNTER_DESCRIPTION_3"] = "En un clin d'œil, Spydyr se téléporte vers l’ennemi ayant le plus de santé, lui infligeant %$heroes.hero_spider.supreme_hunter.damage_min[3]%$-%$heroes.hero_spider.supreme_hunter.damage_max[3]%$ dégâts.",
["HERO_SPIDER_SUPREME_HUNTER_TITLE"] = "Pas de l’Ombre",
["HERO_SPIDER_TUNNELING_DESCRIPTION_1"] = "Le creusement de Spydyr inflige désormais %$heroes.hero_spider.tunneling.damage_min[1]%$-%$heroes.hero_spider.tunneling.damage_max[1]%$ de dégâts en refaisant surface.",
["HERO_SPIDER_TUNNELING_DESCRIPTION_2"] = "Le creusement de Spydyr inflige désormais %$heroes.hero_spider.tunneling.damage_min[2]%$-%$heroes.hero_spider.tunneling.damage_max[2]%$ de dégâts en refaisant surface.",
["HERO_SPIDER_TUNNELING_DESCRIPTION_3"] = "Le creusement de Spydyr inflige désormais %$heroes.hero_spider.tunneling.damage_min[3]%$-%$heroes.hero_spider.tunneling.damage_max[3]%$ de dégâts en refaisant surface.",
["HERO_SPIDER_TUNNELING_TITLE"] = "Creusement",
["HERO_VENOM_CLASS"] = "Pourfendeur Corrompu",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_1"] = "Remplit une zone avec une substance gluante qui ralentit les ennemis et, après un moment, se transforme en épines perforantes qui infligent %$heroes.hero_venom.ultimate.s_damage[2]%$ de dégâts réels.",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_2"] = "Remplit une zone avec une substance gluante qui ralentit les ennemis et, après un moment, se transforme en épines perforantes qui infligent %$heroes.hero_venom.ultimate.s_damage[3]%$ de dégâts réels.",
["HERO_VENOM_CREEPING_DEATH_DESCRIPTION_3"] = "Remplit une zone avec une substance gluante qui ralentit les ennemis et, après un moment, se transforme en épines perforantes qui infligent %$heroes.hero_venom.ultimate.s_damage[4]%$ de dégâts réels.",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_DESCRIPTION"] = "Invoque une substance gluante sur le chemin qui ralentit et endommage les ennemis.",
["HERO_VENOM_CREEPING_DEATH_MENUBOTTOM_NAME"] = "Mort Rampante",
["HERO_VENOM_CREEPING_DEATH_TITLE"] = "MORT RAMPANTE",
["HERO_VENOM_DESC"] = "Après avoir résisté à être transformé en abomination par le Culte, le mercenaire Grimson a été emprisonné et laissé pourrir. Le processus tortueux lui a accordé des pouvoirs de métamorphose, qu'il a utilisés pour s'échapper du Culte, jurant de revenir pour se venger.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_1"] = "Grimson dévore un ennemi ayant moins de %$heroes.hero_venom.eat_enemy.hp_trigger%$% de santé, regagnant %$heroes.hero_venom.eat_enemy.regen[1]%$% de sa propre santé totale dans le processus.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_2"] = "Grimson dévore un ennemi ayant moins de %$heroes.hero_venom.eat_enemy.hp_trigger%$% de santé, regagnant %$heroes.hero_venom.eat_enemy.regen[2]%$% de sa propre santé totale dans le processus.",
["HERO_VENOM_EAT_ENEMY_DESCRIPTION_3"] = "Grimson dévore un ennemi ayant moins de %$heroes.hero_venom.eat_enemy.hp_trigger%$% de santé, regagnant %$heroes.hero_venom.eat_enemy.regen[3]%$% de sa propre santé totale dans le processus.",
["HERO_VENOM_EAT_ENEMY_TITLE"] = "RENOUVELER LA CHAIR",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_1"] = "Étend des vrilles épineuses sur le chemin, infligeant %$heroes.hero_venom.floor_spikes.s_damage[1]%$ de dégâts réels par épine aux ennemis proches.",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_2"] = "Étend des vrilles épineuses sur le chemin, infligeant %$heroes.hero_venom.floor_spikes.s_damage[2]%$ de dégâts réels par épine aux ennemis proches.",
["HERO_VENOM_FLOOR_SPIKES_DESCRIPTION_3"] = "Étend des vrilles épineuses sur le chemin, infligeant %$heroes.hero_venom.floor_spikes.s_damage[3]%$ de dégâts réels par épine aux ennemis proches.",
["HERO_VENOM_FLOOR_SPIKES_TITLE"] = "PICS MORTELS",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_1"] = "Lorsque sa santé est inférieure à %$heroes.hero_venom.inner_beast.trigger_hp%$%, Grimson se transforme totalement, gagnant %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[1]%$% de dégâts supplémentaires et se soignant de %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$% de sa vie totale par coup pendant %$heroes.hero_venom.inner_beast.duration%$ secondes.",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_2"] = "Lorsque sa santé est inférieure à %$heroes.hero_venom.inner_beast.trigger_hp%$%, Grimson se transforme totalement, gagnant %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[2]%$% de dégâts supplémentaires et se soignant de %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$% de sa vie totale par coup pendant %$heroes.hero_venom.inner_beast.duration%$ secondes.",
["HERO_VENOM_INNER_BEAST_DESCRIPTION_3"] = "Lorsque sa santé est inférieure à %$heroes.hero_venom.inner_beast.trigger_hp%$%, Grimson se transforme totalement, gagnant %$heroes.hero_venom.inner_beast.basic_melee.s_damage_factor[3]%$% de dégâts supplémentaires et se soignant de %$heroes.hero_venom.inner_beast.basic_melee.regen_health%$% de sa vie totale par coup pendant %$heroes.hero_venom.inner_beast.duration%$ secondes.",
["HERO_VENOM_INNER_BEAST_TITLE"] = "BÊTE INTÉRIEURE",
["HERO_VENOM_NAME"] = "Grimson",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_1"] = "Frappe un ennemi à distance, infligeant %$heroes.hero_venom.ranged_tentacle.s_damage[1]%$ de dégâts physiques avec %$heroes.hero_venom.ranged_tentacle.bleed_chance[1]%$% de chance de causer des saignements. Le saignement inflige %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$ de dégâts par seconde pendant %$heroes.hero_venom.ranged_tentacle.bleed_duration[1]%$ secondes.",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_2"] = "Frappe un ennemi à distance, infligeant %$heroes.hero_venom.ranged_tentacle.s_damage[2]%$ de dégâts physiques avec %$heroes.hero_venom.ranged_tentacle.bleed_chance[2]%$% de chance de causer des saignements. Le saignement inflige %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$ de dégâts par seconde pendant %$heroes.hero_venom.ranged_tentacle.bleed_duration[2]%$ secondes.",
["HERO_VENOM_RANGED_TENTACLE_DESCRIPTION_3"] = "Frappe un ennemi à distance, infligeant %$heroes.hero_venom.ranged_tentacle.s_damage[3]%$ de dégâts physiques avec %$heroes.hero_venom.ranged_tentacle.bleed_chance[3]%$% de chance de causer des saignements. Le saignement inflige %$heroes.hero_venom.ranged_tentacle.s_bleed_damage%$ de dégâts par seconde pendant %$heroes.hero_venom.ranged_tentacle.bleed_duration[3]%$ secondes.",
["HERO_VENOM_RANGED_TENTACLE_TITLE"] = "CHERCHEUR DE CŒUR",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_1"] = "Arrose une zone de %$heroes.hero_vesper.ultimate.s_spread[2]%$ flèches, chacune infligeant %$heroes.hero_vesper.ultimate.damage[2]%$ de dégâts physiques aux ennemis.",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_2"] = "Arrose une zone de %$heroes.hero_vesper.ultimate.s_spread[3]%$ flèches, chacune infligeant %$heroes.hero_vesper.ultimate.damage[3]%$ de dégâts physiques aux ennemis.",
["HERO_VESPER_ARROW_STORM_DESCRIPTION_3"] = "Arrose une zone de %$heroes.hero_vesper.ultimate.s_spread[4]%$ flèches, chacune infligeant %$heroes.hero_vesper.ultimate.damage[4]%$ de dégâts physiques aux ennemis.",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_DESCRIPTION"] = "Arrose une zone avec des flèches, infligeant des dégâts aux ennemis.",
["HERO_VESPER_ARROW_STORM_MENUBOTTOM_NAME"] = "Pluie de Flèches",
["HERO_VESPER_ARROW_STORM_TITLE"] = "PLUIE DE FLÈCHES",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_1"] = "Tire une flèche qui étourdit l'ennemi pendant %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[1]%$ secondes, infligeant %$heroes.hero_vesper.arrow_to_the_knee.s_damage[1]%$ de dégâts physiques.",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_2"] = "Tire une flèche qui étourdit l'ennemi pendant %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[2]%$ secondes, infligeant %$heroes.hero_vesper.arrow_to_the_knee.s_damage[2]%$ de dégâts physiques.",
["HERO_VESPER_ARROW_TO_THE_KNEE_DESCRIPTION_3"] = "Tire une flèche qui étourdit l'ennemi pendant %$heroes.hero_vesper.arrow_to_the_knee.stun_duration[3]%$ seconde, infligeant %$heroes.hero_vesper.arrow_to_the_knee.s_damage[3]%$ de dégâts physiques.",
["HERO_VESPER_ARROW_TO_THE_KNEE_TITLE"] = "FLÈCHE DANS LE GENOU",
["HERO_VESPER_CLASS"] = "Capitaine Royal",
["HERO_VESPER_DESC"] = "Doux à l'épée comme à l'arc, Vesper a gagné sa place de commandant des forces de Linirea. Après la chute de Linirea et la disparition du roi Denas, il a rassemblé toutes les troupes qu'il a pu et a commencé une croisade pour ramener l'ancien souverain.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_1"] = "Lorsqu'il est en dessous de %$heroes.hero_vesper.disengage.hp_to_trigger%$% de santé, Vesper esquive la prochaine attaque en mêlée en sautant en arrière. Il tire ensuite trois flèches qui infligent %$heroes.hero_vesper.disengage.s_damage[1]%$ de dégâts physiques chacune aux ennemis proches.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_2"] = "Lorsqu'il est en dessous de %$heroes.hero_vesper.disengage.hp_to_trigger%$% de santé, Vesper esquive la prochaine attaque en mêlée en sautant en arrière. Il tire ensuite trois flèches qui infligent %$heroes.hero_vesper.disengage.s_damage[2]%$ de dégâts physiques chacune aux ennemis proches.",
["HERO_VESPER_DISENGAGE_DESCRIPTION_3"] = "Lorsqu'il est en dessous de %$heroes.hero_vesper.disengage.hp_to_trigger%$% de santé, Vesper esquive la prochaine attaque en mêlée en sautant en arrière. Il tire ensuite trois flèches qui infligent %$heroes.hero_vesper.disengage.s_damage[3]%$ de dégâts physiques chacune aux ennemis proches.",
["HERO_VESPER_DISENGAGE_TITLE"] = "SE DÉSENGAGER",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_1"] = "Frappe un ennemi trois fois, infligeant %$heroes.hero_vesper.martial_flourish.s_damage[1]%$ de dégâts physiques.",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_2"] = "Frappe un ennemi trois fois, infligeant %$heroes.hero_vesper.martial_flourish.s_damage[2]%$ de dégâts physiques.",
["HERO_VESPER_MARTIAL_FLOURISH_DESCRIPTION_3"] = "Frappe un ennemi trois fois, infligeant %$heroes.hero_vesper.martial_flourish.s_damage[3]%$ de dégâts physiques.",
["HERO_VESPER_MARTIAL_FLOURISH_TITLE"] = "PANACHE MARTIAL",
["HERO_VESPER_NAME"] = "Vesper",
["HERO_VESPER_RICOCHET_DESCRIPTION_1"] = "Tire une flèche qui rebondit entre %$heroes.hero_vesper.ricochet.s_bounces[1]%$ ennemis, infligeant %$heroes.hero_vesper.ricochet.s_damage[1]%$ de dégâts physiques à chaque fois.",
["HERO_VESPER_RICOCHET_DESCRIPTION_2"] = "Tire une flèche qui rebondit entre %$heroes.hero_vesper.ricochet.s_bounces[2]%$ ennemis, infligeant %$heroes.hero_vesper.ricochet.s_damage[2]%$ de dégâts physiques à chaque fois.",
["HERO_VESPER_RICOCHET_DESCRIPTION_3"] = "Tire une flèche qui rebondit entre %$heroes.hero_vesper.ricochet.s_bounces[3]%$ ennemis, infligeant %$heroes.hero_vesper.ricochet.s_damage[3]%$ de dégâts physiques à chaque fois.",
["HERO_VESPER_RICOCHET_TITLE"] = "FLÈCHE RICOCHET",
["HERO_WITCH_CLASS"] = "Sorcière farceuse",
["HERO_WITCH_DESC"] = "Bien qu'elle aime surprendre les étrangers qui traversent la forêt féerique avec des tours amusants et inoffensifs, ceux qui représentent une menace pour les bois ou ses compagnons gnomes découvrent rapidement que son sourire espiègle cache une sorcière implacable avec laquelle il faut compter.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_1"] = "Lorsque sa santé est inférieure à %$heroes.hero_witch.disengage.hp_to_trigger%$%, Stregi se téléporte en arrière en laissant un leurre pour combattre à sa place. Le leurre a %$heroes.hero_witch.disengage.decoy.hp_max[1]%$ points de vie et explose lorsqu'il est détruit, étourdissant les ennemis pendant %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[1]%$ seconde.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_2"] = "Lorsque sa santé est inférieure à %$heroes.hero_witch.disengage.hp_to_trigger%$%, Stregi se téléporte en arrière en laissant un leurre pour combattre à sa place. Le leurre a %$heroes.hero_witch.disengage.decoy.hp_max[2]%$ points de vie et explose lorsqu'il est détruit, étourdissant les ennemis pendant %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[2]%$ secondes.",
["HERO_WITCH_DISENGAGE_DESCRIPTION_3"] = "Lorsque sa santé est inférieure à %$heroes.hero_witch.disengage.hp_to_trigger%$%, Stregi se téléporte en arrière en laissant un leurre pour combattre à sa place. Le leurre a %$heroes.hero_witch.disengage.decoy.hp_max[3]%$ points de vie et explose lorsqu'il est détruit, étourdissant les ennemis pendant %$heroes.hero_witch.disengage.decoy.explotion.stun_duration[3]%$ secondes.",
["HERO_WITCH_DISENGAGE_TITLE"] = "LEURRE ÉBLOUISSANT",
["HERO_WITCH_NAME"] = "Stregi",
["HERO_WITCH_PATH_AOE_DESCRIPTION_1"] = "Lance une potion géante sur le chemin, infligeant %$heroes.hero_witch.skill_path_aoe.s_damage[1]%$ de dégâts magiques dans une zone et ralentissant les ennemis pendant %$heroes.hero_witch.skill_path_aoe.duration[1]%$ secondes.",
["HERO_WITCH_PATH_AOE_DESCRIPTION_2"] = "Lance une potion géante sur le chemin, infligeant %$heroes.hero_witch.skill_path_aoe.s_damage[2]%$ de dégâts magiques dans une zone et ralentissant les ennemis pendant %$heroes.hero_witch.skill_path_aoe.duration[2]%$ secondes.",
["HERO_WITCH_PATH_AOE_DESCRIPTION_3"] = "Lance une potion géante sur le chemin, infligeant %$heroes.hero_witch.skill_path_aoe.s_damage[3]%$ de dégâts magiques dans une zone et ralentissant les ennemis pendant %$heroes.hero_witch.skill_path_aoe.duration[3]%$ secondes.",
["HERO_WITCH_PATH_AOE_TITLE"] = "TOURNICOTI ÉCLATE !",
["HERO_WITCH_POLYMORPH_DESCRIPTION_1"] = "Transforme un ennemi en un petit citrouille pendant %$heroes.hero_witch.skill_polymorph.duration[1]%$ secondes. Le petit citrouille possède %$heroes.hero_witch.skill_polymorph.pumpkin.hp[1]%$% de la santé de la cible.",
["HERO_WITCH_POLYMORPH_DESCRIPTION_2"] = "Transforme un ennemi en un petit citrouille pendant %$heroes.hero_witch.skill_polymorph.duration[2]%$ secondes. Le petit citrouille possède %$heroes.hero_witch.skill_polymorph.pumpkin.hp[2]%$% de la santé de la cible.",
["HERO_WITCH_POLYMORPH_DESCRIPTION_3"] = "Transforme un ennemi en un petit citrouille pendant %$heroes.hero_witch.skill_polymorph.duration[3]%$ secondes. Le petit citrouille possède %$heroes.hero_witch.skill_polymorph.pumpkin.hp[3]%$% de la santé de la cible.",
["HERO_WITCH_POLYMORPH_TITLE"] = "VÉGÉTALISATION!",
["HERO_WITCH_SOLDIERS_DESCRIPTION_1"] = "Invoque %$heroes.hero_witch.skill_soldiers.soldiers_amount[1]%$ chat qui combat les ennemis. Le chat a %$heroes.hero_witch.skill_soldiers.soldier.hp_max[1]%$ points de vie et inflige %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[1]%$ de dégâts physiques.",
["HERO_WITCH_SOLDIERS_DESCRIPTION_2"] = "Invoque %$heroes.hero_witch.skill_soldiers.soldiers_amount[2]%$ chats qui combattent les ennemis. Les chats ont %$heroes.hero_witch.skill_soldiers.soldier.hp_max[2]%$ points de vie et infligent %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[2]%$ de dégâts physiques.",
["HERO_WITCH_SOLDIERS_DESCRIPTION_3"] = "Invoque %$heroes.hero_witch.skill_soldiers.soldiers_amount[3]%$ chats qui combattent les ennemis. Les chats ont %$heroes.hero_witch.skill_soldiers.soldier.hp_max[3]%$ points de vie et infligent %$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_min[3]%$-%$heroes.hero_witch.skill_soldiers.soldier.melee_attack.damage_max[3]%$ de dégâts physiques.",
["HERO_WITCH_SOLDIERS_TITLE"] = "FURIES NOCTURNES",
["HERO_WITCH_ULTIMATE_DESCRIPTION_1"] = "Téléporte %$heroes.hero_witch.ultimate.max_targets[2]%$ ennemis en arrière, les laissant endormis pendant %$heroes.hero_witch.ultimate.duration[2]%$ seconde.",
["HERO_WITCH_ULTIMATE_DESCRIPTION_2"] = "Téléporte %$heroes.hero_witch.ultimate.max_targets[3]%$ ennemis en arrière, les laissant endormis pendant %$heroes.hero_witch.ultimate.duration[3]%$ secondes.",
["HERO_WITCH_ULTIMATE_DESCRIPTION_3"] = "Téléporte %$heroes.hero_witch.ultimate.max_targets[4]%$ ennemis en arrière, les laissant endormis pendant %$heroes.hero_witch.ultimate.duration[4]%$ secondes.",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "Téléporte les ennemis en arrière sur le chemin, les laissant endormis pendant un certain temps.",
["HERO_WITCH_ULTIMATE_MENUBOTTOM_NAME"] = "Retour Somnolent",
["HERO_WITCH_ULTIMATE_TITLE"] = "RETOUR SOMNOLENT",
["HERO_WUKONG_CLASS"] = "Le Roi Singe",
["HERO_WUKONG_DESC"] = "Né d'une pierre céleste de Yin et Yang, Sun Wukong reçut la force, l'agilité et l'immortalité. Mais les rois démons lui ont volé les sphères de pouvoir. À présent, le légendaire farceur se lève pour les récupérer avant qu’il ne soit trop tard.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_1"] = "Tombe et agrandit le Jingu Bang pour écraser un ennemi, le tuant instantanément et infligeant %$heroes.hero_wukong.giant_staff.area_damage.damage_min[1]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[1]%$ de dégâts dans une zone autour de la cible.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_2"] = "Tombe et agrandit le Jingu Bang pour écraser un ennemi, le tuant instantanément et infligeant %$heroes.hero_wukong.giant_staff.area_damage.damage_min[2]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[2]%$ de dégâts dans une zone autour de la cible.",
["HERO_WUKONG_GIANT_STAFF_DESCRIPTION_3"] = "Tombe et agrandit le Jingu Bang pour écraser un ennemi, le tuant instantanément et infligeant %$heroes.hero_wukong.giant_staff.area_damage.damage_min[3]%$-%$heroes.hero_wukong.giant_staff.area_damage.damage_max[3]%$ de dégâts dans une zone autour de la cible.",
["HERO_WUKONG_GIANT_STAFF_TITLE"] = "Technique du Jingu Bang",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_1"] = "Suscite 2 clones capillaires de Sun Wukong pour combattre à ses côtés. Ils infligent %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[1]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[1]%$ de dégâts et durent %$heroes.hero_wukong.hair_clones.soldier.duration[1]%$ secondes.",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_2"] = "Suscite 2 clones capillaires de Sun Wukong pour combattre à ses côtés. Ils infligent %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[2]%$-%$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[2]%$ de dégâts et durent %$heroes.hero_wukong.hair_clones.soldier.duration[2]%$ secondes.",
["HERO_WUKONG_HAIR_CLONES_DESCRIPTION_3"] = "Suscite 2 clones capillaires de Sun Wukong pour combattre à ses côtés. Ils infligent entre %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_min[3]%$ et %$heroes.hero_wukong.hair_clones.soldier.melee_attack.damage_max[3]%$ de dégâts et durent %$heroes.hero_wukong.hair_clones.soldier.duration[3]%$ secondes.",
["HERO_WUKONG_HAIR_CLONES_TITLE"] = "Hair Clones",
["HERO_WUKONG_NAME"] = "Sun Wukong",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_1"] = "Lance le Jingu Bang dans les airs, le multipliant en %$heroes.hero_wukong.pole_ranged.pole_amounts[1]%$ bâtons qui retombent sur les ennemis, infligeant chacun %$heroes.hero_wukong.pole_ranged.damage_min[1]%$ de dégâts et étourdissant les ennemis dans une petite zone.",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_2"] = "Lance le Jingu Bang dans les airs, le multipliant en %$heroes.hero_wukong.pole_ranged.pole_amounts[2]%$ bâtons qui retombent sur les ennemis, infligeant chacun %$heroes.hero_wukong.pole_ranged.damage_min[2]%$ de dégâts et étourdissant les ennemis dans une petite zone.",
["HERO_WUKONG_POLE_RANGED_DESCRIPTION_3"] = "Lance le Jingu Bang dans les airs, le multipliant en %$heroes.hero_wukong.pole_ranged.pole_amounts[3]%$ bâtons qui retombent sur les ennemis, infligeant chacun %$heroes.hero_wukong.pole_ranged.damage_min[3]%$ de dégâts et étourdissant les ennemis dans une petite zone.",
["HERO_WUKONG_POLE_RANGED_TITLE"] = "Barrage de Poteaux",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_1"] = "Le Dragon Blanc s’écrase au sol avec une force colossale, infligeant %$heroes.hero_wukong.ultimate.damage_total[2]%$ de dégâts réels et laissant une zone de ralentissement.",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_2"] = "Le Dragon Blanc s’écrase au sol avec une force prodigieuse, infligeant %$heroes.hero_wukong.ultimate.damage_total[3]%$ de dégâts réels et laissant une zone de ralentissement.",
["HERO_WUKONG_ULTIMATE_DESCRIPTION_3"] = "Le Dragon Blanc s'écrase au sol avec une force colossale, infligeant %$heroes.hero_wukong.ultimate.damage_total[4]%$ de dégâts réels et laissant une zone de ralentissement.",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_DESCRIPTION"] = "Appelle le Dragon Blanc.",
["HERO_WUKONG_ULTIMATE_MENUBOTTOM_NAME"] = "Le Dragon Blanc",
["HERO_WUKONG_ULTIMATE_TITLE"] = "Le Dragon Blanc",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_1"] = "Zhu Bajie, le fidèle compagnon de Sun Wukong, le suit partout. Inflige %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[1]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[1]%$ de dégâts et a une faible chance d'infliger une attaque de zone importante.",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_2"] = "Zhu Bajie, le fidèle compagnon de Sun Wukong, le suit partout. Inflige %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[2]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[2]%$ de dégâts et a une faible chance d'infliger une attaque de zone importante.",
["HERO_WUKONG_ZHU_APPRENTICE_DESCRIPTION_3"] = "Zhu Bajie, le fidèle compagnon de Sun Wukong, le suit partout. Inflige %$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_min[3]%$-%$heroes.hero_wukong.zhu_apprentice.melee_attack.damage_max[3]%$ de dégâts et a une petite chance de provoquer une attaque de zone puissante.",
["HERO_WUKONG_ZHU_APPRENTICE_TITLE"] = "Zhu - Apprenti",
["HINT"] = "ASTUCE",
["HOURS_ABBREVIATION"] = "h",
["Hardcore! play at your own risk!"] = "Super dangereux! Joue à tes risques et périls!",
["Help"] = "Aide",
["Hero at your command!"] = "Héros à ton commandement!",
["Heroes"] = "Héros",
["Heroes are elite units that can face strong enemies and support your forces."] = "Les héros sont des unités d'élite qui peuvent affronter des ennemis puissants et soutenir tes forces.",
["Heroes gain experience every time they damage an enemy or use an ability."] = "Les héros gagnent de l'expérience à chaque fois qu'ils blessent un ennemi ou qu'ils utilisent une capacité.",
["Heroic"] = "Héroïque",
["Heroic challenge"] = "Défi héroïque",
["High"] = "Élevé",
["I'm ready. Now bring it on!"] = "Aller, fais parler l'acier!",
["INCOMING NEXT WAVE!"] = "PROCHAINE VAGUE EN APPROCHE!",
["INCOMING WAVE"] = "VAGUE EN APPROCHE",
["INGAME_BALLOON_BUILD_HERE"] = "Construisez ici !",
["INGAME_BALLOON_GOAL"] = "Ne laissez pas les ennemis passer au-delà de ce point",
["INGAME_BALLOON_GOLD"] = "Gagnez de l'or en tuant des ennemis",
["INGAME_BALLOON_INCOMING"] = "PROCHAINE VAGUE IMMINENTE !",
["INGAME_BALLOON_NEW_HERO"] = "Nouveau héros !",
["INGAME_BALLOON_NEW_POWER"] = "Nouveau pouvoir !",
["INGAME_BALLOON_NOTIFICATION_TAP_HERE"] = "Tapez ici !",
["INGAME_BALLOON_SELECT_HERO"] = "Tapez pour sélectionner !",
["INGAME_BALLOON_START_BATTLE"] = "COMMENCER LA BATAILLE !",
["INGAME_BALLOON_TAP_HERE"] = "Tapez sur la route",
["INGAME_BALLOON_TAP_TO_CALL"] = "TAPER POUR L'APPELER TÔT",
["INGAME_BALLOON_TAP_TWICE_BUILD"] = "Touchez pour construire une tour",
["INGAME_BALLOON_TAP_TWICE_START"] = "TAPER DEUX FOIS POUR COMMENCER LA BATAILLE",
["INGAME_BALLOON_TAP_TWICE_WAVE"] = "Touchez pour appeler une vague",
["INGAME_TUTORIAL1_HELP1"] = "Ne laissez pas les ennemis passer ce point.",
["INGAME_TUTORIAL1_HELP2"] = "Construisez des tours pour défendre la route.",
["INGAME_TUTORIAL1_HELP3"] = "Gagnez de l'or en tuant des ennemis.",
["INGAME_TUTORIAL1_SUBTITLE1"] = "Protégez vos terres des attaques ennemies.",
["INGAME_TUTORIAL1_SUBTITLE2"] = "Construisez des tours défensives le long de la route pour les arrêter.",
["INGAME_TUTORIAL1_TITLE"] = "Objectif",
["INGAME_TUTORIAL_GOTCHA_1"] = "Compris !",
["INGAME_TUTORIAL_GOTCHA_2"] = "Je suis prêt, allez-y !",
["INGAME_TUTORIAL_HINT"] = "INDICE",
["INGAME_TUTORIAL_INSTRUCTIONS"] = "INSTRUCTIONS",
["INGAME_TUTORIAL_NEW_TIP"] = "NOUVEAU CONSEIL",
["INGAME_TUTORIAL_NEXT"] = "Suivant !",
["INGAME_TUTORIAL_OK"] = "Ok !",
["INGAME_TUTORIAL_SKIP"] = "Sauter ça !",
["INGAME_TUTORIAL_TIP_CHALLENGE"] = "AVERTISSEMENT",
["INSTRUCTIONS"] = "INSTRUCTIONS",
["ITEM_CLUSTER_BOMB_BOTTOM_DESC"] = "Comme le popcorn, mais beaucoup plus amusant et moins savoureux.",
["ITEM_CLUSTER_BOMB_BOTTOM_INFO"] = "Une bombe qui crée de nouvelles petites bombes.",
["ITEM_CLUSTER_BOMB_DESC"] = "Lancez une bombe qui endommage les ennemis dans la zone et lance d'autres petites bombes autour.",
["ITEM_CLUSTER_BOMB_NAME"] = "Bombe à fragmentation",
["ITEM_DEATHS_TOUCH_BOTTOM_DESC"] = "Parfait quand vous voulez vous sentir comme un dieu... DE LA MORT!",
["ITEM_DEATHS_TOUCH_BOTTOM_INFO"] = "Sélectionnez. Touchez votre cible. Tuez.",
["ITEM_DEATHS_TOUCH_DESC"] = "Imprégnez-vous du pouvoir de la Mort et touchez n'importe quel ennemi pour l'éliminer instantanément. Ne fonctionne pas sur les boss ou les mini-boss.",
["ITEM_DEATHS_TOUCH_NAME"] = "Toucher de la Mort",
["ITEM_LOOT_BOX_BOTTOM_DESC"] = "Quelques-uns de ceux-ci et vous êtes prêt pour la vie.",
["ITEM_LOOT_BOX_BOTTOM_INFO"] = "Lâchez une caisse sur le chemin, endommageant les ennemis et obtenant instantanément de l'or.",
["ITEM_LOOT_BOX_DESC"] = "Lâchez une caisse sur le chemin, endommageant les ennemis et obtenant instantanément 300 or.",
["ITEM_LOOT_BOX_NAME"] = "Boîte de Filon",
["ITEM_MEDICAL_KIT_BOTTOM_DESC"] = "Tout ce dont vous avez besoin pour vous remettre sur pied, général.",
["ITEM_MEDICAL_KIT_BOTTOM_INFO"] = "Restaure jusqu'à 3 cœurs au joueur.",
["ITEM_MEDICAL_KIT_DESC"] = "Un kit spécial qui restaure jusqu'à 3 cœurs au joueur.",
["ITEM_MEDICAL_KIT_NAME"] = "Trousse médicale",
["ITEM_PORTABLE_COIL_BOTTOM_DESC"] = "Zip ! Zap ! Frit comme un rat !",
["ITEM_PORTABLE_COIL_BOTTOM_INFO"] = "Installe un piège qui inflige des dégâts et étourdit les ennemis dans une zone.",
["ITEM_PORTABLE_COIL_DESC"] = "Installe un piège de zone qui blesse et étourdit les ennemis qui le déclenchent. Ses effets peuvent se propager aux ennemis proches.",
["ITEM_PORTABLE_COIL_NAME"] = "Bobine portable",
["ITEM_ROOM_EQUIP"] = "Équiper",
["ITEM_ROOM_EQUIPPED"] = "Équipé",
["ITEM_ROOM_EQUIPPED_ITEMS"] = "Articles équipés",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_DESC"] = "Vous êtes déjà à court de temps pour combattre vos ennemis ? Ne vous inquiétez plus !",
["ITEM_SCROLL_OF_SPACESHIFT_BOTTOM_INFO"] = "Téléportez un groupe d'ennemis en arrière sur le chemin.",
["ITEM_SCROLL_OF_SPACESHIFT_DESC"] = "Téléportez un groupe d'ennemis en arrière sur le chemin.",
["ITEM_SCROLL_OF_SPACESHIFT_NAME"] = "Parchemin de Transfert",
["ITEM_SECOND_BREATH_BOTTOM_DESC"] = "Sortez de la tombe, sans l'inconvénient des morts-vivants.",
["ITEM_SECOND_BREATH_BOTTOM_INFO"] = "Ressuscite les héros tombés, soigne les blessés et réinitialise le temps de recharge des pouvoirs des héros.",
["ITEM_SECOND_BREATH_DESC"] = "Une bénédiction divine qui ressuscite les héros tombés, soigne les blessés et réinitialise le temps de recharge des pouvoirs des héros.",
["ITEM_SECOND_BREATH_NAME"] = "Deuxième Souffle",
["ITEM_SUMMON_BLACKBURN_BOTTOM_DESC"] = "L'unique. Le seul. L'inimitable.",
["ITEM_SUMMON_BLACKBURN_BOTTOM_INFO"] = "Invoquez le puissant Blackburn pour combattre à vos côtés.",
["ITEM_SUMMON_BLACKBURN_DESC"] = "Invoquez le puissant guerrier revenant pour vaincre vos ennemis.",
["ITEM_SUMMON_BLACKBURN_NAME"] = "Heaume de Blackburn",
["ITEM_VEZNAN_WRATH_BOTTOM_DESC"] = "Qu'ils goûtent un peu au pouvoir illimité du Sorcier Noir!",
["ITEM_VEZNAN_WRATH_BOTTOM_INFO"] = "Décime chaque ennemi sur le champ de bataille.",
["ITEM_VEZNAN_WRATH_DESC"] = "Vez'nan lance un puissant sort qui décime chaque ennemi sur le champ de bataille.",
["ITEM_VEZNAN_WRATH_NAME"] = "Colère de Vez'nan",
["ITEM_WINTER_AGE_BOTTOM_DESC"] = "Également utile si vous n'aimez vraiment pas l'été.",
["ITEM_WINTER_AGE_BOTTOM_INFO"] = "Gèle tous les ennemis à l'écran.",
["ITEM_WINTER_AGE_DESC"] = "Un puissant sort qui crée des vents glaciaux pour geler tous les ennemis pendant plusieurs secondes.",
["ITEM_WINTER_AGE_NAME"] = "Âge d'Hiver",
["Impossible"] = "Impossible",
["Iron"] = "Fer",
["Iron Challenge"] = "Défi de fer",
["Iron challenge"] = "Défi de fer",
["JOYSTICK_CONFIG_AXIS_DEAD_ZONE"] = "Zone insensible stick analogique",
["JOYSTICK_CONFIG_AXIS_DEAD_ZONE_XBOX"] = "Zone insensible stick analogique",
["JOYSTICK_CONFIG_FIRST_REPEAT_DELAY"] = "Délai 1re répétition",
["JOYSTICK_CONFIG_POINTER_ACCEL"] = "Accél. curseur",
["JOYSTICK_CONFIG_POINTER_MAX_ACCEL"] = "Accél. max. curseur",
["JOYSTICK_CONFIG_POINTER_SENS"] = "Sensibilité curseur",
["JOYSTICK_CONFIG_POINTER_SPEED"] = "Vitesse curseur",
["JOYSTICK_CONFIG_REPEAT_DELAY"] = "Délai répétition",
["JOYSTICK_CONFIG_SWAP_ABXY"] = "Inverser A/B et X/Y",
["JOYSTICK_HELP_INGAME_A"] = "Sélectionner",
["JOYSTICK_HELP_INGAME_AXIS_LEFT"] = "Déplacement",
["JOYSTICK_HELP_INGAME_AXIS_LEFT_BUTTON"] = "Afficher curseur",
["JOYSTICK_HELP_INGAME_B"] = "Annuler/Retour",
["JOYSTICK_HELP_INGAME_BACK"] = "Afficher les infos",
["JOYSTICK_HELP_INGAME_DPAD_DOWN"] = "Déplacer les renforts",
["JOYSTICK_HELP_INGAME_DPAD_LEFT"] = "Appeler des renforts",
["JOYSTICK_HELP_INGAME_DPAD_RIGHT"] = "Pouvoir de héros 2",
["JOYSTICK_HELP_INGAME_DPAD_UP"] = "Pouvoir de héros 1",
["JOYSTICK_HELP_INGAME_ESCAPE"] = "Annuler/Retour",
["JOYSTICK_HELP_INGAME_LB"] = "Héros principal",
["JOYSTICK_HELP_INGAME_MOVE_HEROES"] = "Déplacer les héros",
["JOYSTICK_HELP_INGAME_MOVE_REINFORCEMENTS"] = "Déplacer les renforts",
["JOYSTICK_HELP_INGAME_NX_A"] = "Sélectionner",
["JOYSTICK_HELP_INGAME_NX_AXIS_LEFT"] = "Déplacement",
["JOYSTICK_HELP_INGAME_NX_AXIS_LEFT_BUTTON"] = "Afficher curseur",
["JOYSTICK_HELP_INGAME_NX_B"] = "Annuler/Retour",
["JOYSTICK_HELP_INGAME_NX_L"] = "Héros principal",
["JOYSTICK_HELP_INGAME_NX_MINUS"] = "Afficher les infos",
["JOYSTICK_HELP_INGAME_NX_PLUS"] = "Pause/Reprendre",
["JOYSTICK_HELP_INGAME_NX_R"] = "Héros secondaire",
["JOYSTICK_HELP_INGAME_NX_X"] = "Envoyer vague",
["JOYSTICK_HELP_INGAME_NX_Y"] = "Infos vague",
["JOYSTICK_HELP_INGAME_POWERS"] = "Pouvoir",
["JOYSTICK_HELP_INGAME_RB"] = "Héros secondaire",
["JOYSTICK_HELP_INGAME_START"] = "Pause/Reprendre",
["JOYSTICK_HELP_INGAME_X"] = "Envoyer vague",
["JOYSTICK_HELP_INGAME_Y"] = "Infos vague",
["JOYSTICK_HELP_MAP_A"] = "Sélectionner",
["JOYSTICK_HELP_MAP_AXIS_LEFT"] = "Déplacement",
["JOYSTICK_HELP_MAP_B"] = "Annuler/Retour",
["JOYSTICK_HELP_MAP_BACK"] = "Afficher/Masquer options",
["JOYSTICK_HELP_MAP_LB"] = "Niveau/Page préc.",
["JOYSTICK_HELP_MAP_NX_A"] = "Sélectionner",
["JOYSTICK_HELP_MAP_NX_AXIS_LEFT"] = "Déplacement",
["JOYSTICK_HELP_MAP_NX_B"] = "Annuler/Retour",
["JOYSTICK_HELP_MAP_NX_L"] = "Niveau/Page préc.",
["JOYSTICK_HELP_MAP_NX_MINUS"] = "Afficher/Masquer options",
["JOYSTICK_HELP_MAP_NX_PLUS"] = "Afficher/Masquer options",
["JOYSTICK_HELP_MAP_NX_R"] = "Niveau/Page suiv.",
["JOYSTICK_HELP_MAP_RB"] = "Niveau/Page suiv.",
["JOYSTICK_HELP_MAP_START"] = "Afficher/Masquer options",
["JOYSTICK_HELP_SLOTS_A"] = "Sélectionner",
["JOYSTICK_HELP_SLOTS_AXIS_LEFT"] = "Déplacement",
["JOYSTICK_HELP_SLOTS_B"] = "Annuler/Retour",
["JOYSTICK_HELP_SLOTS_BACK"] = "Afficher/Masquer options",
["JOYSTICK_HELP_SLOTS_NX_A"] = "Sélectionner",
["JOYSTICK_HELP_SLOTS_NX_AXIS_LEFT"] = "Déplacement",
["JOYSTICK_HELP_SLOTS_NX_B"] = "Annuler/Retour",
["JOYSTICK_HELP_SLOTS_NX_MINUS"] = "Afficher/Masquer options",
["JOYSTICK_HELP_SLOTS_NX_PLUS"] = "Afficher/Masquer options",
["JOYSTICK_HELP_SLOTS_START"] = "Afficher/Masquer options",
["KEYBOARD_KEY_ESCAPE"] = "ECHAP.",
["KEYBOARD_KEY_PAGE_DOWN"] = "PAG.PRÉ",
["KEYBOARD_KEY_PAGE_UP"] = "PAG.SUI",
["KEYBOARD_KEY_RETURN"] = "ENTRÉE",
["KEYBOARD_KEY_SPACE"] = "ESPACE",
["LEVEL_10_HEROIC"] = "Description Héroïque 10",
["LEVEL_10_HISTORY"] = "Il s'avère que le Culte utilise les cristaux extraits pour construire un artefact d'aspect sinistre juste à la sortie du canyon. Il bourdonne d'une énergie étrange et l'air entourant l'endroit est lourd. Nous devons nous assurer qu'il soit détruit avant de progresser.",
["LEVEL_10_IRON"] = "Description de Fer 10",
["LEVEL_10_IRON_UNLOCK"] = "À définir",
["LEVEL_10_MODES_UPGRADES"] = "niveau max 5",
["LEVEL_10_TITLE"] = "10. Cour du Temple",
["LEVEL_11_HEROIC"] = "Description Héroïque 11",
["LEVEL_11_HISTORY"] = "Nous avons finalement quitté les canyons, mais le chemin est encore long. Nous sommes maintenant devant un portail géant incrusté de cristaux alors que la Voyante Mydrias termine ses rituels. Ce qui viendra de l'au-delà, nous ne le savons pas, mais nous sommes toujours prêts. Préparez-vous !",
["LEVEL_11_IRON"] = "Description de Fer 11",
["LEVEL_11_IRON_UNLOCK"] = "À définir",
["LEVEL_11_MODES_UPGRADES"] = "niveau max 5",
["LEVEL_11_TITLE"] = "11. Plateau du Canyon",
["LEVEL_12_HEROIC"] = "Description Héroïque 12",
["LEVEL_12_HISTORY"] = "Avec Denas de retour à nos côtés, nous avons traversé le portail vers l'inconnu. Ce monde étrange ressemble à un reflet tordu de Linirea, mais englouti par le fléau. Faites attention à où vous mettez les pieds, quelque chose de pire que le Culte se cache dans l'obscurité.",
["LEVEL_12_IRON"] = "Description de Fer 12",
["LEVEL_12_IRON_UNLOCK"] = "À définir",
["LEVEL_12_MODES_UPGRADES"] = "niveau max 5",
["LEVEL_12_TITLE"] = "12. Terres Agricoles Ravagées",
["LEVEL_13_HEROIC"] = "Description Héroïque 13",
["LEVEL_13_HISTORY"] = "La vue familière du Temple des Tempêtes se profile à l'horizon. Le chemin est assez clair, suivez la puanteur et la corruption alors qu'elles grandissent et nous trouverons la source de tout cela. Il nous suffit de survivre aux horreurs tordues qui semblent émerger de la terre elle-même.",
["LEVEL_13_IRON"] = "Description de Fer 13",
["LEVEL_13_IRON_UNLOCK"] = "À définir",
["LEVEL_13_MODES_UPGRADES"] = "niveau max 5",
["LEVEL_13_TITLE"] = "13. Temple Profané",
["LEVEL_14_HEROIC"] = "Description Héroïque 14",
["LEVEL_14_HISTORY"] = "Ces maudites créatures semblent surgir de nulle part ! Les troupes sont agitées, tout ce que nous touchons semble vivant et prêt à nous attaquer, comme si la terre elle-même se battait contre nous de toutes ses forces. La prophétesse Mydrias et ses sbires doivent être près.",
["LEVEL_14_IRON"] = "Description de Fer 14",
["LEVEL_14_IRON_UNLOCK"] = "À définir",
["LEVEL_14_MODES_UPGRADES"] = "niveau 5 max",
["LEVEL_14_TITLE"] = "14. Vallée de la Corruption",
["LEVEL_15_HEROIC"] = "Description héroïque 15",
["LEVEL_15_HISTORY"] = "Nous sommes sortis de la vallée victorieux et maintenant, la seule chose qui se dresse entre nous et le Surveillant, c'est Mydrias elle-même. Nous avons vu de quoi elle était capable dans les canyons, mais ici, sous le regard et la puissance de son maître, elle a l'avantage. Pas que les chances nous aient jamais arrêtés auparavant. Soyez prêt!",
["LEVEL_15_IRON"] = "Description de Fer 15",
["LEVEL_15_IRON_UNLOCK"] = "À définir",
["LEVEL_15_MODES_UPGRADES"] = "niveau 5 max",
["LEVEL_15_TITLE"] = "15. La Tour Inesthétique",
["LEVEL_16_HEROIC"] = "Description héroïque 16",
["LEVEL_16_HISTORY"] = "Mydrias n'est plus et l'Observateur est le plus grand ennemi qui reste. C'est notre dernière chance de mettre fin au Culte et à l'invasion. Ce qui arrive ensuite n'a pas d'importance si nous ne nous unissons pas une dernière fois. En avant !",
["LEVEL_16_IRON"] = "Description de Fer 16",
["LEVEL_16_IRON_UNLOCK"] = "À définir",
["LEVEL_16_MODES_UPGRADES"] = "niveau 5 max",
["LEVEL_16_TITLE"] = "16. Pic de la Faim",
["LEVEL_17_HISTORY"] = "Les alentours de la forêt féerique autrefois fantaisiste semblent maintenant inhospitaliers et lugubres. On dit que des hordes de guerriers elfes déchus et d'êtres spectraux errent désormais dans ces terres, attaquant les voyageurs et corrompant la forêt elle-même par leur présence. Général, nous devons enquêter davantage.",
["LEVEL_17_TITLE"] = "17. Ruines brumeuses",
["LEVEL_18_HISTORY"] = "Un message nous est parvenu de l'avant-poste de Feuille-profonde, où certains elfes résistent à peine à l'avancée de la horde des revenants. Nous devons nous dépêcher de les aider, eux et leur capitaine, Eridan, avant qu'il ne soit trop tard. Une fois l'avant-poste correctement sécurisé, nous pourrons avancer pour atteindre la racine de cette invasion.",
["LEVEL_18_TITLE"] = "18. Avant-poste de Feuille-profonde",
["LEVEL_19_HISTORY"] = "Un Eridan épuisé nous a indiqué le Temple des Déchus, d'où la horde est levée pour submerger le continent, commandée par un mage qui se fait appeler Navira, le Plieur d'Âmes. Il doit être arrêté à tout prix!",
["LEVEL_19_TITLE"] = "19. Temple des Déchus",
["LEVEL_1_HEROIC"] = "Description Héroïque 1",
["LEVEL_1_HISTORY"] = "Nous parcourons les forêts du sud depuis des mois sans succès, puisque le Roi Denas est introuvable. Entretemps, nous nous sommes liés d'amitié avec les Arboreans, esprits de la nature, et avons rencontré leurs voisins en guerre, les Bêtes Sauvages, qui nous attaquent à vue.\nPrenons part à ce combat pour que nous puissions continuer à chercher le Roi.",
["LEVEL_1_IRON"] = "Description de Fer 1",
["LEVEL_1_IRON_UNLOCK"] = "Archers Royaux\nConvenant de Paladins",
["LEVEL_1_MODES_UPGRADES"] = "niveau 1 max",
["LEVEL_1_TITLE"] = "1. Mer d'arbres",
["LEVEL_20_HISTORY"] = "Nous avons reçu un message urgent des Arboréens à la lisière de la forêt, appelant désespérément à l'aide. Ils sont attaqués par les Croks implacables. Ils ne pourront pas tenir beaucoup plus longtemps. Soyez prudent, Général. Les Croks ont de nombreux tours dans leurs écailles.",
["LEVEL_20_TITLE"] = "20. Hameau Arborean",
["LEVEL_21_HISTORY"] = "Après avoir assuré la sécurité de la ville, les Arboreens ont révélé qu'ils avaient senti leur ancien sceau faiblir juste avant l'attaque. Armés d'une piste sur l'invasion soudaine des Croks, nous nous sommes plongés au cœur du marais. Nous sommes tombés sur un vieux cercle de pierres arboreen, on dirait un repaire... un repaire de quelque chose d'énorme.",
["LEVEL_21_TITLE"] = "21. Les Ruines Englouties",
["LEVEL_22_HISTORY"] = "Arrivant au temple ancien, nos pires craintes se sont confirmées. Le sceau qui avait longtemps protégé notre monde d'Abominor — le Dévoreur de Royaumes — était presque dénoué, maintenu ensemble uniquement par la magie de liaison désespérée des chamans Arboreans. Général, arrêtez Abominor ou les royaumes seront consumés par sa gueule insatiable.",
["LEVEL_22_TITLE"] = "22. Creux Affamé",
["LEVEL_23_HISTORY"] = "Les éclaireurs ont signalé des glissements de terrain anormaux sur les montagnes voisines. Une enquête plus approfondie a révélé qu'ils sont causés par des nains que nous ne reconnaissons pas. Ils assemblent un automate géant sur le flanc sud de la montagne. Vous devriez aller voir cela, Général.",
["LEVEL_23_TITLE"] = "23. Portes de l'Acier Sombre",
["LEVEL_24_HISTORY"] = "Les nains ont toujours été connus comme des inventeurs, mais ce clan autoproclamé \"Acier Sombre\" pousse sa dévotion au métal beaucoup trop loin, mettant même la tribu de Bolgur dans l'ombre en utilisant leur forge pour se \"perfectionner\" à une vitesse fulgurante. Qui est derrière une telle folie ? Nous devons le découvrir !",
["LEVEL_24_TITLE"] = "24. Assemblée Frénétique",
["LEVEL_25_HISTORY"] = "Les choses sont comme nous le craignions, seuls les entrailles d'une montagne aussi grande pourraient abriter une forge capable de créer cet automate. Combien de nains sont ici ? Ils résistent à notre avance et pourtant ils continuent de forger et de souder. Et ce qui est encore plus étrange, ils se ressemblent tous ? Quelque chose cloche.",
["LEVEL_25_TITLE"] = "25. Noyau Colossal",
["LEVEL_26_HISTORY"] = "Notre route à l'intérieur et à l'extérieur de la montagne nous a menés à une chambre remplie de cuves, et elles n'étaient pas vides. Pas étonnant qu'ils soient nombreux et qu'ils possèdent également le savoir-faire et l'apparence. Ce sont tous le même nain, Grymbeard ! Il a créé des répliques de lui-même grâce à une science perverse. Général, nous devons arrêter cela !",
["LEVEL_26_TITLE"] = "26. Chambre de Réplication",
["LEVEL_27_HISTORY"] = "Nous avons réussi à perturber la majeure partie de l'opération Acier Sombre dans la montagne, mais tout sera vain si Grymbeard est toujours en liberté. Il travaille sûrement sur les finitions de la tête de l'automate. Général, emmenez les troupes aux sommets et espérons que cette fois nous aurons affaire au bon nain.",
["LEVEL_27_TITLE"] = "27. Dôme de la Domination",
["LEVEL_28_HISTORY"] = "En suivant les indices laissés par nos éclaireurs, nous avons découvert une piste menant aux vestiges de ces maudits cultistes. Il semble qu’ils aient trouvé une nouvelle déesse à vénérer, une immonde abomination tissant des toiles… Des cultistes ET des araignées ? Rien de bon ne peut sortir de cette combinaison.",
["LEVEL_28_TITLE"] = "28. Temple Profané",
["LEVEL_29_HISTORY"] = "Plus nous avançons, plus il devient évident que cette terreur grandit sous nos pieds depuis longtemps, attendant le bon moment pour attaquer. À en juger par l’épaississement des toiles autour de nous et l’obscurité oppressante qui semble respirer dans notre cou, je parierais que nous approchons du cœur même du repaire.",
["LEVEL_29_TITLE"] = "29. Chambre d’Élevage",
["LEVEL_2_HEROIC"] = "Description Héroïque 2",
["LEVEL_2_HISTORY"] = "Soyez alertes, une nouvelle nous est parvenue par un murmure ! Le Cœur de la Forêt est sous attaque ! Nous devons retourner et aider les Arborens. Certaines forces de l'Armée des Ténèbres nous rejoindront sur le champ de bataille, alors gardez les yeux ouverts. Nous pouvons être dans le même bateau pour l'instant, mais cela pourrait changer à tout moment.",
["LEVEL_2_IRON"] = "Description de Fer 2",
["LEVEL_2_IRON_UNLOCK"] = "Mage Arcane\nTricannon",
["LEVEL_2_MODES_UPGRADES"] = "niveau 2 max",
["LEVEL_2_TITLE"] = "2. La Porte du Gardien",
["LEVEL_30_HISTORY"] = "Enfin, nous sommes arrivés au repaire de leur soi-disant dieu, un temple décrépit, abandonné depuis longtemps, s’effondrant sous le poids de son passé oublié. Un trône bien approprié pour une divinité déchue. Cette fois, nous nous assurerons de ne laisser aucun survivant et exterminerons ces vermines une bonne fois pour toutes.",
["LEVEL_30_TITLE"] = "30. Le Trône Oublié",
["LEVEL_31_HISTORY"] = "Après tous ces combats et cette lutte, la paix est enfin revenue dans les royaumes. Maintenant, écouter les vagues s’écraser et jouer à des jeux de société en attendant un vieil ami est la seule chose à faire. Et pourtant, même si tout semble si calme, je me demande combien de temps cette paix durera...",
["LEVEL_31_TITLE"] = "31. Forêt des Singes Célestes",
["LEVEL_32_HISTORY"] = "Notre poursuite nous a menés au cœur du volcan, où un temple oublié rendait autrefois hommage aux flammes. Mais le Grand Dragon de Feu, autrefois gardien neutre de ces profondeurs enflammées, est maintenant en proie à une rage surnaturelle. Tous les signes indiquent que l'influence de Garçon Rouge a corrompu sa volonté. Affronter un dragon n’est pas chose facile, mais nous n’avons pas le choix. Au combat !",
["LEVEL_32_TITLE"] = "32. Grotte du Dragon de Feu",
["LEVEL_33_HISTORY"] = "Après un affrontement épuisant contre le Garçon Rouge, nous poursuivons notre route vers l’Île Tempête. Dès que nous avons posé le pied ici, des nuages chargés d’éclairs et des rafales violentes se sont mis à hurler en des motifs étranges et tourbillonnants. Pourtant, nous n’avons pas le choix : l’île abrite la seule entrée vers le palais de la Princesse.Préparez-vous... la tempête arrive.",
["LEVEL_33_TITLE"] = "33. Île de la Tempête",
["LEVEL_34_HISTORY"] = "Nous pouvons remercier la Princesse et son éventail de fer pour les épreuves que nous avons endurées. Après avoir franchi le pont et bravé les tempêtes les plus féroces, nous nous tenons maintenant au cœur de tout cela. Cet endroit reste intact—trompeusement calme et magnifique. Nous ne pouvons pas baisser notre garde. Même la royauté démoniaque ne nous arrêtera pas.",
["LEVEL_34_TITLE"] = "34. L'Œil de la Tempête",
["LEVEL_35_HISTORY"] = "Nous y sommes. Le Roi Démon Taureau se dresse fièrement dans sa forteresse opulente et imprenable. Nous avons rassemblé le reste de nos troupes pour attaquer de front ses murs, une tâche qui demandera plus de ruse que de force brute. Nous devons frapper avant qu’il ne libère pleinement le pouvoir des orbes.\nPar tout ce qui vous est cher sur cette terre bénie... Tenez bon, Liniréens !",
["LEVEL_35_TITLE"] = "35. Bastion du Roi Démon",
["LEVEL_3_HEROIC"] = "Description Héroïque 3",
["LEVEL_3_HISTORY"] = "Nous sommes arrivés juste à temps au Cœur, mais les Bêtes Sauvages passent déjà. Soyez vifs et fortifiez vos positions ! Protégez le Cœur à tout prix, sinon la forêt et les Arborens périront sûrement.",
["LEVEL_3_IRON"] = "Description du Fer 3",
["LEVEL_3_IRON_UNLOCK"] = "Archers Royaux\nCovenant de Paladins",
["LEVEL_3_MODES_UPGRADES"] = "niveau 3 max",
["LEVEL_3_TITLE"] = "3. Le Cœur de la Forêt",
["LEVEL_4_HEROIC"] = "Description Héroïque 4",
["LEVEL_4_HISTORY"] = "Maintenant que le Cœur de la Forêt est en sécurité, nous devons nous regrouper et presser l'avantage. Il est temps de porter le combat en territoire des Bêtes Sauvages. Emmenez les troupes dans la cime des arbres de la forêt et cherchez leur campement d'en haut.",
["LEVEL_4_IRON"] = "Description du Fer 4",
["LEVEL_4_IRON_UNLOCK"] = "Tricannon\nÉmissaire Arboréen",
["LEVEL_4_MODES_UPGRADES"] = "niveau 4 max",
["LEVEL_4_TITLE"] = "4. Cimes d'Arbres Émeraude",
["LEVEL_5_HEROIC"] = "Description Héroïque 5",
["LEVEL_5_HISTORY"] = "Grâce à vos efforts pour prendre le terrain en hauteur, nous avons localisé le campement des Bêtes Sauvages dans des ruines anciennes au-delà des limites de la forêt. Menez les forces vers leur territoire et méfiez-vous de leurs tactiques. Nous avons peut-être remporté une autre bataille, mais ce n'est pas encore fini.",
["LEVEL_5_IRON"] = "Description du Fer 5",
["LEVEL_5_IRON_UNLOCK"] = "Mage Arcanique\nCovenant Paladin",
["LEVEL_5_MODES_UPGRADES"] = "niveau 5 max",
["LEVEL_5_TITLE"] = "5. Périphéries Ravagées",
["LEVEL_6_HEROIC"] = "Description Héroïque 6",
["LEVEL_6_HISTORY"] = "Nous avons peut-être l'avantage sur les Bêtes Sauvages, mais nous devons encore affronter leur chef, Goregrind. Le soi-disant Roi des Bêtes Sauvages est un ennemi puissant, alors ne vous laissez pas tromper par ses singeries ou vous finirez sous ses défenses.",
["LEVEL_6_IRON"] = "Description du Fer 6",
["LEVEL_6_IRON_UNLOCK"] = "Archers Royaux\nFosse Démoniaque",
["LEVEL_6_MODES_UPGRADES"] = "niveau 5 max",
["LEVEL_6_TITLE"] = "6. La Tanière des Bêtes Sauvages",
["LEVEL_7_HEROIC"] = "Description Héroïque 7",
["LEVEL_7_HISTORY"] = "Suivant la piste des cultistes qui ont aidé les Bêtes Sauvages à raser une partie de la forêt, nous arrivons dans un lieu désolé où nous soupçonnons que le Culte met en œuvre ses étranges plans. Nous devons être prudents, car nous ne savons pas exactement à quoi nous faisons face... mais ils semblent avoir quelques tours dans leurs manches.",
["LEVEL_7_IRON"] = "Description du Fer 7",
["LEVEL_7_IRON_UNLOCK"] = "Pas d'archers royaux",
["LEVEL_7_MODES_UPGRADES"] = "niveau 5 max",
["LEVEL_7_TITLE"] = "7. Vallée Morose ",
["LEVEL_8_HEROIC"] = "Description Héroïque 8",
["LEVEL_8_HISTORY"] = "En pénétrant sur le territoire des cultistes, nous sommes arrivés dans un ensemble de vastes cavernes remplies de cristaux qui résonnent avec une magie étrange. Le Culte extrait ces cristaux, sûrement pour les utiliser comme source de pouvoir. Dans quel but, nous l'ignorons, mais perturber leurs activités est un bon moyen de semer le chaos dans leurs rangs. ",
["LEVEL_8_IRON"] = "Description du Fer 8",
["LEVEL_8_IRON_UNLOCK"] = "Tricannon\nConvenant des Paladins",
["LEVEL_8_MODES_UPGRADES"] = "niveau max 5",
["LEVEL_8_TITLE"] = "8. Mines Carmin",
["LEVEL_9_HEROIC"] = "Description Héroïque 9",
["LEVEL_9_HISTORY"] = "Les torsions et les détours de ces tunnels sont exaspérants, mais nous savons que nous sommes sur la bonne voie car l'activité des cultistes ne cesse de s'intensifier. Au fur et à mesure que nous progressons, nous faisons face à de nouveaux types d'horreurs, ce qui soulève la question de la profondeur de la corruption au sein des rangs du Culte.",
["LEVEL_9_IRON"] = "Description de Fer 9",
["LEVEL_9_IRON_UNLOCK"] = "Fosse Démoniaque\nMagicien Arcanique",
["LEVEL_9_MODES_UPGRADES"] = "niveau max 5",
["LEVEL_9_TITLE"] = "9. Passage Maudit",
["LEVEL_DEFEAT_TITLE"] = "DÉFAITE!",
["LEVEL_MODE_CAMPAIGN"] = "Campagne",
["LEVEL_MODE_HEROIC"] = "Défi héroïque",
["LEVEL_MODE_HEROIC_DESCRIPTION"] = "Teste tes talents de tacticien contre une force d'élite dans ce défi taillé sur mesure pour les plus héroïques défenseurs!",
["LEVEL_MODE_IRON"] = "Défi de fer",
["LEVEL_MODE_IRON_DESCRIPTION"] = "Test pour le défenseur ultime, le Défi de fer poussera tes talents de tacticien dans leurs derniers retranchements.",
["LEVEL_MODE_LOCKED_DESCRIPTION"] = "Termine cette Étape avec 3 étoiles pour débloquer ce mode.",
["LEVEL_SELECT_AVAILABLE_TOWERS"] = "Tours disponibles",
["LEVEL_SELECT_CHALLENGE_ONE_ELITE_WAVE"] = "1 vague d'élite",
["LEVEL_SELECT_CHALLENGE_ONE_LIFE"] = "1 PV au total",
["LEVEL_SELECT_CHALLENGE_RULES"] = "Règles du défi",
["LEVEL_SELECT_CHALLENGE_SIX_ELITE_WAVE"] = "6 vagues d'élite",
["LEVEL_SELECT_DIFFICULTY_CASUAL"] = "Décontracté",
["LEVEL_SELECT_DIFFICULTY_IMPOSSIBLE"] = "Impossible",
["LEVEL_SELECT_DIFFICULTY_NORMAL"] = "Normal",
["LEVEL_SELECT_DIFFICULTY_VETERAN"] = "Vétéran",
["LEVEL_SELECT_GET_DLC"] = "PRENEZ-LE",
["LEVEL_SELECT_MODE_LOCKED1"] = "Mode bloqué",
["LEVEL_SELECT_MODE_LOCKED2"] = "Déverrouillez ce mode en terminant cette étape.",
["LEVEL_SELECT_TO_BATTLE"] = "À LA\nBATAILLE",
["LV22_BOSS_BEFORE_FIGHT_EAT_01"] = "Délicieux ! Ha Ha Ha",
["LV22_BOSS_BEFORE_FIGHT_EAT_02"] = "Je déteste les plantes",
["LV22_BOSS_BEFORE_FIGHT_EAT_03"] = "Tu es ce que tu manges",
["LV22_BOSS_BEFORE_FIGHT_EAT_04"] = "Cette bouchée était rafraîchissante",
["LV22_BOSS_BEFORE_FIGHT_EAT_05"] = "Déjà fatigué ?",
["LV22_BOSS_BEFORE_FIGHT_EAT_06"] = "Je n'aurai plus jamais faim",
["LV22_BOSS_BEFORE_FIGHT_EAT_07"] = "C'était une grande tour, hahaha",
["LV22_BOSS_BEFORE_FIGHT_EAT_08"] = "Ça a le goût de la liberté",
["LV22_BOSS_INTRO_01"] = "J'apporte des en-cas pour mon premier repas.",
["LV22_BOSS_INTRO_02"] = "Ils ont l'air... croustillants",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_01"] = "Vous ne goûterez que des creepers.",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_02"] = "Les verts sont des amis, pas de la nourriture",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_03"] = "Et tu ne mangeras plus rien d'autre !",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_04"] = "Retourne dans ta prison, monstre !",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_05"] = "Tu ne mangeras pas !!",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_06"] = "Je protégerai le Vert !",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_07"] = "Tu ne riras pas à la fin",
["LV22_MAGE_BEFORE_FIGHT_RESPONSE_08"] = "Il devient plus fort ! À l'aide !!",
["LV22_MAGE_INTRO_01"] = "Ferme ta gueule !",
["LV22_MAGE_INTRO_02"] = "Dépêchez-vous ! Je ne peux pas le retenir plus longtemps !",
["Level"] = "Niveau",
["Localization Manager"] = "Gestionnaire de localisation",
["Long"] = "Longue",
["Low"] = "Faible",
["MAGES’ GUILD"] = "GUILDE DES MAGES",
["MAGIC RESISTANT ENEMIES!"] = "ENNEMIS RÉSISTANTS À LA MAGIE!",
["MAP_BALLON_BUY_UPGRADES_DESCRIPTION"] = "Utilise les étoiles gagnées pour améliorer tes tours et tes pouvoirs!",
["MAP_BALLON_BUY_UPGRADES_TITLE"] = "ACHETER AMÉLIORATIONS!",
["MAP_BALLON_HERO_LEVELUP_DESCRIPTION"] = "Utilise les points de héros gagnés pour entraîner ton héros!",
["MAP_BALLON_HERO_LEVELUP_TITLE"] = "GAIN DE NIVEAU DE HÉROS!",
["MAP_BALLON_HERO_UNLOCKED"] = "HÉROS DÉBLOQUÉ!",
["MAP_BALLON_START_HERE"] = "COMMENCE ICI!",
["MAP_BUTTON_ACHIEVEMENTS"] = "RÉALISATIONS",
["MAP_BUTTON_HERO_ROOM"] = "HÉROS",
["MAP_BUTTON_ITEMS"] = "ARTICLES",
["MAP_BUTTON_SHOP"] = "BOUTIQUE",
["MAP_BUTTON_TOWER_ROOM"] = "TOURS",
["MAP_BUTTON_UPGRADES"] = "AMÉLIORATIONS",
["MAP_HEROROOM_HELP1"] = "Sélectionne et entraîne tes capacités!",
["MAP_HEROROOM_HELP2"] = "Touche pour sélectionner",
["MAP_HEROROOM_HELP3"] = "Améliore la puissance du héros!",
["MAP_HERO_ROOM_GET_IT_NOW"] = "OBTIENS-LE MAINTENANT!",
["MAP_HERO_ROOM_SELECT"] = "ÉQUIPER",
["MAP_HERO_ROOM_SELECTED"] = "ÉQUIPÉ",
["MAP_HERO_ROOM_TRAIN"] = "ENTRAÎNER",
["MAP_HERO_ROOM_UNLOCK"] = "SE DÉBLOQUE À L'ÉTAPE %s",
["MAP_HERO_ROOM_UNLOCK_10"] = "SE DÉBLOQUE AU STAGE 10",
["MAP_HERO_ROOM_UNLOCK_14"] = "SE DÉBLOQUE AU STAGE 14",
["MAP_HERO_ROOM_UNLOCK_15"] = "SE DÉBLOQUE AU STAGE 15",
["MAP_HERO_ROOM_UNLOCK_4"] = "SE DÉBLOQUE AU STAGE 4",
["MAP_HERO_ROOM_UNLOCK_7"] = "SE DÉBLOQUE AU STAGE 7",
["MAP_HERO_ROOM_UNLOCK_9"] = "SE DÉBLOQUE AU STAGE 9",
["MAP_HERO_ROOM_UNLOCK_AFTER_CAMPAIGN"] = "Se déverrouille à la fin du jeu",
["MAP_INAPPS_BUBBLE_INFO_1"] = "Récupère des gemmes en jouant au jeu.",
["MAP_INAPPS_BUBBLE_INFO_2"] = "Utilise des gemmes pour acheter des objets spéciaux!",
["MAP_INAPPS_BUBBLE_MORE_GEMS"] = "Il te faut plus de gemmes!",
["MAP_INAPPS_BUBBLE_SUCCESSFUL"] = "Achat\nréussi!",
["MAP_INAPP_GEMS_GEM_SHOP_TITLE"] = "BOUTIQUE DE GEMMES",
["MAP_INAPP_GEM_PACK_1"] = "POIGNÉE DE GEMMES",
["MAP_INAPP_GEM_PACK_2"] = "BOURSE DE GEMMES",
["MAP_INAPP_GEM_PACK_3"] = "TONNEAU DE GEMMES",
["MAP_INAPP_GEM_PACK_4"] = "COFFRE DE GEMMES",
["MAP_INAPP_GEM_PACK_5"] = "WAGONNET DE GEMMES",
["MAP_INAPP_GEM_PACK_6"] = "MONTAGNE DE GEMMES",
["MAP_INAPP_GEM_PACK_BAG"] = "Sac de gemmes",
["MAP_INAPP_GEM_PACK_BARREL"] = "Tonneau de gemmes",
["MAP_INAPP_GEM_PACK_CHEST"] = "Coffre de gemmes",
["MAP_INAPP_GEM_PACK_FREE"] = "Gemmes gratuites",
["MAP_INAPP_GEM_PACK_HANDFUL"] = "Poignée de gemmes",
["MAP_INAPP_GEM_PACK_VAULT"] = "Coffre-fort de gemmes",
["MAP_INAPP_GEM_PACK_WAGON"] = "Wagon de gemmes",
["MAP_INAPP_MORE_GEMS"] = "PLUS DE GEMMES",
["MAP_INAPP_TEXT_1"] = "Poignée de gemmes",
["MAP_INAPP_TEXT_2"] = "Sac de gemmes",
["MAP_INAPP_TEXT_3"] = "Coffre de gemmes",
["MAP_INAPP_TEXT_4"] = "Gemmes gratuites",
["MAP_INAPP_TEXT_GEMS"] = "Gemmes",
["MAP_NEW_GAMEMODE_UNLOCKED_DESCRIPTION"] = "Affronte indéfiniment des ennemis et tente de décrocher le record!",
["MAP_NEW_GAMEMODE_UNLOCKED_TITLE"] = "NOUVEAU DÉFI!",
["MAP_NEW_HERO_ALERT"] = "NOUVEAU\nHÉROS!",
["MAP_NEW_TOWER_ALERT"] = "NOUVELLE\nTOUR!",
["MAP_TOWER_ROOM_SELECT"] = "ÉQUIPER",
["MAP_TOWER_ROOM_SELECTED"] = "ÉQUIPÉ",
["MENU_HUD_WAVES"] = "%i/%i",
["MINUTES_ABBREVIATION"] = "m",
["MORE_GAMES"] = "Plus de jeux",
["MUSIC"] = "MUSIQUE",
["Magic resistant enemies take less damage from mages."] = "Les ennemis dotés de résistance à la magie encaissent moins de dégâts des mages.",
["Medium"] = "Interm.",
["Music"] = "Musique",
["NEW POWER!"] = "NOUVEAU POUVOIR!",
["NEW SPECIAL POWER!"] = "NOUVEAU POUVOIR SPÉCIAL!",
["NEW TOWER UNLOCKED"] = "NOUVELLE TOUR DÉBLOQUÉE",
["NEW TOWER UPGRADES"] = "NOUVELLES AMÉLIORATIONS DE TOUR",
["NEW TOWERS UNLOCKED"] = "NOUVELLES TOURS DÉBLOQUÉES",
["NEWS"] = "ACTUALITÉS",
["NEW_ENEMY_ALERT_ICON"] = "NOUVEL",
["NO HEROES"] = "AUCUN HÉROS",
["NOTIFICATION_NEW_ENEMY_TITLE"] = "NOUVEL ENNEMI",
["NOTIFICATION_NEW_SPECIAL_TITLE"] = "NOUVEAU POUVOIR SPÉCIAL!",
["NOTIFICATION_NEW_TOWERS_SUB_DESCRIPTION"] = "Vous pouvez désormais améliorer vos tours jusqu'au niveau %d.",
["NOTIFICATION_NEW_TOWERS_SUB_TITLE"] = "TOURS DE NIVEAU %d DISPONIBLES",
["NOTIFICATION_NEW_TOWERS_TITLE"] = "NOUVELLES AMÉLIORATIONS DE TOUR",
["NOTIFICATION_NEW_TOWER_TITLE"] = "NOUVELLE TOUR DÉBLOQUÉE",
["NOTIFICATION_armored_enemies_desc_body_1"] = "Certains ennemis portent des armures de différentes résistances, ce qui les protège contre les attaques non magiques.",
["NOTIFICATION_armored_enemies_desc_body_2"] = "Résiste aux dégâts de",
["NOTIFICATION_armored_enemies_desc_body_3"] = "Les ennemis blindés prennent moins de dégâts des tours de Tireur d'élite, de Caserne et d'Artillerie.",
["NOTIFICATION_armored_enemies_desc_title"] = "Ennemis Blindés!",
["NOTIFICATION_armored_enemies_enemy_name"] = "Bagarreur à Défenses",
["NOTIFICATION_bottom_info_desc_body"] = "Vous pouvez consulter les informations sur l'ennemi à tout moment en cliquant sur une unité et son portrait.",
["NOTIFICATION_bottom_info_desc_title"] = "INFORMATIONS SUR L'ENNEMI",
["NOTIFICATION_bottom_info_tap_portrait_desc"] = "Cliquez ici pour rouvrir.",
["NOTIFICATION_button_ok"] = "Ok",
["NOTIFICATION_glare_desc_body"] = "L'Intendant scrute le champ de bataille, renforçant les ennemis proches avec son Regard corrompu.",
["NOTIFICATION_glare_desc_bullets"] = " - Soigne les ennemis se tenant dans la zone\n- Déclenche les capacités uniques des ennemis",
["NOTIFICATION_glare_desc_title"] = "Regard de l'Intendant",
["NOTIFICATION_hero_desc"] = "Affiche le niveau, la santé et l'expérience.",
["NOTIFICATION_hero_desc_baloon_1"] = "Sélectionne en cliquant sur le portrait ou l'unité du héros. Raccourci : barre d'espace",
["NOTIFICATION_hero_desc_baloon_2"] = "CLIQUE sur le chemin pour déplacer ton héros.",
["NOTIFICATION_hero_desc_body_1"] = "Les héros sont des unités d'élite capables d'affronter des ennemis puissants et de soutenir vos forces.",
["NOTIFICATION_hero_desc_body_2"] = "Les héros gagnent de l'expérience à chaque fois qu'ils attaquent un ennemi ou utilisent une capacité.",
["NOTIFICATION_hero_desc_title"] = "Héros à votre commandement!",
["NOTIFICATION_magic_resistant_enemies_desc_body_1"] = "Certains ennemis possèdent différents niveaux de résistance magique, ce qui les protège contre les attaques magiques.",
["NOTIFICATION_magic_resistant_enemies_desc_body_2"] = "Résiste aux dégâts de",
["NOTIFICATION_magic_resistant_enemies_desc_body_3"] = "Les ennemis résistants à la magie subissent moins de dégâts des tours de Mages.",
["NOTIFICATION_magic_resistant_enemies_desc_title"] = "Ennemis Résistants à la Magie!",
["NOTIFICATION_magic_resistant_enemies_enemy_name"] = "Tortue Chaman",
["NOTIFICATION_rally_point_desc_body_1"] = "Vous pouvez ajuster le point de rassemblement de vos casernes pour faire défendre une zone différente par les unités.",
["NOTIFICATION_rally_point_desc_body_2"] = "Sélectionne la commande du point de ralliement",
["NOTIFICATION_rally_point_desc_body_3"] = "Sélectionne là où tu veux déplacer tes soldats",
["NOTIFICATION_rally_point_desc_subtitle"] = "Portée du Rassemblement",
["NOTIFICATION_rally_point_desc_title"] = "Commandez vos troupes !",
["NOTIFICATION_special_desc_body"] = "Vous pouvez invoquer des troupes supplémentaires pour vous aider sur le champ de bataille.",
["NOTIFICATION_special_desc_bullets"] = "Les renforts sont excellents pour retarder les ennemis.",
["NOTIFICATION_special_desc_title"] = "Appeler des renforts",
["NOTIFICATION_title_enemy"] = "Informations sur l'Ennemi",
["NOTIFICATION_title_glare"] = "NOUVEAU CONSEIL!",
["NOTIFICATION_title_hint"] = "HÉROS DÉBLOQUÉ",
["NOTIFICATION_title_new_tip"] = "NOUVEAU CONSEIL",
["NOTIFICATION_title_special"] = "SPÉCIAL DÉBLOQUÉ",
["Next!"] = "Suivant!",
["No"] = "Non",
["None"] = "Aucune",
["Nope"] = "Non.",
["Normal"] = "Normal",
["OFFER_GET_IT_NOW"] = "À OBTENIR MAINTENANT",
["OFFER_GET_THEM_NOW"] = "OBTIENS-LES MAINTENANT",
["OFFER_OFF"] = "RÉDUIT",
["OFFER_REGULAR"] = "PRIX NORMAL",
["OK!"] = "OK!",
["ONE_TIME_OFFER"] = "OFFRE UNIQUE!",
["OPTIONS"] = "OPTIONS",
["OPTIONS_PAGE_CONTROLS"] = "CONTRÔLES",
["OPTIONS_PAGE_HELP"] = "AIDE",
["OPTIONS_PAGE_SHORTCUTS"] = "AIDE DU CLAVIER",
["OPTIONS_PAGE_VIDEO"] = "VIDÉO",
["Objective"] = "Objectif",
["Options"] = "Options",
["Over 50 stars are recommended to face this stage."] = "Il est recommandé d'avoir plus de 50 étoiles pour affronter cette Étape.",
["POPUP_CLEAR_PROGRESS_CONFIRM"] = "ÊTES-VOUS SÛR DE VOULOIR EFFACER VOTRE PROGRESSION?",
["POPUP_LABEL_MAIN_MENU"] = "Menu principal",
["POPUP_SETTINGS_LANGUAGE"] = "Langue",
["POPUP_SETTINGS_MUSIC"] = "MUSIQUE",
["POPUP_SETTINGS_SFX"] = "Effets audio",
["POPUP_label_error_msg"] = "Oups ! Quelque chose cloche.",
["POPUP_label_error_msg2"] = "Oups ! Quelque chose cloche.",
["POPUP_label_purchasing"] = "REQUÊTE EN COURS DE TRAITEMENT",
["POPUP_label_title_options"] = "Options",
["POPUP_label_version"] = "Version 0.0.8b",
["POWER_SUMMON_DESCRIPTION"] = "Tu peux appeler des troupes à la rescousse sur le champ de bataille.",
["POWER_SUMMON_LARGE_DESCRIPTION"] = "Tu peux appeler des troupes à la rescousse sur le champ de bataille.\n\nLes renforts sont gratuits, et tu peux en appeler toutes les 15 secondes.",
["POWER_SUMMON_NAME"] = "Appeler des renforts",
["PRICE_FREE"] = "Gratuit",
["PRIVACY_POLICY_ASK_AGE"] = "Quand es-tu né?",
["PRIVACY_POLICY_BUTTON_LINK"] = "Politique de confidentialité",
["PRIVACY_POLICY_CONSENT_SHORT"] = "Avant de jouer à notre jeu, confirmez que vous (ou votre tuteur légal si vous êtes un enfant ou un adolescent) avez lu et acceptez notre politique de confidentialité.",
["PRIVACY_POLICY_LINK"] = "Politique de confidentialité",
["PRIVACY_POLICY_WELCOME"] = "Bienvenue!",
["PROCESSING YOUR REQUEST"] = "REQUÊTE EN COURS DE TRAITEMENT",
["Produced by %s"] = "Produit par %s",
["QUIT"] = "Quitter",
["Quit"] = "Quitter",
["RESTORE_PURCHASES"] = "Rest. achats",
["Reset"] = "Réinitialiser",
["Restart"] = "Redémarrer",
["Resume"] = "Reprendre",
["SECONDS_ABBREVIATION"] = "s",
["SETTINGS_DISPLAY"] = "Écran",
["SETTINGS_FRAMES_PER_SECOND"] = "FPS",
["SETTINGS_FULLSCREEN"] = "Plein écran",
["SETTINGS_FULLSCREEN_BORDERLESS"] = "Sans bordure",
["SETTINGS_IMAGE_QUALITY"] = "Qualité d'image",
["SETTINGS_LANGUAGE"] = "Langue",
["SETTINGS_LARGE_MOUSE_POINTER"] = "Grand pointeur de la souris",
["SETTINGS_LOW_IMAGE_QUALITY_LINK"] = "Mauvaise qualité d'image? Cliquez ici.",
["SETTINGS_RETINA_DISPLAY"] = "Écran Retina (Mac)",
["SETTINGS_SCREEN_RESOLUTION"] = "Résolution d'écran",
["SETTINGS_SUPPORT"] = "Soutien",
["SETTINGS_VSYNC"] = "Vsync",
["SFX"] = "Effets audio",
["SHOP_DESKTOP_GET_DLC_BUTTON"] = "PRENEZ-LE",
["SHOP_DESKTOP_TITLE"] = "BOUTIQUE",
["SHOP_ROOM_BEST_VALUE_TITLE"] = "MEILLEUR\nRAPPORT QUALITÉ-PRIX",
["SHOP_ROOM_DLC_1_DESCRIPTION"] = "PARTEZ POUR CETTE NOUVELLE AVENTURE ÉPIQUE",
["SHOP_ROOM_DLC_1_TITLE"] = "CAMPAGNE COLOSSALE DES NAINES",
["SHOP_ROOM_DLC_1_TOOLTIP_DESCRIPTION"] = "5 nouvelles étapes\nNouvelle tour\nNouveau héros\nPlus de 10 nouveaux ennemis\n2 combats de mini-boss\nUn combat épique contre un boss\nEt bien plus...",
["SHOP_ROOM_DLC_1_TOOLTIP_TITLE"] = "CAMPAGNE COLOSSALE DES NAINES",
["SHOP_ROOM_DLC_2_DESCRIPTION"] = "ENTREZ DANS CETTE NOUVELLE AVENTURE ÉPIQUE",
["SHOP_ROOM_DLC_2_TITLE"] = "LA CAMPAGNE DU VOYAGE DE WUKONG",
["SHOP_ROOM_MOST_POPULAR_TITLE"] = "PLUS\nPOPULAIRE",
["SLOT_CLOUD_DOWNLOADING"] = "Téléchargement...",
["SLOT_CLOUD_DOWNLOAD_FAILED"] = "Impossible de télécharger la partie sauvegardée depuis l'iCloud, merci de réessayer plus tard.",
["SLOT_CLOUD_DOWNLOAD_SUCCESSFUL"] = "Téléchargement réussi.",
["SLOT_CLOUD_UPLOADING"] = "Téléchargement...",
["SLOT_CLOUD_UPLOAD_FAILED"] = "Impossible d'envoyer la partie sauvegardée sur l'iCloud, merci de réessayer plus tard.",
["SLOT_CLOUD_UPLOAD_ICLOUD_NOT_CONFIGURED"] = "iCloud non configuré sur cet appareil.",
["SLOT_CLOUD_UPLOAD_SUCCESSFUL"] = "Téléchargement réussi.",
["SLOT_DELETE_SLOT"] = "Supprimer l'emplacement?",
["SLOT_NAME"] = "Emplac.",
["SLOT_NEW_GAME"] = "NOUVELLE PARTIE",
["SOLDIER_ARBOREAN_BARRACK_NAME"] = "Soldat Arboreen",
["SOLDIER_ARBOREAN_SENTINELS_1_NAME"] = "Baluu",
["SOLDIER_ARBOREAN_SENTINELS_2_NAME"] = "Vylla",
["SOLDIER_ARBOREAN_SENTINELS_3_NAME"] = "Ykkon",
["SOLDIER_ARBOREAN_SENTINELS_4_NAME"] = "Haavi",
["SOLDIER_ARBOREAN_SENTINELS_5_NAME"] = "Plook",
["SOLDIER_ARBOREAN_SENTINELS_6_NAME"] = "Guldd",
["SOLDIER_ARBOREAN_SENTINELS_7_NAME"] = "Teena",
["SOLDIER_ARBOREAN_SENTINELS_8_NAME"] = "Uuzky",
["SOLDIER_ARBOREAN_SENTINELS_9_NAME"] = "Deluu",
["SOLDIER_DRAGON_BONE_ULTIMATE_DOG_NAME"] = "Dragon d'Os",
["SOLDIER_EARTH_HOLDER_NAME"] = "Guerrier de Pierre",
["SOLDIER_GHOST_TOWER_NAME"] = "spectre",
["SOLDIER_HERO_BUILDER_WORKER_1_NAME"] = "Hemmar",
["SOLDIER_HERO_BUILDER_WORKER_2_NAME"] = "O'Tool",
["SOLDIER_HERO_BUILDER_WORKER_3_NAME"] = "Crews",
["SOLDIER_HERO_BUILDER_WORKER_4_NAME"] = "Birck",
["SOLDIER_HERO_BUILDER_WORKER_5_NAME"] = "Lauck",
["SOLDIER_HERO_BUILDER_WORKER_6_NAME"] = "O'Nail",
["SOLDIER_HERO_BUILDER_WORKER_7_NAME"] = "Hovels",
["SOLDIER_HERO_BUILDER_WORKER_8_NAME"] = "Woody",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL1_NAME"] = "Gardien Arboreal",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL2_NAME"] = "Gardien Arboreal",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_LVL3_NAME"] = "Gardien Arboreal",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL1_NAME"] = "Parangon Arboreal",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL2_NAME"] = "Parangon Arboreal",
["SOLDIER_HERO_DRAGON_ARB_SPAWN_PARAGON_LVL3_NAME"] = "Parangon Arboreal",
["SOLDIER_HERO_SPIDER_ULTIMATE_NAME"] = "Arachnide",
["SOLDIER_HERO_WITCH_CAT_1_NAME"] = "Conan",
["SOLDIER_HERO_WITCH_CAT_2_NAME"] = "Alfajor",
["SOLDIER_HERO_WITCH_CAT_3_NAME"] = "Babieca",
["SOLDIER_HERO_WITCH_CAT_4_NAME"] = "Peluche",
["SOLDIER_HERO_WITCH_CAT_5_NAME"] = "Pipa",
["SOLDIER_HERO_WITCH_CAT_6_NAME"] = "Watson",
["SOLDIER_HERO_WITCH_CAT_7_NAME"] = "Chimi",
["SOLDIER_HERO_WITCH_CAT_8_NAME"] = "Pantufla",
["SOLDIER_HERO_WITCH_DECOY_NAME"] = "Ragdoll",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_1_NAME"] = "San Wikung",
["SOLDIER_HERO_WUKONG_HAIR_CLONES_2_NAME"] = "Son Wokeng",
["SOLDIER_ITEM_SUMMON_BLACKBURN_NAME"] = "Seigneur Blackburn",
["SOLDIER_PALADINS_10_NAME"] = "Sir Joacim",
["SOLDIER_PALADINS_11_NAME"] = "Sir Andre",
["SOLDIER_PALADINS_12_NAME"] = "Sir Sammet",
["SOLDIER_PALADINS_13_NAME"] = "Sir Udo",
["SOLDIER_PALADINS_14_NAME"] = "Sir Eric",
["SOLDIER_PALADINS_15_NAME"] = "Sir Bruce",
["SOLDIER_PALADINS_16_NAME"] = "Sir Rob",
["SOLDIER_PALADINS_17_NAME"] = "Sir Biff",
["SOLDIER_PALADINS_18_NAME"] = "Sir Bowes",
["SOLDIER_PALADINS_1_NAME"] = "Sir Kai",
["SOLDIER_PALADINS_2_NAME"] = "Sir Hansi",
["SOLDIER_PALADINS_3_NAME"] = "Sir Luca",
["SOLDIER_PALADINS_4_NAME"] = "Sir Timo",
["SOLDIER_PALADINS_5_NAME"] = "Sir Ralf",
["SOLDIER_PALADINS_6_NAME"] = "Sir Tobias",
["SOLDIER_PALADINS_7_NAME"] = "Sir Deris",
["SOLDIER_PALADINS_8_NAME"] = "Sir Kiske",
["SOLDIER_PALADINS_9_NAME"] = "Sir Pesch",
["SOLDIER_PRIESTS_BARRACK_1_NAME"] = "Willy",
["SOLDIER_PRIESTS_BARRACK_2_NAME"] = "Henry",
["SOLDIER_PRIESTS_BARRACK_3_NAME"] = "Geoffrey",
["SOLDIER_PRIESTS_BARRACK_4_NAME"] = "Nicholas",
["SOLDIER_PRIESTS_BARRACK_5_NAME"] = "Ed",
["SOLDIER_PRIESTS_BARRACK_6_NAME"] = "Hob",
["SOLDIER_PRIESTS_BARRACK_7_NAME"] = "Odo",
["SOLDIER_PRIESTS_BARRACK_8_NAME"] = "Cedric",
["SOLDIER_PRIESTS_BARRACK_9_NAME"] = "Hal",
["SOLDIER_RANDOM_10_NAME"] = "Alvus",
["SOLDIER_RANDOM_11_NAME"] = "Borin",
["SOLDIER_RANDOM_12_NAME"] = "Hadrien",
["SOLDIER_RANDOM_13_NAME"] = "Thomas",
["SOLDIER_RANDOM_14_NAME"] = "Henry",
["SOLDIER_RANDOM_15_NAME"] = "Bryce",
["SOLDIER_RANDOM_16_NAME"] = "Rulf",
["SOLDIER_RANDOM_17_NAME"] = "Allister",
["SOLDIER_RANDOM_18_NAME"] = "Altair",
["SOLDIER_RANDOM_19_NAME"] = "Simon",
["SOLDIER_RANDOM_1_NAME"] = "Douglas",
["SOLDIER_RANDOM_20_NAME"] = "Egbert",
["SOLDIER_RANDOM_21_NAME"] = "Eldon",
["SOLDIER_RANDOM_22_NAME"] = "Garrett",
["SOLDIER_RANDOM_23_NAME"] = "Godwin",
["SOLDIER_RANDOM_24_NAME"] = "Gordon",
["SOLDIER_RANDOM_25_NAME"] = "Jerald",
["SOLDIER_RANDOM_26_NAME"] = "Kelvin",
["SOLDIER_RANDOM_27_NAME"] = "Lando",
["SOLDIER_RANDOM_28_NAME"] = "Maddox",
["SOLDIER_RANDOM_29_NAME"] = "Peyton",
["SOLDIER_RANDOM_2_NAME"] = "Dan McKill",
["SOLDIER_RANDOM_30_NAME"] = "Ramsey",
["SOLDIER_RANDOM_31_NAME"] = "Raymond",
["SOLDIER_RANDOM_32_NAME"] = "Robert",
["SOLDIER_RANDOM_33_NAME"] = "Sawyer",
["SOLDIER_RANDOM_34_NAME"] = "Silas",
["SOLDIER_RANDOM_35_NAME"] = "Stuart",
["SOLDIER_RANDOM_36_NAME"] = "Tanner",
["SOLDIER_RANDOM_37_NAME"] = "Usher",
["SOLDIER_RANDOM_38_NAME"] = "Wallace",
["SOLDIER_RANDOM_39_NAME"] = "Wesley",
["SOLDIER_RANDOM_3_NAME"] = "James Lee",
["SOLDIER_RANDOM_40_NAME"] = "Willard",
["SOLDIER_RANDOM_4_NAME"] = "Jar Johson",
["SOLDIER_RANDOM_5_NAME"] = "Phil",
["SOLDIER_RANDOM_6_NAME"] = "Robin",
["SOLDIER_RANDOM_7_NAME"] = "William",
["SOLDIER_RANDOM_8_NAME"] = "Martin",
["SOLDIER_RANDOM_9_NAME"] = "Arthur",
["SOLDIER_REINFORCEMENTS_F_1_NAME"] = "Ataina",
["SOLDIER_REINFORCEMENTS_F_2_NAME"] = "Maucil",
["SOLDIER_REINFORCEMENTS_F_3_NAME"] = "Gulica",
["SOLDIER_REINFORCEMENTS_F_4_NAME"] = "Rogas",
["SOLDIER_REINFORCEMENTS_M_10_NAME"] = "Podgie",
["SOLDIER_REINFORCEMENTS_M_1_NAME"] = "Gabini",
["SOLDIER_REINFORCEMENTS_M_2_NAME"] = "O'Bell",
["SOLDIER_REINFORCEMENTS_M_3_NAME"] = "Kent",
["SOLDIER_REINFORCEMENTS_M_4_NAME"] = "Jendars",
["SOLDIER_REINFORCEMENTS_M_5_NAME"] = "Jarlosc",
["SOLDIER_REINFORCEMENTS_M_6_NAME"] = "Astong",
["SOLDIER_REINFORCEMENTS_M_7_NAME"] = "Buigell",
["SOLDIER_REINFORCEMENTS_M_8_NAME"] = "Clane",
["SOLDIER_REINFORCEMENTS_M_9_NAME"] = "Magus",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_1_NAME"] = "Dench",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_2_NAME"] = "Smith",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_3_NAME"] = "Andrews",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_4_NAME"] = "Thompson",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_F_5_NAME"] = "Taylor",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_1_NAME"] = "McCartney",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_2_NAME"] = "McKellen",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_3_NAME"] = "Hopkins",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_4_NAME"] = "Caine",
["SOLDIER_REINFORCEMENTS_REBEL_MILITIA_M_5_NAME"] = "Kingsley",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_10_NAME"] = "Vipère",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_1_NAME"] = "Fang",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_2_NAME"] = "Blade",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_3_NAME"] = "Claw",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_4_NAME"] = "Talon",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_5_NAME"] = "Edgee",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_6_NAME"] = "Shiv",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_7_NAME"] = "Faux",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_8_NAME"] = "Dague",
["SOLDIER_REINFORCEMENTS_SHADOW_ARCHER_9_NAME"] = "Piqûre",
["SOLDIER_REINFORCEMENTS_SPECIAL_DARK_ARMY_1_NAME"] = "Corbeau des Ombres",
["SOLDIER_REINFORCEMENTS_SPECIAL_LINIREA_1_NAME"] = "Chevalier Parangon",
["SOLDIER_STAGE_10_YMCA_BIKER_NAME"] = "Glenn",
["SOLDIER_STAGE_10_YMCA_CONSTRUCTOR_NAME"] = "David",
["SOLDIER_STAGE_10_YMCA_INDIO_NAME"] = "Philippe",
["SOLDIER_STAGE_10_YMCA_POLICIA_NAME"] = "Victor",
["SOLDIER_STAGE_15_DENAS_NAME"] = "Roi Denas",
["SOLDIER_TOWER_DARK_ELF_1_NAME"] = "Filraen",
["SOLDIER_TOWER_DARK_ELF_2_NAME"] = "Faeryl",
["SOLDIER_TOWER_DARK_ELF_3_NAME"] = "Gurina",
["SOLDIER_TOWER_DARK_ELF_4_NAME"] = "Jhalass",
["SOLDIER_TOWER_DARK_ELF_5_NAME"] = "Solenzar",
["SOLDIER_TOWER_DARK_ELF_6_NAME"] = "Tebryn",
["SOLDIER_TOWER_DARK_ELF_7_NAME"] = "Vierna",
["SOLDIER_TOWER_DARK_ELF_8_NAME"] = "Zyn",
["SOLDIER_TOWER_DARK_ELF_9_NAME"] = "Elerra",
["SOLDIER_TOWER_DWARF_10_NAME"] = "Babbi",
["SOLDIER_TOWER_DWARF_1_NAME"] = "Pippi",
["SOLDIER_TOWER_DWARF_2_NAME"] = "Ginni",
["SOLDIER_TOWER_DWARF_3_NAME"] = "Merri",
["SOLDIER_TOWER_DWARF_4_NAME"] = "Lorri",
["SOLDIER_TOWER_DWARF_5_NAME"] = "Talli",
["SOLDIER_TOWER_DWARF_6_NAME"] = "Danni",
["SOLDIER_TOWER_DWARF_7_NAME"] = "Getti",
["SOLDIER_TOWER_DWARF_8_NAME"] = "Daffi",
["SOLDIER_TOWER_DWARF_9_NAME"] = "Bibbi",
["SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "Elandil",
["SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "Puck",
["SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "Thas",
["SOLDIER_TOWER_ELVEN_BARRACK_4_NAME"] = "Kastore",
["SOLDIER_TOWER_ELVEN_BARRACK_5_NAME"] = "Elric",
["SOLDIER_TOWER_ELVEN_BARRACK_6_NAME"] = "Elaith",
["SOLDIER_TOWER_NECROMANCER_SKELETON_GOLEM_NAME"] = "Golem d'os",
["SOLDIER_TOWER_NECROMANCER_SKELETON_NAME"] = "Squelette",
["SOLDIER_TOWER_PANDAS_FEMALE_1_NAME"] = "Yan",
["SOLDIER_TOWER_PANDAS_FEMALE_2_NAME"] = "Qingzhao",
["SOLDIER_TOWER_PANDAS_FEMALE_3_NAME"] = "Hui",
["SOLDIER_TOWER_PANDAS_FEMALE_4_NAME"] = "Ailing",
["SOLDIER_TOWER_PANDAS_MALE_1_NAME"] = "Tzu",
["SOLDIER_TOWER_PANDAS_MALE_2_NAME"] = "Qian",
["SOLDIER_TOWER_PANDAS_MALE_3_NAME"] = "Xueqin",
["SOLDIER_TOWER_PANDAS_MALE_4_NAME"] = "Nai'an",
["SOLDIER_TOWER_PANDAS_MALE_5_NAME"] = "Xun",
["SOLDIER_TOWER_PANDAS_MALE_6_NAME"] = "Xingjian",
["SOLDIER_TOWER_PANDAS_MALE_7_NAME"] = "Wei",
["SOLDIER_TOWER_PANDAS_MALE_8_NAME"] = "Chen",
["SOLDIER_TOWER_ROCKET_GUNNERS_10_NAME"] = "Fortus",
["SOLDIER_TOWER_ROCKET_GUNNERS_1_NAME"] = "Axl",
["SOLDIER_TOWER_ROCKET_GUNNERS_2_NAME"] = "Rose",
["SOLDIER_TOWER_ROCKET_GUNNERS_3_NAME"] = "Slash",
["SOLDIER_TOWER_ROCKET_GUNNERS_4_NAME"] = "Hudson",
["SOLDIER_TOWER_ROCKET_GUNNERS_5_NAME"] = "Izzy",
["SOLDIER_TOWER_ROCKET_GUNNERS_6_NAME"] = "Duff",
["SOLDIER_TOWER_ROCKET_GUNNERS_7_NAME"] = "Adler",
["SOLDIER_TOWER_ROCKET_GUNNERS_8_NAME"] = "Dizzy",
["SOLDIER_TOWER_ROCKET_GUNNERS_9_NAME"] = "Ferrer",
["SOLDIER_ZHU_APPRENTICE_NAME"] = "Zhu Bajie",
["SPECIAL_ARBOREAN_BARRACK_DESCRIPTION"] = "Invoquez 3 soldats arboreens qui combattent les ennemis sur leur chemin.",
["SPECIAL_ARBOREAN_BARRACK_NAME"] = "Citoyens Arboreens",
["SPECIAL_ARBOREAN_HONEY_DESCRIPTION"] = "L'apiculteur prend son poste, ordonnant à ses abeilles de ralentir et d'endommager les ennemis avec du miel collant !",
["SPECIAL_ARBOREAN_HONEY_NAME"] = "Apiculteur Arboreen",
["SPECIAL_ARBOREAN_OLDTREE_DESCRIPTION"] = "Le bougon libère un énorme rondin qui écrase les ennemis sur son passage.",
["SPECIAL_ARBOREAN_OLDTREE_NAME"] = "Vieil Arbre",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_DESCRIPTION"] = "Protecteurs agiles de la forêt.",
["SPECIAL_ARBOREAN_SENTINELS_SPEARMEN_NAME"] = "Lance d'épines Arborean",
["SPECIAL_PRIESTS_SOLDIERS_DESCRIPTION"] = "Cultistes rachetés qui se transforment en abominations à leur mort.",
["SPECIAL_PRIESTS_SOLDIERS_NAME"] = "Cultistes Aveuglés",
["SPECIAL_REPAIR_HOLDER_DRAGON_DESCRIPTION"] = "Éteignez les flammes pour libérer la tour instantanément.",
["SPECIAL_REPAIR_HOLDER_DRAGON_NAME"] = "Enveloppé dans les flammes",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_DESCRIPTION"] = "Augmente la santé des unités de la tour.\nInvoque périodiquement 2 Guerriers de Pierre.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_EARTH_NAME"] = "Elemental Holder: Earth",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_DESCRIPTION"] = "Augmente les dégâts de la tour construite.\nTue occasionnellement un ennemi instantanément.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_FIRE_NAME"] = "Elemental Holder: Fire",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_DESCRIPTION"] = "Réduit le coût de construction.\nGénère de l’or à partir des ennemis.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_METAL_NAME"] = "Elemental Holder: Metal",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_DESCRIPTION"] = "Soigne constamment les unités alliées à proximité.\nTéléporte les ennemis en arrière le long du chemin.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WATER_NAME"] = "Elemental Holder: Water",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_DESCRIPTION"] = "Augmente la portée de la tour construite.\nFait occasionnellement apparaître des racines persistantes qui ralentissent les ennemis.",
["SPECIAL_REPAIR_HOLDER_ELEMENTAL_WOOD_NAME"] = "Elemental Holder: Wood",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_DESCRIPTION"] = "Nettoyez les décombres pour activer cette position stratégique. ",
["SPECIAL_REPAIR_HOLDER_SEA_OF_TREES_NAME"] = "Décombres ",
["SPECIAL_REPAIR_HOLDER_SPIDERS_DESCRIPTION"] = "Délivrez le porteur des toiles pour activer ce point stratégique.",
["SPECIAL_REPAIR_HOLDER_SPIDERS_NAME"] = "Porteur Enchevêtré",
["SPECIAL_REPAIR_OVERSEER_DESCRIPTION"] = "Repoussez les tentacules pour déverrouiller cette position stratégique.",
["SPECIAL_REPAIR_OVERSEER_NAME"] = "Tentacules",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_DESCRIPTION"] = "Engagez un mercenaire elfe pour aider au combat. Réapparaît toutes les 10 secondes.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_1_NAME"] = "Mercenaires elfes I",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_DESCRIPTION"] = "Engagez jusqu'à 2 mercenaires elfes pour aider au combat. Réapparaît toutes les 10 secondes.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_2_NAME"] = "Mercenaires elfes II",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_DESCRIPTION"] = "Engagez jusqu'à 3 mercenaires elfes pour aider au combat. Réapparaît toutes les 10 secondes.",
["SPECIAL_SOLDIER_TOWER_ELVEN_BARRACK_3_NAME"] = "Mercenaires elfes III",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_1"] = "Lance des éclairs de magie qui détruisent les illusions de Mydrias et l'empêchent d'en créer d'autres pendant quelques secondes.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_2"] = "Invoque 2 Gardes Démoniaques qui parcourent le chemin en combattant les ennemis.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_DESCRIPTION_3"] = "Piège Denas, l'empêchant de bouger et d'attaquer.",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_1"] = "Impact de l'Âme",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_2"] = "Rejetons Infernaux",
["SPECIAL_STAGE_11_VEZNAN_ABILITY_NAME_3"] = "Entraves Magiques",
["START"] = "COMMENCER",
["START BATTLE!"] = "COMMENCER LA BATAILLE!",
["START HERE!"] = "COMMENCE ICI!",
["STRATEGY BASICS!"] = "STRATÉGIE DE BASE!",
["Select"] = "Sélectionner",
["Select and train abilities"] = "Sélectionne et entraîne tes capacités",
["Select by clicking on the portrait or hero unit. Hotkey: space bar"] = "Sélectionne en cliquant sur le portrait ou l'unité du héros. Raccourci : barre d'espace",
["Select hero"] = "Sélectionne ton héros",
["Selected"] = "Sélectionné",
["Sell Tower"] = "Vendre tour",
["Sell this tower and get a %s GP refund."] = "Vends cette tour pour obtenir un remboursement de %s PO.",
["Short"] = "Courte",
["Shows level, health and experience."] = "Montre le niveau, la santé et l'expérience.",
["Skills"] = "Compétences",
["Skip this!"] = "Passer ça!",
["Slow"] = "Lent",
["Special abilities"] = "Capacités spéciales",
["Support your soldiers with ranged towers!"] = "Soutiens tes soldats avec des tours à distance!",
["Survival mode!"] = "Mode survie !",
["TAP_TO_START"] = "Tapez pour démarrer",
["TAUNT_BOSS_PIG_FROM_POOL_0001"] = "Je vais te faire crier !",
["TAUNT_BOSS_PIG_FROM_POOL_0002"] = "Dis 'bacon' encore. Je te mets au défi !",
["TAUNT_BOSS_PIG_FROM_POOL_0003"] = "Les humains sont de retour au menu, les garçons !",
["TAUNT_BOSS_PIG_FROM_POOL_0004"] = "Dépêche-toi ! J'ai faim.",
["TAUNT_BOSS_PIG_FROM_POOL_0005"] = "Je prendrai plaisir à te regarder mourir.",
["TAUNT_BOSS_PIG_FROM_POOL_0006"] = "Je sais, je suis le pire.",
["TAUNT_LVL30_BOSS_ABILITY_01"] = "Banquetez-vous, mes enfants !",
["TAUNT_LVL30_BOSS_ABILITY_02"] = "Tiens bon ! MWAHAHAHA !",
["TAUNT_LVL30_BOSS_ABILITY_03"] = "Pour le Culte !",
["TAUNT_LVL30_BOSS_ABILITY_04"] = "De délicieux repas pour tout le monde !",
["TAUNT_LVL30_BOSS_ABILITY_05"] = "Mon instinct d’araignée s’agite !",
["TAUNT_LVL30_BOSS_ABILITY_06"] = "Agenouille-toi devant moi, Alliance !",
["TAUNT_LVL30_BOSS_ABILITY_07"] = "Ma maison, mes règles !",
["TAUNT_LVL30_BOSS_ABILITY_08"] = "Personne n’échappe à ma toile !",
["TAUNT_LVL30_BOSS_ABILITY_09"] = "Meurs, peste humanoïde !",
["TAUNT_LVL30_BOSS_ABILITY_10"] = "Je tire vos ficelles !",
["TAUNT_LVL30_BOSS_ABILITY_11"] = "Tuez-les tous !",
["TAUNT_LVL30_BOSS_INTRO_01"] = "Enfin ! Les meurtriers de mes sœurs montrent enfin leur visage…",
["TAUNT_LVL30_BOSS_INTRO_02"] = "À la mémoire de mes frères et sœurs Sarelgaz et Mactans…",
["TAUNT_LVL30_BOSS_INTRO_03"] = "Vous finirez tous à genoux, à m’adorer !",
["TAUNT_LVL30_BOSS_PREFIGHT_01"] = "Ça suffit...",
["TAUNT_LVL30_BOSS_PREFIGHT_02"] = "Vous n’êtes que des insectes insignifiants...",
["TAUNT_LVL30_BOSS_PREFIGHT_03"] = "Pris dans la toile de la Reine !",
["TAUNT_LVL32_BOSS_ABILITY_01"] = "Imbéciles ! Je manie la flamme divine, le Feu Samadhi !",
["TAUNT_LVL32_BOSS_ABILITY_02"] = "Des flammes ardentes jaillissent des cieux !",
["TAUNT_LVL32_BOSS_ABILITY_03"] = "Craignez le vrai feu dans sa forme la plus pure !",
["TAUNT_LVL32_BOSS_ABILITY_04"] = "Chairs et âmes brûlent de la même manière !",
["TAUNT_LVL32_BOSS_FIGHT_01"] = "Le feu en moi ne mourra jamais !",
["TAUNT_LVL32_BOSS_FINAL_01"] = "Ma flamme s’éteint...\nmais j’ai encore mon dragon...",
["TAUNT_LVL32_BOSS_INTRO_01"] = "Alors tu as une armée ?",
["TAUNT_LVL32_BOSS_INTRO_02"] = "J’ai un dragon ! Ha ha ha ha !",
["TAUNT_LVL32_BOSS_PREFIGHT_01"] = "Assez ! C’est le moment où je gagne !",
["TAUNT_LVL32_BOSS_PREFIGHT_02"] = "Admire ma vraie forme !",
["TAUNT_LVL34_BOSS_BOSSFIGHT_01"] = "D’accord alors, je sais exactement ce qu’il nous faut. Plus de moi. Moi, moi, moi…",
["TAUNT_LVL34_BOSS_DEATH_01"] = "Ce n’est pas possible… Peu importe, mon mari vous fera payer…",
["TAUNT_LVL34_BOSS_INTRO_01"] = "Espèces de singes ! Vous osez venir ici après ce que vous avez fait à mon fils ?",
["TAUNT_LVL34_BOSS_WAVES_01"] = "Goûtez à mon pouvoir, imbéciles insolents !",
["TAUNT_LVL34_BOSS_WAVES_02"] = "La fin est proche !",
["TAUNT_STAGE02_RAELYN_0001"] = "Faisons-le.",
["TAUNT_STAGE02_VEZNAN_0001"] = "Les voici. Je vais aider vos forces dérisoires...",
["TAUNT_STAGE02_VEZNAN_0002"] = "...je veux dire, l'un de mes meilleurs soldats le fera. HA !",
["TAUNT_STAGE02_VEZNAN_0003"] = "HA HA HA !",
["TAUNT_STAGE06_BOSS_PIG_PREBATTLE_0001"] = "Bien... Je le ferai moi-même.",
["TAUNT_STAGE06_BOSS_PIG_RESPONSE_0001"] = "Détends-toi, tout est sous contrôle.",
["TAUNT_STAGE06_CULTIST_GREETING_0001"] = "Je vois que tu es très à l'aise là-bas...",
["TAUNT_STAGE06_CULTIST_GREETING_0002"] = "...tu ferais mieux de tenir ta part du marché.",
["TAUNT_STAGE11_CULTIST_LEADER_0001"] = "C'est bien que tu sois arrivé jusque-là...",
["TAUNT_STAGE11_CULTIST_LEADER_0002"] = "...mais tu ne peux pas arrêter l'inévitable !",
["TAUNT_STAGE11_CULTIST_LEADER_0003"] = "ASSEZ!!!",
["TAUNT_STAGE11_CULTIST_LEADER_0004"] = "Il est temps pour toi de T'INCLINER devant nous !",
["TAUNT_STAGE11_CULTIST_LEADER_0005"] = "Grrr... ce n'est pas la fin !",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0001"] = "Un nouveau monde nous attend.",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0002"] = "Tu sous-estimes mon pouvoir.",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0003"] = "Oculus Poculus !",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0004"] = "Écoute le son de l'inévitable !",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0005"] = "Suis-je maléfique ? Oui, je le suis !",
["TAUNT_STAGE11_CULTIST_LEADER_FIGHT_0006"] = "Le Tout-Voyant nous bénit !",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0001"] = "Ta fin est proche !",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0002"] = "Mes yeux ont été ouverts !",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0003"] = "Dites « bonjour » à mes amis du vide !",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0004"] = "Oculus Poculus !",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0005"] = "Pathétique faible déchet !",
["TAUNT_STAGE11_CULTIST_LEADER_IN_BOSSFIGHT_0006"] = "Le Tout-Voyant nous bénit !",
["TAUNT_STAGE11_VEZNAN_0001"] = "Denas, mon ami. Ça fait longtemps qu'on ne s'est pas vus !",
["TAUNT_STAGE15_CULTIST_0001"] = "C'est proche... Je peux le sentir se réveiller !",
["TAUNT_STAGE15_CULTIST_0002"] = "Une nouvelle ère est proche. Vos efforts seront vains !",
["TAUNT_STAGE15_CULTIST_0003"] = "Grrr... votre alliance est puissante.",
["TAUNT_STAGE15_CULTIST_0004"] = "Mais je vais vous montrer ce qu'est le vrai pouvoir !",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0001"] = "Fous ! Vous êtes venus pour mourir. ",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0002"] = "Surrendez-vous devant son regard !",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0003"] = "Tu deviendras un vrai croyant.",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0004"] = "Alliance ou non, vous êtes condamné !",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0005"] = "Il n'y a pas de vie dans le Vide. Seulement la mort",
["TAUNT_STAGE15_CULTIST_BEFORE_BOSSFIGHT_0006"] = "Arrête de perdre mon temps!",
["TAUNT_STAGE15_DENAS_0001"] = "J'ai un compte à régler. Je ne raterai pas ce combat!",
["TAUNT_STAGE16_DENAS_AFTER_BOSSFIGHT_0001"] = "Tu ne l'as pas vu venir, hein?",
["TAUNT_STAGE18_ERIDAN_FIGHT_0001"] = "Du sang a été versé ce soir.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0002"] = "En Elynie, nous avons confiance.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0003"] = "Gnilur speek Edihnori!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0004"] = "Je ne peux tout simplement pas manquer.",
["TAUNT_STAGE18_ERIDAN_FIGHT_0005"] = "Aredhel prévaudra!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0006"] = "Ce ne sont pas de simples rangers!",
["TAUNT_STAGE18_ERIDAN_FIGHT_0007"] = "Tu comptes les points?",
["TAUNT_STAGE18_ERIDAN_FIGHT_0008"] = "Laissez-les venir!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0001"] = "Tu as mon arc!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0002"] = "Agissez rapidement!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0003"] = "À vos postes!",
["TAUNT_STAGE18_ERIDAN_PREPARATION_0004"] = "Gardez les yeux ouverts!",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0001"] = "C'est suffisant pour l'échauffement!",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0002"] = "Vous avez prouvé que vous êtes une nuisance...",
["TAUNT_STAGE19_BOSS_NAVIRA_BEFORE_BOSSFIGHT_0003"] = "Que le véritable combat commence!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0001"] = "Je plie toutes les âmes!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0002"] = "Les elfes se relèveront.",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0003"] = "Je soulève même... les morts!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0004"] = "Par les anciens pouvoirs impies!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0005"] = "Craignez mes enfants de la tombe!",
["TAUNT_STAGE19_BOSS_NAVIRA_FIGHT_0006"] = "Je vais restaurer la gloire de mon peuple.",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0001"] = "Ah, la puissante Alliance est venue nous rendre visite.",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0002"] = "Il est temps de lever le voile!",
["TAUNT_STAGE19_BOSS_NAVIRA_START_0003"] = "Laissez-moi vous montrer le pouvoir de la mort!",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0001"] = "Enfin libre de dévorer...",
["TAUNT_STAGE22_BOSS_CROCS_BEFORE_BOSSFIGHT_0002"] = "TOUT !!!!!",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0001"] = "Assez d'ingérence !",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0002"] = "Grymbeard va vous apprendre les bonnes manières.",
["TAUNT_STAGE24_BOSS_MACHINIST_BEFORE_BOSSFIGHT_0003"] = "Tous à bord, AHAHAHA !",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0001"] = "Imbéciles insolents !",
["TAUNT_STAGE25_BOSS_MACHINIST_END_0002"] = "Vous ne m'attraperez jamais, HAHAHA !",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "Non ! Il y en a encore plus-...",
["TAUNT_STAGE26_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "GRAND SCOTT !!!",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0001"] = "Vous n'êtes pas de taille face à cette armée !",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0002"] = "Grymbeard n'est pas en danger.",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0003"] = "Grymbeard EST le danger !",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0004"] = "Un fou serait-il capable de faire ça ?",
["TAUNT_STAGE26_BOSS_GRYMBEARD_FIGHT_0005"] = "Le monde s'inclinera devant Grymbeard !",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0001"] = "La patience de Grymbeard a atteint ses limites.",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0002"] = "Vous allez voir des choses sérieuses !",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0003"] = "Grymbeard n'a besoin de personne d'autre que lui-même !",
["TAUNT_STAGE26_BOSS_GRYMBEARD_PREPARATION_0004"] = "Allez-vous vous dépêcher ?!",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0001"] = "Vous et votre maudite Alliance de fouineurs !",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0002"] = "Je vais vous apprendre à ne pas vous frotter...",
["TAUNT_STAGE27_BOSS_GRYMBEARD_BEFORE_BOSSFIGHT_0003"] = "...au nain PRINCIPAL !",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0001"] = "Écrasez toutes les répliques que vous voulez, j'en fabriquerai d'autres.",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0002"] = "Si vous voulez que quelque chose soit bien fait, faites-le vous-même.",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0003"] = "Oh Grymbeard, quel génie !",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0004"] = "Vous ne vous en tirerez pas comme ça !",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0005"] = "Vous faites vraiment un effort ?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_FIGHT_0006"] = "Vous pensez pouvoir surpasser mes créations ?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0001"] = "Je suppose que vous ne pouviez pas vous passer de moi...",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0002"] = "...et maintenant vous voulez vous en prendre au nain SUPRÊME ?",
["TAUNT_STAGE27_BOSS_GRYMBEARD_START_0003"] = "Vous êtes les bienvenus pour essayer.",
["TAUNT_TUTORIAL_ARBOREAN_ALL_0001"] = "Continuez ! Nous croyons en vous.",
["TAUNT_TUTORIAL_ARBOREAN_BARRACK_0001"] = "Ici, construisez une caserne!",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_1_NAME"] = "Limblliam",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_2_NAME"] = "Henry Tentaculaire",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_3_NAME"] = "Geoffrey Tentaculaire",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_4_NAME"] = "Tentaclas",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_5_NAME"] = "Tedtacle",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_6_NAME"] = "Holimb",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_7_NAME"] = "Tentodo",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_8_NAME"] = "Limbdric",
["TENTACLED_SOLDIER_PRIESTS_BARRACK_9_NAME"] = "Halimb",
["TERMS_OF_SERVICE_LINK"] = "Conditions d’utilisation",
["TIP!"] = "ASTUCE!",
["TIP_1"] = "N’oublie pas de toucher les ennemis pour lire des astuces sur la façon de t’en débarrasser. ",
["TIP_10"] = "Les ennemis volants ne peuvent pas être bloqués et la plupart des tours d’artillerie les ignorent.",
["TIP_11"] = "Au combat, fais pencher la balance en ta faveur avec les objets puissants de la boutique !",
["TIP_2"] = "Les dégâts magiques sont très efficaces pour éliminer les ennemis en armure lourde.",
["TIP_3"] = "Quand tu améliores une caserne, des soldats tout frais en sortent pour remplacer leurs camarades.",
["TIP_4"] = "Appelle une vague plus tôt pour obtenir de l’or supplémentaire et diminuer les temps de recharge.",
["TIP_5"] = "Tu peux ajuster le point de ralliement des casernes en fonction de tes stratégies.",
["TIP_6"] = "Touche le drapeau de la vague suivante pour savoir ce qui approche. Tiens-toi prêt !",
["TIP_7"] = "Les ennemis volants subissent la plupart des attaques de zone, même s’ils ne sont pas visés.",
["TIP_8"] = "Parfois, vendre une tour un peu inutile peut te donner assez d’argent pour te sortir du pétrin.",
["TIP_9"] = "Améliorer une tour est généralement plus efficace qu’en construire une autre identique.",
["TIP_ALERT_ICON"] = "ASTUCE",
["TIP_TITLE"] = "Astuce :",
["TOWER_ARBOREAN_EMISSARY_1_DESCRIPTION"] = "Les Arboréens rendent leurs ennemis plus vulnérables en utilisant une puissante magie de la nature",
["TOWER_ARBOREAN_EMISSARY_1_NAME"] = "Émissaire Arborean I",
["TOWER_ARBOREAN_EMISSARY_2_DESCRIPTION"] = "Les Arboréens rendent leurs ennemis plus vulnérables en utilisant une puissante magie de la nature. ",
["TOWER_ARBOREAN_EMISSARY_2_NAME"] = "Émissaire Arborean II",
["TOWER_ARBOREAN_EMISSARY_3_DESCRIPTION"] = "Les Arboreans rendent leurs ennemis plus vulnérables en utilisant une puissante magie de la nature. ",
["TOWER_ARBOREAN_EMISSARY_3_NAME"] = "Émissaire Arborean III",
["TOWER_ARBOREAN_EMISSARY_4_DESCRIPTION"] = "Les Arboreans rendent leurs ennemis plus vulnérables en utilisant une puissante magie de la nature",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_DESCRIPTION"] = "Invoque des feux follets qui soignent %$towers.arborean_emissary.gift_of_nature.s_heal[1]%$ de santé par seconde pendant %$towers.arborean_emissary.gift_of_nature.duration[1]%$ secondes aux alliés dans une zone",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_1_NAME"] = "DON DE LA NATURE",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_DESCRIPTION"] = "Invoque des feux follets qui restaurent %$towers.arborean_emissary.gift_of_nature.s_heal[2]%$ de santé pour une durée de %$towers.arborean_emissary.gift_of_nature.duration[2]%$ secondes aux alliés dans une zone.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_2_NAME"] = "DON DE LA NATURE",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_DESCRIPTION"] = "Invoque des feux follets qui restaurent %$towers.arborean_emissary.gift_of_nature.s_heal[3]%$ de santé pour une durée de %$towers.arborean_emissary.gift_of_nature.duration[3]%$ secondes aux alliés dans une zone.",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_3_NAME"] = "DON DE LA NATURE",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NAME"] = "DON DE LA NATURE",
["TOWER_ARBOREAN_EMISSARY_4_GIFT_OF_NATURE_NOTE"] = "Ne jamais se mêler du Vert.",
["TOWER_ARBOREAN_EMISSARY_4_NAME"] = "Émissaire Arborean IV",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_DESCRIPTION"] = "Fait pousser %$towers.arborean_emissary.wave_of_roots.max_targets[1]%$ racines le long du chemin, infligeant %$towers.arborean_emissary.wave_of_roots.s_damage[1]%$ de dégâts réels et étourdissant les ennemis pendant %$towers.arborean_emissary.wave_of_roots.mod_duration[1]%$ secondes.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_1_NAME"] = "EMBRASEMENT D'ÉPINES",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_DESCRIPTION"] = "Fait pousser %$towers.arborean_emissary.wave_of_roots.max_targets[2]%$ racines le long du chemin, infligeant %$towers.arborean_emissary.wave_of_roots.s_damage[2]%$ de dégâts réels et étourdissant les ennemis pendant %$towers.arborean_emissary.wave_of_roots.mod_duration[2]%$ secondes.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_2_NAME"] = "EMBRASEMENT D'ÉPINES",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_DESCRIPTION"] = "Fait pousser %$towers.arborean_emissary.wave_of_roots.max_targets[3]%$ racines le long du chemin, infligeant %$towers.arborean_emissary.wave_of_roots.s_damage[3]%$ de dégâts réels et étourdissant les ennemis pendant %$towers.arborean_emissary.wave_of_roots.mod_duration[3]%$ secondes.",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_3_NAME"] = "EMBRASEMENT D'ÉPINES",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NAME"] = "EMBRASEMENT D'ÉPINES",
["TOWER_ARBOREAN_EMISSARY_4_WAVE_OF_ROOTS_NOTE"] = "Faites attention à votre pas.",
["TOWER_ARBOREAN_EMISSARY_DESC"] = "Lorsqu'ils sont provoqués, les Arboreans ont été connus pour utiliser leur magie pour marquer et affaiblir leurs ennemis.",
["TOWER_ARBOREAN_EMISSARY_NAME"] = "Émissaire Arborean",
["TOWER_ARBOREAN_SENTINELS_DESCRIPTION"] = "Protecteurs agiles de la forêt.",
["TOWER_ARBOREAN_SENTINELS_NAME"] = "Lances d'épines Arborean",
["TOWER_ARCANE_WIZARD_1_DESCRIPTION"] = "Bien versés dans les arts de la magie, ces sorciers sont toujours prêts à se battre. ",
["TOWER_ARCANE_WIZARD_1_NAME"] = "Sorcier Arcanique I ",
["TOWER_ARCANE_WIZARD_2_DESCRIPTION"] = "Bien versés dans les arts de la magie, ces sorciers sont toujours prêts à se battre. ",
["TOWER_ARCANE_WIZARD_2_NAME"] = "Sorcier Arcanique II ",
["TOWER_ARCANE_WIZARD_3_DESCRIPTION"] = "Bien versés dans les arts de la magie, ces sorciers sont toujours prêts à se battre. ",
["TOWER_ARCANE_WIZARD_3_NAME"] = "Sorcier Arcanique III ",
["TOWER_ARCANE_WIZARD_4_DESCRIPTION"] = "Bien versés dans les arts de la magie, ces sorciers sont toujours prêts à se battre. ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_DESCRIPTION"] = "Lance un rayon qui tue instantanément la cible. Les boss et les mini-boss reçoivent %$towers.arcane_wizard.disintegrate.boss_damage[1]%$ de dégâts magiques à la place.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_1_NAME"] = "DÉSINTÉGRER ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_DESCRIPTION"] = "Réduit son temps de recharge à %$towers.arcane_wizard.disintegrate.cooldown[2]%$ secondes. Les dégâts infligés aux boss et mini-boss sont désormais de %$towers.arcane_wizard.disintegrate.boss_damage[2]%$.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_2_NAME"] = "DÉSINTÉGRER ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_DESCRIPTION"] = "Réduit son temps de recharge à %$towers.arcane_wizard.disintegrate.cooldown[3]%$ secondes. Les dégâts aux boss et mini-boss sont désormais de %$towers.arcane_wizard.disintegrate.boss_damage[3]%$.",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_3_NAME"] = "DÉSINTÉGRER ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NAME"] = "DÉSINTÉGRER ",
["TOWER_ARCANE_WIZARD_4_DISINTEGRATE_NOTE"] = "Poussière à la poussière. ",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_DESCRIPTION"] = "Augmente les dégâts des tours voisines de %$towers.arcane_wizard.empowerment.s_damage_factor[1]%$%.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_1_NAME"] = "Renforcement",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_DESCRIPTION"] = "Augmente les dégâts des tours à proximité de %$towers.arcane_wizard.empowerment.s_damage_factor[2]%$%.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_2_NAME"] = "Renforcement",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_DESCRIPTION"] = "Augmente les dégâts des tours à proximité de %$towers.arcane_wizard.empowerment.s_damage_factor[3]%$%.",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_3_NAME"] = "Renforcement",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NAME"] = "Renforcement",
["TOWER_ARCANE_WIZARD_4_EMPOWERMENT_NOTE"] = "Pouvoir illimité.",
["TOWER_ARCANE_WIZARD_4_NAME"] = "Sorcier Arcanique IV ",
["TOWER_ARCANE_WIZARD_DESC"] = "En puisant dans la magie pure, les sorciers Liniréens possèdent assez de pouvoir pour anéantir totalement leurs ennemis. ",
["TOWER_ARCANE_WIZARD_NAME"] = "Sorcier Arcanique ",
["TOWER_BALLISTA_1_DESCRIPTION"] = "Un excellent ajout à la guerre des peaux-vertes, c'est un miracle qu'elle n'ait pas encore éclaté.",
["TOWER_BALLISTA_1_NAME"] = "Avant-poste de Baliste I",
["TOWER_BALLISTA_2_DESCRIPTION"] = "Un excellent ajout à la guerre des peaux-vertes, c'est un miracle qu'elle n'ait pas encore éclaté.",
["TOWER_BALLISTA_2_NAME"] = "Avant-poste de Baliste II",
["TOWER_BALLISTA_3_DESCRIPTION"] = "Un excellent ajout à la guerre des peaux-vertes, c'est un miracle qu'elle n'ait pas encore éclaté.",
["TOWER_BALLISTA_3_NAME"] = "Avant-poste de Baliste III",
["TOWER_BALLISTA_4_DESCRIPTION"] = "Un excellent ajout à la guerre des peaux-vertes, c'est un miracle qu'elle n'ait pas encore éclaté.",
["TOWER_BALLISTA_4_NAME"] = "Avant-poste de Baliste IV",
["TOWER_BALLISTA_4_SKILL_BOMB_1_DESCRIPTION"] = "Lance une bombe faite de débris à longue portée qui inflige %$towers.ballista.skill_bomb.damage_min[1]%$-%$towers.ballista.skill_bomb.damage_max[1]%$ de dégâts physiques. Elle ralentit les ennemis pendant %$towers.ballista.skill_bomb.duration[1]%$ secondes.",
["TOWER_BALLISTA_4_SKILL_BOMB_1_NAME"] = "BOMBE DE FERRAILLE",
["TOWER_BALLISTA_4_SKILL_BOMB_2_DESCRIPTION"] = "La bombe de ferraille inflige %$towers.ballista.skill_bomb.damage_min[2]%$-%$towers.ballista.skill_bomb.damage_max[2]%$ de dégâts physiques. Elle ralentit les ennemis pendant %$towers.ballista.skill_bomb.duration[1]%$ secondes.",
["TOWER_BALLISTA_4_SKILL_BOMB_2_NAME"] = "BOMBE DE FERRAILLE",
["TOWER_BALLISTA_4_SKILL_BOMB_3_DESCRIPTION"] = "La bombe de ferraille inflige %$towers.ballista.skill_bomb.damage_min[3]%$-%$towers.ballista.skill_bomb.damage_max[3]%$ de dégâts physiques. Elle ralentit les ennemis pendant %$towers.ballista.skill_bomb.duration[1]%$ secondes.",
["TOWER_BALLISTA_4_SKILL_BOMB_3_NAME"] = "BOMBE DE FERRAILLE",
["TOWER_BALLISTA_4_SKILL_BOMB_NOTE"] = "Balle!",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_DESCRIPTION"] = "Le dernier tir de la tour inflige %$towers.ballista.skill_final_shot.s_damage_factor[1]%$% de dégâts supplémentaires et étourdit la cible pendant %$towers.ballista.skill_final_shot.s_stun%$ seconde.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_1_NAME"] = "DERNIER CLOU",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_DESCRIPTION"] = "Le dernier tir inflige %$towers.ballista.skill_final_shot.s_damage_factor[2]%$% de dégâts supplémentaires et étourdit la cible pendant %$towers.ballista.skill_final_shot.s_stun%$ seconde.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_2_NAME"] = "DERNIER CLOU",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_DESCRIPTION"] = "Le dernier tir inflige %$towers.ballista.skill_final_shot.s_damage_factor[3]%$% de dégâts supplémentaires et étourdit la cible pendant %$towers.ballista.skill_final_shot.s_stun%$ seconde.",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_3_NAME"] = "DERNIER CLOU",
["TOWER_BALLISTA_4_SKILL_FINAL_SHOT_NOTE"] = "C'était un sur un million, gamin !",
["TOWER_BALLISTA_DESC"] = "Extrêmement enthousiastes à l'idée de la guerre, les gobelins ont redoublé d'efforts pour s'assurer qu'ils n'auraient plus jamais à manier un arc.",
["TOWER_BALLISTA_NAME"] = "Avant-poste de Baliste",
["TOWER_BARREL_1_DESCRIPTION"] = "Les caisses de potion des nordiques sont une arme puissante contre les hordes d'ennemis.",
["TOWER_BARREL_1_NAME"] = "Maîtres Fermenteurs I",
["TOWER_BARREL_2_DESCRIPTION"] = "Les caisses de potion des nordiques sont une arme puissante contre les hordes d'ennemis.",
["TOWER_BARREL_2_NAME"] = "Maîtres Fermenteurs II",
["TOWER_BARREL_3_DESCRIPTION"] = "Les caisses de potion des nordiques sont une arme puissante contre les hordes d'ennemis.",
["TOWER_BARREL_3_NAME"] = "Maîtres Fermenteurs III",
["TOWER_BARREL_4_DESCRIPTION"] = "Les caisses de potion des nordiques sont une arme puissante contre les hordes d'ennemis.",
["TOWER_BARREL_4_NAME"] = "Maîtres Fermenteurs IV",
["TOWER_BARREL_4_SKILL_BARREL_1_DESCRIPTION"] = "Lance un baril nocif qui inflige de %$towers.barrel.skill_barrel.explosion.damage_min[1]%$ à %$towers.barrel.skill_barrel.explosion.damage_max[1]%$ de dégâts physiques. Le baril laisse un poison qui inflige %$towers.barrel.skill_barrel.poison.s_damage%$ de dégâts réels par seconde pendant %$towers.barrel.skill_barrel.poison.duration%$ secondes. ",
["TOWER_BARREL_4_SKILL_BARREL_1_NAME"] = "MAUVAIS LOT",
["TOWER_BARREL_4_SKILL_BARREL_2_DESCRIPTION"] = "L'explosion du baril de poison cause de %$towers.barrel.skill_barrel.explosion.damage_min[2]%$ à %$towers.barrel.skill_barrel.explosion.damage_max[2]%$ de dégâts physiques. Le poison du baril cause %$towers.barrel.skill_barrel.poison.s_damage%$ de dégâts réels par seconde pendant %$towers.barrel.skill_barrel.poison.duration%$ secondes.",
["TOWER_BARREL_4_SKILL_BARREL_2_NAME"] = "MAUVAIS LOT",
["TOWER_BARREL_4_SKILL_BARREL_3_DESCRIPTION"] = "L'explosion du baril de poison inflige de %$towers.barrel.skill_barrel.explosion.damage_min[3]%$ à %$towers.barrel.skill_barrel.explosion.damage_max[3]%$ de dégâts physiques. Le poison du baril inflige %$towers.barrel.skill_barrel.poison.s_damage%$ de dégâts réels par seconde pendant %$towers.barrel.skill_barrel.poison.duration%$ secondes.",
["TOWER_BARREL_4_SKILL_BARREL_3_NAME"] = "MAUVAIS LOT",
["TOWER_BARREL_4_SKILL_BARREL_NOTE"] = "Uniquement pour les courageux !",
["TOWER_BARREL_4_SKILL_WARRIOR_1_DESCRIPTION"] = "Invoque un guerrier renforcé pour combattre sur le chemin. Il a %$towers.barrel.skill_warrior.entity.hp_max[1]%$ de santé et inflige %$towers.barrel.skill_warrior.entity.damage_min[1]%$-%$towers.barrel.skill_warrior.entity.damage_max[1]%$ de dégâts physiques.",
["TOWER_BARREL_4_SKILL_WARRIOR_1_NAME"] = "ÉLIXIR DE PUISSANCE",
["TOWER_BARREL_4_SKILL_WARRIOR_2_DESCRIPTION"] = "Le guerrier a %$towers.barrel.skill_warrior.entity.hp_max[2]%$ de santé et inflige %$towers.barrel.skill_warrior.entity.damage_min[2]%$-%$towers.barrel.skill_warrior.entity.damage_max[2]%$ de dégâts physiques.",
["TOWER_BARREL_4_SKILL_WARRIOR_2_NAME"] = "ÉLIXIR DE PUISSANCE",
["TOWER_BARREL_4_SKILL_WARRIOR_3_DESCRIPTION"] = "Le guerrier a %$towers.barrel.skill_warrior.entity.hp_max[3]%$ points de vie et inflige %$towers.barrel.skill_warrior.entity.damage_min[3]%$-%$towers.barrel.skill_warrior.entity.damage_max[3]%$ dégâts physiques.",
["TOWER_BARREL_4_SKILL_WARRIOR_3_NAME"] = "ÉLIXIR DE PUISSANCE",
["TOWER_BARREL_4_SKILL_WARRIOR_NOTE"] = "Ça a le goût de la victoire !",
["TOWER_BARREL_DESC"] = "Les nordiques sont experts dans l'art de la fabrication de potions et utilisent leurs boissons au combat pour lutter contre les ennemis.",
["TOWER_BARREL_NAME"] = "Maîtres Fermenteurs",
["TOWER_BARREL_WARRIOR_NAME"] = "Halfdan le Terne",
["TOWER_BROKEN_DESCRIPTION"] = "Cette tour est endommagée, payez de l'or pour la réparer.",
["TOWER_BROKEN_NAME"] = "Tour Endommagée",
["TOWER_CROCS_EATEN_DESCRIPTION"] = "Reconstruire magiquement la tour à sa forme originale.",
["TOWER_CROCS_EATEN_NAME"] = "Restes de tour",
["TOWER_DARK_ELF_1_DESCRIPTION"] = "Peu importe la distance ou la force de l'ennemi, leur visée est toujours précise.",
["TOWER_DARK_ELF_1_NAME"] = "Arcs longs du crépuscule I",
["TOWER_DARK_ELF_2_DESCRIPTION"] = "Peu importe la distance ou la force de l'ennemi, leur visée est toujours précise.",
["TOWER_DARK_ELF_2_NAME"] = "Arcs longs du crépuscule II",
["TOWER_DARK_ELF_3_DESCRIPTION"] = "Peu importe la distance ou la force de l'ennemi, leur visée est toujours précise.",
["TOWER_DARK_ELF_3_NAME"] = "Arcs longs du crépuscule III",
["TOWER_DARK_ELF_4_DESCRIPTION"] = "Peu importe la distance ou la force de l'ennemi, leur visée est toujours précise.",
["TOWER_DARK_ELF_4_NAME"] = "Arcs longs du crépuscule IV",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_DESCRIPTION"] = "Chaque fois qu'elle tue un ennemi, la tour augmente ses dégâts d'attaque de %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_1_NAME"] = "FRISSON DE LA CHASSE",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_DESCRIPTION"] = "Chaque fois qu'elle tue un ennemi, la tour augmente ses dégâts d'attaque de %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_2_NAME"] = "FRISSON DE LA CHASSE",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_DESCRIPTION"] = "Chaque fois qu'elle tue un ennemi, la tour augmente ses dégâts d'attaque de %$towers.dark_elf.skill_buff.extra_damage_min[1]%$-%$towers.dark_elf.skill_buff.extra_damage_max[1]%$.",
["TOWER_DARK_ELF_4_SKILL_BUFF_3_NAME"] = "FRISSON DE LA CHASSE",
["TOWER_DARK_ELF_4_SKILL_BUFF_NOTE"] = "Tally-ho!",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_DESCRIPTION"] = "Invoque deux Harceleurs du Crépuscule. Ils ont %$towers.dark_elf.soldier.hp[1]%$ points de vie et infligent %$towers.dark_elf.soldier.basic_attack.damage_min[1]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[1]%$ de dégâts physiques.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_1_NAME"] = "LAMES DE SOUTIEN",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_DESCRIPTION"] = "Les Harceleurs du Crépuscule ont maintenant %$towers.dark_elf.soldier.hp[2]%$ points de vie et infligent %$towers.dark_elf.soldier.basic_attack.damage_min[2]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[2]%$ de dégâts physiques.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_2_NAME"] = "LAMES DE SOUTIEN",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_DESCRIPTION"] = "Les Harceleurs du Crépuscule ont maintenant %$towers.dark_elf.soldier.hp[3]%$ points de vie et infligent %$towers.dark_elf.soldier.basic_attack.damage_min[3]%$-%$towers.dark_elf.soldier.basic_attack.damage_max[3]%$ de dégâts physiques.",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_3_NAME"] = "LAMES DE SOUTIEN",
["TOWER_DARK_ELF_4_SKILL_SOLDIERS_NOTE"] = "Ils descendent pour jouer.",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_DESCRIPTION"] = "Change la priorité de la tour vers l'ennemi le plus proche de la sortie.",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NAME"] = "Cible Ennemi : Plus proche",
["TOWER_DARK_ELF_CHANGE_MODE_FOREMOST_NOTE"] = "Ne les laissez pas passer !",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_DESCRIPTION"] = "Change la priorité de la tour vers l'ennemi ayant le plus de santé.",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NAME"] = "Cible Ennemi : PV Max",
["TOWER_DARK_ELF_CHANGE_MODE_MAXHP_NOTE"] = "Attaquez le plus costaud !",
["TOWER_DARK_ELF_DESC"] = "Archers spécialisés dans la chasse aux ennemis puissants à distance, renforçant leurs tirs avec de l'énergie sombre.",
["TOWER_DARK_ELF_NAME"] = "Arcs longs du crépuscule",
["TOWER_DEMON_PIT_1_DESCRIPTION"] = "Malicieux et dangereux, ces démons sont toujours à la recherche de problèmes.",
["TOWER_DEMON_PIT_1_NAME"] = "Fosse Démoniaque I",
["TOWER_DEMON_PIT_2_DESCRIPTION"] = "Malicieux et dangereux, ces démons sont toujours à la recherche de problèmes.",
["TOWER_DEMON_PIT_2_NAME"] = "Fosse Démoniaque II",
["TOWER_DEMON_PIT_3_DESCRIPTION"] = "Malicieux et dangereux, ces démons sont toujours à la recherche de problèmes.",
["TOWER_DEMON_PIT_3_NAME"] = "Fosse Démoniaque III",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_DESCRIPTION"] = "Invoque un énorme diablotin avec %$towers.demon_pit.big_guy.hp_max[1]%$ de santé qui inflige %$towers.demon_pit.big_guy.melee_attack.damage_min[1]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[1]%$ de dégâts physiques. Lorsqu'il explose, inflige %$towers.demon_pit.big_guy.explosion_damage[1]%$ de dégâts.",
["TOWER_DEMON_PIT_4_BIG_DEMON_1_NAME"] = "GRAND PATRON",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_DESCRIPTION"] = "Le Grand Imp a %$towers.demon_pit.big_guy.hp_max[2]%$ de santé et inflige %$towers.demon_pit.big_guy.melee_attack.damage_min[2]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[2]%$ de dégâts physiques. L'explosion inflige %$towers.demon_pit.big_guy.explosion_damage[2]%$ de dégâts.",
["TOWER_DEMON_PIT_4_BIG_DEMON_2_NAME"] = "GRAND PATRON",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_DESCRIPTION"] = "Le Grand Imp a %$towers.demon_pit.big_guy.hp_max[3]%$ de santé et inflige %$towers.demon_pit.big_guy.melee_attack.damage_min[3]%$-%$towers.demon_pit.big_guy.melee_attack.damage_max[3]%$ de dégâts physiques. L'explosion inflige %$towers.demon_pit.big_guy.explosion_damage[3]%$ de dégâts.",
["TOWER_DEMON_PIT_4_BIG_DEMON_3_NAME"] = "GRAND PATRON",
["TOWER_DEMON_PIT_4_BIG_DEMON_NAME"] = "GRAND PATRON",
["TOWER_DEMON_PIT_4_BIG_DEMON_NOTE"] = "Juste en train de me détendre.",
["TOWER_DEMON_PIT_4_DESCRIPTION"] = "Malicieux et dangereux, ces démons sont toujours à la recherche de problèmes.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_DESCRIPTION"] = "L'explosion des diablotins inflige désormais %$towers.demon_pit.master_exploders.s_damage_increase[1]%$% de dégâts supplémentaires et brûle les ennemis, infligeant %$towers.demon_pit.master_exploders.s_total_burning_damage_min[1]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[1]%$ de dégâts réels par seconde pendant %$towers.demon_pit.master_exploders.s_burning_duration[1]%$ secondes. ",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_1_NAME"] = "MAÎTRES EXPLOSEURS",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_DESCRIPTION"] = "L'explosion des diablotins inflige %$towers.demon_pit.master_exploders.s_damage_increase[2]%$% de dégâts supplémentaires. Les brûlures infligent %$towers.demon_pit.master_exploders.s_total_burning_damage_min[2]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[2]%$ de dégâts réels par seconde pendant %$towers.demon_pit.master_exploders.s_burning_duration[2]%$ secondes.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_2_NAME"] = "MAÎTRES EXPLOSEURS",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_DESCRIPTION"] = "L'explosion des diablotins inflige %$towers.demon_pit.master_exploders.s_damage_increase[3]%$% de dégâts supplémentaires. Les brûlures infligent %$towers.demon_pit.master_exploders.s_total_burning_damage_min[3]%$-%$towers.demon_pit.master_exploders.s_total_burning_damage_max[3]%$ de dégâts réels par seconde pendant %$towers.demon_pit.master_exploders.s_burning_duration[3]%$ secondes.",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_3_NAME"] = "MAÎTRES EXPLOSEURS",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NAME"] = "MAÎTRES EXPLOSEURS",
["TOWER_DEMON_PIT_4_MASTER_EXPLODERS_NOTE"] = "Seul un idiot ferait ce travail.",
["TOWER_DEMON_PIT_4_NAME"] = "Fosse Démoniaque IV",
["TOWER_DEMON_PIT_DESC"] = "Jaillissant des profondeurs de la lave, ces diablotins n'hésitent pas à se jeter sur le chemin des ennemis.",
["TOWER_DEMON_PIT_NAME"] = "Fosse Démoniaque",
["TOWER_DEMON_PIT_SOLDIER_BIG_GUY_NAME"] = "Grand Gaillard",
["TOWER_DEMON_PIT_SOLDIER_NAME"] = "Diablotin",
["TOWER_DWARF_1_DESCRIPTION"] = "Bien que leurs mèches soient aussi courtes qu'eux, rien ne passe vivant à travers leurs lignes.",
["TOWER_DWARF_1_NAME"] = "Escouade de Canonniers I",
["TOWER_DWARF_2_DESCRIPTION"] = "Bien que leurs mèches soient aussi courtes qu'eux, rien ne passe vivant à travers leurs lignes.",
["TOWER_DWARF_2_NAME"] = "Escouade de Canonniers II",
["TOWER_DWARF_3_DESCRIPTION"] = "Bien que leurs mèches soient aussi courtes qu'eux, rien ne passe vivant à travers leurs lignes.",
["TOWER_DWARF_3_NAME"] = "Escouade de Canonniers III",
["TOWER_DWARF_4_DESCRIPTION"] = "Bien que leurs mèches soient aussi courtes qu'eux, rien ne passe vivant à travers leurs lignes.",
["TOWER_DWARF_4_FORMATION_1_DESCRIPTION"] = "Ajoutez un troisième Canonniers à l'escouade.",
["TOWER_DWARF_4_FORMATION_1_NAME"] = "RANGS CROISSANTS",
["TOWER_DWARF_4_FORMATION_2_DESCRIPTION"] = "Ajoutez un quatrième Canonniers à l'escouade.",
["TOWER_DWARF_4_FORMATION_2_NAME"] = "RANGS CROISSANTS",
["TOWER_DWARF_4_FORMATION_3_DESCRIPTION"] = "Ajoutez un cinquième Canonniers à l'escouade.",
["TOWER_DWARF_4_FORMATION_3_NAME"] = "RANGS CROISSANTS",
["TOWER_DWARF_4_FORMATION_NOTE"] = "Les filles veulent juste des fusils.",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_DESCRIPTION"] = "Tirez un explosif qui inflige %$towers.dwarf.incendiary_ammo.damages_min[1]%$ - %$towers.dwarf.incendiary_ammo.damages_max[1]%$ dégâts et brûle les ennemis dans la zone pour %$towers.dwarf.incendiary_ammo.burn.s_damage[1]%$ dégâts pendant %$towers.dwarf.incendiary_ammo.burn.duration%$ secondes.",
["TOWER_DWARF_4_INCENDIARY_AMMO_1_NAME"] = "MUNITIONS INCENDIAIRES",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_DESCRIPTION"] = "Tirez un explosif qui inflige %$towers.dwarf.incendiary_ammo.damages_min[2]%$ - %$towers.dwarf.incendiary_ammo.damages_max[2]%$ dégâts et brûle les ennemis dans la zone pour %$towers.dwarf.incendiary_ammo.burn.s_damage[2]%$ dégâts pendant %$towers.dwarf.incendiary_ammo.burn.duration%$ secondes.",
["TOWER_DWARF_4_INCENDIARY_AMMO_2_NAME"] = "MUNITIONS INCENDIAIRES",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_DESCRIPTION"] = "Tirez un explosif qui inflige %$towers.dwarf.incendiary_ammo.damages_min[3]%$ - %$towers.dwarf.incendiary_ammo.damages_max[3]%$ dégâts et brûle les ennemis dans la zone pour %$towers.dwarf.incendiary_ammo.burn.s_damage[3]%$ dégâts pendant %$towers.dwarf.incendiary_ammo.burn.duration%$ secondes.",
["TOWER_DWARF_4_INCENDIARY_AMMO_3_NAME"] = "MUNITIONS INCENDIAIRES",
["TOWER_DWARF_4_INCENDIARY_AMMO_NOTE"] = "Prêt à chauffer !",
["TOWER_DWARF_4_NAME"] = "Escouade de Canonniers IV",
["TOWER_DWARF_DESC"] = "Tireurs experts avec un esprit de corps inégalé, envoyés du nord pour contrôler l'utilisation inadéquate de la technologie.",
["TOWER_DWARF_NAME"] = "Escouade de Canonniers",
["TOWER_ELVEN_STARGAZERS_DESC"] = "En invoquant les énergies du cosmos, les Étoilevoyants Elfes peuvent combattre de nombreux ennemis en même temps.",
["TOWER_ELVEN_STARGAZERS_NAME"] = "Étoilevoyant Elfe",
["TOWER_FLAMESPITTER_1_DESCRIPTION"] = "Son feu peut être facilement comparé à celui d'un dragon, répandant la terreur parmi les méchants. ",
["TOWER_FLAMESPITTER_1_NAME"] = "Cracheur de flammes nain I ",
["TOWER_FLAMESPITTER_2_DESCRIPTION"] = "Son feu peut être facilement comparé à celui d'un dragon, répandant la terreur parmi les méchants. ",
["TOWER_FLAMESPITTER_2_NAME"] = "Cracheur de flammes nain II ",
["TOWER_FLAMESPITTER_3_DESCRIPTION"] = "Son feu peut être facilement comparé à celui d'un dragon, répandant la terreur parmi les méchants. ",
["TOWER_FLAMESPITTER_3_NAME"] = "Cracheur de flammes nain III ",
["TOWER_FLAMESPITTER_4_DESCRIPTION"] = "Son feu peut être facilement comparé à celui d'un dragon, répandant la terreur parmi les méchants. ",
["TOWER_FLAMESPITTER_4_NAME"] = "Cracheur de flammes nain IV ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_DESCRIPTION"] = "Lance une bombe enflammée qui inflige %$towers.flamespitter.skill_bomb.s_damage[1]%$ de dégâts physiques et brûle les ennemis pour %$towers.flamespitter.skill_bomb.burning.s_damage%$ dégâts réels par seconde pendant %$towers.flamespitter.skill_bomb.burning.duration%$ secondes.",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_1_NAME"] = "SENTIER ARDENT ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_DESCRIPTION"] = "La bombe enflammée inflige %$towers.flamespitter.skill_bomb.s_damage[2]%$ de dégâts physiques. La brûlure inflige %$towers.flamespitter.skill_bomb.burning.s_damage%$ de dégâts réels par seconde pendant %$towers.flamespitter.skill_bomb.burning.duration%$ secondes. ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_2_NAME"] = "SENTIER ARDENT ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_DESCRIPTION"] = "La bombe enflammée inflige %$towers.flamespitter.skill_bomb.s_damage[3]%$ de dégâts physiques. La brûlure inflige %$towers.flamespitter.skill_bomb.burning.s_damage%$ de dégâts réels par seconde pendant %$towers.flamespitter.skill_bomb.burning.duration%$ secondes. ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_3_NAME"] = "SENTIER ARDENT ",
["TOWER_FLAMESPITTER_4_SKILL_BOMB_NOTE"] = "Brûle pour être sauvage. ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_DESCRIPTION"] = "Des colonnes de feu jaillissent du chemin infligeant %$towers.flamespitter.skill_columns.s_damage_out[1]%$-%$towers.flamespitter.skill_columns.s_damage_in[1]%$ de dégâts physiques et étourdissant les ennemis pendant %$towers.flamespitter.skill_columns.s_stun%$ seconde. ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_1_NAME"] = "TORCHES BRÛLANTES ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_DESCRIPTION"] = "Les colonnes de feu infligent de %$towers.flamespitter.skill_columns.s_damage_out[2]%$-%$towers.flamespitter.skill_columns.s_damage_in[2]%$ de dégâts physiques et étourdissent les ennemis pendant %$towers.flamespitter.skill_columns.s_stun%$ seconde. ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_2_NAME"] = "TORCHES BRÛLANTES ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_DESCRIPTION"] = "Les colonnes de feu infligent de %$towers.flamespitter.skill_columns.s_damage_out[3]%$-%$towers.flamespitter.skill_columns.s_damage_in[3]%$ de dégâts physiques et étourdissent les ennemis pendant %$towers.flamespitter.skill_columns.s_stun%$ seconde. ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_3_NAME"] = "TORCHES BRÛLANTES ",
["TOWER_FLAMESPITTER_4_SKILL_COLUMNS_NOTE"] = "Faites attention où vous mettez les pieds ! ",
["TOWER_FLAMESPITTER_DESC"] = "Apportant la chaleur de la forge au combat, les nains prêtent leur résolution ardente à l'alliance. ",
["TOWER_FLAMESPITTER_NAME"] = "Cracheur de flammes nain ",
["TOWER_GHOST_1_DESCRIPTION"] = "Maintenant vous les voyez, maintenant vous ne les voyez pas. Maintenant, vous êtes mort.",
["TOWER_GHOST_1_NAME"] = "Spectres Sinistres I",
["TOWER_GHOST_2_DESCRIPTION"] = "Maintenant vous les voyez, maintenant vous ne les voyez pas. Maintenant, vous êtes mort.",
["TOWER_GHOST_2_NAME"] = "Spectres Sinistres II",
["TOWER_GHOST_3_DESCRIPTION"] = "Maintenant vous les voyez, maintenant vous ne les voyez pas. Maintenant, vous êtes mort.",
["TOWER_GHOST_3_NAME"] = "Spectres Sinistres III",
["TOWER_GHOST_4_DESCRIPTION"] = "Maintenant vous les voyez, maintenant vous ne les voyez pas. Maintenant, vous êtes mort.",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_DESCRIPTION"] = "Les spectres infligent %$towers.ghost.extra_damage.s_damage[1]%$% de dégâts supplémentaires après avoir passé %$towers.ghost.extra_damage.cooldown_start%$ secondes en combat.",
["TOWER_GHOST_4_EXTRA_DAMAGE_1_NAME"] = "SIPHONNAGE D'ÂME",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_DESCRIPTION"] = "Les spectres infligent %$towers.ghost.extra_damage.s_damage[2]%$% de dégâts supplémentaires après avoir passé %$towers.ghost.extra_damage.cooldown_start%$ secondes en combat.",
["TOWER_GHOST_4_EXTRA_DAMAGE_2_NAME"] = "SIPHONNAGE D'ÂME",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_DESCRIPTION"] = "Les spectres infligent %$towers.ghost.extra_damage.s_damage[3]%$% de dégâts supplémentaires après avoir passé %$towers.ghost.extra_damage.cooldown_start%$ secondes en combat.",
["TOWER_GHOST_4_EXTRA_DAMAGE_3_NAME"] = "SIPHONNAGE D'ÂME",
["TOWER_GHOST_4_EXTRA_DAMAGE_NOTE"] = "Exposition non recommandée.",
["TOWER_GHOST_4_NAME"] = "Spectres Sinistres IV",
["TOWER_GHOST_4_SOUL_ATTACK_1_DESCRIPTION"] = "Les spectres vaincus se jettent sur un ennemi proche, infligeant %$towers.ghost.soul_attack.s_damage[1]%$ de dégâts vrais, réduisant sa vitesse et diminuant de moitié ses dégâts d'attaque.",
["TOWER_GHOST_4_SOUL_ATTACK_1_NAME"] = "TEOR ETERNO",
["TOWER_GHOST_4_SOUL_ATTACK_2_DESCRIPTION"] = "Les wraiths vaincus se jettent sur un ennemi proche, infligeant %$towers.ghost.soul_attack.s_damage[2]%$ de dommages réels, réduisant sa vitesse et réduisant de moitié ses dégâts d'attaque.",
["TOWER_GHOST_4_SOUL_ATTACK_2_NAME"] = "TERREUR ÉTERNELLE",
["TOWER_GHOST_4_SOUL_ATTACK_3_DESCRIPTION"] = "Les spectres vaincus se jettent sur un ennemi proche, infligeant %$towers.ghost.soul_attack.s_damage[3]%$ de dégâts réels, réduisant sa vitesse et diminuant de moitié ses dégâts d'attaque.",
["TOWER_GHOST_4_SOUL_ATTACK_3_NAME"] = "TERREUR ÉTERNELLE",
["TOWER_GHOST_4_SOUL_ATTACK_NOTE"] = "Tu viens avec nous !",
["TOWER_GHOST_DESC"] = "Spectres qui combattent même après la mort. Leur pouvoir leur permet de se déplacer à travers les ombres et de surprendre les ennemis.",
["TOWER_GHOST_NAME"] = "Spectres Sinistres",
["TOWER_HERMIT_TOAD_1_DESCRIPTION"] = "Un peu de magie, un peu de force brute, tout ce qu'il faut pour se débarrasser des intrus importuns.",
["TOWER_HERMIT_TOAD_1_NAME"] = "Ermite des Tourbières I",
["TOWER_HERMIT_TOAD_2_DESCRIPTION"] = "Un peu de magie, un peu de force brute, tout ce qu'il faut pour se débarrasser des intrus importuns.",
["TOWER_HERMIT_TOAD_2_NAME"] = "Ermite des Tourbières II",
["TOWER_HERMIT_TOAD_3_DESCRIPTION"] = "Un peu de magie, un peu de force brute, tout ce qu'il faut pour se débarrasser des intrus importuns.",
["TOWER_HERMIT_TOAD_3_NAME"] = "Ermite des Tourbières III",
["TOWER_HERMIT_TOAD_4_DESCRIPTION"] = "Un peu de magie, un peu de force brute, tout ce qu'il faut pour se débarrasser des intrus importuns.",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_DESCRIPTION"] = "Tous les %$towers.hermit_toad.power_instakill.cooldown[1]%$ secondes, il utilise sa langue pour dévorer un ennemi.",
["TOWER_HERMIT_TOAD_4_INSTAKILL_1_NAME"] = "Langue Collante",
["TOWER_HERMIT_TOAD_4_JUMP_1_DESCRIPTION"] = "Tous les %$towers.hermit_toad.power_jump.cooldown[1]%$ secondes, l'ermite saute haut dans le ciel, s'écrasant sur les ennemis, infligeant %$towers.hermit_toad.power_jump.damage_min[1]%$ dégâts et les étourdissant pendant %$towers.hermit_toad.power_jump.stun_duration[1]%$ secondes à l'atterrissage.",
["TOWER_HERMIT_TOAD_4_JUMP_1_NAME"] = "Pilonneur",
["TOWER_HERMIT_TOAD_4_NAME"] = "Ermite des Tourbières IV",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_DESCRIPTION"] = "Tous les %$towers.hermit_toad.power_instakill.cooldown[1]%$ secondes, il utilise sa langue pour dévorer un ennemi.",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_1_NAME"] = "Langue Collante I",
["TOWER_HERMIT_TOAD_4_SKILL_INSTAKILL_NOTE"] = "Affaire collante.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_DESCRIPTION"] = "Tous les %$towers.hermit_toad.power_jump.cooldown[1]%$ secondes, l'ermite saute haut dans le ciel, s'écrasant sur les ennemis, infligeant %$towers.hermit_toad.power_jump.damage_min[1]%$ dégâts et les étourdissant pendant %$towers.hermit_toad.power_jump.stun_duration[1]%$ secondes à l'atterrissage.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_1_NAME"] = "Pilonneur I",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_DESCRIPTION"] = "Tous les %$towers.hermit_toad.power_jump.cooldown[2]%$ secondes, l'ermite saute haut dans le ciel, s'écrasant sur les ennemis, infligeant %$towers.hermit_toad.power_jump.damage_min[2]%$ dégâts et les étourdissant pendant %$towers.hermit_toad.power_jump.stun_duration[2]%$ secondes à l'atterrissage.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_2_NAME"] = "Pilonneur II",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_DESCRIPTION"] = "Tous les %$towers.hermit_toad.power_jump.cooldown[3]%$ secondes, l'ermite saute haut dans le ciel, s'écrasant sur les ennemis, infligeant %$towers.hermit_toad.power_jump.damage_min[3]%$ dégâts et les étourdissant pendant %$towers.hermit_toad.power_jump.stun_duration[3]%$ secondes à l'atterrissage.",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_3_NAME"] = "Pilonneur III",
["TOWER_HERMIT_TOAD_4_SKILL_JUMP_NOTE"] = "Prêt pour l'équipe de volley-ball de marais",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_DESCRIPTION"] = "L'ermite adopte une stance physique.",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NAME"] = "Marais ordinaire",
["TOWER_HERMIT_TOAD_CHANGE_MODE_ENGINEER_NOTE"] = "Se salir !",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_DESCRIPTION"] = "L'ermite adopte une stance magique.",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NAME"] = "Étang magique",
["TOWER_HERMIT_TOAD_CHANGE_MODE_MAGE_NOTE"] = "Pouvoir Illimité !!",
["TOWER_HERMIT_TOAD_DESC"] = "Un mage crapaud géant doué pour cracher des boules de mucus. Tout ce qu'il veut, c'est un peu de paix et de tranquillité pour ses bains d'étang. Ne le dérangez pas.",
["TOWER_HERMIT_TOAD_NAME"] = "Ermite des Tourbières",
["TOWER_NECROMANCER_1_DESCRIPTION"] = "Avec leur maîtrise de la mort, les Nécromanciens récoltent le chaos qu’ils sèment sur le champ de bataille.",
["TOWER_NECROMANCER_1_NAME"] = "Nécromancien I",
["TOWER_NECROMANCER_2_DESCRIPTION"] = "Avec leur maîtrise de la mort, les Nécromanciens récoltent le chaos qu’ils sèment sur le champ de bataille.",
["TOWER_NECROMANCER_2_NAME"] = "Nécromancien II",
["TOWER_NECROMANCER_3_DESCRIPTION"] = "Avec leur maîtrise de la mort, les Nécromanciens récoltent le chaos qu’ils sèment sur le champ de bataille.",
["TOWER_NECROMANCER_3_NAME"] = "Nécromancien III",
["TOWER_NECROMANCER_4_DESCRIPTION"] = "Avec leur maîtrise de la mort, les Nécromanciens récoltent le chaos qu’ils sèment sur le champ de bataille.",
["TOWER_NECROMANCER_4_NAME"] = "Nécromancien IV",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_DESCRIPTION"] = "Place un totem qui dure %$towers.necromancer.skill_debuff.aura_duration[1]%$ secondes, maudissant les ennemis et donnant aux squelettes %$towers.necromancer.skill_debuff.s_damage_factor[1]%$% de dégâts d'attaque supplémentaires.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_1_NAME"] = "Balise Sonore",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_DESCRIPTION"] = "Le totem donne aux squelettes %$towers.necromancer.skill_debuff.s_damage_factor[2]%$% de dégâts d'attaque supplémentaires. Le temps de recharge est réduit à %$towers.necromancer.skill_debuff.cooldown[2]%$ secondes.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_2_NAME"] = "Balise Sonore",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_DESCRIPTION"] = "Le totem donne aux squelettes %$towers.necromancer.skill_debuff.s_damage_factor[3]%$% de dégâts d'attaque supplémentaires. Le temps de recharge est réduit à %$towers.necromancer.skill_debuff.cooldown[3]%$ secondes.",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_3_NAME"] = "Balise Sonore",
["TOWER_NECROMANCER_4_SKILL_DEBUFF_NOTE"] = "Armée aux os solides!",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_DESCRIPTION"] = "Invoque un Chevalier de la Mort sur le chemin qui inflige %$towers.necromancer.skill_rider.s_damage[1]%$ de dégâts réels à tout ennemi qu'il traverse.",
["TOWER_NECROMANCER_4_SKILL_RIDER_1_NAME"] = "CAVALIER DE LA MORT",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_DESCRIPTION"] = "Le Cavalier de la Mort inflige %$towers.necromancer.skill_rider.s_damage[2]%$ de dégâts réels.",
["TOWER_NECROMANCER_4_SKILL_RIDER_2_NAME"] = "CAVALIER DE LA MORT",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_DESCRIPTION"] = "Le Cavalier de la Mort inflige %$towers.necromancer.skill_rider.s_damage[3]%$ de dégâts réels.",
["TOWER_NECROMANCER_4_SKILL_RIDER_3_NAME"] = "CAVALIER DE LA MORT",
["TOWER_NECROMANCER_4_SKILL_RIDER_NOTE"] = "Un aller simple...",
["TOWER_NECROMANCER_DESC"] = "Maniant la forme la plus sombre de magie, les Nécromanciens utilisent leurs ennemis comme une partie des rangs d'une armée sans fin.",
["TOWER_NECROMANCER_NAME"] = "Nécromancien",
["TOWER_PALADIN_COVENANT_1_DESCRIPTION"] = "Féroces et dédiés, les paladins travaillent dur pour protéger le royaume du danger.",
["TOWER_PALADIN_COVENANT_1_NAME"] = "Convenant des Paladins I",
["TOWER_PALADIN_COVENANT_2_DESCRIPTION"] = "Féroces et dédiés, les paladins travaillent dur pour protéger le royaume du danger.",
["TOWER_PALADIN_COVENANT_2_NAME"] = "Convenant des Paladins II",
["TOWER_PALADIN_COVENANT_3_DESCRIPTION"] = "Féroces et dédiés, les paladins travaillent dur pour protéger le royaume du danger.",
["TOWER_PALADIN_COVENANT_3_NAME"] = "Convenant des Paladins III",
["TOWER_PALADIN_COVENANT_4_DESCRIPTION"] = "Féroces et dédiés, les paladins travaillent dur pour protéger le royaume du danger.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_DESCRIPTION"] = "Quand les soldats atteignent %$towers.paladin_covenant.healing_prayer.health_trigger_factor[1]%$% de leur santé, ils deviennent invincibles et restaurent %$towers.paladin_covenant.healing_prayer.s_healing[1]%$ santé par seconde pendant %$towers.paladin_covenant.healing_prayer.duration%$ secondes.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_1_NAME"] = "PRIÈRE DE GUÉRISON",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_DESCRIPTION"] = "La guérison augmente de %$towers.paladin_covenant.healing_prayer.s_healing[2]%$ points de santé par seconde pendant %$towers.paladin_covenant.healing_prayer.duration%$ secondes.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_2_NAME"] = "PRIÈRE DE GUÉRISON",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_DESCRIPTION"] = "La guérison augmente de %$towers.paladin_covenant.healing_prayer.s_healing[3]%$ points de santé par seconde pendant %$towers.paladin_covenant.healing_prayer.duration%$ secondes.",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_3_NAME"] = "PRIÈRE DE GUÉRISON",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NAME"] = "PRIÈRE DE GUÉRISON",
["TOWER_PALADIN_COVENANT_4_HEALING_PRAYER_NOTE"] = "Devoir jusqu'à la mort.",
["TOWER_PALADIN_COVENANT_4_LEAD_1_DESCRIPTION"] = "Remplace l'un des paladins par un Vétéran de la Garde, ce qui donne aux alliés à proximité un bonus de %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$% de dégâts d'attaque.",
["TOWER_PALADIN_COVENANT_4_LEAD_1_NAME"] = "MENER PAR L'EXEMPLE",
["TOWER_PALADIN_COVENANT_4_LEAD_2_DESCRIPTION"] = "Remplace l'un des paladins par un Vétéran de la Garde, ce qui donne aux alliés à proximité un bonus de %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$% de dégâts d'attaque.",
["TOWER_PALADIN_COVENANT_4_LEAD_2_NAME"] = "MENER PAR L'EXEMPLE",
["TOWER_PALADIN_COVENANT_4_LEAD_3_DESCRIPTION"] = "Remplace l'un des paladins par un Vétéran de la Garde, ce qui donne aux alliés à proximité un bonus de %$towers.paladin_covenant.lead.soldier_veteran.s_aura_damage_buff_factor%$% de dégâts d'attaque.",
["TOWER_PALADIN_COVENANT_4_LEAD_3_NAME"] = "MENER PAR L'EXEMPLE",
["TOWER_PALADIN_COVENANT_4_LEAD_NAME"] = "MENER PAR L'EXEMPLE",
["TOWER_PALADIN_COVENANT_4_LEAD_NOTE"] = "Pour le roi, pour la terre, pour les montagnes.",
["TOWER_PALADIN_COVENANT_4_NAME"] = "Convenant des Paladins IV",
["TOWER_PALADIN_COVENANT_DESC"] = "Les paladins sont l'épine dorsale des forces d'élite de Linirea, utilisant leur pouvoir divin pour se protéger et se soigner au combat.",
["TOWER_PALADIN_COVENANT_NAME"] = "Convenant des Paladins",
["TOWER_PANDAS_1_DESCRIPTION"] = "Armés de maîtrise élémentaire et d'une détermination sans faille, les Maîtres se battent sans relâche pour préserver l'équilibre naturel du monde.",
["TOWER_PANDAS_1_NAME"] = "Bambous Maîtres I",
["TOWER_PANDAS_2_DESCRIPTION"] = "Armés de maîtrise élémentaire et d'une détermination sans faille, les Maîtres se battent sans relâche pour préserver l'équilibre naturel du monde.",
["TOWER_PANDAS_2_NAME"] = "Bambous Maîtres II",
["TOWER_PANDAS_3_DESCRIPTION"] = "Armés de maîtrise élémentaire et d'une détermination sans faille, les Maîtres se battent sans relâche pour préserver l'équilibre naturel du monde.",
["TOWER_PANDAS_3_NAME"] = "Bambous Maîtres III",
["TOWER_PANDAS_4_DESCRIPTION"] = "Armés de maîtrise élémentaire et d'une détermination sans faille, les Maîtres se battent sans relâche pour préserver l'équilibre naturel du monde.",
["TOWER_PANDAS_4_FIERY"] = "Kawoosh",
["TOWER_PANDAS_4_FIERY_1_DESCRIPTION"] = "Lance un trait de feu infligeant %$towers.pandas.soldier.teleport.damage_min[1]%$-%$towers.pandas.soldier.teleport.damage_max[1]%$ de dégâts véritables et téléporte les ennemis touchés en arrière sur le chemin.",
["TOWER_PANDAS_4_FIERY_1_NAME"] = "Flamme du Néant",
["TOWER_PANDAS_4_FIERY_2_DESCRIPTION"] = "Cast une boule de feu infligeant %$towers.pandas.soldier.teleport.damage_min[2]%$-%$towers.pandas.soldier.teleport.damage_max[2]%$ de dégâts véritables et téléportant les ennemis touchés en arrière sur le chemin.",
["TOWER_PANDAS_4_FIERY_2_NAME"] = "Flamme du Néant",
["TOWER_PANDAS_4_HAT"] = "Un chapeau pour tous les frapper",
["TOWER_PANDAS_4_HAT_1_DESCRIPTION"] = "Lance son chapeau aiguisé sur un ennemi, ricochant entre les ennemis pour infliger %$towers.pandas.soldier.hat.damage_levels[1].min%$-%$towers.pandas.soldier.hat.damage_levels[1].max%$ de dégâts à chaque rebond.",
["TOWER_PANDAS_4_HAT_1_NAME"] = "Tour de chapeau",
["TOWER_PANDAS_4_HAT_2_DESCRIPTION"] = "Lance son chapeau aiguisé sur un ennemi, ricochant entre les ennemis pour infliger %$towers.pandas.soldier.hat.damage_levels[2].min%$-%$towers.pandas.soldier.hat.damage_levels[2].max%$ de dégâts à chaque rebond.",
["TOWER_PANDAS_4_HAT_2_NAME"] = "Tour de chapeau",
["TOWER_PANDAS_4_NAME"] = "Bambous Maîtres IV",
["TOWER_PANDAS_4_THUNDER"] = "Combat Panda",
["TOWER_PANDAS_4_THUNDER_1_DESCRIPTION"] = "Appelle des éclairs sur une petite zone, chacun infligeant %$towers.pandas.soldier.thunder.damage_min[1]%$-%$towers.pandas.soldier.thunder.damage_max[1]%$ de dégâts de zone et étourdissant brièvement les ennemis touchés.",
["TOWER_PANDAS_4_THUNDER_1_NAME"] = "Surcharge Foudroyante",
["TOWER_PANDAS_4_THUNDER_2_DESCRIPTION"] = "Appelle des éclairs sur une petite zone, chacun infligeant %$towers.pandas.soldier.thunder.damage_min[2]%$-%$towers.pandas.soldier.thunder.damage_max[2]%$ de dégâts de zone et étourdissant brièvement les ennemis touchés.",
["TOWER_PANDAS_4_THUNDER_2_NAME"] = "Surcharge de foudre",
["TOWER_PANDAS_DESC"] = "Alliant prouesse martiale et affinité élémentaire, ce trio de pandas traverse les ennemis et reste une menace même lorsqu'il semble vaincu.",
["TOWER_PANDAS_NAME"] = "Maîtres du Bambou",
["TOWER_PANDAS_RETREAT_DESCRIPTION"] = "Retraitez les pandas debout vers le refuge pendant 8 secondes.",
["TOWER_PANDAS_RETREAT_NAME"] = "Retraite Tactique",
["TOWER_PANDAS_RETREAT_NOTE"] = "La discrétion est la meilleure part de la vaillance.",
["TOWER_RAY_1_DESCRIPTION"] = "Les formes dangereuses et corrompues de magie n'ont jamais arrêté les mages maléfiques de poursuivre des desseins néfastes.",
["TOWER_RAY_1_NAME"] = "Canalisateur Étrange I",
["TOWER_RAY_2_DESCRIPTION"] = "Les formes dangereuses et corrompues de magie n'ont jamais arrêté les mages maléfiques de poursuivre des desseins néfastes.",
["TOWER_RAY_2_NAME"] = "Canalisateur Étrange II",
["TOWER_RAY_3_DESCRIPTION"] = "Les formes dangereuses et corrompues de magie n'ont jamais arrêté les mages maléfiques de poursuivre des desseins néfastes.",
["TOWER_RAY_3_NAME"] = "Canalisateur Étrange III",
["TOWER_RAY_4_CHAIN_1_DESCRIPTION"] = "Le rayon magique s'étend désormais à %$towers.ray.skill_chain.s_max_enemies%$ ennemis supplémentaires, les ralentissant et infligeant %$towers.ray.skill_chain.damage_mult[1]%$% des dégâts magiques totaux à chaque cible.",
["TOWER_RAY_4_CHAIN_1_NAME"] = "SURCHARGE DE PUISSANCE",
["TOWER_RAY_4_CHAIN_2_DESCRIPTION"] = "Le rayon magique s'étend désormais à %$towers.ray.skill_chain.s_max_enemies%$ ennemis supplémentaires, les ralentissant et infligeant %$towers.ray.skill_chain.damage_mult[2]%$% des dégâts magiques totaux à chaque cible.",
["TOWER_RAY_4_CHAIN_2_NAME"] = "SURCHARGE DE PUISSANCE",
["TOWER_RAY_4_CHAIN_3_DESCRIPTION"] = "Le rayon magique s'étend désormais à %$towers.ray.skill_chain.s_max_enemies%$ ennemis supplémentaires, les ralentissant et infligeant %$towers.ray.skill_chain.damage_mult[3]%$% des dégâts magiques totaux à chaque cible.",
["TOWER_RAY_4_CHAIN_3_NAME"] = "Surcharge de puissance",
["TOWER_RAY_4_CHAIN_NOTE"] = "Il y a assez de douleur pour tout le monde.",
["TOWER_RAY_4_DESCRIPTION"] = "Les formes dangereuses et corrompues de magie n'ont jamais arrêté les mages maléfiques de poursuivre des desseins néfastes.",
["TOWER_RAY_4_NAME"] = "Canalisateur Étrange IV",
["TOWER_RAY_4_SHEEP_1_DESCRIPTION"] = "Transforme un ennemi proche en un mouton sans défense. Le mouton possède %$towers.ray.skill_sheep.sheep.hp_mult%$% de la santé de la cible.",
["TOWER_RAY_4_SHEEP_1_NAME"] = "MALÉDICTION DES MUTATIONS",
["TOWER_RAY_4_SHEEP_2_DESCRIPTION"] = "Transforme un ennemi proche en mouton sans défense. Le mouton a %$towers.ray.skill_sheep.sheep.hp_mult%$% de la santé de la cible.",
["TOWER_RAY_4_SHEEP_2_NAME"] = "MALÉDICTION DES MUTATIONS",
["TOWER_RAY_4_SHEEP_3_DESCRIPTION"] = "Transforme un ennemi proche en mouton sans défense. Le mouton a %$towers.ray.skill_sheep.sheep.hp_mult%$% de la santé de la cible.",
["TOWER_RAY_4_SHEEP_3_NAME"] = "MALÉDICTION DES MUTATIONS",
["TOWER_RAY_4_SHEEP_NOTE"] = "Honnêtement, tu as l'air mieux maintenant.",
["TOWER_RAY_DESC"] = "Les apprentis de Vez'nan utilisent leur pouvoir corrompu pour lancer un rayon sombre d'affliction sur leurs ennemis.",
["TOWER_RAY_NAME"] = "Canalisateur Étrange",
["TOWER_ROCKET_GUNNERS_1_DESCRIPTION"] = "Équipés avec la dernière technologie de l’Armée Noire, les artilleurs patrouillent les cieux.",
["TOWER_ROCKET_GUNNERS_1_NAME"] = "Artilleurs de Fusées I",
["TOWER_ROCKET_GUNNERS_2_DESCRIPTION"] = "Équipés avec la dernière technologie de l’Armée Noire, les artilleurs patrouillent les cieux.",
["TOWER_ROCKET_GUNNERS_2_NAME"] = "Artilleurs de Fusées II",
["TOWER_ROCKET_GUNNERS_3_DESCRIPTION"] = "Équipés avec la dernière technologie de l’Armée Noire, les artilleurs patrouillent les cieux.",
["TOWER_ROCKET_GUNNERS_3_NAME"] = "Artilleurs de Fusées III",
["TOWER_ROCKET_GUNNERS_4_DESCRIPTION"] = "Équipés avec la dernière technologie de l’Armée Noire, les artilleurs patrouillent les cieux.",
["TOWER_ROCKET_GUNNERS_4_NAME"] = "Artilleurs de Fusées IV",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_DESCRIPTION"] = "Chaque attaque détruit %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[1]%$% de l'armure ennemie et inflige des dégâts de zone.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_1_NAME"] = "REVÊTEMENT PHOSPHORIQUE",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_DESCRIPTION"] = "Chaque attaque détruit %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[2]%$% de l'armure ennemie et inflige %$towers.rocket_gunners.soldier.phosphoric.damage_area_min[2]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[2]%$ de dégâts de zone.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_2_NAME"] = "REVÊTEMENT PHOSPHORIQUE",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_DESCRIPTION"] = "Chaque attaque détruit %$towers.rocket_gunners.soldier.phosphoric.armor_reduction[3]%$% de l'armure ennemie et inflige %$towers.rocket_gunners.soldier.phosphoric.damage_area_min[3]%$-%$towers.rocket_gunners.soldier.phosphoric.damage_area_max[3]%$ de dégâts de zone.",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_3_NAME"] = "REVÊTEMENT PHOSPHORIQUE",
["TOWER_ROCKET_GUNNERS_4_PHOSPHORIC_NOTE"] = "Balles assaisonnées diaboliquement.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_DESCRIPTION"] = "Tire un missile qui tue instantanément une cible ayant jusqu'à %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[1]%$ de santé.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_1_NAME"] = "MISSILES STING",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_DESCRIPTION"] = "Réduit le délai de récupération à %$towers.rocket_gunners.sting_missiles.cooldown[2]%$ secondes. Peut désormais cibler des ennemis ayant jusqu'à %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[2]%$ de santé.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_2_NAME"] = "MISSILES STING",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_DESCRIPTION"] = "Réduit le temps de recharge à %$towers.rocket_gunners.sting_missiles.cooldown[3]%$ secondes. Il peut maintenant cibler des ennemis ayant jusqu'à %$towers.rocket_gunners.soldier.sting_missiles.hp_max_target[3]%$ de santé.",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_3_NAME"] = "MISSILES STING",
["TOWER_ROCKET_GUNNERS_4_STING_MISSILES_NOTE"] = "Esquive ça !",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_DESCRIPTION"] = "Les artilleurs de roquettes décollent et ne peuvent pas bloquer les ennemis.",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NAME"] = "Décollage",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_FLY_NOTE"] = "Vers l'infini et au-delà !",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_DESCRIPTION"] = "Les artilleurs de roquettes atterrissent au sol et peuvent bloquer les ennemis.",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NAME"] = "Atterrir",
["TOWER_ROCKET_GUNNERS_CHANGE_MODE_GROUND_NOTE"] = "L'aigle a atterri !",
["TOWER_ROCKET_GUNNERS_DESC"] = "Ces troupes spéciales peuvent tenir leur position aussi bien au sol qu'en l'air, déchaînant leur armement avancé sur des ennemis imprévus.",
["TOWER_ROCKET_GUNNERS_NAME"] = "Artilleurs de Fusées",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_1"] = "Cette tour est incluse dans la Campagne de la Menace Colossale",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_DESC_KR5_DLC_2"] = "Cette tour est incluse dans la campagne Le Voyage de Wukong.",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_1"] = "Campagne de la Menace Colossale",
["TOWER_ROOM_DLC_BADGE_TOOLTIP_TITLE_KR5_DLC_2"] = "Campagne Le Voyage de Wukong",
["TOWER_ROOM_EQUIPPED_TOWERS_TITLE"] = "tours équipées",
["TOWER_ROOM_GET_DLC"] = "PRENEZ-LE",
["TOWER_ROOM_LABEL_ROSTER_THUMB_NEW"] = "Nouveau!",
["TOWER_ROOM_SKILLS_TITLE"] = "Compétences",
["TOWER_ROYAL_ARCHERS_1_DESCRIPTION"] = "Fidèles jusqu'à la toute fin, les Archers Royaux protègent les forces de Linirea à distance. ",
["TOWER_ROYAL_ARCHERS_1_NAME"] = "Archers Royaux I ",
["TOWER_ROYAL_ARCHERS_2_DESCRIPTION"] = "Fidèles jusqu'à la toute fin, les Archers Royaux protègent les forces de Linirea à distance. ",
["TOWER_ROYAL_ARCHERS_2_NAME"] = "Archers Royaux II ",
["TOWER_ROYAL_ARCHERS_3_DESCRIPTION"] = "Fidèles jusqu'à la toute fin, les Archers Royaux protègent les forces de Linirea à distance. ",
["TOWER_ROYAL_ARCHERS_3_NAME"] = "Archers Royaux III ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_DESCRIPTION"] = "Tire trois flèches améliorées qui infligent %$towers.royal_archers.armor_piercer.damage_min[1]%$-%$towers.royal_archers.armor_piercer.damage_max[1]%$ de dégâts physiques, ignorant %$towers.royal_archers.armor_piercer.armor_penetration[1]%$% de l'armure ennemie. ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_1_NAME"] = "PERCE-ARMURE ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_DESCRIPTION"] = "Tire trois flèches surpuissantes qui infligent de %$towers.royal_archers.armor_piercer.damage_min[2]%$ à %$towers.royal_archers.armor_piercer.damage_max[2]%$ de dégâts physiques, ignorant %$towers.royal_archers.armor_piercer.armor_penetration[2]%$% de l'armure ennemie. ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_2_NAME"] = "PERCE-ARMURE ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_DESCRIPTION"] = "Tire trois flèches surpuissantes qui infligent de %$towers.royal_archers.armor_piercer.damage_min[3]%$ à %$towers.royal_archers.armor_piercer.damage_max[3]%$ de dégâts physiques, ignorant %$towers.royal_archers.armor_piercer.armor_penetration[3]%$% de l'armure ennemie. ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_3_NAME"] = "PERCE-ARMURE ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NAME"] = "PERCE-ARMURE ",
["TOWER_ROYAL_ARCHERS_4_ARMOR_PIERCER_NOTE"] = "On vous a dans notre viseur. ",
["TOWER_ROYAL_ARCHERS_4_DESCRIPTION"] = "Fidèles jusqu'à la toute fin, les Archers Royaux protègent les forces de Linirea à distance. ",
["TOWER_ROYAL_ARCHERS_4_NAME"] = "Archers Royaux IV ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_DESCRIPTION"] = "Invoque un aigle qui attaque les ennemis sur le chemin, infligeant entre %$towers.royal_archers.rapacious_hunter.damage_min[1]%$ et %$towers.royal_archers.rapacious_hunter.damage_max[1]%$ de dégâts physiques.",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_1_NAME"] = "CHASSEUR RAPACE ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_DESCRIPTION"] = "L'aigle inflige de %$towers.royal_archers.rapacious_hunter.damage_min[2]%$ à %$towers.royal_archers.rapacious_hunter.damage_max[2]%$ de dégâts physiques. ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_2_NAME"] = "CHASSEUR RAPACE ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_DESCRIPTION"] = "L'aigle inflige de %$towers.royal_archers.rapacious_hunter.damage_min[3]%$ à %$towers.royal_archers.rapacious_hunter.damage_max[3]%$ de dégâts physiques. ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_3_NAME"] = "CHASSEUR RAPACE ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NAME"] = "CHASSEUR RAPACE ",
["TOWER_ROYAL_ARCHERS_4_RAPACIOUS_HUNTER_NOTE"] = "L'œil de l'aigle cache quelque chose de tragique. ",
["TOWER_ROYAL_ARCHERS_DESC"] = "Les tireurs les plus puissants du royaume, ils sont également réputés pour être assistés par des aigles de guerre. ",
["TOWER_ROYAL_ARCHERS_NAME"] = "Archers Royaux ",
["TOWER_SAND_1_DESCRIPTION"] = "Leur habileté avec la lame lancée est suffisante pour effrayer n'importe quel mercenaire trop plein de lui-même.",
["TOWER_SAND_1_NAME"] = "Sentinelles des Dunes I",
["TOWER_SAND_2_DESCRIPTION"] = "Leur habileté avec la lame lancée est suffisante pour effrayer n'importe quel mercenaire trop plein de lui-même.",
["TOWER_SAND_2_NAME"] = "Sentinelles des Dunes II",
["TOWER_SAND_3_DESCRIPTION"] = "Leur habileté avec la lame lancée est suffisante pour effrayer tout mercenaire trop plein de lui-même.",
["TOWER_SAND_3_NAME"] = "Sentinelles des Dunes III",
["TOWER_SAND_4_DESCRIPTION"] = "Leur habileté avec la lame lancée est suffisante pour effrayer tout mercenaire trop plein de lui-même.",
["TOWER_SAND_4_NAME"] = "Sentinelles des Dunes IV",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_DESCRIPTION"] = "Lance des lames tourbillonnantes sur le chemin qui infligent %$towers.sand.skill_big_blade.s_damage_min[1]%$-%$towers.sand.skill_big_blade.s_damage_max[1]%$ de dégâts physiques par seconde pendant %$towers.sand.skill_big_blade.duration[1]%$ secondes.",
["TOWER_SAND_4_SKILL_BIG_BLADE_1_NAME"] = "TOURBILLON DE DOOM",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_DESCRIPTION"] = "Les lames tournoyantes infligent %$towers.sand.skill_big_blade.s_damage_min[2]%$-%$towers.sand.skill_big_blade.s_damage_max[2]%$ de dégâts physiques par seconde pendant %$towers.sand.skill_big_blade.duration[2]%$ secondes.",
["TOWER_SAND_4_SKILL_BIG_BLADE_2_NAME"] = "TOURBILLON DU CHAOS",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_DESCRIPTION"] = "Les lames tourbillonnantes infligent de %$towers.sand.skill_big_blade.s_damage_min[3]%$ à %$towers.sand.skill_big_blade.s_damage_max[3]%$ de dégâts physiques par seconde pendant %$towers.sand.skill_big_blade.duration[3]%$ secondes.",
["TOWER_SAND_4_SKILL_BIG_BLADE_3_NAME"] = "TOURBILLON DU CHAOS",
["TOWER_SAND_4_SKILL_BIG_BLADE_NOTE"] = "Tu me fais tourner, tourner, bébé.",
["TOWER_SAND_4_SKILL_GOLD_1_DESCRIPTION"] = "Lance une lame rebondissante qui inflige %$towers.sand.skill_gold.s_damage[1]%$ de dégâts physiques aux ennemis ciblés. Tout ennemi tué par la lame rapporte %$towers.sand.skill_gold.gold_extra[1]%$ d'or en bonus. ",
["TOWER_SAND_4_SKILL_GOLD_1_NAME"] = "CHASSEUR DE PRIMES",
["TOWER_SAND_4_SKILL_GOLD_2_DESCRIPTION"] = "La lame inflige %$towers.sand.skill_gold.s_damage[2]%$ de dégâts physiques. Un kill accorde %$towers.sand.skill_gold.gold_extra[2]%$ d'or bonus. ",
["TOWER_SAND_4_SKILL_GOLD_2_NAME"] = "CHASSEUR DE PRIMES",
["TOWER_SAND_4_SKILL_GOLD_3_DESCRIPTION"] = "L'épée inflige %$towers.sand.skill_gold.s_damage[3]%$ de dégâts physiques. Un meurtre accorde %$towers.sand.skill_gold.gold_extra[3]%$ d'or bonus.",
["TOWER_SAND_4_SKILL_GOLD_3_NAME"] = "CHASSEUR DE PRIMES",
["TOWER_SAND_4_SKILL_GOLD_NOTE"] = "Le dépliant dit mort OU vivant.",
["TOWER_SAND_DESC"] = "Originaires de Hammerhold, les Sentinelles des Dunes pourraient être les habitants les plus mortels du désert.",
["TOWER_SAND_NAME"] = "Sentinelles des Dunes",
["TOWER_SELL"] = "Vendre tour",
["TOWER_SPARKING_GEODE_1_DESCRIPTION"] = "Invocateur de tempêtes et fauteur de chaos certifié. Attention à sa consommation d’énergie.",
["TOWER_SPARKING_GEODE_1_NAME"] = "Colosse Orageux I",
["TOWER_SPARKING_GEODE_2_DESCRIPTION"] = "Colosse Orageux III",
["TOWER_SPARKING_GEODE_2_NAME"] = "Colosse Orageux II",
["TOWER_SPARKING_GEODE_3_DESCRIPTION"] = "Cristallisation I",
["TOWER_SPARKING_GEODE_3_NAME"] = "Colosse Orageux IV",
["TOWER_SPARKING_GEODE_4_CRISTALIZE"] = "Éclair !",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_DESCRIPTION"] = "Toutes les %$towers.sparking_geode.crystalize.cooldown[1]%$ secondes, il cristallise %$towers.sparking_geode.crystalize.max_targets[1]%$ ennemis à portée, les étourdissant et leur infligeant %$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$% de dégâts supplémentaires.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_1_NAME"] = "Crystalisation",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_DESCRIPTION"] = "Toutes les %$towers.sparking_geode.crystalize.cooldown[2]%$ secondes, elle cristallise %$towers.sparking_geode.crystalize.max_targets[2]%$ ennemis à portée, les étourdissant et leur faisant subir %$towers.sparking_geode.crystalize.s_received_damage_factor[2]%$% de dégâts supplémentaires.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_2_NAME"] = "Cristallisation",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_DESCRIPTION"] = "Toutes les %$towers.sparking_geode.crystalize.cooldown[3]%$ secondes, elle cristallise %$towers.sparking_geode.crystalize.max_targets[3]%$ ennemis à portée, les étourdissant et leur faisant subir %$towers.sparking_geode.crystalize.s_received_damage_factor[3]%$% de dégâts supplémentaires.",
["TOWER_SPARKING_GEODE_4_CRISTALIZE_3_NAME"] = "Cristallisation",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_DESCRIPTION"] = "Toutes les %$towers.sparking_geode.crystalize.cooldown[1]%$ secondes, il cristallise %$towers.sparking_geode.crystalize.max_targets[1]%$ ennemis à portée, les étourdissant et leur faisant subir %$towers.sparking_geode.crystalize.s_received_damage_factor[1]%$% de dégâts supplémentaires.",
["TOWER_SPARKING_GEODE_4_CRYSTALIZE_1_NAME"] = "Cristallisation",
["TOWER_SPARKING_GEODE_4_DESCRIPTION"] = "Invocateur de tempêtes et maître du chaos certifié. Faites attention à sa consommation d’énergie.",
["TOWER_SPARKING_GEODE_4_NAME"] = "Colosse de l’Onde IV",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST"] = "Plus dur, meilleur, plus rapide, plus fort.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_DESCRIPTION"] = "Toutes les %$towers.sparking_geode.spike_burst.cooldown[1]%$ secondes, le Colosse invoque un champ électrique qui endommage et ralentit les ennemis proches pendant %$towers.sparking_geode.spike_burst.duration[1]%$ secondes.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_1_NAME"] = "Surge Électrique",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_DESCRIPTION"] = "Toutes les %$towers.sparking_geode.spike_burst.cooldown[2]%$ secondes, le Colosse invoque un champ électrique qui endommage et ralentit les ennemis proches pendant %$towers.sparking_geode.spike_burst.duration[2]%$ secondes.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_2_NAME"] = "Surge Électrique",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_DESCRIPTION"] = "Toutes les %$towers.sparking_geode.spike_burst.cooldown[3]%$ secondes, le Colosse invoque un champ électrique qui endommage et ralentit les ennemis proches pendant %$towers.sparking_geode.spike_burst.duration[3]%$ secondes.",
["TOWER_SPARKING_GEODE_4_SPIKE_BURST_3_NAME"] = "Surge Électrique",
["TOWER_SPARKING_GEODE_DESC"] = "Issu d’une ancienne race pacifique, cet être puissant suit son instinct protecteur et utilise ses pouvoirs électriques pour combattre pour l’Alliance, frappant avec la fureur d’une tempête.",
["TOWER_SPARKING_GEODE_NAME"] = "Colosse Orageux",
["TOWER_STAGE_13_SUNRAY_NAME"] = "Tour Rayon Sombre",
["TOWER_STAGE_13_SUNRAY_REPAIR_DESCRIPTION"] = "Réparez la tour pour utiliser ses pouvoirs destructeurs.",
["TOWER_STAGE_13_SUNRAY_REPAIR_NAME"] = "Réparer",
["TOWER_STAGE_17_WEIRDWOOD_NAME"] = "Weirdwood",
["TOWER_STAGE_18_ELVEN_BARRACK_DESCRIPTION"] = "Des elfes engagés pour se battre jusqu'à la fin.",
["TOWER_STAGE_18_ELVEN_BARRACK_NAME"] = "Mercenaires elfes",
["TOWER_STAGE_20_ARBOREAN_BARRACK_DESCRIPTION"] = "Appelez le peuple arboreen à combattre.",
["TOWER_STAGE_20_ARBOREAN_BARRACK_NAME"] = "Citoyens Arboreens",
["TOWER_STAGE_20_ARBOREAN_HONEY_DESCRIPTION"] = "Invoquez le grand commandant des abeilles.",
["TOWER_STAGE_20_ARBOREAN_HONEY_NAME"] = "Apiculteur Arboreen",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_DESCRIPTION"] = "Demandez de l'aide à l'arbre ancien.",
["TOWER_STAGE_20_ARBOREAN_OLDTREE_NAME"] = "Vieil Arbre",
["TOWER_STAGE_22_ARBOREAN_MAGES_NAME"] = "Mage Arboreen",
["TOWER_STAGE_28_PRIESTS_BARRACK_DESCRIPTION"] = "Cultistes rachetés qui apportent leur sorcellerie sur le champ de bataille et se transforment en abominations à leur mort.",
["TOWER_STAGE_28_PRIESTS_BARRACK_NAME"] = "Croyants de l’Inexorable",
["TOWER_STARGAZER_1_DESCRIPTION"] = "Les Étoilevoyants cherchent à maîtriser une puissante magie venant d'au-delà du royaume terrestre.",
["TOWER_STARGAZER_1_NAME"] = "Étoilevoyant Elfe I",
["TOWER_STARGAZER_2_DESCRIPTION"] = "Les Étoilevoyants cherchent à maîtriser une puissante magie venant d'au-delà du royaume terrestre.",
["TOWER_STARGAZER_2_NAME"] = "Étoilevoyant Elfe II",
["TOWER_STARGAZER_3_DESCRIPTION"] = "Les Étoilevoyants cherchent à maîtriser une puissante magie venant d'au-delà du royaume terrestre.",
["TOWER_STARGAZER_3_NAME"] = "Étoilevoyant Elfe III",
["TOWER_STARGAZER_4_DESCRIPTION"] = "Les Étoilevoyants cherchent à maîtriser une puissante magie venant d'au-delà du royaume terrestre.",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_DESCRIPTION"] = "Téléporte jusqu'à %$towers.elven_stargazers.teleport.max_targets[1]%$ ennemis plus loin sur le chemin.",
["TOWER_STARGAZER_4_EVENT_HORIZON_1_NAME"] = "HORIZON DES ÉVÉNEMENTS",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_DESCRIPTION"] = "Téléporte jusqu'à %$towers.elven_stargazers.teleport.max_targets[2]%$ ennemis plus loin sur le chemin. ",
["TOWER_STARGAZER_4_EVENT_HORIZON_2_NAME"] = "HORIZON DES ÉVÉNEMENTS",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_DESCRIPTION"] = "Téléporte jusqu'à %$towers.elven_stargazers.teleport.max_targets[3]%$ ennemis encore plus loin en arrière sur le chemin.",
["TOWER_STARGAZER_4_EVENT_HORIZON_3_NAME"] = "HORIZON DES ÉVÉNEMENTS",
["TOWER_STARGAZER_4_EVENT_HORIZON_NAME"] = "HORIZON DES ÉVÉNEMENTS",
["TOWER_STARGAZER_4_EVENT_HORIZON_NOTE"] = "Se désintégrer, se matérialiser.",
["TOWER_STARGAZER_4_NAME"] = "Étoilevoyant Elfe IV",
["TOWER_STARGAZER_4_RISING_STAR_1_DESCRIPTION"] = "Les ennemis tués par la tour explosent dans une explosion de %$towers.elven_stargazers.stars_death.stars[1]%$ étoiles qui infligent %$towers.elven_stargazers.stars_death.damage_min[1]%$-%$towers.elven_stargazers.stars_death.damage_max[1]%$ dégâts magiques aux ennemis.",
["TOWER_STARGAZER_4_RISING_STAR_1_NAME"] = "ÉTOILE MONTANTE",
["TOWER_STARGAZER_4_RISING_STAR_2_DESCRIPTION"] = "La quantité d'étoiles augmente à %$towers.elven_stargazers.stars_death.stars[2]%$. Les étoiles infligent de %$towers.elven_stargazers.stars_death.damage_min[2]%$-%$towers.elven_stargazers.stars_death.damage_max[2]%$ de dégâts magiques .",
["TOWER_STARGAZER_4_RISING_STAR_2_NAME"] = "ÉTOILE MONTANTE",
["TOWER_STARGAZER_4_RISING_STAR_3_DESCRIPTION"] = "La quantité d'étoiles augmente à %$towers.elven_stargazers.stars_death.stars[3]%$. Les étoiles infligent de %$towers.elven_stargazers.stars_death.damage_min[3]%$-%$towers.elven_stargazers.stars_death.damage_max[3]%$ de dégâts magiques .",
["TOWER_STARGAZER_4_RISING_STAR_3_NAME"] = "ÉTOILE MONTANTE",
["TOWER_STARGAZER_4_RISING_STAR_NAME"] = "ÉTOILE MONTANTE",
["TOWER_STARGAZER_4_RISING_STAR_NOTE"] = "C'est une révolution de poussière d'étoile!",
["TOWER_TRICANNON_1_DESCRIPTION"] = "Une chanson d'amour dévastatrice pour la guerre et un spectacle effrayant pour les ennemis et les alliés tout autant.",
["TOWER_TRICANNON_1_NAME"] = "Tricannon I",
["TOWER_TRICANNON_2_DESCRIPTION"] = "Une chanson d'amour dévastatrice pour la guerre et un spectacle effrayant pour les ennemis et les alliés tout autant.",
["TOWER_TRICANNON_2_NAME"] = "Tricannon II",
["TOWER_TRICANNON_3_DESCRIPTION"] = "Une chanson d'amour dévastatrice pour la guerre et un spectacle effrayant pour les ennemis et les alliés tout autant.",
["TOWER_TRICANNON_3_NAME"] = "Tricannon III",
["TOWER_TRICANNON_4_BOMBARDMENT_1_DESCRIPTION"] = "Tire des bombes rapidement sur une large zone, chacune infligeant %$towers.tricannon.bombardment.damage_min[1]%$-%$towers.tricannon.bombardment.damage_max[1]%$ de dégâts physiques.",
["TOWER_TRICANNON_4_BOMBARDMENT_1_NAME"] = "BOMBARDEMENT",
["TOWER_TRICANNON_4_BOMBARDMENT_2_DESCRIPTION"] = "Lance plus de bombes sur une zone plus large, chacune infligeant %$towers.tricannon.bombardment.damage_min[2]%$-%$towers.tricannon.bombardment.damage_max[2]%$ de dégâts physiques",
["TOWER_TRICANNON_4_BOMBARDMENT_2_NAME"] = "BOMBARDEMENT",
["TOWER_TRICANNON_4_BOMBARDMENT_3_DESCRIPTION"] = "Lance encore plus de bombes dans une zone plus large, chacune causant %$towers.tricannon.bombardment.damage_min[3]%$-%$towers.tricannon.bombardment.damage_max[3]%$ dégâts physiques",
["TOWER_TRICANNON_4_BOMBARDMENT_3_NAME"] = "BOMBARDEMENT",
["TOWER_TRICANNON_4_BOMBARDMENT_NAME"] = "BOMBARDEMENT",
["TOWER_TRICANNON_4_BOMBARDMENT_NOTE"] = "Parlons d'évolutivité.",
["TOWER_TRICANNON_4_DESCRIPTION"] = "Une chanson d'amour dévastatrice pour la guerre et un spectacle effrayant pour les ennemis et les alliés tout autant.",
["TOWER_TRICANNON_4_NAME"] = "Tricannon IV",
["TOWER_TRICANNON_4_OVERHEAT_1_DESCRIPTION"] = "Les canons du Tricannon deviennent brûlants pendant %$towers.tricannon.overheat.duration[1]%$ secondes, faisant que les bombes embrasent le sol pour infliger %$towers.tricannon.overheat.decal.effect.s_damage[1]%$ dégâts réels par seconde aux ennemis.",
["TOWER_TRICANNON_4_OVERHEAT_1_NAME"] = "SURCHAUFFE",
["TOWER_TRICANNON_4_OVERHEAT_2_DESCRIPTION"] = "Chaque zone brûlante inflige %$towers.tricannon.overheat.decal.effect.s_damage[2]%$ de dégâts réels par seconde. La durée est augmentée à %$towers.tricannon.overheat.duration[2]%$ secondes.",
["TOWER_TRICANNON_4_OVERHEAT_2_NAME"] = "SURCHAUFFE",
["TOWER_TRICANNON_4_OVERHEAT_3_DESCRIPTION"] = "Chaque zone brûlante inflige %$towers.tricannon.overheat.decal.effect.s_damage[3]%$ de dégâts réels par seconde. La durée est augmentée à %$towers.tricannon.overheat.duration[3]%$ secondes.",
["TOWER_TRICANNON_4_OVERHEAT_3_NAME"] = "SURCHAUFFE",
["TOWER_TRICANNON_4_OVERHEAT_NAME"] = "SURCHAUFFE",
["TOWER_TRICANNON_4_OVERHEAT_NOTE"] = "Nous sommes surchauffés.",
["TOWER_TRICANNON_DESC"] = "L'armée des ténèbres apporte une nouvelle définition de la guerre moderne, faisant pleuvoir le feu et la destruction grâce à ses multiples canons.",
["TOWER_TRICANNON_NAME"] = "Tricannon",
["TUTORIAL_hero_room_hero_points_desc"] = "Gagnez des Points de Héros en faisant monter de niveau chaque héros au combat.",
["TUTORIAL_hero_room_hero_points_title"] = "Points de Héros",
["TUTORIAL_hero_room_power_desc"] = "Utilisez des Points de Héros pour acheter et améliorer les pouvoirs de votre héros.",
["TUTORIAL_hero_room_power_title"] = "Pouvoirs de Héros",
["TUTORIAL_hero_room_tutorial_navigate_desc"] = "Naviguez à travers différents héros.",
["TUTORIAL_hero_room_tutorial_select_desc"] = "Équipez les héros que vous souhaitez utiliser sur le champ de bataille.",
["TUTORIAL_item_room_buy_desc"] = "Utilisez vos Gemmes pour acheter des articles qui vous aideront sur le champ de bataille.",
["TUTORIAL_item_room_buy_title"] = "Achat d'Articles",
["TUTORIAL_item_room_tutorial_equip_desc"] = "Utilisez chaque emplacement pour équiper vos articles. Glissez pour changer leur ordre !",
["TUTORIAL_item_room_tutorial_navigate_desc"] = "Naviguez à travers les différents articles disponibles.",
["TUTORIAL_tower_room_power_desc"] = "Ces compétences sont disponibles une fois la tour atteinte le niveau IV.",
["TUTORIAL_tower_room_power_title"] = "Compétences de Niveau IV",
["TUTORIAL_tower_room_tutorial_equip_desc"] = "Équipez de nouvelles tours pour essayer différentes combinaisons.",
["TUTORIAL_tower_room_tutorial_navigate_desc"] = "Naviguez à travers les différentes tours.",
["TUTORIAL_tower_room_tutorial_slots_desc"] = "Utilisez chaque emplacement pour équiper vos tours. Glissez pour changer leur ordre !",
["TUTORIAL_upgrade_room_tooltip_buy_desc"] = "Utilisez des Points pour acheter des améliorations pour vos pouvoirs, tours et héros.",
["TUTORIAL_upgrade_room_tooltip_souls_desc"] = "Gagnez des Points d'Amélioration en complétant les étapes de la campagne.",
["TUTORIAL_upgrade_room_tooltip_souls_title"] = "Points d'Amélioration",
["Tap the road!"] = "Touche la route!",
["Tip"] = "Astuce",
["Tower construction"] = "Construction des tours",
["Towers"] = "Tours",
["Try again"] = "Réessayer",
["Typography"] = "Typographie",
["UPDATE_POPUP"] = "MISE À JOUR",
["UPDATING_CLOUDSAVE_MESSAGE"] = "Mise à jour des jeux enregistrés dans le cloud ...",
["UPGRADES"] = "AMÉLIORATIONS",
["UPGRADES AND HEROES RESTRICTIONS!"] = "RESTRICTIONS D'AMÉLIORATION ET DE HÉROS!",
["UPGRADE_LEVEL"] = "Améliorer le niveau",
["Undo"] = "Annuler",
["Unlocks at Level"] = "Se débloque à l'étape",
["Upgrades"] = "Améliorations",
["Use the earned hero points to train your hero!"] = "Utilise les points de héros gagnés pour entraîner ton héros!",
["Use the earned stars to improve your towers and powers!"] = "Utilise les étoiles gagnées pour améliorer tes tours et tes pouvoirs!",
["VICTORY"] = "VICTOIRE",
["Very fast"] = "Très rapide",
["Very slow"] = "Très lent",
["Veteran"] = "Vétéran",
["Victory!"] = "Victoire!",
["Voice Talent"] = "Doublage",
["WARNING"] = "ATTENTION",
["WAVE_TOOLTIP_TAP_AGAIN"] = "CLIQUE POUR L'APPELER PLUS TÔT",
["WAVE_TOOLTIP_TITLE"] = "VAGUE EN APPROCHE",
["We would like to thank"] = "Remerciements particuliers",
["Yes"] = "Oui",
["You can always change the difficulty in the options menu."] = "Tu peux toujours changer le niveau de difficulté dans les options",
["_manually_included_characters"] = "$ ¥ ￥ ƒ ₩ € ™ × $ zł ¢ £ ¤ ¥ ƒ ден дин лв. ؋ ৳ ฿ ლ ₡ ₣ ₤ ₥ ₦ ₨ ₩ ₪ ₫ € ₭ ₮ ₱ ₲ ₴ ₵ ₹ ₺ ₽ ﷼",
["alliance_close_to_home_DESCRIPTION"] = "Accorde de l'or supplémentaire au début de l'étape.",
["alliance_close_to_home_NAME"] = "RÉSERVES PARTAGÉES",
["alliance_corageous_stand_DESCRIPTION"] = "Chaque tour LINIRÉENNE construite augmente les points de santé des héros.",
["alliance_corageous_stand_NAME"] = "POSITION COURAGEUSE",
["alliance_display_of_true_might_dark_DESCRIPTION"] = "Les sorts du héros de l'Armée Noire ralentissent maintenant également tous les ennemis à l'écran.",
["alliance_display_of_true_might_dark_NAME"] = "MALEDICTION SINISTRE",
["alliance_display_of_true_might_linirea_DESCRIPTION"] = "Les sorts du héros de Linirea guérissent maintenant aussi et font réapparaître toutes les unités alliées.",
["alliance_display_of_true_might_linirea_NAME"] = "BÉNÉDICTION DE VITALITÉ",
["alliance_flux_altering_coils_DESCRIPTION"] = "Remplace tous les drapeaux de sortie par des piliers arcaniques qui téléportent les ennemis proches en arrière.",
["alliance_flux_altering_coils_NAME"] = "PILIERS ARCANE",
["alliance_friends_of_the_crown_DESCRIPTION"] = "Chaque héros LINIRÉEN équipé réduit le coût de construction et d'amélioration des tours.",
["alliance_friends_of_the_crown_NAME"] = "AMIS DE LA COURONNE",
["alliance_merciless_DESCRIPTION"] = "Chaque tour de l'ARMÉE SOMBRE construite augmente les dégâts d'attaque des héros.",
["alliance_merciless_NAME"] = "DÉFENSE IMPITOYABLE",
["alliance_seal_of_punishment_DESCRIPTION"] = "Remplace le point de défense par un sceau magique qui endommage les ennemis qui le franchissent.",
["alliance_seal_of_punishment_NAME"] = "SCÉAU DE CHÂTIMENT",
["alliance_shady_company_DESCRIPTION"] = "Chaque héros de l'ARMÉE SOMBRE équipé augmente les dégâts d'attaque des tours.",
["alliance_shady_company_NAME"] = "COMPAGNIE LOUCHE",
["alliance_shared_reserves_DESCRIPTION"] = "Accorde de l'or supplémentaire au début de l'étape.",
["alliance_shared_reserves_NAME"] = "RÉSERVES PARTAGÉES",
["build defensive towers along the road to stop them."] = "Construis des tours défensives le long de la route pour les stopper.",
["build towers to defend the road."] = "Construis des tours pour défendre la route.",
["check the stage description to see:"] = "Consulte la description de l'étape pour voir :",
["click these!"] = "ici!",
["click to continue..."] = "clique pour continuer...",
["deals area damage"] = "Inflige des dégâts de zone",
["don't let enemies past this point."] = "Ne laisse pas les ennemis passer ce point.",
["earn gold by killing enemies."] = "Gagne de l'or en tuant tes ennemis.",
["good rate of fire"] = "Bonne cadence de tir",
["heroes_desperate_effort_DESCRIPTION"] = "Les attaques des héros ignorent 10% de la résistance des ennemis.",
["heroes_desperate_effort_NAME"] = "CONNAIS TON ENNEMI",
["heroes_lethal_focus_DESCRIPTION"] = "Les héros infligent des dégâts critiques dans 20% de leurs attaques.",
["heroes_lethal_focus_NAME"] = "FOCUS LÉTAL",
["heroes_limit_pushing_DESCRIPTION"] = "Après avoir utilisé chaque Sort de Héros cinq fois, son temps de recharge sera instantanément réinitialisé. ",
["heroes_limit_pushing_NAME"] = "POUSSER LES LIMITES",
["heroes_lone_wolves_DESCRIPTION"] = "Les héros gagnent plus d'expérience lorsqu'ils sont éloignés l'un de l'autre.",
["heroes_lone_wolves_NAME"] = "LOUPS SOLITAIRES",
["heroes_nimble_physique_DESCRIPTION"] = "Les héros esquivent 20% des attaques ennemies.",
["heroes_nimble_physique_NAME"] = "PHYSIQUE AGILE",
["heroes_unlimited_vigor_DESCRIPTION"] = "Réduit tous les temps de recharge des Sorts du Héros.",
["heroes_unlimited_vigor_NAME"] = "VIGUEUR ILLIMITÉE",
["heroes_visual_learning_DESCRIPTION"] = "Les héros ont 10 % d'armure supplémentaire lorsqu'ils sont proches les uns des autres. ",
["heroes_visual_learning_NAME"] = "COUP DE MAIN",
["high damage, armor piercing"] = "Dégâts élevés et perforants",
["iron and heroic challenges may have restrictions on upgrades!"] = "Les défis fer et héroïques peuvent avoir des restrictions d'amélioration!",
["max lvl allowed"] = "niveau max.",
["multi-shot, armor piercing"] = "tirs multiples, perforants",
["no heroes"] = "aucun héros",
["protect your lands from the enemy attacks."] = "Protège tes terres des attaques ennemies.",
["rally range"] = "Portée de ralliement",
["ready for action!"] = "Prêt à l'action!",
["reinforcements_intense_workout_DESCRIPTION"] = "Améliore la santé et la durée des Renforts.",
["reinforcements_intense_workout_NAME"] = "ENTRAÎNEMENT INTENSIF",
["reinforcements_master_blacksmiths_DESCRIPTION"] = "Améliore les dégâts d'attaque et l'armure des Renforts.",
["reinforcements_master_blacksmiths_NAME"] = "MAÎTRES FORGERONS",
["reinforcements_night_veil_DESCRIPTION"] = "Les Archers de l'Ombre ont une portée et une vitesse d'attaque accrues.",
["reinforcements_night_veil_NAME"] = "ARCS CENDRÉS",
["reinforcements_power_trio_DESCRIPTION"] = "Appeler les Renforts invoque désormais également un Chevalier Parangon.",
["reinforcements_power_trio_NAME"] = "PARANGON LINIRÉEN",
["reinforcements_power_trio_dark_DESCRIPTION"] = "Appeler les Renforts invoque également un Corbeau de l'Ombre.",
["reinforcements_power_trio_dark_NAME"] = "CORBEAU DE L'OMBRE",
["reinforcements_rebel_militia_DESCRIPTION"] = "Les renforts sont remplacés par des Rebelles Liniréens, des combattants résistants portant de grandes armures.",
["reinforcements_rebel_militia_NAME"] = "MILICE LINIRÉENNE",
["reinforcements_shadow_archer_DESCRIPTION"] = "Les renforts sont remplacés par des Archers de l'Ombre, attaquant à distance et ciblant les unités volantes.",
["reinforcements_shadow_archer_NAME"] = "ORDRE DES OMBRES",
["reinforcements_thorny_armor_DESCRIPTION"] = "Les Rebelles Liniréens reflètent une partie des dégâts des attaques en mêlée ennemies.",
["reinforcements_thorny_armor_NAME"] = "ARMURE CLOUTÉE",
["resist damage from"] = "Résistance aux dégâts de",
["resists damage from"] = "Résiste aux dégâts de",
["select the rally point control"] = "Sélectionne la commande du point de ralliement",
["select the tower you want to build!"] = "Sélectionne la tour que tu désires construire!",
["select where you want to move your soldiers"] = "Sélectionne là où tu veux déplacer tes soldats",
["soldiers block enemies"] = "Les soldats bloquent les ennemis",
["some enemies enjoy different levels of magic resistance that protects them against magical attacks."] = "Certains ennemis dont dotés de résistance à la magie de niveau variable qui les protège contre les attaques magiques.",
["some enemies wear armor of different strengths that protects them against non-magical attacks."] = "Certains ennemis dont dotés d'une armure de qualité variable qui les protège contre les attaques non magiques.",
["tap these!"] = "Touche-les!",
["this is a strategic point."] = "Ceci est un point stratégique.",
["towers_favorite_customer_DESCRIPTION"] = "Lors de l'achat du dernier niveau d'une compétence, réduisez son coût de 50 %.",
["towers_favorite_customer_NAME"] = "CLIENT FAVORI",
["towers_golden_time_DESCRIPTION"] = "Augmente l'or bonus pour l'appel d'une vague en avance.",
["towers_golden_time_NAME"] = "TEMPS D'OR",
["towers_improved_formulas_DESCRIPTION"] = "Maximise les dégâts de TOUTES les explosions des tours et augmente leur zone d'effet.",
["towers_improved_formulas_NAME"] = "FORMULES AMÉLIORÉES",
["towers_keen_accuracy_DESCRIPTION"] = "Réduit le temps de recharge de TOUTES les compétences de tour de 20%.",
["towers_keen_accuracy_NAME"] = "FERVEUR DE BATAILLE",
["towers_royal_training_DESCRIPTION"] = "Réduit le temps d'apparition des unités de tour et le délai de récupération des Renforts.",
["towers_royal_training_NAME"] = "APPEL À L'ACTION",
["towers_scoping_mechanism_DESCRIPTION"] = "Augmente la portée d'attaque de TOUTES les tours de 10 %.",
["towers_scoping_mechanism_NAME"] = "MÉCANISME DE PORTÉE",
["towers_war_rations_DESCRIPTION"] = "Augmente la santé de TOUTES les unités de tour de 10 %.",
["towers_war_rations_NAME"] = "RATIONS DE GUERRE",
["towers_wise_investment_DESCRIPTION"] = "Les tours remboursent désormais 90 % de leur coût lors de la vente.",
["towers_wise_investment_NAME"] = "INVESTISSEMENT JUDICIEUX",
["wOOt!"] = "Cool!",
["you can adjust your soldiers rally point to make them defend a different area."] = "Tu peux ajuster le point de ralliement de tes soldats pour leur ordonner de défendre une autre zone.",
}
