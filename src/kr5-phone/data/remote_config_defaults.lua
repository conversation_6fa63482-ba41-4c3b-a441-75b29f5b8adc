local d = {

    -- config taken from: http://configuration.ironhidegames.com/krorigins-1.0.json
    
    ------------------------------------------------------------
    -- links
    link_more_games = {
        ['com.ironhidegames.kingdomrush.alliance'] = 'https://www.ironhidegames.com/games',         -- TODO: definir si precisa un tracker
        ['com.ironhidegames.android.kingdomrush.alliance'] = 'https://www.ironhidegames.com/games', -- TODO
        ['com.ironhidegames.kingdomrush.alliance.windows.steam'] = 'https://store.steampowered.com/franchise/ironhidegames/',
        ['com.ironhidegames.kingdomrush.alliance.mac.steam'] = 'https://store.steampowered.com/franchise/ironhidegames/',
        ['com.ironhidegames.kingdomrush.alliance.mac.appstore'] = 'https://www.ironhidegames.com/games',
    },

    url_twitter          = { default = "https://twitter.com/ironhidegames" },
    url_facebook         = { default = "http://www.facebook.com/ironhidegames" },
    url_instagram        = { default = "https://www.instagram.com/ironhidegames" },
    url_discord          = { default = "https://discord.gg/aqHGabqupe" },
    url_tiktok          = { default = "https://www.tiktok.com/@ironhidegames" },

    url_ih               = { default = "https://www.ironhidegames.com/games" },
    url_privacy_policy   = { 
        ['com.ironhidegames.kingdomrush.alliance'] = 'https://www.ironhidegames.com/PrivacyPolicy',
        ['com.ironhidegames.android.kingdomrush.alliance'] = 'https://www.ironhidegames.com/PrivacyPolicy',
        ['com.ironhidegames.kingdomrush.alliance.cn'] = 'https://east2west.cn/statement/PrivacyPolicy.html',
        ['com.ironhidegames.android.kingdomrush.alliance.cn'] = 'https://east2west.cn/statement/PrivacyPolicy.html',
        ['com.ironhidegames.kingdomrush.alliance.universal.premium'] = 'https://www.kingdomrushalliance.com/privacy-policy/apple-arcade',
        default = "https://www.ironhidegames.com/PrivacyPolicy" 
    },
    url_terms_of_service = { default = "https://www.ironhidegames.com/TermsOfService" },
    
    url_strategy_guide   = { default = "http://www.kingdomrushorigins.com/strategy.html" }, -- TODO

    url_store = { ['com.ironhidegames.android.kingdomrush.alliance']="https://play.google.com/store/apps/details?id=com.ironhidegames.android.kingdomrush.alliance",['com.ironhidegames.kingdomrush.alliance']="https://apps.apple.com/us/app/kingdom-rush-5-alliance-td/id1622869542", default = "http://www.kingdomrushorigins.com" },
    
    min_version = nil, -- versions < will not be playable
    -- rating
    ask_for_rating = true,
    ask_for_rating_level = {4, 7, 12}, 

    -- ads
    --show_free_gems_for_video_button = true,
    --ads_prio = {'admob','adcolony','chartboost'},

    -- news
    news_url = "https://news.ironhidegames.com/api/v1/PromoNews",
    
    -- in-game shop
    ingame_shop = true,

    -- premium flags
    premium_show_more_games = false,
    premium_show_news = false,
    
    ------------------------------------------------------------
    -- products and offers / gpiab (Google Play In-App Billing)
    products_gpiab = {
        'dlc_1','dlc_2','hero_lava','tower_dwarf', 'hero_wukong', 'tower_pandas',
        'offer_dlc_1','offer_dlc_1_b','offer_dlc_2','offer_dlc_2_b',
        'premium_unlock',
        'gems_handful','gems_pouch','gems_barrel','gems_chest','gems_wagon','gems_mountain',
        'sale_gems_handful','sale_gems_pouch','sale_gems_barrel','sale_gems_chest','sale_gems_wagon','sale_gems_mountain',
        'hero_space_elf','hero_builder','hero_lumenir','hero_mecha','hero_bird', 'hero_dragon_gem', 'hero_hunter', 'hero_robot', 'hero_witch', 'hero_dragon_bone', 'hero_dragon_arb', 'hero_spider',
        'tower_elven_stargazers','tower_necromancer','tower_barrel','tower_sand','tower_ghost', 'tower_dark_elf', 'tower_hermit_toad','tower_sparking_geode',
        'sale_hero_space_elf','sale_hero_builder','sale_hero_lumenir','sale_hero_mecha','sale_hero_bird', 'sale_hero_dragon_gem', 'sale_hero_hunter', 'sale_hero_robot',
        'sale_hero_witch', 'sale_hero_dragon_bone', 'sale_hero_dragon_arb', 'sale_hero_spider',
        'sale_tower_elven_stargazers','sale_tower_necromancer','sale_tower_barrel','sale_tower_sand','sale_tower_ghost', 'sale_tower_dark_elf', 'sale_tower_hermit_toad', 'sale_tower_sparking_geode',
        'offer_allheroes','offer_alltowers','offer_starterpack',
        'offer_allheroes_2','offer_alltowers_2',
        'offer_allheroes_2_b','offer_alltowers_2_b',
        'offer_allheroes_3', 'offer_alltowers_3',
        'offer_allheroes_3_b', 'offer_alltowers_3_b',
        'offer_allheroes_4', 'offer_alltowers_4',
        'offer_allheroes_4_b', 'offer_alltowers_4_b',
        'offer_halloween','offer_halloween_b', 
        'offer_crocodile', 'offer_crocodile_b',
        'offer_arachnophobia', 'offer_arachnophobia_b',
        'offer_heroes_1','offer_heroes_2','offer_heroes_3','offer_heroes_4','offer_heroes_5','offer_heroes_6','offer_heroes_7','offer_heroes_8','offer_heroes_9','offer_heroes_10','offer_heroes_11','offer_heroes_12','offer_heroes_13','offer_heroes_14','offer_heroes_15','offer_heroes_16',
        'offer_towers_1','offer_towers_2','offer_towers_3','offer_towers_4','offer_towers_5','offer_towers_6','offer_towers_7','offer_towers_8','offer_towers_9','offer_towers_10','offer_towers_11','offer_towers_12',
        'offer_heroestowers_1','offer_heroestowers_2','offer_heroestowers_3','offer_heroestowers_4','offer_heroestowers_5','offer_heroestowers_6','offer_heroestowers_7','offer_heroestowers_8','offer_heroestowers_9','offer_heroestowers_10','offer_heroestowers_11','offer_heroestowers_12',
        'offer_showcase_1','offer_showcase_2','offer_showcase_3','offer_showcase_4','offer_showcase_5','offer_showcase_6','offer_showcase_7','offer_showcase_8','offer_showcase_9','offer_showcase_10','offer_showcase_11','offer_showcase_12'
    },
    hero_sales_gpiab = {},    -- array of hero names
    tower_sales_gpiab = {},    -- array of tower names
    gems_sales_gpiab = {},    -- array of gems names
    offers_gpiab = {
        'offer_dlc_1','offer_dlc_2',
        'offer_allheroes_4','offer_alltowers_4','offer_starterpack',
        'offer_heroes_1','offer_heroes_3','offer_heroes_5','offer_heroes_7','offer_heroes_9','offer_heroes_11','offer_heroes_13','offer_heroes_15',
        'offer_towers_1','offer_towers_3','offer_towers_5','offer_towers_7','offer_towers_9','offer_towers_11',
        'offer_heroestowers_1','offer_heroestowers_3','offer_heroestowers_5','offer_heroestowers_7','offer_heroestowers_9',
        'offer_showcase_1','offer_showcase_3','offer_showcase_5','offer_showcase_7','offer_showcase_9','offer_showcase_11'
    },

    -- storekit (Apple AppStore)
    products_storekit = {
        'dlc_1','dlc_2','hero_lava','tower_dwarf', 'hero_wukong', 'tower_pandas',
        'offer_dlc_1','offer_dlc_1_b','offer_dlc_2','offer_dlc_2_b',
        'gems_handful','gems_pouch','gems_barrel','gems_chest','gems_wagon','gems_mountain',
        'sale_gems_handful','sale_gems_pouch','sale_gems_barrel','sale_gems_chest','sale_gems_wagon','sale_gems_mountain',
        'hero_space_elf','hero_builder','hero_lumenir','hero_mecha','hero_bird', 'hero_dragon_gem', 'hero_hunter', 'hero_robot', 'hero_witch', 'hero_dragon_bone', 'hero_dragon_arb', 'hero_spider',
        'tower_elven_stargazers','tower_necromancer','tower_barrel','tower_sand','tower_ghost', 'tower_dark_elf', 'tower_hermit_toad','tower_sparking_geode',
        'sale_hero_space_elf','sale_hero_builder','sale_hero_lumenir','sale_hero_mecha','sale_hero_bird', 'sale_hero_dragon_gem', 'sale_hero_hunter', 'sale_hero_robot',
        'sale_hero_witch', 'sale_hero_dragon_bone', 'sale_hero_dragon_arb', 'sale_hero_spider',
        'sale_tower_elven_stargazers','sale_tower_necromancer','sale_tower_barrel','sale_tower_sand','sale_tower_ghost', 'sale_tower_dark_elf', 'sale_tower_hermit_toad', 'sale_tower_sparking_geode',
        'offer_allheroes','offer_alltowers','offer_starterpack',
        'offer_allheroes_b','offer_alltowers_b','offer_starterpack_b',
        'offer_allheroes_2','offer_alltowers_2',
        'offer_allheroes_2_b','offer_alltowers_2_b',
        'offer_allheroes_3','offer_alltowers_3',
        'offer_allheroes_3_b','offer_alltowers_3_b',
        'offer_allheroes_4','offer_alltowers_4',
        'offer_allheroes_4_b','offer_alltowers_4_b',
        'offer_halloween','offer_halloween_b', 
        'offer_crocodile','offer_crocodile_b',
        'offer_arachnophobia','offer_arachnophobia_b',
        'offer_heroes_1','offer_heroes_2','offer_heroes_3','offer_heroes_4','offer_heroes_5','offer_heroes_6','offer_heroes_7','offer_heroes_8','offer_heroes_9','offer_heroes_10','offer_heroes_11','offer_heroes_12','offer_heroes_13','offer_heroes_14','offer_heroes_15','offer_heroes_16',
        'offer_towers_1','offer_towers_2','offer_towers_3','offer_towers_4','offer_towers_5','offer_towers_6','offer_towers_7','offer_towers_8','offer_towers_9','offer_towers_10','offer_towers_11','offer_towers_12',
        'offer_heroestowers_1','offer_heroestowers_2','offer_heroestowers_3','offer_heroestowers_4','offer_heroestowers_5','offer_heroestowers_6','offer_heroestowers_7','offer_heroestowers_8','offer_heroestowers_9','offer_heroestowers_10','offer_heroestowers_11','offer_heroestowers_12',
        'offer_showcase_1','offer_showcase_2','offer_showcase_3','offer_showcase_4','offer_showcase_5','offer_showcase_6','offer_showcase_7','offer_showcase_8','offer_showcase_9','offer_showcase_10','offer_showcase_11','offer_showcase_12'
    },
    hero_sales_storekit = {},    -- array of hero names
    tower_sales_storekit = {},    -- array of tower names
    gems_sales_storekit = {},    -- array of gems names
    offers_storekit = {
        'offer_dlc_1','offer_dlc_2',
        'offer_allheroes_4','offer_alltowers_4','offer_starterpack',
        'offer_heroes_1','offer_heroes_3','offer_heroes_5','offer_heroes_7','offer_heroes_9','offer_heroes_11',
        'offer_towers_1','offer_towers_3','offer_towers_5','offer_towers_7','offer_towers_9','offer_towers_11','offer_heroes_13','offer_heroes_15',
        'offer_heroestowers_1','offer_heroestowers_3','offer_heroestowers_5','offer_heroestowers_7','offer_heroestowers_9','offer_heroestowers_11',
        'offer_showcase_1','offer_showcase_3','offer_showcase_5','offer_showcase_7','offer_showcase_9','offer_showcase_11'
    },

    one_time_gifts = {   }, -- ej: {         id ='launch',         custom_title = 'FIRST_WEEK_PACK',         custom_description = 'CLAIM_GIFT',         includes_consumables={             {name='item_summon_blackburn', count=2},             {name='item_cluster_bomb', count=4},             {name='gems_handful', count=1}             }         }       

    ------------------------------------------------------------
    -- default offer conditions
    -- notes:
    -- * Conditions accept a special string value: 'any'
    -- This allows an offer to override the default and skip the check.
    -- * Use multiline comments because Firebase strips newlines.
    --
    default_offer_conditions = {
        --[[debug_skip_conditions = true,         --[[ DEBUG ]]
        offer_includes_hero_on_sale = false,
        offer_includes_purchased_product = false,
        -- offer_includes_unpurchased_products_count = 1,
      --  offer_was_shown = false,                  --[[ recheckTimeInSeconds: 0 ]]
        offer_was_purchased = false,
        player_made_purchases = 'any',            --[[ checkSomethingPurchased: false ]]
        --[[player_reached_level = 0,  ]]
        --[[player_reached_stars = 0,  ]]
        --[[player_reached_sessions = 0, ]]
     --   seconds_elapsed_since_any_offer_purchased = 86400,
     --   seconds_elapsed_since_any_offer_shown = 86400,
        --[[sessions_passed_since_offer_purchased = 0,   ]]
        --[[sessions_passed_since_offer_shown = 0,       ]]
    },
    
    default_offer_params = {
        seconds_icon_is_visible = 43200,
    },
    
    ------------------------------------------------------------
    -- product definitions
    -- note: the internal name does not have the product_ prefix.
    product_premium_unlock   = { premium=true, skus={default='com.ironhidegames.kingdomrush5.googlepass'} },

    product_gems_handful     = { gems=true, reward=1200,  consumable=true, skus={default='com.ironhidegames.kingdomrush5.gems_handful'  } },
    product_gems_pouch       = { gems=true, reward=2200,  consumable=true, skus={default='com.ironhidegames.kingdomrush5.gems_pouch'    } },
    product_gems_barrel      = { gems=true, reward=5000,  consumable=true, skus={default='com.ironhidegames.kingdomrush5.gems_barrel'   } },
    product_gems_chest       = { gems=true, reward=11000, consumable=true, skus={default='com.ironhidegames.kingdomrush5.gems_chest'    } },
    product_gems_wagon       = { gems=true, reward=30000, consumable=true, skus={default='com.ironhidegames.kingdomrush5.gems_wagon'    } },
    product_gems_mountain    = { gems=true, reward=70000, consumable=true, skus={default='com.ironhidegames.kingdomrush5.gems_mountain' } },

    product_sale_gems_handful     = { consumable=true, skus={default='com.ironhidegames.kingdomrush5.gems_handful_sale'  }, includes={'gems_handful'}, conditions={} },
    product_sale_gems_pouch       = { consumable=true, skus={default='com.ironhidegames.kingdomrush5.gems_pouch_sale'    }, includes={'gems_pouch'}, conditions={}  },
    product_sale_gems_barrel      = { consumable=true, skus={default='com.ironhidegames.kingdomrush5.gems_barrel_sale'   }, includes={'gems_barrel'}, conditions={}  },
    product_sale_gems_chest       = { consumable=true, skus={default='com.ironhidegames.kingdomrush5.gems_chest_sale'    }, includes={'gems_chest'}, conditions={}  },
    product_sale_gems_wagon       = { consumable=true, skus={default='com.ironhidegames.kingdomrush5.gems_wagon_sale'    }, includes={'gems_wagon'}, conditions={}  },
    product_sale_gems_mountain    = { consumable=true, skus={default='com.ironhidegames.kingdomrush5.gems_mountain_sale' }, includes={'gems_mountain'}, conditions={}  },


    product_hero_space_elf              = { skus={default="com.ironhidegames.kingdomrush5.hero_therien" }},
    product_hero_builder                = { skus={default="com.ironhidegames.kingdomrush5.hero_torres"  }},
    product_hero_bird                   = { skus={default="com.ironhidegames.kingdomrush5.hero_broden"  }},
    product_hero_dragon_gem             = { skus={default="com.ironhidegames.kingdomrush5.hero_kosmyr"  }},
    product_hero_lumenir                = { skus={default="com.ironhidegames.kingdomrush5.hero_lumenir" }},
    product_hero_mecha                  = { skus={default="com.ironhidegames.kingdomrush5.hero_onagro"  }},
    product_hero_hunter                 = { skus={default="com.ironhidegames.kingdomrush5.hero_anya"    }},
    product_hero_robot                  = { skus={default="com.ironhidegames.kingdomrush5.hero_warhead" }},
    product_hero_witch                  = { skus={default="com.ironhidegames.kingdomrush5.hero_stregi"  }, expansion='undying_fury'},
    product_hero_dragon_bone            = { skus={default="com.ironhidegames.kingdomrush5.hero_bonehart"}, expansion='undying_fury'},
    product_hero_dragon_arb             = { skus={default="com.ironhidegames.kingdomrush5.hero_sylvara" }, expansion = 'ancient_hunger'},
    product_hero_lava                   = { skus={default="no_sale_hero_lava" }, expansion = 'dlc_1'},
    product_hero_spider                 = { skus={default="com.ironhidegames.kingdomrush5.hero_spydyr"  }, expansion = 'arachnophobia'},
    product_hero_wukong                 = { skus={default="no_sale_hero_wukong" }, expansion = 'dlc_2'},

    product_tower_elven_stargazers      = { skus={default="com.ironhidegames.kingdomrush5.tower_elven_stargazers"   }},
    product_tower_necromancer           = { skus={default="com.ironhidegames.kingdomrush5.tower_necromancer"        }},
    product_tower_barrel                = { skus={default="com.ironhidegames.kingdomrush5.tower_battle_brewmasters" }},
    product_tower_sand                  = { skus={default="com.ironhidegames.kingdomrush5.tower_dune_sentinels"     }},
    product_tower_ghost                 = { skus={default="com.ironhidegames.kingdomrush5.tower_grim_wraiths"       }},
    product_tower_dark_elf              = { skus={default="com.ironhidegames.kingdomrush5.tower_twilight_longbows"  }, expansion='undying_fury'},
    product_tower_hermit_toad           = { skus={default="com.ironhidegames.kingdomrush5.tower_bog_hermit"         }, expansion = 'ancient_hunger'},
    product_tower_dwarf                 = { skus={default="no_sale_tower_dwarf"       }, expansion = 'dlc_1'},
    product_tower_sparking_geode        = { skus={default="com.ironhidegames.kingdomrush5.tower_lightning_sentinel" }, expansion = 'arachnophobia'},
    product_tower_pandas                = { skus={default="no_sale_tower_pandas"       }, expansion = 'dlc_2'},

    product_sale_hero_space_elf ={
        skus={default='com.ironhidegames.kingdomrush5.hero_therien_sale'},
        includes={'hero_space_elf'},
        conditions={}
    },
    product_sale_hero_builder ={
        skus={default='com.ironhidegames.kingdomrush5.hero_torres_sale'},
        includes={'hero_builder'},
        conditions={}
    },
    product_sale_hero_bird ={
        skus={default='com.ironhidegames.kingdomrush5.hero_broden_sale'},
        includes={'hero_bird'},
        conditions={}
    },
    product_sale_hero_dragon_gem ={
        skus={default='com.ironhidegames.kingdomrush5.hero_kosmyr_sale'},
        includes={'hero_dragon_gem'},
        conditions={}
    },
    product_sale_hero_lumenir ={
        skus={default='com.ironhidegames.kingdomrush5.hero_lumenir_sale'},
        includes={'hero_lumenir'},
        conditions={}
    },
    product_sale_hero_mecha ={
        skus={default='com.ironhidegames.kingdomrush5.hero_onagro_sale'},
        includes={'hero_mecha'},
        conditions={}
    },
    product_sale_hero_hunter ={
        skus={default='com.ironhidegames.kingdomrush5.hero_anya_sale'},
        includes={'hero_hunter'},
        conditions={}
    },
    product_sale_hero_robot ={
        skus={default='com.ironhidegames.kingdomrush5.hero_warhead_sale'},
        includes={'hero_robot'},
        conditions={}
    },
    product_sale_hero_witch ={
        skus={default='com.ironhidegames.kingdomrush5.hero_stregi_sale'},
        includes={'hero_witch'},
        conditions={},
        expansion='undying_fury',
    },
    product_sale_hero_dragon_bone ={
        skus={default='com.ironhidegames.kingdomrush5.hero_bonehart_sale'},
        includes={'hero_dragon_bone'},
        conditions={},
        expansion='undying_fury',
    },
    product_sale_hero_dragon_arb ={
        skus={default='com.ironhidegames.kingdomrush5.hero_sylvara_sale'},
        includes={'hero_dragon_arb'},
        conditions={},
        expansion='ancient_hunger'
    },
    product_sale_hero_spider ={
        skus={default='com.ironhidegames.kingdomrush5.hero_spydyr_sale'},
        includes={'hero_spider'},
        conditions={},
        expansion='arachnophobia'
    },
    product_sale_tower_elven_stargazers ={
        skus={default='com.ironhidegames.kingdomrush5.tower_elven_stargazers_sale'},
        includes={'tower_elven_stargazers'},
        conditions={},
    },
    product_sale_tower_necromancer ={
        skus={default='com.ironhidegames.kingdomrush5.tower_necromancer_sale'},
        includes={'tower_necromancer'},
        conditions={}
    },
    product_sale_tower_barrel ={
        skus={default='com.ironhidegames.kingdomrush5.tower_battle_brewmasters_sale'},
        includes={'tower_barrel'},
        conditions={}
    },
    product_sale_tower_sand ={
        skus={default='com.ironhidegames.kingdomrush5.tower_dune_sentinels_sale'},
        includes={'tower_sand'},
        conditions={}
    },
    product_sale_tower_ghost ={
        skus={default='com.ironhidegames.kingdomrush5.tower_grim_wraiths_sale'},
        includes={'tower_ghost'},
        conditions={}
    },
    product_sale_tower_dark_elf ={
        skus={default='com.ironhidegames.kingdomrush5.tower_twilight_longbows_sale'},
        includes={'tower_dark_elf'},
        conditions={},
        expansion = 'undying_fury',
    },
    product_sale_tower_hermit_toad ={
        skus={default='com.ironhidegames.kingdomrush5.tower_bog_hermit_sale'},
        includes={'tower_hermit_toad'},
        conditions={},
        expansion = 'ancient_hunger'
    },
    product_sale_tower_sparking_geode ={
        skus={default='com.ironhidegames.kingdomrush5.tower_lightning_sentinel_sale'},
        includes={'tower_sparking_geode'},
        conditions={},
        expansion = 'arachnophobia'
    },

    product_dlc_1 = {  
        skus={default="no_sale_dlc_1"},
        includes={'hero_lava','tower_dwarf'},    
        persistent = true,
        conditions={},
    },
    product_offer_dlc_1 ={
        skus={default="com.ironhidegames.kingdomrush5.dlc_dwarves"},
        includes={'dlc_1'},    
        persistent = true,
        conditions={
            player_reached_level=1,
        },
        custom_title = 'SHOP_ROOM_DLC_1_TITLE', --temp move to the card
        custom_description = 'SHOP_ROOM_DLC_1_DESCRIPTION'
    },
    product_offer_dlc_1_b = {
        skus={default="com.ironhidegames.kingdomrush5.dlc_dwarves_b"},
        includes={'dlc_1'},    
        persistent = true,
        conditions={
            player_reached_level=1,
        },
        custom_title = 'SHOP_ROOM_DLC_1_TITLE', --temp move to the card
        custom_description = 'SHOP_ROOM_DLC_1_DESCRIPTION',
        custom_video = 'dlc_1.ogv'
    },

    product_dlc_2 = {  
        skus={default="no_sale_dlc_2"},
        includes={'hero_wukong','tower_pandas'},    
        persistent = true,
        conditions={},
    },
    product_offer_dlc_2 ={
        skus={default="com.ironhidegames.kingdomrush5.dlc_wukong"},
        includes={'dlc_2'},    
        persistent = true,
        conditions={
            player_reached_level=1,
        },
        custom_title = 'SHOP_ROOM_DLC_2_TITLE', --temp move to the card
        custom_description = 'SHOP_ROOM_DLC_2_DESCRIPTION'
    },
    product_offer_dlc_2_b = {
        skus={default="com.ironhidegames.kingdomrush5.dlc_wukong_b"},
        includes={'dlc_2'},    
        conditions={},
        persistent = true
    },

    product_offer_starterpack = {
        consumable=true,
        skus={default='com.ironhidegames.kingdomrush5.special_offer_starter_pack_1'},
        includes_consumables={
            {name='item_summon_blackburn', count=3},
            {name='item_veznan_wrath', count=2},
            {name='gems_handful', count=1}
        },
        conditions={
            player_reached_level=1,
            offer_was_shown = false
        },
        custom_title = 'OFFER_PACK_TITLE_STARTER_PACK',
        priority = 100
    },

    product_offer_allheroes = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_all_heroes_1"},
        includes={'hero_space_elf','hero_builder','hero_bird','hero_dragon_gem','hero_lumenir','hero_mecha','hero_hunter','hero_robot'},    
        conditions = {
            offer_includes_hero_on_sale = 'any',
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 4,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        persistent = true,
    },
    product_offer_alltowers = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_all_towers_1"},
        includes={'tower_elven_stargazers','tower_necromancer','tower_barrel','tower_sand','tower_ghost'},    
        conditions = {
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 3,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        persistent = true,
    },

    product_offer_allheroes_b = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_all_heroes_1_b"},
        includes={'hero_space_elf','hero_builder','hero_bird','hero_dragon_gem','hero_lumenir','hero_mecha','hero_hunter','hero_robot'},    
        conditions = {
            offer_includes_hero_on_sale = 'any',
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 4,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        all_heroes = true,
        persistent = true,
    },
    product_offer_alltowers_b = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_all_towers_1_b"},
        includes={'tower_elven_stargazers','tower_necromancer','tower_barrel','tower_sand','tower_ghost'},    
        conditions = {
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 3,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        all_towers = true,
        persistent = true,
    },

    product_offer_halloween = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_dead_elves"},
        includes={'hero_witch','hero_dragon_bone','tower_dark_elf'},    
        conditions = {
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 3,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        season_offer = 'halloween', -- season: halloween, christmas, etc
        persistent = true,
        expansion = 'undying_fury',
    },
    product_offer_halloween_b = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_dead_elves_b"},
        includes={'hero_witch','hero_dragon_bone','tower_dark_elf'},    
        conditions = {
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 3,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        season_offer = 'halloween',
        persistent = true,
        expansion = 'undying_fury',
    },
    product_offer_crocodile = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_crocodile"},
        includes={'hero_dragon_arb','tower_hermit_toad'},    
        conditions = {
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 2,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        season_offer = 'crocs', -- season: halloween, christmas, etc
        persistent = true,
        expansion = 'ancient_hunger'
    },
    product_offer_crocodile_b = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_crocodile_b"},
        includes={'hero_dragon_arb','tower_hermit_toad'},    
        conditions = {
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 2,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        season_offer = 'crocs',
        persistent = true,
        expansion = 'ancient_hunger'
    },
    product_offer_arachnophobia = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_spider"},
        includes={'hero_spider','tower_sparking_geode'},    
        conditions = {
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 2,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        season_offer = 'spider', -- season: halloween, christmas, etc
        persistent = true,
        expansion = 'arachnophobia'
    },
    product_offer_arachnophobia_b = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_spider_b"},
        includes={'hero_spider','tower_sparking_geode'},    
        conditions = {
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 2,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        season_offer = 'spider',
        persistent = true,
        expansion = 'arachnophobia'
    },

    product_offer_allheroes_2 = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_all_heroes_2"},
        includes={'hero_space_elf','hero_builder','hero_bird','hero_dragon_gem','hero_lumenir','hero_mecha','hero_hunter','hero_robot','hero_dragon_bone','hero_witch'},    
        conditions = {
            offer_includes_hero_on_sale = 'any',
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 4,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        all_heroes = true,
        persistent = true,
        expansion = 'undying_fury',
    },
    product_offer_allheroes_2_b = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_all_heroes_2_b"},
        includes={'hero_space_elf','hero_builder','hero_bird','hero_dragon_gem','hero_lumenir','hero_mecha','hero_hunter','hero_robot','hero_dragon_bone','hero_witch'},    
        conditions = {
            offer_includes_hero_on_sale = 'any',
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 4,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        all_heroes = true,
        persistent = true,
        expansion = 'undying_fury',
    },
    product_offer_allheroes_3 = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_all_heroes_3"},
        includes={'hero_space_elf','hero_builder','hero_bird','hero_dragon_gem','hero_lumenir','hero_mecha','hero_hunter','hero_robot','hero_dragon_bone','hero_witch','hero_dragon_arb'},    
        conditions = {
            offer_includes_hero_on_sale = 'any',
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 4,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        all_heroes = true,
        persistent = true,
        expansion = 'ancient_hunger'
    },
    product_offer_allheroes_3_b = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_all_heroes_3_b"},
        includes={'hero_space_elf','hero_builder','hero_bird','hero_dragon_gem','hero_lumenir','hero_mecha','hero_hunter','hero_robot','hero_dragon_bone','hero_witch','hero_dragon_arb'},    
        conditions = {
            offer_includes_hero_on_sale = 'any',
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 4,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        all_heroes = true,
        persistent = true,
        expansion = 'ancient_hunger'
    },
    product_offer_allheroes_4 = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_all_heroes_4"},
        includes={'hero_space_elf','hero_builder','hero_bird','hero_dragon_gem','hero_lumenir','hero_mecha','hero_hunter','hero_robot','hero_dragon_bone','hero_witch','hero_dragon_arb','hero_spider'},    
        conditions = {
            offer_includes_hero_on_sale = 'any',
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 4,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        all_heroes = true,
        persistent = true,
        expansion = 'arachnophobia'
    },
    product_offer_allheroes_4_b = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_all_heroes_4_b"},
        includes={'hero_space_elf','hero_builder','hero_bird','hero_dragon_gem','hero_lumenir','hero_mecha','hero_hunter','hero_robot','hero_dragon_bone','hero_witch','hero_dragon_arb','hero_spider'},    
        conditions = {
            offer_includes_hero_on_sale = 'any',
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 4,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        all_heroes = true,
        persistent = true,
        expansion = 'arachnophobia'
    },
    product_offer_alltowers_2 = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_all_towers_2"},
        includes={'tower_elven_stargazers','tower_necromancer','tower_barrel','tower_sand','tower_ghost','tower_dark_elf','tower_hermit_toad'},    
        conditions = {
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 3,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        all_towers = true,
        persistent = true,
        expansion = 'undying_fury',
    },
    product_offer_alltowers_2_b = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_all_towers_2_b"},
        includes={'tower_elven_stargazers','tower_necromancer','tower_barrel','tower_sand','tower_ghost','tower_dark_elf'},    
        conditions = {
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 3,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        all_towers = true,
        persistent = true,
        expansion = 'undying_fury',
    },
    product_offer_alltowers_3 = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_all_towers_3"},
        includes={'tower_elven_stargazers','tower_necromancer','tower_barrel','tower_sand','tower_ghost','tower_dark_elf','tower_hermit_toad'},    
        conditions = {
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 3,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        all_towers = true,
        persistent = true,
        expansion = 'ancient_hunger'
    },
    product_offer_alltowers_3_b = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_all_towers_3_b"},
        includes={'tower_elven_stargazers','tower_necromancer','tower_barrel','tower_sand','tower_ghost','tower_dark_elf','tower_hermit_toad'},    
        conditions = {
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 3,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        all_towers = true,
        persistent = true,
        expansion = 'ancient_hunger'
    },

    product_offer_alltowers_4 = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_all_towers_4"},
        includes={'tower_elven_stargazers','tower_necromancer','tower_barrel','tower_sand','tower_ghost','tower_dark_elf','tower_hermit_toad','tower_sparking_geode'},    
        conditions = {
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 3,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        all_towers = true,
        persistent = true,
        expansion = 'arachnophobia'
    },
    product_offer_alltowers_4_b = {  
        skus={default="com.ironhidegames.kingdomrush5.special_offer_all_towers_4_b"},
        includes={'tower_elven_stargazers','tower_necromancer','tower_barrel','tower_sand','tower_ghost','tower_dark_elf','tower_hermit_toad','tower_sparking_geode'},    
        conditions = {
            offer_includes_purchased_product = 'any',
            offer_includes_unpurchased_products_count = 3,
            offer_was_shown = 'any',
            player_reached_level = 1,
            player_reached_sessions = 1,
        },
        all_towers = true,
        persistent = true,
        expansion = 'arachnophobia'
    },

    product_offer_starterpack_b = {
        consumable=true,
        skus={default='com.ironhidegames.kingdomrush5.special_offer_starter_pack_2'},
        includes_consumables={
            {name='item_summon_blackburn', count=3},
            {name='item_veznan_wrath', count=2},
            {name='gems_handful', count=1}
        },
        conditions={
            player_reached_level=1,
            offer_was_shown = false
        },
        custom_title = 'OFFER_PACK_TITLE_STARTER_PACK',
        priority = 100
    },

    ------------------------------------------------------------
    -- restore
    -- received link: https://link.kingdomrushalliance.com/restore?token={token}
    -- sent link    : https://restore.ironhidegames.com/restore/{token}     
    restore_extract_token_regex = '.*/restore%?token=(.*)',
    restore_url_fmt = 'https://restore.ironhidegames.com/restore/%s',  -- %s is replaced by the token
    

    product_offer_heroes_1 ={
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_1'},
        includes={'hero_dragon_gem','hero_lumenir'},
        conditions={player_reached_level=11,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_heroes_2 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_2'},
        includes={'hero_dragon_gem','hero_lumenir'},
        conditions={player_reached_level=11,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_heroes_3 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_3'},
        includes={'hero_space_elf','hero_mecha','hero_robot'},
        conditions={player_not_reached_level=12,
        offer_includes_unpurchased_products_count = 3,},
        priority = 50
    },
    product_offer_heroes_4 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_4'},
        includes={'hero_space_elf','hero_mecha','hero_robot'},
        conditions={player_not_reached_level=12,
        offer_includes_unpurchased_products_count = 3,},
        priority = 50
    },
    product_offer_heroes_5 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_5'},
        includes={'hero_builder','hero_bird'},
        conditions={player_reached_level=7,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_heroes_6 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_6'},
        includes={'hero_builder','hero_bird'},
        conditions={player_reached_level=7,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_heroes_7 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_7'},
        includes={'hero_space_elf','hero_builder','hero_hunter'},
        conditions={player_not_reached_level=7,
        offer_includes_unpurchased_products_count = 3,},
        priority = 50
    },
    product_offer_heroes_8 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_8'},
        includes={'hero_space_elf','hero_builder','hero_hunter'},
        conditions={player_not_reached_level=7,
        offer_includes_unpurchased_products_count = 3,},
        priority = 50
    },
    product_offer_heroes_9 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_9'},
        includes={'hero_mecha','hero_robot'},
        conditions={player_reached_level=1,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_heroes_10 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_10'},
        includes={'hero_mecha','hero_robot'},
        conditions={player_reached_level=7,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_heroes_11 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_11'},
        includes={'hero_space_elf','hero_hunter'},
        conditions={player_reached_level=7,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_heroes_12 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_12'},
        includes={'hero_space_elf','hero_hunter'},
        conditions={    
            player_reached_level=7,
            offer_includes_unpurchased_products_count = 2
        },
        priority = 50
    },
    product_offer_heroes_13 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_13'},
        includes={'hero_witch','hero_dragon_bone'},
        conditions={
            player_not_reached_level=7,
            offer_includes_unpurchased_products_count = 2,
        },
        priority = 50,
        expansion = 'undying_fury',
    },
    product_offer_heroes_14 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_14'},
        includes={'hero_witch','hero_dragon_bone'},
        conditions={
            player_not_reached_level=7,
            offer_includes_unpurchased_products_count = 2,
        },
        priority = 50,
        expansion = 'undying_fury',
    },
    product_offer_heroes_15 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_15'},
        includes={'hero_witch','hero_hunter'},
        conditions={
            player_not_reached_level=7,
            offer_includes_unpurchased_products_count = 2,
        },
        priority = 50,
        expansion = 'undying_fury',
    },
    product_offer_heroes_16 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_16'},
        includes={'hero_witch','hero_hunter'},
        conditions={player_not_reached_level=7,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50,
        expansion = 'undying_fury',
    },

    product_offer_towers_1 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_towers_1'},
        includes={'tower_elven_stargazers','tower_barrel','tower_sand'},
        conditions={player_not_reached_level=12,
        offer_includes_unpurchased_products_count = 3,},
        priority = 50
    },
    product_offer_towers_2 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_towers_2'},
        includes={'tower_elven_stargazers','tower_barrel','tower_sand'},
        conditions={player_not_reached_level=12,
        offer_includes_unpurchased_products_count = 3,},
        priority = 50
    },
    product_offer_towers_3 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_towers_3'},
        includes={'tower_elven_stargazers','tower_necromancer','tower_ghost'},
        conditions={player_reached_level=12,
        offer_includes_unpurchased_products_count = 3,},
        priority = 50
    },
    product_offer_towers_4 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_towers_4'},
        includes={'tower_elven_stargazers','tower_necromancer','tower_ghost'},
        conditions={player_reached_level=12,
        offer_includes_unpurchased_products_count = 3,},
        priority = 50
    },
    product_offer_towers_5 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_towers_5'},
        includes={'tower_elven_stargazers','tower_necromancer'},
        conditions={player_not_reached_level=12,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_towers_6 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_towers_6'},
        includes={'tower_elven_stargazers','tower_necromancer'},
        conditions={player_not_reached_level=12,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_towers_7 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_towers_7'},
        includes={'tower_barrel','tower_sand'},
        conditions={player_not_reached_level=7,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_towers_8 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_towers_8'},
        includes={'tower_barrel','tower_sand'},
        conditions={player_not_reached_level=7,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_towers_9 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_towers_9'},
        includes={'tower_necromancer','tower_ghost'},
        conditions={player_reached_level=7,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_towers_10 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_towers_10'},
        includes={'tower_necromancer','tower_ghost'},
        conditions={player_reached_level=7,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_towers_11 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_towers_11'},
        includes={'tower_necromancer','tower_barrel'},
        conditions={player_reached_level=12,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_towers_12 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_towers_12'},
        includes={'tower_necromancer','tower_barrel'},
        conditions={player_reached_level=12,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_heroestowers_1 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_towers_1'},
        includes={'hero_mecha','hero_robot','tower_barrel'},
        conditions={player_reached_level=7,
        offer_includes_unpurchased_products_count = 3,},
        priority = 50
    },
    product_offer_heroestowers_2 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_towers_2'},
        includes={'hero_mecha','hero_robot','tower_barrel'},
        conditions={player_reached_level=7,
        offer_includes_unpurchased_products_count = 3,},
        priority = 50
    },
    product_offer_heroestowers_3 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_towers_3'},
        includes={'hero_dragon_gem','hero_lumenir','tower_ghost'},
        conditions={player_reached_level=12,
        offer_includes_unpurchased_products_count = 3,},
        priority = 50
    },
    product_offer_heroestowers_4 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_towers_4'},
        includes={'hero_dragon_gem','hero_lumenir','tower_ghost'},
        conditions={player_reached_level=12,
        offer_includes_unpurchased_products_count = 3,},
        priority = 50
    },
    product_offer_heroestowers_5 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_towers_5'},
        includes={'hero_space_elf','tower_elven_stargazers'},
        conditions={player_not_reached_level=7,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_heroestowers_6 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_towers_6'},
        includes={'hero_space_elf','tower_elven_stargazers'},
        conditions={player_not_reached_level=7,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_heroestowers_7 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_towers_7'},
        includes={'hero_builder','tower_barrel'},
        conditions={player_reached_level=7,player_not_reached_level=12,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_heroestowers_8 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_towers_8'},
        includes={'hero_builder','tower_barrel'},
        conditions={player_reached_level=7,player_not_reached_level=12,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_heroestowers_9 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_towers_9'},
        includes={'hero_hunter','tower_necromancer'},
        conditions={player_not_reached_level=16,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_heroestowers_10 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_towers_10'},
        includes={'hero_hunter','tower_necromancer'},
        conditions={player_not_reached_level=16,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_heroestowers_11 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_towers_11'},
        includes={'hero_dragon_bone','tower_dark_elf'},
        conditions={player_not_reached_level = 7,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },
    product_offer_heroestowers_12 = {
        skus={default='com.ironhidegames.kingdomrush5.special_offer_heroes_towers_12'},
        includes={'hero_dragon_bone','tower_dark_elf'},
        conditions={player_not_reached_level = 7,
        offer_includes_unpurchased_products_count = 2,},
        priority = 50
    },

    product_offer_showcase_1 = {
        consumable=true,
        skus={default='com.ironhidegames.kingdomrush5.showcase_offer_1'},
        includes_consumables={
            {name='item_scroll_of_spaceshift', count=8},
            {name='item_loot_box', count=4}
        },
        conditions={player_reached_level=1}
    },

    product_offer_showcase_2 = {
        consumable=true,
        skus={default='com.ironhidegames.kingdomrush5.showcase_offer_2'},
        includes_consumables={
            {name='item_scroll_of_spaceshift', count=8},
            {name='item_loot_box', count=4}
        },
        conditions={player_reached_level=1}
    },

    product_offer_showcase_3 = {
        consumable=true,
        skus={default='com.ironhidegames.kingdomrush5.showcase_offer_3'},
        includes_consumables={
            {name='item_summon_blackburn', count=3},
            {name='item_veznan_wrath', count=2},
            {name='gems_handful', count=1}
        },
        conditions={player_reached_level=1}
    },

    product_offer_showcase_4 = {
        consumable=true,
        skus={default='com.ironhidegames.kingdomrush5.showcase_offer_4'},
        includes_consumables={
            {name='item_summon_blackburn', count=3},
            {name='item_veznan_wrath', count=2},
            {name='gems_handful', count=1}
        },
        conditions={player_reached_level=1}
    },

    product_offer_showcase_5 = {
        consumable=true,
        skus={default='com.ironhidegames.kingdomrush5.showcase_offer_5'},
        includes_consumables={
            {name='item_second_breath', count=4},
            {name='item_medical_kit', count=4}
        },
        conditions={player_reached_level=1}
    },

    product_offer_showcase_6 = {
        consumable=true,
        skus={default='com.ironhidegames.kingdomrush5.showcase_offer_6'},
        includes_consumables={
            {name='item_second_breath', count=4},
            {name='item_medical_kit', count=4}
        },
        conditions={player_reached_level=1}
    },

    product_offer_showcase_7 = {
        consumable=true,
        skus={default='com.ironhidegames.kingdomrush5.showcase_offer_7'},
        includes_consumables={
            {name='item_deaths_touch', count=4},
            {name='item_winter_age', count=1},
            {name='item_medical_kit', count=2}
        },
        conditions={player_reached_level=1}
    },

    product_offer_showcase_8 = {
        consumable=true,
        skus={default='com.ironhidegames.kingdomrush5.showcase_offer_8'},
        includes_consumables={
            {name='item_deaths_touch', count=4},
            {name='item_winter_age', count=1},
            {name='item_medical_kit', count=2}
        },
        conditions={player_reached_level=1}
    },

    product_offer_showcase_9 = {
        consumable=true,
        skus={default='com.ironhidegames.kingdomrush5.showcase_offer_9'},
        includes_consumables={
            {name='item_loot_box', count=3},
            {name='item_medical_kit', count=4},
            {name='item_summon_blackburn', count=3}
        },
        conditions={player_reached_level=1}
    },


    product_offer_showcase_10 = {
        consumable=true,
        skus={default='com.ironhidegames.kingdomrush5.showcase_offer_10'},
        includes_consumables={
            {name='item_loot_box', count=3},
            {name='item_medical_kit', count=4},
            {name='item_summon_blackburn', count=3}
        },
        conditions={player_reached_level=1}
    },

    product_offer_showcase_11 = {
        consumable=true,
        skus={default='com.ironhidegames.kingdomrush5.showcase_offer_11'},
        includes_consumables={
            {name='item_winter_age', count=4},
            {name='item_summon_blackburn', count=2}
        },
        conditions={player_reached_level=1}
    },

    product_offer_showcase_12 = {
        consumable=true,
        skus={default='com.ironhidegames.kingdomrush5.showcase_offer_12'},
        includes_consumables={
            {name='item_winter_age', count=4},
            {name='item_summon_blackburn', count=2}
        },
        conditions={player_reached_level=1}
    },

    product_offer_showcase_15 = {
        consumable=true,
        skus={default='com.ironhidegames.kingdomrush5.showcase_offer_15'},
        includes_consumables={
            {name='item_cluster_bomb', count=4},
            {name='item_portable_coil', count=4},
            {name='item_scroll_of_spaceshift', count=4}
        },
        conditions={player_reached_level=1}
    }
}

------------------------------------------------------------
-- TESTING OVERRIDES
if bundle_id == 'net.kalio.test.android.krf' then
    -- TEST: com.ironhidegames.frontiers  --> net.kalio.test.android.krf
    -- TEST: com.ironhidegames.kingdomrush.frontiers.offer -> net.kalio.test.android.krf.offer
    for k,v in pairs(d) do
        if v and type(v)=='table' and v.skus then
            for kk,vv in pairs(v.skus) do
                if kk == 'gpiab' then
                    local nv = vv
                    nv = string.gsub(nv,'com.ironhidegames.kingdomrush.frontiers','net.kalio.test.android.krf')
                    nv = string.gsub(nv,'com.ironhidegames.frontiers','net.kalio.test.android.krf')
                    v.skus.gpiab = nv
                end
            end
        end
    end
end

return d
