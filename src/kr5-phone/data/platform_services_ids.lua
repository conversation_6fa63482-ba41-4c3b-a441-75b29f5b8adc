--
-- id mappings for the different services
-- 

require 'constants'
require 'version'
require 'klua.table'

local ids = {}  

------------------------------------------------------------
-- AppleGameCenter - Appstore US (cocos2d replacement) - iPhone!
ids.gamecenter={}

-- leaderboards metadata
-- ids.gamecenter.leaderboards_metadata = table.deepclone(ids.gamecenter_fullads_cn.leaderboards_metadata)

-- achievements metadata
ids.gamecenter.achievements_metadata = {
    -- {points, hidden}
    ["LEARNING_THE_ROPES"]          = {5, false},
    ["TIPPING_THE_SCALES"]          = {5, false},
    ["FIELD_TRIP_RUINER"]           = {5, false},
    ["ITS_A_SECRET_TO_EVERYONE"]    = {5, false},
    ["CIRCLE_OF_LIFE"]              = {5, false},
    ["PLAYFUL_FRIENDS"]             = {5, false},
    ["MOST_DELICIOUS"]              = {5, false},
    ["NATURES_WRATH"]               = {5, false},
    ["MIGHTY_I"]                    = {5, false},
    ["GREENLIT_ALLIES"]             = {5, false},
    ["OVER_THE_EDGE"]               = {5, false},
    ["CLEANUP_IS_OPTIONAL"]         = {5, false},
    ["RUNEQUEST"]                   = {5, false},
    ["NONE_SHALL_PASS"]             = {5, false},
    ["CRAFTING_IN_THE_MINES"]       = {5, false},
    ["PORKS_OFF_THE_MENU"]          = {5, false},
    ["OUTBACK_BARBEQUICK"]          = {5, false},
    ["SAVIOUR_OF_THE_GREEN"]        = {5, false},
    ["NOT_A_MOMENT_TO_WASTE"]       = {5, false},
    ["SILVER_FOR_MONSTERS"]         = {5, false},
    ["CROW_SCARER"]                 = {5, false},
    ["WE_RE_NOT_GONNA_TAKE_IT"]     = {5, false},
    ["BREAKER_OF_CHAINS"]           = {5, false},
    ["GEM_SPILLER"]                 = {5, false},
    ["UNBOUND_VICTORY"]             = {5, false},
    ["GET_THE_PARTY_STARTED"]       = {5, false},
    ["WAR_MASONRY"]                 = {5, false},
    ["PROMOTION_DENIED"]            = {5, false},
    ["STARLIGHT"]                   = {5, false},
    ["CLEANSE_THE_KING"]            = {10, false},
    ["YOU_SHALL_NOT_CAST"]          = {5, false},
    ["CRYSTAL_CLEAR"]               = {10, false},
    ["MIGHTY_II"]                   = {10, false},
    ["ALL_THE_SMALL_THINGS"]        = {5, false},
    ["THE_CAVALRY_IS_HERE"]         = {5, false},
    ["WEIRDER_THINGS"]              = {5, false},
    ["OVINE_JOURNALISM"]            = {5, false},
    ["ONE_SHOT_TOWER"]              = {5, false},
    ["CROWD_CONTROL"]               = {5, false},
    ["WOBBA_LUBBA_DUB_DUB"]         = {5, false},
    ["PEST_CONTROL"]                = {5, false},
    ["TURN_A_BLIND_EYE"]            = {5, false},
    ["TAKE_ME_HOME"]                = {5, false},
    ["BUTTERTENTACLES"]             = {5, false},
    ["BYE_BYE_BEAUTIFUL"]           = {10, false},
    ["CONJUNTIVICTORY"]             = {10, false},
    ["CONQUEROR_OF_THE_VOID"]       = {10, false},
    ["LINIREAN_RESISTANCE"]         = {5, false},
    ["DARK_RUTHLESSNESS"]           = {5, false},
    ["UNENDING_RICHES"]             = {5, false},
    ["SIGNATURE_TECHNIQUES"]        = {5, false},
    ["ROYAL_CAPTAIN"]               = {5, false},
    ["DARK_LIEUTENANT"]             = {5, false},
    ["FOREST_PROTECTOR"]            = {5, false},
    ["UNTAMED_BEAST"]               = {5, false},
    ["MIGHTY_III"]                  = {10, false},
    ["AGE_OF_HEROES"]               = {30, false},
    ["IRONCLAD"]                    = {30, false},
    ["SEASONED_GENERAL"]            = {10, false},
    ["MASTER_TACTICIAN"]            = {30, false},
    ["TREE_HUGGER"]                 = {5, false},
    ["RUST_IN_PEACE"]               = {5, false},
    ["WE_ARE_ALL_MAD_HERE"]         = {5, false},
    ["ROCK_BEATS_ROCK"]             = {5, false},
    ["SPECTRAL_FURY"]               = {10, false},
    ["SAVIOUR_OF_THE_FOREST"]       = {5, false},
    ["SMOOTH_OPER_GATOR"]           = {5, false},
    ["SEE_YA_LATER_ALLIGATOR"]      = {10, false},
    ["HAIL_TO_THE_K_BABY"]          = {5, false},
    ["SCRAMBLED_EGGS"]              = {5, false},
    ["MECHANICAL_BURNOUT"]          = {5, false},
    ["FACTORY_STRIKE"]              = {5, false},
    ["DOMO_ARIGATO"]                = {5, false},
    ["KEPT_YOU_WAITING"]            = {5, false},
    ["GIFT_OF_LIFE"]                = {5, false},
    ["GARBAGE_DISPOSAL"]            = {5, false},
    ["DISTURBING_THE_PEACE"]        = {5, false},
    ["OBLITERATE"]                  = {5, false},
    ["SHUT_YOUR_MOUTH"]             = {5, false},
    ["DLC1_WIN_BOSS"]               = {10, false},
    ["INTO_THE_OGREVERSE"]          = {5, false},
    ["A_COON_OF_SURPRISES"]         = {5, false},
    ["LUCAS_SPIDER"]                = {5, false},
    ["NO_FLY_ZONE"]                 = {5, false},
    ["ARACHNED"]                    = {10, false},
}

-- leaderboards
-- ids.gamecenter.leaderboards={
--     [81] = {
--         [DIFFICULTY_EASY]   = "endless_gnolls_casual_iphone" ,
--         [DIFFICULTY_NORMAL] = "endless_gnolls_normal_iphone" ,
--         [DIFFICULTY_HARD]   = "endless_gnolls_veteran_iphone",
--     },
--     [82] = {
--         [DIFFICULTY_EASY]   = "endless_twilight_casual_iphone" ,
--         [DIFFICULTY_NORMAL] = "endless_twilight_normal_iphone" ,
--         [DIFFICULTY_HARD]   = "endless_twilight_veteran_iphone",
--     },        
-- }

-- achievements
ids.gamecenter.achievements={
    ["LEARNING_THE_ROPES"]          = "kr5mobile.LEARNING_THE_ROPES",
    ["TIPPING_THE_SCALES"]          = "kr5mobile.TIPPING_THE_SCALES",
    ["FIELD_TRIP_RUINER"]           = "kr5mobile.FIELD_TRIP_RUINER",
    ["ITS_A_SECRET_TO_EVERYONE"]    = "kr5mobile.ITS_A_SECRET_TO_EVERYONE",
    ["CIRCLE_OF_LIFE"]              = "kr5mobile.CIRCLE_OF_LIFE",
    ["PLAYFUL_FRIENDS"]             = "kr5mobile.PLAYFUL_FRIENDS",
    ["MOST_DELICIOUS"]              = "kr5mobile.MOST_DELICIOUS",
    ["NATURES_WRATH"]               = "kr5mobile.NATURES_WRATH",
    ["MIGHTY_I"]                    = "kr5mobile.MIGHTY_I",
    ["GREENLIT_ALLIES"]             = "kr5mobile.GREENLIT_ALLIES",
    ["OVER_THE_EDGE"]               = "kr5mobile.OVER_THE_EDGE",
    ["CLEANUP_IS_OPTIONAL"]         = "kr5mobile.CLEANUP_IS_OPTIONAL",
    ["RUNEQUEST"]                   = "kr5mobile.RUNEQUEST",
    ["NONE_SHALL_PASS"]             = "kr5mobile.NONE_SHALL_PASS",
    ["CRAFTING_IN_THE_MINES"]       = "kr5mobile.CRAFTING_IN_THE_MINES",
    ["PORKS_OFF_THE_MENU"]          = "kr5mobile.PORKS_OFF_THE_MENU",
    ["OUTBACK_BARBEQUICK"]          = "kr5mobile.OUTBACK_BARBEQUICK",
    ["SAVIOUR_OF_THE_GREEN"]        = "kr5mobile.SAVIOUR_OF_THE_GREEN",
    ["NOT_A_MOMENT_TO_WASTE"]       = "kr5mobile.NOT_A_MOMENT_TO_WASTE",
    ["SILVER_FOR_MONSTERS"]         = "kr5mobile.SILVER_FOR_MONSTERS",
    ["CROW_SCARER"]                 = "kr5mobile.CROW_SCARER",
    ["WE_RE_NOT_GONNA_TAKE_IT"]     = "kr5mobile.WE_RE_NOT_GONNA_TAKE_IT",
    ["BREAKER_OF_CHAINS"]           = "kr5mobile.BREAKER_OF_CHAINS",
    ["GEM_SPILLER"]                 = "kr5mobile.GEM_SPILLER",
    ["UNBOUND_VICTORY"]             = "kr5mobile.UNBOUND_VICTORY",
    ["GET_THE_PARTY_STARTED"]       = "kr5mobile.GET_THE_PARTY_STARTED",
    ["WAR_MASONRY"]                 = "kr5mobile.WAR_MASONRY",
    ["PROMOTION_DENIED"]            = "kr5mobile.PROMOTION_DENIED",
    ["STARLIGHT"]                   = "kr5mobile.STARLIGHT",
    ["CLEANSE_THE_KING"]            = "kr5mobile.CLEANSE_THE_KING",
    ["YOU_SHALL_NOT_CAST"]          = "kr5mobile.YOU_SHALL_NOT_CAST",
    ["CRYSTAL_CLEAR"]               = "kr5mobile.CRYSTAL_CLEAR",
    ["MIGHTY_II"]                   = "kr5mobile.MIGHTY_II",
    ["ALL_THE_SMALL_THINGS"]        = "kr5mobile.ALL_THE_SMALL_THINGS",
    ["THE_CAVALRY_IS_HERE"]         = "kr5mobile.THE_CAVALRY_IS_HERE",
    ["WEIRDER_THINGS"]              = "kr5mobile.WEIRDER_THINGS",
    ["OVINE_JOURNALISM"]            = "kr5mobile.OVINE_JOURNALISM",
    ["ONE_SHOT_TOWER"]              = "kr5mobile.ONE_SHOT_TOWER",
    ["CROWD_CONTROL"]               = "kr5mobile.CROWD_CONTROL",
    ["WOBBA_LUBBA_DUB_DUB"]         = "kr5mobile.WOBBA_LUBBA_DUB_DUB",
    ["PEST_CONTROL"]                = "kr5mobile.PEST_CONTROL",
    ["TURN_A_BLIND_EYE"]            = "kr5mobile.TURN_A_BLIND_EYE",
    ["TAKE_ME_HOME"]                = "kr5mobile.TAKE_ME_HOME",
    ["BUTTERTENTACLES"]             = "kr5mobile.BUTTERTENTACLES",
    ["BYE_BYE_BEAUTIFUL"]           = "kr5mobile.BYE_BYE_BEAUTIFUL",
    ["CONJUNTIVICTORY"]             = "kr5mobile.CONJUNTIVICTORY",
    ["CONQUEROR_OF_THE_VOID"]       = "kr5mobile.CONQUEROR_OF_THE_VOID",
    ["LINIREAN_RESISTANCE"]         = "kr5mobile.LINIREAN_RESISTANCE",
    ["DARK_RUTHLESSNESS"]           = "kr5mobile.DARK_RUTHLESSNESS",
    ["UNENDING_RICHES"]             = "kr5mobile.UNENDING_RICHES",
    ["SIGNATURE_TECHNIQUES"]        = "kr5mobile.SIGNATURE_TECHNIQUES",
    ["ROYAL_CAPTAIN"]               = "kr5mobile.ROYAL_CAPTAIN",
    ["DARK_LIEUTENANT"]             = "kr5mobile.DARK_LIEUTENANT",
    ["FOREST_PROTECTOR"]            = "kr5mobile.FOREST_PROTECTOR",
    ["UNTAMED_BEAST"]               = "kr5mobile.UNTAMED_BEAST",
    ["MIGHTY_III"]                  = "kr5mobile.MIGHTY_III",
    ["AGE_OF_HEROES"]               = "kr5mobile.AGE_OF_HEROES",
    ["IRONCLAD"]                    = "kr5mobile.IRONCLAD",
    ["SEASONED_GENERAL"]            = "kr5mobile.SEASONED_GENERAL",
    ["MASTER_TACTICIAN"]            = "kr5mobile.MASTER_TACTICIAN",
    ["TREE_HUGGER"]                 = "kr5mobile.TREE_HUGGER",
    ["RUST_IN_PEACE"]               = "kr5mobile.RUST_IN_PEACE",
    ["WE_ARE_ALL_MAD_HERE"]         = "kr5mobile.WE_ARE_ALL_MAD_HERE",
    ["ROCK_BEATS_ROCK"]             = "kr5mobile.ROCK_BEATS_ROCK",
    ["SPECTRAL_FURY"]               = "kr5mobile.SPECTRAL_FURY",
    ["SAVIOUR_OF_THE_FOREST"]       = "kr5mobile.SAVIOUR_OF_THE_FOREST",
    ["SMOOTH_OPER_GATOR"]           = "kr5mobile.SMOOTH_OPER_GATOR",
    ["SEE_YA_LATER_ALLIGATOR"]      = "kr5mobile.SEE_YA_LATER_ALLIGATOR",
    ["HAIL_TO_THE_K_BABY"]          = "kr5mobile.HAIL_TO_THE_K_BABY",
    ["SCRAMBLED_EGGS"]              = "kr5mobile.SCRAMBLED_EGGS",
    ["MECHANICAL_BURNOUT"]          = "kr5mobile.MECHANICAL_BURNOUT",
    ["FACTORY_STRIKE"]              = "kr5mobile.FACTORY_STRIKE",
    ["DOMO_ARIGATO"]                = "kr5mobile.DOMO_ARIGATO",
    ["KEPT_YOU_WAITING"]            = "kr5mobile.KEPT_YOU_WAITING",
    ["GIFT_OF_LIFE"]                = "kr5mobile.GIFT_OF_LIFE",
    ["GARBAGE_DISPOSAL"]            = "kr5mobile.GARBAGE_DISPOSAL",
    ["DISTURBING_THE_PEACE"]        = "kr5mobile.DISTURBING_THE_PEACE",
    ["OBLITERATE"]                  = "kr5mobile.OBLITERATE",
    ["SHUT_YOUR_MOUTH"]             = "kr5mobile.SHUT_YOUR_MOUTH",
    ["DLC1_WIN_BOSS"]               = "kr5mobile.DLC1_WIN_BOSS",
    ["INTO_THE_OGREVERSE"]          = "kr5mobile.INTO_THE_OGREVERSE",
    ["A_COON_OF_SURPRISES"]         = "kr5mobile.A_COON_OF_SURPRISES",
    ["LUCAS_SPIDER"]                = "kr5mobile.LUCAS_SPIDER",
    ["NO_FLY_ZONE"]                 = "kr5mobile.NO_FLY_ZONE",
    ["ARACHNED"]                    = "kr5mobile.ARACHNED",
}

ids.gamecenter_universal_premium={}

--AppleGameCenter/Achievements
ids.gamecenter_universal_premium.achievements={
    ["LEARNING_THE_ROPES"]          = "kr5.universal.premium.LEARNING_THE_ROPES",
    ["TIPPING_THE_SCALES"]          = "kr5.universal.premium.TIPPING_THE_SCALES",
    ["FIELD_TRIP_RUINER"]           = "kr5.universal.premium.FIELD_TRIP_RUINER",
    ["ITS_A_SECRET_TO_EVERYONE"]    = "kr5.universal.premium.ITS_A_SECRET_TO_EVERYONE",
    ["CIRCLE_OF_LIFE"]              = "kr5.universal.premium.CIRCLE_OF_LIFE",
    ["PLAYFUL_FRIENDS"]             = "kr5.universal.premium.PLAYFUL_FRIENDS",
    ["MOST_DELICIOUS"]              = "kr5.universal.premium.MOST_DELICIOUS",
    ["NATURES_WRATH"]               = "kr5.universal.premium.NATURES_WRATH",
    ["MIGHTY_I"]                    = "kr5.universal.premium.MIGHTY_I",
    ["GREENLIT_ALLIES"]             = "kr5.universal.premium.GREENLIT_ALLIES",
    ["OVER_THE_EDGE"]               = "kr5.universal.premium.OVER_THE_EDGE",
    ["CLEANUP_IS_OPTIONAL"]         = "kr5.universal.premium.CLEANUP_IS_OPTIONAL",
    ["RUNEQUEST"]                   = "kr5.universal.premium.RUNEQUEST",
    ["NONE_SHALL_PASS"]             = "kr5.universal.premium.NONE_SHALL_PASS",
    ["CRAFTING_IN_THE_MINES"]       = "kr5.universal.premium.CRAFTING_IN_THE_MINES",
    ["PORKS_OFF_THE_MENU"]          = "kr5.universal.premium.PORKS_OFF_THE_MENU",
    ["OUTBACK_BARBEQUICK"]          = "kr5.universal.premium.OUTBACK_BARBEQUICK",
    ["SAVIOUR_OF_THE_GREEN"]        = "kr5.universal.premium.SAVIOUR_OF_THE_GREEN",
    ["NOT_A_MOMENT_TO_WASTE"]       = "kr5.universal.premium.NOT_A_MOMENT_TO_WASTE",
    ["SILVER_FOR_MONSTERS"]         = "kr5.universal.premium.SILVER_FOR_MONSTERS",
    ["CROW_SCARER"]                 = "kr5.universal.premium.CROW_SCARER",
    ["WE_RE_NOT_GONNA_TAKE_IT"]     = "kr5.universal.premium.WE_RE_NOT_GONNA_TAKE_IT",
    ["BREAKER_OF_CHAINS"]           = "kr5.universal.premium.BREAKER_OF_CHAINS",
    ["GEM_SPILLER"]                 = "kr5.universal.premium.GEM_SPILLER",
    ["UNBOUND_VICTORY"]             = "kr5.universal.premium.UNBOUND_VICTORY",
    ["GET_THE_PARTY_STARTED"]       = "kr5.universal.premium.GET_THE_PARTY_STARTED",
    ["WAR_MASONRY"]                 = "kr5.universal.premium.WAR_MASONRY",
    ["PROMOTION_DENIED"]            = "kr5.universal.premium.PROMOTION_DENIED",
    ["STARLIGHT"]                   = "kr5.universal.premium.STARLIGHT",
    ["CLEANSE_THE_KING"]            = "kr5.universal.premium.CLEANSE_THE_KING",
    ["YOU_SHALL_NOT_CAST"]          = "kr5.universal.premium.YOU_SHALL_NOT_CAST",
    ["CRYSTAL_CLEAR"]               = "kr5.universal.premium.CRYSTAL_CLEAR",
    ["MIGHTY_II"]                   = "kr5.universal.premium.MIGHTY_II",
    ["ALL_THE_SMALL_THINGS"]        = "kr5.universal.premium.ALL_THE_SMALL_THINGS",
    ["THE_CAVALRY_IS_HERE"]         = "kr5.universal.premium.THE_CAVALRY_IS_HERE",
    ["WEIRDER_THINGS"]              = "kr5.universal.premium.WEIRDER_THINGS",
    ["OVINE_JOURNALISM"]            = "kr5.universal.premium.OVINE_JOURNALISM",
    ["ONE_SHOT_TOWER"]              = "kr5.universal.premium.ONE_SHOT_TOWER",
    ["CROWD_CONTROL"]               = "kr5.universal.premium.CROWD_CONTROL",
    ["WOBBA_LUBBA_DUB_DUB"]         = "kr5.universal.premium.WOBBA_LUBBA_DUB_DUB",
    ["PEST_CONTROL"]                = "kr5.universal.premium.PEST_CONTROL",
    ["TURN_A_BLIND_EYE"]            = "kr5.universal.premium.TURN_A_BLIND_EYE",
    ["TAKE_ME_HOME"]                = "kr5.universal.premium.TAKE_ME_HOME",
    ["BUTTERTENTACLES"]             = "kr5.universal.premium.BUTTERTENTACLES",
    ["BYE_BYE_BEAUTIFUL"]           = "kr5.universal.premium.BYE_BYE_BEAUTIFUL",
    ["CONJUNTIVICTORY"]             = "kr5.universal.premium.CONJUNTIVICTORY",
    ["CONQUEROR_OF_THE_VOID"]       = "kr5.universal.premium.CONQUEROR_OF_THE_VOID",
    ["LINIREAN_RESISTANCE"]         = "kr5.universal.premium.LINIREAN_RESISTANCE",
    ["DARK_RUTHLESSNESS"]           = "kr5.universal.premium.DARK_RUTHLESSNESS",
    ["UNENDING_RICHES"]             = "kr5.universal.premium.UNENDING_RICHES",
    ["SIGNATURE_TECHNIQUES"]        = "kr5.universal.premium.SIGNATURE_TECHNIQUES",
    ["ROYAL_CAPTAIN"]               = "kr5.universal.premium.ROYAL_CAPTAIN",
    ["DARK_LIEUTENANT"]             = "kr5.universal.premium.DARK_LIEUTENANT",
    ["FOREST_PROTECTOR"]            = "kr5.universal.premium.FOREST_PROTECTOR",
    ["UNTAMED_BEAST"]               = "kr5.universal.premium.UNTAMED_BEAST",
    ["MIGHTY_III"]                  = "kr5.universal.premium.MIGHTY_III",
    ["AGE_OF_HEROES"]               = "kr5.universal.premium.AGE_OF_HEROES",
    ["IRONCLAD"]                    = "kr5.universal.premium.IRONCLAD",
    ["SEASONED_GENERAL"]            = "kr5.universal.premium.SEASONED_GENERAL",
    ["MASTER_TACTICIAN"]            = "kr5.universal.premium.MASTER_TACTICIAN",
    ["TREE_HUGGER"]                 = "kr5.universal.premium.TREE_HUGGER",
    ["RUST_IN_PEACE"]               = "kr5.universal.premium.RUST_IN_PEACE",
    ["WE_ARE_ALL_MAD_HERE"]         = "kr5.universal.premium.WE_ARE_ALL_MAD_HERE",
    ["ROCK_BEATS_ROCK"]             = "kr5.universal.premium.ROCK_BEATS_ROCK",
    ["SPECTRAL_FURY"]               = "kr5.universal.premium.SPECTRAL_FURY",
    ["SAVIOUR_OF_THE_FOREST"]       = "kr5.universal.premium.SAVIOUR_OF_THE_FOREST",
    ["SMOOTH_OPER_GATOR"]           = "kr5.universal.premium.SMOOTH_OPER_GATOR",
    ["SEE_YA_LATER_ALLIGATOR"]      = "kr5.universal.premium.SEE_YA_LATER_ALLIGATOR",
    ["HAIL_TO_THE_K_BABY"]          = "kr5.universal.premium.HAIL_TO_THE_K_BABY",
    ["SCRAMBLED_EGGS"]              = "kr5.universal.premium.SCRAMBLED_EGGS",
    ["MECHANICAL_BURNOUT"]          = "kr5.universal.premium.MECHANICAL_BURNOUT",
    ["FACTORY_STRIKE"]              = "kr5.universal.premium.FACTORY_STRIKE",
    ["DOMO_ARIGATO"]                = "kr5.universal.premium.DOMO_ARIGATO",
    ["KEPT_YOU_WAITING"]            = "kr5.universal.premium.KEPT_YOU_WAITING",
    ["GIFT_OF_LIFE"]                = "kr5.universal.premium.GIFT_OF_LIFE",
    ["GARBAGE_DISPOSAL"]            = "kr5.universal.premium.GARBAGE_DISPOSAL",
    ["DISTURBING_THE_PEACE"]        = "kr5.universal.premium.DISTURBING_THE_PEACE",
    ["OBLITERATE"]                  = "kr5.universal.premium.OBLITERATE",
    ["SHUT_YOUR_MOUTH"]             = "kr5.universal.premium.SHUT_YOUR_MOUTH",
    ["DLC1_WIN_BOSS"]               = "kr5.universal.premium.DLC1_WIN_BOSS",
    ["INTO_THE_OGREVERSE"]          = "kr5.universal.premium.INTO_THE_OGREVERSE",
    ["A_COON_OF_SURPRISES"]         = "kr5.universal.premium.A_COON_OF_SURPRISES",
    ["LUCAS_SPIDER"]                = "kr5.universal.premium.LUCAS_SPIDER",
    ["NO_FLY_ZONE"]                 = "kr5.universal.premium.NO_FLY_ZONE",
    ["ARACHNED"]                    = "kr5.universal.premium.ARACHNED",
}

-- metadata in game center (used for export)
ids.gamecenter_universal_premium.achievements_metadata =  table.deepclone(ids.gamecenter.achievements_metadata)

------------------------------------------------------------
-- Google Play Services
ids.gps = {}

-- Google Play Services / Achievements

ids.gps.achievements = {
    ["LEARNING_THE_ROPES"]          = "CgkI3IL01pEEEAIQBA",
    ["TIPPING_THE_SCALES"]          = "CgkI3IL01pEEEAIQBQ",
    ["FIELD_TRIP_RUINER"]           = "CgkI3IL01pEEEAIQBg",
    ["ITS_A_SECRET_TO_EVERYONE"]    = "CgkI3IL01pEEEAIQBw",
    ["CIRCLE_OF_LIFE"]              = "CgkI3IL01pEEEAIQCA",
    ["PLAYFUL_FRIENDS"]             = "CgkI3IL01pEEEAIQCQ",
    ["MOST_DELICIOUS"]              = "CgkI3IL01pEEEAIQCg",
    ["NATURES_WRATH"]               = "CgkI3IL01pEEEAIQCw",
    ["MIGHTY_I"]                    = "CgkI3IL01pEEEAIQDA",
    ["GREENLIT_ALLIES"]             = "CgkI3IL01pEEEAIQDQ",
    ["OVER_THE_EDGE"]               = "CgkI3IL01pEEEAIQDg",
    ["CLEANUP_IS_OPTIONAL"]         = "CgkI3IL01pEEEAIQDw",
    ["RUNEQUEST"]                   = "CgkI3IL01pEEEAIQEA",
    ["NONE_SHALL_PASS"]             = "CgkI3IL01pEEEAIQEQ",
    ["CRAFTING_IN_THE_MINES"]       = "CgkI3IL01pEEEAIQEg",
    ["PORKS_OFF_THE_MENU"]          = "CgkI3IL01pEEEAIQEw",
    ["OUTBACK_BARBEQUICK"]          = "CgkI3IL01pEEEAIQFA",
    ["SAVIOUR_OF_THE_GREEN"]        = "CgkI3IL01pEEEAIQFQ",
    ["NOT_A_MOMENT_TO_WASTE"]       = "CgkI3IL01pEEEAIQFg",
    ["SILVER_FOR_MONSTERS"]         = "CgkI3IL01pEEEAIQFw",
    ["CROW_SCARER"]                 = "CgkI3IL01pEEEAIQGA",
    ["WE_RE_NOT_GONNA_TAKE_IT"]     = "CgkI3IL01pEEEAIQGQ",
    ["BREAKER_OF_CHAINS"]           = "CgkI3IL01pEEEAIQGg",
    ["GEM_SPILLER"]                 = "CgkI3IL01pEEEAIQGw",
    ["UNBOUND_VICTORY"]             = "CgkI3IL01pEEEAIQHA",
    ["GET_THE_PARTY_STARTED"]       = "CgkI3IL01pEEEAIQHQ",
    ["WAR_MASONRY"]                 = "CgkI3IL01pEEEAIQHg",
    ["PROMOTION_DENIED"]            = "CgkI3IL01pEEEAIQHw",
    ["STARLIGHT"]                   = "CgkI3IL01pEEEAIQIA",
    ["CLEANSE_THE_KING"]            = "CgkI3IL01pEEEAIQIQ",
    ["YOU_SHALL_NOT_CAST"]          = "CgkI3IL01pEEEAIQIg",
    ["CRYSTAL_CLEAR"]               = "CgkI3IL01pEEEAIQIw",
    ["MIGHTY_II"]                   = "CgkI3IL01pEEEAIQJA",
    ["ALL_THE_SMALL_THINGS"]        = "CgkI3IL01pEEEAIQJQ",
    ["THE_CAVALRY_IS_HERE"]         = "CgkI3IL01pEEEAIQJg",
    ["WEIRDER_THINGS"]              = "CgkI3IL01pEEEAIQJw",
    ["OVINE_JOURNALISM"]            = "CgkI3IL01pEEEAIQKA",
    ["ONE_SHOT_TOWER"]              = "CgkI3IL01pEEEAIQKQ",
    ["CROWD_CONTROL"]               = "CgkI3IL01pEEEAIQKg",
    ["WOBBA_LUBBA_DUB_DUB"]         = "CgkI3IL01pEEEAIQKw",
    ["PEST_CONTROL"]                = "CgkI3IL01pEEEAIQLA",
    ["TURN_A_BLIND_EYE"]            = "CgkI3IL01pEEEAIQLQ",
    ["TAKE_ME_HOME"]                = "CgkI3IL01pEEEAIQLg",
    ["BUTTERTENTACLES"]             = "CgkI3IL01pEEEAIQLw",
    ["BYE_BYE_BEAUTIFUL"]           = "CgkI3IL01pEEEAIQMA",
    ["CONJUNTIVICTORY"]             = "CgkI3IL01pEEEAIQMQ",
    ["CONQUEROR_OF_THE_VOID"]       = "CgkI3IL01pEEEAIQMg",
    ["LINIREAN_RESISTANCE"]         = "CgkI3IL01pEEEAIQMw",
    ["DARK_RUTHLESSNESS"]           = "CgkI3IL01pEEEAIQNA",
    ["UNENDING_RICHES"]             = "CgkI3IL01pEEEAIQNQ",
    ["SIGNATURE_TECHNIQUES"]        = "CgkI3IL01pEEEAIQNg",
    ["ROYAL_CAPTAIN"]               = "CgkI3IL01pEEEAIQNw",
    ["DARK_LIEUTENANT"]             = "CgkI3IL01pEEEAIQOA",
    ["FOREST_PROTECTOR"]            = "CgkI3IL01pEEEAIQOQ",
    ["UNTAMED_BEAST"]               = "CgkI3IL01pEEEAIQOg",
    ["MIGHTY_III"]                  = "CgkI3IL01pEEEAIQOw",
    ["AGE_OF_HEROES"]               = "CgkI3IL01pEEEAIQPA",
    ["IRONCLAD"]                    = "CgkI3IL01pEEEAIQPQ",
    ["SEASONED_GENERAL"]            = "CgkI3IL01pEEEAIQPg",
    ["MASTER_TACTICIAN"]            = "CgkI3IL01pEEEAIQPw",
    ["TREE_HUGGER"]                 = "CgkI3IL01pEEEAIQQQ",
    ["RUST_IN_PEACE"]               = "CgkI3IL01pEEEAIQQg",
    ["WE_ARE_ALL_MAD_HERE"]         = "CgkI3IL01pEEEAIQQw",
    ["ROCK_BEATS_ROCK"]             = "CgkI3IL01pEEEAIQRA",
    ["SPECTRAL_FURY"]               = "CgkI3IL01pEEEAIQRQ",
    ["SAVIOUR_OF_THE_FOREST"]       = "CgkI3IL01pEEEAIQRg",
    ["SMOOTH_OPER_GATOR"]           = "CgkI3IL01pEEEAIQRw",
    ["SEE_YA_LATER_ALLIGATOR"]      = "CgkI3IL01pEEEAIQSA",
    ["HAIL_TO_THE_K_BABY"]          = "CgkI3IL01pEEEAIQSQ",
    ["SCRAMBLED_EGGS"]              = "CgkI3IL01pEEEAIQSg",     
    ["MECHANICAL_BURNOUT"]          = "CgkI3IL01pEEEAIQSw",
    ["FACTORY_STRIKE"]              = "CgkI3IL01pEEEAIQTA",
    ["DOMO_ARIGATO"]                = "CgkI3IL01pEEEAIQTQ",
    ["KEPT_YOU_WAITING"]            = "CgkI3IL01pEEEAIQTg",
    ["GIFT_OF_LIFE"]                = "CgkI3IL01pEEEAIQTw",
    ["GARBAGE_DISPOSAL"]            = "CgkI3IL01pEEEAIQUA",
    ["DISTURBING_THE_PEACE"]        = "CgkI3IL01pEEEAIQUQ",
    ["OBLITERATE"]                  = "CgkI3IL01pEEEAIQUg",
    ["SHUT_YOUR_MOUTH"]             = "CgkI3IL01pEEEAIQUw",
    ["DLC1_WIN_BOSS"]               = "CgkI3IL01pEEEAIQVA",
    ["INTO_THE_OGREVERSE"]          = "CgkI3IL01pEEEAIQVQ",
    ["A_COON_OF_SURPRISES"]         = "CgkI3IL01pEEEAIQVg",
    ["LUCAS_SPIDER"]                = "CgkI3IL01pEEEAIQVw",
    ["NO_FLY_ZONE"]                 = "CgkI3IL01pEEEAIQWA",
    ["ARACHNED"]                    = "CgkI3IL01pEEEAIQWQ",
}


ids.gps.achievements_metadata = table.deepclone(ids.gamecenter.achievements_metadata)


------------------------------------------------------------
------------------------------------------------------------
-- TESTING OVERRIDES

--if version.bundle_id == 'net.kalio.test.android.krf' then
--    ids.gps.achievements['FIRST_BLOOD']       = "CgkIoLyo3f4LEAIQAQ" -- "CgkIio3Ql5UKEAIQAQ",
--    ids.gps.achievements['ANDSOITBEGINS']     = "CgkIoLyo3f4LEAIQAg" -- "CgkIio3Ql5UKEAIQAg", 
--    ids.gps.leaderboards[81][DIFFICULTY_EASY] = "CgkIoLyo3f4LEAIQAw" -- "CgkIio3Ql5UKEAIQWw",
--    ids.gps.leaderboards[82][DIFFICULTY_EASY] = "CgkIoLyo3f4LEAIQBA" -- "CgkIio3Ql5UKEAIQXg",
--end

------------------------------------------------------------
return ids
