return
{class='KWindow', size={x=ctx.sw,y=ctx.sh}, children = {

-- Layer_6
{class='KView', id='popup_dlc_desktop', pos=v(ctx.sw/2,512.5), size=v(ctx.sw,ctx.sh), context=ctx.context, UNLESS=ctx.is_mobile, template_name='popup_dlc_desktop'},
-- Layer_4
{class='KView', id='group_shop_room_cards_container', pos=v(893.75,358.95), WHEN=false, children={
     {class='KView', id='group_shop_offers', pos=v(185.55,24.4), template_name='group_shop_offers'},
     {class='KView', id='group_shop_gems_portrait', pos=v(-464.65,7.1), template_name='group_shop_gems_portrait'},
     {class='KView', id='group_shop_offers_x2', pos=v(185.55,24.4), template_name='group_shop_offers_x2'},
     {class='KView', id='group_shop_offers_halloween', pos=v(185.55,24.4), template_name='group_shop_offers_halloween'},
     {class='KView', id='group_shop_offers_crocs', pos=v(185.55,24.4), template_name='group_shop_offers_crocs'},
     {class='KView', id='group_shop_offers_dlc_1', pos=v(184.65,24.4), template_name='group_shop_offers_dlc_1'},
     {class='KView', id='group_shop_offers_spiders', pos=v(185.55,24.4), template_name='group_shop_offers_spiders'},
}},
-- Layer_1
{class='KView', id='group_shop_gems', pos=v(ctx.sw/2,0), transition='down', transition_delay=0.25, WHEN=ctx.is_mobile, children={
     {class='KView', id='group_shop_total_gems', pos=v(-481.25,39.65), template_name='group_shop_total_gems'},
}},
{class='KView', id='group_shop_done', pos=v(ctx.sw/2,0), transition='up', transition_delay=0.25, WHEN=ctx.is_mobile, children={
     {class='KView', id='group_shop_room_done', pos=v(-0.55,0.9), template_name='group_shop_room_done'},
}},


}}
