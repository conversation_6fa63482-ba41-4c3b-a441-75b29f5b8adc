--
-- id mappings for the different services
-- 

require 'constants'
require 'version'

local ids = {}

------------------------------------------------------------
-- Apple Game Center
ids.gamecenter = {}

-- Apple Game Center / Achievements
ids.gamecenter.achievements = {    
    ["LEARNING_THE_ROPES"]          = "kr5mac.LEARNING_THE_ROPES",
    ["TIPPING_THE_SCALES"]          = "kr5mac.TIPPING_THE_SCALES",
    ["FIELD_TRIP_RUINER"]           = "kr5mac.FIELD_TRIP_RUINER",
    ["ITS_A_SECRET_TO_EVERYONE"]    = "kr5mac.ITS_A_SECRET_TO_EVERYONE",
    ["CIRCLE_OF_LIFE"]              = "kr5mac.CIRCLE_OF_LIFE",
    ["PLAYFUL_FRIENDS"]             = "kr5mac.PLAYFUL_FRIENDS",
    ["MOST_DELICIOUS"]              = "kr5mac.MOST_DELICIOUS",
    ["NATURES_WRATH"]               = "kr5mac.NATURES_WRATH",
    ["MIGHTY_I"]                    = "kr5mac.MIGHTY_I",
    ["GREENLIT_ALLIES"]             = "kr5mac.GREENLIT_ALLIES",
    ["OVER_THE_EDGE"]               = "kr5mac.OVER_THE_EDGE",
    ["CLEANUP_IS_OPTIONAL"]         = "kr5mac.CLEANUP_IS_OPTIONAL",
    ["RUNEQUEST"]                   = "kr5mac.RUNEQUEST",
    ["NONE_SHALL_PASS"]             = "kr5mac.NONE_SHALL_PASS",
    ["CRAFTING_IN_THE_MINES"]       = "kr5mac.CRAFTING_IN_THE_MINES",
    ["PORKS_OFF_THE_MENU"]          = "kr5mac.PORKS_OFF_THE_MENU",
    ["OUTBACK_BARBEQUICK"]          = "kr5mac.OUTBACK_BARBEQUICK",
    ["SAVIOUR_OF_THE_GREEN"]        = "kr5mac.SAVIOUR_OF_THE_GREEN",
    ["NOT_A_MOMENT_TO_WASTE"]       = "kr5mac.NOT_A_MOMENT_TO_WASTE",
    ["SILVER_FOR_MONSTERS"]         = "kr5mac.SILVER_FOR_MONSTERS",
    ["CROW_SCARER"]                 = "kr5mac.CROW_SCARER",
    ["WE_RE_NOT_GONNA_TAKE_IT"]     = "kr5mac.WE_RE_NOT_GONNA_TAKE_IT",
    ["BREAKER_OF_CHAINS"]           = "kr5mac.BREAKER_OF_CHAINS",
    ["GEM_SPILLER"]                 = "kr5mac.GEM_SPILLER",
    ["UNBOUND_VICTORY"]             = "kr5mac.UNBOUND_VICTORY",
    ["GET_THE_PARTY_STARTED"]       = "kr5mac.GET_THE_PARTY_STARTED",
    ["WAR_MASONRY"]                 = "kr5mac.WAR_MASONRY",
    ["PROMOTION_DENIED"]            = "kr5mac.PROMOTION_DENIED",
    ["STARLIGHT"]                   = "kr5mac.STARLIGHT",
    ["CLEANSE_THE_KING"]            = "kr5mac.CLEANSE_THE_KING",
    ["YOU_SHALL_NOT_CAST"]          = "kr5mac.YOU_SHALL_NOT_CAST",
    ["CRYSTAL_CLEAR"]               = "kr5mac.CRYSTAL_CLEAR",
    ["MIGHTY_II"]                   = "kr5mac.MIGHTY_II",
    ["ALL_THE_SMALL_THINGS"]        = "kr5mac.ALL_THE_SMALL_THINGS",
    ["THE_CAVALRY_IS_HERE"]         = "kr5mac.THE_CAVALRY_IS_HERE",
    ["WEIRDER_THINGS"]              = "kr5mac.WEIRDER_THINGS",
    ["OVINE_JOURNALISM"]            = "kr5mac.OVINE_JOURNALISM",
    ["ONE_SHOT_TOWER"]              = "kr5mac.ONE_SHOT_TOWER",
    ["CROWD_CONTROL"]               = "kr5mac.CROWD_CONTROL",
    ["WOBBA_LUBBA_DUB_DUB"]         = "kr5mac.WOBBA_LUBBA_DUB_DUB",
    ["PEST_CONTROL"]                = "kr5mac.PEST_CONTROL",
    ["TURN_A_BLIND_EYE"]            = "kr5mac.TURN_A_BLIND_EYE",
    ["TAKE_ME_HOME"]                = "kr5mac.TAKE_ME_HOME",
    ["BUTTERTENTACLES"]             = "kr5mac.BUTTERTENTACLES",
    ["BYE_BYE_BEAUTIFUL"]           = "kr5mac.BYE_BYE_BEAUTIFUL",
    ["CONJUNTIVICTORY"]             = "kr5mac.CONJUNTIVICTORY",
    ["CONQUEROR_OF_THE_VOID"]       = "kr5mac.CONQUEROR_OF_THE_VOID",
    ["LINIREAN_RESISTANCE"]         = "kr5mac.LINIREAN_RESISTANCE",
    ["DARK_RUTHLESSNESS"]           = "kr5mac.DARK_RUTHLESSNESS",
    ["UNENDING_RICHES"]             = "kr5mac.UNENDING_RICHES",
    ["SIGNATURE_TECHNIQUES"]        = "kr5mac.SIGNATURE_TECHNIQUES",
    ["ROYAL_CAPTAIN"]               = "kr5mac.ROYAL_CAPTAIN",
    ["DARK_LIEUTENANT"]             = "kr5mac.DARK_LIEUTENANT",
    ["FOREST_PROTECTOR"]            = "kr5mac.FOREST_PROTECTOR",
    ["UNTAMED_BEAST"]               = "kr5mac.UNTAMED_BEAST",
    ["MIGHTY_III"]                  = "kr5mac.MIGHTY_III",
    ["AGE_OF_HEROES"]               = "kr5mac.AGE_OF_HEROES",
    ["IRONCLAD"]                    = "kr5mac.IRONCLAD",
    ["SEASONED_GENERAL"]            = "kr5mac.SEASONED_GENERAL",
    ["MASTER_TACTICIAN"]            = "kr5mac.MASTER_TACTICIAN",
    ["TREE_HUGGER"]                 = "kr5mac.TREE_HUGGER",
    ["RUST_IN_PEACE"]               = "kr5mac.RUST_IN_PEACE",
    ["WE_ARE_ALL_MAD_HERE"]         = "kr5mac.WE_ARE_ALL_MAD_HERE",
    ["ROCK_BEATS_ROCK"]             = "kr5mac.ROCK_BEATS_ROCK",
    ["SPECTRAL_FURY"]               = "kr5mac.SPECTRAL_FURY",
    ["SAVIOUR_OF_THE_FOREST"]       = "kr5mac.SAVIOUR_OF_THE_FOREST",
    ["SMOOTH_OPER_GATOR"]           = "kr5mac.SMOOTH_OPER_GATOR",
    ["SEE_YA_LATER_ALLIGATOR"]      = "kr5mac.SEE_YA_LATER_ALLIGATOR",
    ["HAIL_TO_THE_K_BABY"]          = "kr5mac.HAIL_TO_THE_K_BABY",
    ["SCRAMBLED_EGGS"]              = "kr5mac.SCRAMBLED_EGGS",
    ["MECHANICAL_BURNOUT"]          = "kr5mac.MECHANICAL_BURNOUT",
    ["FACTORY_STRIKE"]              = "kr5mac.FACTORY_STRIKE",
    ["DOMO_ARIGATO"]                = "kr5mac.DOMO_ARIGATO",
    ["KEPT_YOU_WAITING"]            = "kr5mac.KEPT_YOU_WAITING",
    ["GIFT_OF_LIFE"]                = "kr5mac.GIFT_OF_LIFE",
    ["GARBAGE_DISPOSAL"]            = "kr5mac.GARBAGE_DISPOSAL",
    ["DISTURBING_THE_PEACE"]        = "kr5mac.DISTURBING_THE_PEACE",
    ["OBLITERATE"]                  = "kr5mac.OBLITERATE",
    ["SHUT_YOUR_MOUTH"]             = "kr5mac.SHUT_YOUR_MOUTH",
    ["DLC1_WIN_BOSS"]               = "kr5mac.DLC1_WIN_BOSS",
    ["INTO_THE_OGREVERSE"]          = "kr5mac.INTO_THE_OGREVERSE",
    ["A_COON_OF_SURPRISES"]         = "kr5mac.A_COON_OF_SURPRISES",
    ["LUCAS_SPIDER"]                = "kr5mac.LUCAS_SPIDER",
    ["NO_FLY_ZONE"]                 = "kr5mac.NO_FLY_ZONE",
    ["ARACHNED"]                    = "kr5mac.ARACHNED",
}    

-- metadata in gamecenter (used for export)
ids.gamecenter.achievements_metadata = {
    -- {points, hidden}
    ["LEARNING_THE_ROPES"]          = {5, false},
    ["TIPPING_THE_SCALES"]          = {5, false},
    ["FIELD_TRIP_RUINER"]           = {5, false},
    ["ITS_A_SECRET_TO_EVERYONE"]    = {5, false},
    ["CIRCLE_OF_LIFE"]              = {5, false},
    ["PLAYFUL_FRIENDS"]             = {5, false},
    ["MOST_DELICIOUS"]              = {5, false},
    ["NATURES_WRATH"]               = {5, false},
    ["MIGHTY_I"]                    = {5, false},
    ["GREENLIT_ALLIES"]             = {5, false},
    ["OVER_THE_EDGE"]               = {5, false},
    ["CLEANUP_IS_OPTIONAL"]         = {5, false},
    ["RUNEQUEST"]                   = {5, false},
    ["NONE_SHALL_PASS"]             = {5, false},
    ["CRAFTING_IN_THE_MINES"]       = {5, false},
    ["PORKS_OFF_THE_MENU"]          = {5, false},
    ["OUTBACK_BARBEQUICK"]          = {5, false},
    ["SAVIOUR_OF_THE_GREEN"]        = {5, false},
    ["NOT_A_MOMENT_TO_WASTE"]       = {5, false},
    ["SILVER_FOR_MONSTERS"]         = {5, false},
    ["CROW_SCARER"]                 = {5, false},
    ["WE_RE_NOT_GONNA_TAKE_IT"]     = {5, false},
    ["BREAKER_OF_CHAINS"]           = {5, false},
    ["GEM_SPILLER"]                 = {5, false},
    ["UNBOUND_VICTORY"]             = {5, false},
    ["GET_THE_PARTY_STARTED"]       = {5, false},
    ["WAR_MASONRY"]                 = {5, false},
    ["PROMOTION_DENIED"]            = {5, false},
    ["STARLIGHT"]                   = {5, false},
    ["CLEANSE_THE_KING"]            = {10, false},
    ["YOU_SHALL_NOT_CAST"]          = {5, false},
    ["CRYSTAL_CLEAR"]               = {10, false},
    ["MIGHTY_II"]                   = {10, false},
    ["ALL_THE_SMALL_THINGS"]        = {5, false},
    ["THE_CAVALRY_IS_HERE"]         = {5, false},
    ["WEIRDER_THINGS"]              = {5, false},
    ["OVINE_JOURNALISM"]            = {5, false},
    ["ONE_SHOT_TOWER"]              = {5, false},
    ["CROWD_CONTROL"]               = {5, false},
    ["WOBBA_LUBBA_DUB_DUB"]         = {5, false},
    ["PEST_CONTROL"]                = {5, false},
    ["TURN_A_BLIND_EYE"]            = {5, false},
    ["TAKE_ME_HOME"]                = {5, false},
    ["BUTTERTENTACLES"]             = {5, false},
    ["BYE_BYE_BEAUTIFUL"]           = {10, false},
    ["CONJUNTIVICTORY"]             = {10, false},
    ["CONQUEROR_OF_THE_VOID"]       = {10, false},
    ["LINIREAN_RESISTANCE"]         = {5, false},
    ["DARK_RUTHLESSNESS"]           = {5, false},
    ["UNENDING_RICHES"]             = {5, false},
    ["SIGNATURE_TECHNIQUES"]        = {5, false},
    ["ROYAL_CAPTAIN"]               = {5, false},
    ["DARK_LIEUTENANT"]             = {5, false},
    ["FOREST_PROTECTOR"]            = {5, false},
    ["UNTAMED_BEAST"]               = {5, false},
    ["MIGHTY_III"]                  = {10, false},
    ["AGE_OF_HEROES"]               = {30, false},
    ["IRONCLAD"]                    = {30, false},
    ["SEASONED_GENERAL"]            = {10, false},
    ["MASTER_TACTICIAN"]            = {30, false},
    ["TREE_HUGGER"]                 = {5, false},
    ["RUST_IN_PEACE"]               = {5, false},
    ["WE_ARE_ALL_MAD_HERE"]         = {5, false},
    ["ROCK_BEATS_ROCK"]             = {5, false},
    ["SPECTRAL_FURY"]               = {10, false},
    ["SAVIOUR_OF_THE_FOREST"]       = {5, false},
    ["SMOOTH_OPER_GATOR"]           = {5, false},
    ["SEE_YA_LATER_ALLIGATOR"]      = {10, false},
    ["HAIL_TO_THE_K_BABY"]          = {5, false},
    ["SCRAMBLED_EGGS"]              = {5, false},
    ["MECHANICAL_BURNOUT"]          = {5, false},
    ["FACTORY_STRIKE"]              = {5, false},
    ["DOMO_ARIGATO"]                = {5, false},
    ["KEPT_YOU_WAITING"]            = {5, false},
    ["GIFT_OF_LIFE"]                = {5, false},
    ["GARBAGE_DISPOSAL"]            = {5, false},
    ["DISTURBING_THE_PEACE"]        = {5, false},
    ["OBLITERATE"]                  = {5, false},
    ["SHUT_YOUR_MOUTH"]             = {5, false},
    ["DLC1_WIN_BOSS"]               = {10, false},
    ["INTO_THE_OGREVERSE"]          = {5, false},
    ["A_COON_OF_SURPRISES"]         = {5, false},
    ["LUCAS_SPIDER"]                = {5, false},
    ["NO_FLY_ZONE"]                 = {5, false},
    ["ARACHNED"]                    = {10, false},
}

ids.gamecenter_universal_premium={}

--AppleGameCenter/Achievements
ids.gamecenter_universal_premium.achievements={
    ["LEARNING_THE_ROPES"]          = "kr5.universal.premium.LEARNING_THE_ROPES",
    ["TIPPING_THE_SCALES"]          = "kr5.universal.premium.TIPPING_THE_SCALES",
    ["FIELD_TRIP_RUINER"]           = "kr5.universal.premium.FIELD_TRIP_RUINER",
    ["ITS_A_SECRET_TO_EVERYONE"]    = "kr5.universal.premium.ITS_A_SECRET_TO_EVERYONE",
    ["CIRCLE_OF_LIFE"]              = "kr5.universal.premium.CIRCLE_OF_LIFE",
    ["PLAYFUL_FRIENDS"]             = "kr5.universal.premium.PLAYFUL_FRIENDS",
    ["MOST_DELICIOUS"]              = "kr5.universal.premium.MOST_DELICIOUS",
    ["NATURES_WRATH"]               = "kr5.universal.premium.NATURES_WRATH",
    ["MIGHTY_I"]                    = "kr5.universal.premium.MIGHTY_I",
    ["GREENLIT_ALLIES"]             = "kr5.universal.premium.GREENLIT_ALLIES",
    ["OVER_THE_EDGE"]               = "kr5.universal.premium.OVER_THE_EDGE",
    ["CLEANUP_IS_OPTIONAL"]         = "kr5.universal.premium.CLEANUP_IS_OPTIONAL",
    ["RUNEQUEST"]                   = "kr5.universal.premium.RUNEQUEST",
    ["NONE_SHALL_PASS"]             = "kr5.universal.premium.NONE_SHALL_PASS",
    ["CRAFTING_IN_THE_MINES"]       = "kr5.universal.premium.CRAFTING_IN_THE_MINES",
    ["PORKS_OFF_THE_MENU"]          = "kr5.universal.premium.PORKS_OFF_THE_MENU",
    ["OUTBACK_BARBEQUICK"]          = "kr5.universal.premium.OUTBACK_BARBEQUICK",
    ["SAVIOUR_OF_THE_GREEN"]        = "kr5.universal.premium.SAVIOUR_OF_THE_GREEN",
    ["NOT_A_MOMENT_TO_WASTE"]       = "kr5.universal.premium.NOT_A_MOMENT_TO_WASTE",
    ["SILVER_FOR_MONSTERS"]         = "kr5.universal.premium.SILVER_FOR_MONSTERS",
    ["CROW_SCARER"]                 = "kr5.universal.premium.CROW_SCARER",
    ["WE_RE_NOT_GONNA_TAKE_IT"]     = "kr5.universal.premium.WE_RE_NOT_GONNA_TAKE_IT",
    ["BREAKER_OF_CHAINS"]           = "kr5.universal.premium.BREAKER_OF_CHAINS",
    ["GEM_SPILLER"]                 = "kr5.universal.premium.GEM_SPILLER",
    ["UNBOUND_VICTORY"]             = "kr5.universal.premium.UNBOUND_VICTORY",
    ["GET_THE_PARTY_STARTED"]       = "kr5.universal.premium.GET_THE_PARTY_STARTED",
    ["WAR_MASONRY"]                 = "kr5.universal.premium.WAR_MASONRY",
    ["PROMOTION_DENIED"]            = "kr5.universal.premium.PROMOTION_DENIED",
    ["STARLIGHT"]                   = "kr5.universal.premium.STARLIGHT",
    ["CLEANSE_THE_KING"]            = "kr5.universal.premium.CLEANSE_THE_KING",
    ["YOU_SHALL_NOT_CAST"]          = "kr5.universal.premium.YOU_SHALL_NOT_CAST",
    ["CRYSTAL_CLEAR"]               = "kr5.universal.premium.CRYSTAL_CLEAR",
    ["MIGHTY_II"]                   = "kr5.universal.premium.MIGHTY_II",
    ["ALL_THE_SMALL_THINGS"]        = "kr5.universal.premium.ALL_THE_SMALL_THINGS",
    ["THE_CAVALRY_IS_HERE"]         = "kr5.universal.premium.THE_CAVALRY_IS_HERE",
    ["WEIRDER_THINGS"]              = "kr5.universal.premium.WEIRDER_THINGS",
    ["OVINE_JOURNALISM"]            = "kr5.universal.premium.OVINE_JOURNALISM",
    ["ONE_SHOT_TOWER"]              = "kr5.universal.premium.ONE_SHOT_TOWER",
    ["CROWD_CONTROL"]               = "kr5.universal.premium.CROWD_CONTROL",
    ["WOBBA_LUBBA_DUB_DUB"]         = "kr5.universal.premium.WOBBA_LUBBA_DUB_DUB",
    ["PEST_CONTROL"]                = "kr5.universal.premium.PEST_CONTROL",
    ["TURN_A_BLIND_EYE"]            = "kr5.universal.premium.TURN_A_BLIND_EYE",
    ["TAKE_ME_HOME"]                = "kr5.universal.premium.TAKE_ME_HOME",
    ["BUTTERTENTACLES"]             = "kr5.universal.premium.BUTTERTENTACLES",
    ["BYE_BYE_BEAUTIFUL"]           = "kr5.universal.premium.BYE_BYE_BEAUTIFUL",
    ["CONJUNTIVICTORY"]             = "kr5.universal.premium.CONJUNTIVICTORY",
    ["CONQUEROR_OF_THE_VOID"]       = "kr5.universal.premium.CONQUEROR_OF_THE_VOID",
    ["LINIREAN_RESISTANCE"]         = "kr5.universal.premium.LINIREAN_RESISTANCE",
    ["DARK_RUTHLESSNESS"]           = "kr5.universal.premium.DARK_RUTHLESSNESS",
    ["UNENDING_RICHES"]             = "kr5.universal.premium.UNENDING_RICHES",
    ["SIGNATURE_TECHNIQUES"]        = "kr5.universal.premium.SIGNATURE_TECHNIQUES",
    ["ROYAL_CAPTAIN"]               = "kr5.universal.premium.ROYAL_CAPTAIN",
    ["DARK_LIEUTENANT"]             = "kr5.universal.premium.DARK_LIEUTENANT",
    ["FOREST_PROTECTOR"]            = "kr5.universal.premium.FOREST_PROTECTOR",
    ["UNTAMED_BEAST"]               = "kr5.universal.premium.UNTAMED_BEAST",
    ["MIGHTY_III"]                  = "kr5.universal.premium.MIGHTY_III",
    ["AGE_OF_HEROES"]               = "kr5.universal.premium.AGE_OF_HEROES",
    ["IRONCLAD"]                    = "kr5.universal.premium.IRONCLAD",
    ["SEASONED_GENERAL"]            = "kr5.universal.premium.SEASONED_GENERAL",
    ["MASTER_TACTICIAN"]            = "kr5.universal.premium.MASTER_TACTICIAN",
    ["TREE_HUGGER"]                 = "kr5.universal.premium.TREE_HUGGER",
    ["RUST_IN_PEACE"]               = "kr5.universal.premium.RUST_IN_PEACE",
    ["WE_ARE_ALL_MAD_HERE"]         = "kr5.universal.premium.WE_ARE_ALL_MAD_HERE",
    ["ROCK_BEATS_ROCK"]             = "kr5.universal.premium.ROCK_BEATS_ROCK",
    ["SPECTRAL_FURY"]               = "kr5.universal.premium.SPECTRAL_FURY",
    ["SAVIOUR_OF_THE_FOREST"]       = "kr5.universal.premium.SAVIOUR_OF_THE_FOREST",
    ["SMOOTH_OPER_GATOR"]           = "kr5.universal.premium.SMOOTH_OPER_GATOR",
    ["SEE_YA_LATER_ALLIGATOR"]      = "kr5.universal.premium.SEE_YA_LATER_ALLIGATOR",
    ["HAIL_TO_THE_K_BABY"]          = "kr5.universal.premium.HAIL_TO_THE_K_BABY",
    ["SCRAMBLED_EGGS"]              = "kr5.universal.premium.SCRAMBLED_EGGS",
    ["MECHANICAL_BURNOUT"]          = "kr5.universal.premium.MECHANICAL_BURNOUT",
    ["FACTORY_STRIKE"]              = "kr5.universal.premium.FACTORY_STRIKE",
    ["DOMO_ARIGATO"]                = "kr5.universal.premium.DOMO_ARIGATO",
    ["KEPT_YOU_WAITING"]            = "kr5.universal.premium.KEPT_YOU_WAITING",
    ["GIFT_OF_LIFE"]                = "kr5.universal.premium.GIFT_OF_LIFE",
    ["GARBAGE_DISPOSAL"]            = "kr5.universal.premium.GARBAGE_DISPOSAL",
    ["DISTURBING_THE_PEACE"]        = "kr5.universal.premium.DISTURBING_THE_PEACE",
    ["OBLITERATE"]                  = "kr5.universal.premium.OBLITERATE",
    ["SHUT_YOUR_MOUTH"]             = "kr5.universal.premium.SHUT_YOUR_MOUTH",
    ["DLC1_WIN_BOSS"]               = "kr5.universal.premium.DLC1_WIN_BOSS",
    ["INTO_THE_OGREVERSE"]          = "kr5.universal.premium.INTO_THE_OGREVERSE",
    ["A_COON_OF_SURPRISES"]         = "kr5.universal.premium.A_COON_OF_SURPRISES",
    ["LUCAS_SPIDER"]                = "kr5.universal.premium.LUCAS_SPIDER",
    ["NO_FLY_ZONE"]                 = "kr5.universal.premium.NO_FLY_ZONE",
    ["ARACHNED"]                    = "kr5.universal.premium.ARACHNED",
}

-- metadata in game center (used for export)
ids.gamecenter_universal_premium.achievements_metadata =  table.deepclone(ids.gamecenter.achievements_metadata)

------------------------------------------------------------
return ids
