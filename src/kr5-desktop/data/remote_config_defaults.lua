-- ------------------------------------------------------------
-- -- KR5 REMOTE CONFIG - DESKTOP ONLY VERSION
-- ------------------------------------------------------------

local d = {
    
    ------------------------------------------------------------
    -- links
    link_more_games = {
        ['com.ironhidegames.kingdomrush.alliance'] = 'https://www.ironhidegames.com/games',         -- TODO: definir si precisa un tracker
        ['com.ironhidegames.android.kingdomrush.alliance'] = 'https://www.ironhidegames.com/games', -- TODO
        ['com.ironhidegames.kingdomrush.alliance.windows.steam'] = 'https://store.steampowered.com/franchise/ironhidegames/',
        ['com.ironhidegames.kingdomrush.alliance.mac.steam'] = 'https://store.steampowered.com/franchise/ironhidegames/',
        ['com.ironhidegames.kingdomrush.alliance.mac.appstore'] = 'https://www.ironhidegames.com/games',
    },

    url_twitter          = { default = "https://twitter.com/ironhidegames" },
    url_facebook         = { default = "http://www.facebook.com/ironhidegames" },
    url_instagram        = { default = "https://www.instagram.com/ironhidegames" },
    url_discord          = { default = "https://discord.gg/aqHGabqupe" },
    url_tiktok          = { default = "https://www.tiktok.com/@ironhidegames" },

    url_ih               = { default = "https://www.ironhidegames.com/games" },
    url_privacy_policy   = { 
        ['com.ironhidegames.kingdomrush.alliance.universal.premium'] = 'https://www.kingdomrushalliance.com/privacy-policy/apple-arcade',
        default = "https://www.ironhidegames.com/PrivacyPolicy" },
    url_terms_of_service = { default = "https://www.ironhidegames.com/TermsOfService" },
    
    url_strategy_guide   = { default = "http://www.kingdomrushorigins.com/strategy.html" }, -- TODO

    url_store = { ['com.ironhidegames.android.kingdomrush.alliance']="https://play.google.com/store/apps/details?id=com.ironhidegames.android.kingdomrush.alliance",['com.ironhidegames.kingdomrush.alliance']="https://apps.apple.com/us/app/kingdom-rush-5-alliance-td/id1622869542", default = "http://www.kingdomrushorigins.com" },
    
    min_version = nil, -- versions < will not be playable
    -- rating
    ask_for_rating = true,
    ask_for_rating_level = {4, 7, 12}, 

    -- news
    news_url = "https://news.ironhidegames.com/api/v1/PromoNews",
    
    -- in-game shop
    ingame_shop = true,
    
    -- premium flags
    premium_show_more_games = false,
    premium_show_news = true,
    
    -- one time gifts
    one_time_gifts = {   }, -- ej: {         id ='launch',         custom_title = 'FIRST_WEEK_PACK',         custom_description = 'CLAIM_GIFT',         includes_consumables={             {name='item_summon_blackburn', count=2},             {name='item_cluster_bomb', count=4},             {name='gems_handful', count=1}             }         }       

    ------------------------------------------------------------
    -- default offer conditions
    -- notes:
    -- * Conditions accept a special string value: 'any'
    -- This allows an offer to override the default and skip the check.
    -- * Use multiline comments because Firebase strips newlines.
    --
    default_offer_conditions = {
        --[[debug_skip_conditions = true,         --[[ DEBUG ]]
        offer_includes_hero_on_sale = false,
        offer_includes_purchased_product = false,
        offer_includes_unpurchased_products_count = 1,
      --  offer_was_shown = false,                  --[[ recheckTimeInSeconds: 0 ]]
        offer_was_purchased = false,
        player_made_purchases = 'any',            --[[ checkSomethingPurchased: false ]]
        --[[player_reached_level = 0,  ]]
        --[[player_reached_stars = 0,  ]]
        --[[player_reached_sessions = 0, ]]
     --   seconds_elapsed_since_any_offer_purchased = 86400,
     --   seconds_elapsed_since_any_offer_shown = 86400,
        --[[sessions_passed_since_offer_purchased = 0,   ]]
        --[[sessions_passed_since_offer_shown = 0,       ]]
    },
    
    default_offer_params = {
        seconds_icon_is_visible = 43200,
    },
    
    ------------------------------------------------------------
    -- product definitions
    -- NOTE: Only DLCs are available for desktop builds.
    -- Steam configuration inside features.lua steam definitions
    -- 
}

return d
