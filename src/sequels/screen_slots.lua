--
--
--
-- luacheck: globals DBG_SLIDE_EDITOR DEFAULT_KEY_MAPPINGS OV_DESKTOP
-- luacheck: globals SGN_PS_PUSH_NOTI_PERMISSION_FINISHED SGN_PS_PUSH_NOTI_SHOULD_SHOW_RATIONALE SGN_PS_SCREEN_SLOTS_READY
-- luacheck: globals GG5PopUp GG5PopUpError GG5PopUpProcessing SlotView  GGLabel RestoreView 

local log = (require 'klua.log'):new('screen_slots')

local class = require 'middleclass'

local F = require 'klove.font_db'
local I = require 'klove.image_db'
local V = require 'klua.vector'
local storage = require 'storage'
local timer = require('hump.timer').new()
local ktw = require('klove.tween').new(timer)
local signal = require 'hump.signal'
local S = require 'sound_db'
local SU = require 'screen_utils'
local km = require 'klua.macros'
local GS = require 'game_settings'
local i18n = require 'i18n'
local ISM = require 'klove.input_state_machine'
local RC = require 'remote_config'
local PS = require 'platform_services'
local PP = require 'privacy_policy_consent'
local features = require 'features'
if DBG_SLIDE_EDITOR then
    local dbe = require 'debug_view_editor'    
end

require 'constants'

require 'kviews_gg_sequels'

require 'klove.kui'
local kui_db = require 'klove.kui_db'

local SN = PS and PS.services.news

------------------------------------------------------------

local screen = {}
screen.required_sounds = {'common', 'music_screen_slots'}
screen.required_textures = {
    'screen_slots', 'screens_common_LOCALE', 'gui_popups',
    'gui_slices', -- TODO se precisa este?
}
if not IS_MOBILE then table.insert(screen.required_textures, 'gui_popups_desktop') end
if SN and SN.texture_group then table.insert(screen.required_textures, SN.texture_group) end

screen.ref_w = 1728
screen.ref_h = 768
screen.ref_res = TEXTURE_SIZE_ALIAS.ipad

-- functions
local function wid(name)
    return screen.window:get_child_by_id(name)
end

------------------------------------------------------------
-- signal handlers
screen.signal_handlers = {
    [SGN_PS_STATUS_CHANGED] =
        function(service_name, success, opt_msg)
            log.debug(SGN_PS_STATUS_CHANGED .. ' : %s %s', service_name, success)
            if service_name == 'achievements' then
                local ps_cloud = PS.services.cloudsave
                local no_signin = ps_cloud and ps_cloud:no_signin()
                -- wid('button_ps_signin').hidden = (success == true or no_signin)
                -- if PP:can_show('achievements') then 
                --     wid('button_ps_achievements').hidden = (success ~= true)
                -- else
                --     wid('button_ps_signout').hidden = (success ~= true or no_signin)
                -- end
            end
        end,
    [SGN_PS_SYNC_SLOTS_FINISHED] =
        function(service_name, success, request_id, status_code)
            log.debug(SGN_PS_SYNC_SLOTS_FINISHED .. ' : %s %s', service_name, success)
            if service_name == 'cloudsave' then
                screen.cloudsave_req_id = nil
                if not screen.popup_processing.hidden and not screen.sync_purchases_req_id then
                    screen:hide_cloudsave_progress()
                    if success then
                        screen.c_show_slots()
                    else
                        screen:show_cloudsave_error(status_code)
                    end
                end
                -- if not wid('cloudsave_progress_view').hidden then
                --     if success then 
                --         -- hide progress and show queued slots
                --         screen:hide_cloudsave_progress()
                --         screen.c_show_slots()
                --     else 
                --         -- show error only if the progress is shown
                --         screen:hide_buttons()
                --         screen:show_cloudsave_error(status_code)
                --     end
                -- end
                -- log the error
                if not success and PS.services.analytics then
                    PS.services.analytics:log_event('kr_error_ps_sync_slots_finished','status_code',status_code)
                end                        
            end
        end,
    [SGN_REMOTE_CONFIG_UPDATED] =
        function(service_name, success)
            log.debug(SGN_REMOTE_CONFIG_UPDATED)
            if SN then
                -- cache after having the updated news_url
                SN:cache_news()
            end


            local function is_version_smaller(v1, v2)
                local function split(version)
                    local parts = {}
                    for num in version:gmatch("(%d+)") do
                        table.insert(parts, tonumber(num))
                    end
                    return parts
                end
            
                local v1Parts, v2Parts = split(v1), split(v2)
                for i = 1, math.max(#v1Parts, #v2Parts) do
                    local n1, n2 = v1Parts[i] or 0, v2Parts[i] or 0
                    if n1 ~= n2 then return n1 < n2 end
                end
                return false 
            end
            if IS_MOBILE and RC.v.min_version and is_version_smaller(version.string_short,RC.v.min_version) then
                screen:show_version_block()
            end
            
        end,
    [SGN_PS_NEWS_CACHED] =
        function(service_name, success, request_id, status_code)
            log.debug(SGN_PS_NEWS_CACHED .. ' : %s %s %s', service_name, success, request_id)
            screen:check_news(1.5)
        end,
    [SGN_PS_NEWS_IMAGE_CACHED] =
        function(service_name, success, request_id, status_code)
            log.info(SGN_PS_NEWS_IMAGE_CACHED .. ' : %s %s %s', service_name, success, request_id)
            if SN then 
                local c = SN:get_cached_request(request_id)
                if c and c.body then 
                    screen:refresh_news()
                end
            end
        end,
    [SGN_PS_AUTH_FINISHED] =
        function(service_name, success, status, error_msg)
            log.debug(SGN_PS_AUTH_FINISHED .. ' : %s %s %s', service_name, success, status, error_msg)
            screen.auth_req_id = nil
            screen:hide_auth_progress()
            if success then
                screen.c_show_slots()
            else
                screen:show_auth_error(status, error_msg)
            end
        end,
    [SGN_PS_CHANNEL_QUIT_REQUESTED] =
        function(service_name, error_msg)
            log.debug(SGN_PS_CHANNEL_QUIT_REQUESTED .. ' : %s %s', service_name, error_msg)
            screen:show_auth_error(0, error_msg)
        end,
    [SGN_PS_SYNC_PURCHASES_FINISHED] =
        function(service_name, success)
            log.debug(SGN_PS_SYNC_PURCHASES_FINISHED .. ' : %s %s', service_name, success)

            screen.sync_purchases_req_id = nil
            if not screen.popup_processing.hidden and not screen.cloudsave_req_id then
                screen:hide_cloudsave_progress()
                if success then
                    screen.c_show_slots()
                else
                    screen:show_cloudsave_error('Store sync')
                end
            end                
            
            -- news and more games visibility
            local iap = PS.services.iap

            wid('group_more_games').hidden = ((iap and iap:is_premium() and not RC.v.premium_show_more_games)
                or not iap:is_premium_valid()
                or not RC.v.link_more_games[version.bundle_id]
                or PP:is_underage()
                or features.hide_external_links)

            -- check news
            if iap and not iap:is_premium() then
                screen:check_news(1.6)
            end
        end,
    [SGN_PS_DEEP_LINK_CHANGED] =
        function(service_name, new_link)
            log.debug(SGN_PS_DEEP_LINK_CHANGED .. ' %s', new_link)
            screen:process_deep_link(new_link)            
        end,    
    [SGN_PS_PUSH_NOTI_SHOULD_SHOW_RATIONALE] =
        function(service_name)
            local global = storage:load_global()
            if global.push_noti_rationale_shown then
                log.debug('push_noti_rationale_view already shown') 
                return
            end
            local v = wid('popup_message')
            if v then
                v:set_msg(_('PUSH_NOTIFICATIONS_PERMISSION_RATIONALE'))
                v:enable()
                v:set_ok_fn(function()
                    local ps_push_noti = PS.services.push_noti
                    if ps_push_noti then
                        ps_push_noti:request_permission()
                    end
                end)
                v:set_no_fn(function()
                    local global = storage:load_global()
                    global.push_noti_rationale_shown = true
                    storage:save_global(global)
                end)
                v:show()
            end
        end,
    [SGN_PS_PUSH_NOTI_PERMISSION_FINISHED] =
        function(service_name, success)
            log.debug(SGN_PS_PUSH_NOTI_PERMISSION_FINISHED .. ' : %s %s', service_name, success)
            -- reset the flag so the dialog can be shown again if the player re-enables the permission
            local global = storage:load_global()
            global.push_noti_rationale_shown = false
            storage:save_global(global)
        end,
}


------------------------------------------------------------

function screen:init(w,h,done_callback)
    self.done_callback = done_callback

    -- silent IAP purchases sync to determine premium mode
    if PS.services.iap and PS.services.iap:get_status() then
        local rid = PS.services.iap:sync_purchases(true)  -- immediate and silent, ignores consumables
        self.sync_purchases_req_id = rid
    end
    
    local sw,sh,scale,origin = SU.clamp_window_aspect(w,h,self.ref_w,self.ref_h)
    self.w,self.h = w,h
    self.sw = sw
    self.sh = sh

    self.selected_locale = i18n.current_locale
    
    GGLabel.static.font_scale = scale
    GGLabel.static.ref_h = self.ref_h

    self.default_base_scale = SU.get_default_base_scale(sw,sh)
    GG5PopUp.static.base_scale = self.default_base_scale -- before kui_db pass
    
    -- IMPORTANT: values used in WHEN cannot be nil! 
    local ctx = SU.new_screen_ctx(self)

    --ctx.shader_scale = self.h/self.sh
    --ctx.safe_frame = SU.get_safe_frame(w,h,self.ref_w,self.ref_h)
    ctx.context = 'slots'
    ctx.hud_scale = SU.get_hud_scale(w,h,self.ref_w,self.ref_h)
    ctx.is_underage = PP:is_underage()
    ctx.hide_external_links = (PP:is_underage() or features.hide_external_links) or false
    ctx.hide_privacy_policy = features.hide_privacy_policy or false
    ctx.simple_priv = features.simple_privacy_button and true or false
    ctx.more_games_with_label = features.more_games_with_label or false
    ctx.show_age_rating_popup = features.show_age_rating_popup and true or false
    ctx.is_main = true
    if ctx.show_age_rating_popup then
        ctx.show_age_rating_popup_icon_data = love.graphics.newImage(features.show_age_rating_popup.icon)
        local dimx,dimy = ctx.show_age_rating_popup_icon_data:getDimensions()
        ctx.show_age_rating_popup_icon_anchor = V.v(dimx/2,dimy/2)
        ctx.show_age_rating_popup_text_key = features.show_age_rating_popup.text_key
    end
    if features.show_options_footer then
        ctx.options_footer_text_key = features.show_options_footer.text_key
    end
    ctx.is_censored_cn = features.censored_cn and true or false
    local tt = kui_db:get_table('screen_slots',ctx)
    local window = KWindow:new_from_table(tt)
    window.scale = {x=scale,y=scale}
    window.size = {x=sw,y=sh}
    window.origin = origin
    window.timer = timer
    window.ktw = ktw

    local ps_cs = PS.services.cloudsave
    if ps_cs then
        -- load processing screen
        self.popup_processing = GG5PopUpProcessing:new_from_table(kui_db:get_table('popup_purchasing',ctx))
        self.popup_processing.pos.x = window.size.x/2
        self.popup_processing.pos.y = 366.85
        self.popup_processing:ci('label_purchasing').text = _('CLOUDSYNC_PLEASE_WAIT')

        self.popup_processing_background = KView:new(V.v(sw*2,sh*2))
        self.popup_processing_background.colors={background={0,0,0,200}}
        self.popup_processing_background.alpha = 1
        self.popup_processing_background.pos.x = -window.size.x/2
        self.popup_processing_background.pos.y = 0
        self.popup_processing_background.propagate_on_click = true
        self.popup_processing_background.propagate_drag = false

        self.popup_processing.hidden = true
        self.popup_processing_background.hidden = true

        window:add_child(self.popup_processing_background)
        window:add_child(self.popup_processing)
    end

    window:set_responder(window)
    self.window = window    

    if ps_cs then
        self.auth_req_id = ps_cs:do_signin()
    end

    -- start button
    if IS_MOBILE then 
        wid('button_start'):focus(true)
        wid('button_start').on_click = function()
            S:queue('GUIButtonCommon')
            if not wid('group_slots_list').hidden then
                screen.c_hide_slots()
            else
                self:hide_buttons()
                if self.popup_news and not self.popup_news.hidden then
                    self.popup_news.hidden = true
                end
                if PS then
                    if self.auth_req_id then
                        -- show authenticiation progress
                        self:show_auth_progress(self.auth_req_id)
                        return
                        
                    elseif self.cloudsave_req_id or self.sync_purchases_req_id then
                        -- show progress for cloudsave sync or purchases in progress
                        self:show_cloudsave_progress()
                        return

                    else
                        local ps_auth = PS.services.auth
                        local ps_cs = PS.services.cloudsave
                        local ps_iap = PS.services.iap
                        local interrupt = false
                        if ps_auth and not ps_auth:is_auth() then
                            -- show auth progres dialog and start request
                            local rid = ps_auth:auth()
                            if rid then
                                self.auth_req_id = rid
                                self:show_auth_progress(rid)
                                interrupt = true
                            end
                        end
                        if ps_iap and ps_iap:get_status() and not ps_iap:get_sync_status().purchases then
                            -- resync if never processed
                            local rid = PS.services.iap:sync_purchases(true)  -- immediate and silent, ignores consumables
                            if rid then 
                                self.sync_purchases_req_id = rid
                                self:show_cloudsave_progress()
                                interrupt = true
                            end
                        end
                        if ps_cs and ps_cs:get_status() then
                            -- resync if never processed or after a certain time
                            local status = ps_cs:get_sync_status()
                            if not status.slots or (os.time() - status.slots) > 5 * 60 then
                                local rid = ps_cs:sync_slots()
                                if rid then
                                    self.cloudsave_req_id = rid
                                    self:show_cloudsave_progress()
                                    interrupt = true
                                end
                            end
                        end
                        if interrupt then return end
                    end
                end
                -- or show the slots with delay
                timer:after(0.1, function() screen.c_show_slots() end)
            end
        end

    else        
        wid('button_start_desktop'):focus(true)
        wid('button_start_desktop').on_click = function(this)
            S:queue('GUIButtonCommon')
            if not wid('group_slots_list').hidden then
                screen.c_hide_slots()
            else
                screen.c_show_slots()
            end
        end
        wid('button_quit_desktop').on_click = function(this)
            S:queue('GUIButtonCommon')
            local p = wid('popup_message')
            p:set_msg(_('CONFIRM_EXIT'))
            p:enable()
            p:set_ok_fn(
                function()
                    this:disable(false)
                    screen.c_quit()
                end
            )
            p:show()
        end
    end    

    if IS_MOBILE then 
        wid('label_start').text = _('TAP_TO_START')
    end

    -- news button
    wid('button_news').on_click =
        function()
            S:queue('GUIButtonCommon')
            self:show_news()
        end

    -- privacy policy button
    wid('button_privacy_policy').on_click =
        function()
            S:queue('GUIButtonCommon')
            love.system.openURL(RC.v.url_privacy_policy[version.bundle_id] or RC.v.url_privacy_policy.default)
        end

    -- moregames container/button
    wid('button_more_games').on_click =
        function()
            S:queue('GUIButtonCommon')
            if (PS.services.channel) then
                PS.services.channel:show_more_games()
            else
                local link = RC.v.link_more_games[version.bundle_id]
                if not link then
                    log.error('link_more_games[%s] is nil', version.bundle_id)
                else
                    love.system.openURL(link)
                end
            end
        end
    wid('button_options').on_click =
        function(this)
            S:queue('GUIButtonCommon')
            wid('popup_options'):show('slots')
        end

    if features.censored_cn then
    wid('cn_censored_8plus_button').on_click =
        function(this)
            S:queue('GUIButtonCommon')
            wid('popup_message_china'):show('slots')
        end
    end

    -- slots
    wid('group_slots_list').hidden = true
    wid('group_slots_list').show = function(this)
        local sl = wid('group_slots_list')
        -- update slots
        for i=#sl.children,1,-1 do
            local v = sl.children[i]
            if v:isInstanceOf(SlotView) then
                v:show()
            end
        end

        -- show panel
        sl.hidden = false
        sl.pos.y = wid('group_slots_list').pos_hidden.y
        if sl.tweener then
            timer:cancel(sl.tweener)
        end
        sl:disable(false)
        sl.tweener = timer:tween(0.4, wid('group_slots_list').pos, {y=wid('group_slots_list').pos_shown.y}, 'out-back',
                                 function()
                                     sl:enable(false)
                                 end
                                )
    end
    wid('group_slots_list').hide = function(this)
        local sl = wid('group_slots_list')
        sl:disable(false)
        sl.tweener = timer:tween(
            0.4, wid('group_slots_list').pos, {y=wid('group_slots_list').pos_hidden.y}, 'in-back',
            function()
                wid('group_slots_list').hidden = true
                wid('group_slots_list').tweener = nil
                if IS_MOBILE then
                    wid('button_start'):focus(true)
                else
                    wid('button_start_desktop'):focus(true)
                end
                
            end
        )    
    end

    wid('button_close_popup').on_click = function()
        S:queue("GUIButtonOut")
        screen:c_hide_slots()
    end

    -- restore savegame
    wid('group_restore').hidden = true;
    
    -- input
    local ism_data = {
        FIRST = {
            --{'escape',         ISM.q_is_view_visible,   {'no_joystick_view'},        nil}, -- ignore
            --{'escape',         ISM.q_is_view_visible,   {'error_view'},             ISM.c_hide_view, {'error_view'}},
            --{'escape',         ISM.q_is_view_visible,   {'processing_view'},        ISM.c_hide_view, {'processing_view'}},
            {'escape',         ISM.q_is_view_visible,   {'popup_message'},            ISM.c_hide_view, {'popup_message'}},
            {'escape',         ISM.q_is_view_visible,   {'popup_news'},               ISM.c_hide_view, {'popup_news'}},
            {'escape',         ISM.q_is_view_visible,   {'popup_confirm'},            ISM.c_hide_view, {'popup_confirm'}},
            {'escape',         ISM.q_is_view_visible,   {'popup_locale_list'},        ISM.c_hide_view, {'popup_locale_list'}},
            {'escape',         ISM.q_is_view_visible,   {'popup_options'},            ISM.c_hide_view, {'popup_options'}},
            {'escape',         ISM.q_is_view_visible,   {'popup_message_china'},      ISM.c_hide_view, {'popup_message_china'}},
            {'escape',         ISM.q_is_view_visible,   {'group_slots_list'},         screen.c_hide_slots},
            {'escape',         ISM.q_is_escape_show_quit,nil                ,         screen.c_show_quit_confirm},
            {'escape',         ISM.q_not_from_alias,    nil,                          ISM.c_show_view, {'popup_options','slots'}},
            {'return',         true,                    nil,                          ISM.c_send_key , {'return'}},
            {'tab',            true,                    nil,                          ISM.c_send_key,  {'tab'}},
            {'reverse_tab',    true,                    nil,                          ISM.c_send_key,  {'reverse_tab'}},
            {'up',             true,                    nil,                          ISM.c_send_key , {'up'}},
            {'down',           true,                    nil,                          ISM.c_send_key , {'down'}},
            {'left',           true,                    nil,                          ISM.c_send_key , {'left'}},
            {'right',          true,                    nil,                          ISM.c_send_key , {'right'}},
            {'pageup',         ISM.q_is_view_visible,   {'popup_options'},            ISM.c_call_view_fn , {'popup_options', 'change_page', 'prev'}},
            {'pagedown',       ISM.q_is_view_visible,   {'popup_options'},            ISM.c_call_view_fn , {'popup_options', 'change_page', 'next'}},
            --
            {'jleftxy',        ISM.q_rate_limit,        nil,                          ISM.c_send_key_axis },
            {'ja',             true,                    nil,                          ISM.c_send_key,  {'return'}},
            {'jb',             'escape'},
            {'jleftshoulder',  'pageup'},
            {'jrightshoulder', 'pagedown'},
            {'jstart',         'escape'},
            {'jback',          'escape'},
            {'jstart',         true,                    nil,                          ISM.c_show_view, {'popup_options', 'slots'}},
            {'jback',          true,                    nil,                          ISM.c_show_view, {'popup_options', 'slots'}},
            {'jdpright',       'right' },
            {'jdpup',          'up'    },
            {'jdpleft',        'left'  },
            {'jdpdown',        'down'  },
        },
    }
    ISM:init(ism_data,window,DEFAULT_KEY_MAPPINGS,storage:load_settings())

    -- adjust initial volume
    local st = storage:load_settings()
    S:set_main_gain_music(st and st.volume_music or 1)
    S:set_main_gain_fx(st and st.volume_fx or 1)

    -- register signals
    for sn,fn in pairs(self.signal_handlers) do
        signal.register(sn, fn)
    end

    -- sync remote config
    RC:sync()

    -- force news sync for deployments without remoteconfig
    if SN and not PS.services.remoteconfig then
        SN:cache_news()
    end
    
    -- start syncing slots if possible
    if PS.services.cloudsave and PS.services.cloudsave:get_status() then
        local rid = PS.services.cloudsave:sync_slots()
        self.cloudsave_req_id = rid
    end
    
    -- sync iap products
    if PS.services.iap and PS.services.iap:get_status() then
        PS.services.iap:sync_products()
    end

    -- base scale overrides per target
    -- NOTE: popups adjusted via static variables above
    do
        local ids = {
            --'group_logo',
            'group_more_games', 'group_privacy_policy', 'group_news', 'group_options',
            'button_start',
            'group_slots_list',
            'group_start_desktop',
        }
        local views = {}        
        for _,id in pairs(ids) do
            if (wid(id)) then 
                table.insert(views, wid(id))
            end
        end
        SU.apply_base_scale(views,self.default_base_scale)
    end
    
    if SN then
        -- load news popup
        SN.texture_scale = self.screen_scale
        self.popup_news = wid('popup_news')
    end
    
    --
    if not S:sound_is_playing('MusicMainMenu') then S:queue('MusicMainMenu') end

    -- background animation + overlay
    if not IS_MOBILE then
        wid('bg_exo_main').pos.y = wid('bg_exo_main').pos_shown.y
        wid('bg_exo_logo').pos.y = wid('bg_exo_logo').pos_shown.y
        wid('bg_exo_tentacles').pos.y = wid('bg_exo_tentacles').pos_shown.y
        wid('bg_exo_main').base_scale = V.vv(0.75)
        wid('bg_exo_logo').base_scale = V.vv(0.7)
        wid('bg_exo_tentacles').base_scale = V.vv(0.75)
    end
    wid('bg_exo_main').on_exo_finished = function(this,runs)
        this.exo_animation = 'idle'
        this.loop = true
    end
    wid('bg_exo_logo').on_exo_finished = function(this,runs)
        this.exo_animation = 'idle'
        this.loop = true
    end
    wid('bg_exo_tentacles').on_exo_finished = function(this,runs)
        this.exo_animation = 'idle'
        this.loop = true
    end
    local iov = wid('intro_overlay')
    ktw:tween(iov, 0.33, iov, {alpha=0}, 'linear', function() iov.hidden=true end)
    
    -- start intro animation
    self:init_buttons()

    wid('group_more_games').hidden = true -- starts hidden by default
    wid('group_news').hidden = true -- starts hidden by default

    timer:after(
        1.0,
        function()            
            self:show_buttons()
            self:check_deep_links()
            if IS_MOBILE then 
                wid('button_start').hidden = false
            end
        end

    )

    -- delayed services start signal
    timer:after(
        1.5,
        function()
            -- init services wating for this signal
            signal.emit(SGN_PS_SCREEN_SLOTS_READY)
            -- push noti permission for android
            if KR_PLATFORM == 'android' and PS.services.push_noti then
                local marketing = require 'marketing'
                local session_count = marketing:md_get('session_count') or 0
                log.debug('session_count: %s', session_count)
                if session_count > 4 then
                    PS.services.push_noti:check_permission()
                end
            end
            -- ftue
            signal.emit('ftue-step', 'screen_slots')
        end
    )

    -- first launch
    local global = storage:load_global()
    if global.launch_count == 1 then 
        timer:after(
            1.5, function()
                local ps_ach = PS.services.achievements
                if ps_ach and not PP:is_underage() then
                    ps_ach:do_signin()
                end
        end)
        -- first launch analytics
        if PS.services.analytics then 
            PS.services.analytics:log_event('kr_pp_first_launch', 'age', PP:is_underage() and 'underage' or 'overage')
        end
    end

    -- analytics
    if PS.services.analytics then
        PS.services.analytics:log_event('kr_screen_init','file','screen_slots')
        PS.services.analytics:log_event('kr_pp_launch', 'age', PP:is_underage() and 'underage' or 'overage')
    end
       
    if DBG_SLIDE_EDITOR then
        --dbe:inject_editor(wid('auth_progress_view'),self)
    end
end

function screen:destroy()
    -- remove signals
    for sn,fn in pairs(self.signal_handlers) do
        signal.remove(sn, fn)
    end

    ISM:destroy(self.window)
    ktw:clear()
    timer:clear()    
    self.window.timer = nil
    self.window.ktw = nil    
    self.window:destroy()
    self.window = nil
    SU.remove_references(self,KView)
end

function screen:update(dt)
    self.window:update(dt)
    timer:update(dt)
end
function screen:draw()
    self.window:draw()
end

function screen:mousepressed(x,y,button,istouch)
    self.window:mousepressed(x,y,button,istouch)
end
function screen:mousereleased(x,y,button,istouch)
    self.window:mousereleased(x,y,button,istouch)
end
function screen:wheelmoved(dx,dy)
    self.window:wheelmoved(dx,dy)
end

------------------------------------------------------------

function screen:handle_slot_button(slot_idx)
    signal.emit('ftue-step', 'click_on_slot')
    
    local new_slot=false
    if not storage:load_slot(slot_idx) then 
        storage:create_slot(slot_idx) 
        new_slot = true
    end
    storage:set_active_slot(slot_idx)

    -- TODO REFACTOR: make this behaviour configurable in features, with the level idx, mode, and difficulty
    -- Do not set director direclty, return it as part of the done_callback
    if new_slot and not features.censored_cn then
        local user_data = storage:load_slot(slot_idx)
        user_data.levels = {[1] = {}}
        storage:save_slot(user_data)

        director.next_item_args  = {
            level_idx=1,
            level_mode=GAME_MODE_CAMPAIGN,
            level_difficulty= DIFFICULTY_NORMAL
        }
        director.next_item_name = 'game'
    else
        self.done_callback({next_item_name = 'map', slot_idx=slot_idx})
    end
end

function screen:show_options()
    local p = wid('popup_options')
    -- p:show('contents')
end

function screen:hide_options()
    local p = wid('popup_options')
    -- p:hide()   
end

function screen:start_animation()
end

local group_names = {'group_logo', 'group_more_games', 'group_privacy_policy', 'group_news', 'group_options', 'group_8plus'}
-- TODO group_age_rating
function screen:init_buttons()
    for _,n in pairs(group_names)
    do
        local b = wid(n)
        if b then
            b.pos_shown = V.v(b.pos.x ,b.pos.y)
            local delta =  screen.sh/4
            if n == 'group_logo' then 
                delta = screen.sh
            end
            if n == 'group_8plus' then 
                delta = screen.sh / 2
            end
            b.pos_hidden = V.v(b.pos.x, b.pos.y + (delta * (b.pos.y > screen.sh/2 and 1 or -1)))
            if n ~= 'group_logo' then 
                b.pos.y = b.pos_hidden.y
                b.hidden = true
            end
        end
    end
end

function screen:show_buttons()
    -- show top and bottom bottoms
    for _,n in pairs(group_names)
    -- TODO group_age_rating
    do
        local b = wid(n)
        if b and b.pos_shown then
            b.hidden = false
            if b.tweener then
                timer:cancel(b.tweener)
            end
            b.tweener = timer:tween(0.4, b.pos, {x=b.pos_shown.x, y=b.pos_shown.y}, 'out-back')
        end
    end

    local iap = PS.services.iap
    wid('group_more_games').hidden = ((iap and iap:is_premium() and not RC.v.premium_show_more_games)
        or (iap and not iap:is_premium_valid())
        or not RC.v.link_more_games[version.bundle_id]
        or PP:is_underage())
    wid('group_news').hidden = ((iap and iap:is_premium() and not RC.v.premium_show_news)
        or (iap and not iap:is_premium_valid())
        or not SN
        or PP:is_underage())
   
    if IS_MOBILE then 
        wid('button_start'):enable()
        ktw:cancel(wid('label_start'))
        ktw:tween(wid('label_start'), 0.3, wid('label_start'), {alpha=1})
    else
        local g = wid('group_start_desktop')
        g.hidden = false
        g:disable(false)
        ktw:cancel(g)
        ktw:tween(g, 0.3, g, {alpha=1}, 'linear', function() g:enable(false) end)
    end

end

function screen:hide_buttons()
    -- hide bottom row (services and social)
    for _,n in pairs(group_names)
    -- TODO group_age_rating
    do
        local b = wid(n)
        if b and b.pos_hidden then
            if b.tweener then
                timer:cancel(b.tweener)
            end
            b.tweener = timer:tween(0.4, b.pos,  {x=b.pos_hidden.x, y=b.pos_hidden.y}, 'out-back',
                                    function() b.hidden = true end)
        end
    end

    if IS_MOBILE then 
        wid('button_start'):disable(false)
        ktw:cancel(wid('label_start'))
        ktw:tween(wid('label_start'), 0.3, wid('label_start'), {alpha=0})
    else
        local g = wid('group_start_desktop')
        g:disable(false)
        ktw:cancel(g)
        ktw:tween(g, 0.3, g, {alpha=0}, 'linear', function() g.hidden=true end )        
    end
end


--- TODO BELOW
--- TODO BELOW
--- TODO BELOW

function screen:check_deep_links()
    if PS.services.deep_links then 
        local link = PS.services.deep_links:get_link()
        if link then
            screen:process_deep_link(link)
        end
    end
end

function screen:process_deep_link(link)
    if features.has_restore_savegame and wid('group_restore') and link ~= nil then
        -- restore link
        if not string.match(link, RC.v.restore_extract_token_regex) then
            log.error('deep link does not match restore format : %s', link)
            return
        else
            if PS.services.deep_links then
                PS.services.deep_links:accept_link(link)
            end
            wid('group_restore'):show(link)
            return
        end
    else
        log.error('group_restore is missing. skipping restore')
    end    
end

function screen:check_news(delay)
    -- call this function once the news are cached and ready
    if not SN then return end

    if PS.services.iap then
        local iap = PS.services.iap
        if not iap:is_premium_valid() or (iap:is_premium() and not RC.v.premium_show_news) then
            return
        end
    end

    -- dont load on underage and on already seeing slots
    if PP:is_underage() or wid('group_slots_list').hidden == false then
        return
    end
    
    local function do_news_button_ani(b)
        ktw:cancel(b)
        ktw:script(b,
                   function(wait)
                       if not b.base_scale_orig then 
                           b.base_scale_orig = b.base_scale and table.deepclone(b.base_scale) or {x=1,y=1}
                       end
                       local bso = b.base_scale_orig
                       while(true) do
                           ktw:tween(b, 0.5, b.base_scale, {x=0.95*bso.x,y=0.95*bso.y}, 'in-out-sine')
                           wait(0.501)
                           ktw:tween(b, 0.5, b.base_scale, {x=1*bso.x,y=1*bso.y}, 'in-out-sine')
                           wait(0.501)
                           if b.stop_animation then                               
                               b.base_scale = V.v(1,1)
                               return
                           end
                       end
                   end
        )
    end

    local b = wid('group_news')

    if SN and SN:has_unseen() then
        if not b.timer then
            do_news_button_ani(b)
            b.timer = true
        end
        b.hidden = nil
    elseif SN and SN:get_news() then 
        if b.timer then 
            b.stop_animation = true
            b.timer = nil
        end
        b.hidden = nil
    else
        b.hidden = true
    end

    if SN then
        local force,force_idx = SN:has_force_show()
        if force then
            if delay then
                local pn = wid('popup_news')
                local ktw = self.window.ktw
                ktw:cancel(pn)
                ktw:after(pn,delay,
                          function()
                              self:show_news(true,force_idx)
                          end
                )
            else
                self:show_news(true,force_idx)
            end
        end
    end
end

-- TODO: refactor / move most of this code to GG5PopUpNews, create new
-- GG5NewsItem to include the loading code, defer link hanlding to GG5PopUpNews
function screen:show_news(forced,force_idx)
    local news_data = SN and SN:get_news()
    if not news_data then return end

    if PS.services.analytics then
        PS.services.analytics:log_event('kr_news_shown','forced',forced and 1 or 0)
    end

    -- TODO: make a new template just for mobile with proper style and size, and remove this custom scale
    if not IS_MOBILE and self.popup_news and self.popup_news.contents and GG5PopUp.static.base_scale and self.popup_news.contents.base_scale == GG5PopUp.static.base_scale then
        self.popup_news.contents.base_scale = table.deepclone(GG5PopUp.static.base_scale)
        self.popup_news.contents.base_scale = V.vv(self.popup_news.contents.base_scale.x * 1.25) 
    end
    
    -- mark seen
    SN:mark_seen()

    -- cleanup
    local rows = self.popup_news:ci('group_news_container')
    rows:remove_children()

    local news_x = 1040  -- TODO: pull from template instead of hardcode
    local drag_width = news_x * (#news_data + 2)

    rows = self.popup_news.slider_view
    rows.size = V.v(drag_width,self.window.size.y)
    self.popup_news.slider_container.size.x = 1040 -- image width
    self.popup_news.slider_container.size.y = 530 -- image height

    rows.drag_limits = V.r(0,0,-drag_width + news_x, 0)
    rows.elastic_limits = V.r(-drag_width,0,drag_width + news_x + 200,0)

    
    local news_position = 0

    local infinite_news_data = {}
    table.insert(infinite_news_data, news_data[#news_data])  -- wraparound at start
    for _, v in ipairs(news_data) do
        table.insert(infinite_news_data, v)
    end
    table.insert(infinite_news_data, news_data[1]) -- wraparound at end

    self.popup_news:set_pages(#news_data)

    -- load rows
    local first_post
    for i=1,#infinite_news_data do
        local post = infinite_news_data[i]
        if i == 2 then first_post = post end  -- infinite_news_data starts with the wraparound 
        local item = KImageView:new_from_table(kui_db:get_table('news_item_view_kr5'))
        local i_load = item:get_child_by_id('news_item_loading')
        local i_img = item:get_child_by_id('news_item_image')
        local i_text = item:get_child_by_id('news_item_text')
        -- basic validation
        if not post.image and not post.text then
            goto next_item
        end
        
        -- has image
        if post.image then 
            if not I:i(post.image, true) then
                SN:cache_image(post.image)
                item.pending_img = post.image
                i_load.hidden = nil
                i_img.hidden = true
                i_load.timer = true
                timer:script(
                    function(wait)
                        local v = i_load
                        while true do
                            v.r = 0
                            timer:tween(1,v,{r=-2*math.pi})
                            wait(1)
                        end
                    end
                )
            else
                i_img:set_image(post.image)
                i_img.hidden = false
            end
        else
            i_load.hidden = true
            i_img.hidden = true
        end
        -- has text
        if post.text and post.text ~= '' then 
            i_text.text = post.text
            i_text.hidden = false
        else
            i_text.text = ''
            i_text.hidden = true
        end
        -- has link
        if post.link then
            item.news_url = post.link
            i_text.on_click = function()
                rows:open_link()
            end
            i_img.on_click = i_text.on_click
        end
        item.pos.x = item.pos.x + (item.size.x * news_position)
        item.post = post
        rows:add_child(item)
        news_position = news_position + 1
        ::next_item::
    end

    -- notify impression of first item
    if first_post then
        signal.emit(SGN_PS_NEWS_URL_SHOWN,
                    first_post.link,
                    'news'
        )
    end
    
    self:refresh_news()

    -- animate
    if forced and force_idx then
        log.debug('jumping to force_idx: %s', force_idx)
        self.popup_news:jump_to_page(force_idx)
    end
    self.popup_news:show()

    rows.pos.x = -news_x
end

function screen:hide_news()
    self:check_news()
    self.popup_news:hide()
end

-- TODO: refactor / move most of this code to GG5PopUpNews
function screen:refresh_news()
    local item_margin_y = 15
    local loading_size_y = 200
    local border_margin = 2

    local item_y = 0
    local rows = self.popup_news:ci('group_news_container')
    for i,v in ipairs(rows.children) do
        local i_bg = v:get_child_by_id('news_item_bg')
        local i_load = v:get_child_by_id('news_item_loading')
        local i_img = v:get_child_by_id('news_item_image')
        local i_text = v:get_child_by_id('news_item_text')
        -- load pending images
        if v.pending_img then
            if I:i(v.pending_img, true) then
                i_img:set_image(v.pending_img)
                i_img.hidden = false
                i_load.hidden = true
                v.pending_img = nil
            end
        end
        -- adjust image scale and size
        local iscale = 1
        local isize_y = 0
        if not i_img.hidden then
            isize_y = i_img.size.y
            iscale = i_bg.size.x / i_img.size.x
            i_img.scale.x = iscale
            i_img.scale.y = iscale            
        elseif not i_load.hidden then
            isize_y = loading_size_y
            iscale = 1
        end
        -- adjust text size
        if i_text.hidden then
            i_text.size.y = 0
        else
            local tw,tn,tl = i_text:get_wrap_lines()
            local h = tn * i_text:get_font_height()
            local des = i_text:get_font_descent()
            i_text.size.y = h - des
            i_text.text_size.y = h - des
        end
    end
end

function screen:show_age_rating_popup()
    wid('age_rating_label').text_key=features.show_age_rating_popup.text_key
    
    local ov = wid('age_rating_popup')
    local y_shown = ov.pos_shown and ov.pos_shown.y or 160
    wid('overlay_view').hidden = false
    wid('overlay_view').alpha = 0    
    timer:tween(0.4, wid('overlay_view'), {alpha=1})
    ov.hidden = false
    timer:tween(0.4, wid('age_rating_popup').pos, {y=y_shown}, 'out-back')    
end

function screen:hide_age_rating_popup()
    local ov = wid('age_rating_popup')
    local y_hidden = ov.pos_hidden and ov.pos_hidden.y or -150
    timer:tween(0.4, wid('overlay_view'), {alpha=0}, 'in-back', function() wid('overlay_view').hidden=true end )
    timer:tween(0.4, wid('age_rating_popup').pos, {y=y_hidden}, 'in-back', function() wid('age_rating_popup').hidden=true end )
end

function screen:show_cloudsave_error(error_code)
    if not self.popup_error then
        self.popup_error = GG5PopUpError:new_from_table(kui_db:get_table('popup_error',nil))
        self.popup_error.pos.x = self.window.size.x/2
        self.popup_error.pos.y = 366.85
        self.popup_error.hidden = false
        self.popup_error:ci('label_button_ok').text = _('BUTTON_DONE')
        self.popup_error:ci('button_popup_confirm_ok').on_click = function()
            self.popup_error.hidden = true
            screen.c_show_slots()
        end
        self.window:add_child(self.popup_error)
    end

    self.popup_error.hidden = false
    self.popup_error:show(string.format("Cloud error code: %s", error_code))
end

function screen:show_version_block(error_code)
    if not self.popup_error then
        self.popup_error = GG5PopUpError:new_from_table(kui_db:get_table('popup_error',nil))
        
        self.popup_error.pos.x = self.window.size.x/2
        self.popup_error.pos.y = 366.85
        self.popup_error.hidden = false
        self.popup_error:ci('label_error_msg').text = _('UPDATE_POPUP')
        self.popup_error:ci('label_button_ok').text = _('BUTTON_DONE')
        self.popup_error:ci('button_popup_confirm_ok').on_click = function()
           love.system.openURL(RC.v.url_store[version.bundle_id] or RC.v.url_store.default)
        end
        self.window:add_child(self.popup_error)
        screen:hide_buttons()
        screen:c_hide_slots()
        screen.block = true
        wid('button_start'):disable(true)
    end

    self.popup_error.hidden = false
    self.popup_error:show(_('ALERT_VERSION'))
end

function screen:show_cloudsave_progress()
    self.popup_processing.hidden = false
    self.popup_processing_background.hidden = false
end
function screen:hide_cloudsave_progress()
    self.popup_processing.hidden = true
    self.popup_processing_background.hidden = true
end

function screen:show_auth_error(error_code, error_msg)
    -- error msg has 3 parts separated by |
    -- 1: internal error message
    -- 2: error title (localized)
    -- 3: error content (localized)
    local __,error_title,error_content
    if error_msg and error_msg ~= '' then
        __,error_title,error_content = unpack(string.split(error_msg, '|'))
        log.error('title:%s content:%s', error_title, error_content)
    end

    if not self.popup_error then
        self.popup_error = GG5PopUpError:new_from_table(kui_db:get_table('popup_error',nil))
        self.popup_error.pos.x = self.window.size.x/2
        self.popup_error.pos.y = 366.85
        self.popup_error.hidden = false
        self.popup_error:ci('label_button_ok').text = _('BUTTON_DONE')
        self.popup_error:ci('button_popup_confirm_ok').on_click = function()
            self.popup_error.hidden = true
        end
        self.window:add_child(self.popup_error)
    end

    local content_text = ''
    if error_title then
        content_text = error_title .. '\n'
    end
    if error_content then
        content_text = content_text .. error_content
    end
    if content_text == '' then 
        content_text = _('ERROR_MESSAGE_GENERIC') .. '\n' .. tostring(error_code)
    end

    content_text = "Auth error" .. '\n' .. tostring(content_text)

    self.popup_error.hidden = false
    self.popup_error:show(content_text)
end
function screen:show_auth_progress(request_id)
    self.popup_processing.hidden = false
    self.popup_processing_background.hidden = false
end

function screen:hide_auth_progress()
    self.popup_processing.hidden = true
    self.popup_processing_background.hidden = true
end

------------------------------------------------------------
-- ISM commands and queries
------------------------------------------------------------
function screen.c_show_slots()
    if screen.block then
        return
    end
    if not IS_MOBILE then
        local ktw = screen.window.ktw
        local logo = wid('bg_exo_logo')
        ktw:cancel(logo)
        ktw:tween(logo, 0.20, logo.pos, {y=logo.pos_up.y}, 'out-quad')
        ktw:tween(logo, 0.20, logo.scale, {x=logo.scale_up.x,y=logo.scale_up.y}, 'out-quad')
        wid('group_start_desktop'):disable(false)
    end
    wid('group_slots_list').base_scale = V.vv(OVT(0.9, OV_PHONE,0.9, OV_TABLET,0.5, OV_DESKTOP,0.6))
    wid('group_slots_list'):show()
end

function screen.c_hide_slots(ctx,no_buttons)
    if not IS_MOBILE then
        local ktw = screen.window.ktw
        local logo = wid('bg_exo_logo')
        ktw:cancel(logo)
        ktw:tween(logo, 0.8, logo.pos, {y=logo.pos_shown.y}, 'in-out-back')
        ktw:tween(logo, 0.8, logo.scale, {x=1,y=1}, 'out-quad')
        wid('group_start_desktop'):enable()
    end
    wid('group_slots_list'):hide()
    if no_buttons == nil then
        timer:after(0.4,
                    function() screen:show_buttons() end
        )
    end
end
function screen.c_show_quit_confirm(ctx,no_buttons)
    S:queue('GUIButtonCommon')
    local p = wid('popup_message')
    p:set_msg(_('CONFIRM_EXIT'))
    p:enable()
    p:set_ok_fn(
        function()
            screen.c_quit()
        end
    )
    p:show()
end

function screen.q_channel_quit()
    if PS.services.channel and PS.services.channel:should_hide_quit_prompt() then
        return true
    end
end

function screen.c_channel_quit()
    PS.services.channel:quit_game()
end

function screen.c_quit()
    screen.done_callback({quit = true})    
end

------------------------------------------------------------
-- custom classes
------------------------------------------------------------

SlotView = class('SlotView', KView)

function SlotView:initialize()
    local function sid(name)
        return self:get_child_by_id(name)
    end
    --
    SlotView.super.initialize(self)

    -- the slots must be named slot_x where x is the idx of the slot
    local id_parts = string.split(self.id,'_')
    self.slot_idx = id_parts[#id_parts]
    
    -- hook up views and text values
    sid('slot_used').on_click = function()
        S:queue('GUIButtonCommon')
        screen:handle_slot_button(self.slot_idx)
    end
    
    sid('slot_empty').on_click = function()
        S:queue('GUIButtonCommon')
        screen:handle_slot_button(self.slot_idx)
    end

    sid('slot_delete_request').on_click = function()
        S:queue('GUIButtonCommon')
        sid('slot_used').hidden = true
        sid('slot_delete').hidden = false
        sid('slot_delete_cancel'):focus(true)
    end
    
    sid('slot_delete_cancel').on_click = function()
        S:queue('GUIButtonCommon')
        sid('slot_used').hidden = false
        sid('slot_delete').hidden = true
        sid('slot_used'):focus(true)
    end
    
    sid('slot_delete_confirm').on_click = function()
        S:queue('GUIButtonCommon')
        self:delete_slot(self.slot_idx)
    end

    -- localize buttons
    sid('label_slot_name').text = string.format("%s %i", _('SLOT_NAME'), tostring(self.slot_idx))

    self:show()    
end

function SlotView:show()
    local function sid(name) return self:get_child_by_id(name) end
    
    local slot = storage:load_slot(self.slot_idx,true )  -- force load
    if not slot then
        -- slot empty
        sid('slot_used').hidden = true
        sid('slot_empty').hidden = false
        sid('slot_delete').hidden = true
        sid('slot_empty'):focus(true)
    else
        -- slot exists
        sid('slot_used').hidden = false
        sid('slot_empty').hidden = true
        sid('slot_delete').hidden = true
        sid('slot_used'):focus(true)

        -- WARNING: must use get_slot_stats to get the last_victory stars not yet moved into levels
        local num_stars,num_heroic,num_iron = storage:get_slot_stats(slot)
        num_stars = num_stars + (num_heroic + num_iron) * (GS.stars_per_mode == 0 and -1 or 0)
        sid('label_stars').text = tostring(num_stars) .. "/" .. tostring(GS.max_stars)
        sid('label_heroic').text = tostring(num_heroic)
        sid('label_iron').text = tostring(num_iron)
    end    
end

function SlotView:delete_slot()
    storage:delete_slot(self.slot_idx)
    self:show()
end

------------------------------------------------------------
-- RestoreView

local ERROR_RESTORE_REQUEST_FAILED = 1
local ERROR_RESTORE_REQUEST_DATA_EMPTY = 2
local ERROR_RESTORE_JSON_PARSE_FAILED = 3
local ERROR_RESTORE_INVALID_LINK = 4
local ERROR_RESTORE_DATA_STRUCTURE_INVALID = 5

RestoreView = class('RestoreView', KView)

function RestoreView:initialize(size)
    KView.initialize(self,size)
    self:ci('restore_view_close_button').on_click =
        function(this)
            S:queue('GUIButtonOut')
            self:hide()
        end
end

function RestoreView:show(link)

    -- start loading data from url
    local function cb_restore(status, req, url, code, header, data)
        log.debug('cb_restore(status:%s, req.id:%s url:%s http_code:%s)',status,req.id,url,code)
        local ok,restore_data
        if status ~= 0 then
            local msg = string.format('http error: request failed. status:%s url:%s', status, url)
            log.error(msg)
            self:show_error(ERROR_RESTORE_REQUEST_FAILED,msg)
            return
        elseif not data then
            local msg = string.format('http error: data is empty for url %s', url)
            log.error(msg)
            self:show_error(ERROR_RESTORE_REQUEST_DATA_EMPTY,msg)
            return
        end
        self.data = data
        ok,restore_data = PS.services.http:parse_json(data)
        if not ok then
            local msg = string.format('http error: failed parsing json %s', data)
            log.error(msg)
            self:show_error(ERROR_RESTORE_JSON_PARSE_FAILED,msg)
            return
        else 
            self:refresh(restore_data)
        end
    end

    log.debug('requesting restore data...')
    local token = string.match(link, RC.v.restore_extract_token_regex)
    if not token then
        local msg = string.format('link error: failed to extract the token from %s', link)
        log.error(msg)
        self:show_error(ERROR_RESTORE_INVALID_LINK,msg)
        return
    end
    local slink = string.format(RC.v.restore_url_fmt,token)
    local headers = {
        ['accept'] = 'application/text',  -- DO NOT USE json or it will come escaped
        ['ih-bundleId']        = version.bundle_id,
        ['ih_bundle']          = version.bundle_id,
        ['ih_appversion']      = version.string_short,
    }
    self.rid = PS.services.http:get(slink, headers, cb_restore, 30)

    -- preparing view
    self:ci('restore_in_progress').hidden = false
    for _,n in pairs({'restore_error_label',
                      'restore_error_code_label',
                      'restore_pick_slot_label',
                      'restore_pick_slot_add_gems_label',
                      'restore_new_stats',
                      'restore_add_gems',
                      'restore_slots'}) do
        local v = self:ci(n)
        if v then v.hidden = true end
    end

    -- hide slots if showing
    if (wid('group_slots_list') and not wid('group_slots_list').hidden) then 
        screen:c_hide_slots({},true)
    end    
    screen:hide_buttons()
    
    self.hidden = false
end

function RestoreView:hide()
    screen:show_buttons()
    self.hidden = true
end


function RestoreView:refresh(restore_data)
    log.debug('restore_data data:%s', restore_data)

    local new_slot,only_gems = storage:restore_slot(restore_data)
    if DEBUG then 
        self._new_slot = new_slot
        self._only_gems = only_gems
    end

    if not new_slot then
        local msg = string.format('the restore data is invalid')
        log.error(msg)
        self:show_error(ERROR_RESTORE_DATA_STRUCTURE_INVALID ,msg)
        return
    end

    self:ci('restore_in_progress').hidden = true
    self:ci('restore_error_label').hidden = true
    self:ci('restore_error_code_label').hidden = true
    self:ci('restore_slots').hidden = false

    if only_gems then
        -- explain you're adding gems to an existing slot
        self:ci('restore_pick_slot_add_gems_label').hidden = false
        local gv = self:ci('restore_add_gems')
        gv.hidden = false
        gv:ci('l_gems').text = tostring(new_slot.gems)
        
    else
        -- new data
        self:ci('restore_pick_slot_label').hidden = false

        -- WARNING: must use get_slot_stats to get the last_victory stars not yet moved into levels
        local pstars,pheroic,piron = storage:get_slot_stats(new_slot)
        pstars = pstars + (pheroic + piron) * (GS.stars_per_mode == 0 and -1 or 0)
        local ns = self:ci('restore_new_stats')
        ns.hidden = false
        ns:ci('l_stars').text = tostring(pstars) .. '/' .. tostring(GS.max_stars)
        ns:ci('l_heroic').text = tostring(pheroic)
        ns:ci('l_iron').text = tostring(piron)
    end
    
    -- slots to replace
    for _,b in pairs(self:ci('restore_slots').children) do
        b:ci('slot_used').on_click = function()
            S:queue('GUIButtonCommon')
            if only_gems then
                -- add gems
                log.debug('adding %s gems to slot %s', new_slot.gems, b.slot_idx)
                local slot = storage:load_slot(b.slot_idx)
                slot.gems = slot.gems or 0
                slot.gems = slot.gems + new_slot.gems or 0
                storage:save_slot(slot, b.slot_idx, true)
            else
                -- replace slot
                log.debug('replacing slot %s with new restore data', b.slot_idx)
                storage:delete_slot(b.slot_idx) -- triggers cloud delete
                storage:save_slot(new_slot, b.slot_idx, true) -- force sync
            end
            self:hide()
        end
        b:ci('slot_empty').on_click = function()
            S:queue('GUIButtonCommon')
            log.debug('using empty slot %s with new restore data', b.slot_idx)
            storage:save_slot(new_slot, b.slot_idx, true) -- force sync
            self:hide()
        end
        b:ci('slot_delete_cancel').on_click = function() end
        b:ci('slot_delete_confirm').on_click = function() end
        b:ci('slot_delete_request').hidden = true;
        b:show()
    end
    
end

function RestoreView:show_error(code,msg)
    log.error('showing error code %s : %s', code, msg)
    self:ci('restore_error_label').hidden = false
    self:ci('restore_error_code_label').hidden = false
    self:ci('restore_error_code_label').text = "ERROR CODE:" .. tostring(code)

    for _,n in pairs({'restore_in_progress', 'restore_pick_slot_label', 'restore_pick_slot_add_gems_label', 'restore_new_stats', 'restore_add_gems', 'restore_slots'}) do
        local v = self:ci(n)
        if v then v.hidden = true end
    end
end

------------------------------------------------------------

return screen


