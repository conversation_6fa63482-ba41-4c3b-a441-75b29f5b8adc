--
--  kr5 - map screen
--
------------------------------------------------------------
-- luacheck: globals _ screen_map version
--
-- from kviews_gg_sequels:
-- luacheck: globals GGLabel
-- luacheck: globals GG5PopUp
-- 
-- from sequels/kviews_screen_map_sequels and krX/kviews_screen_map_game:
-- luacheck: globals HeroRoomView StageFlag5
-- luacheck: globals AchievementsPageButton EncycEnemyThumbView
--
------------------------------------------------------------


local log = (require 'klua.log'):new('screen_map')
local class = require 'middleclass'
local DI = require 'difficulty'
local E = require 'entity_db'
local F = require 'klove.font_db'
local G = love.graphics
local GS = require 'game_settings'
local GU = require 'gui_utils'
local I = require 'klove.image_db'
local ISM = require 'klove.input_state_machine'
local S = require 'sound_db'
local SU = require 'screen_utils'
local U = require 'utils'
local UPGR = require 'upgrades'
local V = require 'klua.vector'
local km = require 'klua.macros'
local i18n = require 'i18n'
local storage = require 'storage'
local signal = require 'hump.signal'
local timer = require('hump.timer').new()
local ktw = require('klove.tween').new(timer)
local utf8 = require('utf8')
local utf8_string = require('klove.utf8_string')
local PP = require 'privacy_policy_consent'
local PS = require 'platform_services'
local RC = require 'remote_config'
local marketing = require 'marketing'
local features = require 'features'
local achievements_data = require 'data.achievements_data'
local map_data = require 'data.map_data'
local iap_data = require 'data.iap_data'

require 'klove.kui'
local kui_db = require 'klove.kui_db'
require 'constants'
require 'kviews_gg_sequels'
require 'kviews_screen_map_sequels'
require 'kviews_screen_map_game'

local dbe
if DBG_OTO_EDITOR or DBG_HERO_ROOM_EDITOR or DBG_SHOP_EDITOR or DBG_MAP_EDITOR then
    dbe = require 'debug_view_editor'    
end

------------------------------------------------------------
-- screen_map

-- global
screen_map = {}

if DEBUG then
    -- list of lua files reloaded with screen_map when pressing Shift-R 
    screen_map.reload_list = { 'kviews_gg_sequels', 'kviews_screen_map_sequels', 'kviews_screen_map_game',}
end

screen_map.required_sounds = {'common','music_screen_map'}

screen_map.required_textures = {
    'screen_map',
    'screen_map_bg',
    'screen_map_hud',
    'screen_map_stage_thumbs',
    'room_achievements',
    'room_difficulty',
    'room_cards',
    'room_common',
    'room_shop',
    'room_hero',
    'room_tower',
    'room_upgrades',
    'room_item',
    'room_levelselect',
    'achievements',
    'gui_popups', 
    'gui_portraits',
}
if not IS_MOBILE then table.insert(screen_map.required_textures, 'gui_popups_desktop') end

if KR_GAME ~= 'kr3' then table.insert(screen_map.required_textures, 'screen_map_road') end  -- TODO CLEANUP

screen_map.ref_w = 1728
screen_map.ref_h = 768
screen_map.ref_res = TEXTURE_SIZE_ALIAS.ipad

screen_map.ism_state = 'LAST'

------------------------------------------------------------
-- helper functions

local function wid(name)
    return screen_map.window:get_child_by_id(name)
end

------------------------------------------------------------
-- signal handlers

screen_map.signal_handlers = {
    [SGN_SHOP_GEMS_CHANGED] =
        function()
            screen_map:update_gems()
        end,

    [SGN_SHOP_SHOW_IAP_PROGRESS] =
        function()       
             screen_map:show_iap_progress()
        end,
    
    [SGN_SHOP_SHOW_MESSAGE] =
        function(kind,arg)
            arg = arg or 'error unknown'
            if kind == 'iap_error' then
                screen_map:show_error(arg)
            else
                screen_map:show_message(kind,arg)
            end
        end,

    [SGN_SHOW_GEMS_STORE] =
        function(no_title)
            -- screen_map:show_gems_store(no_title)
        end,
    
    [SGN_PS_STATUS_CHANGED] =
        function(service_name, status, opt_msg)
            -- log.debug(SGN_PS_STATUS_CHANGED .. ' : %s %s', service_name, status)
            -- if service_name == 'leaderboards' and PP:can_show('leaderboards') then 
            --     wid('endless_level_rankings_button').alpha = ( status == true and 1 or 0.5 )
            -- end
        end,
    [SGN_PS_REMOTE_CONFIG_SYNC_FINISHED] =
        function(service_name, status, opt_msg)
            if PS.services.iap then
                PS.services.iap:sync_products()
            end
        end,
    
    [SGN_PS_PURCHASE_PRODUCT_FINISHED] =
        function(service_name, success, product_id, currency, is_restored)
            log.debug(SGN_PS_PURCHASE_PRODUCT_FINISHED .. ' : %s %s %s', service_name, success, product_id, currency, is_restored)
            -- hide progress view
            screen_map:hide_iap_progress()
            if success then
                if PS.services.iap then
                    local p = PS.services.iap:get_product(product_id,true)
                    if p and p.gems then
                        -- gems fx
                        S:queue('InAppEarnGems')
                    end
                end
                -- refresh gems
                screen_map:update_gems()
                screen_map:update_hero_room()
                screen_map:update_tower_room()
                screen_map:update_item_room()
                -- refresh offers
              --  screen_map:hide_offer_icons()
              --  screen_map:refresh_offer_icons()
            
                -- refresh hero room if currently visible
                if not wid('hero_room_view').hidden then
                    local hero_name = string.gsub(product_id,'sale_','')
                    screen_map:show_hero_room(hero_name, true)
                end
                if not wid('tower_room_view').hidden then
                    local tower = string.gsub(product_id,'tower_','')
                     tower = string.gsub(tower,'sale_','')
                    screen_map:show_tower_room(tower, true)
                end
                if not wid('shop_room_view').hidden then
                    local offer,exp_time = marketing:get_active_offer()
                    if not offer or (offer and offer.id == product_id) then
                        offer = marketing:get_one_time_offer(false)
                        if offer then
                            exp_time = marketing:set_active_offer(offer)
                        end 
                    end
                    wid('shop_room_view'):refresh_offers(true)
                    wid('group_offer_icon'):update_offer(offer,exp_time)

                    local peristent_offers = marketing:get_candidate_offers(true) or {}
                    for k, v in pairs(peristent_offers) do
                        if v.season_offer and wid('group_offer_icon_' .. v.season_offer) then
                            wid('group_offer_icon_' .. v.season_offer).hidden = false
                        end
                    end

                    if PS.services.iap then
                        screen_map:hide_shop()
                        if string.find(product_id,'sale_gems_') then
                            product_id = string.gsub(product_id,'sale_','')
                        end
                        screen_map:show_product_cards(product_id)
                        
                    end
                end
                
                -- hide offers if showing
                -- if not wid('one_time_offer_view').hidden then
                --     screen_map:hide_one_time_offer()
                -- end
            else
                -- if not self.popup_error then
                --     self.popup_error = GG5PopUpError:new_from_table(kui_db:get_table('popup_error',nil))
                --     self.popup_error.pos.x = self.window.size.x/2
                --     self.popup_error.pos.y = 366.85
                --     self.popup_error.hidden = false
                --     self.popup_error:ci('label_button_ok').text = _('BUTTON_DONE')
                --     self.popup_error:ci('button_popup_confirm_ok').on_click = function()
                --         self.popup_error.hidden = true
                --     end
                --     self.window:add_child(self.popup_error)
                -- end
            
                -- self.popup_error.hidden = false
                -- self.popup_error:show(_("KR5_PURCHASE_ERROR"))
            end
        end,
    
    [SGN_PS_SYNC_PRODUCTS_FINISHED] = 
        function(service_name, success)
            if success then
                wid('shop_room_view'):refresh_offers(true)
                local offer,exp_time = marketing:get_active_offer()
                wid('group_offer_icon'):update_offer(offer,exp_time)

                local peristent_offers = marketing:get_candidate_offers(true) or {}
                for k, v in pairs(peristent_offers) do
                    if v.season_offer and wid('group_offer_icon_' .. v.season_offer) then
                        wid('group_offer_icon_' .. v.season_offer).hidden = false
                    end
                end
            end
        end,

    [SGN_PS_RESTORE_PURCHASES_FINISHED] =
        function(service_name, success)
            log.debug(SGN_PS_RESTORE_PURCHASES_FINISHED .. ' : %s', success)
            -- hidding progress here is not done here, but in sync purchases finished because:
            -- iOS: calls sync_purchases
            -- Android: it's just sync_purchases, so this event will not trigger.
        end,

    [SGN_PS_SYNC_PURCHASES_FINISHED] =
        function(service_name, success)
            -- If it was not hidden in purchase product finished, progress will be hidden here
            -- This should only be necessary on Android
            screen_map:hide_iap_progress()
        end,
    
    [SGN_PS_AD_SHOW_VIDEO_FINISHED] =
        function(service_name, success, data, status)
            log.debug(SGN_PS_AD_SHOW_VIDEO_FINISHED .. ' : %s reward:%s', success, data)
            screen_map:hide_iap_progress()
            if success then
                if PS.services.fullads then
                    -- fullad workflow
                    log.error('TODO!')
                    --PS.services.fullads:do_fullads_workflow(data, 'reward')
                    --screen_map:refresh_fullads_icons()
                elseif data and data.rewards and data.rewards.gems then 
                    -- normal gems reward
                    screen_map:show_message('reward',data.rewards.gems)
                    S:queue('InAppEarnGems')
                end
                -- refresh gems
                screen_map:update_gems()
            elseif status == 31 then -- closed early, silent failure
                log.debug(SGN_PS_AD_SHOW_VIDEO_FINISHED .. ' : video closed early, no reward given. status: %s', status )
            else
                screen_map:show_message('reward_error')
            end
            -- prepare the next one
            PS.services.ads:cache_video_ad()
        end,
    [SGN_PS_CHANNEL_QUIT_REQUESTED] =
        function(service_name, error_msg)
            -- log.error(SGN_PS_CHANNEL_QUIT_REQUESTED .. ' : %s %s', service_name, error_msg)
            -- screen_map:show_message('channel_quit_game', error_msg)
        end,
    ['map-pan-to-flag'] =
        function(flag)
            if not flag then return end
            local w = screen_map.window
            local mv = w:ci('map_view')
            local mx,my = flag:view_to_view(0,0, mv)  -- convert to map coords
            w:ci('map_view'):pan_to_map_coord(mx,my, true)
        end,
}


------------------------------------------------------------
-- screen_map functions

function screen_map:init(w,h,done_callback)
    self.done_callback = done_callback        
    local user_data = storage:load_slot(nil,true)  -- force slot load

    -- load templates with normal difficulty (for encyclopedia)
    E:load()
    DI:set_level(DIFFICULTY_NORMAL)
    DI:patch_templates()    

    ------------------------------------------------------------
    -- process store.last_victory (see slot_template for format)
    -- unlock_data:
    --  - new_level         : id of level unlocked but not won yet
    --  - show_stars_level  : level to show stars (just won)
    --  - star_count_before : when showing stars, what was the count of stars before, to animate it
    --  - heroic_level      : level just won in heroic
    --  - iron_level        : level just won in iron mode
    --  - unlocked_levels   : list with levels to be unlocked (for parallel unlocking of updates)
    self.unlock_data = {}
    self.unlock_data.unlocked_levels = {}

    local map_data = require 'data.map_data'

    -- update hero data according to iap/premium/free state
    if not PS.services.iap or PS.services.iap:is_premium() then
        screen_map.hero_data =  table.deepclone(map_data.hero_data_free)  -- modified below for premium
        screen_map.hero_order = map_data.hero_order_free
    else
        screen_map.hero_data = map_data.hero_data_iap
        screen_map.hero_order = map_data.hero_order_iap
    end

    self:update_tower_data()

    local levels = user_data.levels
    local victory = user_data.last_victory
    local show_card_rewards = false

    local premium, exceptions = nil, nil
    if PS.services.iap then
        premium, exceptions = PS.services.iap:is_premium()
    end
    
    if victory then
        local level = levels[victory.level_idx]

        if not level then
            -- sanity checks
            log.error('victory level %s was not shown in map before. ignoring victory', victory.level_idx)
            goto skip
        end
        if victory.level_idx > GS.last_level then
            -- sanity check: this save game is probably from a newer version of the game
            -- so sadly, discard the victory.
            log.error('victory level %s was discarded for being from a newer version of the game', victory.level_idx)
            goto skip
        end

        if victory.level_mode == GAME_MODE_CAMPAIGN then
            if not level[GAME_MODE_CAMPAIGN] then
                -- first time winning campaign
                level.stars = victory.stars
                self.unlock_data.show_stars_level = victory.level_idx
                self.unlock_data.star_count_before = 0
                local next_level_idx = U.find_next_level_in_ranges(GS.level_ranges, victory.level_idx)
                if not levels[next_level_idx] then
                    -- mark next level as shown
                    levels[next_level_idx] = {}
                    self.unlock_data.new_level = next_level_idx
                    table.insert(self.unlock_data.unlocked_levels, self.unlock_data.new_level)
                end

                -- TODO: possible refactor. Do we need this here? 
                if victory.level_idx > GS.main_campaign_levels then
                    for k, v in pairs(screen_map.hero_data) do
                        if v.available_at_stage == victory.level_idx + 1 and not (v.iap and exceptions) then
                            show_card_rewards = true
                            break
                        end
                    end
                    if not show_card_rewards then
                        for k, v in pairs(screen_map.tower_data) do
                            if v.available_at_stage == victory.level_idx + 1 and not (v.iap and exceptions) then
                                show_card_rewards = true
                                break
                            end
                        end
                    end
                else
                    show_card_rewards = true
                end
                -- ------------------------------------------------

            elseif victory.stars > level.stars then
                -- improving on campaign
                self.unlock_data.show_stars_level = victory.level_idx
                self.unlock_data.star_count_before = level.stars
                level.stars = victory.stars
            end

        elseif victory.level_mode == GAME_MODE_HEROIC then
            self.unlock_data.heroic_level = ( not level[GAME_MODE_HEROIC] ) and victory.level_idx or nil

        elseif victory.level_mode == GAME_MODE_IRON then
            self.unlock_data.iron_level = ( not level[GAME_MODE_IRON] ) and victory.level_idx or nil

        end

        -- won difficulty: keep highest won, even with less stars
        level[victory.level_mode] = math.max(victory.level_difficulty, ( level[victory.level_mode] or 0 ) )

        ::skip::
        -- clean up last victory and save changes
        user_data.last_victory = nil
        storage:save_slot(user_data)

    elseif #levels == 0 then
        -- first load of the map. 
        self.unlock_data.unlocked_levels = {1}
        levels[1] = {}
        storage:save_slot(user_data)
    end
    
    local unlocked_campaigns = {}
    local owned_dlcs = PS.services.iap and PS.services.iap:get_dlcs(true) or {}
    if premium and not exceptions then
        -- full premium case, does not unlocks dlcs directly
        owned_dlcs = {}
    end
    
    -- unlock after main campaign finishes and next level in range, if missing
    if U.unlock_next_levels_in_ranges(self.unlock_data, levels, GS, owned_dlcs) then
        storage:save_slot(user_data)
        -- not count dlcs for updates
        local dlcsCount = 0
        for i = 2, (#GS.level_ranges), 1 do
            if U.is_dlc_level(GS.level_ranges[i][1]) then
                dlcsCount = dlcsCount + 1
            elseif table.contains(self.unlock_data.unlocked_levels, GS.level_ranges[i][1]) then
                local update_id = string.format("%02d",(i - 1 - dlcsCount))
                table.insert(unlocked_campaigns, "update_" .. update_id)
            end
        end

        if #unlocked_campaigns >= 1 then
            -- show new campaign card
            show_card_rewards = true
        end
    end
    ------------------------------------------------------------
    
    -- count stars
    self.total_stars = U.count_stars(user_data)

    -- silent IAP purchases sync to determine premium mode and update hero data
    if PS.services.iap and PS.services.iap:get_status() then
        PS.services.iap:sync_purchases(true)  -- immediate and silent, ignores consumables
    end

    self:update_item_data()
    
    -- init window
    self.w,self.h = w,h
    local sw,sh,scale,origin = SU.clamp_window_aspect(w,h,self.ref_w,self.ref_h)
    self.sw,self.sh = sw,sh

    GGLabel.static.font_scale = scale
    GGLabel.static.ref_h = self.ref_h

    self.default_base_scale = SU.get_default_base_scale(sw,sh)
    GG5PopUp.static.base_scale = self.default_base_scale  -- before kui_db pass
    RoomView.static.base_scale = self.default_base_scale  -- before kui_db pass
    RoomView.static.sw = sw
    RoomView.static.sh = sh
    RoomView.static.scale = scale
    RoomView.static.origin = origin

    -- IMPORTANT: values used in WHEN cannot be nil!
    local ctx = SU.new_screen_ctx(self)
    ctx.context = 'map'
    ctx.hud_scale = SU.get_hud_scale(w,h,self.ref_w,self.ref_h)    
    ctx.premium = (not PS.services.iap or PS.services.iap:is_premium())
    ctx.fullads = (PS.services.fullads ~= nil)
    ctx.hide_strategy_guide = (not PP:can_show('strategy') or features.hide_strategy_guide) and true or false
    ctx.hide_leaderboards = (not PP:can_show('leaderboards')) and true or false
    ctx.is_new_ui = storage.active_slot_idx == '3'
    ctx.is_underage = PP:is_underage()
    ctx.is_main = false
    ctx.is_censored_cn = features.censored_cn and true or false
    local tt = kui_db:get_table('screen_map',ctx)
    local window = KWindow:new_from_table(tt)    
    window.scale = V.v(scale,scale)
    window.size = {x=sw,y=sh}
    window.origin = origin
    window.timer = timer
    window.ktw = ktw
    window:set_responder(window)
    self.window = window

    ------------------------------------------------------------
    -- post initialization to have window information

    -- adjust ui base scale
    -- NOTE: rooms and popups adjusted via static variables above
    do 
        local views = {}
        table.append(views, wid('group_map_hud').children)
        SU.apply_base_scale(views, self.default_base_scale)
        SU.apply_base_scale(views, V.vv(OVT(1, OV_TABLET,0.6)))  -- override tablet scale
    end
    SU.apply_base_scale({window:ci('card_rewards_view')}, V.vv(OVtargets(1,1,1,0.8,0.8)))
    SU.apply_base_scale({window:ci('level_select_view').contents}, V.vv(OVtargets(1,1,0.8,0.65,0.65)), true)
    
    -- map
    local mv = wid('map_view')

    -- map pan/zoom (also resets drag_limits and zoom)
    wid('map_touch_view'):set_puppet(wid('map_view'))
    
    -- map vignette
    local vignette = wid('vignette_view')
    vignette.scale.x = 1.02 * self.sw / wid('vignette_view').size.x
    vignette.scale.y = 1.02 * self.sh / wid('vignette_view').size.y
    vignette.pos.x = -0.01 * self.sw
    vignette.pos.y = -0.01 * self.sh    

    -- button bars
    self:init_bars()

    wid('button_map_options').on_click = function(this)
        S:queue('GUIButtonCommon')
        wid('popup_options'):show('map')
    end
    
    -- top bar view    
    wid('label_map_stars').text = self.total_stars .. '/' .. GS.max_stars
    
    wid('button_map_heroes').on_click = function()
        S:queue('GUIButtonCommon')
        --TODOwid('map_balloon_hero_unlocked_view'):hide()
        --TODOwid('map_balloon_level_up_view'):hide()
        screen_map:show_hero_room()
    end
    wid('button_map_heroes'):focus(true)

    wid('button_map_towers').on_click = function()
        S:queue('GUIButtonCommon')
        screen_map:show_tower_room()
    end                
    wid('button_map_upgrades').on_click = function()
        S:queue('GUIButtonCommon')
        --TODOwid('map_balloon_upgrades_view'):hide()
        screen_map:show_upgrades()
    end
    --wid('button_map_encyclopedia').on_click = function()
    --    S:queue('GUIButtonCommon')
    --    screen_map:show_encyclopedia()
    --end
    wid('button_map_achievements').on_click = function(this)
        S:queue('GUIButtonCommon')
        screen_map:show_achievements()
    end
    wid('button_map_shop').on_click = function(this)
        S:queue('GUIButtonCommon')
        screen_map:show_shop()
    end
    wid('button_map_hud_buy_gems').on_click = function(this)
        S:queue('GUIButtonCommon')
        screen_map:show_shop_gems()
    end
    
    wid('button_map_items').on_click = function()
        S:queue('GUIButtonCommon')
        screen_map:show_item_room()
    end
    -- prevent buttons from interrupting map pan/zoom
    wid('button_map_options').propagate_on_touch_move = true
    wid('button_map_heroes').propagate_on_touch_move = true
    wid('button_map_towers').propagate_on_touch_move = true
    wid('button_map_upgrades').propagate_on_touch_move = true
    wid('button_map_achievements').propagate_on_touch_move = true
    

    -- TODO: shop
    -- TODO: gems store
    -- TODO: iap progress

    -- TODO: one time offer
    --wid('one_time_offer_view').pos.x = self.sw/2
    --wid('one_time_offer_close_button').on_click = function(this)
    --                                                  S:queue('GUIButtonCommon')
    --                                                  screen_map:hide_one_time_offer()
    --                                              end
    --local function buy_one_time_offer(this)
    --    local offer = screen_map.offer_shown
    --    if not offer then
    --        log.error('screen_map.offer_shown not present')
    --        return
    --    end
    --    if not PS.services.iap or not PS.services.iap:purchase_product(offer.id) then
    --        screen_map:show_message('iap_error')
    --        log.error('Error trying to purchase product %s', offer.id)
    --        return
    --    end
    --    screen_map:show_iap_progress()    
    --end
    --wid('one_time_offer_buy_1_button').on_click = buy_one_time_offer
    --wid('one_time_offer_buy_2_button').on_click = buy_one_time_offer
    --wid('one_time_offer_buy_3_button').on_click = buy_one_time_offer
    --wid('one_time_offer_buy_all_button').on_click = buy_one_time_offer

    -- TODO: ask for rating view
    --wid('ask_for_rating_view').pos.x = self.sw/2
    --wid('ask_for_rating_yes_button').on_click =
    --    function(this)
    --        S:queue('GUIButtonCommon')
    --        local global = storage:load_global()
    --        if global then
    --            global.rating_accepted = true
    --            storage:save_global(global)
    --        end
    --        screen_map:hide_ask_for_rating()            
    --        --
    --        if KR_PLATFORM == 'android' then 
    --            local jnia = require 'all.jni_android'
    --            jnia.launch_market()
    --        end
    --    end
    --wid('ask_for_rating_no_button').on_click =
    --    function(this)
    --        S:queue('GUIButtonCommon')
    --        local global = storage:load_global()
    --        if global then
    --            global.rating_accepted = false
    --            storage:save_global(global)
    --        end
    --        screen_map:hide_ask_for_rating()
    --    end
    --wid('ask_for_rating_close_button').on_click =
    --    function(this)
    --        S:queue('GUIButtonCommon')
    --        -- ask again next time            
    --        screen_map:hide_ask_for_rating()
    --    end

    -- adjust initial volume
    local st = storage:load_settings()
    S:set_main_gain_music(st and st.volume_music or 1)
    S:set_main_gain_fx(st and st.volume_fx or 1)
        
    -- music
    S:queue('MusicMap')

    ----------------------------------------
    -- input
    local ism_data = {
        MODAL = {
            -- used by CardRewardsView, room help tips
            {'return',         self.q_is_modal,   nil,        self.c_hide_modal},
            {'escape',         'return'},
            {'ja',             'return'},
            {'jb',             'return'},
            {'STOP'},  -- prevents processing LAST mode
        },
        LAST = {
            {'escape',         self.q_is_picking_team,  nil,                          self.c_stop_picking_team},
            {'escape',         self.q_is_picking_tower, nil,                          self.c_stop_picking_tower},
            {'escape',         self.q_is_picking_item,  nil,                          self.c_stop_picking_item},
            --
            {'escape',         ISM.q_is_view_visible,   {'difficulty_room_view'},     nil, },
            {'escape',         ISM.q_is_view_visible,   {'gems_store_view'},          ISM.c_hide_view, {'gems_store_view'}},
            {'escape',         ISM.q_is_view_visible,   {'shop_room_view'},           ISM.c_hide_view, {'shop_room_view'}},
            {'escape',         ISM.q_is_view_visible,   {'achievements_room_view'},   ISM.c_hide_view, {'achievements_room_view'}},
            -- TODO: encyclopedia views
            {'escape',         ISM.q_is_view_visible,   {'message_view'},             ISM.c_hide_view, {'message_view'}},
            {'escape',         ISM.q_is_view_visible,   {'popup_options'},            self.hide_popup_options},
            {'escape',         ISM.q_is_view_visible,   {'endless_level_view'},       ISM.c_hide_view, {'endless_level_view'}},
            {'escape',         ISM.q_is_view_visible,   {'level_select_view'},        self.hide_level },
            {'escape',         ISM.q_is_view_visible,   {'hero_room_view'},           self.hide_hero_room },
            {'escape',         ISM.q_is_view_visible,   {'tower_room_view'},          self.hide_tower_room },
            {'escape',         ISM.q_is_view_visible,   {'upgrades_room_view'},       self.hide_upgrades },
            {'escape',         ISM.q_is_view_visible,   {'item_room_view'},           self.hide_item_room },            
            {'escape',         ISM.q_is_escape_show_quit,nil,                         self.c_return_main_menu},
            {'escape',         true,                    nil,                          ISM.c_show_view, {'popup_options', 'map'}},
            --
            {'return',         true,                    nil,                          ISM.c_send_key , {'return'}},
            {'up',             true,                    nil,                          ISM.c_send_key , {'up'}},
            {'down',           true,                    nil,                          ISM.c_send_key , {'down'}},
            {'left',           true,                    nil,                          ISM.c_send_key , {'left'}},
            {'right',          true,                    nil,                          ISM.c_send_key , {'right'}},
            {'pageup',         ISM.q_is_view_visible,   {'popup_options'},            ISM.c_call_view_fn , {'popup_options', 'change_page', 'prev'}},
            {'pagedown',       ISM.q_is_view_visible,   {'popup_options'},            ISM.c_call_view_fn , {'popup_options', 'change_page', 'next'}},
            {'pageup',         ISM.q_is_view_visible,   {'achievements_room_view'},   ISM.c_call_view_fn , {'achievements_room_view', 'change_page', 'prev'}},
            {'pagedown',       ISM.q_is_view_visible,   {'achievements_room_view'},   ISM.c_call_view_fn , {'achievements_room_view', 'change_page', 'next'}},
            {'pageup',         self.q_is_flag_focused,  nil,                          self.c_focus_next_flag, {-1}},
            {'pagedown',       self.q_is_flag_focused,  nil,                          self.c_focus_next_flag, { 1}},
            --
            {'jleftxy',        ISM.q_rate_limit,        nil,                          ISM.c_send_key_axis },
            {'ja',             true,                    nil,                          ISM.c_send_key,  {'return'}},
            {'jb',             'escape'},
            {'jleftshoulder',  'pageup'},
            {'jrightshoulder', 'pagedown'},
            {'jstart',         'escape'},
            {'jback',          'escape'},
            {'jstart',         true,                    nil,                          ISM.c_show_view, {'popup_options','map'}},
            {'jback',          true,                    nil,                          ISM.c_show_view, {'popup_options','map'}},
            {'jdpright',       'right' },
            {'jdpup',          'up'    },
            {'jdpleft',        'left'  },
            {'jdpdown',        'down'  },
            
        },
    }
    ISM:init(ism_data,window,DEFAULT_KEY_MAPPINGS,storage:load_settings())
    
    ----------------------------------------
    -- platform services polling
    if PS then
        PS.paused = nil  -- poll active to get status updates
    end

    -- register signals
    for sn,fn in pairs(self.signal_handlers) do
        signal.register(sn, fn)
    end
    -- remote config
    RC:sync()

    -- after signals are setup, sync IAP, cache ads
    if PS.services.iap and PS.services.iap:get_status() then
        PS.services.iap:sync_purchases()  -- should be immediate (cached)
        PS.services.iap:sync_products()
    end
    if PS.services.ads then 
        PS.services.ads:cache_video_ad()
    end

   

    -------------------- GOTO SECTION BEGINS --------------------

    -- show top/bottom bars
    self:update_badges()

    -- wid('card_rewards_view'):show(1)
    if show_card_rewards then
        wid('map_view'):show_flags_unlocked()
        if #unlocked_campaigns >= 1 and self.unlock_data.unlocked_levels[1] > GS.main_campaign_levels then
            wid('card_rewards_view'):show(nil, unlocked_campaigns)
        else
            wid('card_rewards_view'):show(victory.level_idx)
        end
    else

        -- hides bars until lvl 2 pass to prevent opening rooms
        -- local last_level_won = 1
        -- for i, v in ipairs(user_data.levels) do
        --     if v['stars'] ~= nil then
        --         last_level_won = i
        --     else
        --         break
        --     end
        -- end
        -- if last_level_won > 1 then
        --     timer:after(0.5,function() self:show_bars() end)
        -- end

        -- timer:after(1,function() self:show_bars() end)

        -- show map flags
        wid('map_view'):show_flags()    

        local offer,exp_time = marketing:get_active_offer()
        if not offer then
            offer = marketing:get_one_time_offer(false)
            if offer then
                exp_time = marketing:set_active_offer(offer)
                wid('group_offer_icon'):update_offer(offer,exp_time)

                local peristent_offers = marketing:get_candidate_offers(true) or {}
                for k, v in pairs(peristent_offers) do
                    if v.season_offer and wid('group_offer_icon_' .. v.season_offer) then
                        wid('group_offer_icon_' .. v.season_offer).hidden = false
                    end
                end
            end 
        end
    end

    -------------------- GOTO SECTION ENDS --------------------
    
    -- custom args
    if self.args then
        if self.args.show == 'shop' then
            self:show_shop()
        end
    end

    if PS.services.analytics then
        PS.services.analytics:log_event('kr_screen_init','file','screen_map')
    end

    -- quit if requested by channel service while the game was in transitions
    if PS and PS.services.channel and PS.services.channel.quit_requested then
        screen_map:show_message('channel_quit_game', PS.services.channel.quit_message)        
    end

    if DBG_HERO_ROOM_EDITOR then
        dbe:inject_editor(wid('hero_room_view'),self)
    elseif DBG_SHOP_EDITOR then
        dbe:inject_editor(wid('shop_view'),self)
        --dbe:inject_editor(wid('shop_items'),self)
    elseif DBG_MAP_EDITOR then
        -- customize!
        --dbe:inject_editor(wid('gems_store_view'),self)
    end

    --setting up cheats
    if DEBUG then
        local cheat_button = wid('cheat_button')
        cheat_button.on_click = function (this)

            if this.cheat_view then
                if this.cheat_view.view.hidden then
                    for _, view in ipairs(this.cheat_view.views) do
                        view:remove_from_parent()
                    end
                    this.cheat_view = nil
                end
            end

            if this.cheat_view then
                for _, view in ipairs(this.cheat_view.views) do
                    view:remove_from_parent()
                end
                this.cheat_view = nil
            else
                -- dynamic reloading
                package.loaded['game_gui_cheats_map'] = nil
                this.cheat_view = require('game_gui_cheats_map')
                this.cheat_view:init()
                for _, view in ipairs(this.cheat_view.views) do
                    this.parent:add_child(view)
                end
                --dbe:inject_editor(this.cheat_window,self)
            end
        end
    end


    -- TEST TEST TEST REPLACING THE CODE BELOW
    -- This forces the creation of the canvas of the shader mixing widgets. Should fix the problems mentioned below
    for _,v in pairs(window:flatten(function(v) return v and v:isInstanceOf(GG5ShaderLabel) end)) do
        --log.todo(' %s : prepare_canvas()', v.id and v.id or v)
        v:prepare_canvas()
    end

    -- 
    -- CCC: this breaks the focused widget and the responder hierarchy. 
    --
    -- open all rooms far from screen to preload textures and prevent glitches on first time open
    --ktw:script(self,
    --    function(wait)
    --
    --        local groups_to_preload = {'hero_room_view', 'tower_room_view', 'upgrades_room_view', 'item_room_view', 'achievements_room_view'}
    --        local bg_original_pos = nil
    --
    --        local far_position = {}
    --        far_position.x = 40000
    --        far_position.y = 40000
    --
    --        for _, room in ipairs(groups_to_preload) do
    --
    --            wid(room)._original_pos = {}
    --            wid(room)._original_pos.x = wid(room).pos.x
    --            wid(room)._original_pos.y = wid(room).pos.y
    --
    --            wid(room).pos.x = far_position.x
    --            wid(room).pos.y = far_position.y
    --
    --            local bg = wid(room):get_window():ci(wid(room).background_id)
    --
    --            if bg_original_pos == nil then
    --                bg_original_pos = V.v(bg.pos.x, bg.pos.y)
    --            end
    --
    --            bg.pos.x = far_position.x
    --            bg.pos.y = far_position.y
    --
    --            bg:show()
    --
    --            wid(room):show(false)
    --        end
    --
    --        wait(1/60)
    --
    --        for _, room in ipairs(groups_to_preload) do
    --            wid(room):hide()
    --        end
    --
    --        wait(1)
    --
    --        for _, room in ipairs(groups_to_preload) do
    --            local bg = wid(room):get_window():ci(wid(room).background_id)
    --
    --            bg.pos.x = bg_original_pos.x
    --            bg.pos.y = bg_original_pos.y
    --
    --            wid(room).pos.x = wid(room)._original_pos.x
    --            wid(room).pos.y = wid(room)._original_pos.y
    --        end
    --
    --        -- TODO TODO TODO / TEMPORARY PATCH 
    --        window:set_responder(window)
    --        wid('button_map_heroes'):focus()
    --        
    --    end
    --)
    if not show_card_rewards then
        screen_map:process_new_dlc()
    end
end

function screen_map:destroy()
    -- remove signals
    for sn,fn in pairs(self.signal_handlers) do
        signal.remove(sn, fn)
    end 
    ISM:destroy(self.window)   
    ktw:clear()
    timer:clear()
    self.window.timer = nil
    self.window.ktw = nil
    self.window:destroy()
    self.window = nil
    SU.remove_references(self,KView)
end

function screen_map:update(dt)
    self.window:update(dt)
    if self.window.timer then 
        self.window.timer:update(dt)
    end
end

function screen_map:draw()
    self.window:draw()
end

function screen_map:keypressed(key,isrepeat)    
    ------------------------------
    -- DEBUG

    if DEBUG and key == 'g' then
        -- add/remove gems
        local user_data = storage:load_slot()
        if love.keyboard.isDown('lshift') then
            user_data.gems = user_data.gems + 100
        else
            user_data.gems = km.clamp(0,10000,user_data.gems - 100)
        end
        storage:save_slot(user_data)
        self:update_gems()
    end
    
    if DBG_OTO_EDITOR or DBG_HERO_ROOM_EDITOR or DBG_SHOP_EDITOR or DBG_MAP_EDITOR then
        dbe:keypressed(self.SEL_VIEW, key, isrepeat)        
    end
    
    if DEBUG_MAP then
        if isrepeat then return end
        local user_data = storage:load_slot()
        
        if not self._test_unlocked_level then self._test_unlocked_level = #user_data.levels end

        local function reset_unlock_data()
            self.unlock_data = {}
            self.unlock_data.unlocked_levels = {}
        end

        local function prepare_test_unlocked_level()
            local new_level = self._test_unlocked_level
            local levels = {}
            levels[new_level] = {}
            if new_level > 1 then
                for i=1,new_level-1 do
                    levels[i] = {2,(math.random()<0.5 and 2),(math.random()<0.5 and 2),stars=math.random(1,3)}
                end
                self.unlock_data.new_level = new_level
                self.unlock_data.show_stars_level = new_level - 1
                self.unlock_data.star_count_before = 0
                self.unlock_data.unlocked_levels = {new_level}

                if (new_level == 16) then
                    levels[19] = {} 
                    self.unlock_data.unlocked_levels = {16,19}
                end
            end
            user_data.levels = levels
        end

        local map_view = wid('map_view')
        
        if map_view.show_flags_in_progress then
            log.debug('show_flags in progress... skipping')
            return
        end

        if key == 'r' then
            map_view:clear_flags()
            reset_unlock_data()
            self._test_unlocked_level = 1
            prepare_test_unlocked_level()
            log.error('restarting flags...')
            map_view:show_flags()

        elseif key == 'n' then
            map_view:clear_flags()
            reset_unlock_data()
            self._test_unlocked_level = self._test_unlocked_level + 1
            prepare_test_unlocked_level()
            map_view:show_flags()

        end

        if self._test_unlocked_level > 1 then
            if key == 's' then
                map_view:clear_flags()
                reset_unlock_data()
                local lvl = self._test_unlocked_level - 1
                self.unlock_data.show_stars_level = lvl
                self.unlock_data.star_count_before = user_data.levels[lvl].stars
                user_data.levels[lvl].stars = km.clamp(1,3,user_data.levels[lvl].stars + 1)
                storage:save_slot(user_data)
                map_view:show_flags()

            elseif key == 'h' then
                map_view:clear_flags()
                reset_unlock_data()
                local lvl = self._test_unlocked_level - 1
                user_data.levels[lvl][2] = 2
                storage:save_slot(user_data)                
                self.unlock_data.heroic_level = lvl
                map_view:show_flags()

            elseif key == 'i' then
                map_view:clear_flags()
                reset_unlock_data()
                local lvl = self._test_unlocked_level - 1
                self.unlock_data.iron_level = lvl
                user_data.levels[lvl][3] = 2
                storage:save_slot(user_data)
                map_view:show_flags()

            end
        end
        
    end
end

function screen_map:keyreleased(key)
end

function screen_map:wheelmoved(dx,dy)
    self.window:wheelmoved(dx,dy)
end

function screen_map:mousepressed(x,y,button,istouch)
    self.window:mousepressed(x,y,button,istouch)
end

function screen_map:mousereleased(x,y,button,istouch)
    self.window:mousereleased(x,y,button,istouch)
end

function screen_map:touchpressed(id, x, y, dx, dy, pressure)
    self.window:touchpressed(id, x, y, dx, dy, pressure)
end

function screen_map:touchreleased(id, x, y, dx, dy, pressure)
    self.window:touchreleased(id, x, y, dx, dy, pressure)    
end

function screen_map:touchmoved(id, x, y, dx, dy, pressure)
    self.window:touchmoved(id, x, y, dx, dy, pressure)
end

----------------------------------------
-- modal views
function screen_map:get_ism_state()
    return self.ism_state
end

function screen_map:set_modal_view(view)
    self.modal_view = view
    self.ism_state = 'MODAL'  -- modal views change ism mode
    log.debug(' SETTING MODAL VIEW: %s', view)
end

function screen_map:remove_modal_view()
    if self.modal_view then 
        self.modal_view = nil
        self.ism_state = 'LAST'
        log.debug(' SETTING MODAL VIEW: %s', view)
    end
end

-- bar views
local bar_names

if IS_MOBILE then
    bar_names = {'group_bottom','group_top_right','group_top_left', 'group_bottom_right', 'group_offer_icon'}
    for k, s in pairs(GS.seasons) do
        table.insert(bar_names, 'group_offer_icon_' .. s)
    end
else
    bar_names = {'group_bottom','group_top_right','group_top_left', 'group_bottom_right'}
end

function screen_map:init_bars()
    -- per platform settings
    if IS_MOBILE then

        if PS.services.iap and PS.services.iap:is_premium() and PS.services.iap:is_premium_valid() then
            wid('group_map_hud'):ci('button_map_hud_buy_gems').hidden = true

            -- hide buttons
            local gr = wid('group_map_hud'):ci('group_bottom')
            local list = {'shop'}
            for _,n in pairs(list) do
                gr:ci('button_map_' .. n).hidden = true
                gr:ci('label_map_' .. n).hidden = true
            end
            -- move button bar to align it
            gr.pos.x = (self.sw/2 + ((gr:ci('button_map_items').pos.x - gr:ci('button_map_upgrades').pos.x)/2)) * gr.base_scale.x
        end
    else
        if features.no_gems then 
            -- hide gems 
            wid('group_map_hud'):ci('label_map_gems').hidden = true
            wid('group_map_hud'):ci('bg_gems').hidden = true
            wid('group_map_hud'):ci('button_map_hud_buy_gems').hidden = true
        end

        -- hide buttons
        local gr = wid('group_map_hud'):ci('group_bottom')
        local list = features.no_gems and {'items', 'shop'} or {'shop'}
        for _,n in pairs(list) do
            gr:ci('button_map_' .. n).hidden = true
            gr:ci('label_map_' .. n).hidden = true
        end
        
        -- move button bar to align it
        if features.no_gems then 
            gr.pos.x = self.sw/2 + (gr:ci('button_map_towers').pos.x - gr:ci('button_map_heroes').pos.x) * gr.base_scale.x
        else
            gr.pos.x = self.sw/2 + (gr:ci('button_map_towers').pos.x - gr:ci('button_map_heroes').pos.x)/2 * gr.base_scale.x
        end
    end

    wid('group_map_hud'):ci('button_map_hud_buy_gems').hidden = features.no_gems or not PS.services.iap or PS.services.iap:is_premium()
    
    for _,n in pairs(bar_names) do
        if string.find(n, 'group_offer_icon') then 
            goto skip 
        end

        local v = wid(n)
        if not v then
            log.error('could not find bar named %s', n)
            goto skip
        end
        
        local delta = self.sh/2
        if not v.pos_shown then 
            v.pos_shown = V.vclone(v.pos)
        end
        if not v.pos_hidden then 
            v.pos_hidden = V.v(v.pos.x, v.pos.y + (delta * (v.pos.y > self.sh/2 and 1 or -1)))
        end
        v.pos.y = v.pos_hidden.y
        v.hidden = true
        ::skip:: 
    end

    if IS_MOBILE then
        local function init_offer_icon(offer_icon)
            local delta = self.sw/2
            if not offer_icon.pos_shown then 
                offer_icon.pos_shown = V.vclone(offer_icon.pos)
            end
            if not offer_icon.pos_hidden then 
                offer_icon.pos_hidden = V.v(offer_icon.pos.x + delta, offer_icon.pos.y)
            end
            offer_icon.pos.x = offer_icon.pos_hidden.x
            offer_icon.hidden = true
        end

        local offer_icons = {'group_offer_icon'}
        for k, s in pairs(GS.seasons) do
            table.insert(offer_icons, 'group_offer_icon_' .. s)
        end

        for k, v in pairs(offer_icons) do
            local offer_icon = wid(v)
            if offer_icon then
                init_offer_icon(offer_icon)
            end
        end
    end
    local new_hero = HeroRoomView:is_new_hero_available()
    local new_tower = TowerRoomView:is_new_tower_available()

    local current_points = UPGR:get_current_points_by_level() - UPGR:get_spent_points()
    local upgrade_points = current_points > 0

    if new_hero then
        wid('map_hud_notification_new_hero'):ci('label_txt_notification_icon').text = _("MAP_NEW_HERO_ALERT")
    else
        wid('map_hud_notification_new_hero').hidden = true
    end
    
    if not HeroRoomView:has_hero_points_to_spend() then
        wid('group_bottom'):ci('alert_heroes').hidden = true
    end

    wid('group_bottom'):ci('alert_towers').hidden = true
    if new_tower then
        wid('map_hud_notification_new_tower'):ci('label_txt_notification_icon').text = _("MAP_NEW_TOWER_ALERT")
    else
        wid('map_hud_notification_new_tower').hidden = true
    end

    if not upgrade_points then
        wid('group_bottom'):ci('alert_upgrades').hidden = true
    end

    local user_data = storage:load_slot()
    wid('group_bottom'):ci('alert_items').hidden = features.no_gems or  (user_data.gems < self.item_data['cluster_bomb'].cost)

    wid('group_bottom'):ci('alert_shop').hidden = features.no_gems or true  -- TODO
    
    local achievement_claimed_pending = false
    for i=1,#achievements_data do
        local ach = achievements_data[i]
        local achievement_completed = user_data.achievements[ach.name]
        if achievement_completed then
            local achievement_claimed = nil
            if user_data.achievements_claimed then
                achievement_claimed = table.contains(user_data.achievements_claimed, ach.name)
            end

            if not achievement_claimed then
                achievement_claimed_pending = true
            end
        end
    end

    if IS_MOBILE and achievement_claimed_pending then
        wid('group_bottom_right'):ci('alert_achievements').hidden = false
    else
        wid('group_bottom_right'):ci('alert_achievements').hidden = true
    end

    local heroes_on_sale = PS.services.iap and PS.services.iap:get_hero_sales() or {}
    if IS_MOBILE and #heroes_on_sale > 1  then
        wid('group_sale_button_overlay_heroes').hidden = false
    else
        wid('group_sale_button_overlay_heroes').hidden = true
    end

    local towers_on_sale = PS.services.iap and PS.services.iap:get_tower_sales() or {}
    if IS_MOBILE and #towers_on_sale > 1  then
        wid('group_sale_button_overlay_towers').hidden = false
    else
        wid('group_sale_button_overlay_towers').hidden = true
    end

    wid('group_bottom'):ci('shadow').scale.x = 4

end

function screen_map:is_tutorial_complete()
    local user_data = storage:load_slot()
    for i, v in ipairs(user_data.levels) do
        if v['stars'] ~= nil then
            return true
        else
            break
        end
    end
    return false
end

function screen_map:show_bars()
    local is_tutorial_complete = self:is_tutorial_complete()
    for _,n in pairs(bar_names) do
        -- hides bottom bar if tutorial incomplete
        if is_tutorial_complete or string.find(n,'top',1,true) then
            --log.todo('showing %s', n)
            local v = wid(n)
            v.hidden = false
            if IS_MOBILE then
                if n == 'group_offer_icon' then
                    local offer,exp_time = marketing:get_active_offer()
                    if not offer or not exp_time then
                        v.hidden = true
                    else
                        local rem_time = os.difftime(exp_time, os.time())
                        v:ci('label_map_shop').text = GU.format_countdown_time(rem_time,false)
                    end
                elseif string.find(n, 'group_offer_icon_') then
                    v.hidden = true

                    if v.season then
                        local peristent_offers = marketing:get_candidate_offers(true) or {}
                        for k, po in pairs(peristent_offers) do
                            if po.season_offer and po.season_offer == v.season then
                                v.hidden = false
                            end
                        end
                    end
                end
            end
            ktw:cancel(v)
            -- only check for rating after showing one bar
            local check_ask_rating = n == bar_names[1]
            ktw:tween(v, 0.4, v.pos, {x=v.pos_shown.x,y=v.pos_shown.y}, 'out-quad',
                function()
                    if not check_ask_rating then return end

                    local global = storage:load_global()
                    local user_data = storage:load_slot()
                    local ask_for_rating_levels = RC.v.ask_for_rating_level or {4, 7, 12}
                    local last_level_asked = global.rating_last_level_asked

                    if last_level_asked and 
                        last_level_asked >= ask_for_rating_levels[#ask_for_rating_levels] 
                    then 
                        log.info("CHECK ASK FOR RATING FALSE. PAST LAST LEVEL")
                        return 
                    end

                    local ask_for_rating_level = ask_for_rating_levels[1]
                    for i = 1, #ask_for_rating_levels, 1 do
                        if not last_level_asked or 
                            last_level_asked < ask_for_rating_levels[i] 
                        then
                            ask_for_rating_level = ask_for_rating_levels[i]
                            break
                        end
                    end
                    log.debug('ask_for_rating_level:%s', ask_for_rating_level)

                    if (RC.v.ask_for_rating and not features.censored_cn and
                        global and not global.rating_accepted and
                        user_data.levels[ask_for_rating_level] and not user_data.levels[ask_for_rating_level+1] and
                        not PP:is_underage() )
                    then
                        global.rating_last_level_asked = ask_for_rating_level
                        storage:save_global(global)

                        -- ask for rating after reaching level 4, 7 and 11
                        if KR_PLATFORM == 'android' then 
                            self:show_ask_for_rating()
                        elseif PS.services.rating then
                            PS.services.rating:request_review()
                        end   
                    else
                        if not RC.v.ask_for_rating then
                            log.info("CHECK ASK FOR RATING FALSE. Disabled in remote config")
                        elseif not global or global.rating_accepted then
                            log.info("CHECK ASK FOR RATING FALSE. No global save, or rating already accepted")
                        elseif not user_data.levels[ask_for_rating_level] or 
                            user_data.levels[ask_for_rating_level+1] 
                        then
                            log.info("CHECK ASK FOR RATING FALSE. Should ask in level " .. ask_for_rating_level)
                            if last_level_asked then 
                                log.info("Already asked in level " .. last_level_asked)
                                return 
                            end
                        else
                            log.info("CHECK ASK FOR RATING FALSE. Underage")
                        end
                    end
                end
            )
        end
    end

    -- gems
    self:update_gems()
    
    -- TODO balloons
    --local user_data = storage:load_slot()
    --local hero_unlock_levels = table.map(screen_map.hero_data, function(k,v) return v.available_at_stage end)
    -- if self.unlock_data.new_level == 2 then
    --     wid('map_balloon_upgrades_view'):show()
    -- elseif ((not self:is_seen('map_balloon_level_up_view')
    --              and GS.default_hero
    --              and user_data.heroes.selected == GS.default_hero
    --              and user_data.heroes.status[GS.default_hero].xp > GS.hero_xp_thresholds[1]
    --         )) then
    --     self:set_seen('map_balloon_level_up_view')
    --     wid('map_balloon_level_up_view'):show()
    -- elseif table.contains(hero_unlock_levels,self.unlock_data.new_level) then
    --     wid('map_balloon_hero_unlocked_view'):show()
    -- end
    -- if DBG_SHOW_BALLOONS then
    --     wid('map_balloon_upgrades_view'):show()
    --     wid('map_balloon_level_up_view'):show()
    --     wid('map_balloon_hero_unlocked_view'):show()
    -- end
    
end

function screen_map:hide_bars()
    for _,n in pairs(bar_names) do
        local v = wid(n)
        ktw:cancel(v)
        ktw:tween(v, 0.3, v.pos, {x=v.pos_hidden.x,y=v.pos_hidden.y}, 'in-quad', function() v.hidden=true end)
    end    
end

function screen_map:show_map_free_gems()
    wid('map_free_gems_view').hidden = false
    wid('map_free_gems_view').scale = V.v(0,0)
    timer:tween(0.5,wid('map_free_gems_view').scale,{x=1,y=1},'out-back') 
end
function screen_map:hide_map_free_gems()
    timer:tween(0.5,wid('map_free_gems_view').scale,{x=0,y=0},'in-back',
                function() wid('map_free_gems_view').hidden=true end)    
end

function screen_map:give_free_gift(items)
    ktw:cancel(self)
    ktw:after(self,0.5,
        function() 
            self:update_gems()
            self:refresh_offers(true)
            self:hide_shop()
            self:show_cards(items)
        end
    )
end

-- popup views
function screen_map:show_difficulty(fn)
    --if IS_TABLET then wid('modal_bg_shaded_view').hidden = false end
    --wid('difficulty_view').hidden = false
    fn = fn or function()
        self:update_badges()

        -- hides bars until lvl 2 pass to prevent opening rooms
        -- local user_data = storage:load_slot()
        -- local last_level_won = 1
        -- for i, v in ipairs(user_data.levels) do
        --     if v['stars'] ~= nil then
        --         last_level_won = i
        --     else
        --         break
        --     end
        -- end
        -- if last_level_won > 1 then
        --     timer:after(1,function() self:show_bars() end)            
        -- end
        timer:after(1,function() self:show_bars() end)           
        wid('map_view'):show_flags()
    end

    wid('difficulty_room_view'):show(fn)
end

function screen_map:show_achievements()
    wid('achievements_room_view'):show()

    wid('group_bottom_right'):ci('alert_achievements').hidden = true
end
function screen_map:hide_achievements()
    wid('achievements_room_view'):hide()
end

function screen_map:show_shop()
    wid('shop_room_view'):show()
    wid('group_bottom'):ci('alert_shop').hidden = true
end
function screen_map:show_shop_gems()
    wid('shop_room_view'):show('gems')
    wid('group_bottom'):ci('alert_shop').hidden = true
end
function screen_map:show_shop_offer()
    wid('shop_room_view'):show('offer')
    wid('group_bottom'):ci('alert_shop').hidden = true
end
function screen_map:show_shop_season()
    wid('shop_room_view'):show('season')
    wid('group_bottom'):ci('alert_shop').hidden = true
end
function screen_map:show_shop_dlc()
    wid('shop_room_view'):show('dlc')
    wid('group_bottom'):ci('alert_shop').hidden = true
end
function screen_map:hide_shop()
    wid('shop_room_view'):hide()
end

function screen_map:show_gems_store(no_title)
    wid('gems_store_view'):show(no_title)
end
function screen_map:hide_gems_store()
    wid('gems_store_view'):hide()
end
function screen_map:show_cards(cards)
    wid('card_rewards_view'):show(nil,cards)
end

function screen_map:show_hero_room(show_hero_name, just_purchased)
    wid('hero_room_view'):show(show_hero_name, just_purchased)

    -- hides alert and notification
    wid('map_hud_notification_new_hero').hidden = true
    wid('group_bottom'):ci('alert_heroes').hidden = true
end
function screen_map:update_hero_room()
    wid('hero_room_view'):update_hero_data()
end
function screen_map:update_tower_room()
    wid('tower_room_view'):update_tower_data()
end
function screen_map:update_item_room()
    wid('item_room_view'):update_item_data()
end

function screen_map:show_product_cards(product_id)

    local p = PS.services.iap:get_product(product_id,true)
    local includes = p.includes_consumables or p.includes
    
    if string.find(product_id,'gems_') then
        includes ={{name = product_id,count =1}}
    else
        if includes then
            local queue = table.clone(includes)
            while #queue > 0 do
                local v = table.remove(queue,1)
                local id = type(v) == 'table' and v.name or v
                local sp = PS.services.iap:get_product(id,true)
                if sp then
                    local sub_includes = sp.includes_consumables or sp.includes
                    if sub_includes then
                        table.append(includes,sub_includes) 
                        table.append(queue,sub_includes)
                    end
                end
            end     
        end
    end

    if string.find(product_id,'dlc_') then
        local premium, exceptions = PS.services.iap:is_premium()
        local premium_full = premium and not exceptions
        
        if IS_DESKTOP then
            table.insert(includes,1,product_id)
        end
        local dlc = product_id
        for _, v in pairs(p.includes) do
            if string.starts(v, 'dlc_') then dlc = v break end
        end
        local dlc_first_level = U.get_dlc_level_range(dlc)[1]
        wid('map_view'):center_map_on_flags(dlc_first_level, dlc_first_level)

        local user_data = storage:load_slot(nil,true)  -- force slot load
        if U.unlock_next_levels_in_ranges({}, user_data.levels, GS, premium_full and {} or {dlc}) then
            storage:save_slot(user_data)
        end

        local global = storage:load_global()
        if not global.unlocked_dlcs then
            global.unlocked_dlcs = {}
        end
        if not table.contains(global.unlocked_dlcs,dlc) then
            table.insert(global.unlocked_dlcs,dlc)
            storage:save_global(global)
        end
    end
    if #includes>0 then
        screen_map:show_cards(includes)
    end
end

function screen_map:process_new_dlc() -- use in desktop to give the dlc
    local user_data = storage:load_slot(nil,true)
    local reached_min_level = user_data.levels[U.get_dlcs_unlock_level(GS) + 1]
    if reached_min_level and PS.services.iap then
        local is_premium,premium_excludes = PS.services.iap:is_premium()
        if is_premium and premium_excludes == nil then
            log.todo('TODO: is this the case for full premium?')
            return false
        end
        local dlcs = PS.services.iap:get_dlcs(true)
        for _, dlc in pairs(dlcs) do
            local dlc_first_level = U.get_dlc_level_range(dlc)[1]
            if not table.contains(user_data.levels,dlc_first_level) then
                local global = storage:load_global()
                if not global.unlocked_dlcs or not table.contains(global.unlocked_dlcs,dlc) then
                    screen_map:show_product_cards(dlc)
                    return true
                else
                    local user_data = storage:load_slot(nil,true)  -- force slot load
                    if U.unlock_next_levels_in_ranges({}, user_data.levels, GS, {dlc}) then
                        storage:save_slot(user_data)
                    end
                    return false
                end
            end
        end
    end
    return false
end

--function screen_map:hero_room_update_active_portraits()
--   self:update_active_portraits_with_prefix('hero_room')
--end

function screen_map:update_active_portraits_with_prefix(prefix)
    local slot = storage:load_slot()
    for i = 1, 2, 1 do
        local portrait_view = wid(prefix .. '_active_portrait_' .. i)
        local hero_name = slot.heroes.team[i]
        local data = screen_map.hero_data[hero_name]
        local thumb_fmt = 'heroroom_portraits_%04i'
        portrait_view:set_image(string.format(thumb_fmt,data.icon_idx)) 
    end

    for i = 1, 2, 1 do
        local portrait_view = wid(prefix .. '_active_portrait_relic_' .. i)
        local relic_name = slot.relics.selected[i]
        local relic = E:get_template(relic_name)
        portrait_view:set_image(string.format('heroroom_upgradeIcons_%04i',relic.relic.rr_icon)) 
    end
end


function screen_map:hide_hero_room()
    wid('hero_room_view'):hide()
end

function screen_map:show_tower_room(show_tower_name, just_purchased)
    wid('tower_room_view'):show(show_tower_name, just_purchased)

    -- hides alert and notification
    wid('map_hud_notification_new_tower').hidden = true
    wid('group_bottom'):ci('alert_towers').hidden = true
end

function screen_map:hide_tower_room()
    wid('tower_room_view'):hide()    
end

function screen_map:check_tower_room()
    local slot = storage:load_slot()
    if #slot.towers.selected ~= 5 then
        return false
    end

    return true
end

function screen_map:show_item_room(show_item_name, just_purchased)
    wid('item_room_view'):show(show_item_name, just_purchased)
end

function screen_map:hide_item_room()
    wid('item_room_view'):hide()    
    local user_data = storage:load_slot()
    wid('group_bottom'):ci('alert_items').hidden = user_data.gems < screen_map.item_data['cluster_bomb'].cost
end

function screen_map:check_item_room()
    local slot = storage:load_slot()
    if #slot.items.selected ~= 5 then
        return false
    end

    return true
end

function screen_map:show_upgrades()
    wid('upgrades_room_view'):show()

    -- hides alert
    wid('group_bottom'):ci('alert_upgrades').hidden = true
end

function screen_map:hide_upgrades()    
    wid('upgrades_room_view'):hide()    
end

function screen_map:show_level(level_idx,stars,diff_campaign,diff_heroic,diff_iron)
    wid('level_select_view'):show(level_idx,stars,diff_campaign,diff_heroic,diff_iron)
end
function screen_map:hide_level()
    wid('level_select_view'):hide()
end

function screen_map:hide_popup_options()
    wid('popup_options'):hide('map')
end

-- ------------------------------------------------------------
-- TODO: one time offer (OTO)
--
--function screen_map:show_one_time_offer(offer)
--    log.debug('offer to show:%s', getfulldump(offer))
--
--    -- hide all other layouts
--    for _,c in pairs(wid('one_time_offer_layouts').children) do
--        c.hidden = true
--    end    
--    
--    -- find layout used
--    local layout_suffix = (#offer.includes > 3) and 'all' or tostring(#offer.includes)
--    local vid = 'one_time_offer_layout_' .. layout_suffix
--    local v = wid(vid)
--    if not v then
--        log.error('Offer with layout suffix %s not found', layout_suffix)
--        return
--    end
--
--    -- layout setup
--    local function cv(id) return v:get_child_by_id(id) end
--    self.offer_shown = offer  -- keep a reference for the buy button handler
--    for i,hero_name in ipairs(offer.includes) do
--        local hd = screen_map.hero_data[hero_name]
--        local vvid = 'portrait_'..i
--        if cv(vvid) then           
--            cv(vvid):set_image(string.format('heroroom_bigPortraits_%04i',hd.icon_idx))
--        end
--    end 
--    cv('new_price').text = offer.new_price_str or ''
--    if cv('old_price') then cv('old_price').text = offer.old_price_str or '' end
--
--    -- show it
--    v.hidden = false
--    wid('modal_bg_transparent_view').hidden = false    
--    wid('one_time_offer_view').hidden = false
--    signal.emit(SGN_MARKETING_OFFER_SHOWN,offer.id)
--
--    if DBG_OTO_EDITOR then
--        dbe:inject_editor(v,self)
--    end
--end
--function screen_map:hide_one_time_offer()
--    self.offer_shown = nil
--    wid('modal_bg_transparent_view').hidden = true
--    wid('one_time_offer_view').hidden = true
--end
--
--function screen_map:show_offer_icon(offer,exp_time)
--    local slots = wid('map_offer_icons_view').pos_slots
--    local p = wid('map_offer_icon_persistent')
--    local r = wid('map_offer_icon_regular')
--    local c = wid('map_offer_icon_countdown')
--    if offer.persistent then
--        p.hidden = false
--        p.offer = offer
--    else
--        r.hidden = false
--        r.offer = offer
--        c.hidden = false
--        c.countdown_to = exp_time
--        c:update(0)
--    end
--    -- positions
--    if r.hidden then
--        p.pos = V.vclone(slots[1])
--    else
--        r.pos = V.vclone(slots[1])
--        c.pos = V.vclone(slots[1])
--        p.pos = V.vclone(slots[2])
--    end
--    -- show/hide bar
--    local tb = wid('top_left_bar_view')
--    if tb.hidden then
--        local safe_frame = SU.get_safe_frame(self.w,self.h,self.ref_w,self.ref_h)
--        if IS_PHONE then 
--            tb.pos.x,tb.pos.y = safe_frame.l,-tb.size.y
--            tb.tweener = timer:tween(0.5,tb.pos,{y=safe_frame.t},'out-back',
--                                     function()
--                                         tb.showing=nil
--                                         tb.tweener=nil
--                                     end
--            )
--        elseif IS_TABLET then
--            tb.pos.x,tb.pos.y = -tb.size.x, safe_frame.t
--            tb.tweener = timer:tween(0.5,tb.pos,{x=safe_frame.l},'out-back',
--                                     function()
--                                         tb.showing=nil
--                                         tb.tweener=nil
--                                     end
--            )                
--        end
--        tb.hidden = false
--        tb.showing = true
--    end
--end
--function screen_map:hide_offer_icon(persistent)
--    local slots = wid('map_offer_icons_view').pos_slots
--    local p = wid('map_offer_icon_persistent')
--    local r = wid('map_offer_icon_regular')
--    local c = wid('map_offer_icon_countdown')
--    if persistent then
--        p.hidden = true
--    else
--        r.hidden = true
--        c.hidden = true
--    end    
--    -- positions
--    if r.hidden then
--        p.pos = V.vclone(slots[1])
--    else
--        r.pos = V.vclone(slots[1])
--        c.pos = V.vclone(slots[1])
--        p.pos = V.vclone(slots[2])
--    end
--    -- show/hide bar
--    local tb = wid('top_left_bar_view')
--    if p.hidden and r.hidden and not tb.hidden then
--        if tb.showing then
--            tb.showing = nil
--            timer:cancel(tb.tweener)
--        end        
--        tb.tweener=nil
--        tb.hidden = true
--    end
--end
--function screen_map:hide_offer_icons()
--    local ids = {'map_offer_icon_regular','map_offer_icon_persistent','map_offer_icon_countdown'}
--    for _,id in pairs(ids) do
--        local v = wid(id)
--        if v then v.hidden = true end
--    end
--end
--
--function screen_map:refresh_offer_icons()
--    -- refresh offer icons
--    -- IMPORTANT: product prices must be refreshed so the prices exist
--    
--    -- persistent offer (always show)
--    local persistent_offer = marketing:get_one_time_offer(true)
--    log.debug('persistent_offer: %s', persistent_offer)
--    if persistent_offer then
--        self:show_offer_icon(persistent_offer)
--    end
--    -- other offers
--    local active_offer,exp_time = marketing:get_active_offer()
--    log.debug('active_offer: %s', active_offer)
--    if active_offer then
--        -- show active offer
--        self:show_offer_icon(active_offer,exp_time)
--    else
--        -- show new offer
--        local offer = marketing:get_one_time_offer(false)
--        if offer then
--            -- show special offer
--            local exp_time_2 = marketing:set_active_offer(offer)
--            self:show_offer_icon(offer,exp_time_2)
--            self:show_one_time_offer(offer)
--        end
--    end    
--end
--
--function screen_map:compose_view_icons(view)
--    if not view.pos_slots then
--        log.error('view has not pos_slots list')
--        return
--    end
--    local start_j=1
--    for i=1,#view.pos_slots do
--        local s = view.pos_slots[i]
--        for j=start_j,#view.children do
--            local c = view.children[j]
--            if not c.hidden then
--                c.pos = V.vclone(s)
--                start_j = j+1
--                goto next
--            end
--        end
--        ::next::
--    end
--end

function screen_map:hide_top_left_bar()
    local tb = wid('top_left_bar_view')
    if not tb.hidden then
        if tb.showing then
            tb.showing = nil
            timer:cancel(tb.tweener)
        end        
        tb.tweener = nil
        tb.hidden = true
    end    
end

function screen_map:show_top_left_bar()
    local tb = wid('top_left_bar_view')
    if tb.hidden then    
        local safe_frame = SU.get_safe_frame(self.w,self.h,self.ref_w,self.ref_h)
        tb.pos.x,tb.pos.y = safe_frame.l,-tb.size.y
        tb.tweener = timer:tween(0.5,tb.pos,{y=safe_frame.t},'out-back',
                                 function()
                                     tb.showing=nil
                                     tb.tweener=nil
                                 end
        )
        tb.hidden = false
        tb.showing = true
    end
end

function screen_map:show_iap_progress()
    wid('processing_view'):show()
end
function screen_map:hide_iap_progress()
    wid('processing_view'):hide()
end

function screen_map:show_error(msg)
    wid('error_view'):show(msg)
end
function screen_map:hide_error()
    wid('error_view'):hide()
end

function screen_map:show_message(kind,arg)
    wid('message_view'):show(kind,arg)    
end
function screen_map:hide_message()
    wid('message_view'):hide()
end

function screen_map:show_ask_for_rating()
    wid('message_view'):show('ask_for_rating')
end
function screen_map:hide_ask_for_rating()
    wid('message_view')
end

-- additional functions

function screen_map:quit_to_slots()
    local user_data = storage:load_slot()
    storage:save_slot(user_data,nil,true)  -- cloud sync    
    self.done_callback({next_item_name='slots'})
end

function screen_map:start_level(level_idx,level_mode)
    local user_data = storage:load_slot()
    storage:save_slot(user_data,nil,true)  -- cloud sync    
    self.done_callback(
        {
            next_item_name='game',
            level_idx=level_idx,
            level_mode=level_mode,
            level_difficulty=user_data.difficulty
        }
    )
end

function screen_map:is_stage_completed(stage,user_data)
    return user_data.levels[stage] and #user_data.levels[stage]>0
end

function screen_map:is_content_stage_unlocked(content_data,user_data)
    if PS.services.iap then
        local premium,exceptions = PS.services.iap:is_premium()
        if exceptions and content_data.iap then
            return true
        end
    end
    return not content_data.available_at_stage or 
        content_data.available_at_stage <= 1 or
        screen_map:is_stage_completed(content_data.available_at_stage-1,user_data)
end

function screen_map:is_seen(key)
    local user_data = storage:load_slot()
    return user_data.seen[key]
end
function screen_map:set_seen(key)
    local user_data = storage:load_slot()
    user_data.seen[key] = true
    storage:save_slot(user_data)
end
function screen_map:refresh_offers(force)
    wid('shop_room_view'):refresh_offers(force)
end

function screen_map:update_gems(amount)    
    local user_data = storage:load_slot()    
    amount = amount or user_data.gems

    -- gems labels
    wid('label_map_gems').text = amount
    wid('shop_room_view'):update_gems(amount)

    -- gem badge
    --TODOlocal g = wid('gems_to_spend_view')
    --TODOg.hidden = (user_data.gems < iap_data.cheapest_item_cost)
end

function screen_map:update_badges()
    local user_data = storage:load_slot()
    local has_all_upgrades = true
    for k,v in pairs(user_data.upgrades) do
        has_all_upgrades = has_all_upgrades and (v == 5)
    end

    -- -- hero badge and portrait
    -- local h = wid('hero_points_to_spend_view')
    -- -- local hp = wid('map_hero_button_portrait')
    -- local hero_name = user_data.heroes.selected
    -- local hero_stats = hero_name and HeroRoomView:get_hero_stats(hero_name) or nil
    -- if hero_name and hero_stats then 
    --     h.text = hero_stats.remaining_points
    --     h.hidden = hero_stats.remaining_points < 1
    --     -- hp:set_image(string.format('mapButtons_portrait_hero_%04i',screen_map.hero_data[hero_name].icon_idx))
    --     -- hp.hidden = false
    -- else
    --     h.hidden = true
    --     --hp.hidden = true
    -- end 

    -- hero room button portraits
    -- TEMPORARY! SHOW HALVES OF EACH PORTRAIT
    -- local im1 = wid('image_map_button_portrait_1')
    -- local im2 = wid('image_map_button_portrait_2')
    -- if user_data.heroes.team then        
    --     local img_fmt = 'hero_room_portraits_small_button_%s_0001'
    --     im1:set_image(string.format(img_fmt, user_data.heroes.team[1]))
    --     im2:set_image(string.format(img_fmt, user_data.heroes.team[2]))
    --
    --     im1.hidden = false
    --     im1.orig_size_x = im1.size.x
    --     im1.clip = true
    --     im1.size.x = im1.orig_size_x/2
    --
    --     im2.hidden = false
    --     im2.clip = true
    --     im2.size.x = im1.size.x
    --     im2.pos.x = im1.pos.x + im1.orig_size_x/2
    --     im2.image_offset = V.v(-im1.orig_size_x/2, 0)
    --
    -- else
    --     im1.hidden = true
    --     im2.hidden = true
    -- end
    
    -- TODO upgrades badge
    -- local u = wid('upgrade_points_to_spend_view')
    -- local remaining_stars = screen_map.total_stars - screen_map.spent_stars
    -- u.text = remaining_stars
    -- u.hidden = has_all_upgrades or (remaining_stars < 1)
    -- 
    -- 
    -- wid('map_active_portrait_view').hidden = true
    -- self:update_active_portraits_with_prefix('map')
end

function screen_map:update_tower_data()
    -- update tower data according to iap/premium/free state
    if features.censored_cn then
        screen_map.tower_data = map_data.tower_data_iap
        screen_map.tower_order = map_data.tower_order_censored_cn
    elseif not PS.services.iap or PS.services.iap:is_premium() then
        screen_map.tower_data =  table.deepclone(map_data.tower_data_free)  -- modified below for premium
        screen_map.tower_order = map_data.tower_order_free
    else
        screen_map.tower_data = map_data.tower_data_iap
        screen_map.tower_order = map_data.tower_order_iap
    end

    if PS.services.iap then
        -- if PS.services.iap:is_premium() then
        --     -- unlock heroes purchased before going into premium
        --     local global = storage:load_global()
        --     if global.purchased_heroes then 
        --         for _,n in pairs (global.purchased_heroes) do
        --             log.debug('unlocking hero %s owned before buying premium pass', hn)
        --             local d = screen_map.hero_data
        --             d[n].available_at_stage = 2
        --         end
        --     end
        --     
        -- else
        --     -- deselect hero that is not purchased
        --     local user_data = storage:load_slot()
        --     local hn = user_data.heroes.selected
        --     if not hn or not screen_map.hero_data[hn] or not screen_map.hero_data[hn].iap then goto done end
        --     local p = PS.services.iap:get_product(hn)
        --     if not p.owned then
        --         log.debug('deselecting hero not owned %s', hn)
        --         user_data.heroes.selected = GS.default_hero
        --         storage:save_slot(user_data)
        --     end
        -- end
        --::done::
    end
end

function screen_map:update_item_data()
    screen_map.item_data = table.deepclone(iap_data.shop_data)
    if features.censored_cn then
        screen_map.item_order = map_data.item_order_censored_cn
    else
        screen_map.item_order = map_data.item_order
    end
end

------------------------------------------------------------
-- ISM commands and queries
function screen_map.q_is_picking_team()
    return wid('hero_room_view').picking_team_slot
end

function screen_map.q_is_picking_tower()
    return wid('tower_room_view').picking_team_slot
end

function screen_map.q_is_picking_item()
    return wid('item_room_view').picking_team_slot
end

function screen_map.q_is_flag_focused(ctx)
    for _,v in pairs(screen_map.window:ci('group_map_flags').children) do
        if v:isInstanceOf(StageFlag5) then
            if v:is_focused() then
                return true
            end
        end
    end
end

function screen_map.q_is_modal()
    return (screen_map.modal_view ~= nil)
end

--

function screen_map.c_stop_picking_team()
    return wid('hero_room_view'):pick_team_slot_stop()
end

function screen_map.c_stop_picking_tower()
    return wid('tower_room_view'):pick_tower_slot_stop()
end

function screen_map.c_stop_picking_item()
    return wid('item_room_view'):pick_item_slot_stop()
end

function screen_map.c_focus_next_flag(ctx,dir)
    screen_map.window:ci('map_view'):focus_next_flag(dir)
end
function screen_map.c_return_main_menu()
    screen_map:quit_to_slots()
end

function screen_map.c_hide_modal()
    if screen_map.modal_view then
        screen_map.modal_view:hide()
    end
end

------------------------------------------------------------
return screen_map

