--
-- Requires
--  - luafilesystem  (brew install luarocks; luarocks install luafilesystem)
--  - rsync: TODO multiplatform
--  - android: bundletool jar (download from android github)
--  - android: jarsigner (brew install java11)
--  - mac: plutil, PlistBuddy
--
-- IMPORTANT: create dist-conf.lua based on dist-conf.lua.example with the necessary paths
--
-- -- Checksums / oggerhead_id -------------------
-- It's important to keep the checksums for the release versions, so we know when a version was modified/cracked.
-- A checksum file (.md5 extension) is generated for the Android builds, but none for the iOS.
-- Spreadsheet to keep checksums: https://docs.google.com/spreadsheets/d/14M8IcI6wXfvNqnexiQTL22FQcJtg_P8pcI9VObPD56U/edit?usp=sharing
-- 
--
-- -- Mac ----------------------------------------
-- 
-- -- Coddesign in Mac:
-- - to find certificates: security find-certificate -a -Z | less
-- - the SHA-1 of the certificate is used as identifier
-- - if using from a remote ssh, remember first to "security unlock-keychain" or it will give error -1=ffffff
-- - IMPORTANT: codesign error -1FFFFF is due to codesign not having access to the key.
-- - IMPORTANT: mac development certificates do not suport iCloud in
--   the entitlements, it will crash and show an error in Console
--   kernel mac_vnode_check_signature fail with "Code has restricted
--   entitlements, but the validation of its code signature failed"
-- - IMPORTANT: two certificates are needed:
--   - Mac App Distribution: the sha-1 should be placed in the app key in certs (in keychain: 3rd Party Mac Developer Application: Ironhide S.A.)
--   - Mac Installer Distribution: sha-1 should be placed in the inst key in certs (in keyhain: 3rd Party Mac Developer Installer: Ironhide S.A.)
-- - IMPORTANT: two provisioning profiles are needed
--   - Development: create using the app identifier, download and install by doubleclick
--   - Production: create using the app identifer, download and install with mac-install-profile.sh (see README.org in this dir for more)
--    
-- -- Mac + dynamic libraries + sandboxing
-- - For libkrequest.dylib to be found in sandboxed apps, it is processed via install_name_tool changing the name to @executable_path/../Frameworks/libkrequest.dylib
-- - For the libraries to work while debugging, libkrequest.dylib must be compiled with "Dynamic Library Install Name" = '' 
-- - Then the other libs that link to it will use the proper id
-- - To check the id use otool -L
-- - To change the id use install_name_tool -change xxxx/libkrequest.dylib yyyy/libkrequest.dylib libkgamekit.dylib
-- 
-- -- Making xcarchive
-- This process creates a .xcarchive so we can use xcodebuild later to export it in a format that can be uploaded to the AppStore.
-- Requires:
--   - .app with the proper icon set
--   - app_Info.plist used as template (TODO: move icon configuration here!)
--   - xarchive_Info.plist used as template to include with the xcarchive
--   - export.plist used as parameters to xcode
--   - love.entitlements used to specify the app entitlements (sandobox for example)
--
-- To verify the entitlements are correct use:
--   /usr/bin/codesign -d --ent :- Kingdom\ Rush\ Origins.xcarchive/Products/Applications/Kingdom\ Rush\ Origins.app 
--
-- Uploading options
-- - Archive window in XCode and copy the .xcarchive into the directory and upload from there.
--   - support xcarchive
-- - command line: altool ( /Applications/Xcode.app/Contents/Applications/Application\ Loader.app/Contents/Frameworks/ITunesSoftwareService.framework/Versions/A/Support/altool )
--   - altool --upload-app -f file.pkg -t osx -u <EMAIL> -p @env:PWKK
--     - add an env var called PWKK with the pass
--
-- luacheck: globals SRC_PATH DIST_OPEN DIST_ARCHIVE LUAJIT_21_GC32 LUAJIT_21_GC64 LUAJIT_21_GC64_PATH LUAJIT_20 LUAJIT_20_PATH NX_PROJECT_ROOT LOVE_XBOX_ASSETS KR_GDK_ROOT MSSTORE_MAKEPKG_WIN_DIR IOS_PROJECT ANDROID_PROJECT GRADLE_JAVA_HOME BUNDLETOOL JARSIGNER ZIPALIGN APKSIGNER KEYSTORES DESKTOP_BINARIES
--
--

do --changed to receive error to allow more complex configurations of dist-conf for build server
    local ok, result = pcall(require, 'dist-conf')
    if not ok then
        print(tostring(result))
        print('ERROR: Could not find dist-conf.lua file.')
        print('Use dist-conf.lua.example as a reference to create it.')
        os.exit(-1)
    end
end

------------------------------------------------------------

local SETS = {

    -- android
    kr1_android_google = { game='kr1', target='phone', platform='android', task='aab', bundle='com.ironhidegames.android.kingdomrush'                 , keystore=KEYSTORES.kr1,  keystore_alias='mykey', gradle_subdir='kr1_google', modify_assets={{replace='images/iphonehd', with='images/iphonehd_etc1'}}},    
    kr2_android_google = { game='kr2', target='phone', platform='android', task='aab', bundle='com.ironhidegames.android.kingdomrushfrontiers'        , keystore=KEYSTORES.kr23, keystore_alias='mykey', gradle_subdir='kr2_google', modify_assets={{replace='images/iphonehd', with='images/iphonehd_etc1'}}},
    kr3_android_google = { game='kr3', target='phone', platform='android', task='aab', bundle='com.ironhidegames.android.kingdomrushorigins'          , keystore=KEYSTORES.kr23, keystore_alias='mykey', gradle_subdir='kr3_google', modify_assets={{replace='images/iphonehd', with='images/iphonehd_etc1'}}},
    --kr5_android_google = { game='kr5', target='phone', platform='android', task='aab', bundle='com.ironhidegames.android.kingdomrush.alliance'     , version_code_suffix='00', keystore=KEYSTORES.kr5,  keystore_alias='mykey', gradle_subdir='kr5_google',modify_assets={{delete='images/ipadhd_etc2'}}},
    
    kr1_android_humble = { game='kr1', target='phone', platform='android', task='apk', obb=false, bundle='com.ironhidegames.android.kingdomrush.humble'          , gradle_subdir='kr1_humble', project_subdir='kr_apps/kr1_humble/src/main/assets', keystore=KEYSTORES.kr1,  keystore_alias='mykey', modify_assets={{replace='images/iphonehd', with='images/iphonehd_etc1'}}},
    kr2_android_humble = { game='kr2', target='phone', platform='android', task='apk', obb=false, bundle='com.ironhidegames.android.kingdomrushfrontiers.humble' , gradle_subdir='kr2_humble', project_subdir='kr_apps/kr2_humble/src/main/assets', keystore=KEYSTORES.kr23, keystore_alias='mykey', modify_assets={{replace='images/iphonehd', with='images/iphonehd_etc1'}}},
    kr3_android_humble = { game='kr3', target='phone', platform='android', task='apk', obb=false, bundle='com.ironhidegames.android.kingdomrushorigins.humble'   , gradle_subdir='kr3_humble', project_subdir='kr_apps/kr3_humble/src/main/assets', keystore=KEYSTORES.kr23, keystore_alias='mykey', modify_assets={{replace='images/iphonehd', with='images/iphonehd_etc1'}}},
    --kr1_android_yodo1sdk = { game='kr1', target='phone', platform='android', task='apk', obb=false, bundle='com.ironhidegames.android.kingdomrush.yodo1sdk',       project_subdir='kr_apps/kr1_yodo1sdk/src/main/assets', keystore=KEYSTORES.kr1,  keystore_alias='mykey', image_override='yodo1sdk'},
    kr1_android_yodo1sdk = { game='kr1', target='phone', platform='android', task='apk', obb=false, bundle='com.ironhidegames.android.kingdomrush.yodo1sdk',       gradle_subdir='kr1_yodo1sdk', project_subdir='kr_apps/kr1_yodo1sdk/src/main/assets', keystore=KEYSTORES.yodo1,  keystore_alias='yodo1', image_override='yodo1sdk', modify_assets={{replace='images/iphonehd', with='images/iphonehd_etc1'}}},
    --kr1_android_bemobi = { game='kr1', target='phone', platform='android', task='aab', bundle='com.ironhidegames.android.kingdomrush.humble'          , keystore=KEYSTORES.kr1,  keystore_alias='mykey'},
    --kr2_android_bemobi = { game='kr2', target='phone', platform='android', task='aab', bundle='com.ironhidegames.android.kingdomrushfrontiers.humble' , keystore=KEYSTORES.kr23, keystore_alias='mykey'},
    --kr3_android_bemobi = { game='kr3', target='phone', platform='android', task='aab', bundle='com.ironhidegames.android.kingdomrushorigins.humble'   , keystore=KEYSTORES.kr23, keystore_alias='mykey'},
    --kr1_android_hatch  = { game='kr1', target='phone', platform='android', task='aab', bundle='com.ironhidegames.android.kingdomrush.hatch'           , keystore=KEYSTORES.kr1,  keystore_alias='mykey'},
    --kr2_android_hatch  = { game='kr2', target='phone', platform='android', task='aab', bundle='com.ironhidegames.android.kingdomrushfrontiers.hatch'  , keystore=KEYSTORES.kr23, keystore_alias='mykey'},
    --kr3_android_hatch  = { game='kr3', target='phone', platform='android', task='aab', bundle='com.ironhidegames.android.kingdomrushorigins.hatch'    , keystore=KEYSTORES.kr23, keystore_alias='mykey'},
    --kr1_android_huawei = { game='kr1', target='phone', platform='android', task='aab', bundle='com.ironhidegames.android.kingdomrush.huawei'          , keystore=KEYSTORES.kr1,  keystore_alias='mykey'},
    --kr2_android_huawei = { game='kr2', target='phone', platform='android', task='aab', bundle='com.ironhidegames.android.kingdomrushfrontiers.huawei' , keystore=KEYSTORES.kr23, keystore_alias='mykey'},
    --kr3_android_huawei = { game='kr3', target='phone', platform='android', task='aab', bundle='com.ironhidegames.android.kingdomrushorigins.huawei'   , keystore=KEYSTORES.kr23, keystore_alias='mykey'},
    --kr2_android_kalio  = { game='kr2', target='phone', platform='android', task='aab', bundle='net.kalio.test.android.krf'                            , keystore=KEYSTORES.kaliotest, keystore_alias='mykey'}, -- not sure about the alias

    -- ios
    kr1_iphone_yodo1mas = { game='kr1', target='phone', platform='ios', task='ios', bundle='com.ironhidegames.kingdomrush.fullads.cn',          project_subdir='kr1-iphone-fullads-cn', modify_assets={{replace='images/iphonehd', with='images/iphonehd_etc1'}} },
    kr2_iphone_yodo1mas = { game='kr2', target='phone', platform='ios', task='ios', bundle='com.ironhidegames.kingdomrushfrontiers.fullads.cn', project_subdir='kr2-iphone-fullads-cn', modify_assets={{replace='images/iphonehd', with='images/iphonehd_etc1'}} },    
    kr3_iphone_yodo1mas = { game='kr3', target='phone', platform='ios', task='ios', bundle='com.ironhidegames.kingdomrushorigins.fullads.cn',   project_subdir='kr3-iphone-fullads-cn', modify_assets={{replace='images/iphonehd', with='images/iphonehd_etc1'}} },

    kr1_iphone = {game='kr1', target='phone',  platform='ios', task='ios', bundle='com.armorgames.kingdomrushiphone',       project_subdir='kr1-iphone-appstore', modify_assets={{replace='images/iphonehd', with='images/iphonehd_etc1'}}},
    kr2_iphone = {game='kr2', target='phone',  platform='ios', task='ios', bundle='com.ironhidegames.frontiers',            project_subdir='kr2-iphone-appstore', modify_assets={{replace='images/iphonehd', with='images/iphonehd_etc1'}}},
    kr3_iphone = {game='kr3', target='phone',  platform='ios', task='ios', bundle='com.ironhidegames.kingdomrush.elves',    project_subdir='kr3-iphone-appstore', modify_assets={{replace='images/iphonehd', with='images/iphonehd_etc1'}}},
    --kr5_iphone = {game='kr5', target='phone',  platform='ios', task='ios', bundle='com.ironhidegames.kingdomrush.alliance',    project_subdir='kr5-iphone'},
    kr1_ipad   = {game='kr1', target='tablet', platform='ios', task='ios', bundle='com.armorgames.kingdomrush',             project_subdir='kr1-ipad-appstore',   modify_assets={{delete='images/ipadhd_etc1'},{replace='images/ipadhd', with='images/ipadhd_etc2'}}},
    kr2_ipad   = {game='kr2', target='tablet', platform='ios', task='ios', bundle='com.ironhidegames.frontiers-hd',         project_subdir='kr2-ipad-appstore',   modify_assets={{delete='images/ipadhd_etc1'},{replace='images/ipadhd', with='images/ipadhd_etc2'}}},
    kr3_ipad   = {game='kr3', target='tablet', platform='ios', task='ios', bundle='com.ironhidegames.kingdomrush.elves-hd', project_subdir='kr3-ipad-appstore',   modify_assets={{delete='images/ipadhd_etc1'},{replace='images/ipadhd', with='images/ipadhd_etc2'}}},    

    kr1_iphone_kalio_test = { game='kr1', target='phone', platform='ios', task='ios', bundle='net.kalio.test.ios.kr.dev', project_subdir='kr1-iphone', modify_assets={{replace='images/iphonehd', with='images/iphonehd_etc1'}} },

    -- universal (ios/ipad/mac)
    kr2_universal_arcade = { game='kr2', target='dynamic', targets={'phone','tablet'}, platform='ios', task='ios', bundle='com.ironhidegames.frontiers.universal.premium', project_subdir='kr2-universal-premium',
                             modify_assets={
                                 {replace='images/iphonehd', with='images/iphonehd_etc1'},
                                 {replace='images/ipadhd', with='images/ipadhd_etc2'},
                                 {delete='images/ipadhd_etc1'}
                             },
                               file_cmds={
                                   {delete='_assets/all-tablet/fonts'},
                                   {delete='_assets/kr2-tablet/fonts'},
                                   {delete='_assets/kr2-tablet/sounds'},
                               },
                           },
    kr2_desktop_arcade   = { game='kr2', target='desktop', platform='mac-appstore', task='mac_prebuilt', title='Kingdom Rush Frontiers+', bundle='com.ironhidegames.frontiers.universal.premium',
                                  certs={app="BDA078B30FEAE352F6E0BA40F91F5A78EB0955E5",    -- app : 17 Dec 2021 - 3rd Party Mac Developer Application: IRONHIDE IRELAND LIMITED (F83U53V592)
                                         inst="24FCDCA3457B55BD991BD2F145E057613F0B7A2A"},  -- inst:  5 Feb 2022 - 3rd Party Mac Developer Installer: IRONHIDE IRELAND LIMITED (F83U53V592)
                                  profile="e01b913f-22d4-420d-88a2-d122269b6208",           -- profile with Arcade Operations
                                  sign_id="3rd Party Mac Developer Application: IRONHIDE IRELAND LIMITED (F83U53V592)",
                                  sign_method='app-store',
                                  icloud='iCloud.com.ironhidegames.frontiers.universal.premium',
                                  kvstore='F83U53V592.com.ironhidegames.frontiers.universal.premium',
                                  gamecenter=true,
                                  --patch_libs_install={{'libkrequest.dylib', '@executable_path/../Frameworks/libkrequest.dylib'}}, -- Not needed after setting LD_DYLIB_INSTALL_NAME = @rpath/$(EXECUTABLE_PATH)
                                  min_version='11.0',
                                  icon='kr2_aa.icns',
                                  love_icon='icon256_aa.png',
                                  arcade=true
    },
    kr2_desktop_arcade_dev = { game='kr2', target='desktop', platform='mac-dev', task='mac_prebuilt', title='Kingdom Rush Frontiers+', bundle='com.ironhidegames.frontiers.universal.premium',
                                      certs={app="FA6B618FF82173553C18BB13D2696CE3B765F4BE"},   -- Apple Development: Ciro Mondueri (7YB32FG43Q) (Ironhide Ireland Limited)
                                      profile="62f5ac41-7edc-4cbb-8330-af3b6d3249fb",           -- use to get it: ./get-profile-uuid.sh profiles/mac/KRFUniversalPremiumMacDev.provisionprofile
                                      sign_method='development',
                                      gamecenter=true,
                                      --patch_libs_install={{'libkrequest.dylib', '@executable_path/../Frameworks/libkrequest.dylib'}}, -- Not needed after setting LD_DYLIB_INSTALL_NAME = @rpath/$(EXECUTABLE_PATH)
                                      icloud='iCloud.com.ironhidegames.frontiers.universal.premium',
                                      kvstore='F83U53V592.com.ironhidegames.frontiers.universal.premium',
                                      icon='kr2_aa.icns',
                                      love_icon='icon256_aa.png',
    },
    kr2_desktop_arcade_devid = { game='kr2', target='desktop', platform='mac',   task='mac_prebuilt', title='Kingdom Rush Frontiers+', bundle='com.ironhidegames.frontiers.universal.premium',
                                        certs={app="0DFF3B86D2BF723DAD1CE0C0CFA1B8D75AA5F3AE"},
    },

    kr5_universal_arcade = { game='kr5', target='dynamic', targets={'phone','tablet'}, platform='ios', task='ios', bundle='com.ironhidegames.kingdomrush.alliance.universal.premium', project_subdir='kr5-universal-premium',
                             modify_assets={
                                 {replace='images/ipadhd', with='images/ipadhd_etc2'},
                                 {delete='images/iphonehd'},
                                 {delete='images/ipadhd_etc1'},
                             },
                             file_cmds={
                                 {delete='_assets/all-phone/fonts'},
                                 {delete='_assets/all-tablet/fonts'},
                                 {delete='_assets/kr5-tablet/images'},
                                 {delete='_assets/kr5-tablet/fonts'},
                                 {delete='_assets/kr5-tablet/sounds'},
                             },
                           },
    kr5_desktop_arcade   = { game='kr5', target='desktop', platform='mac-appstore', task='mac_prebuilt', title='Kingdom Rush Alliance+', bundle='com.ironhidegames.kingdomrush.alliance.universal.premium', modify_assets={{delete='images/ipadhd'}}, file_cmds={{delete='_assets/all-desktop/fonts'}},
                             force_luajit=LUAJIT_21_GC64,
                             -- guille certs={app="062360AA1B76BB3CAE46F34AF8F4E687CFA61D2F",    -- app : 17 Dec 2021 - 3rd Party Mac Developer Application: IRONHIDE IRELAND LIMITED (F83U53V592)  -- TODO GUILLE
                             -- guille        inst="69802587255BA1281600751C1D47869FEC1F35ED"},  -- inst:  5 Feb 2022 - 3rd Party Mac Developer Installer: IRONHIDE IRELAND LIMITED (F83U53V592)    -- TODO GUILLE
                             certs={ app="59C69D94D047A0FB2B1DD723959BF8E087CFFD11",    -- app : 17 Dec 2021 - 3rd Party Mac Developer Application: IRONHIDE IRELAND LIMITED (F83U53V592) / Mac App Distribution
                                   inst="A69DEDC0561A3E4AB88D09E840E3B05E39F1D8F3"},   -- inst:  5 Feb 2022 - 3rd Party Mac Developer Installer: IRONHIDE IRELAND LIMITED (F83U53V592)   / Mac Installer Distribution
                             profile="488b8da8-c826-4ac2-9534-1a1d3f4593b6",           -- profile with Arcade Operations : KRAllianceUniversalPremiumMac
                             sign_id="3rd Party Mac Developer Application: IRONHIDE IRELAND LIMITED (F83U53V592)",
                             sign_method='app-store',
                             icloud='iCloud.com.ironhidegames.kingdomrush.alliance.universal.premium',
                             kvstore='F83U53V592.com.ironhidegames.kingdomrush.alliance.universal.premium',
                             gamecenter=true,
                             min_version='11.0',
                             icon='kr5_mac_aa.icns',
                             love_icon='icon256_aa.png', 
                             arcade=true
    },

    -- dynamic targets: solved at game launch (phone/tablet)
    kr5_dyn_ios            = { game='kr5', target='dynamic', targets={'phone','tablet'}, platform='ios', task='ios', bundle='com.ironhidegames.kingdomrush.alliance', project_subdir='kr5-universal',
                               modify_assets={
                                   {replace='images/ipadhd', with='images/ipadhd_etc2'},
                                   {delete='images/iphonehd'},
                                   {delete='images/ipadhd_etc1'},
                               },
                               file_cmds={
                                   {delete='_assets/all-phone/fonts'},
                                   {delete='_assets/all-tablet/fonts'},
                                   {delete='_assets/kr5-tablet/images'},
                                   {delete='_assets/kr5-tablet/fonts'},
                                   {delete='_assets/kr5-tablet/sounds'},
                               },
                               --checksum_files={'game.love'},
                             },
    kr5_dyn_android_google = { game='kr5', target='dynamic', targets={'phone','tablet'}, platform='android', task='aab', bundle='com.ironhidegames.android.kingdomrush.alliance', version_code_suffix='00',
                               keystore=KEYSTORES.kr5,  keystore_alias='mykey', gradle_subdir='kr5_google',
                               --checksum_files={'asset-slices/kr5_gc64-master.apk','asset-slices/kr5_gc32-master.apk'}, -- order is important, must be the same as features.lua entry
                               modify_assets={
                                   {replace='images/ipadhd', with='images/ipadhd_etc2'},
                                   {delete='../kr5-tablet/images'},
                                   {delete='../kr5-tablet/fonts'},  -- asset_all_fallback set to phone
                                   {delete='../kr5-tablet/sounds'}, -- asset_game_fallback set to phone
                                   {delete='../all-tablet/fonts'},  -- asset_all_fallback set to phone
                                   {delete='../all-phone/fonts'},   -- asset_all_fallback set to phone
                                   {lz4='images/ipadhd'},
                               },
                             },
    kr5_dyn_ios_china      = { game='kr5', target='dynamic', targets={'phone','tablet'}, platform='ios', task='ios', bundle='com.ironhidegames.kingdomrush.alliance.cn', project_subdir='kr5-universal-fullads-cn',
                            image_override = 'censored_cn',
                            modify_assets={
                                 {replace='images/ipadhd', with='images/ipadhd_etc2'},
                                 {delete='images/iphonehd'},
                                 {delete='images/ipadhd_etc1'},
                             },
                             file_cmds={
                                 {delete='_assets/all-phone/fonts'},
                                 {delete='_assets/all-tablet/fonts'},
                                 {delete='_assets/kr5-tablet/images'},
                                 {delete='_assets/kr5-tablet/fonts'},
                                 {delete='_assets/kr5-tablet/sounds'},
                                 {delete='_assets/kr5-phone/strings/de.lua'},{delete='_assets/kr5-phone/strings/en.lua'},{delete='_assets/kr5-phone/strings/es.lua'},{delete='_assets/kr5-phone/strings/fr.lua'},{delete='_assets/kr5-phone/strings/ja.lua'},{delete='_assets/kr5-phone/strings/ko.lua'},{delete='_assets/kr5-phone/strings/pt.lua'},{delete='_assets/kr5-phone/strings/ru.lua'},{delete='_assets/kr5-phone/strings/zh-Hant.lua'},
                                 {delete='_assets/kr5-tablet/strings/de.lua'},{delete='_assets/kr5-tablet/strings/en.lua'},{delete='_assets/kr5-tablet/strings/es.lua'},{delete='_assets/kr5-tablet/strings/fr.lua'},{delete='_assets/kr5-tablet/strings/ja.lua'},{delete='_assets/kr5-tablet/strings/ko.lua'},{delete='_assets/kr5-tablet/strings/pt.lua'},{delete='_assets/kr5-tablet/strings/ru.lua'},{delete='_assets/kr5-tablet/strings/zh-Hant.lua'},
                                 {rename='_assets/kr5-tablet/strings/zh-Hans-e2w.lua', to='_assets/kr5-tablet/strings/zh-Hans.lua'},
                                 {rename='_assets/kr5-phone/strings/zh-Hans-e2w.lua', to='_assets/kr5-phone/strings/zh-Hans.lua'}
                             },
                             --checksum_files={'game.love'},
                           },
    kr5_dyn_android_china = { game='kr5', target='dynamic', targets={'phone','tablet'}, platform='android', task='aab', bundle='com.ironhidegames.android.kingdomrush.alliance.cn', version_code_suffix='00',
                           keystore=KEYSTORES.kr5,  keystore_alias='mykey', gradle_subdir='kr5_censored_cn',
                           --checksum_files={'asset-slices/kr5_gc64-master.apk','asset-slices/kr5_gc32-master.apk'}, -- order is important, must be the same as features.lua entry
                           image_override = 'censored_cn',
                           modify_assets={
                               {replace='images/ipadhd', with='images/ipadhd_etc2'},
                               {delete='../kr5-tablet/images'},
                               {delete='../kr5-tablet/fonts'},  -- asset_all_fallback set to phone
                               {delete='../kr5-tablet/sounds'}, -- asset_game_fallback set to phone
                               {delete='../all-tablet/fonts'},  -- asset_all_fallback set to phone
                               {delete='../all-phone/fonts'},   -- asset_all_fallback set to phone
                               {delete='../kr5-phone/strings/de.lua'},{delete='../kr5-phone/strings/en.lua'},{delete='../kr5-phone/strings/es.lua'},{delete='../kr5-phone/strings/fr.lua'},{delete='../kr5-phone/strings/ja.lua'},{delete='../kr5-phone/strings/ko.lua'},{delete='../kr5-phone/strings/pt.lua'},{delete='../kr5-phone/strings/ru.lua'},{delete='../kr5-phone/strings/zh-Hant.lua'},
                               {delete='../kr5-tablet/strings/de.lua'},{delete='../kr5-tablet/strings/en.lua'},{delete='../kr5-tablet/strings/es.lua'},{delete='../kr5-tablet/strings/fr.lua'},{delete='../kr5-tablet/strings/ja.lua'},{delete='../kr5-tablet/strings/ko.lua'},{delete='../kr5-tablet/strings/pt.lua'},{delete='../kr5-tablet/strings/ru.lua'},{delete='../kr5-tablet/strings/zh-Hant.lua'},
                               {rename='kr5-tablet/strings/zh-Hans-e2w.lua', to='kr5-tablet/strings/zh-Hans.lua'},
                               {rename='kr5-phone/strings/zh-Hans-e2w.lua', to='kr5-phone/strings/zh-Hans.lua'}
                            },
                            },

    -- desktop / kr1
    kr1_standalone_win32      = { game='kr1', target='desktop', platform='win32',        task='win_prebuilt',   title='Kingdom Rush', bundle='com.ironhidegames.kingdomrush.standalone' },
    kr1_standalone_win        = { game='kr1', target='desktop', platform='win',          task='win_prebuilt',   title='Kingdom Rush', bundle='com.ironhidegames.kingdomrush.standalone' },    
    kr1_standalone_mac        = { game='kr1', target='desktop', platform='mac',          task='mac_prebuilt',   title='Kingdom Rush', bundle='com.ironhidegames.kingdomrush.standalone', certs={app="0DFF3B86D2BF723DAD1CE0C0CFA1B8D75AA5F3AE"}},
    kr1_standalone_linux      = { game='kr1', target='desktop', platform='linux',        task='linux_prebuilt', title='Kingdom Rush', bundle='com.ironhidegames.kingdomrush.standalone' },

    kr1_desktop_steam_win32   = { game='kr1', target='desktop', platform='win32',        task='win_prebuilt',   title='Kingdom Rush', bundle='com.ironhidegames.kingdomrush.windows.steam' },
    kr1_desktop_steam_win     = { game='kr1', target='desktop', platform='win',          task='win_prebuilt',   title='Kingdom Rush', bundle='com.ironhidegames.kingdomrush.windows.steam' },    
    kr1_desktop_steam_mac     = { game='kr1', target='desktop', platform='mac',          task='mac_prebuilt',   title='Kingdom Rush', bundle='com.ironhidegames.kingdomrush.mac.steam', certs={app="0DFF3B86D2BF723DAD1CE0C0CFA1B8D75AA5F3AE"}},
    kr1_desktop_steam_linux   = { game='kr1', target='desktop', platform='linux',        task='linux_prebuilt', title='Kingdom Rush', bundle='com.ironhidegames.kingdomrush.linux.steam' },
    
    kr1_desktop_appstore      = { game='kr1', target='desktop', platform='mac-appstore', task='mac_prebuilt',   title='Kingdom Rush', bundle='com.ironhidegames.kingdomrush-mac',
                                  certs={
                                      app="D16FD1E27CF0BAD9DE591D679CA8E45E2E95114E",  --  app="6F63B2360BF1380595EFB6B511AE77004BC98EDF",
                                      inst="9A35EB2E0292DF00DB967D8807E0D7A4690CB1EA", -- inst="09B5C5346FAD888308FD1E644DE72478C19B00A7"
                                  },
                                  profile="2abaf045-71e7-4876-9b67-44b867d20233",   -- Kingdom Rush HD Mac App Store
                                  sign_id="3rd Party Mac Developer Application: Ironhide (Q4V458A2B4)",
                                  sign_method='app-store'},

    
    -- desktop / kr2          
    kr2_standalone_win32      = { game='kr2', target='desktop', platform='win32',        task='win_prebuilt',   title='Kingdom Rush Frontiers', bundle='com.ironhidegames.frontiers.standalone' },
    kr2_standalone_win        = { game='kr2', target='desktop', platform='win',          task='win_prebuilt',   title='Kingdom Rush Frontiers', bundle='com.ironhidegames.frontiers.standalone' },    
    kr2_standalone_mac        = { game='kr2', target='desktop', platform='mac',          task='mac_prebuilt',   title='Kingdom Rush Frontiers', bundle='com.ironhidegames.frontiers.standalone', certs={app="0DFF3B86D2BF723DAD1CE0C0CFA1B8D75AA5F3AE"}},
    kr2_standalone_linux      = { game='kr2', target='desktop', platform='linux',        task='linux_prebuilt', title='Kingdom Rush Frontiers', bundle='com.ironhidegames.frontiers.standalone' },

    kr2_desktop_steam_win32   = { game='kr2', target='desktop', platform='win32',        task='win_prebuilt',   title='Kingdom Rush Frontiers', bundle='com.ironhidegames.frontiers.windows.steam' },
    kr2_desktop_steam_win     = { game='kr2', target='desktop', platform='win',          task='win_prebuilt',   title='Kingdom Rush Frontiers', bundle='com.ironhidegames.frontiers.windows.steam' },    
    kr2_desktop_steam_mac     = { game='kr2', target='desktop', platform='mac',          task='mac_prebuilt',   title='Kingdom Rush Frontiers', bundle='com.ironhidegames.frontiers.mac.steam', certs={app="0DFF3B86D2BF723DAD1CE0C0CFA1B8D75AA5F3AE"}},
    kr2_desktop_steam_linux   = { game='kr2', target='desktop', platform='linux',        task='linux_prebuilt', title='Kingdom Rush Frontiers', bundle='com.ironhidegames.frontiers.linux.steam' },
        
    kr2_desktop_appstore      = { game='kr2', target='desktop', platform='mac-appstore', task='mac_prebuilt', title='Kingdom Rush Frontiers', bundle='com.ironhidegames.kingdomrushfrontiers-mac',
                                  certs={app="6F63B2360BF1380595EFB6B511AE77004BC98EDF",
                                         inst="09B5C5346FAD888308FD1E644DE72478C19B00A7"},
                                  profile="",
                                  sign_id="3rd Party Mac Developer Application: Ironhide (Q4V458A2B4)",
                                  sign_method='app-store',
                                  gamecenter=true,
                                  icon='kr2_appstore.icns',
                                  love_icon='icon256_appstore.png',
    },

    -- desktop / kr5
    kr5_standalone_win    = { game='kr5', target='desktop', platform='win',          task='win_prebuilt',   title='Kingdom Rush Alliance', bundle='com.ironhidegames.kingdomrush.alliance.windows.standalone', modify_assets={{delete='images/ipadhd'}}, file_cmds={{delete='_assets/all-desktop/fonts'}}},
    kr5_standalone_mac    = { game='kr5', target='desktop', platform='mac',          task='mac_prebuilt',   title='Kingdom Rush Alliance', bundle='com.ironhidegames.kingdomrush.alliance.mac.standalone',     modify_assets={{delete='images/ipadhd'}}, file_cmds={{delete='_assets/all-desktop/fonts'}}, certs={app="E6E7E3BAE1E60C552FA41686E6E80CDCD3B640D5"}, force_luajit=LUAJIT_21_GC64 },
    kr5_desktop_steam_win = { game='kr5', target='desktop', platform='win',          task='win_prebuilt',   title='Kingdom Rush Alliance', bundle='com.ironhidegames.kingdomrush.alliance.windows.steam',      modify_assets={{delete='images/ipadhd'}}, file_cmds={{delete='_assets/all-desktop/fonts'}}},
    kr5_desktop_steam_mac = { game='kr5', target='desktop', platform='mac',          task='mac_prebuilt',   title='Kingdom Rush Alliance', bundle='com.ironhidegames.kingdomrush.alliance.mac.steam',          modify_assets={{delete='images/ipadhd'}}, file_cmds={{delete='_assets/all-desktop/fonts'}}, certs={app="E6E7E3BAE1E60C552FA41686E6E80CDCD3B640D5"}, force_luajit=LUAJIT_21_GC64 },
        
    kr5_desktop_appstore  = { game='kr5', target='desktop', platform='mac-appstore', task='mac_prebuilt',   title='Kingdom Rush Alliance', bundle='com.ironhidegames.kingdomrush.alliance.mac.appstore',       modify_assets={{delete='images/ipadhd'}}, file_cmds={{delete='_assets/all-desktop/fonts'}},
                              force_luajit=LUAJIT_21_GC64,
                              certs={app="304448FB16806745A16A32F999A7CE0DE5A2B820",
                                     inst="F959F5928B8ABC850D683C794224652DD1516721"},
                              profile="4597dd5c-49d6-4ddd-bf0b-81b332f42252",
                              sign_id="3rd Party Mac Developer Application: Ironhide (Q4V458A2B4)",
                              sign_method='app-store',
                              gamecenter=true,
                              icon='kr5_appstore.icns',
                              love_icon='icon256_appstore.png',
                            },
    
    --kr5_standalone_win32      = { game='kr5', target='phone', platform='win32',        task='win32_prebuilt', title='Kingdom Rush Alliance', bundle='com.ironhidegames.kingdomrush.alliance' },
    
    -- console / nx
    kr1_console_switch =  { game='kr1', target='console', platform='nx', task='nx', title='Kingdom Rush',           bundle='com.ironhidegames.kingdomrush.nx', project_subdir='kr-love/platform/src/nx/kr-nx/kr1', modify_assets={{delete='images/ipad'},{replace='images/fullhd', with='images/fullhd_astc'}},file_cmds={{delete_file='_assets/all-console/fonts/segoe-xbox-mdl2-assets.ttf'},{delete_file='all-console/platform_services_xbox.lua'}} },
    kr2_console_switch =  { game='kr2', target='console', platform='nx', task='nx', title='Kingdom Rush Frontiers', bundle='com.ironhidegames.frontiers.nx'  , project_subdir='kr-love/platform/src/nx/kr-nx/kr2', modify_assets={{delete='images/ipad'},{replace='images/fullhd', with='images/fullhd_astc'}},file_cmds={{delete_file='_assets/all-console/fonts/segoe-xbox-mdl2-assets.ttf'},{delete_file='all-console/platform_services_xbox.lua'}} },
    kr3_console_switch =  { game='kr3', target='console', platform='nx', task='nx', title='Kingdom Rush Origins',   bundle='com.ironhidegames.origins.nx'    , project_subdir='kr-love/platform/src/nx/kr-nx/kr3', modify_assets={{delete='images/ipad'},{replace='images/fullhd', with='images/fullhd_astc'}},file_cmds={{delete_file='_assets/all-console/fonts/segoe-xbox-mdl2-assets.ttf'},{delete_file='all-console/platform_services_xbox.lua'}} },
    
    -- console / xbox (UWP - deprecated)
    --kr1_console_xbox =  { game='kr1', target='console', platform='xbox', task='xbox', title='Kingdom Rush',           bundle='com.ironhidegames.kingdomrush.xbox', project_subdir='kr-love/platform/src/xbox/kr-xbox/kr1', image_override='xbox', modify_assets={{delete='images/fullhd_astc'},{delete='images/fullhd_bc3'}} },
    kr1_console_xbox =  { game='kr1', target='console', platform='xbox', task='xbox', title='Kingdom Rush',           bundle='com.ironhidegames.kingdomrush.xbox', project_subdir='kr-love/platform/src/xbox/kr-xbox/kr1', image_override='xbox', modify_assets={{delete='images/fullhd_astc'},{replace='images/fullhd', with='images/fullhd_bc3'}},file_cmds={{delete_file='_assets/all-console/fonts/nintendo_ext_LE_003.ttf'},{delete_file='all-console/platform_services_nx.lua'}} },
    kr2_console_xbox =  { game='kr2', target='console', platform='xbox', task='xbox', title='Kingdom Rush Frontiers', bundle='com.ironhidegames.frontiers.xbox'  , project_subdir='kr-love/platform/src/xbox/kr-xbox/kr2', image_override='xbox', modify_assets={{delete='images/fullhd_astc'},{replace='images/fullhd', with='images/fullhd_bc3'}},file_cmds={{delete_file='_assets/all-console/fonts/nintendo_ext_LE_003.ttf'},{delete_file='all-console/platform_services_nx.lua'}, {delete='_assets/kr2-console/images/ipad'}} },
    kr3_console_xbox =  { game='kr3', target='console', platform='xbox', task='xbox', title='Kingdom Rush Origins',   bundle='com.ironhidegames.origins.xbox'    , project_subdir='kr-love/platform/src/xbox/kr-xbox/kr3', image_override='xbox', modify_assets={{delete='images/fullhd_astc'},{replace='images/fullhd', with='images/fullhd_bc3'}},file_cmds={{delete_file='_assets/all-console/fonts/nintendo_ext_LE_003.ttf'},{delete_file='all-console/platform_services_nx.lua'}} },
    
    -- windows store build of the xbox uwp binary (deprecated)
    kr1_desktop_msstore = { game='kr1', target='desktop', platform='win',  task='win_prebuilt',  msstore_dir='msstore/kr1', title='Kingdom Rush',           bundle='com.ironhidegames.kingdomrush.windows.msstore' },
    --kr2_desktop_msstore = { game='kr2', target='desktop', platform='win',  task='win_prebuilt',  msstore='msstore/kr1', title='Kingdom Rush Frontiers', bundle='com.ironhidegames.frontiers.windows.msstore' },
    kr3_desktop_msstore = { game='kr3', target='desktop', platform='win',  task='win_prebuilt',  msstore_dir='msstore/kr3', title='Kingdom Rush Origins',   bundle='com.ironhidegames.origins.windows.msstore' },    

    -- console gdk_xboxone
    kr1_console_gdk_xboxone =  { game='kr1', target='console', platform='xbox', task='gdk_xbox', xbox_platform='XboxOne', title='Kingdom Rush',           bundle='com.ironhidegames.kingdomrush.gdk_xbox', project_subdir='kr1-gdk', image_override='xbox', modify_assets={{delete='images/fullhd_astc'},{replace='images/fullhd', with='images/fullhd_bc3'}},file_cmds={{delete_match='_assets/all%-console/shaders/[%w_]+%.c$'},{delete_file='_assets/all-console/fonts/nintendo_ext_LE_003.ttf'},{delete_file='all-console/platform_services_nx.lua'}} },
    kr2_console_gdk_xboxone =  { game='kr2', target='console', platform='xbox', task='gdk_xbox', xbox_platform='XboxOne', title='Kingdom Rush Frontiers', bundle='com.ironhidegames.frontiers.gdk_xbox',   project_subdir='kr2-gdk', image_override='xbox', modify_assets={{delete='images/fullhd_astc'},{replace='images/fullhd', with='images/fullhd_bc3'}},file_cmds={{delete_match='_assets/all%-console/shaders/[%w_]+%.c$'},{delete_file='_assets/all-console/fonts/nintendo_ext_LE_003.ttf'},{delete_file='all-console/platform_services_nx.lua'}} },
    kr3_console_gdk_xboxone =  { game='kr3', target='console', platform='xbox', task='gdk_xbox', xbox_platform='XboxOne', title='Kingdom Rush Origins',   bundle='com.ironhidegames.origins.gdk_xbox',     project_subdir='kr3-gdk', image_override='xbox', modify_assets={{delete='images/fullhd_astc'},{replace='images/fullhd', with='images/fullhd_bc3'}},file_cmds={{delete_match='_assets/all%-console/shaders/[%w_]+%.c$'},{delete_file='_assets/all-console/fonts/nintendo_ext_LE_003.ttf'},{delete_file='all-console/platform_services_nx.lua'}} },
    
    -- desktop gdk builds
    kr1_desktop_gdk_win =  { game='kr1', target='desktop', platform='win', task='gdk_win', title='Kingdom Rush',           bundle='com.ironhidegames.kingdomrush.gdk_win', project_subdir='kr1-gdk', file_cmds={{delete_match='_assets/all%-desktop/shaders/[%w_]+%.c$'}} },
    kr2_desktop_gdk_win =  { game='kr2', target='desktop', platform='win', task='gdk_win', title='Kingdom Rush Frontiers', bundle='com.ironhidegames.frontiers.gdk_win',   project_subdir='kr2-gdk', file_cmds={{delete_match='_assets/all%-desktop/shaders/[%w_]+%.c$'}} },
    kr3_desktop_gdk_win =  { game='kr3', target='desktop', platform='win', task='gdk_win', title='Kingdom Rush Origins',   bundle='com.ironhidegames.origins.gdk_win',     project_subdir='kr3-gdk', file_cmds={{delete_match='_assets/all%-desktop/shaders/[%w_]+%.c$'}} },

    -- console / marketing
    kr1_xbox_on_pc  = { game='kr1', target='console', platform='xbox', task='win_prebuilt', title='Kingdom Rush',           bundle='net.kalio.test.kr1_xbox_on_pc', image_override='xbox', modify_assets={{delete='images/fullhd_astc'},{delete='images/fullhd_bc3'}} },
    kr2_xbox_on_pc  = { game='kr2', target='console', platform='xbox', task='win_prebuilt', title='Kingdom Rush Frontiers', bundle='net.kalio.test.kr2_xbox_on_pc', image_override='xbox', modify_assets={{delete='images/fullhd_astc'},{delete='images/fullhd_bc3'},{delete='images/ipad'}} },
    kr3_xbox_on_pc  = { game='kr3', target='console', platform='xbox', task='win_prebuilt', title='Kingdom Rush Origins',   bundle='net.kalio.test.kr3_xbox_on_pc', image_override='xbox', modify_assets={{delete='images/fullhd_astc'},{delete='images/fullhd_bc3'}} },

    -- challenges development
    kr2_challenges = { game='kr2', target='phone', task='win_prebuilt', title='KRF Challenges', bundle='net.kalio.test.CHALLENGES' },
}

-- add key as id to SETS
for k,v in pairs(SETS) do
    v.id = k
end

------------------------------------------------------------

package.path = package.path .. ';' .. '../src/lib/?.lua'
require('klua.string')
require('klua.table')
require('klua.dump')
local lfs = require('lfs')

local DIR_SEP     = '/'  -- TODO: multiplatform

local LEVEL_DEBUG = 5
local LEVEL_INFO  = 3
local LEVEL_ERROR = 1
local LOG_LEVEL = LEVEL_INFO

local report = {}  -- output report data
report.comments = {}
local tasks = {} -- tasks container

------------------------------------------------------------
-- helper functions

local function has_arg(key)
    return table.contains(arg, '-' .. key)
end
local function argv(key)
    return arg[table.keyforobject(arg, '-' .. key) + 1]
end

local function path(...)
    return table.concat({...}, DIR_SEP)
end

local function basename(p)
    local parts = string.split(p, DIR_SEP)
    return parts[#parts]
end

local function replace_path(p,pattern,replacement)
    -- escape the pattern as we can have - on the path
    return string.gsub(p, string.gsub(pattern, '-','%%-'), replacement)
end

local function dirname(file)
    local bn = basename(file)
    return replace_path(file, bn, '')
end
--------------------

local function log_debug(...)
    if LOG_LEVEL >= LEVEL_DEBUG then
        io.stderr:write('DEBUG: ' .. string.format(...))
        io.stderr:write("\n")
    end
end
local function log_info(...)
    if LOG_LEVEL >= LEVEL_INFO then 
        io.stderr:write('INFO : ' .. string.format(...))
        io.stderr:write("\n")
    end
end
local function log_error(...)
    if LOG_LEVEL >= LEVEL_ERROR then
        io.stderr:write('ERROR: ' .. string.format(...))
        io.stderr:write("\n")
    end
end

--------------------

local function run(...)
    local cmd = string.format(...)
    log_debug('run: %s', cmd)
    local ret = os.execute(cmd)
    if ret ~= true then
        log_error('error %s executing %s', ret, cmd)
        os.exit(-1)
    end
    return true
end
local function run_no_fail(...)
    local cmd = string.format(...)
    log_debug('run_no_fail: %s', cmd)
    local ret = os.execute(cmd)
    if ret ~= true then
        log_debug('ignoring error %s executing %s', ret, cmd)
    end
    return true
end
local function runget(...)
    local cmd = string.format(...)
    log_debug('runget: %s', cmd)
    local h = io.popen(cmd)
    if not h then
        log_error('error executing %s', cmd)
        os.exit(-1)
    end
    local out = h:read("*a")
    out = string.gsub(out, '\n$', '')
    h:close()
    return out
end

--------------------
local function is_file(file)
    local t,msg = lfs.attributes(file)
    if t == nil then
        log_debug('could not find file %s: %s', file, msg)
        return false
    else
        return t.mode == 'file'
    end
end

local function is_dir(p)
    local t,msg = lfs.attributes(p)
    if t == nil then
        log_debug('could not find path %s: %s', p, msg)
        return false
    else
        return t.mode == 'directory'
    end    
end

local function chmod(mode, file)
    run('chmod %s %s', mode, file)
end

local function rm(file)
    log_info('rm "%s"', file)
    run('rm "%s"', file)
end

local function rmdir(dir)
    if dir ~= '' and dir ~= '/' then
        log_info('rmdir "%s"', dir)
        run('rm -rf "%s"', dir)
    end
end

local function mkdir(dir)
    if dir ~= '' and dir ~= '/' and dir ~= '~' then
        log_info('mdir -p "%s"', dir)
        run('mkdir -p "%s"', dir)
    end    
end

local function rsync(src,dst,args)
    log_info('rsync -r %s "%s" "%s"', (args or ''), src, dst)
    run('rsync -r %s "%s" "%s"', (args or ''), src, dst)
end
local function cp(src,dst,nofail)
    if nofail == nil or is_file(src) then
        run('cp "%s" "%s"', src, dst)
    end
end
local function mv(src,dst)
    run('mv "%s" "%s"', src, dst)
end

local function append(dst, ...)
    local sources = ''
    for _,v in pairs({...}) do
        sources = sources ..' "'..v..'"'
    end
        
    run('cat %s > "%s"', sources, dst)
end


local function echo(str,file,appends)
    local h = io.open(file, appends and 'wa' or 'w')
    if not h then
        log_error('echo error: could not open %s for writing', file)
        return
    end
    h:write(str)
    h:close()
end

local function read(file)
    local h = io.open(file, 'r')
    if not h then
        log_error('read error: could not open %s for reading', file)
        return
    end
    local out = h:read('*a')
    h:close()
    return out
end

local function call(file, env)
    -- lua load file
    local data = read(file)
    if not data then
        log_error('call error: could not read %s', file)
        return
    end
    
    env = env or {}
    local chunk,err
    if not loadstring then
        -- lua 5.2
        chunk,err = load(data,nil,nil,env)
    else        
        chunk,err = loadstring(data)
    end
    if not chunk then
        log_error('call error: could not load %s. Error %s', file, err)
        return
    end
    
    if loadstring then
        -- lua 5.1
        setfenv(chunk, env)
    end
    local ok,result = pcall(chunk)
    if not ok then
        log_error('call error: could not call %s. Error: %s', file, tostring(result))
        return
    end
    return result
end

local function find_files(dir,pattern,out,shallow)
    if out == nil then
        log_error('find_files(): output table parameter missing')
        os.exit(-1)
    end
    for file in lfs.dir(dir) do
        if file == '..' or file == '.' then
            goto skip
        end
        local fullname = dir ..  DIR_SEP .. file
        local attr = lfs.attributes(fullname)
        if attr.mode == 'directory' and not shallow then
            -- recurse
            find_files(fullname, pattern, out)
        elseif attr.mode == 'file' then
            -- check pattern
            if not pattern or string.match(fullname,pattern) then
                table.insert(out, fullname)
            end
        end
        ::skip::
    end
end

local function find_dirs(dir)
    local out = {}
    for file in lfs.dir(dir) do
        if file == '..' or file == '.' then
            goto skip
        end
        local fullname = dir ..  DIR_SEP .. file
        local attr = lfs.attributes(fullname)
        if attr and attr.mode == 'directory' then
            out = table.append(out, find_dirs(fullname))
            table.insert(out, fullname)
        end
        ::skip::
    end
    return out
end

local function zip(src,dst,fast)
    local cwd = lfs.currentdir()
    lfs.chdir(src)
    log_info('zipping "%s" to "%s"...', src,dst)
    run('zip %s -q -r "%s" .', fast and '' or '-9', dst)
    lfs.chdir(cwd)
end

local function ditto(src,dst)
    log_info('ditto "%s" to "%s"...', src,dst)
    run('ditto -c -k --keepParent "%s" "%s"', src, dst)
end

local function unzip(src,dst)
    local cwd = lfs.currentdir()
    lfs.chdir(dst)
    log_info('unzipping "%s" to "%s"...', src,dst)
    run('unzip -q "%s"', src)
    lfs.chdir(cwd)
end

local function du(dir)
    local line = runget('du -k -s %s', dir)
    local parts = string.split(line, '\t')
    return tonumber(parts[1])
end

local function checksum(files,separator)
        local sums = ''        
        for _,d in pairs(files) do
            log_info('checksumming %s ...', d)
            local line = runget('md5sum "%s"', d)
            local parts = string.split(line,' ')
            if separator then                
                sums = sums .. parts[1] .. (separator or '')
            else
                sums = sums .. basename(d) .. ' : ' .. parts[1] .. '\n'
            end
            log_debug('   %s:%s', d, parts[1])
        end
        if separator then
            -- non-nil separator returns md5 of concatenated sums
            log_debug('---- SUMS: %s',sums)
            local line = runget('echo "%s\\c" | md5sum -', sums)  -- \c is required as the echo command in /bin/sh has no -n parameter
            local parts = string.split(line, ' ')
            local sum = parts[1]
            log_debug('---- SUM: %s', sum)
            return sum
        else
            return sums
        end
end

------------------------------------------------------------

local function get_git_rev(set)
    local cmd = "git describe --tags  --match %s-%s-*"

    if not table.contains({'gdk_win', 'gdk_xbox', 'xbox', 'nx', 'win_prebuilt'}, set.task) then 
        -- dirty is SUPER slow for remote mounted repos, so don't use it in those cases
        cmd = cmd .. " --dirty='*'"
    end

    log_info('get_git_rev task:%s, cmd:%s', set.task, cmd)
    local rev = runget(cmd, set.game, set.targets and set.targets[1] or set.target)
    rev = string.gsub(rev, '\n', '')
    return rev
end

local function export_assets(set,output,dbg)
    log_info('exporting assets...')
    local game = set.game

    local args = ''
    if not set.image_override then
        args = args .. '--exclude="_ov/" '
    end
    args = args .. '--exclude-from=exclude.list --include "*/" --prune-empty-dirs --copy-links'
    local targets = set.targets or {set.target}
    for _,target in pairs(targets) do
        rsync(path(SRC_PATH, '_assets/all-' .. target    ),output, args)
        rsync(path(SRC_PATH, '_assets/',game..'-'..target),output, args)
    end

    if is_dir(path(SRC_PATH, '_assets/_resources', set.bundle)) then
        log_info('copying _assets_resources file for this bundle...')
        rsync(path(SRC_PATH, '_assets/_resources', set.bundle), path(output,'_resources'), args)
    end

    if set.include_assets then
        local aargs = '--exclude-from=exclude.list --prune-empty-dirs --copy-links '
        for _,row in pairs(set.include_assets) do
            local a = aargs
            if row.include then
                a = a .. string.format("--include='%s' --include='*/' --exclude='*' ", row.include)
            end
            rsync(path(SRC_PATH, '_assets', row.dir), output, a)
        end
    end

    -- TODO: move this to asset_all_fallback in features for dynamic apps    
    --if set.platform == 'ios' and set.target == 'universal' then
    --    -- sound files are shared, so remove the tablet set as we will fallback to phone (saves 50MB)
    --    log_info('universal: removing tablet sound files...')
    --    rmdir(path(output,game..'-tablet','sounds'))
    --    -- font files are shared, so remove the tablet set (saves 7MB)
    --    log_info('universal: removing tablet font files...')
    --    rmdir(path(output,'all-tablet','fonts'))
    --end

    if set.image_override then
        -- remove overriden original textures (they will be loaded from the _ov dir by director.lua)
        for _,target in pairs(targets) do 
            local packs_dir = path(output,game..'-'..target, 'images')
            for pack in lfs.dir(packs_dir) do
                if pack ~= '..' and pack ~= '.' then
                    local ovdir = path(packs_dir, pack, '_ov', set.image_override)
                    if is_dir(ovdir) then 
                        for file in lfs.dir(ovdir) do
                            if file ~= '..' and file ~= '.' then
                                local file_path = path(packs_dir, pack, file)
                                log_info('override %s removes: %s', set.image_override, file_path)
                                rm(file_path)
                            end
                        end
                    end
                end
            end
        end
    end
    
    if set.sound_override then
        -- remove overriden original sounds (they will be loaded from the _ov dir by sound_db.lua)
        for _,target in pairs(targets) do 
            local packs_dir = path(output,game..'-'..target, 'sounds','files')
             log_info('sound dir '..packs_dir)
            for pack in lfs.dir(packs_dir) do
                if pack ~= '..' and pack ~= '.' then
                    local ovdir = path(packs_dir, '_ov', set.sound_override,pack)
                    if is_file(ovdir) then 
                        local ddir = path(packs_dir,pack)
                        log_info('delete '..ddir)
                        rm(ddir)
                    end
                end
            end
        end
    end
    if set.platform == 'android' or set.platform == 'ios' then
        
        for _,target in pairs(targets) do
            -- remove stray ipad dirs in phone dirs
            if target == 'phone' then
                rmdir(path(output, game..'-'..target, 'images/ipad'))
            end
        end
    end

    if set.modify_assets then
        for _,target in pairs(targets) do        
            for _,row in pairs(set.modify_assets) do
                if row.delete then
                    local p_delete = path(output, game..'-'..target, row.delete)
                    log_info('deleting %s ...',row.delete)
                    rmdir(p_delete)
                elseif row.delete_file then
                    local p_delete = path(output, game..'-'..target, row.delete_file)
                    log_info('deleting file %s ...',row.delete_file)
                    rm(p_delete)
                elseif row.replace then 
                    local p_replace = path(output, game..'-'..target, row.replace)
                    local p_with = path(output, game..'-'..target, row.with)            
                    if is_dir(p_replace) then
                        log_info('replacing %s with %s...', row.replace, row.with)
                        rmdir(p_replace)
                        mv(p_with, p_replace)
                    end
                elseif row.lz4 then
                    if set.platform ~= 'android' then
                        log_error('lz4 is only implemented for android. skipping...')
                    else
                        local p_dir = path(output, game..'-'..target, row.lz4)
                        log_info('lz4 packing dir %s', p_dir)
                        if is_dir(p_dir) then
                            -- 1. replace all pkm or ktx files in lua files with  pkm.lz4 or ktx.lz4
                            local lua_files = {}
                            find_files(p_dir, '.*%.lua', lua_files)
                            for _,f in pairs(lua_files) do
                                run('sed -i "" s/.pkm\\"/.pkm.lz4\\"/g %s', f)
                                run('sed -i "" s/.ktx\\"/.ktx.lz4\\"/g %s', f)
                            end
                            -- 2. compress all pkm or ktx files mentioned in the lua files with lz4, including file size in the header
                            local lz4_cmd = 'lz4 -9 -q -z --rm --content-size'
                            local image_files = {}
                            find_files(p_dir, '.*%.pkm', image_files)
                            find_files(p_dir, '.*%.ktx', image_files)
                            for _,f in pairs(image_files) do
                                run('%s %s %s.lz4', lz4_cmd, f, f)
                            end
                        end
                    end
                elseif row.rename then  
                    local p_old = path(output, row.rename)
                    local p_new = path(output, row.to)
                    log_info('renaming %s to %s...', p_old, p_new)
                    os.rename(p_old, p_new)
                end
            end
        end
    end

    -- bundle only files
    local bundle_dir = path(SRC_PATH, '_assets/_bundle', set.bundle)
    if is_dir(bundle_dir) then
        log_info('exporting bundle only assets...')
        rsync(bundle_dir, path(output,'_bundle'), args)
    end
end

local function export_license(set,output,dbg)
    log_info('exporting licenses...')
    cp(path(SRC_PATH,'license-kr-'..(set.targets and set.targets[1] or set.target)..'.txt'),output)
end

local function export_src(set,output,dbg)
    log_info('exporting sources...')
    local args = '--exclude-from=exclude.list --include "*/"  --prune-empty-dirs --copy-links'
    local game = set.game
    local ist = table.contains({'kr1','kr2','kr3'},set.game)
    rsync(path(SRC_PATH, 'all'                         ),output,args)
    rsync(path(SRC_PATH, ist and 'trilogy' or 'sequels'),output,args)
    rsync(path(SRC_PATH, game                          ),output,args)
    rsync(path(SRC_PATH, 'lib'                         ),output,args)
    cp(path(SRC_PATH,'main.lua'),output)
    cp(path(SRC_PATH,'log_levels_release.lua'),output)

    if set.task == 'gdk_xbox' then
        log_info('creating conf.lua for Xbox GDK...')
        local data = ''
        data = data .. "function love.conf(t)\n"
        data = data .. "    t.window.width = 1920\n"
        data = data .. "    t.window.height = 1080\n"
        data = data .. "    t.window.fullscreen = true\n"
        data = data .. "end"
        echo(data, path(output,'conf.lua'))        
    else        
        cp(path(SRC_PATH,'conf.lua'),output)
    end
    
    local targets = set.targets or {set.target}
    for _,target in pairs(targets) do
        rsync(path(SRC_PATH, 'all-'..target   ),output,args)
        rsync(path(SRC_PATH, game..'-'..target),output,args)
    end
    
    if (dbg) then
        rsync(path(SRC_PATH,'test')   ,output,args)
        rsync(path(SRC_PATH,'lib/jit'),path(output,'lib'))
        cp(path(SRC_PATH,'args.lua'                          ),output,true) -- nofail
        cp(path(SRC_PATH,'debug_eval.lua'                    ),output)
        cp(path(SRC_PATH,'log_levels_debug.lua'              ),output)
        cp(path(SRC_PATH,'lib/klove/draw_stats.lua'                ),path(output,'lib/klove/'))
        cp(path(SRC_PATH,'lib/klove/profiler.lua'                  ),path(output,'lib/klove/'))
        cp(path(SRC_PATH,'all/test_platform_services_ads.lua'),path(output,'all/'))
        cp(path(SRC_PATH,'all/test_platform_services_iap.lua'),path(output,'all/'))        
        cp(path(SRC_PATH,'lib/klua/repl.lua'                 ),path(output,'lib/klua/'))
        cp(path(SRC_PATH,game,'data/slot_template_debug.lua' ),path(output,game,'data/'))
    end
end

local function do_file_cmds(set,output,dbg)
    if not set.file_cmds then
        return
    end
    log_info('executing file_cmds...')
    local targets = set.targets or {set.target}
    for _,target in pairs(targets) do        
        for _,row in pairs(set.file_cmds) do
            if row.delete then
                local p_delete = path(output, row.delete)
                log_info('deleting %s ...',row.delete)
                rmdir(p_delete)
            elseif row.delete_file then
                local p_delete = path(output, row.delete_file)
                log_info('deleting file %s ...',row.delete_file)
                rm(p_delete)
            elseif row.delete_match then 
                log_info('deleting matches for %s ...', row.delete_match)
                local list = {}
                find_files(path(output), row.delete_match, list)
                for _,l in pairs(list) do
                    log_info('  deleting %s', l)
                    rm(l)
                end
            elseif row.replace then 
                local p_replace = path(output, row.replace)
                local p_with = path(output, row.with)            
                if is_dir(p_replace) then
                    log_info('replacing %s with %s...', row.replace, row.with)
                    rmdir(p_replace)
                    mv(p_with, p_replace)
                end
            elseif row.rename then  
                local p_old = path(output, row.rename)
                local p_new = path(output, row.to)
                log_info('renaming %s to %s...', p_old, p_new)
                os.rename(p_old, p_new)
            end
        end    
    end
end

local function add_arg_override(output,set,args)
    local out = ''
    if args.repl then
        log_info('adding arg overrides override... repl')
        if set.platform == 'ios' then
            out = out .. "'-repl', '*:9000', "
        else
            out = out .. "'-repl', '0.0.0.0:9000', "
        end
    end
    if args.localuser then
        log_info('adding arg overrides override... localuser')
        out = out .. "'-localuser' ,"
    end
    if args.draw_stats then 
        log_info('adding arg overrides override... draw_stats')
        out = out .. "'-draw-stats' ,"
    end
    if args.profiler then 
        log_info('adding arg overrides override... profiler')
        out = out .. "'-profiler' ,"
    end

    if out ~= '' then
        echo ('return {' .. out .. '}', path(output,'args.lua') )
    end
end

local function make_version_file(set,output,dbg, args)
    log_info('making version file...')
    local game = set.game
    local target = set.targets and set.targets[1] or set.target
    local git_rev = get_git_rev(set)
    local version = git_rev
    local version_short = string.gsub(version,'kr%d%-%a+%-(%d%d?%.%d%d?%.%d%d?%d?)%-?.*','%1')
    if args and args.override_version_short then
        version_short = args.override_version_short
    end
    local bundle_keywords = set.bundle
    -- longer strings first!
    for _,v in pairs({'com','ironhidegames','android','kingdomrushfrontiers','kingdomrushorigins','kingdomrush','armorgames','elves','frontiers','alliance'}) do
        bundle_keywords = string.gsub(bundle_keywords, v, '')
    end
    bundle_keywords = string.gsub(bundle_keywords, '%.+', '-')

    local data = read(path(SRC_PATH,game..'-'..target,'version.lua'))
    -- remove
    data = string.gsub(data, "version.string =.*\n",       "")
    data = string.gsub(data, "version.string_short =.*\n", "")
    data = string.gsub(data, "version.bundle_id =.*\n",    "")
    -- add 
    data = data .. string.format("version.string = '%s'\n", version)
    data = data .. string.format("version.string_short = '%s'\n", version_short)
    data = data .. string.format("version.bundle_id = '%s'\n", set.bundle)
    data = data .. string.format("version.vc = '%s'\n", git_rev)
    data = data .. string.format("version.build = '%s'\n", dbg and 'DEBUG' or 'RELEASE')
    data = data .. string.format("version.bundle_keywords = '%s'\n", bundle_keywords)
    echo(data, path(output,'version.lua'))

    report.version_short = version_short
    report.version_lua = data
    return version_short,data
end

local function make_main_globals(set,output)
    log_info('making main_globals file...')
    local data = string.format("KR_GAME='%s'; KR_TARGET='%s'; KR_PLATFORM='%s';",
                               set.game, set.target, set.platform)
    echo(data,path(output,'main_globals.lua'))
    report.main_globals_lua = data
    return data
end

local function make_features(set,output)
    -- filter out features file
    log_info('making features file...')
    echo(runget('%s %s/all/features.lua %s', LUAJIT_21_GC64, SRC_PATH, set.bundle),
         path(output, 'features.lua'))
end

local function bytecompile(set,dir,isgc64)
    local LUAJIT
    local LUAPATH = ''
    if set.force_luajit then
        LUAJIT = set.force_luajit
        if set.force_luajit == LUAJIT_21_GC64 and LUAJIT_21_GC64_PATH then
            LUAPATH = "LUA_PATH='" .. LUAJIT_21_GC64_PATH .. "'"
        end
    elseif set.platform == 'nx' then
        LUAJIT = LUAJIT_21_GC32
    elseif set.platform == 'android' or set.platform == 'ios' then
        LUAJIT = isgc64 and LUAJIT_21_GC64 or LUAJIT_21_GC32
        if LUAJIT_21_GC64_PATH then
            LUAPATH = "LUA_PATH='" .. LUAJIT_21_GC64_PATH .. "'"
        end
    elseif set.task == 'xbox' or set.task == 'gdk_xbox' or set.task == 'gdk_win' then
        LUAJIT = LUAJIT_21_GC64
    else
        LUAJIT = LUAJIT_20
        if LUAJIT_20_PATH then
            LUAPATH = "LUA_PATH='" .. LUAJIT_20_PATH .. "'"
        end
    end
    log_info('bytecompiling with %s...', LUAJIT)
    local luajit_ver = runget("%s -e 'print(jit.version)'", LUAJIT)
    local luajit_ver_num = tonumber(runget("%s -e 'print(jit.version_num)'", LUAJIT))

    if set.force_luajit then
        log_info('Bytecompiling with forced luajit: %s', set.force_luajit)
    elseif (set.platform == 'android'
        or set.platform == 'nx'
        or set.platform == 'ios'
        or set.task == 'xbox' or set.task == 'gdk_xbox' or set.task == 'gdk_win')
    then
        -- make sure we're using luajit 2.1.x
        if luajit_ver_num < 20100 or luajit_ver_num >= 20200 then
            log_error("Wrong LuaJIT version found:%s. LuaJIT 2.1.x is required.", luajit_ver)
            os.exit(-1)
            return
        end
    else
        -- make sure we're using luajit 2.0.x
        if luajit_ver_num < 20001 or luajit_ver_num >= 20100 then
            log_error("Wrong LuaJIT version found:%s. LuaJIT 2.0.x is required.", luajit_ver)
            os.exit(-1)
            return
        end
    end

    local cwd = lfs.currentdir()
    lfs.chdir(dir)
    local files = {}
    find_files('.', ".*%.lua",files)
    for _,f in pairs(files) do
        run('bash -c "unset LUA_PATH; %s %s -bg %s %s"', LUAPATH, LUAJIT, f, f)
    end
    lfs.chdir(cwd)
end

local function calculate_version_code(set,version_short,flavor)
    if set.version_code_suffix then
        -- KR5+ approach
        -- kr5 has builds in this range already: 1880004
        --
        -- max:  2147483647
        -- ver:   VVvvppp     - version VV.vv.ppp 
        -- suf:          00
        --
        --          18 800 04 - existing already on google play
        -- min:     19 000 00 - 0.19.0
        -- start:   20 000 00 - 0.20.0
        -- max   99 99 999 00 - 99.99.999
        -- eg:    1 00 001 00 - 1.0.1
        -- eg:    2 33 001 00 - 2.33.1

        local vver = string.split(version_short, '.')
        local VV = tonumber(vver[1])
        local vv = tonumber(vver[2])
        local pp = tonumber(vver[3])
        local out = tonumber(string.format("%d%02d%03d%s",VV,vv,pp, set.version_code_suffix))
        if out >= 2147483647 then
            print('error calculating version code for %s : %s', version_short, out)
            os.exit(-1)
        end
        return out
        
    else
        -- TRILOGY approach
        -- 
        -- apportable version codes are timestamps
        -- main.1501690411.com.ironhidegames.android.kingdomrushfrontiers.obb	162.4 MB 
        -- current version from apportable: 1501690411
        --              largest 32 bit int: 2147483647
        --
        -- These version codes allow for other packages but must be greater
        -- than apportable values.
        -- 
        -- versionCode = 1500000000 + aatscvvvv
        --   aa: api level  (max supported around 40!)
        --    t: texture comp format (0:etc1, 1:etc2, 4:pvr, 6:ati)
        --    s: screen size (0: multi size, 1:small, 2:normal, 3:large, 4:xlarge)
        --    c: ABI (0:multi 1:multi-32 2:multi-64 4:armabi 5:armabi-v7a, 6:arm64-v8a 7:x86, 8:x86_64)
        -- vvvv: version k.k.kk
        --
        -- the largest obb (64bit) was around 1710025103
        --
        -- for aab we start as 171 so the version code is larger than both 32 and 65 bit obbs
        -- 

        -- pull minsdk from project .gradle file
        flavor = flavor or 'fall'
        local dotgradle = read(path(ANDROID_PROJECT, 'kr_apps', set.gradle_subdir, 'build.gradle'))
        local minsdk = string.gsub(dotgradle, ".+"..flavor.." {[^}]+minSdkVersion (%d%d).+", "%1")
        minsdk = tonumber(minsdk)
        if minsdk > 35 then
            print('Running out of version_codes! minsdk > 35. Change the numbering system increase less in each version.')
            os.exit(-1)
        end
        report.minsdk = minsdk
        local t_abi_idx = 0
        local t_texture = 0
        local t_screen_size = 0
        local startsdk
        if set.task == 'aab' then 
            startsdk = 171 + minsdk   -- see comment above
        else
            startsdk = 150 + minsdk
        end
        local vver = string.gsub(version_short, '%.', '')
        local out = string.format('%d%s%s%s%s', startsdk, t_texture, t_screen_size, t_abi_idx, vver)
        return out
    end
end

local function patch_lib_install(lib_path, name, name_out)
    local o = runget('otool -l "%s" | grep %s', lib_path, name)
    if not o or o == '' then return end
    local name_in = string.gsub(o,'[^/]+([^ ]+).+$', '%1')
    log_info('patching install name for lib:%s from %s to %s', lib_path, name_in, name_out)
    run('install_name_tool -change %s %s "%s"', name_in, name_out, lib_path)
end

local function archive(set,args,src,suffix)
    -- keep a backup of the package

    local t,msg = lfs.attributes(src)
    if t == nil then
        log_error('could not find %s: %s', src, msg)
        os.exit(-1)
        return
    end
    
    -- calculate output name
    local archive_name = table.concat({
            set.game,
            set.target,
            report.version_short,
            set.platform,
            set.id,
            (args.debug and 'DEBUG' or 'RELEASE')
    }, '-')
    if set.task == 'aab' then
        archive_name = archive_name .. '-' .. report.aab_version_code .. '.aab'
    elseif suffix then
        archive_name = archive_name .. '-' .. suffix
    elseif t.mode == 'directory' then
        archive_name = archive_name .. '.zip'
    elseif set.platform == 'mac-appstore' then
        archive_name = archive_name .. '.pkg'
    end
    archive_name = path(DIST_ARCHIVE, archive_name)

    if t.mode == 'directory' then
        -- compress directory
        log_info('compressing archive...')
        if string.starts(set.platform,'mac') then
            ditto(src, archive_name)
        else
            zip(src, archive_name)
        end
    elseif t.mode == 'file' then
        log_info('copying archive...')
        cp(src, archive_name)
    end
    if not report.archive_names then
        report.archive_names = {}
    end
    table.insert(report.archive_names,archive_name)
    return archive_name
end

------------------------------------------------------------
-- tasks
------------------------------------------------------------

------------------------------------------------------------
-- nx
function tasks.nx(set, args)
    local tmpdir = runget('mktemp -d -t %s', set.bundle)
    local assetsdir = tmpdir..'/_assets'
    local outdir = path(NX_PROJECT_ROOT, set.project_subdir)
    local appdatadir = path(outdir, 'AppData')

    -- check output directory exists
    log_info('checking output directory %s ...', outdir)
    if (not is_dir(outdir)) then
        log_error('No output directory. Check NX_PROJECT_ROOT in dist-conf.lua')
        os.exit(-1)
        return
    end
    
    -- prepare temp dir
    log_info('preparing temp dir %s ...', tmpdir)
    mkdir(assetsdir)
    export_assets(set, assetsdir, args.debug)
    export_src(set, tmpdir, args.debug)
    export_license(set, tmpdir, args.debug)
    if args.has_override then add_arg_override(tmpdir,set,args) end
    local version_short = make_version_file(set,tmpdir,args.debug)
    make_main_globals(set,tmpdir)
    make_features(set,tmpdir)
    do_file_cmds(set,tmpdir,args.debug)
        
    if args.bytecompile then
        bytecompile(set, tmpdir, true)
    end

    -- patch NMETA version
    log_info('patching nmeta file version...')
    local nmetafile = path(outdir, 'Application.arm.ilp32.nmeta')
    local data = read(nmetafile)
    data = string.gsub(data, "<DisplayVersion>.*</DisplayVersion>", "<DisplayVersion>".. version_short .. "</DisplayVersion>")
    echo(data, nmetafile)
    
    -- copy into project
    log_info('copying into Nintendo dir %s ...', appdatadir)
    mkdir(appdatadir)
    rsync(tmpdir..'/', appdatadir, '--delete')
    
    -- clean
    if not args.no_clean then 
        log_info('cleaning temp dir %s...', tmpdir)
        rmdir(tmpdir)
    end

    log_info('task.nx finished.')
    
end

------------------------------------------------------------
-- xbox
function tasks.xbox(set, args)
    local tmpdir = runget('mktemp -d -t %s', set.bundle..'.XXX') -- TODO this concatenation is a fix for mktemp on linux 
    -- We should detect the running platform before running that concatenation.
    local assetsdir = tmpdir..'/_assets'
    local outdir = LOVE_XBOX_ASSETS[set.platform] ..'/'..set.game
    
    log_info('preparing temp dir %s ...', tmpdir)
    mkdir(assetsdir)
    export_assets(set, assetsdir, args.debug)
    export_src(set, tmpdir, args.debug)
    export_license(set, tmpdir, args.debug)
    if args.has_override then add_arg_override(tmpdir,set,args) end
    local version_short = make_version_file(set,tmpdir,args.debug)
    make_main_globals(set,tmpdir)
    make_features(set,tmpdir)
    do_file_cmds(set,tmpdir)
    if args.bytecompile then
        bytecompile(set, tmpdir, true)
    end

    -- copy into project
    log_info('copying into XBOX dir %s ...', outdir)
    rsync(tmpdir..'/', outdir, '--delete')

    -- not required for UWP apps as they are packaged from VS directly.
    -- -- create layout for makepkg.exe
    -- log_info('creating layout.xml for makepkg.exe')
    -- local dirs = find_dirs(tmpdir)
    -- dirs = table.map(dirs,
    --                  function(k,v)
    --                      local s = string.gsub(v, tmpdir, '')
    --                      s = string.gsub(s, '/', '\\')
    --                      return s
    --                  end
    -- )
    -- local builddir = 'x64\\' .. (args.debug and 'Debug' or 'Release') .. '\\' .. set.game
    -- local l = {}
    -- local i = function(s,...) table.insert(l, string.format(s,...)) end
    -- -- makepkg uses /d pointed to the outdir, so the root and Assets dirs are 3 dirs up.
    -- i('<Package>')
    -- i('  <Chunk Id="1000" Marker="Launch">')
    -- i('    <FileGroup DestinationPath="\\"            SourcePath=".\\"              Include="*.*"/>', builddir)
    -- i('    <FileGroup DestinationPath="\\Assets"      SourcePath=".\\Assets"      Include="*.*"/>', builddir)    
    -- -- _assets dir
    -- for _,dir in pairs(dirs) do
    --     i('    <FileGroup DestinationPath="%s"            SourcePath=".%s"            Include="*.*"/>', dir, dir)
    -- end
    -- i('  </Chunk>')
    -- i('  <Chunk Id="1073741823">')
    -- i('    <FileGroup DestinationPath="\\" SourcePath="..\\..\\..\\" Include="Update.AlignmentChunk"/>')
    -- i('  </Chunk>')
    -- i('</Package>')
    -- local ls = table.concat(l, '\n')
    -- echo(ls, path(outdir, 'layout.xml'))
    
    -- archive
    if args.archive then 
        mkdir(DIST_ARCHIVE)
        archive(set,args,tmpdir)
    end

    -- clean
    if not args.no_clean then 
        log_info('cleaning temp dir %s...', tmpdir)
        rmdir(tmpdir)
    end
    
    log_info('tasks.xbox finished.')

    --table.insert(report.comments, 'Now run makepkg.exe in the windows pc from the platform\\src\\Xbox\\dist dir')
    --table.insert(report.comments, 'devel: makepkg pack /f "..\\Game\\kr1\\layout.xml" /d "..\\x64\\Debug\\kr1" /pd .')
end

------------------------------------------------------------
-- gdk_xbox
function tasks.gdk_xbox(set, args)
    local configuration_dir = args.debug and 'Debug' or 'Release'
    local xbox_platform_dirs = {
        XboxOne  = 'output/Gaming.Xbox.XboxOne.x64/Layout/' .. configuration_dir .. '/Image/Loose/game_data',
        Scarlett = 'output/Gaming.Xbox.Scarlett.x64/Layout/' .. configuration_dir .. '/Image/Loose/game_data'
    }
    
    local tmpdir = runget('mktemp -d -t %s', set.bundle)
    local assetsdir = tmpdir..'/_assets'
    local outdir = path(KR_GDK_ROOT, set.project_subdir, xbox_platform_dirs[set.xbox_platform])
    
    log_info('preparing temp dir %s ...', tmpdir)
    mkdir(assetsdir)
    export_assets(set, assetsdir, args.debug)
    export_src(set, tmpdir, args.debug)
    export_license(set, tmpdir, args.debug)
    if args.has_override then add_arg_override(tmpdir,set,args) end
    local version_short = make_version_file(set,tmpdir,args.debug)
    make_main_globals(set,tmpdir)
    make_features(set,tmpdir)
    do_file_cmds(set,tmpdir)
    if args.bytecompile then
        bytecompile(set, tmpdir, true)
    end

    -- copy into project
    log_info('copying into GDK XBOX dir %s ...', outdir)
    rsync(tmpdir..'/', outdir, '--delete')
    
    -- -- archive
    -- if args.archive then 
    --     mkdir(DIST_ARCHIVE)
    --     archive(set,args,tmpdir)
    -- end

    -- clean
    if not args.no_clean then 
        log_info('cleaning temp dir %s...', tmpdir)
        rmdir(tmpdir)
    end
    
    table.insert(report.comments,'IMPORTANT: if shaders change they must be recompiled with shaderc in Windows.')
    table.insert(report.comments,'           See README.org for more details')
    log_info('tasks.gdk_xbox finished.')
end

------------------------------------------------------------
-- gdk_win
function tasks.gdk_win(set, args)
    local configuration_dir = args.debug and 'Debug' or 'Release'
    local platform_dir = 'output/Gaming.Desktop.x64/' .. configuration_dir .. '/game_data'

    local tmpdir = runget('mktemp -d -t %s', set.bundle)
    local assetsdir = tmpdir..'/_assets'
    local outdir = path(KR_GDK_ROOT, set.project_subdir, platform_dir)
    
    log_info('preparing temp dir %s ...', tmpdir)
    mkdir(assetsdir)
    export_assets(set, assetsdir, args.debug)
    export_src(set, tmpdir, args.debug)
    export_license(set, tmpdir, args.debug)
    if args.has_override then add_arg_override(tmpdir,set,args) end
    local version_short = make_version_file(set,tmpdir,args.debug)
    make_main_globals(set,tmpdir)
    make_features(set,tmpdir)
    do_file_cmds(set,tmpdir)
    if args.bytecompile then
        bytecompile(set, tmpdir, true)
    end

    -- copy into project
    log_info('copying into GDK Desktop dir %s ...', outdir)
    rsync(tmpdir..'/', outdir, '--delete')
    
    -- -- archive
    -- if args.archive then 
    --     mkdir(DIST_ARCHIVE)
    --     archive(set,args,tmpdir)
    -- end

    -- clean
    if not args.no_clean then     
        log_info('cleaning temp dir %s...', tmpdir)
        rmdir(tmpdir)
    end
    
    table.insert(report.comments,'IMPORTANT: if shaders change they must be recompiled with shaderc in Windows.')
    table.insert(report.comments,'           See README.org for more details')
    log_info('tasks.gdk_win finished.')
end

------------------------------------------------------------
-- ios
function tasks.ios(set, args)
    local tmpdir = runget('mktemp -d -t %s', set.bundle)
    local assetsdir = tmpdir..'/_assets'
    local outdir = path(IOS_PROJECT, set.project_subdir, 'lovegame')
    local outfile = path(outdir, 'game.love')    
    
    -- prepare temp dir
    log_info('preparing temp dir %s ...', tmpdir)
    mkdir(assetsdir)    
    export_assets(set, assetsdir, args.debug or args.love_debug)
    export_src(set, tmpdir, args.debug or args.love_debug)
    export_license(set, tmpdir, args.debug or args.love_debug)
    if args.has_override then add_arg_override(tmpdir,set,args) end
    local version_short = make_version_file(set,tmpdir,args.debug or args.love_debug)
    make_main_globals(set,tmpdir)
    make_features(set,tmpdir)
    do_file_cmds(set,tmpdir)
    if args.bytecompile then
        bytecompile(set, tmpdir, true)
    end

    -- copy into project
    log_info('copying into Xcode project %s ...', outfile)
    rmdir(outdir)
    mkdir(outdir)
    zip(tmpdir, outfile, args.debug)

    if set.checksum_files then
        local files = {}
        for _,f in pairs(set.checksum_files) do
            table.insert(files, path(outdir,f))
        end
        local sum = checksum(files)
        report.checksum = sum
    end
    
    -- clean
    if not args.no_clean then     
        log_info('cleaning temp dir %s...', tmpdir)
        rmdir(tmpdir)
    end

    log_info('task.ios finished.')
end

------------------------------------------------------------
-- android apk (with or without obb)
function tasks.apk(set,args)
    local tmpdir32 = runget('mktemp -d -t %s%s', set.bundle, '.32')
    local tmpdir64 = runget('mktemp -d -t %s%s', set.bundle, '.64')
    local assetsdir = tmpdir32..'/_assets'
    local outdir = path(ANDROID_PROJECT, set.project_subdir)
    local outfile = path(outdir, 'game.love')

    -- prepare temp dir
    log_info('preparing temp dir %s ...', tmpdir32)
    mkdir(assetsdir)
    export_assets(set, assetsdir, args.debug)
    export_src(set, tmpdir32, args.debug)
    if args.repl or args.localuser then add_arg_override(tmpdir32,set,args) end
    local version_short = make_version_file(set,tmpdir32,args.debug)
    make_main_globals(set,tmpdir32)
    make_features(set,tmpdir32)
    do_file_cmds(set,tmpdir32)

    rsync(tmpdir32..'/', tmpdir64)
    if args.bytecompile then
        bytecompile(set, tmpdir32, false)
        bytecompile(set, tmpdir64, true)
    end

    if not args.run_gradle and args.arch_filter then
        -- devel: just copy the desired arch to the output dir        
        local tmpdir = (args.arch_filter == '32') and tmpdir32 or tmpdir64            
        -- copy into project
        log_info('copying game.love %s into Android Studio project %s ...', args.arch_filter, outfile)
        rmdir(outfile)
        zip(tmpdir, outfile)
        
    else
        -- run gradle
        local apks_list = {}

        -- main pass
        for _,arch in pairs({'32','64'}) do
            log_info('making apk for arch %s', arch)
            
            local tmpdir = (arch == '32') and tmpdir32 or tmpdir64
            
            -- copy into project
            log_info('copying into Android Studio project %s ...', outfile)
            rmdir(outfile)
            zip(tmpdir, outfile)
                
            local cwd = lfs.currentdir()
            lfs.chdir(ANDROID_PROJECT)

            local arch_str = arch
            local type_sen_str = args.debug and 'Debug' or 'Release'
            local type_low_str = args.debug and 'debug' or 'release'

            local variant_all_type = 'f'  .. arch_str .. 'all'                  --'f32all'
            local variant          = 'f'  .. arch_str .. 'all'  .. type_sen_str -- args.debug and 'f32allDebug'   or 'f32allRelease'
            local variant_dash     = '-f' .. arch_str .. 'all-' .. type_low_str -- args.debug and '-f32all-debug' or '-f32all-release'
            local variant_sentence = 'F'  .. arch_str .. 'all'  .. type_sen_str -- args.debug and 'F32allDebug'   or 'F32allRelease'

            -- "0:armeabi-v7a,x86,arm64-v8a,x86_64"
            -- "1:armeabi-v7a,x86"
            -- "2:arm64-v8a,x86_64"
            -- "3:for_the_future"
            -- "4:armeabi"
            -- "5:armeabi-v7a"
            -- "6:arm64-v8a"
            -- "7:x86"
            -- "8:x86_64"
            local abiidx = (arch == '64') and '2' or '1'

            local version_code     = calculate_version_code(set,version_short,variant_all_type)

            -- run gradlew
            log_info('running gradle...')
            run('export JAVA_HOME="%s"; bash ./gradlew :kr_apps:%s:assemble%s -PversionName=%s -PversionCode=%s -PobbSize=0 -PobbOn=false -PabiIdx=%s',
                GRADLE_JAVA_HOME, set.gradle_subdir, variant_sentence, version_short, version_code, abiidx)
            
            -- align and sign
            local apk_prefix = path(ANDROID_PROJECT,'kr_apps',set.gradle_subdir,'build/outputs/apk',variant_all_type,type_low_str,set.gradle_subdir..variant_dash)
            local apk_file_raw = apk_prefix .. '-unsigned.apk'
            local apk_file = apk_prefix .. '.apk'

            if args.debug then
                -- in the debug build the product apk is named like the apk_file, so rename it
                mv(apk_file, apk_file_raw)
            else
                if is_file(apk_file) then rm(apk_file) end
            end

            log_info('aligning apk...')
            run_no_fail('%s -p 4 %s %s', ZIPALIGN, apk_file_raw, apk_file)            
            rm(apk_file_raw)

            table.insert(apks_list, {file=apk_file, version_code=version_code})
        end

        -- signature pass
        -- cannot use jarsigner as it's incompatible with V1 apks used in Android < 7
        for _,row in pairs(apks_list) do
            local apk_file = row.file
            log_info('signing apk %s ...', apk_file)
            local cwd = lfs.currentdir()
            lfs.chdir(ANDROID_PROJECT)            
            run('export JAVA_HOME="%s"; %s sign --ks %s %s', GRADLE_JAVA_HOME, APKSIGNER, set.keystore, apk_file)
            lfs.chdir(cwd)
        end

        -- archive pass
        if args.archive then
            for _,row in pairs(apks_list) do
                archive(set,args,row.file,row.version_code..'.apk')
            end
        end

        report.apk_files = {}
        for _,row in pairs(apks_list) do
            table.insert(report.apk_files, row.file)
        end
    end

    -- clean
    if not args.no_clean then 
        log_info('cleaning temp dir %s...', tmpdir32)
        rmdir(tmpdir32)
        log_info('cleaning temp dir %s...', tmpdir64)    
        rmdir(tmpdir64)
    end

    log_info('task_apk finished.')
end

------------------------------------------------------------
-- android aab
function tasks.aab(set,args)
    local game = set.game
    local ANDROID_ASSETS_PACK = ANDROID_PROJECT .. '/kr_assets'
    local adir    = path(ANDROID_ASSETS_PACK, game..'_assets','src/main/assets')
    local gc32dir = path(ANDROID_ASSETS_PACK, game..'_gc32','src/main/assets/gc32')
    local gc64dir = path(ANDROID_ASSETS_PACK, game..'_gc64','src/main/assets/gc64')

    -- clean
    rmdir(adir)
    rmdir(gc32dir)
    rmdir(gc64dir)
    mkdir(adir)
    mkdir(gc32dir)
    mkdir(gc64dir)

    export_assets(set,adir,args.debug or args.love_debug)

    -- make sure the assets don't go over 2GB
    local adir_size = du(adir)
    local max_size = 1.95*1024*1024
    if adir_size > max_size then
        log_error('assets pack is %s bytes and cannot exceed %s bytes', adir_size, max_size)
        os.exit(-1)
    end
    
    export_src(set,gc64dir,args.debug or args.love_debug)
    export_license(set,gc64dir,args.debug or args.love_debug)

    if args.repl or args.localuser then add_arg_override(gc64dir,set,args) end
    local version_short = make_version_file(set,gc64dir,args.debug or args.love_debug)
    make_main_globals(set,gc64dir)
    make_features(set,gc64dir)
    do_file_cmds(set,gc64dir)
    -- (not android) features: extract binary libs
    -- (not android) features: extract overrides (eg. china censorship)

    rsync(gc64dir..'/', gc32dir) -- duplicate for gc32
    if args.bytecompile then
        bytecompile(set, gc64dir, true)
        bytecompile(set, gc32dir, false)
    end

    -- flags gc32 and 64 for runtime debugging
    echo('gc32', path(gc32dir, 'gc32.flag'))
    echo('gc64', path(gc64dir, 'gc64.flag'))

    if not args.run_gradle then
        local ver = version_short
        local code = args.override_version_code or calculate_version_code(set,ver)
        local fmt = "ext.krDebugVersionName = '%s'\next.krDebugVersionCode = %s\n"
        log_info('writing debugconfig.gradle with %s (%s)', ver, code)
        echo(string.format(fmt, ver, code), path(ANDROID_PROJECT, 'kr_apps', set.gradle_subdir, 'debugconfig.gradle'))
            
    else
        local cwd = lfs.currentdir()
        lfs.chdir(ANDROID_PROJECT)

        local version_code     = calculate_version_code(set,version_short)
        if args.override_version_code then
            version_code = args.override_version_code
        end
        
        local variant          = args.debug and 'fallDebug'   or 'fallRelease'
        local variant_dash     = args.debug and '-fall-debug' or '-fall-release'
        local variant_sentence = args.debug and 'FallDebug'   or 'FallRelease'
        --                                      kr_apps/kr2_google/build/outputs/bundle/fallRelease/kr2_google-fall-release.aab
        local aab_path  = path(ANDROID_PROJECT,'kr_apps',set.gradle_subdir,'build/outputs/bundle',variant)
        local aab_file  = path(ANDROID_PROJECT,'kr_apps',set.gradle_subdir,'build/outputs/bundle',variant,set.gradle_subdir..variant_dash..'.aab')
        local apks_file = path(ANDROID_PROJECT,'kr_apps',set.gradle_subdir,'build/outputs/bundle',variant,set.gradle_subdir..variant_dash..'.apks')
        report.aab_file = aab_file
        report.aab_version_code = version_code

        -- run gradlew
        log_info('running gradle...')
        run('export JAVA_HOME="%s"; bash ./gradlew :kr_apps:%s:bundle%s -PversionName=%s -PversionCode=%s -PobbSize=0 -PobbOn=false -PabiIdx=0',
            GRADLE_JAVA_HOME, set.gradle_subdir, variant_sentence, version_short, version_code)

        -- sign
        log_info('signing aab...')

        local keystore = set.keystore
        local keystore_alias = set.keystore_alias

        if type(set.keystore) == 'table' then
            keystore = set.keystore.keystore
            keystore_alias = set.keystore.alias
            local keystore_password_command = ""
            if set.keystore.password and os.getenv(set.keystore.password) then
                keystore_password_command = string.format('-keypass:env %s -storepass:env %s', set.keystore.password, set.keystore.password) 
            end
            run('"%s" -keystore %s %s %s %s',
                JARSIGNER, keystore, keystore_password_command,  aab_file, keystore_alias)
        else
            run('"%s" -keystore %s %s %s',
                JARSIGNER, keystore, aab_file, keystore_alias)
        end

        -- export apks
        if args.export_apks or (not args.debug and set.checksum_files) then
            if is_file(apks_file) then rm(apks_file) end
            log_info('running bundletool...')
            run('export JAVA_HOME="%s"; java -jar %s build-apks --bundle=%s --output=%s --ks=%s --ks-key-alias=%s',
                GRADLE_JAVA_HOME, BUNDLETOOL, aab_file, apks_file, keystore, keystore_alias)
            report.apks_file = apks_file
        end

        lfs.chdir(cwd)
        
        -- checksums
        -- TODO: preformance. Can these apks be found someplace else without having to export them?
        if not args.debug and set.checksum_files then
            local dst_path = path(aab_path,'_tmp')
            mkdir(dst_path)
            unzip(apks_file, dst_path)
            local files = {}
            for _,f in pairs(set.checksum_files) do
                table.insert(files, path(dst_path,f))
            end
            local sum = checksum(files)
            report.checksum = sum
            rmdir(dst_path)            
        end
       
        -- archive aab
        if args.archive then
            local archive_name = archive(set,args,aab_file)
            if not args.debug and report.checksum then
                echo(report.checksum, archive_name..'.md5')
            end
        end
    end

    log_info('task_aab finished.')
end

------------------------------------------------------------
-- windows 32/64 bit
function tasks.win_prebuilt(set,args)
    local lovebinaries = DESKTOP_BINARIES[set.id]
    local tmpdir = runget('mktemp -d -t %s', set.bundle)
    local assetsdir = tmpdir..'/_assets'
    local outdir = path(DIST_OPEN, set.id)
    local gamefile = path(outdir, 'game.love')
    local exefile = path(outdir, set.title .. '.exe')

    -- cleanup
    rmdir(outdir)
    mkdir(outdir)    
    
    log_info('preparing temp dir in %s ...', tmpdir)
    mkdir(assetsdir)
    
    export_assets(set,assetsdir,args.debug)
    export_src(set,tmpdir,args.debug)
    if args.has_override then add_arg_override(tmpdir,set,args) end
    local version_short = make_version_file(set,tmpdir,args.debug)
    make_main_globals(set,tmpdir)
    make_features(set,tmpdir)
    do_file_cmds(set,tmpdir)

    -- TODO features: extract overrides (eg. china censorship)

    -- load features before bytecompiling
    local features = call(path(tmpdir, 'features.lua'), {KR_TARGET=set.target, setmetatable=setmetatable, rawget=rawget})

    -- bytecompile
    if args.bytecompile then
        bytecompile(set, tmpdir, true)
    end

    if set.msstore_dir then
        -- open filesystem for microsoft store
        -- exe is encrypted and cannot read the zip portion of itself.
        log_info('msstore: copy open filesystem to %s', outdir)
        rsync(tmpdir..'/', outdir, '--delete')
        cp(path(lovebinaries,'love.exe'), exefile)
    else
        -- zip game
        zip(tmpdir, gamefile)
        -- concat zip with love.exe
        append(exefile, path(lovebinaries,'love.exe'), gamefile)
        rm(gamefile)
    end

    -- export license outside game.love
    export_license(set,outdir,args.debug)    
        
    -- copy windows libraries
    rsync(lovebinaries .. '/', outdir, '--exclude=*.exe')

    -- copy libraries in features
    if features and features.libs then
        local suffix = set.platform == 'win' and 'x64' or 'x86'
        for _,v in pairs(features.libs) do
            log_info('copying library %s', v)
            cp(path(SRC_PATH,'../platform/bin/Windows.'..suffix, v..'.dll'), outdir)
        end
    end

    -- microsoft windows store specific
    if set.msstore_dir then
        -- copy files
        log_info('msstore: copy windows specific files to to %s', outdir)
        rsync(path(SRC_PATH,'../dist',set.msstore_dir,'/'), outdir)
        -- patch MicrosoftGame.config version
        local mg = read(path(outdir,'MicrosoftGame.config'))
        mg = string.gsub(mg,'0%.0%.0%.0',version_short .. '.0')
        echo(mg,path(outdir,'MicrosoftGame.config'))

        -- copy to windows machine
        mkdir(MSSTORE_MAKEPKG_WIN_DIR ..'/input/'..set.game)
        mkdir(MSSTORE_MAKEPKG_WIN_DIR ..'/output/'..set.game)
        rsync(outdir..'/', MSSTORE_MAKEPKG_WIN_DIR ..'/input/'..set.game, '--delete')

        -- makepkg genmap /f input/kr1-layout.xml /d input/kr1
        -- makepkg pack /f input/kr1-layout.xml /l /d input/kr1 /nogameos /pc /pd output/kr1
        
        table.insert(report.comments, 'The files were copied to the Windows machine.')
        table.insert(report.comments, string.format('Now create the package with the following commands from Windows.'))
        table.insert(report.comments, '')
        table.insert(report.comments, string.format('cd %s', MSSTORE_MAKEPKG_WIN_DIR))
        table.insert(report.comments, string.format('makepkg genmap /f input/%s-layout.xml /d input/%s', set.game,set.game))
        table.insert(report.comments, string.format('makepkg pack /f input/%s-layout.xml /l /d input/%s /nogameos /pc /pd output/%s', set.game,set.game,set.game))
        table.insert(report.comments, '')
        table.insert(report.comments, string.format('IMPORTANT: for testing use /lt instead of /l, to encrypt using test key', set.game))
    end

    -- archive
    if args.archive and not set.msstore_dir then 
        archive(set,args,outdir)
    end

    -- clean
    if not args.no_clean then 
        log_info('cleaning temp dir %s...', tmpdir)
        rmdir(tmpdir)
    end
    
    log_info('task.win_prebuilt finished.')
end

------------------------------------------------------------
-- linux
function tasks.linux_prebuilt(set,args)
    local lovebinaries = DESKTOP_BINARIES[set.id]
    local tmpdir = runget('mktemp -d -t %s', set.bundle)
    local assetsdir = tmpdir..'/_assets'
    local outdir = path(DIST_OPEN, set.id)
    local gamefile = path(outdir, 'game.love')
    local exefile = path(outdir, set.title)
    local runsh = path(outdir, 'run.sh')
    
    -- cleanup
    rmdir(outdir)
    mkdir(outdir)    
    
    log_info('preparing temp dir in %s ...', tmpdir)
    mkdir(assetsdir)
    
    export_assets(set,assetsdir,args.debug)
    export_src(set,tmpdir,args.debug)
    if args.has_override then add_arg_override(tmpdir,set,args) end
    local version_short = make_version_file(set,tmpdir,args.debug)
    make_main_globals(set,tmpdir)
    make_features(set,tmpdir)
    do_file_cmds(set,tmpdir)

    -- TODO features: extract overrides (eg. china censorship)

    -- load features before bytecompiling
    local features = call(path(tmpdir, 'features.lua'), {KR_TARGET=set.target, setmetatable=setmetatable, rawget=rawget})

    -- bytecompile
    if args.bytecompile then
        bytecompile(set, tmpdir, true)
    end

    -- concat zip with love.exe
    zip(tmpdir, gamefile)
    append(exefile, path(lovebinaries,'love'), gamefile)
    rm(gamefile)

    -- export license outside game.love
    export_license(set,outdir,args.debug)    
        
    -- copy linux libraries
    rsync(lovebinaries .. '/', outdir, '--exclude=love')

    -- copy libraries in features
    if features and features.libs then
        for _,v in pairs(features.libs) do
            log_info('copying library %s', v)
            cp(path(SRC_PATH,'../platform/bin/Linux','lib'..v..'.so'), outdir)
        end
    end

    -- create run script that prefixes LD_LIBRARY_PATH
    log_info('creating run script to set LD_LIBRARY_PATH...')
    local o = ''    
    o = o .. "#!/bin/sh\n"                                              
    o = o .. 'export LAUNCH_LOC="$(dirname "$(which "$0")")"\n'         
    o = o .. 'export LD_LIBRARY_PATH="${LAUNCH_LOC}:$LD_LIBRARY_PATH"\n'
    o = o .. 'exec "${LAUNCH_LOC}/' .. set.title .. '" "$@"\n'
    echo(o, runsh)
    chmod('a+rx', runsh)

        -- the launch command for kr2 is different in steam
    if set.game == 'kr2' then 
        cp(runsh, path(outdir, 'krf-love'))        
    end

    -- archive
    if args.archive then 
        archive(set,args,outdir)
    end

    -- clean
    if not args.no_clean then 
        log_info('cleaning temp dir %s...', tmpdir)
        rmdir(tmpdir)
    end
    
    log_info('task.linux_prebuilt finished.')
end

------------------------------------------------------------
-- mac
function tasks.mac_prebuilt(set,args)
    local lovebinaries = DESKTOP_BINARIES[set.id]
    local tmpdir = runget('mktemp -d -t %s', set.bundle)
    local assetsdir = tmpdir..'/_assets'
    local outdir = path(DIST_OPEN, set.id)
    local appdir = path(outdir, set.title .. '.app')
    local gamefile = path(appdir, 'Contents/Resources', 'game.love')
    local infoplist = path(appdir, 'Contents/Info.plist')
    
    -- cleanup
    rmdir(outdir)
    mkdir(outdir)    

    -- preparing app dir
    mkdir(appdir)
    rsync(path(lovebinaries,'love.app/'), appdir, '-a')
    cp(path(lovebinaries,'app_Info.plist'), infoplist)

    -- prepare temp dir
    log_info('preparing temp dir in %s ...', tmpdir)
    mkdir(assetsdir)    
    export_assets(set,assetsdir,args.debug)
    export_src(set,tmpdir,args.debug)
    if set.love_icon then
        log_info('replacing love app icon with %s', set.love_icon)
        cp(path(lovebinaries,'icons',set.love_icon),path(tmpdir,'_assets',set.game..'-'..set.target,'icons/icon256.png'))
    end
    if args.has_override then add_arg_override(tmpdir,set,args) end
    local version_short = make_version_file(set,tmpdir,args.debug)
    make_main_globals(set,tmpdir)
    make_features(set,tmpdir)
    do_file_cmds(set,tmpdir)

    -- load features before bytecompiling
    local features = call(path(tmpdir, 'features.lua'), {KR_TARGET=set.target, setmetatable=setmetatable, rawget=rawget})

    -- bytecompile
    if args.bytecompile then
        bytecompile(set, tmpdir, true)
    end 
    zip(tmpdir, gamefile)
    
    -- export license outside game.love
    export_license(set,path(appdir, 'Contents/Resources/'),args.debug)
    -- TODO features: extract overrides (eg. china censorship)
    -- copy libraries to .app/Contents/Frameworks/
    if features and features.libs then
        for _,v in pairs(features.libs) do
            log_info('copying library %s', v)
            local libname = 'lib'..v..'.dylib'
            cp(path(SRC_PATH,'../platform/bin/macOS',libname), path(appdir, 'Contents/Frameworks/'))
            local olib = path(appdir, 'Contents/Frameworks/', libname)
            -- fix library linking name as relative path for sandboxed apps
            -- this is not necessary if:
            --   - compiling the library with LD_DYLIB_INSTALL_NAME = @rpath/$(EXECUTABLE_PATH)
            --   - running the devel love version with DYLD_LIBRARY_PATH set to the dylibs path
            if set.patch_libs_install then
                for _,vv in pairs(set.patch_libs_install) do
                    if vv[1] ~= libname then 
                        patch_lib_install(olib, vv[1], vv[2])
                    end
                end
            end

        end
    end

    if features and features.resource_dirs then
        local dst = path(appdir, 'Contents/Resources')
        for _,v in pairs(features.resource_dirs) do
            local src = path(SRC_PATH, '_assets/_resources/', set.bundle, v)
            if is_dir(src) then 
                log_info('copying resources dir %s -> %s', src,dst)
                rsync(src,dst)
            end
        end
    end
    
    -- patch plists
    log_info('patching Info.plist ...')
    local version_mac = string.gsub(version_short, '%*', '')
    local version_build = string.gsub(version_mac, '.*%.', '')  -- keep only last digit
    version_build = string.gsub(version_build, '^0', '')        -- remove leading zeroes
    run('plutil -replace CFBundleIdentifier         -string "%s" "%s"', set.bundle,  infoplist)
    run('plutil -replace CFBundleName               -string "%s" "%s"', set.title,   infoplist)
    run('plutil -replace CFBundleVersion            -string "%s" "%s"', version_build, infoplist)  -- iteration of the bundle    (eg. 4.5.02 (3) : this is (3) ) 
    run('plutil -replace CFBundleShortVersionString -string "%s" "%s"', version_mac, infoplist)  -- the visible version string (eg. 4.5.02 )
    run('plutil -replace NSHumanReadableCopyright   -string "(C) 2010-%s Ironhide Game Studio" "%s"', os.date("%Y", os.time()) , infoplist)
    run('plutil -insert ITSAppUsesNonExemptEncryption -bool NO "%s"', infoplist)
    if set.arcade then
        run('plutil -insert NSApplicationRequiresArcade -bool YES "%s"', infoplist)
    end
    if set.min_version then
        run('plutil -replace LSMinimumSystemVersion -string "%s" "%s"', set.min_version, infoplist)        
    end
    if set.icon then
        log_info('replacing app icon with %s ...', set.icon)
        local oldicons = {path(appdir,'Contents/Resources/kr2_appstore.icns'),
                          path(appdir,'Contents/Resources/kr5.icns')}
        for _,oldicon in pairs(oldicons) do
            if is_file(oldicon) then rm(oldicon) end
        end
        cp(path(lovebinaries,'icons',set.icon), path(appdir,'Contents/Resources',set.icon))
        run('plutil -replace CFBundleIconFile         -string "%s" "%s"', set.icon,  infoplist)
    end
    
    -- prepare entitlements
    log_info('patching entitlements...')
    local entplist = path(tmpdir, 'patched.entitlements')
    if set.sign_method == 'development' then 
        cp(path(lovebinaries, 'love-dev.entitlements'), entplist)
    else
        cp(path(lovebinaries, 'love.entitlements'), entplist)
    end
    
    -- code sign
    if set.platform == 'mac' then
        -- standalone, developer id
        if set.certs and set.certs.app then
            log_info('signing standalone app %s', appdir)
            run('/usr/bin/codesign --force --deep --sign %s --timestamp  --options=runtime --entitlements "%s" "%s"',
                set.certs.app, entplist, appdir)
        end
    else
        -- development 
        if set.sign_method == 'development' then
            if set.gamecenter then
                run('/usr/libexec/PlistBuddy -c "Add :com.apple.developer.game-center bool true" "%s"', entplist)
                run('/usr/libexec/PlistBuddy -c "Add :com.apple.security.network.client bool true" "%s"', entplist)
            end
            if set.icloud then
                -- empty list
                run('/usr/libexec/PlistBuddy -c "Add :com.apple.developer.icloud-container-identifiers array" "%s"', entplist)            
            end
            if set.kvstore then
                run('/usr/libexec/PlistBuddy -c "Add :com.apple.developer.ubiquity-kvstore-identifier string \'%s\'" "%s"', set.kvstore, entplist)            
            end
            if set.arcade then
                log_info('  adding NSApplicationRequiresArcade ...')
                run('/usr/libexec/PlistBuddy -c "Add :com.apple.developer.arcade-operations bool true" "%s"', entplist)
            end

            -- sign libs without touching entitlements
            local fwdir = path(appdir, 'Contents', 'Frameworks')
            log_info('signing libs without entitlementss in %s', fwdir)
            for e in lfs.dir(fwdir) do
                if e == '..' or e == '.' then
                    goto skip
                end
                run('/usr/bin/codesign --force --sign %s -o runtime --preserve-metadata=identifier,entitlements,flags "%s"',
                    set.certs.app, path(fwdir,e))
                ::skip::
            end

            -- sign app
            log_info('signing mac development app %s', appdir)
            run('/usr/bin/codesign --force --sign %s -o runtime --entitlements "%s" "%s"',
                set.certs.app, entplist, appdir)
            
        else        
            -- mac-appstore needs more stuff
            if set.icloud then        
                run('/usr/libexec/PlistBuddy -c "Add :com.apple.developer.icloud-services string CloudDocuments" "%s"', entplist)    
                run('/usr/libexec/PlistBuddy -c "Add :com.apple.developer.icloud-container-identifiers array" "%s"', entplist)
                run('/usr/libexec/PlistBuddy -c "Add :com.apple.developer.icloud-container-identifiers:0 string %s" "%s"', set.icloud, entplist)
                run('/usr/libexec/PlistBuddy -c "Add :com.apple.developer.icloud-container-environment string Production" "%s"', entplist)
            end
            if set.kvstore then
                run('/usr/libexec/PlistBuddy -c "Add :com.apple.developer.ubiquity-container-identifiers array" "%s"', entplist)
                run('/usr/libexec/PlistBuddy -c "Add :com.apple.developer.ubiquity-container-identifiers:0 string %s" "%s"', set.icloud, entplist)        
                run('/usr/libexec/PlistBuddy -c "Add :com.apple.developer.ubiquity-kvstore-identifier string \'%s\'" "%s"', set.kvstore, entplist)
            end
            if set.arcade then
                log_info('  adding NSApplicationRequiresArcade ...')
                run('/usr/libexec/PlistBuddy -c "Add :com.apple.developer.arcade-operations bool true" "%s"', entplist)
            end

            -- remvoe existing signatures
            local fwdir = path(appdir, 'Contents', 'Frameworks')
            log_info('remove existing signatures for %s', fwdir)
            for e in lfs.dir(fwdir) do
                if e == '..' or e == '.' then
                    goto skip
                end
                run('/usr/bin/codesign --remove-signature "%s"', path(fwdir, e))
                ::skip::
            end
            
            -- sign
            log_info('signing mac appstore app %s', appdir)
            run('/usr/bin/codesign --force --deep --sign %s --timestamp=none --entitlements "%s" "%s"',
                set.certs.app, entplist, appdir)
        end

        
        -- prepare files for the xcode archive
        local arch_dist = path(outdir, set.title .. '.xcarchive')
        local arch_plist = path(arch_dist, 'Info.plist')
        local export_plist = path(arch_dist, 'export.plist')

        log_info('making .xcarchive structure...')
        mkdir(path(arch_dist,'Products/Applications'))
        mkdir(path(arch_dist,'dSYMs'))
        mkdir(path(arch_dist,'SCMBlueprint'))
        mv(appdir, path(arch_dist,'Products/Applications'))

        log_info('patching .xcarchive Info.plist')
        cp(path(lovebinaries, 'xarchive_Info.plist'), arch_plist)
        run('/usr/libexec/PlistBuddy -c "Set :ApplicationProperties:ApplicationPath \'Applications/%s.app\'" "%s"', set.title,   arch_plist)
        run('/usr/libexec/PlistBuddy -c "Set :ApplicationProperties:CFBundleIdentifier \'%s\'"               "%s"', set.bundle,  arch_plist)
        run('/usr/libexec/PlistBuddy -c "Set :ApplicationProperties:CFBundleVersion \'%s\'"                  "%s"', version_mac, arch_plist)
        run('/usr/libexec/PlistBuddy -c "Set :ApplicationProperties:CFBundleShortVersionString \'%s\'"       "%s"', version_mac, arch_plist)
        run('/usr/libexec/PlistBuddy -c "Set :ApplicationProperties:SigningIdentity \'%s\'"                  "%s"', set.sign_id, arch_plist)
        run('/usr/libexec/PlistBuddy -c "Set :CreationDate \'%s\'" "%s"', os.date("%a %b %d %T %Y %z", os.time()), arch_plist)
        run('/usr/libexec/PlistBuddy -c "Set :Name \'%s\'"         "%s"', set.title, arch_plist) --love
        run('/usr/libexec/PlistBuddy -c "Set :SchemeName \'%s\'"   "%s"', set.title, arch_plist) --love        

        log_info('patching xcode export.plist')
        cp(path(lovebinaries,"export.plist"),export_plist)
        run('/usr/libexec/PlistBuddy -c "Set :signingCertificate %s"                      "%s"', set.certs.app, export_plist)
        run('/usr/libexec/PlistBuddy -c "Delete :provisioningProfiles:REMOVE_THIS string" "%s"', export_plist)
        run('/usr/libexec/PlistBuddy -c "Add :provisioningProfiles:%s string \'%s\'"      "%s"', set.bundle, set.profile, export_plist) 
        run('/usr/libexec/PlistBuddy -c "Set :method \'%s\'"                              "%s"', set.sign_method, export_plist)
        if set.sign_method == 'app-store' then
            run('/usr/libexec/PlistBuddy -c "Set :installerSigningCertificate %s"         "%s"', set.certs.inst, export_plist)
        else
            run('/usr/libexec/PlistBuddy -c "Delete :installerSigningCertificate"         "%s"',export_plist)
        end
        if set.sign_method == 'development' then
            run('/usr/libexec/PlistBuddy -c "Add :iCloudContainerEnvironment string \'%s\'" "%s"', 'Development', export_plist)
        else
            run('/usr/libexec/PlistBuddy -c "Add :iCloudContainerEnvironment string \'%s\'" "%s"', 'Production', export_plist)
        end

        -- no need to export, open with xcode organizer and upload
        --log_info('exporting archive with xcodebuild')
        --run('xcodebuild -exportArchive -archivePath "%s" -exportPath "%s/export" -exportOptionsPlist  "%s"',
        --    arch_dist, outdir, export_plist)

        if set.sign_method == 'development' then
            table.insert(report.comments, 'IMPORTANT: copy the exported .app to /Applications for the signature to work!')
            table.insert(report.comments, 'IMPORTANT: disable the FairPlay service in features to test!')
        end
    end

    -- archive
    if args.archive then
        log_info('archiving...')
        local archive_src
        if set.platform == 'mac-appstore' then
            --archive_src = path(outdir, 'export', set.title .. '.pkg')
            archive_src = path(outdir, set.title .. '.xcarchive')
        else
            archive_src = appdir
        end
        archive(set,args,archive_src)
    end

    -- clean
    if not args.no_clean then 
        log_info('cleaning temp dir %s...', tmpdir)
        rmdir(tmpdir)
    end
    
    log_info('task.mac_prebuilt finished.')    
end
------------------------------------------------------------
------------------------------------------------------------

local usage_msg = [[
Usage: lua dist.lua [options] set
Options:
  -a   : export apks from the aab to be installed in the device for testing
  -d   : debug build (release by default)
  -l N : debug level (5:debug 3:info 1:error 0:off)
  -p   : profiler override (requires -d) : added to args.lua
  -r   : repl override with default settings (requires -d) : added to args.lua
  -u   : loads localuser.lua from game path (requires -d)
  -w   : draw_stats override (requires -d) : added to args.lua

  -A   : do not archive
  -D   : love debug, build the game in debug but in release binaries
  -C   : do not clean temp dir after build
  -G   : do not run gradle (for android dev builds. copies game.love for tasks.apk)
  -K   : do not bytecompile
  -32  : only build 32bit
  -64  : only build 64bit
]]

local function usage()
    print(usage_msg)
    print('Available sets:')
    local keys = table.keys(SETS)
    table.sort(keys)
    print(table.concat(keys, '\n'))
end

if #arg < 1 then
    log_error('Missing arguments\n')
    usage()
    os.exit(-1)
end

local set_id = arg[#arg]
local set = SETS[set_id]
if set == nil then
    log_error("Wrong set:" .. set_id);
    usage()
    os.exit(-1)
end

-- args parsing
local args = {}
args.export_apks = has_arg('a')
args.debug = has_arg('d')

args.repl = args.debug and has_arg('r')
args.draw_stats = args.debug and has_arg('w')
args.profiler = args.debug and has_arg('p')
args.localuser = args.debug and has_arg('u')
args.has_override = args.repl or args.draw_stats or args.profiler or args.localuser

args.archive = not has_arg('A')
args.love_debug = has_arg('D')
args.no_clean = has_arg('C') and true or nil
args.run_gradle = not has_arg('G')
args.bytecompile = not has_arg('K')
args.log_level = has_arg('l') and argv('l') or LEVEL_INFO
args.override_version_short = has_arg('vs') and argv('vs') or nil
args.override_version_code = has_arg('vc') and argv('vc') or nil

if args.log_level then
    LOG_LEVEL = tonumber(args.log_level)
end
if has_arg('32') then 
    args.arch_filter = '32'
elseif has_arg('64') then
    args.arch_filter = '64'
end

-- execute task
if not tasks[set.task] then
    log_error('Could not find task %s specified in set %s', set.task, set.id)
    os.exit(-1)
end
tasks[set.task](set,args)

-- output report
log_info('------------------------------------------------------------')
log_info('main_globals.lua : %s',report.main_globals_lua)
log_info('version.lua      :\n%s',report.version_lua)
log_info('----------------------------------------')
log_info('set              : %s', set.id)
log_info('debug            : %s', args.debug)
log_info('love_debug       : %s', args.love_debug)
log_info('bytecompile      : %s', args.bytecompile)
if args.has_override then
log_info('arg overrides    ')
log_info('    draw_stats   : %s', args.draw_stats)
log_info('    profiler     : %s', args.profiler)
log_info('    localuser    : %s', args.localuser)
log_info('    repl         : %s', args.repl)
end
log_info('version          : %s', report.version_short)
if report.aab_file then
    log_info('minsdk           : %s', report.minsdk)
    log_info('version_code     : %s', report.aab_version_code)
    log_info('aab_file         : %s', report.aab_file)
end
if report.apks_file then
    log_info('apks_file        : %s', report.apks_file)
end
if report.archive_names then
    for _,f in pairs(report.archive_names) do
        log_info('archive_name     : %s', f)
    end
end
if report.apk_files then
    for _,f in pairs(report.apk_files) do
        log_info('apk_file         : %s', f)
    end
end
if report.checksum then
    log_info('loggerhead_id    :\n%s', report.checksum)
    local ssheet_lines = ''
    for _,line in pairs(string.split(report.checksum,'\n')) do
        local cs_parts = string.split(line,' : ')
        ssheet_lines = ssheet_lines .. string.format('%s\t%s\t%s\t%s\t%s\t%s\n', os.date('%Y-%m-%d',os.time()), set.game, set.id, report.version_short, cs_parts[1], cs_parts[2] or '')
    end
    log_info('  save published ids in: https://docs.google.com/spreadsheets/d/14M8IcI6wXfvNqnexiQTL22FQcJtg_P8pcI9VObPD56U/edit?usp=sharing \n%s', ssheet_lines)
end
if report.comments then
    log_info('----------------------------------------')
    log_info('comments         :\n%s', table.concat(report.comments,'\n'))
end
log_info('----------------------------------------')
if report.apks_file then
    log_info('install script   : export JAVA_HOME="%s"; java -jar %s install-apks --apks=%s',
             GRADLE_JAVA_HOME, BUNDLETOOL, report.apks_file)
end
if not args.debug and report.archive_names then
    for _,f in pairs(report.archive_names) do 
        log_info('backup script    : ./kr-binaries-upload.sh releases/%s-%s/%s/%s %s',             
                 set.game, set.target, report.version_short, set.id, f)
    end
end

