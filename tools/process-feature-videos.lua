--
-- compress and copy feature videos to the corresponding locations
--

if not pcall(require, 'KR_PATHS') then
    print('ERROR: Could not find KR_PATHS.lua file.')
    print('Use KR_PATHS.lua.example as a reference to create it.')
    os.exit(-1)
end
package.path = package.path .. ';' .. '../src/lib/?.lua'

-- lua 5.4 compat
if table.unpack then
    unpack = table.unpack
end

local log = (require 'klua.log'):new('process-feature-videos')
log.level = log.INFO_LEVEL
local ksh = require('klua.shell')
require('klua.string')
require('klua.table')
require('klua.dump')

--ksh.DRY_RUN = true

------------------------------------------------------------

-- uses same set names as dist.lua
local sets = {
    kr5_dyn_ios            = {game='kr5', bundle='com.ironhidegames.kingdomrush.alliance'},
    kr5_dyn_android_google = {game='kr5', bundle='com.ironhidegames.android.kingdomrush.alliance'},
}

local cmd_args = {
    'ffmpeg',
    '-i %s',           -- input file

    --'-vf "scale=320:-2,crop=320:480:(iw-320)/2:(ih-480)/2"',  -- rescaling

    '-c:v libtheora',  -- video encoder
    '-q:v 7',          -- video encoding quality
    '-r 15',           -- video framerate
    '-g 15',           -- video keyframe every

    --'-an',             -- NO AUDIO
    --                 -- OR
    --                 -- AUDIO
    '-c:a libvorbis',  -- audio compression 
    '-q:a 4',          -- audio compression quality
    '-ar 44100',       -- audio sampling rate

    '%s'               -- output file
}

function process(set)
    local input_dir = ksh.path(KR_LOVE_PREPROC, 'resources', set.game, 'feature_videos')
    local files = {}
    ksh.find_files(input_dir, '.*%.mp4', files)
    for _,f in pairs(files) do
        local on = string.gsub(ksh.basename(f), '.mp4$', '.ogv')
        local of = ksh.path(KR_LOVE_GIT, 'src/_assets/_resources', set.bundle, 'feature_videos', on)
        ksh.run(table.concat(cmd_args, ' '), f, of)
    end
end

------------------------------------------------------------
local usage_msg = [[
Usage: lua process-feature-videos.lua set_name

Available sets: %s
]]

local set_id = arg[#arg]
local set = sets[set_id]
if not set then
    io.stderr:write(string.format('Could not find set %s\n', set_id))
    io.stderr:write(string.format(usage_msg, table.concat(table.keys(sets), ', ')))
    os.exit(-1)
end

process(set)

